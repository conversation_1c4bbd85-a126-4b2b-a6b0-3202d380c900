## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET `.aspx` and C# code-behind files are minimal placeholders, primarily serving as an entry point for a "Print" functionality for "Material Credit Notes." They do not contain explicit database schema, UI elements, or backend logic. Therefore, this modernization plan will infer the necessary components for a comprehensive Django application based on the module and file names (`Module_Inventory_Transactions_MaterialCreditNote_MCN_Print.aspx`).

**Business Value of Django Modernization:**

Migrating to Django offers significant business benefits:
*   **Reduced Operational Costs:** Open-source nature eliminates licensing fees for the framework.
*   **Enhanced Scalability:** Django's robust architecture allows your application to grow with your business needs.
*   **Improved Maintainability:** Adherence to best practices and a well-defined structure makes the codebase easier to understand and update.
*   **Increased Development Speed:** Django's "batteries included" philosophy and strong community support accelerate feature development.
*   **Modern User Experience:** Leveraging HTMX and Alpine.js provides highly responsive and dynamic interfaces without complex JavaScript frameworks, improving user satisfaction.
*   **Future-Proofing:** Moving away from legacy ASP.NET ensures your application stays relevant and secure.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is only a skeleton for a "print" page and contains no direct database interactions, we must infer the database schema based on the file name `MaterialCreditNote_MCN_Print`. This suggests a primary entity named `MaterialCreditNote`.

*   **Inferred Table Name:** `tblMaterialCreditNote`
*   **Inferred Columns:**
    *   `MCN_Id` (Primary Key, typically an auto-incrementing integer)
    *   `MCN_Number` (Unique alphanumeric string, e.g., "MCN-2023-001")
    *   `MCN_Date` (Date of the credit note)
    *   `Party_Name` (Name of the party/customer associated with the credit note)
    *   `Total_Amount` (Decimal value for the credit note amount)
    *   `Reason` (Text description for the reason of the credit note)
    *   `Created_Date` (Timestamp for creation)
    *   `Updated_Date` (Timestamp for last update)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The given ASP.NET code for `MaterialCreditNote_MCN_Print.aspx` is purely for displaying information, likely for printing. It does not explicitly define CRUD (Create, Read, Update, Delete) operations. However, for a `MaterialCreditNote` entity to be managed within an ERP system, these operations are fundamental.

*   **Read (List & Detail):** Displaying a list of all Material Credit Notes and viewing details of a single note. The original `MCN_Print` page falls under "Read".
*   **Create:** Adding new Material Credit Notes.
*   **Update:** Modifying existing Material Credit Notes.
*   **Delete:** Removing Material Credit Notes.

No validation logic is present in the provided ASP.NET snippet, but standard field validation (e.g., required fields, data type constraints) will be assumed and implemented in Django forms and models.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The provided ASP.NET `.aspx` file contains no specific UI controls beyond content placeholders. However, based on typical ERP patterns for managing an entity like `Material Credit Note`, we infer the need for:

*   **List View:** A table (analogous to a `GridView`) to display multiple `MaterialCreditNote` entries, with search, sort, and pagination capabilities (will be handled by DataTables). This view will also have buttons for Add, Edit, and Delete.
*   **Form View:** A form (analogous to `TextBox`, `DropDownList`, etc.) for creating and editing `MaterialCreditNote` entries.
*   **Confirmation Dialog:** A simple modal for confirming deletion.

All dynamic interactions (loading forms, submitting data, refreshing lists) will be handled using HTMX, making the experience seamless without full page reloads. Alpine.js will manage simple UI state for modals.

## Step 4: Generate Django Code

We will create a Django application named `inventory` to house the `MaterialCreditNote` module.

### 4.1 Models

Task: Create a Django model based on the inferred database schema.

## Instructions:

The `MaterialCreditNote` model will represent the `tblMaterialCreditNote` table in the database.

```python
# inventory/models.py
from django.db import models
from django.utils import timezone

class MaterialCreditNote(models.Model):
    # Django automatically creates an 'id' field as the primary key.
    # MCN_Id (PK) is mapped to Django's default 'id' field if it's auto-incrementing.
    mcn_number = models.CharField(
        db_column='MCN_Number',
        max_length=50,
        unique=True,
        verbose_name='Credit Note Number',
        help_text='Unique identifier for the Material Credit Note.'
    )
    mcn_date = models.DateField(
        db_column='MCN_Date',
        verbose_name='Credit Note Date',
        default=timezone.now
    )
    party_name = models.CharField(
        db_column='Party_Name',
        max_length=255,
        verbose_name='Party Name',
        help_text='The name of the party associated with this credit note.'
    )
    total_amount = models.DecimalField(
        db_column='Total_Amount',
        max_digits=10,
        decimal_places=2,
        verbose_name='Total Amount'
    )
    reason = models.TextField(
        db_column='Reason',
        blank=True,
        null=True,
        verbose_name='Reason for Credit Note',
        help_text='Detailed reason for issuing the credit note.'
    )
    created_date = models.DateTimeField(
        db_column='Created_Date',
        auto_now_add=True,
        verbose_name='Created Date'
    )
    updated_date = models.DateTimeField(
        db_column='Updated_Date',
        auto_now=True,
        verbose_name='Updated Date'
    )

    class Meta:
        managed = False  # Set to True if Django should manage table creation/alteration
        db_table = 'tblMaterialCreditNote'
        verbose_name = 'Material Credit Note'
        verbose_name_plural = 'Material Credit Notes'
        ordering = ['-mcn_date', 'mcn_number']

    def __str__(self):
        return f"{self.mcn_number} ({self.party_name})"
        
    # Business logic methods (Fat Model approach)
    def calculate_tax(self, tax_rate):
        """Calculates tax based on total_amount and a given tax_rate."""
        if not isinstance(tax_rate, (int, float)) or tax_rate < 0:
            raise ValueError("Tax rate must be a non-negative number.")
        return self.total_amount * (tax_rate / 100)

    def get_status_display(self):
        """Placeholder for a status display method, e.g., based on related transactions."""
        # In a real scenario, this might involve checking associated transactions, etc.
        return "Processed" # Example
```

### 4.2 Forms

Task: Define a Django form for user input for `MaterialCreditNote`.

## Instructions:

A `ModelForm` will be used to automatically generate form fields from the `MaterialCreditNote` model.

```python
# inventory/forms.py
from django import forms
from .models import MaterialCreditNote

class MaterialCreditNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialCreditNote
        fields = ['mcn_number', 'mcn_date', 'party_name', 'total_amount', 'reason']
        widgets = {
            'mcn_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mcn_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'party_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'reason': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
        }
        labels = {
            'mcn_number': 'Credit Note Number',
            'mcn_date': 'Credit Note Date',
            'party_name': 'Party Name',
            'total_amount': 'Total Amount',
            'reason': 'Reason',
        }
        
    def clean_total_amount(self):
        amount = self.cleaned_data['total_amount']
        if amount <= 0:
            raise forms.ValidationError("Total amount must be a positive value.")
        return amount

    # Example of a custom validation (if needed, otherwise rely on model/field defaults)
    # def clean_mcn_number(self):
    #     mcn_number = self.cleaned_data['mcn_number']
    #     # Add specific validation for MCN number format here if required
    #     return mcn_number
```

### 4.3 Views

Task: Implement CRUD operations using Django Class-Based Views (CBVs).

## Instructions:

A `TablePartialView` is added to render only the DataTables HTML, allowing HTMX to swap just that portion of the page.

```python
# inventory/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialCreditNote
from .forms import MaterialCreditNoteForm

class MaterialCreditNoteListView(ListView):
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/list.html'
    context_object_name = 'materialcreditnotes' # This will be used in the template to loop through objects

class MaterialCreditNoteTablePartialView(ListView):
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/_materialcreditnote_table.html'
    context_object_name = 'materialcreditnotes'

class MaterialCreditNoteCreateView(CreateView):
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html' # Use partial for HTMX
    success_url = reverse_lazy('materialcreditnote_list') # Redundant for HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content response
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList' # Custom event for HTMX
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return response
        return response

class MaterialCreditNoteUpdateView(UpdateView):
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html' # Use partial for HTMX
    success_url = reverse_lazy('materialcreditnote_list') # Redundant for HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content response
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return response
        return response

class MaterialCreditNoteDeleteView(DeleteView):
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/_materialcreditnote_confirm_delete.html' # Use partial for HTMX
    success_url = reverse_lazy('materialcreditnote_list') # Redundant for HTMX, but good practice

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Credit Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content response
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

All templates are located under `inventory/materialcreditnote/`.

**`inventory/materialcreditnote/list.html`**
This is the main page that displays the Material Credit Note list and serves as the container for HTMX-loaded modals.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Credit Notes</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialcreditnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Material Credit Note
        </button>
    </div>
    
    <div id="materialcreditnoteTable-container"
         hx-trigger="load, refreshMaterialCreditNoteList from:body"
         hx-get="{% url 'materialcreditnote_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Credit Notes...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) and confirmation (Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 sm:mx-0 transform transition-all sm:my-8 sm:align-middle">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed for basic modal, HTMX + _hyperscript handles it.
    });

    // Custom event listener to close modal after HTMX form submission
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) { // HTMX swap none, status 204 for successful forms
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });

    // Re-initialize DataTables when HTMX swaps in new content
    document.body.addEventListener('htmx:afterSettle', function(event) {
        if (event.detail.target && event.detail.target.id === 'materialcreditnoteTable-container') {
            // Check if DataTables is already initialized to avoid re-initialization errors
            if ($.fn.DataTable.isDataTable('#materialcreditnoteTable')) {
                $('#materialcreditnoteTable').DataTable().destroy();
            }
            $('#materialcreditnoteTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true,
                "autoWidth": false
            });
        }
    });
</script>
{% endblock %}
```

**`inventory/materialcreditnote/_materialcreditnote_table.html`**
This partial template renders the DataTables table itself. It's designed to be swapped in via HTMX.

```html
<div class="overflow-x-auto shadow-lg rounded-lg">
    <table id="materialcreditnoteTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Credit Note Number</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Party Name</th>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Reason</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in materialcreditnotes %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.mcn_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.mcn_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.party_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-right text-gray-800">{{ obj.total_amount }}</td>
                <td class="py-3 px-4 text-sm text-gray-800">{{ obj.reason|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'materialcreditnote_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'materialcreditnote_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center text-gray-600">No Material Credit Notes found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script. This runs when the partial is loaded by HTMX. -->
<!-- It should be re-initialized after every HTMX swap into the table container -->
<!-- The main list.html handles re-initialization on htmx:afterSettle for this container -->
<script>
// This script block is primarily for initial page load or if this partial is loaded standalone.
// For HTMX, the parent `list.html` handles re-initialization upon `htmx:afterSettle`.
// You can remove this script block if you are certain DataTables will *always* be initialized
// via the `htmx:afterSettle` listener in `list.html`. Keeping it for robustness.
if (typeof jQuery !== 'undefined') {
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#materialcreditnoteTable')) {
            $('#materialcreditnoteTable').DataTable().destroy();
        }
        $('#materialcreditnoteTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false
        });
    });
}
</script>
```

**`inventory/materialcreditnote/_materialcreditnote_form.html`**
This partial template renders the form for adding or editing a `MaterialCreditNote`. It's designed to be loaded into a modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Material Credit Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-1 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <ul class="text-red-600 text-sm list-none p-0">
                    {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out">
                Save Material Credit Note
            </button>
        </div>
    </form>
</div>
```

**`inventory/materialcreditnote/_materialcreditnote_confirm_delete.html`**
This partial template provides a confirmation dialog for deleting a `MaterialCreditNote`. It's loaded into a modal via HTMX.

```html
<div class="p-6 text-center">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Material Credit Note: <br><strong class="text-red-600">{{ materialcreditnote.mcn_number }} ({{ materialcreditnote.party_name }})</strong>?</p>
    <p class="text-sm text-gray-500 mb-8">This action cannot be undone.</p>
    
    <form hx-post="{% url 'materialcreditnote_delete' materialcreditnote.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out">
                Yes, Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views within the `inventory` app.

## Instructions:

These URLs will be included in the project's main `urls.py`.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    MaterialCreditNoteListView, 
    MaterialCreditNoteCreateView, 
    MaterialCreditNoteUpdateView, 
    MaterialCreditNoteDeleteView,
    MaterialCreditNoteTablePartialView,
)

urlpatterns = [
    path('material-credit-notes/', MaterialCreditNoteListView.as_view(), name='materialcreditnote_list'),
    path('material-credit-notes/table/', MaterialCreditNoteTablePartialView.as_view(), name='materialcreditnote_table'),
    path('material-credit-notes/add/', MaterialCreditNoteCreateView.as_view(), name='materialcreditnote_add'),
    path('material-credit-notes/edit/<int:pk>/', MaterialCreditNoteUpdateView.as_view(), name='materialcreditnote_edit'),
    path('material-credit-notes/delete/<int:pk>/', MaterialCreditNoteDeleteView.as_view(), name='materialcreditnote_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `MaterialCreditNote` model and integration tests for its CRUD views will ensure application stability.

```python       
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import MaterialCreditNote
from decimal import Decimal

class MaterialCreditNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test MaterialCreditNote instance for all tests
        cls.mcn1 = MaterialCreditNote.objects.create(
            mcn_number='MCN-TEST-001',
            mcn_date='2023-01-15',
            party_name='Test Party A',
            total_amount=Decimal('100.50'),
            reason='Returned goods',
        )
        cls.mcn2 = MaterialCreditNote.objects.create(
            mcn_number='MCN-TEST-002',
            mcn_date='2023-01-16',
            party_name='Test Party B',
            total_amount=Decimal('250.75'),
            reason='Price adjustment',
        )
  
    def test_materialcreditnote_creation(self):
        """Test that a MaterialCreditNote instance is created correctly."""
        self.assertEqual(self.mcn1.mcn_number, 'MCN-TEST-001')
        self.assertEqual(self.mcn1.party_name, 'Test Party A')
        self.assertEqual(self.mcn1.total_amount, Decimal('100.50'))
        self.assertEqual(self.mcn1.reason, 'Returned goods')
        self.assertIsNotNone(self.mcn1.created_date)
        self.assertIsNotNone(self.mcn1.updated_date)

    def test_mcn_number_unique(self):
        """Test that mcn_number is unique."""
        with self.assertRaises(Exception): # Expecting IntegrityError or similar
            MaterialCreditNote.objects.create(
                mcn_number='MCN-TEST-001', # Duplicate
                mcn_date='2023-01-17',
                party_name='Another Party',
                total_amount=Decimal('50.00'),
            )
        
    def test_str_method(self):
        """Test the __str__ method returns the expected string."""
        expected_str = f"{self.mcn1.mcn_number} ({self.mcn1.party_name})"
        self.assertEqual(str(self.mcn1), expected_str)

    def test_calculate_tax_method(self):
        """Test the calculate_tax business logic method."""
        # Test with a positive tax rate
        tax_rate = 10
        expected_tax = Decimal('10.05')
        self.assertEqual(self.mcn1.calculate_tax(tax_rate), expected_tax)

        # Test with zero tax rate
        self.assertEqual(self.mcn1.calculate_tax(0), Decimal('0.00'))

        # Test with invalid tax rate (non-numeric)
        with self.assertRaises(ValueError):
            self.mcn1.calculate_tax("invalid")

        # Test with negative tax rate
        with self.assertRaises(ValueError):
            self.mcn1.calculate_tax(-5)

    def test_get_status_display_method(self):
        """Test the get_status_display placeholder method."""
        self.assertEqual(self.mcn1.get_status_display(), "Processed")


class MaterialCreditNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.mcn_existing = MaterialCreditNote.objects.create(
            mcn_number='MCN-VIEWS-001',
            mcn_date='2023-02-01',
            party_name='View Test Party',
            total_amount=Decimal('300.00'),
            reason='Defective product',
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view_get(self):
        """Test that the list view loads correctly."""
        response = self.client.get(reverse('materialcreditnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/list.html')
        self.assertTrue('materialcreditnotes' in response.context)
        self.assertContains(response, self.mcn_existing.mcn_number)
        
    def test_table_partial_view_get(self):
        """Test that the HTMX partial table view loads correctly."""
        response = self.client.get(reverse('materialcreditnote_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_table.html')
        self.assertTrue('materialcreditnotes' in response.context)
        self.assertContains(response, self.mcn_existing.mcn_number)

    def test_create_view_get(self):
        """Test that the create form view loads correctly via HTMX."""
        response = self.client.get(reverse('materialcreditnote_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        """Test successful creation of a MaterialCreditNote via POST (HTMX)."""
        data = {
            'mcn_number': 'MCN-NEW-001',
            'mcn_date': '2023-03-01',
            'party_name': 'New Customer Inc.',
            'total_amount': '450.25',
            'reason': 'Customer goodwill',
        }
        response = self.client.post(reverse('materialcreditnote_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post + HX-Swap: none
        self.assertTrue(MaterialCreditNote.objects.filter(mcn_number='MCN-NEW-001').exists())
        self.assertEqual(response['HX-Trigger'], 'refreshMaterialCreditNoteList')

    def test_create_view_post_invalid(self):
        """Test invalid creation of a MaterialCreditNote via POST (HTMX)."""
        data = {
            'mcn_number': 'MCN-INVALID',
            'mcn_date': '2023-03-02',
            'party_name': '', # Missing required field
            'total_amount': '-100', # Invalid amount
        }
        response = self.client.post(reverse('materialcreditnote_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertFalse(MaterialCreditNote.objects.filter(mcn_number='MCN-INVALID').exists())
        self.assertContains(response, 'This field is required.') # Check for validation error messages
        self.assertContains(response, 'Total amount must be a positive value.')

    def test_update_view_get(self):
        """Test that the update form view loads correctly via HTMX."""
        response = self.client.get(reverse('materialcreditnote_edit', args=[self.mcn_existing.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.pk, self.mcn_existing.pk)
        self.assertContains(response, self.mcn_existing.mcn_number) # Check if existing data is pre-filled
        
    def test_update_view_post_success(self):
        """Test successful update of a MaterialCreditNote via POST (HTMX)."""
        updated_data = {
            'mcn_number': 'MCN-VIEWS-001', # Keep same unique number
            'mcn_date': '2023-02-01',
            'party_name': 'Updated Party Name',
            'total_amount': '350.00',
            'reason': 'Updated reason',
        }
        response = self.client.post(reverse('materialcreditnote_edit', args=[self.mcn_existing.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.mcn_existing.refresh_from_db()
        self.assertEqual(self.mcn_existing.party_name, 'Updated Party Name')
        self.assertEqual(self.mcn_existing.total_amount, Decimal('350.00'))
        self.assertEqual(response['HX-Trigger'], 'refreshMaterialCreditNoteList')

    def test_delete_view_get(self):
        """Test that the delete confirmation view loads correctly via HTMX."""
        response = self.client.get(reverse('materialcreditnote_delete', args=[self.mcn_existing.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_confirm_delete.html')
        self.assertTrue('materialcreditnote' in response.context)
        self.assertContains(response, self.mcn_existing.mcn_number)
        
    def test_delete_view_post_success(self):
        """Test successful deletion of a MaterialCreditNote via POST (HTMX)."""
        mcn_to_delete = MaterialCreditNote.objects.create(
            mcn_number='MCN-TO-DELETE',
            mcn_date='2023-04-01',
            party_name='Party To Delete',
            total_amount=Decimal('50.00'),
        )
        response = self.client.post(reverse('materialcreditnote_delete', args=[mcn_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialCreditNote.objects.filter(pk=mcn_to_delete.pk).exists())
        self.assertEqual(response['HX-Trigger'], 'refreshMaterialCreditNoteList')

    def test_hx_request_header_handling(self):
        """Test that non-HTMX requests behave as standard Django redirects."""
        data = {
            'mcn_number': 'MCN-NON-HTMX',
            'mcn_date': '2023-05-01',
            'party_name': 'Non-HTMX Test',
            'total_amount': '123.45',
        }
        response = self.client.post(reverse('materialcreditnote_add'), data) # No HTTP_HX_REQUEST
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('materialcreditnote_list'))
        self.assertTrue(MaterialCreditNote.objects.filter(mcn_number='MCN-NON-HTMX').exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for All Dynamic Interactions:**
    *   Loading the list view (`materialcreditnote_list`) initiates an `hx-get` request to `materialcreditnote_table` to load the actual DataTables content. This makes the list view responsive to updates.
    *   "Add New" button uses `hx-get` to fetch the add form (`materialcreditnote_add`) into a modal content div.
    *   "Edit" buttons use `hx-get` to fetch the edit form (`materialcreditnote_edit/<pk>/`) into the modal content div.
    *   "Delete" buttons use `hx-get` to fetch the delete confirmation (`materialcreditnote_delete/<pk>/`) into the modal content div.
    *   Form submissions (`hx-post`) are configured with `hx-swap="none"` and return a `204 No Content` status with an `HX-Trigger` header (`refreshMaterialCreditNoteList`). This tells HTMX to do nothing with the response body but trigger a custom event.
    *   The `list.html` template listens for the `refreshMaterialCreditNoteList` event on the body and re-fetches the `materialcreditnote_table` to update the list.
*   **Alpine.js for UI State Management:**
    *   While not heavily used in this specific implementation due to HTMX's capabilities, Alpine.js can be used for more complex client-side state, e.g., showing/hiding elements based on user interaction *within* a loaded HTMX partial.
    *   For the modal, `_hyperscript` (`_="on click add .is-active to #modal"`) is used for simple toggle logic, which is often sufficient for basic modal behavior with HTMX.
*   **DataTables for List Views:**
    *   The `_materialcreditnote_table.html` partial contains the `<table>` element that DataTables will operate on.
    *   The DataTables initialization script is included within this partial and in the `list.html`'s `htmx:afterSettle` event listener to ensure it re-initializes correctly whenever the table content is swapped in. This provides client-side searching, sorting, and pagination without server-side processing for typical list views.
*   **No Custom JavaScript (Beyond Libraries):** All dynamic behavior is achieved through HTMX attributes and minimal `_hyperscript` directives for UI toggles, adhering to the "no additional JavaScript" principle. jQuery is only used for DataTables library.
*   **Full Page Reloads Avoided:** All CRUD operations and list refreshes occur dynamically via HTMX, providing a Single Page Application (SPA)-like experience without a heavy frontend framework.

## Final Notes

*   **Placeholders:** Replace all placeholder instructions and example data with actual values from your legacy ASP.NET application where identified (e.g., specific field lengths, default values, complex business logic).
*   **DRY Principles:** The use of partial templates (`_materialcreditnote_table.html`, `_materialcreditnote_form.html`, etc.) ensures that reusable components are defined once and included where needed, reducing code duplication.
*   **Separation of Concerns:** Business logic (like `calculate_tax` in the model) is strictly kept in the `MaterialCreditNote` model, while views remain thin, only handling HTTP requests and responses. HTML is completely separated into templates.
*   **Comprehensive Testing:** The provided unit and integration tests are critical. As you add more complex business logic to the models or specific validations to forms, corresponding tests must be expanded to maintain high coverage and ensure correctness.
*   **Deployment:** Remember to configure your Django project's `settings.py` for database connection, installed apps (`'inventory'`), static files, and include `inventory.urls` in your project's main `urls.py`. You'll also need to ensure `django.contrib.messages` is properly configured for success messages.
*   **Database Synchronization:** Since `managed = False` is used, Django will assume the `tblMaterialCreditNote` table already exists in your database with the specified column names (`db_column`). Ensure your database schema matches the model definition for successful migration.