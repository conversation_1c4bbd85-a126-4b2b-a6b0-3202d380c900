This comprehensive modernization plan outlines the strategic transition of your existing ASP.NET application, specifically the Material Issue Note (MIN) search and list functionality, to a modern Django-based solution. Our approach prioritizes automated conversion, clean architecture, and enhanced user experience through contemporary web technologies.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several database tables to retrieve and filter Material Requisition Notes (MRS). These tables and their primary fields are:

*   **`tblInv_MaterialRequisition_Master`**: This is the core table representing the Material Requisition Notes.
    *   `Id` (Primary Key, integer)
    *   `SysDate` (Date/Time of creation)
    *   `FinYearId` (Foreign Key, links to `tblFinancial_master`)
    *   `MRSNo` (Material Requisition Number, string)
    *   `CompId` (Company ID, integer)
    *   `SessionId` (Foreign Key, links to `tblHR_OfficeStaff` to identify the employee who generated the MRS)
*   **`tblInv_MaterialRequisition_Details`**: Contains details for each Material Requisition, specifically the requested quantity.
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key, links to `tblInv_MaterialRequisition_Master.Id`)
    *   `ReqQty` (Requested Quantity, decimal)
*   **`tblFinancial_master`**: Stores financial year information.
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (Financial Year, e.g., "2023-2024", string)
    *   `CompId` (Company ID, integer)
*   **`tblHR_OfficeStaff`**: Contains employee details.
    *   `EmpId` (Primary Key, integer)
    *   `Title` (Employee Title, e.g., "Mr.", "Ms.", string)
    *   `EmployeeName` (Employee's full name, string)
    *   `CompId` (Company ID, integer)
*   **`tblInv_MaterialIssue_Master`**: Records details of material issues against requisitions.
    *   `Id` (Primary Key, integer)
    *   `MRSId` (Foreign Key, links to `tblInv_MaterialRequisition_Master.Id` - crucial for linking issues to requisitions)
    *   `MRSNo` (Material Requisition Number, string - potentially redundant but used in joins)
    *   `CompId` (Company ID, integer)
*   **`tblInv_MaterialIssue_Details`**: Contains the quantity of materials actually issued.
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key, links to `tblInv_MaterialIssue_Master.Id`)
    *   `IssueQty` (Issued Quantity, decimal)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business rules from the ASP.NET code.

**Instructions:**

*   **Read (Display and Filter):**
    *   The primary function is to display a list of "Material Requisition Notes" that have an *outstanding balance*. This means the total quantity issued against a requisition is less than the total quantity requested.
    *   Users can search or filter this list by:
        *   "MRS No" (Material Requisition Number).
        *   "Employee Name" (the person who generated the requisition), using an auto-completion feature.
    *   The list supports pagination, showing 15 records per page.
    *   The displayed data includes a serial number, financial year, MRS No, date of requisition, and the name of the employee who generated it.
*   **Navigation (Select Action):**
    *   Each row in the list has a "Select" button. Clicking this button redirects the user to a "Material Issue Note Details" page, passing along the `Id`, `MRSNo`, and `FinYearId` of the selected requisition. This implies a drill-down capability to manage issues for a specific requisition.
*   **Data Aggregation and Business Logic:**
    *   The system calculates the total `ReqQty` (requested quantity) from `tblInv_MaterialRequisition_Details` and `IssuedQty` (issued quantity) from `tblInv_MaterialIssue_Details` (linked via `tblInv_MaterialIssue_Master`) for each `MaterialRequisitionMaster`.
    *   Only `MaterialRequisitionMaster` records where `ReqQty > IssuedQty` (i.e., `BalQty > 0`) are displayed. This is a critical filtering rule.
    *   Filtering also considers the `FinYearId` (records up to the current financial year) and `CompId` (company ID).

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles in the user interface.

**Instructions:**

*   **Search/Filter Controls:**
    *   A `DropDownList` (dropdown) to select the search criterion: "Employee Name" or "MRS No". This dynamically controls the visibility of the corresponding input field.
    *   Two `TextBox` controls: one for "MRS No" and one for "Employee Name".
    *   The "Employee Name" `TextBox` is enhanced with an `AutoCompleteExtender` for predictive text input.
    *   A "Search" `Button` to trigger the filtering and refresh the list.
*   **Data Display Grid:**
    *   A `GridView` control that displays the filtered list of Material Requisition Notes.
    *   It shows columns for "SN" (Serial Number), "Fin Year", "MRS No", "Date", and "Gen. By" (Generated By).
    *   Each row includes a "Select" `LinkButton` for drill-down.
    *   The grid handles client-side pagination.
*   **Overall Layout:**
    *   The page has a header indicating "Material Issue Note [MIN] - New".
    *   Styling is applied using CSS files.

### Step 4: Generate Django Code

We will structure the Django application as `inventory`, containing models, forms, views, templates, and URLs to replicate and modernize the existing functionality.

#### 4.1 Models

The models map directly to your existing database tables. The crucial business logic for determining outstanding requisitions will be embedded within a custom Manager and QuerySet for the `MaterialRequisitionMaster` model, embodying the "fat model" principle.

```python
# inventory/models.py
from django.db import models
from django.db.models import Sum, F, Q # Q for complex lookups

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master.
    Holds information about financial years.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50) # e.g., "2023-2024"
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class OfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff.
    Holds employee details, used for "Generated By" field and autocomplete.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}".strip() # Combines title and name

class MaterialRequisitionQuerySet(models.QuerySet):
    """
    Custom QuerySet to encapsulate logic for calculating quantities and filtering.
    """
    def with_quantities(self):
        """
        Annotates the queryset with total requisition quantity and total issued quantity.
        Uses F() expressions for efficient database-level calculations.
        """
        return self.annotate(
            requisition_qty=Sum('materialrequisitiondetail__req_qty'),
            # Sum issued_qty from MaterialIssueDetail linked via MaterialIssueMaster,
            # ensuring it's tied to the correct MaterialRequisitionMaster.
            issued_qty=Sum(
                F('materialissuemaster__materialissuedetail__issue_qty'),
                filter=Q(
                    materialissuemaster__mrs_id=F('id') # Explicitly join on MRSId in MaterialIssueMaster
                )
            )
        )

    def outstanding_requisitions(self, financial_year_id, company_id):
        """
        Filters Material Requisition Masters to show only those with an outstanding balance.
        """
        return self.filter(
            fin_year__fin_year_id__lte=financial_year_id, # Requisitions up to the current financial year
            comp_id=company_id
        ).with_quantities().filter(
            # Crucial business logic: issued_qty is less than requisition_qty.
            # Coalesce issued_qty to 0 if NULL to handle cases with no issues yet.
            requisition_qty__gt=F('issued_qty') # Filters where ReqQty > IssuedQty (balance > 0)
        ).order_by('-mrs_no') # Sorts by MRSNo Desc, as in original ASP.NET

class MaterialRequisitionManager(models.Manager):
    """
    Custom Manager to provide easily callable methods for complex queries.
    """
    def get_queryset(self):
        return MaterialRequisitionQuerySet(self.model, using=self._db)

    def get_outstanding_requisitions(self, financial_year_id, company_id, mrs_no=None, emp_id=None):
        """
        Retrieves outstanding Material Requisitions with optional filters.
        This method mirrors the ASP.NET fillgrid logic.
        """
        queryset = self.get_queryset().outstanding_requisitions(financial_year_id, company_id)

        if mrs_no:
            queryset = queryset.filter(mrs_no=mrs_no)
        if emp_id:
            queryset = queryset.filter(session_id=emp_id) # SessionId links to EmpId from OfficeStaff

        return queryset

class MaterialRequisitionMaster(models.Model):
    """
    Maps to tblInv_MaterialRequisition_Master.
    Represents the main Material Requisition Note.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate')
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='material_requisitions')
    mrs_no = models.CharField(db_column='MRSNo', max_length=50)
    session_id = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='material_requisitions_generated')
    comp_id = models.IntegerField(db_column='CompId')

    objects = MaterialRequisitionManager() # Assign our custom manager

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Note'
        verbose_name_plural = 'Material Requisition Notes'

    def __str__(self):
        return self.mrs_no

    @property
    def formatted_date(self):
        """Returns the system date formatted as DD/MM/YYYY."""
        return self.sys_date.strftime('%d/%m/%Y')

    @property
    def generated_by(self):
        """Returns the name of the employee who generated the requisition."""
        return str(self.session_id) # Uses OfficeStaff.__str__ for formatting


class MaterialRequisitionDetail(models.Model):
    """
    Maps to tblInv_MaterialRequisition_Details.
    Stores details for each requisition, including requested quantities.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(MaterialRequisitionMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='materialrequisitiondetail_set')
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

class MaterialIssueMaster(models.Model):
    """
    Maps to tblInv_MaterialIssue_Master.
    Records master details for material issues.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrs_id = models.ForeignKey(MaterialRequisitionMaster, on_delete=models.DO_NOTHING, db_column='MRSId', related_name='materialissuemaster_set')
    mrs_no = models.CharField(db_column='MRSNo', max_length=50) # Included for direct mapping if existing in DB
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Master'
        verbose_name_plural = 'Material Issue Masters'

class MaterialIssueDetail(models.Model):
    """
    Maps to tblInv_MaterialIssue_Details.
    Stores details for each material issue, including issued quantities.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(MaterialIssueMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='materialissuedetail_set')
    issue_qty = models.DecimalField(db_column='IssueQty', max_digits=18, decimal_places=3)
    # The original ASP.NET code had commented-out logic referring to MRSId on this table
    # Sticking to the working SQL, which links via MaterialIssueMaster.MRSId to MaterialRequisitionMaster.Id.
    # If this column exists and is needed, it would be another ForeignKey.
    # mrs_detail_id = models.IntegerField(db_column='MRSId', null=True, blank=True) # Uncomment if actually needed.

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Details'
        verbose_name = 'Material Issue Detail'
        verbose_name_plural = 'Material Issue Details'
```

#### 4.2 Forms

A Django `Form` will be used to handle the search inputs, replacing the ASP.NET DropDownList and TextBoxes.

```python
# inventory/forms.py
from django import forms

class MaterialRequisitionSearchForm(forms.Form):
    """
    Form for filtering Material Requisition Notes.
    Mirrors the DrpField, TxtMrs, and TxtEmpName controls.
    """
    SEARCH_FIELD_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'MRS No'),
    ]

    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-ref': 'searchField', # Alpine.js reference
        }),
        label="Search By"
    )

    mrs_no = forms.CharField(
        required=False,
        label="MRS No",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter MRS No',
        })
    )

    employee_name = forms.CharField(
        required=False,
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'autocomplete': 'off', # Prevent browser autocomplete
            'hx-get': '/inventory/autocomplete/employee/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:300ms, search', # Trigger on keyup after delay or explicit search
            'hx-target': '#employee-suggestions', # Target div for suggestions
            'hx-swap': 'innerHTML', # Replace content
            'hx-indicator': '#autocomplete-spinner', # Show a loading spinner
            '@focus': 'showSuggestions()', # Alpine.js to show suggestions on focus
            '@blur': 'hideSuggestions($event)' # Alpine.js to hide on blur
        }),
    )

    employee_id = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput() # Stores the selected employee's ID for backend query
    )

    def clean(self):
        """
        Custom cleaning logic for search parameters.
        Ensures appropriate fields are used based on the search_field selection.
        """
        cleaned_data = super().clean()
        search_field = cleaned_data.get('search_field')
        mrs_no = cleaned_data.get('mrs_no')
        employee_name = cleaned_data.get('employee_name')
        employee_id = cleaned_data.get('employee_id')

        if search_field == '1' and not mrs_no:
            # Optionally add validation if MRS No is required when selected
            # self.add_error('mrs_no', 'MRS No is required when "MRS No" is selected.')
            pass
        elif search_field == '0' and not (employee_name or employee_id):
            # Optionally add validation if Employee Name/ID is required when selected
            # self.add_error('employee_name', 'Employee Name is required when "Employee Name" is selected.')
            pass
        return cleaned_data
```

#### 4.3 Views

The views will be thin, delegating complex data retrieval and business logic to the models and managers. We'll use Django's Class-Based Views (CBVs) for simplicity and structure, combined with HTMX for dynamic partial updates.

```python
# inventory/views.py
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.db.models import Q
from .models import MaterialRequisitionMaster, OfficeStaff
from .forms import MaterialRequisitionSearchForm
import json # Potentially for session handling or complex data structures

class MaterialRequisitionListView(TemplateView):
    """
    Main page view for Material Issue Note search and list.
    Corresponds to MaterialIssueNote_MIN_New.aspx.
    This view primarily renders the search form and the container for the HTMX-loaded table.
    """
    template_name = 'inventory/materialrequisition/list.html'

    def get_context_data(self, **kwargs):
        """Initializes the search form."""
        context = super().get_context_data(**kwargs)
        context['form'] = MaterialRequisitionSearchForm(self.request.GET) # Pre-populate form from URL params if present
        return context

class MaterialRequisitionTablePartialView(ListView):
    """
    Renders only the table content, designed to be loaded dynamically via HTMX.
    This view contains the core logic for fetching and filtering the data,
    mirroring the fillgrid method in ASP.NET.
    """
    model = MaterialRequisitionMaster
    template_name = 'inventory/materialrequisition/_materialrequisition_table.html'
    context_object_name = 'material_requisitions'
    paginate_by = 15 # Matches the PageSize of the ASP.NET GridView

    def get_queryset(self):
        """
        Retrieves the filtered queryset of outstanding material requisitions.
        Simulates ASP.NET session variables for current financial year and company ID.
        """
        # In a production Django app, these would come from:
        # request.user.profile (if user-specific), request.session, or Django settings.
        # For this migration, we'll assume they are stored in the session or a mechanism provides them.
        financial_year_id = self.request.session.get('finyear', 2023) # Default for testing
        company_id = self.request.session.get('compid', 1) # Default for testing

        form = MaterialRequisitionSearchForm(self.request.GET)
        form.is_valid() # Validate the form to access cleaned_data

        mrs_no = form.cleaned_data.get('mrs_no')
        employee_id = form.cleaned_data.get('employee_id') # Get the actual EmpId from the hidden field

        # The core business logic for filtering outstanding requisitions
        # and applying search criteria is now encapsulated in the model manager.
        queryset = MaterialRequisitionMaster.objects.get_outstanding_requisitions(
            financial_year_id,
            company_id,
            mrs_no=mrs_no,
            emp_id=employee_id
        )
        return queryset

class EmployeeAutoCompleteView(View):
    """
    Provides JSON responses for employee name autocomplete.
    Corresponds to the GetCompletionList WebMethod in ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '') # 'term' is a common parameter for autocomplete libraries
        # Simulate company ID from session
        company_id = request.session.get('compid', 1) # Default for testing

        if prefix_text:
            employees = OfficeStaff.objects.filter(
                comp_id=company_id,
                employee_name__istartswith=prefix_text # Case-insensitive starts-with
            ).values('emp_id', 'employee_name', 'title')[:10] # Limit results for performance

            suggestions = []
            for emp in employees:
                # Format: "Title. EmployeeName" as in ASP.NET's "Gen. By" column
                suggestions.append({
                    'id': emp['emp_id'],
                    'value': f"{emp['title'] or ''} {emp['employee_name']}".strip()
                })
            return JsonResponse(suggestions, safe=False)
        return JsonResponse([], safe=False)

class MaterialIssueNoteDetailsRedirectView(View):
    """
    Handles the "Select" button click and redirects to the details page.
    Corresponds to GridView2_RowCommand and Response.Redirect logic.
    This view will issue an HX-Redirect header for HTMX-driven navigation.
    """
    def get(self, request, *args, **kwargs):
        mrs_id = kwargs.get('pk')
        mrs_no = request.GET.get('mrs_no', '')
        fin_year_id = request.GET.get('fyid', '')

        # Construct the redirect URL for the "details" page.
        # 'material_issue_note_details_stub' is a placeholder and should be replaced
        # with the actual URL pattern for your Material Issue Note Details page.
        # The ModId and SubModId are passed as query parameters, similar to ASP.NET.
        from urllib.parse import urlencode
        redirect_url = reverse_lazy('inventory:material_issue_note_details_stub') # Placeholder for the actual details URL

        query_params = {
            'Id': mrs_id,
            'ModId': 9, # Matching original ASP.NET ModId
            'SubModId': 41, # Matching original ASP.NET SubModId
            'MRSNo': mrs_no,
            'FYId': fin_year_id
        }
        redirect_url = f"{redirect_url}?{urlencode(query_params)}"

        # For HTMX-driven full page navigation, respond with HX-Redirect header.
        return HttpResponse(status=204, headers={'HX-Redirect': str(redirect_url)})
```

#### 4.4 Templates

Templates will leverage DRY principles, extending `core/base.html` and using partials for HTMX-loaded content. Tailwind CSS will be used for modern styling, and DataTables for enhanced table functionality.

```html
{# inventory/materialrequisition/list.html #}
{% extends 'core/base.html' %} {# Assumes core/base.html exists for main layout #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 bg-white p-4 rounded-lg shadow">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Material Issue Note [MIN] - New</h2>
        
        {# Search Form - Uses HTMX to dynamically update the table below #}
        <form id="searchForm" 
              class="flex flex-col md:flex-row gap-4 w-full md:w-auto" 
              hx-get="{% url 'inventory:material_requisition_table' %}" 
              hx-target="#material-requisition-table-container" 
              hx-swap="innerHTML" 
              hx-trigger="submit, DrpFieldChanged from:body, keyup changed delay:500ms from:#id_mrs_no"> {# Trigger on submit, custom event, or MRS No keyup #}
            {% csrf_token %}
            <div class="flex-grow">
                <label for="{{ form.search_field.id_for_label }}" class="sr-only">Search By</label>
                {{ form.search_field }} {# Dropdown for search field selection #}
            </div>
            
            {# Conditional rendering of search inputs based on dropdown selection using Alpine.js #}
            <div class="flex-grow relative" 
                 x-data="{ showMrs: '{{ form.search_field.value|default:'0' }}' == '1', showEmp: '{{ form.search_field.value|default:'0' }}' == '0' }" 
                 x-init="$watch('$refs.searchField.value', value => { 
                     showMrs = value == '1'; 
                     showEmp = value == '0'; 
                     // Clear the other field when switching search type
                     if (showMrs) { $refs.employeeNameInput.value = ''; document.getElementById('id_employee_id').value = ''; }
                     else { $refs.mrsNoInput.value = ''; }
                 });">
                
                {# Hidden element to link Alpine.js watch to the form field's actual value #}
                <template x-ref="searchField" x-model="form.search_field" hidden></template>

                <div x-show="showMrs" class="w-full">
                    <label for="{{ form.mrs_no.id_for_label }}" class="sr-only">MRS No</label>
                    {{ form.mrs_no|attr:"x-ref:mrsNoInput" }} {# MRS No input #}
                </div>
                <div x-show="showEmp" class="w-full" x-data="materialRequisitionPage()"> {# Nested Alpine component for autocomplete #}
                    <label for="{{ form.employee_name.id_for_label }}" class="sr-only">Employee Name</label>
                    {{ form.employee_name|attr:"x-ref:employeeNameInput" }} {# Employee Name input with HTMX attributes #}
                    <div id="employee-suggestions" 
                         class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden overflow-y-auto max-h-48"
                         hx-ext="json-enc" {# For sending JSON if needed (not strictly for this autocomplete) #}
                         x-ref="suggestionsContainer" {# Alpine.js reference for suggestions div #}
                         @keydown.down.prevent="selectedIndex = Math.min(selectedIndex + 1, $refs.suggestionsContainer.children.length - 1); $refs.suggestionsContainer.children[selectedIndex].scrollIntoView({ block: 'nearest' });" 
                         @keydown.up.prevent="selectedIndex = Math.max(selectedIndex - 1, 0); $refs.suggestionsContainer.children[selectedIndex].scrollIntoView({ block: 'nearest' });" 
                         @keydown.enter.prevent="if (selectedIndex >= 0) $refs.suggestionsContainer.children[selectedIndex].click();">
                        {# Autocomplete suggestions will be loaded here via HTMX #}
                    </div>
                    <span id="autocomplete-spinner" class="htmx-indicator ml-2">
                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </span>
                </div>
            </div>
            
            {# Hidden input for employee_id to be populated by Alpine.js from autocomplete selection #}
            {{ form.employee_id }}

            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Search
            </button>
        </form>
    </div>
    
    {# Container for the HTMX-loaded DataTables content #}
    <div id="material-requisition-table-container" 
         hx-trigger="load, reloadMaterialRequisitionList from:body" 
         hx-get="{% url 'inventory:material_requisition_table' %}" 
         hx-target="this" 
         hx-swap="innerHTML"
         class="bg-white p-4 rounded-lg shadow">
        <!-- Initial loading state, replaced by HTMX content -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Requisitions...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
{# Include DataTables and Alpine.js CDNs in base.html as per guidelines #}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('materialRequisitionPage', () => ({
            selectedIndex: -1, // For keyboard navigation in autocomplete
            
            init() {
                // Dispatch event when dropdown changes, triggering HTMX form submission
                this.$watch('$refs.searchField.value', value => {
                    this.$dispatch('DrpFieldChanged');
                });
            },
            
            selectEmployee(empName, empId) {
                document.getElementById('id_employee_name').value = empName;
                document.getElementById('id_employee_id').value = empId;
                document.getElementById('employee-suggestions').innerHTML = ''; // Clear suggestions
                document.getElementById('employee-suggestions').classList.add('hidden'); // Hide suggestions
                htmx.trigger(document.getElementById('searchForm'), 'submit'); // Trigger form submission
            },

            // Auto-hide autocomplete suggestions if focus leaves the input or suggestions
            hideSuggestions(event) {
                if (!event.relatedTarget || (!this.$refs.suggestionsContainer.contains(event.relatedTarget) && !this.$refs.employeeNameInput.contains(event.relatedTarget))) {
                    this.$refs.suggestionsContainer.classList.add('hidden');
                }
            },
            showSuggestions() {
                // Only show suggestions container if there's content inside it
                if (this.$refs.suggestionsContainer.children.length > 0 && this.$refs.suggestionsContainer.innerText.trim() !== 'No suggestions') {
                    this.$refs.suggestionsContainer.classList.remove('hidden');
                }
            }
        }));
    });

    // Event listener for HTMX afterSwap to initialize DataTables on new table content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'material-requisition-table-container') {
            // Check if DataTable is already initialized on the table to avoid re-initialization errors
            if ($.fn.DataTable.isDataTable('#materialRequisitionTable')) {
                $('#materialRequisitionTable').DataTable().destroy(); // Destroy existing instance
            }
            $('#materialRequisitionTable').DataTable({
                "pageLength": 15, // Matches ASP.NET default page size
                "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
                "pagingType": "full_numbers", // Provides full pagination controls
                "dom": '<"flex justify-between items-center mb-4"lf><"overflow-x-auto"t><"flex justify-between items-center mt-4"ip>' // Custom DOM for styling
            });
        }
        // Handle autocomplete suggestions visibility after they are swapped
        if (event.detail.target.id === 'employee-suggestions') {
            const suggestionsDiv = document.getElementById('employee-suggestions');
            if (suggestionsDiv.children.length > 0 && suggestionsDiv.innerText.trim() !== 'No suggestions') {
                suggestionsDiv.classList.remove('hidden');
            } else {
                suggestionsDiv.classList.add('hidden');
            }
        }
    });
</script>
{% endblock %}
```

```html
{# inventory/materialrequisition/_materialrequisition_table.html #}
{% comment %}
This partial template renders the Material Requisition list table.
It is loaded into 'material-requisition-table-container' via HTMX.
{% endcomment %}

{% if page_obj.object_list %} {# Use page_obj.object_list if using pagination #}
<div class="overflow-x-auto">
    <table id="materialRequisitionTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for req in page_obj.object_list %} {# Iterate over paginated objects #}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ page_obj.start_index|add:forloop.counter0 }}</td> {# Corrected SN for pagination #}
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ req.fin_year.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ req.mrs_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ req.formatted_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ req.generated_by }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button 
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-sm"
                        hx-get="{% url 'inventory:material_issue_note_details_redirect' pk=req.id %}?mrs_no={{ req.mrs_no }}&fyid={{ req.fin_year.fin_year_id }}"
                        hx-trigger="click"
                        hx-swap="none" {# No content swap, just triggering redirect header #}
                        title="Select this Material Requisition">
                        Select
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center py-8">
    <p class="text-lg text-red-700 font-semibold">No data to display !</p>
</div>
{% endif %}

{# Script for DataTable initialization. Note: This script block won't directly execute
   when HTMX swaps content. Initialization is handled in list.html via htmx:afterSwap. #}
<script>
    // This script is here primarily for reference and does not execute directly due to HTMX swap.
</script>
```

```html
{# inventory/materialrequisition/_employee_suggestions.html #}
{% comment %}
This partial template renders the autocomplete suggestions for employee names.
It is loaded into '#employee-suggestions' by HTMX.
{% endcomment %}

{% if suggestions %}
    {% for suggestion in suggestions %}
    <div class="p-2 cursor-pointer hover:bg-gray-100"
         @click="selectEmployee('{{ suggestion.value|escapejs }}', '{{ suggestion.id }}')" {# Escapejs for safety #}
         tabindex="0"
         role="option"
         :class="{ 'bg-blue-500 text-white': selectedIndex === {{ forloop.counter0 }} }">
        {{ suggestion.value }}
    </div>
    {% endfor %}
{% else %}
    <div class="p-2 text-gray-500">No suggestions</div>
{% endif %}
```

#### 4.5 URLs

URL patterns define the endpoints for accessing your Django views.

```python
# inventory/urls.py
from django.urls import path
from django.http import HttpResponse # For placeholder stub
from .views import (
    MaterialRequisitionListView,
    MaterialRequisitionTablePartialView,
    EmployeeAutoCompleteView,
    MaterialIssueNoteDetailsRedirectView,
)

app_name = 'inventory' # Define application namespace for reverse lookups

urlpatterns = [
    # Main page for Material Issue Note search and list
    path('material-issue-note/new/', MaterialRequisitionListView.as_view(), name='material_requisition_list'),

    # HTMX endpoint to load/refresh the table content
    path('material-issue-note/new/table/', MaterialRequisitionTablePartialView.as_view(), name='material_requisition_table'),

    # API endpoint for employee name autocomplete suggestions
    path('autocomplete/employee/', EmployeeAutoCompleteView.as_view(), name='employee_autocomplete'),

    # Endpoint to handle the "Select" action and redirect to the details page
    path('material-issue-note/select/<int:pk>/', MaterialIssueNoteDetailsRedirectView.as_view(), name='material_issue_note_details_redirect'),

    # Placeholder URL for the Material Issue Note Details page.
    # This should be replaced with the actual URL from the target application/module.
    # For now, it's a simple HttpResponse to prevent a "NoReverseMatch" error.
    path('material-issue-note/details-stub/', lambda request: HttpResponse("Material Issue Note Details Page (Placeholder)"), name='material_issue_note_details_stub'),
]

# To integrate this app's URLs into your Django project,
# add the following to your project's main urls.py (e.g., myproject/urls.py):
# from django.urls import path, include
# urlpatterns = [
#     path('inventory/', include('inventory.urls', namespace='inventory')),
#     # ... other project URLs ...
# ]
```

#### 4.6 Tests

Comprehensive unit tests for models and integration tests for views are essential to ensure the correctness and reliability of the migrated application.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.http import JsonResponse, HttpResponse # Import HttpResponse for HX-Redirect check
from django.db.models import Sum
from unittest.mock import patch, MagicMock # For mocking session or external dependencies
from .models import (
    FinancialYear,
    OfficeStaff,
    MaterialRequisitionMaster,
    MaterialRequisitionDetail,
    MaterialIssueMaster,
    MaterialIssueDetail,
)
from .forms import MaterialRequisitionSearchForm
from decimal import Decimal
import datetime # For sys_date field

class ModelTestBase(TestCase):
    """Base class for setting up common test data across model and view tests."""
    @classmethod
    def setUpTestData(cls):
        # Create FinancialYear records
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-2024', comp_id=1)
        cls.fin_year_2022 = FinancialYear.objects.create(fin_year_id=2022, fin_year='2022-2023', comp_id=1)
        cls.fin_year_2024 = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025', comp_id=1)

        # Create OfficeStaff records
        cls.emp_john_doe = OfficeStaff.objects.create(emp_id=101, title='Mr.', employee_name='John Doe', comp_id=1)
        cls.emp_jane_smith = OfficeStaff.objects.create(emp_id=102, title='Ms.', employee_name='Jane Smith', comp_id=1)
        cls.emp_peter_jones = OfficeStaff.objects.create(emp_id=103, title='Dr.', employee_name='Peter Jones', comp_id=2) # Different company

        # Create Material Requisition Master records
        # MRS 1: Outstanding (Requested 10, Issued 5)
        cls.mrs_outstanding = MaterialRequisitionMaster.objects.create(
            id=1, sys_date=datetime.datetime(2023, 1, 15, 10, 0, 0), fin_year=cls.fin_year_2023,
            mrs_no='MRS001', session_id=cls.emp_john_doe, comp_id=1
        )
        MaterialRequisitionDetail.objects.create(id=101, m_id=cls.mrs_outstanding, req_qty=Decimal('10.00'))
        issue_master_partial = MaterialIssueMaster.objects.create(id=1001, mrs_id=cls.mrs_outstanding, mrs_no='MRS001', comp_id=1)
        MaterialIssueDetail.objects.create(id=2001, m_id=issue_master_partial, issue_qty=Decimal('5.00'))

        # MRS 2: Fully Issued (Requested 20, Issued 20) - Should NOT appear in outstanding list
        cls.mrs_fully_issued = MaterialRequisitionMaster.objects.create(
            id=2, sys_date=datetime.datetime(2023, 2, 1, 11, 0, 0), fin_year=cls.fin_year_2023,
            mrs_no='MRS002', session_id=cls.emp_jane_smith, comp_id=1
        )
        MaterialRequisitionDetail.objects.create(id=102, m_id=cls.mrs_fully_issued, req_qty=Decimal('20.00'))
        issue_master_full = MaterialIssueMaster.objects.create(id=1002, mrs_id=cls.mrs_fully_issued, mrs_no='MRS002', comp_id=1)
        MaterialIssueDetail.objects.create(id=2002, m_id=issue_master_full, issue_qty=Decimal('20.00'))

        # MRS 3: No Issues yet (Requested 15, Issued 0) - Should appear
        cls.mrs_no_issue = MaterialRequisitionMaster.objects.create(
            id=3, sys_date=datetime.datetime(2023, 3, 10, 12, 0, 0), fin_year=cls.fin_year_2023,
            mrs_no='MRS003', session_id=cls.emp_john_doe, comp_id=1
        )
        MaterialRequisitionDetail.objects.create(id=103, m_id=cls.mrs_no_issue, req_qty=Decimal('15.00'))

        # MRS 4: From previous financial year (Requested 5, Issued 0) - Should appear if FinYearId <= current
        cls.mrs_prev_year = MaterialRequisitionMaster.objects.create(
            id=4, sys_date=datetime.datetime(2022, 11, 20, 9, 0, 0), fin_year=cls.fin_year_2022,
            mrs_no='MRS004', session_id=cls.emp_jane_smith, comp_id=1
        )
        MaterialRequisitionDetail.objects.create(id=104, m_id=cls.mrs_prev_year, req_qty=Decimal('5.00'))

        # MRS 5: From future financial year (Requested 8, Issued 0) - Should NOT appear if FinYearId > current
        cls.mrs_future_year = MaterialRequisitionMaster.objects.create(
            id=5, sys_date=datetime.datetime(2024, 5, 1, 14, 0, 0), fin_year=cls.fin_year_2024,
            mrs_no='MRS005', session_id=cls.emp_john_doe, comp_id=1
        )
        MaterialRequisitionDetail.objects.create(id=105, m_id=cls.mrs_future_year, req_qty=Decimal('8.00'))

        # MRS 6: Different Company (Requested 10, Issued 0) - Should NOT appear if CompId different
        cls.mrs_diff_company = MaterialRequisitionMaster.objects.create(
            id=6, sys_date=datetime.datetime(2023, 4, 1, 16, 0, 0), fin_year=cls.fin_year_2023,
            mrs_no='MRS006', session_id=cls.emp_peter_jones, comp_id=2
        )
        MaterialRequisitionDetail.objects.create(id=106, m_id=cls.mrs_diff_company, req_qty=Decimal('10.00'))


class MaterialRequisitionModelTest(ModelTestBase):
    def test_material_requisition_master_creation(self):
        self.assertEqual(MaterialRequisitionMaster.objects.count(), 6)
        mrs = MaterialRequisitionMaster.objects.get(id=1)
        self.assertEqual(mrs.mrs_no, 'MRS001')
        self.assertEqual(mrs.session_id.employee_name, 'John Doe')
        self.assertEqual(mrs.fin_year.fin_year, '2023-2024')

    def test_material_requisition_detail_creation(self):
        self.assertEqual(MaterialRequisitionDetail.objects.count(), 6)
        mrs_detail = MaterialRequisitionDetail.objects.get(id=101)
        self.assertEqual(mrs_detail.m_id.mrs_no, 'MRS001')
        self.assertEqual(mrs_detail.req_qty, Decimal('10.00'))

    def test_material_issue_master_creation(self):
        self.assertEqual(MaterialIssueMaster.objects.count(), 2)
        issue_master = MaterialIssueMaster.objects.get(id=1001)
        self.assertEqual(issue_master.mrs_id.mrs_no, 'MRS001')

    def test_material_issue_detail_creation(self):
        self.assertEqual(MaterialIssueDetail.objects.count(), 2)
        issue_detail = MaterialIssueDetail.objects.get(id=2001)
        self.assertEqual(issue_detail.m_id.mrs_id.mrs_no, 'MRS001')
        self.assertEqual(issue_detail.issue_qty, Decimal('5.00'))

    def test_material_requisition_properties(self):
        mrs = self.mrs_outstanding
        self.assertEqual(mrs.formatted_date, '15/01/2023')
        self.assertEqual(mrs.generated_by, 'Mr. John Doe')

    def test_outstanding_requisitions_manager_method_no_filters(self):
        # Assuming current financial year is 2023, company ID is 1
        outstanding_mrs = MaterialRequisitionMaster.objects.get_outstanding_requisitions(2023, 1)
        self.assertEqual(outstanding_mrs.count(), 3)
        self.assertIn(self.mrs_outstanding, outstanding_mrs)
        self.assertIn(self.mrs_no_issue, outstanding_mrs)
        self.assertIn(self.mrs_prev_year, outstanding_mrs) # MRS004 is from 2022 <= 2023

        # MRS002 (fully issued), MRS005 (future year), MRS006 (different company) should not be included
        self.assertNotIn(self.mrs_fully_issued, outstanding_mrs)
        self.assertNotIn(self.mrs_future_year, outstanding_mrs)
        self.assertNotIn(self.mrs_diff_company, outstanding_mrs)

    def test_outstanding_requisitions_manager_method_mrs_no_filter(self):
        filtered_by_mrs = MaterialRequisitionMaster.objects.get_outstanding_requisitions(2023, 1, mrs_no='MRS001')
        self.assertEqual(filtered_by_mrs.count(), 1)
        self.assertIn(self.mrs_outstanding, filtered_by_mrs)

    def test_outstanding_requisitions_manager_method_employee_id_filter(self):
        filtered_by_emp = MaterialRequisitionMaster.objects.get_outstanding_requisitions(2023, 1, emp_id=self.emp_john_doe.emp_id)
        self.assertEqual(filtered_by_emp.count(), 2)
        self.assertIn(self.mrs_outstanding, filtered_by_emp)
        self.assertIn(self.mrs_no_issue, filtered_by_emp)
        self.assertNotIn(self.mrs_prev_year, filtered_by_emp) # MRS004 is by Jane Smith

    def test_outstanding_requisitions_manager_method_combined_filters(self):
        filtered_both = MaterialRequisitionMaster.objects.get_outstanding_requisitions(2023, 1, mrs_no='MRS001', emp_id=self.emp_john_doe.emp_id)
        self.assertEqual(filtered_both.count(), 1)
        self.assertIn(self.mrs_outstanding, filtered_both)

    def test_outstanding_requisitions_manager_method_future_fin_year_filter(self):
        # If current financial year is 2022, MRS001 (2023) should not appear
        outstanding_mrs_2022 = MaterialRequisitionMaster.objects.get_outstanding_requisitions(2022, 1)
        self.assertEqual(outstanding_mrs_2022.count(), 1) # Only MRS004 from 2022 should be included
        self.assertIn(self.mrs_prev_year, outstanding_mrs_2022)
        self.assertNotIn(self.mrs_outstanding, outstanding_mrs_2022)


class MaterialRequisitionViewsTest(ModelTestBase):
    def setUp(self):
        super().setUp() # Call parent setup to get test data
        self.client = Client()
        # Mock session variables for financial year and company ID
        # In a real setup, this might be handled by login or specific middleware
        self.session = self.client.session
        self.session['finyear'] = 2023
        self.session['compid'] = 1
        self.session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:material_requisition_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisition/list.html')
        self.assertIsInstance(response.context['form'], MaterialRequisitionSearchForm)
        self.assertContains(response, 'Material Issue Note [MIN] - New')
        self.assertContains(response, 'id="material-requisition-table-container"')

    def test_table_partial_view_initial_load(self):
        # Simulate HTMX request by adding HTTP_HX_REQUEST header
        response = self.client.get(reverse('inventory:material_requisition_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisition/_materialrequisition_table.html')
        self.assertTrue('material_requisitions' in response.context)
        # Should show 3 outstanding MRS items for comp_id=1 and fin_year_id<=2023
        self.assertEqual(response.context['material_requisitions'].count(), 3)
        self.assertContains(response, 'MRS001')
        self.assertContains(response, 'MRS003')
        self.assertContains(response, 'MRS004')
        self.assertNotContains(response, 'MRS002') # Fully issued
        self.assertNotContains(response, 'MRS005') # Future year
        self.assertNotContains(response, 'MRS006') # Different company

    def test_table_partial_view_search_by_mrs_no(self):
        response = self.client.get(reverse('inventory:material_requisition_table'), {'search_field': '1', 'mrs_no': 'MRS001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRS001')
        self.assertNotContains(response, 'MRS003')
        self.assertNotContains(response, 'MRS004')
        self.assertEqual(response.context['material_requisitions'].count(), 1)

    def test_table_partial_view_search_by_employee_id(self):
        # The form passes employee_id, not employee_name for backend search
        response = self.client.get(reverse('inventory:material_requisition_table'), {'search_field': '0', 'employee_id': self.emp_john_doe.emp_id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRS001')
        self.assertContains(response, 'MRS003')
        self.assertNotContains(response, 'MRS004') # MRS004 is by Jane Smith
        self.assertEqual(response.context['material_requisitions'].count(), 2)

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('inventory:employee_autocomplete'), {'term': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, JsonResponse)
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['id'], self.emp_john_doe.emp_id)
        self.assertEqual(data[0]['value'], 'Mr. John Doe')

        response_jane = self.client.get(reverse('inventory:employee_autocomplete'), {'term': 'jane'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_jane.status_code, 200)
        self.assertEqual(len(response_jane.json()), 1)
        self.assertEqual(response_jane.json()[0]['id'], self.emp_jane_smith.emp_id)

        response_empty_term = self.client.get(reverse('inventory:employee_autocomplete'), {'term': 'xyz'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_empty_term.status_code, 200)
        self.assertEqual(len(response_empty_term.json()), 0)

        response_no_term_param = self.client.get(reverse('inventory:employee_autocomplete'), HTTP_HX_REQUEST='true')
        self.assertEqual(response_no_term_param.status_code, 200)
        self.assertEqual(len(response_no_term_param.json()), 0)

    def test_material_issue_note_details_redirect_view(self):
        mrs_id = self.mrs_outstanding.id
        mrs_no = self.mrs_outstanding.mrs_no
        fin_year_id = self.mrs_outstanding.fin_year.fin_year_id

        # Simulate an HTMX request that triggers a redirect
        response = self.client.get(
            reverse('inventory:material_issue_note_details_redirect', kwargs={'pk': mrs_id}),
            {'mrs_no': mrs_no, 'fyid': fin_year_id},
            HTTP_HX_REQUEST='true'
        )

        self.assertEqual(response.status_code, 204) # HTMX expects 204 for HX-Redirect
        self.assertTrue('HX-Redirect' in response.headers)

        expected_redirect_url_prefix = reverse('inventory:material_issue_note_details_stub')
        self.assertIn(expected_redirect_url_prefix, response.headers['HX-Redirect'])
        self.assertIn(f"Id={mrs_id}", response.headers['HX-Redirect'])
        self.assertIn(f"MRSNo={mrs_no}", response.headers['HX-Redirect'])
        self.assertIn(f"FYId={fin_year_id}", response.headers['HX-Redirect'])
        self.assertIn("ModId=9", response.headers['HX-Redirect'])
        self.assertIn("SubModId=41", response.headers['HX-Redirect'])

    def test_empty_data_template_display(self):
        # Clear all relevant data to simulate no results
        MaterialRequisitionMaster.objects.all().delete()
        MaterialRequisitionDetail.objects.all().delete()
        MaterialIssueMaster.objects.all().delete()
        MaterialIssueDetail.objects.all().delete()

        response = self.client.get(reverse('inventory:material_requisition_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')
        # Ensure that the table headers are not rendered when there is no data
        self.assertNotContains(response, '<th>MRS No</th>')
        self.assertNotContains(response, '<th>Actions</th>')
```

### Step 5: HTMX and Alpine.js Integration

This step describes how the frontend interactions are powered by HTMX and Alpine.js, moving away from ASP.NET's postback and traditional JavaScript.

*   **HTMX for Dynamic Content Loading:**
    *   The main `list.html` page uses `hx-get` on a `div` (`#material-requisition-table-container`) to load the `_materialrequisition_table.html` partial. This is triggered on `load` (initial page load) and by a custom `reloadMaterialRequisitionList` event (triggered by form submission or dropdown changes).
    *   The search `form` uses `hx-get` to submit its parameters to the `material_requisition_table` endpoint. Upon success, the response (the updated table partial) is swapped (`hx-swap="innerHTML"`) into `#material-requisition-table-container`.
    *   The dropdown (`id_search_field`) dispatches a custom event (`DrpFieldChanged`) via Alpine.js when its value changes. The search `form` listens for this event and triggers an `hx-get` submission, effectively replacing the ASP.NET `AutoPostBack`.
    *   The "Select" button on each table row uses `hx-get` to call the `material_issue_note_details_redirect` view. This view responds with an `HX-Redirect` header, which HTMX intercepts to perform a full page navigation, mimicking the ASP.NET `Response.Redirect`.
    *   The employee name input utilizes `hx-get` to fetch autocomplete suggestions from the `employee_autocomplete` endpoint. Suggestions are swapped into `#employee-suggestions`. `hx-trigger="keyup changed delay:300ms, search"` ensures suggestions are fetched efficiently as the user types.

*   **Alpine.js for UI State Management:**
    *   Alpine.js is used to manage the visibility of the "MRS No" and "Employee Name" input fields (`x-show`) based on the selected value in the "Search By" dropdown (`x-data` and `x-watch`). This replaces the ASP.NET `TxtMrs.Visible` and `TxtEmpName.Visible` logic.
    *   A dedicated Alpine component (`materialRequisitionPage`) handles the autocomplete interaction:
        *   It tracks `selectedIndex` for keyboard navigation (up/down arrows) within the autocomplete suggestions.
        *   It provides `selectEmployee` function to populate the employee name input and the hidden `employee_id` field when a suggestion is clicked, and then triggers the search form submission.
        *   `showSuggestions` and `hideSuggestions` methods control the visibility of the suggestions dropdown, replacing the AJAX Control Toolkit's behavior.
    *   Alpine is also used to clear the unused search input when switching search types.

*   **DataTables for List Views:**
    *   The `_materialrequisition_table.html` partial contains the HTML `table` element with `id="materialRequisitionTable"`.
    *   In `list.html`, a `document.body.addEventListener('htmx:afterSwap', ...)` is used to detect when the `material-requisition-table-container` has been updated by HTMX. Inside this listener, `$('#materialRequisitionTable').DataTable()` is called to initialize (or re-initialize) DataTables. This ensures that features like client-side search, sort, and pagination work correctly even after the table content is dynamically replaced.

By integrating these technologies, the modernized Django application provides a highly responsive, interactive, and efficient user experience without relying on traditional full-page reloads, significantly improving performance and user satisfaction compared to the legacy ASP.NET application.

## Final Notes

*   **Placeholder Replacement:** Ensure all placeholders like `[APP_NAME]`, `[MODEL_NAME]`, `[TABLE_NAME]`, etc., are replaced with their actual values from your project setup.
*   **Session Data:** The `financial_year_id` and `company_id` are currently mocked using `request.session.get`. In a real application, these should be securely managed, typically derived from the authenticated user's profile or a global context.
*   **Database Connection:** The Django `managed = False` and `db_table = '...'` in models means Django will use your existing database schema without attempting to create or modify tables. You need to configure your `settings.py` to connect to your SQL Server database (e.g., using `django-mssql-backend`).
*   **Styling:** The templates use Tailwind CSS classes. Ensure Tailwind CSS is set up and configured in your Django project.
*   **Extensibility:** The redirection to `MaterialIssueNote_MIN_New_Details.aspx` is mapped to a placeholder URL in Django (`material_issue_note_details_stub`). This `details` functionality should be migrated as a separate, comprehensive module following the same guidelines.
*   **Error Handling:** While `try-catch` blocks were prevalent in ASP.NET, Django handles exceptions differently. Proper error handling, including form validation messages and robust server-side error logging, should be implemented.
*   **Security:** Ensure that all user inputs are properly sanitized and validated to prevent SQL injection and other vulnerabilities (Django forms and ORM inherently handle much of this). Authentication and authorization (e.g., `LoginRequiredMixin`, `PermissionRequiredMixin`) should be applied to views as necessary.