## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code utilizes `tblMM_Supplier_master` for supplier data, as evident from the `sql` WebMethod. The `GetSupplier` stored procedure also interacts with supplier data, likely from the same table or a related view.
The `SearchGridView1` binds to `SupplierId`, `SupplierName`, and `FinYear`. The `DataKeyNames` is `SupplierId`. The `sql` method fetches `SupplierId` and `SupplierName`. `CompId` (Company ID) is passed as a parameter to the `GetSupplier` stored procedure and retrieved from session for the `sql` method, indicating it's a critical filtering column, likely present in the `tblMM_Supplier_master` table.

*   **TABLE_NAME**: `tblMM_Supplier_master`
*   **Columns Identified**:
    *   `SupplierId` (Primary Key, unique identifier)
    *   `SupplierName` (Text, for display and search)
    *   `FinYear` (Financial Year, likely Integer or Varchar)
    *   `CompId` (Company ID, Integer, for multi-tenancy/filtering)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The provided ASP.NET page is primarily for **Read** and **Search/Filter** operations for supplier data.
*   **Read (R):** Data is fetched using `GetSupplier` stored procedure and displayed in `SearchGridView1`. The `BindData` method is responsible for this.
*   **Search/Filter:** The `TxtSearchValue` (Supplier Name) and `Search` button trigger a search, filtering the displayed data. The `sql` WebMethod provides autocomplete functionality.
*   **Create (C), Update (U), Delete (D):** While not explicitly shown on *this* `.aspx` page, the `HyperLinkField` navigating to `SupplierChallan_New_Details.aspx` strongly implies that this is a companion page for managing (creating/editing) supplier details. For a complete modernization, we will infer and include full CRUD capabilities for the `Supplier` entity in Django, following the standard module structure.
*   **Pagination & Sorting:** Handled by `GridView` in ASP.NET, this will be managed by DataTables in Django.
*   **Session Management:** `compid` and `finyear` are retrieved from session, used for data filtering. This will be handled in Django views.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Analysis:**
The ASP.NET page contains common web form controls:
*   `TxtSearchValue` (TextBox with `AutoCompleteExtender`): Input field for searching by supplier name, offering suggestions as the user types.
*   `Search` (Button): Triggers the search operation based on the `TxtSearchValue`.
*   `SearchGridView1` (GridView): Displays the list of suppliers in a tabular format, supporting pagination, sorting, and linking to a detail page.
*   `Label2`: "Supplier Name" label for `TxtSearchValue`.

**Django equivalents:**
*   `TxtSearchValue` and `Search` button will be converted into an HTML form with an input field, utilizing HTMX for dynamic search and possibly an HTMX/Alpine.js powered autocomplete.
*   `SearchGridView1` will be replaced by an HTML `<table>` element, which will be initialized as a DataTables instance for client-side functionality. CRUD action buttons (Edit, Delete) will be added to each row, triggering HTMX modals.

### Step 4: Generate Django Code

We will create a Django application named `inventory` to house this module.

#### 4.1 Models (`inventory/models.py`)

This model will map directly to the existing `tblMM_Supplier_master` table. We assume `SupplierId` is the primary key.

```python
from django.db import models

class Supplier(models.Model):
    # SupplierId is assumed to be the primary key from the ASP.NET DataKeyNames
    # and its use in URL parameters.
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    financial_year = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

    # Business logic methods can be added here following the fat model principle.
    # For instance, a method to get active suppliers for the current company/financial year.
    @classmethod
    def get_suppliers_by_company_and_finyear(cls, company_id, fin_year, search_query=None):
        """
        Retrieves suppliers filtered by company, financial year, and an optional search query.
        This method encapsulates the logic previously in 'GetSupplier' stored procedure
        and 'BindData' function.
        """
        queryset = cls.objects.filter(company_id=company_id, financial_year=fin_year)
        if search_query:
            # Assuming search_query can be either supplier_id or supplier_name
            # The ASP.NET fun.getCode(TxtSearchValue.Text) implies parsing something like 'Name [ID]'
            # We'll try to match by ID if bracketed, else by name.
            if '[' in search_query and ']' in search_query:
                # Extract ID from "Name [ID]" format
                try:
                    parsed_id = search_query.split('[')[-1].strip(']')
                    queryset = queryset.filter(supplier_id__iexact=parsed_id)
                except IndexError:
                    queryset = queryset.filter(supplier_name__icontains=search_query)
            else:
                queryset = queryset.filter(supplier_name__icontains=search_query)
        return queryset

```

#### 4.2 Forms (`inventory/forms.py`)

```python
from django import forms
from .models import Supplier

class SupplierForm(forms.ModelForm):
    class Meta:
        model = Supplier
        fields = ['supplier_id', 'supplier_name', 'financial_year', 'company_id']
        widgets = {
            'supplier_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'supplier_id': 'Supplier Code',
            'supplier_name': 'Supplier Name',
            'financial_year': 'Financial Year',
            'company_id': 'Company ID',
        }
        
    def clean_supplier_id(self):
        # Ensure supplier_id is not changed on update and is unique on create.
        supplier_id = self.cleaned_data['supplier_id']
        if self.instance.pk and self.instance.pk != supplier_id:
            raise forms.ValidationError("Supplier Code cannot be changed.")
        elif not self.instance.pk and Supplier.objects.filter(supplier_id=supplier_id).exists():
            raise forms.ValidationError("Supplier Code already exists.")
        return supplier_id
```

#### 4.3 Views (`inventory/views.py`)

The views will handle session context (`compid`, `finyear`) and integrate HTMX for dynamic content loading.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from .models import Supplier
from .forms import SupplierForm
import re # For parsing supplier ID from autocomplete string

# Helper to get session values, mimicking ASP.NET Session usage
def get_session_context(request):
    comp_id = request.session.get('compid', None)
    fin_year = request.session.get('finyear', None)
    # Dummy values for demonstration if session not set
    if comp_id is None:
        comp_id = 1 # Default company ID for testing
        request.session['compid'] = comp_id
    if fin_year is None:
        fin_year = '2023-2024' # Default financial year for testing
        request.session['finyear'] = fin_year
    return comp_id, fin_year

class SupplierListView(ListView):
    model = Supplier
    template_name = 'inventory/supplier/list.html'
    context_object_name = 'suppliers'
    
    # We will fetch data via HTMX to a partial table, so the initial list view
    # might not need to pass the queryset directly.
    # However, for initial render or direct access, it's good practice.
    def get_queryset(self):
        company_id, fin_year = get_session_context(self.request)
        # Initial load might not have a search query
        return Supplier.get_suppliers_by_company_and_finyear(company_id, fin_year).order_by('supplier_name')

class SupplierTablePartialView(ListView):
    model = Supplier
    template_name = 'inventory/supplier/_supplier_table.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        company_id, fin_year = get_session_context(self.request)
        search_query = self.request.GET.get('search_value', '').strip()
        return Supplier.get_suppliers_by_company_and_finyear(company_id, fin_year, search_query).order_by('supplier_name')

    def render_to_response(self, context, **response_kwargs):
        # HTMX requests should only return the partial HTML
        return super().render_to_response(context, **response_kwargs)

class SupplierCreateView(CreateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'inventory/supplier/_supplier_form.html'
    success_url = reverse_lazy('supplier_list') # Not directly used with HX-Trigger

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        company_id, fin_year = get_session_context(self.request)
        # Pre-fill company_id and financial_year if necessary
        # Or make them hidden fields/non-editable in the form
        if 'initial' not in kwargs:
            kwargs['initial'] = {}
        kwargs['initial']['company_id'] = company_id
        kwargs['initial']['financial_year'] = fin_year
        return kwargs

    def form_valid(self, form):
        # Ensure company_id and financial_year are set if not part of form submission
        # Or if they are hidden fields
        company_id, fin_year = get_session_context(self.request)
        form.instance.company_id = company_id
        form.instance.financial_year = fin_year
        
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to indicate success and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response # Fallback for non-HTMX requests

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # Render the form again with errors for HTMX
            return HttpResponse(render_to_string(self.template_name, {'form': form}, self.request))
        return response

class SupplierUpdateView(UpdateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'inventory/supplier/_supplier_form.html'
    success_url = reverse_lazy('supplier_list') # Not directly used with HX-Trigger
    pk_url_kwarg = 'supplier_id' # Match URL kwarg for supplier_id

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, self.request))
        return response

class SupplierDeleteView(DeleteView):
    model = Supplier
    template_name = 'inventory/supplier/_supplier_confirm_delete.html'
    success_url = reverse_lazy('supplier_list') # Not directly used with HX-Trigger
    pk_url_kwarg = 'supplier_id' # Match URL kwarg for supplier_id

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierAutocompleteView(ListView):
    # This view replaces the ASP.NET 'sql' WebMethod for AutoCompleteExtender
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '').lower() # 'term' is common for autocomplete
        company_id, _ = get_session_context(request)
        
        # Filter for suppliers matching the prefix_text and company_id
        # Mimicking the ASP.NET logic of filtering by name starting with prefix
        queryset = Supplier.objects.filter(
            company_id=company_id,
            supplier_name__istartswith=prefix_text
        ).values('supplier_id', 'supplier_name')[:10] # Limit to 10 results as in ASP.NET

        results = []
        for obj in queryset:
            results.append(f"{obj['supplier_name']} [{obj['supplier_id']}]")
        
        # Return results as a JSON array, suitable for jQuery UI Autocomplete or similar HTMX patterns
        return JsonResponse(results, safe=False)

```

#### 4.4 Templates (`inventory/templates/inventory/supplier/`)

We will create three templates: `list.html`, `_supplier_table.html`, `_supplier_form.html`, and `_supplier_confirm_delete.html`.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Supplier List</h2>
        <div class="flex items-center space-x-4 w-full md:w-auto">
            <label for="search_value" class="sr-only">Search Supplier</label>
            <input 
                type="text" 
                id="search_value" 
                name="search_value"
                placeholder="Search by Supplier Name or Code..."
                class="flex-grow md:flex-none block w-full md:w-64 px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                hx-get="{% url 'supplier_table' %}"
                hx-target="#supplierTable-container"
                hx-trigger="keyup changed delay:500ms, searchButton from:body"
                hx-indicator="#loadingIndicator"
                hx-swap="innerHTML"
            >
            <button 
                id="searchButton"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                hx-trigger="click"
                hx-target="#supplierTable-container"
                hx-get="{% url 'supplier_table' %}"
                hx-include="#search_value"
                hx-swap="innerHTML"
            >
                Search
            </button>
            <button 
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm ml-auto md:ml-0"
                hx-get="{% url 'supplier_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .block to #modal then add .opacity-100 to #modal transition ease-out duration-300"
            >
                Add New Supplier
            </button>
        </div>
    </div>
    
    <div id="loadingIndicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading suppliers...</p>
    </div>

    <div id="supplierTable-container"
         hx-trigger="load, refreshSupplierList from:body"
         hx-get="{% url 'supplier_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center z-50 transition-opacity ease-in duration-300 opacity-0"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then remove .block from me transition ease-out duration-300">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For example, if you wanted to manage the autocomplete visibility directly with Alpine
    });

    // jQuery UI Autocomplete integration (if desired for richer autocomplete)
    // Ensure jQuery UI is loaded in base.html
    $(function() {
        $("#search_value").autocomplete({
            source: function(request, response) {
                $.ajax({
                    url: "{% url 'supplier_autocomplete' %}",
                    dataType: "json",
                    data: {
                        term: request.term
                    },
                    success: function(data) {
                        response(data);
                    }
                });
            },
            minLength: 1, // Minimum characters before triggering autocomplete
            select: function(event, ui) {
                // When an item is selected, trigger the HTMX search
                $('#search_value').val(ui.item.value); // Set selected value to input
                htmx.trigger(document.getElementById('search_value'), 'changed'); // Trigger the hx-get
            }
        });
    });

    // Close modal function for Alpine.js if using Alpine for modal state
    window.closeModal = () => {
        const modal = document.getElementById('modal');
        modal.classList.remove('opacity-100');
        modal.classList.add('opacity-0');
        setTimeout(() => modal.classList.add('hidden'), 300); // Hide after transition
    };

    // HTMX lifecycle event to re-initialize DataTables after content swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'supplierTable-container') {
            $('#supplierTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTables instance before re-initialization
            });
        }
    });

    // HTMX lifecycle event to close modal on successful form submission
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.requestConfig.headers['HX-Trigger'] === 'refreshSupplierList') {
            const modal = document.getElementById('modal');
            if (modal) {
                window.closeModal();
            }
        }
    });
</script>
{% endblock %}
```

**`_supplier_table.html`**

```html
<div class="bg-white shadow-sm rounded-lg overflow-hidden">
    {% if suppliers %}
    <table id="supplierTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin. Year</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for supplier in suppliers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.supplier_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-blue-600 hover:text-blue-900">
                    {{ supplier.supplier_name }}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.financial_year }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'supplier_edit' supplier_id=supplier.supplier_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal transition ease-out duration-300"
                    >
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'supplier_delete' supplier_id=supplier.supplier_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal transition ease-out duration-300"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-8 text-gray-500">
        <p class="text-lg font-semibold">No data to display!</p>
        <p class="mt-2">Try adjusting your search criteria or add new suppliers.</p>
    </div>
    {% endif %}
</div>

<!-- DataTables initialization script. This will run after HTMX loads this partial. -->
<script>
    // DataTables will be re-initialized in the parent 'list.html' via htmx:afterSwap
    // This script block is primarily for clarity in the partial template,
    // but the actual initialization is handled by the parent.
</script>
```

**`_supplier_form.html`**

```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} Supplier
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <ul class="mt-1 text-sm text-red-600">
                {% for error in field.errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        {% endfor %}
        
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .opacity-100 from #modal then remove .block from #modal transition ease-out duration-300"
            >
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
            >
                Save Supplier
            </button>
        </div>
    </form>
</div>
```

**`_supplier_confirm_delete.html`**

```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">Confirm Delete</h3>
    <p class="text-gray-700 text-lg mb-6">
        Are you sure you want to delete the supplier: 
        <span class="font-bold">{{ object.supplier_name }} ({{ object.supplier_id }})</span>?
    </p>
    <p class="text-red-600 text-sm mb-8">This action cannot be undone.</p>

    <form hx-post="{{ request.path }}" hx-swap="none" class="flex justify-end space-x-4">
        {% csrf_token %}
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
            _="on click remove .opacity-100 from #modal then remove .block from #modal transition ease-out duration-300"
        >
            Cancel
        </button>
        <button 
            type="submit" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
        >
            Delete
        </button>
    </form>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import SupplierListView, SupplierTablePartialView, SupplierCreateView, SupplierUpdateView, SupplierDeleteView, SupplierAutocompleteView

urlpatterns = [
    # Main list view for suppliers
    path('suppliers/', SupplierListView.as_view(), name='supplier_list'),
    
    # HTMX endpoint to load/refresh the DataTables partial
    path('suppliers/table/', SupplierTablePartialView.as_view(), name='supplier_table'),
    
    # CRUD operations via HTMX modals
    path('suppliers/add/', SupplierCreateView.as_view(), name='supplier_add'),
    path('suppliers/edit/<str:supplier_id>/', SupplierUpdateView.as_view(), name='supplier_edit'),
    path('suppliers/delete/<str:supplier_id>/', SupplierDeleteView.as_view(), name='supplier_delete'),
    
    # Autocomplete endpoint for supplier search
    path('suppliers/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Supplier
from unittest.mock import patch

# Mock session data for testing purposes
def mock_get_session_context(request):
    return 1, '2023-2024'

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Supplier.objects.create(
            supplier_id='SUP001',
            supplier_name='Test Supplier One',
            financial_year='2023-2024',
            company_id=1
        )
        Supplier.objects.create(
            supplier_id='SUP002',
            supplier_name='Another Supplier',
            financial_year='2023-2024',
            company_id=1
        )
        Supplier.objects.create(
            supplier_id='SUP003',
            supplier_name='Third Party Inc.',
            financial_year='2022-2023',
            company_id=1
        )
        Supplier.objects.create(
            supplier_id='SUP004',
            supplier_name='Different Company',
            financial_year='2023-2024',
            company_id=2
        )
  
    def test_supplier_creation(self):
        supplier = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(supplier.supplier_name, 'Test Supplier One')
        self.assertEqual(supplier.financial_year, '2023-2024')
        self.assertEqual(supplier.company_id, 1)
        
    def test_supplier_name_label(self):
        supplier = Supplier.objects.get(supplier_id='SUP001')
        field_label = supplier._meta.get_field('supplier_name').verbose_name
        self.assertEqual(field_label, 'supplier name') # Django's default verbose_name from field name

    def test_str_representation(self):
        supplier = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(str(supplier), 'Test Supplier One [SUP001]')

    def test_get_suppliers_by_company_and_finyear(self):
        # Test basic filtering
        suppliers = Supplier.get_suppliers_by_company_and_finyear(1, '2023-2024')
        self.assertEqual(suppliers.count(), 2)
        self.assertIn(Supplier.objects.get(supplier_id='SUP001'), suppliers)
        self.assertIn(Supplier.objects.get(supplier_id='SUP002'), suppliers)

        # Test search by name
        suppliers = Supplier.get_suppliers_by_company_and_finyear(1, '2023-2024', 'Test')
        self.assertEqual(suppliers.count(), 1)
        self.assertIn(Supplier.objects.get(supplier_id='SUP001'), suppliers)
        
        # Test search by parsed ID
        suppliers = Supplier.get_suppliers_by_company_and_finyear(1, '2023-2024', 'Anything [SUP002]')
        self.assertEqual(suppliers.count(), 1)
        self.assertIn(Supplier.objects.get(supplier_id='SUP002'), suppliers)

        # Test no results
        suppliers = Supplier.get_suppliers_by_company_and_finyear(1, '2023-2024', 'NonExistent')
        self.assertEqual(suppliers.count(), 0)

class SupplierViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.supplier1 = Supplier.objects.create(
            supplier_id='SVIEW1',
            supplier_name='View Test Supplier',
            financial_year='2023-2024',
            company_id=1
        )
        cls.supplier2 = Supplier.objects.create(
            supplier_id='SVIEW2',
            supplier_name='Another View Test',
            financial_year='2023-2024',
            company_id=1
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
        # Patch the session context getter to ensure consistent session data for tests
        patcher = patch('inventory.views.get_session_context', side_effect=mock_get_session_context)
        self.mock_session_context = patcher.start()
        self.addCleanup(patcher.stop)
    
    def test_list_view(self):
        response = self.client.get(reverse('supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier/list.html')
        # Check that context contains suppliers for initial render if needed, but HTMX loads later
        # self.assertTrue('suppliers' in response.context) 
        # self.assertEqual(len(response.context['suppliers']), 2) # based on mock_get_session_context

    def test_table_partial_view(self):
        response = self.client.get(reverse('supplier_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier/_supplier_table.html')
        self.assertTrue('suppliers' in response.context)
        self.assertEqual(response.context['suppliers'].count(), 2) # based on mock session
        
        # Test search functionality for partial table
        response = self.client.get(reverse('supplier_table') + '?search_value=Test', HTTP_HX_REQUEST='true')
        self.assertEqual(response.context['suppliers'].count(), 1)
        self.assertEqual(response.context['suppliers'].first(), self.supplier1)

    def test_create_view_get(self):
        response = self.client.get(reverse('supplier_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier/_supplier_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].initial['company_id'], 1)
        
    def test_create_view_post_success(self):
        data = {
            'supplier_id': 'NEW001',
            'supplier_name': 'New Test Supplier',
            'financial_year': '2023-2024',
            'company_id': 1
        }
        response = self.client.post(reverse('supplier_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(Supplier.objects.filter(supplier_id='NEW001').exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSupplierList')

    def test_create_view_post_invalid(self):
        data = {
            'supplier_id': 'SVIEW1', # Duplicate ID
            'supplier_name': 'Invalid Supplier',
            'financial_year': '2023-2024',
            'company_id': 1
        }
        response = self.client.post(reverse('supplier_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns form with errors
        self.assertTemplateUsed(response, 'inventory/supplier/_supplier_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Supplier Code already exists.', response.content.decode())

    def test_update_view_get(self):
        response = self.client.get(reverse('supplier_edit', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier/_supplier_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.supplier1)
        
    def test_update_view_post_success(self):
        data = {
            'supplier_id': self.supplier1.supplier_id, # Must be the same PK
            'supplier_name': 'Updated Supplier Name',
            'financial_year': self.supplier1.financial_year,
            'company_id': self.supplier1.company_id
        }
        response = self.client.post(reverse('supplier_edit', args=[self.supplier1.supplier_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.supplier1.refresh_from_db()
        self.assertEqual(self.supplier1.supplier_name, 'Updated Supplier Name')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSupplierList')

    def test_update_view_post_invalid(self):
        data = {
            'supplier_id': 'NONEXISTENT', # Cannot change PK on update
            'supplier_name': 'Invalid Update',
            'financial_year': self.supplier1.financial_year,
            'company_id': self.supplier1.company_id
        }
        response = self.client.post(reverse('supplier_edit', args=[self.supplier1.supplier_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier/_supplier_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Supplier Code cannot be changed.', response.content.decode())

    def test_delete_view_get(self):
        response = self.client.get(reverse('supplier_delete', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier/_supplier_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.supplier1)
        
    def test_delete_view_post_success(self):
        initial_count = Supplier.objects.count()
        response = self.client.post(reverse('supplier_delete', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Supplier.objects.count(), initial_count - 1)
        self.assertFalse(Supplier.objects.filter(supplier_id=self.supplier1.supplier_id).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSupplierList')

    def test_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete') + '?term=view')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertIn('View Test Supplier [SVIEW1]', data)
        self.assertIn('Another View Test [SVIEW2]', data)
        self.assertNotIn('Third Party Inc. [SUP003]', data) # Different fin year
        self.assertNotIn('Different Company [SUP004]', data) # Different company

        response = self.client.get(reverse('supplier_autocomplete') + '?term=another')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertIn('Another View Test [SVIEW2]', data)
```

### Step 5: HTMX and Alpine.js Integration

**Summary of Integration:**

1.  **Search & List Refresh:**
    *   The `list.html` contains a search input (`#search_value`) and a search button (`#searchButton`).
    *   Both are configured with `hx-get="{% url 'supplier_table' %}"` and `hx-target="#supplierTable-container"`.
    *   The input uses `hx-trigger="keyup changed delay:500ms, searchButton from:body"` for dynamic updates and `hx-include="#search_value"` to pass the input value.
    *   `hx-indicator="#loadingIndicator"` provides visual feedback during AJAX requests.
    *   The `_supplier_table.html` partial is loaded into `#supplierTable-container`.
    *   After the swap, `htmx:afterSwap` event listener re-initializes DataTables on the newly loaded table.
2.  **CRUD Modals:**
    *   "Add New Supplier", "Edit", and "Delete" buttons in `list.html` and `_supplier_table.html` use `hx-get` to fetch the respective `_supplier_form.html` or `_supplier_confirm_delete.html` partials.
    *   `hx-target="#modalContent"` directs the loaded content into a hidden modal div.
    *   Alpine.js (`_="on click add .block to #modal then add .opacity-100 to #modal transition ease-out duration-300"`) handles showing the modal when the buttons are clicked.
    *   The modal `div` itself has Alpine.js to hide it (`_="on click if event.target.id == 'modal' remove .opacity-100 from me then remove .block from me transition ease-out duration-300"`) when the background is clicked.
    *   The "Cancel" buttons within forms also use Alpine.js for closing the modal.
3.  **Form Submission & List Refresh:**
    *   Forms within the modal (`_supplier_form.html`, `_supplier_confirm_delete.html`) use `hx-post="{{ request.path }}" hx-swap="none"`. `hx-swap="none"` prevents the modal from being replaced, as the view will return `204 No Content` on success.
    *   On successful form submission (e.g., `SupplierCreateView`, `SupplierUpdateView`, `SupplierDeleteView`), the Django view sends an `HX-Trigger` header with `'refreshSupplierList'`.
    *   A global `htmx:afterRequest` listener on `document.body` detects this trigger, closes the modal using `window.closeModal()`, and implicitly causes the `supplierTable-container` to refresh its content (due to `hx-trigger="load, refreshSupplierList from:body"` on the container itself).
4.  **DataTables:**
    *   The `_supplier_table.html` partial contains the `<table>` element with `id="supplierTable"`.
    *   The `list.html`'s `htmx:afterSwap` event listener ensures that `$('#supplierTable').DataTable()` is called every time the table content is reloaded via HTMX, guaranteeing proper initialization and functionality (pagination, searching, sorting). `destroy: true` is crucial for re-initialization.
5.  **Autocomplete:**
    *   The `search_value` input uses a jQuery UI Autocomplete component. This component makes an AJAX call to the Django `supplier_autocomplete` URL.
    *   When an item is selected from the autocomplete suggestions, it explicitly triggers the HTMX `changed` event on the input field, which in turn causes the `supplier_table` to refresh with the selected value. This elegantly bridges jQuery UI Autocomplete with HTMX.

**Business Benefits:**
*   **Enhanced User Experience:** Eliminates full page reloads for searches, CRUD operations, and autocomplete, making the application feel faster and more responsive, akin to a single-page application but with simpler development.
*   **Reduced Server Load:** By only fetching and rendering partial HTML snippets, bandwidth and server processing are optimized, especially for high-traffic list views.
*   **Simplified Frontend Development:** Leverages HTMX for declarative AJAX, minimizing custom JavaScript. Alpine.js handles simple UI state, keeping the frontend lean and manageable.
*   **Modernization & Maintainability:** Migrating to Django with these patterns results in a clean, modular, and easily testable codebase, aligning with modern web development best practices.
*   **Scalability:** Django's robust ORM and architecture provide a solid foundation for future growth and feature expansion.

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced based on the analysis.
*   **DRY Templates:** The use of partial templates (`_supplier_table.html`, `_supplier_form.html`, `_supplier_confirm_delete.html`) ensures reusability and adherence to DRY principles.
*   **Thin Views, Fat Models:** Business logic (like `get_suppliers_by_company_and_finyear` and form validations) is pushed into the `Supplier` model and `SupplierForm`, keeping views concise and focused on request/response handling.
*   **Comprehensive Tests:** Unit tests for the `Supplier` model cover data integrity and custom methods. Integration tests for all views ensure correct functionality, including HTMX interactions and form handling. This ensures code quality and reduces regressions.
*   **Session Management:** The `get_session_context` helper function demonstrates how ASP.NET session variables (`compid`, `finyear`) are mapped to Django's `request.session` for consistent data filtering across the application.
*   **Tailwind CSS:** All generated HTML includes appropriate Tailwind CSS classes for a modern and responsive design, assuming Tailwind is configured in the base project.