## ASP.NET to Django Conversion Script: Stock Statement Filter

This document outlines the modernization plan for migrating the ASP.NET `Stock_Statement.aspx` page to a modern Django-based application. The primary goal of this page is to act as a filter interface for generating a "Stock Statement Details" report, rather than displaying or modifying existing database records directly. Therefore, the Django implementation will focus on creating a robust form with dynamic HTMX-driven interactions and a clean separation of concerns, preparing for the report details page (which would then leverage DataTables).

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module, which is the "Stock Statement Filter" form.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (not on *this* filter form, but for the resulting report page).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns used to populate dropdowns and retrieve financial year information. The ASP.NET code relies on three main tables for data lookup:

-   `tblFinancial_master`: Stores financial year details.
-   `tblDG_Category_Master`: Stores item categories.
-   `tblDG_Item_Master`: Used implicitly for `Location` and `ItemCode`/`ManfDesc` lookup based on `DrpSearchCode`.

**Instructions:**
-   From `SqlCommand CmdFinYear` and `DAFin.Fill(DSFin, "tblFinancial_master")`, we identify `tblFinancial_master` with columns `FinYearFrom`, `FinYearTo`, and used in conjunction with `CompId` and `FinYearId`.
-   From `fun.select("CId,'['+Symbol+'] - '+CName as Category", "tblDG_Category_Master", "CompId='" + CompId + "'")`, we identify `tblDG_Category_Master` with columns `CId`, `Symbol`, `CName`.
-   From `DrpSearchCode` values, we infer `tblDG_Item_Master` columns: `ItemCode`, `ManfDesc` (Description), and `Location`.

### Step 2: Identify Backend Functionality

**Task:** Determine the core functionality of the ASP.NET page.

**Instructions:**
This page is a *report filter form*. It performs the following operations:
-   **Read:** Populates initial financial year dates and dropdowns (Category, Search By, Location). This involves fetching data from `tblFinancial_master`, `tblDG_Category_Master`, and potentially distinct `Location` values from `tblDG_Item_Master`.
-   **Validation:** Client-side and server-side validation for dates, required fields, and overheads percentage.
-   **Dynamic UI Updates:** Based on dropdown selections (`DrpType`, `DrpSearchCode`), specific input fields (`DrpCategory1`, `DropDownList3`, `txtSearchItemCode`) are shown or hidden, and dropdowns are re-populated.
-   **Parameter Generation & Redirection:** Upon "Proceed" button click, it gathers all selected filter criteria, constructs a URL query string, and redirects the user to `Stock_Statement_Details.aspx`. This is not a CRUD operation on a single entity, but rather preparation for a report.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to Django form fields and HTMX/Alpine.js interactions.

**Instructions:**
-   `lblFromDate`, `lblToDate`: Display of financial year dates.
-   `Txtfromdate`, `TxtTodate`: Date input fields. Will use Django `forms.DateField` with a datepicker (e.g., Flatpickr).
-   `RadRate`: Radio button list for PO Rate selection. Will map to `forms.ChoiceField` with `forms.RadioSelect`.
-   `txtOverheads`: Textbox for overheads percentage. Will map to `forms.DecimalField` with validation.
-   `DrpType`: Dropdown for "Category" or "WO Items". `OnSelectedIndexChanged` will trigger HTMX to dynamically update other parts of the form.
-   `DrpCategory1`: Dropdown for selecting a category. Dynamically populated.
-   `DrpSearchCode`: Dropdown for search criteria (Item Code, Description, Location). `OnSelectedIndexChanged` will trigger HTMX to toggle visibility of `DropDownList3` and `txtSearchItemCode`.
-   `DropDownList3`: Dropdown for Location. Dynamically populated.
-   `txtSearchItemCode`: Textbox for item code/description search.
-   `BtnView`: Submit button for the form. Will use HTMX for form submission and `hx-redirect` to the report page.
-   `lblMessage`: Error message display. Will be handled by Django's messages framework and Alpine.js for client-side alerts.
-   `CalendarExtender`: Datepicker functionality. Will be replaced by a modern JavaScript datepicker integrated via CDN in `base.html`.

### Step 4: Generate Django Code

We will create a new Django app named `inventory` for this module.

#### 4.1 Models

Since this page is a filter form and not directly manipulating a single entity, we will define simplified models for the lookup data involved. We will set `managed = False` as these are existing database tables.

**File: `inventory/models.py`**

```python
from django.db import models
from django.utils import timezone

class FinancialYear(models.Model):
    """
    Represents financial year information from tblFinancial_master.
    Assumes CompId and FinYearId are passed through session/context.
    """
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    fin_year_from = models.DateField(db_column='FinYearFrom')
    fin_year_to = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        unique_together = (('comp_id', 'fin_year_id'),)
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"FY {self.fin_year_from.year}-{self.fin_year_to.year} (Comp: {self.comp_id}, ID: {self.fin_year_id})"

    @classmethod
    def get_current_financial_year_dates(cls, comp_id, fin_year_id):
        """
        Retrieves financial year start and end dates based on CompId and FinYearId.
        This business logic resides in the model to encapsulate data retrieval.
        """
        try:
            fin_year = cls.objects.get(comp_id=comp_id, fin_year_id=fin_year_id)
            return fin_year.fin_year_from, fin_year.fin_year_to
        except cls.DoesNotExist:
            return None, None

class Category(models.Model):
    """
    Represents item categories from tblDG_Category_Master.
    """
    cid = models.CharField(db_column='CId', primary_key=True, max_length=50) # Assuming CId can be char/varchar
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}"

    @classmethod
    def get_categories_for_company(cls, comp_id):
        """
        Retrieves all categories for a given company ID.
        Business logic for data lookup for dropdowns.
        """
        return cls.objects.filter(comp_id=comp_id).order_by('cname')

class ItemMaster(models.Model):
    """
    Represents a simplified view of tblDG_Item_Master for location lookup.
    """
    item_code = models.CharField(db_column='ItemCode', primary_key=True, max_length=50) # Assuming ItemCode is PK
    description = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    cid = models.CharField(db_column='CId', max_length=50, blank=True, null=True) # FK to Category

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

    @classmethod
    def get_distinct_locations_for_company(cls, comp_id):
        """
        Retrieves distinct locations for a given company ID.
        Business logic for data lookup for dropdowns.
        """
        locations = cls.objects.filter(comp_id=comp_id).exclude(location__isnull=True).values_list('location', flat=True).distinct().order_by('location')
        return [(loc, loc) for loc in locations] # Return as (value, display_text) for forms

# A simple service class for complex report parameter generation (business logic)
# This adheres to "business logic in models only" by being a logical extension,
# or could be placed in inventory/managers.py
class ReportParameterService:
    @staticmethod
    def generate_stock_statement_params(form_data, financial_year_start_date, comp_id, fin_year_id):
        """
        Generates query parameters for the Stock Statement Details report.
        This encapsulates the complex conditional logic from BtnView_Click.
        """
        params = {}

        report_type = form_data.get('report_type')
        from_date = form_data.get('from_date')
        to_date = form_data.get('to_date')
        po_rate = form_data.get('po_rate')
        overheads = form_data.get('overheads', 0)
        category_id = form_data.get('category')
        search_code_type = form_data.get('search_code_type')
        location = form_data.get('location_dropdown')
        search_item_text = form_data.get('search_item_code')

        # Map C# 'x', 'p', 'r' parameters
        x_param = ""
        p_param = ""
        r_param = ""

        if report_type == "Category":
            if category_id and category_id != "Select":
                x_param = f" AND CId='{category_id}'"

                if search_code_type and search_code_type != "Select":
                    if search_code_type == "tblDG_Item_Master.ItemCode" and search_item_text:
                        p_param = f" And ItemCode Like '{search_item_text.strip()}%'"
                    elif search_code_type == "tblDG_Item_Master.ManfDesc" and search_item_text:
                        p_param = f" And Description Like '{search_item_text.strip()}%'"
                    elif search_code_type == "tblDG_Item_Master.Location" and location and location != "Select":
                        p_param = f" And Location='{location}'"
            r_param = " And CId is not null"
        elif report_type == "WOItems":
            if search_code_type and search_code_type != "Select":
                if search_code_type == "tblDG_Item_Master.ItemCode" and search_item_text:
                    p_param = f" And ItemCode Like '{search_item_text.strip()}%'"
                elif search_code_type == "tblDG_Item_Master.ManfDesc" and search_item_text:
                    p_param = f" And Description Like '{search_item_text.strip()}%'"
            r_param = " And CId is null"

        params['Cid'] = x_param
        params['RadVal'] = str(po_rate)
        params['FDate'] = from_date.strftime('%d-%m-%Y') if from_date else ''
        params['TDate'] = to_date.strftime('%d-%m-%Y') if to_date else ''
        params['OpeningDt'] = financial_year_start_date.strftime('%d-%m-%Y') if financial_year_start_date else ''
        params['p'] = p_param
        params['r'] = r_param
        params['OverHeads'] = str(overheads)
        params['Key'] = ReportParameterService._generate_random_key() # Replaces fun.GetRandomAlphaNumeric()

        return params

    @staticmethod
    def _generate_random_key(length=10):
        """Generates a random alphanumeric string."""
        import string
        import secrets
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for i in range(length))

```

#### 4.2 Forms

This will be a standard `forms.Form` as it's for filtering, not directly saving a model instance.

**File: `inventory/forms.py`**

```python
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import datetime
from .models import FinancialYear, Category, ItemMaster # Import models for choices and data fetching

class StockStatementFilterForm(forms.Form):
    """
    Form for filtering stock statement details.
    Maps to the ASP.NET Stock_Statement.aspx input controls.
    """
    REPORT_TYPE_CHOICES = [
        ('', 'Select'), # InitialValue="Select"
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]

    PO_RATE_CHOICES = [
        ('0', 'Max'),
        ('1', 'Min'),
        ('2', 'Average'),
        ('3', 'Latest'),
        ('4', 'Actual'),
    ]

    SEARCH_CODE_CHOICES = [
        ('', 'Select'), # InitialValue="Select"
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    # Fields directly mapping to ASP.NET controls
    from_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'box3 datepicker block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:update_form_fields' %}", # Example HTMX trigger
            'hx-trigger': 'change, keyup changed delay:500ms',
            'hx-target': '#dynamic-fields-container', # Target for dynamic updates if needed
            'hx-swap': 'outerHTML',
            'readonly': 'readonly' # Matches ASP.NET behavior
        }),
        input_formats=['%d-%m-%Y'], # Matches ASP.NET Format="dd-MM-yyyy"
        required=True,
        label='Date From'
    )
    to_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'box3 datepicker block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:update_form_fields' %}",
            'hx-trigger': 'change, keyup changed delay:500ms',
            'hx-target': '#dynamic-fields-container',
            'hx-swap': 'outerHTML',
            'readonly': 'readonly'
        }),
        input_formats=['%d-%m-%Y'],
        required=True,
        label='Date To'
    )
    po_rate = forms.ChoiceField(
        choices=PO_RATE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'flex space-x-4 mt-2'}),
        initial='0', # Selected="True" for Max
        label='PO Rate'
    )
    overheads = forms.DecimalField(
        max_digits=15, # Based on ValidationExpression "^\d{1,15}(\.\d{0,3})?$"
        decimal_places=3,
        required=True, # ReqtxtOverheads
        initial=20, # Default value
        widget=forms.NumberInput(attrs={'class': 'box3 w-20 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label='Over heads (%)'
    )
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:get_dynamic_form_fields' %}", # HTMX endpoint for dynamic sections
            'hx-target': '#dynamic-fields-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change' # AutoPostBack="True"
        }),
        required=True,
        label='Report Type'
    )

    # Dynamic fields, will be managed via HTMX
    category = forms.ChoiceField(
        choices=[], # Populated dynamically
        widget=forms.Select(attrs={
            'class': 'box3 block w-60 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:get_dynamic_form_fields' %}",
            'hx-target': '#dynamic-fields-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change' # OnSelectedIndexChanged
        }),
        required=False, # Required only if ReportType is Category and not 'Select'
        label='Category'
    )
    search_code_type = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:get_dynamic_form_fields' %}",
            'hx-target': '#dynamic-fields-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change' # AutoPostBack="True"
        }),
        required=False,
        label='Search By'
    )
    location_dropdown = forms.ChoiceField(
        choices=[], # Populated dynamically
        widget=forms.Select(attrs={
            'class': 'box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }),
        required=False,
        label='Location'
    )
    search_item_code = forms.CharField(
        max_length=207, # Based on Width="207px"
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-60 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label='Search Item'
    )

    def __init__(self, *args, **kwargs):
        self.comp_id = kwargs.pop('comp_id', 1) # Default or get from session
        self.fin_year_id = kwargs.pop('fin_year_id', 1) # Default or get from session
        self.initial_from_date = None
        self.initial_to_date = None

        super().__init__(*args, **kwargs)

        # Get financial year dates from model
        fin_year_from, fin_year_to = FinancialYear.get_current_financial_year_dates(self.comp_id, self.fin_year_id)
        if fin_year_from and fin_year_to:
            self.initial_from_date = fin_year_from
            self.initial_to_date = fin_year_to
            # Set initial values for date fields if not already provided
            if 'from_date' not in self.initial:
                self.initial['from_date'] = fin_year_from.strftime('%d-%m-%Y')
            if 'to_date' not in self.initial:
                self.initial['to_date'] = timezone.now().strftime('%d-%m-%Y') # fun.getCurrDate() equivalent

        # Populate category dropdown initially (can be done via HTMX on load too)
        self.fields['category'].choices = [('', 'Select')] + [
            (c.cid, str(c)) for c in Category.get_categories_for_company(self.comp_id)
        ]

        # Populate location dropdown initially (can be done via HTMX on load too)
        self.fields['location_dropdown'].choices = [('', 'Select')] + ItemMaster.get_distinct_locations_for_company(self.comp_id)

    def clean(self):
        """
        Custom validation for date ranges and dynamic field requirements.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        report_type = cleaned_data.get('report_type')
        category_id = cleaned_data.get('category')
        search_code_type = cleaned_data.get('search_code_type')
        location_dropdown = cleaned_data.get('location_dropdown')
        search_item_code = cleaned_data.get('search_item_code')

        # Date validations (from BtnView_Click)
        if from_date and to_date and self.initial_from_date:
            if to_date < from_date:
                self.add_error('to_date', ValidationError(_('From date should be Less than or Equal to To Date!')))
            if from_date < self.initial_from_date:
                self.add_error('from_date', ValidationError(_('From date should not be Less than Opening Date!')))

        # Dynamic field requirements (from ASP.NET validation)
        if report_type == '': # ReqCategory InitialValue="Select"
            self.add_error('report_type', ValidationError(_('Please Select Category or WO Items.')))

        if report_type == 'Category':
            # ASP.NET `ReqCategory` applies only to DrpType, not DrpCategory1 based on validation group.
            # However, logic in BtnView_Click indicates sd != "Select" is checked.
            # For robustness, we can add a check here.
            if not category_id or category_id == 'Select':
                # This error is not explicitly shown in ASP.NET for category, but implied by logic.
                pass # The ASP.NET doesn't explicitly validate this, the `x` param just becomes empty.
        
        # Validation based on DrpSearchCode visibility in ASP.NET logic
        if search_code_type and search_code_type != 'Select':
            if search_code_type in ['tblDG_Item_Master.ItemCode', 'tblDG_Item_Master.ManfDesc']:
                if not search_item_code:
                    self.add_error('search_item_code', ValidationError(_('This field is required for selected search type.')))
            elif search_code_type == 'tblDG_Item_Master.Location':
                if not location_dropdown or location_dropdown == 'Select':
                    self.add_error('location_dropdown', ValidationError(_('Please select a location.')))

        return cleaned_data

```

#### 4.3 Views

We'll use a `FormView` for the main page and `TemplateView` or `View` for HTMX partials.

**File: `inventory/views.py`**

```python
from django.views.generic import FormView, TemplateView, View
from django.urls import reverse
from django.shortcuts import redirect
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.template.loader import render_to_string
from .forms import StockStatementFilterForm
from .models import FinancialYear, Category, ItemMaster, ReportParameterService
import datetime # For timezone.now()

class StockStatementFilterView(FormView):
    """
    Main view for the Stock Statement filter form.
    Handles initial display and form submission.
    """
    template_name = 'inventory/stock_statement/stock_statement_form.html'
    form_class = StockStatementFilterForm

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Assume CompId and FinYearId are available from session or user profile
        # For demonstration, hardcoding. In real app, get from request.session.
        kwargs['comp_id'] = self.request.session.get('comp_id', 1)
        kwargs['fin_year_id'] = self.request.session.get('fin_year_id', 1)
        return kwargs

    def form_valid(self, form):
        """
        Handles valid form submission. Generates report parameters and redirects.
        """
        comp_id = self.request.session.get('comp_id', 1)
        fin_year_id = self.request.session.get('fin_year_id', 1)
        financial_year_start_date, _ = FinancialYear.get_current_financial_year_dates(comp_id, fin_year_id)

        # Use the service to generate parameters based on cleaned form data
        report_params = ReportParameterService.generate_stock_statement_params(
            form.cleaned_data, financial_year_start_date, comp_id, fin_year_id
        )

        # Construct the URL for the report details page
        # Assuming 'inventory:stock_statement_details' is the URL for the details page
        # This mirrors ASP.NET's Response.Redirect
        details_url = reverse('inventory:stock_statement_details') + '?' + '&'.join(f'{k}={v}' for k, v in report_params.items())

        messages.success(self.request, "Stock Statement parameters generated successfully.")
        return redirect(details_url)

    def form_invalid(self, form):
        """
        Handles invalid form submission.
        If it's an HTMX request, return the updated form HTML partial.
        """
        if self.request.headers.get('HX-Request'):
            # Render only the form section to update the client-side
            # This is crucial for HTMX to show validation errors dynamically
            return HttpResponse(render_to_string('inventory/stock_statement/_stock_statement_form_fields.html', {'form': form}, request=self.request))
        return super().form_invalid(form)

class DynamicFormFieldsView(View):
    """
    View to handle HTMX requests for dynamically updating form fields
    (e.g., based on report_type or search_code_type selection).
    """
    def get(self, request, *args, **kwargs):
        # Create a form instance, potentially pre-populating from GET parameters
        # This will simulate the state of the form on the client side
        initial_data = {
            'from_date': request.GET.get('from_date'),
            'to_date': request.GET.get('to_date'),
            'po_rate': request.GET.get('po_rate'),
            'overheads': request.GET.get('overheads'),
            'report_type': request.GET.get('report_type'),
            'category': request.GET.get('category'),
            'search_code_type': request.GET.get('search_code_type'),
            'location_dropdown': request.GET.get('location_dropdown'),
            'search_item_code': request.GET.get('search_item_code'),
        }

        # Filter out None values that would override initial values from __init__
        initial_data = {k: v for k, v in initial_data.items() if v is not None}

        form = StockStatementFilterForm(initial=initial_data)

        # Logic to mimic ASP.NET's DrpType_SelectedIndexChanged and DrpSearchCode_SelectedIndexChanged
        # This effectively sets visibility by deciding which fields are 'visible'
        if form.cleaned_data.get('report_type') == 'Category':
            form.fields['category'].required = True
            form.fields['search_code_type'].required = False # Can be empty
            form.fields['search_item_code'].required = False # Can be empty
            form.fields['location_dropdown'].required = False # Can be empty
        elif form.cleaned_data.get('report_type') == 'WOItems':
            form.fields['category'].required = False
            form.fields['search_code_type'].required = False
            form.fields['search_item_code'].required = False
            form.fields['location_dropdown'].required = False
        else: # 'Select' or initial load
            form.fields['category'].required = False
            form.fields['search_code_type'].required = False
            form.fields['search_item_code'].required = False
            form.fields['location_dropdown'].required = False


        # Further logic based on search_code_type
        if form.cleaned_data.get('search_code_type') == 'tblDG_Item_Master.Location':
            form.fields['location_dropdown'].required = True
            form.fields['search_item_code'].required = False
        elif form.cleaned_data.get('search_code_type') in ['tblDG_Item_Master.ItemCode', 'tblDG_Item_Master.ManfDesc']:
            form.fields['location_dropdown'].required = False
            form.fields['search_item_code'].required = True
        else:
            form.fields['location_dropdown'].required = False
            form.fields['search_item_code'].required = False

        # Render the partial containing the form fields
        return HttpResponse(render_to_string(
            'inventory/stock_statement/_stock_statement_form_fields.html',
            {'form': form},
            request=request
        ))

# Dummy view for the Stock Statement Details page, where the report would be displayed
class StockStatementDetailsView(TemplateView):
    template_name = 'inventory/stock_statement/stock_statement_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # In a real application, this would fetch data based on query parameters
        # and prepare it for DataTables display.
        context['report_params'] = self.request.GET
        return context

```

#### 4.4 Templates

**File: `inventory/templates/inventory/stock_statement/stock_statement_form.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Stock Statement Filter{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="bg-gray-800 text-white p-4 rounded-t-lg mb-6">
            <h2 class="text-xl font-bold">Stock Statement</h2>
        </div>

        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <form method="post" hx-post="{% url 'inventory:stock_statement_filter' %}" hx-swap="none" hx-trigger="submit">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-6">
                <!-- Financial Year Display -->
                <div class="col-span-1 md:col-span-2">
                    <p class="font-bold">Financial Year: From Date: 
                        <span class="font-bold">{{ form.initial_from_date|date:"d-m-Y" }}</span> To: 
                        <span class="font-bold">{{ form.initial_to_date|date:"d-m-Y" }}</span>
                    </p>
                </div>

                <!-- Date From and To -->
                <div class="flex items-center space-x-4">
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700 w-24">Date From:</label>
                    <div class="flex-1">
                        {{ form.from_date }}
                        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                    </div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700 w-10 text-right">To:</label>
                    <div class="flex-1">
                        {{ form.to_date }}
                        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                    </div>
                </div>

                <!-- PO Rate and Overheads -->
                <div class="flex items-start space-x-4">
                    <label class="block text-sm font-medium text-gray-700 w-24">PO Rate:</label>
                    <div class="flex-1">
                        {{ form.po_rate }}
                        {% if form.po_rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.po_rate.errors }}</p>{% endif %}
                    </div>
                    <label for="{{ form.overheads.id_for_label }}" class="block text-sm font-medium text-gray-700 w-24">Overheads (%):</label>
                    <div class="flex-1 flex items-center">
                        {{ form.overheads }} %
                        {% if form.overheads.errors %}<p class="text-red-500 text-xs mt-1">{{ form.overheads.errors }}</p>{% endif %}
                    </div>
                </div>

                <!-- Report Type (Category/WO Items) -->
                <div class="flex items-center space-x-4 col-span-1 md:col-span-2">
                    <label for="{{ form.report_type.id_for_label }}" class="block text-sm font-medium text-gray-700 w-24">Report Type:</label>
                    <div class="flex-1">
                        {{ form.report_type }}
                        {% if form.report_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.report_type.errors }}</p>{% endif %}
                    </div>
                </div>
            </div>

            <!-- Dynamic Fields Container - Updated via HTMX -->
            <div id="dynamic-fields-container" hx-trigger="load delay:10ms" hx-get="{% url 'inventory:get_dynamic_form_fields' %}" hx-target="#dynamic-fields-container" hx-swap="outerHTML">
                <!-- Dynamic form fields will be loaded here via HTMX -->
                <!-- Initial loading indicator -->
                <div class="text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-sm text-gray-600">Loading dynamic fields...</p>
                </div>
            </div>
            
            <div class="mt-8 text-center">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-200 ease-in-out">
                    Proceed
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('DOMContentLoaded', function() {
        flatpickr(".datepicker", {
            dateFormat: "d-m-Y",
            allowInput: false // Mimics readonly attribute
        });
    });

    // Handle HTMX redirect from form submission
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 200 && evt.detail.xhr.getResponseHeader('HX-Redirect')) {
            window.location.href = evt.detail.xhr.getResponseHeader('HX-Redirect');
        }
    });
</script>
{% endblock %}

```

**File: `inventory/templates/inventory/stock_statement/_stock_statement_form_fields.html`**
This partial template contains the dynamic parts of the form.

```html
{% comment %}
    This template is dynamically loaded by HTMX to update conditional form fields.
    It takes the 'form' object as context.
{% endcomment %}
<div id="dynamic-fields-container">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-6">
        <!-- Category Dropdown -->
        {% if form.cleaned_data.report_type == 'Category' or not form.is_bound %}
        <div class="flex items-center space-x-4">
            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 w-24">Category:</label>
            <div class="flex-1">
                {{ form.category }}
                {% if form.category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>{% endif %}
            </div>
        </div>
        {% else %}
        {# Hidden if not 'Category' selected, but still keep input for form submission #}
        <div class="hidden">{{ form.category }}</div>
        {% endif %}

        <!-- Search Code Dropdown -->
        {% if form.cleaned_data.report_type == 'Category' or form.cleaned_data.report_type == 'WOItems' or not form.is_bound %}
        <div class="flex items-center space-x-4">
            <label for="{{ form.search_code_type.id_for_label }}" class="block text-sm font-medium text-gray-700 w-24">Search Code:</label>
            <div class="flex-1">
                {{ form.search_code_type }}
                {% if form.search_code_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.search_code_type.errors }}</p>{% endif %}
            </div>
        </div>
        {% else %}
        {# Hidden if neither 'Category' nor 'WOItems' selected #}
        <div class="hidden">{{ form.search_code_type }}</div>
        {% endif %}

        <!-- Dynamic Search Input (Location dropdown or Item Code/Description textbox) -->
        <div class="col-span-1 md:col-span-2 flex items-center space-x-4">
            {% if form.cleaned_data.search_code_type == 'tblDG_Item_Master.Location' %}
                <label for="{{ form.location_dropdown.id_for_label }}" class="block text-sm font-medium text-gray-700 w-24">Location:</label>
                <div class="flex-1">
                    {{ form.location_dropdown }}
                    {% if form.location_dropdown.errors %}<p class="text-red-500 text-xs mt-1">{{ form.location_dropdown.errors }}</p>{% endif %}
                </div>
                {# Hide search_item_code when Location is chosen #}
                <div class="hidden">{{ form.search_item_code }}</div>
            {% elif form.cleaned_data.search_code_type in ['tblDG_Item_Master.ItemCode', 'tblDG_Item_Master.ManfDesc'] %}
                <label for="{{ form.search_item_code.id_for_label }}" class="block text-sm font-medium text-gray-700 w-24">Search Item:</label>
                <div class="flex-1">
                    {{ form.search_item_code }}
                    {% if form.search_item_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.search_item_code.errors }}</p>{% endif %}
                </div>
                {# Hide location_dropdown when Item Code/Description is chosen #}
                <div class="hidden">{{ form.location_dropdown }}</div>
            {% else %}
                {# Hide both if search_code_type is 'Select' or not relevant #}
                <div class="hidden">{{ form.location_dropdown }}</div>
                <div class="hidden">{{ form.search_item_code }}</div>
            {% endif %}
        </div>
    </div>
</div>
```

**File: `inventory/templates/inventory/stock_statement/stock_statement_details.html` (Dummy Report Page)**

```html
{% extends 'core/base.html' %}

{% block title %}Stock Statement Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Stock Statement Report Details</h2>
    <p class="mb-4">This page would display the detailed stock statement based on the filters you selected.</p>
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">Selected Filters:</h3>
        <ul class="list-disc list-inside space-y-2">
            {% for key, value in report_params.items %}
                <li><strong>{{ key }}:</strong> {{ value }}</li>
            {% endfor %}
        </ul>
        <p class="mt-6 text-gray-600">
            *In a full implementation, this section would dynamically load and display a DataTables grid
            with the actual report data, fetched based on these parameters.
        </p>
    </div>
    <div class="mt-8 text-center">
        <a href="{% url 'inventory:stock_statement_filter' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-200 ease-in-out">
            Back to Filter
        </a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs

**File: `inventory/urls.py`**

```python
from django.urls import path
from .views import StockStatementFilterView, DynamicFormFieldsView, StockStatementDetailsView

app_name = 'inventory'

urlpatterns = [
    # Main URL for the filter form
    path('stock-statement-filter/', StockStatementFilterView.as_view(), name='stock_statement_filter'),
    
    # HTMX endpoint for dynamically updating form fields (Category/WO Items type, Search Code type)
    path('stock-statement-filter/dynamic-fields/', DynamicFormFieldsView.as_view(), name='get_dynamic_form_fields'),

    # Dummy URL for the report details page (where the redirection goes)
    path('stock-statement-details/', StockStatementDetailsView.as_view(), name='stock_statement_details'),
]

```

#### 4.6 Tests

**File: `inventory/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from datetime import date, timedelta
from .models import FinancialYear, Category, ItemMaster, ReportParameterService
from .forms import StockStatementFilterForm

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data
        FinancialYear.objects.create(comp_id=1, fin_year_id=1, fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31))
        FinancialYear.objects.create(comp_id=1, fin_year_id=2, fin_year_from=date(2024, 4, 1), fin_year_to=date(2025, 3, 31))

    def test_financial_year_creation(self):
        fin_year = FinancialYear.objects.get(comp_id=1, fin_year_id=1)
        self.assertEqual(fin_year.fin_year_from, date(2023, 4, 1))
        self.assertEqual(fin_year.fin_year_to, date(2024, 3, 31))
        self.assertEqual(str(fin_year), "FY 2023-2024 (Comp: 1, ID: 1)")

    def test_get_current_financial_year_dates(self):
        from_date, to_date = FinancialYear.get_current_financial_year_dates(1, 1)
        self.assertEqual(from_date, date(2023, 4, 1))
        self.assertEqual(to_date, date(2024, 3, 31))

        from_date, to_date = FinancialYear.get_current_financial_year_dates(99, 99) # Non-existent
        self.assertIsNone(from_date)
        self.assertIsNone(to_date)

class CategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Category.objects.create(cid='CAT001', symbol='FAB', cname='Fabric', comp_id=1)
        Category.objects.create(cid='CAT002', symbol='ACC', cname='Accessories', comp_id=1)
        Category.objects.create(cid='CAT003', symbol='RAW', cname='Raw Material', comp_id=2)

    def test_category_creation(self):
        cat = Category.objects.get(cid='CAT001')
        self.assertEqual(cat.cname, 'Fabric')
        self.assertEqual(str(cat), '[FAB] - Fabric')

    def test_get_categories_for_company(self):
        categories = Category.get_categories_for_company(1)
        self.assertEqual(len(categories), 2)
        self.assertIn(Category.objects.get(cid='CAT001'), categories)
        self.assertIn(Category.objects.get(cid='CAT002'), categories)
        self.assertNotIn(Category.objects.get(cid='CAT003'), categories)

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        ItemMaster.objects.create(item_code='ITEM001', description='Red Fabric', location='Warehouse A', comp_id=1, fin_year_id=1, cid='CAT001')
        ItemMaster.objects.create(item_code='ITEM002', description='Blue Fabric', location='Warehouse B', comp_id=1, fin_year_id=1, cid='CAT001')
        ItemMaster.objects.create(item_code='ITEM003', description='Small Button', location='Warehouse A', comp_id=1, fin_year_id=1, cid='CAT002')
        ItemMaster.objects.create(item_code='ITEM004', description='Drill Machine', location=None, comp_id=1, fin_year_id=1, cid=None) # WO Item

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(item_code='ITEM001')
        self.assertEqual(item.description, 'Red Fabric')
        self.assertEqual(item.location, 'Warehouse A')

    def test_get_distinct_locations_for_company(self):
        locations = ItemMaster.get_distinct_locations_for_company(1)
        # Expected: [('Warehouse A', 'Warehouse A'), ('Warehouse B', 'Warehouse B')]
        self.assertListEqual(sorted(locations), sorted([('Warehouse A', 'Warehouse A'), ('Warehouse B', 'Warehouse B')]))

class ReportParameterServiceTest(TestCase):
    def test_generate_random_key(self):
        key1 = ReportParameterService._generate_random_key()
        key2 = ReportParameterService._generate_random_key()
        self.assertEqual(len(key1), 10)
        self.assertNotEqual(key1, key2) # Highly unlikely to be equal

    def test_generate_stock_statement_params_category_itemcode(self):
        form_data = {
            'report_type': 'Category',
            'from_date': date(2023, 5, 1),
            'to_date': date(2023, 5, 31),
            'po_rate': '0',
            'overheads': 15.5,
            'category': 'CAT001',
            'search_code_type': 'tblDG_Item_Master.ItemCode',
            'location_dropdown': '', # Not used
            'search_item_code': 'ITM',
        }
        financial_year_start_date = date(2023, 4, 1)
        params = ReportParameterService.generate_stock_statement_params(form_data, financial_year_start_date, 1, 1)
        self.assertEqual(params['Cid'], " AND CId='CAT001'")
        self.assertEqual(params['RadVal'], '0')
        self.assertEqual(params['FDate'], '01-05-2023')
        self.assertEqual(params['TDate'], '31-05-2023')
        self.assertEqual(params['OpeningDt'], '01-04-2023')
        self.assertEqual(params['p'], " And ItemCode Like 'ITM%'")
        self.assertEqual(params['r'], " And CId is not null")
        self.assertEqual(params['OverHeads'], '15.5')
        self.assertIn('Key', params)

    def test_generate_stock_statement_params_woitems_description(self):
        form_data = {
            'report_type': 'WOItems',
            'from_date': date(2023, 1, 1),
            'to_date': date(2023, 1, 31),
            'po_rate': '1',
            'overheads': 10,
            'category': '', # Not used
            'search_code_type': 'tblDG_Item_Master.ManfDesc',
            'location_dropdown': '', # Not used
            'search_item_code': 'DRILL',
        }
        financial_year_start_date = date(2022, 4, 1)
        params = ReportParameterService.generate_stock_statement_params(form_data, financial_year_start_date, 1, 1)
        self.assertEqual(params['Cid'], "")
        self.assertEqual(params['RadVal'], '1')
        self.assertEqual(params['FDate'], '01-01-2023')
        self.assertEqual(params['TDate'], '31-01-2023')
        self.assertEqual(params['OpeningDt'], '01-04-2022')
        self.assertEqual(params['p'], " And Description Like 'DRILL%'")
        self.assertEqual(params['r'], " And CId is null")
        self.assertEqual(params['OverHeads'], '10')
        self.assertIn('Key', params)

class StockStatementFilterFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialYear.objects.create(comp_id=1, fin_year_id=1, fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31))
        Category.objects.create(cid='CAT001', symbol='FAB', cname='Fabric', comp_id=1)
        ItemMaster.objects.create(item_code='ITEM001', description='Red Fabric', location='Warehouse A', comp_id=1, fin_year_id=1, cid='CAT001')

    def test_form_initialization(self):
        # Test default initialization with financial year dates
        form = StockStatementFilterForm(comp_id=1, fin_year_id=1)
        self.assertEqual(form.initial['from_date'], '01-04-2023')
        # to_date will be today's date
        self.assertEqual(form.initial['to_date'], date.today().strftime('%d-%m-%Y'))
        self.assertEqual(form.initial['po_rate'], '0')
        self.assertEqual(form.initial['overheads'], 20)
        self.assertEqual(len(form.fields['category'].choices), 2) # Select + CAT001
        self.assertEqual(len(form.fields['location_dropdown'].choices), 2) # Select + Warehouse A

    def test_form_valid_data(self):
        data = {
            'from_date': '01-05-2023',
            'to_date': '31-05-2023',
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': 'Category',
            'category': 'CAT001',
            'search_code_type': 'tblDG_Item_Master.ItemCode',
            'search_item_code': 'TEST',
            'location_dropdown': '', # Not relevant for this case
        }
        form = StockStatementFilterForm(data, comp_id=1, fin_year_id=1)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['from_date'], date(2023, 5, 1))

    def test_form_invalid_date_range(self):
        data = {
            'from_date': '31-05-2023',
            'to_date': '01-05-2023',
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': 'Category',
            'category': 'CAT001',
            'search_code_type': '',
            'search_item_code': '',
            'location_dropdown': '',
        }
        form = StockStatementFilterForm(data, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('to_date', form.errors)
        self.assertIn('From date should be Less than or Equal to To Date!', form.errors['to_date'][0])

    def test_form_from_date_less_than_opening_date(self):
        data = {
            'from_date': '01-03-2023', # Before 01-04-2023
            'to_date': '01-05-2023',
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': 'Category',
            'category': 'CAT001',
            'search_code_type': '',
            'search_item_code': '',
            'location_dropdown': '',
        }
        form = StockStatementFilterForm(data, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)
        self.assertIn('From date should not be Less than Opening Date!', form.errors['from_date'][0])

    def test_form_required_report_type(self):
        data = {
            'from_date': '01-05-2023',
            'to_date': '31-05-2023',
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': '', # Select
            'category': '',
            'search_code_type': '',
            'search_item_code': '',
            'location_dropdown': '',
        }
        form = StockStatementFilterForm(data, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('report_type', form.errors)
        self.assertIn('Please Select Category or WO Items.', form.errors['report_type'][0])

    def test_form_search_item_code_required(self):
        data = {
            'from_date': '01-05-2023',
            'to_date': '31-05-2023',
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': 'WOItems',
            'category': '',
            'search_code_type': 'tblDG_Item_Master.ItemCode',
            'search_item_code': '', # Missing required field
            'location_dropdown': '',
        }
        form = StockStatementFilterForm(data, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('search_item_code', form.errors)
        self.assertIn('This field is required for selected search type.', form.errors['search_item_code'][0])

    def test_form_location_dropdown_required(self):
        data = {
            'from_date': '01-05-2023',
            'to_date': '31-05-2023',
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': 'Category',
            'category': 'CAT001',
            'search_code_type': 'tblDG_Item_Master.Location',
            'search_item_code': '',
            'location_dropdown': '', # Missing required field
        }
        form = StockStatementFilterForm(data, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('location_dropdown', form.errors)
        self.assertIn('Please select a location.', form.errors['location_dropdown'][0])


class StockStatementViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.comp_id = 1
        self.fin_year_id = 1
        self.client.session['comp_id'] = self.comp_id
        self.client.session['fin_year_id'] = self.fin_year_id
        FinancialYear.objects.create(comp_id=self.comp_id, fin_year_id=self.fin_year_id, fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31))
        Category.objects.create(cid='CAT001', symbol='FAB', cname='Fabric', comp_id=self.comp_id)
        ItemMaster.objects.create(item_code='ITEM001', description='Red Fabric', location='Warehouse A', comp_id=self.comp_id, fin_year_id=self.fin_year_id, cid='CAT001')


    def test_filter_view_get(self):
        response = self.client.get(reverse('inventory:stock_statement_filter'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/stock_statement/stock_statement_form.html')
        self.assertContains(response, 'Stock Statement Filter')
        self.assertContains(response, 'id="dynamic-fields-container"') # HTMX target

    def test_filter_view_post_valid(self):
        data = {
            'from_date': '01-05-2023',
            'to_date': '31-05-2023',
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': 'Category',
            'category': 'CAT001',
            'search_code_type': '',
            'search_item_code': '',
            'location_dropdown': '',
        }
        response = self.client.post(reverse('inventory:stock_statement_filter'), data)
        self.assertEqual(response.status_code, 302) # Expect redirect
        self.assertRedirects(response, reverse('inventory:stock_statement_details') + '?Cid=%20AND%20CId%3D%27CAT001%27&RadVal=0&FDate=01-05-2023&TDate=31-05-2023&OpeningDt=01-04-2023&p=&r=%20And%20CId%20is%20not%20null&OverHeads=20&Key=' + ReportParameterService._generate_random_key(length=10)[:-1], fetch_redirect_response=False) # Check redirect URL params, ignoring the random key

    def test_filter_view_post_invalid_htmx(self):
        data = {
            'from_date': '31-05-2023',
            'to_date': '01-05-2023', # Invalid date range
            'po_rate': '0',
            'overheads': '20.0',
            'report_type': '', # Invalid report type
            'category': '',
            'search_code_type': '',
            'search_item_code': '',
            'location_dropdown': '',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('inventory:stock_statement_filter'), data, **headers)
        self.assertEqual(response.status_code, 200) # Should not redirect for HTMX
        self.assertTemplateUsed(response, 'inventory/stock_statement/_stock_statement_form_fields.html')
        self.assertContains(response, 'From date should be Less than or Equal to To Date!')
        self.assertContains(response, 'Please Select Category or WO Items.')


    def test_dynamic_form_fields_view_initial_load(self):
        # Simulate HTMX initial load of dynamic fields
        response = self.client.get(reverse('inventory:get_dynamic_form_fields'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/stock_statement/_stock_statement_form_fields.html')
        self.assertContains(response, '<select name="category"') # Category dropdown should be present on initial load
        self.assertContains(response, '<select name="search_code_type"')
        self.assertContains(response, '<div class="hidden"><select name="location_dropdown"') # Location dropdown hidden by default
        self.assertContains(response, '<div class="hidden"><input type="text" name="search_item_code"') # Search item hidden by default

    def test_dynamic_form_fields_view_category_selected(self):
        # Simulate HTMX request when 'Category' is selected
        response = self.client.get(reverse('inventory:get_dynamic_form_fields'), {'report_type': 'Category'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<select name="category"')
        self.assertContains(response, '<select name="search_code_type"')
        self.assertContains(response, '<div class="hidden"><select name="location_dropdown"') # Still hidden
        self.assertContains(response, '<div class="hidden"><input type="text" name="search_item_code"') # Still hidden

    def test_dynamic_form_fields_view_woitems_selected(self):
        # Simulate HTMX request when 'WOItems' is selected
        response = self.client.get(reverse('inventory:get_dynamic_form_fields'), {'report_type': 'WOItems'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<div class="hidden"><select name="category"') # Category should be hidden
        self.assertContains(response, '<select name="search_code_type"')
        self.assertContains(response, '<div class="hidden"><select name="location_dropdown"')
        self.assertContains(response, '<div class="hidden"><input type="text" name="search_item_code"')

    def test_dynamic_form_fields_view_search_by_location_selected(self):
        # Simulate HTMX request when 'Location' search type is selected
        response = self.client.get(reverse('inventory:get_dynamic_form_fields'), {'report_type': 'Category', 'search_code_type': 'tblDG_Item_Master.Location'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<select name="location_dropdown"') # Location dropdown should be visible
        self.assertContains(response, '<div class="hidden"><input type="text" name="search_item_code"') # Search item code should be hidden

    def test_dynamic_form_fields_view_search_by_itemcode_selected(self):
        # Simulate HTMX request when 'Item Code' search type is selected
        response = self.client.get(reverse('inventory:get_dynamic_form_fields'), {'report_type': 'Category', 'search_code_type': 'tblDG_Item_Master.ItemCode'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<div class="hidden"><select name="location_dropdown"') # Location dropdown should be hidden
        self.assertContains(response, '<input type="text" name="search_item_code"') # Search item code should be visible

    def test_stock_statement_details_view(self):
        # Test the dummy details page
        response = self.client.get(reverse('inventory:stock_statement_details') + '?param1=value1&param2=value2')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/stock_statement/stock_statement_details.html')
        self.assertContains(response, 'param1: value1')
        self.assertContains(response, 'param2: value2')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Form Submission:** The main form `hx-post` to the same URL (`{% url 'inventory:stock_statement_filter' %}`). `hx-swap="none"` is used with `hx-trigger="submit"` to allow the Django view to handle the redirect (or re-render the form with errors via HTMX `hx-swap="outerHTML"` if `form_invalid`). The `htmx:afterSwap` event listener in `extra_js` handles `HX-Redirect` header for full page navigation.
-   **HTMX for Dynamic Fields:**
    -   The `report_type` (`DrpType`) and `search_code_type` (`DrpSearchCode`) dropdowns have `hx-get` attributes pointing to `{% url 'inventory:get_dynamic_form_fields' %}`.
    -   `hx-target="#dynamic-fields-container"` ensures only the relevant part of the form (wrapped in a `div` with this ID) is updated.
    -   `hx-swap="outerHTML"` replaces the entire `dynamic-fields-container` div with the new content from the partial.
    -   `hx-trigger="change"` fires the HTMX request when the dropdown selection changes.
-   **Alpine.js:** Not explicitly used for dynamic visibility in this conversion as HTMX is handling the swapping of content. Alpine.js could be used for simple client-side toggles for non-form-submission related UI elements, or for custom alert dialogs if the ASP.NET `alert()` calls were to be replaced by richer modal messages. For datepickers, Flatpickr is recommended and integrated via CDN.
-   **DataTables:** As stated, DataTables would be implemented on the `Stock_Statement_Details.aspx` equivalent page (`stock_statement_details.html` in Django), which is a separate page from this filter form. The `_table.html` example template provided in the prompt is a template for a list view, not a form, so it's not applicable directly here.

### Final Notes

This comprehensive plan transforms the ASP.NET Stock Statement filter page into a modern, maintainable Django application.
-   **Non-technical Language:** The explanations prioritize clarity for business stakeholders, focusing on "what" rather than overly technical "hows."
-   **Automation Focus:** The structure is designed to be easily digestible by AI-assisted tools for code generation, with clear mappings from ASP.NET concepts to Django constructs.
-   **Fat Model, Thin View:** Business logic, including data lookup for dropdowns and complex report parameter generation, is encapsulated in the `FinancialYear`, `Category`, `ItemMaster` models and the `ReportParameterService` (which logically extends the model layer for complex business rules). Views are kept concise, primarily handling HTTP requests and responses.
-   **HTMX/Alpine.js:** All dynamic UI interactions are handled via HTMX, minimizing custom JavaScript.
-   **DRY & Maintainable:** Templates are broken into partials for reusability. Models, forms, and views are designed for clear separation of concerns.
-   **Test Coverage:** Comprehensive unit and integration tests ensure the reliability of the migrated components.