## ASP.NET to Django Conversion Script: Stock Ledger Details

This document outlines the comprehensive plan for modernizing the ASP.NET Stock Ledger Details page to a Django-based solution. The focus is on leveraging AI-assisted automation for systematic conversion, adhering to modern Django practices, and delivering a highly interactive user experience with HTMX and Alpine.js.

### Business Value of Modernization

Migrating to Django offers significant business advantages:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET technologies, making the application easier to maintain, understand, and extend.
2.  **Improved Performance & Scalability:** Django's architecture is inherently more scalable and performant for web applications, leading to faster loading times and better user responsiveness.
3.  **Enhanced User Experience (UX):** HTMX and Alpine.js enable dynamic, single-page application (SPA)-like interactions without the complexity of traditional JavaScript frameworks, providing a smoother and more intuitive user experience for ledger viewing and filtering. DataTables ensures efficient handling of large datasets.
4.  **Cost Efficiency:** Python's ecosystem, combined with Django's rapid development capabilities, can lead to faster feature delivery and lower ongoing development costs compared to maintaining legacy systems.
5.  **Future-Proofing:** Adopting a popular, open-source framework like Django ensures access to a vibrant community, continuous updates, and a wealth of tools and libraries, making the application resilient to future technology changes.
6.  **Better Testability and Reliability:** Django's structured approach, combined with comprehensive testing, leads to more robust and reliable software, reducing bugs and operational disruptions.

### Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables to compile the stock ledger. For this modernization, we will map these to Django models, using `managed = False` to connect to the existing database schema without Django migrations altering it.

**Core Tables Identified:**

*   **`tblDG_Item_Master`**: Main item details.
    *   Columns: `Id` (PK), `ItemCode`, `ManfDesc` (Description), `UOMBasic` (Foreign Key to `Unit_Master`), `AttName` (Spec Sheet file name), `FileName` (Image file name), `FinYearId` (Foreign Key to `tblFinancial_master`), `CompId`, `OpeningBalQty`.
*   **`Unit_Master`**: Unit of Measure definitions.
    *   Columns: `Id` (PK), `Symbol`.
*   **`tblHR_OfficeStaff`**: Employee details.
    *   Columns: `EmpId` (PK), `CompId`, `Title`, `EmployeeName`.
*   **`BusinessGroup`**: Business unit/department information.
    *   Columns: `Id` (PK), `Symbol` (Department Name).
*   **`tblFinancial_master`**: Financial year configurations.
    *   Columns: `FinYearId` (PK), `CompId`, `FinYearFrom`, `FinYearTo`.
*   **`tblDG_Item_Master_Clone`**: Specific item opening quantities for different financial years.
    *   Columns: `ItemId` (FK to `tblDG_Item_Master`), `CompId`, `FinYearId` (FK), `OpeningQty`, `OpeningDate`.

**Key Transaction Tables (involved in ledger aggregation):**

The ASP.NET `Page_Load` and `clsFunctions` make extensive use of complex joins across many transaction tables. These include:
*   `tblQc_MaterialReturnQuality_Details`, `tblQc_MaterialReturnQuality_Master`, `tblInv_MaterialReturn_Details`, `tblInv_MaterialReturn_Master` (for MRN transactions)
*   `tblPM_MaterialCreditNote_Details`, `tblQc_AuthorizedMCN`, `tblPM_MaterialCreditNote_Master`, `tblDG_BOM_Master` (for MCN transactions)
*   `tblQc_MaterialQuality_Details`, `tblQc_MaterialQuality_Master`, `tblinv_MaterialReceived_Details`, `tblinv_MaterialReceived_Master`, `tblMM_PO_Details`, `tblMM_PO_Master`, `tblMM_SPR_Details`, `tblMM_SPR_Master`, `tblMM_PR_Details`, `tblMM_PR_Master`, `AccHead` (for GQN transactions)
*   `tblinv_MaterialServiceNote_Master`, `tblinv_MaterialServiceNote_Details`, `tblInv_Inward_Master`, `tblInv_Inward_Details` (for GSN transactions)
*   `tblInv_MaterialIssue_Details`, `tblInv_MaterialIssue_Master`, `tblInv_MaterialRequisition_Master`, `tblInv_MaterialRequisition_Details` (for MIN transactions)
*   `tblInv_WIS_Master`, `tblInv_WIS_Details` (for WIS transactions)

An AI-driven automation process would analyze the specific join conditions, filters, and aggregations within the C# SQL queries to precisely translate these into highly optimized Django ORM statements within the "fat model" layer. For this example, we will conceptually represent this complex aggregation.

### Step 2: Identify Backend Functionality

The `StockLedger_Details.aspx` page primarily provides a **Read/Report** function:

*   **Display Item Details:** Shows `Item Code`, `Description`, `Unit`, and links to `Image` and `Spec. Sheet`.
*   **Display Stock Ledger:** Presents a detailed transaction log (GridView) for the specified item within a given date range. This involves complex data aggregation from multiple transaction tables.
*   **Calculate Summary Quantities:** Determines `Opening Qty`, `Total Received Qty`, `Total Issued Qty`, and `Closing Qty` for the selected period, incorporating intricate financial year logic and past transactions.
*   **File Downloads:** Allows viewing/downloading of the associated item image and specification sheet.
*   **Navigation:** "Print" button to generate a print-friendly version and "Cancel" button to navigate back.

No direct "Create", "Update", or "Delete" operations are performed on the ledger entries from this page itself.

### Step 3: Infer UI Components

The ASP.NET controls will be translated to standard HTML elements with Tailwind CSS for styling and HTMX/Alpine.js for dynamic behavior.

*   **Labels (`<asp:Label>`):** Will become `<span>` tags displaying dynamic data from Django context.
*   **LinkButtons (`<asp:LinkButton>`):** Will become `<a>` tags with appropriate `href` attributes for file downloads or `button` tags with HTMX for actions.
*   **GridView (`<asp:GridView>`):** Will be replaced by a `<table>` element initialized as a DataTables instance, ensuring client-side sorting, searching, and pagination. The data will be loaded dynamically via HTMX.
*   **Buttons (`<asp:Button>`):** Will become `<a>` or `<button>` tags with direct links or HTMX attributes for form submissions/redirections.
*   **Date Inputs:** The ASP.NET page uses query strings for `From Date` and `To Date`. In Django, these will be implemented with standard HTML `<input type="text">` fields enhanced with Flatpickr for date selection and Alpine.js for binding, triggering HTMX requests upon "Apply Filters".

### Step 4: Generate Django Code

The Django application will be named `inventory_reports`.

#### 4.1 Models (`inventory_reports/models.py`)

This section defines the Django models for the core tables and encapsulates the complex stock ledger logic within a custom manager, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import F, Sum, Q, Min, Max
from django.utils import timezone
from datetime import datetime, timedelta, date
import logging
from decimal import Decimal

logger = logging.getLogger(__name__)

# Helper to format dates and times as in C# fun.FromDateDMY and fun.ShortDateTime
def format_date_dmy(date_obj):
    """Formats a date object to 'DD-MM-YYYY' string."""
    if not date_obj:
        return ""
    return date_obj.strftime("%d-%m-%Y")

def format_short_datetime(date_obj, time_obj):
    """Combines date and time objects into a sortable 'YYYY-MM-DD HH:MM:SS' string."""
    if not date_obj or not time_obj:
        return ""
    dt_obj = datetime.combine(date_obj, time_obj)
    return dt_obj.strftime("%Y-%m-%d %H:%M:%S")

class FinancialYear(models.Model):
    """Maps to tblFinancial_master for financial year details."""
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_from = models.DateField(db_column='FinYearFrom')
    fin_year_to = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"{self.fin_year_from.year}-{self.fin_year_to.year}"

class Unit(models.Model):
    """Maps to Unit_Master for item units of measurement."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class Employee(models.Model):
    """Maps to tblHR_OfficeStaff for employee details."""
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name}".strip()

class BusinessGroup(models.Model):
    """Maps to BusinessGroup for department details."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class StockLedgerEntry:
    """
    A lightweight data class to represent a single row in the aggregated stock ledger.
    This is not a Django model, but a Python object assembled from various database queries.
    """
    def __init__(self, sys_date, sys_time, processed_by_name, accepted_qty,
                 department_name, wo_no, issued_qty, trans_for, trans_of,
                 transacted_by_name):
        self.sys_date = sys_date
        self.sys_time = sys_time
        self.processed_by_name = processed_by_name
        self.accepted_qty = Decimal(accepted_qty) if accepted_qty is not None else Decimal('0.000')
        self.department_name = department_name
        self.wo_no = wo_no
        self.issued_qty = Decimal(issued_qty) if issued_qty is not None else Decimal('0.000')
        self.trans_for = trans_for
        self.trans_of = trans_of
        self.transacted_by_name = transacted_by_name
        
        # Calculate seconds and sort_datetime for sorting as in ASP.NET
        try:
            sys_time_dt = datetime.strptime(sys_time, "%H:%M:%S").time()
            self.seconds = sys_time_dt.hour * 3600 + sys_time_dt.minute * 60 + sys_time_dt.second
            date_obj = datetime.strptime(sys_date, "%d-%m-%Y").date()
            self.sort_datetime = format_short_datetime(date_obj, sys_time_dt)
        except (ValueError, TypeError):
            self.seconds = 0
            self.sort_datetime = "0000-00-00 00:00:00" # Fallback for sorting

class StockLedgerManager(models.Manager):
    """
    Custom manager for the Item model, encapsulating all complex stock ledger
    data aggregation and calculation logic from the original C# code-behind.
    This ensures a 'Fat Model, Thin View' architecture.
    """
    def get_stock_ledger_details(self, item_id, comp_id, fin_year_id, from_date_str, to_date_str):
        """
        Aggregates stock transaction data from various tables based on the C# logic.
        
        An AI-driven automation process would meticulously translate each complex
        SQL query (MRN, MCN, GQN, GSN, MIN, WIS) from the C# code into optimized
        Django ORM queries (using F, Q objects, .annotate(), .aggregate(),
        .select_related(), .prefetch_related() or even raw SQL).
        
        For demonstration, this method returns placeholder data structured
        as StockLedgerEntry objects.
        """
        from_date = datetime.strptime(from_date_str, "%d-%m-%Y").date()
        to_date = datetime.strptime(to_date_str, "%d-%m-%Y").date()

        # In a real scenario, this is where a series of complex ORM queries
        # for MRN, MCN, GQN, GSN, MIN, WIS would be executed.
        # Each query would fetch relevant fields and join related tables
        # (e.g., Employee, BusinessGroup) to get names/symbols.
        
        # Placeholder for aggregated data (simulates the C# DataTable 'dt')
        ledger_entries_raw = []
        
        # Example dummy data, simulating aggregated results for Item ID 1
        # In actual migration, these would be results of the complex ORM queries.
        if item_id == 1 and from_date <= date(2023, 4, 30) and to_date >= date(2023, 4, 1):
            ledger_entries_raw.append({
                'sys_date': date(2023, 4, 15), 'sys_time': '10:30:00',
                'processed_by_emp_id': 1, 'accepted_qty': 100.500, 'dept_id': 1, 'wo_no': 'WO-001', 'issued_qty': 0,
                'trans_for': 'MRN No[123]', 'trans_of': 'MRQN No[456]', 'transacted_by_emp_id': 2,
            })
            ledger_entries_raw.append({
                'sys_date': date(2023, 4, 16), 'sys_time': '14:00:00',
                'processed_by_emp_id': 3, 'accepted_qty': 0, 'dept_id': 2, 'wo_no': 'WO-002', 'issued_qty': 50.250,
                'trans_for': 'MRS No[789]', 'trans_of': 'MIN No[012]', 'transacted_by_emp_id': 3,
            })
            ledger_entries_raw.append({
                'sys_date': date(2023, 4, 17), 'sys_time': '09:15:00',
                'processed_by_emp_id': 1, 'accepted_qty': 75.000, 'dept_id': 1, 'wo_no': 'WO-003', 'issued_qty': 0,
                'trans_for': 'PO No[345]', 'trans_of': 'GQN No[678]', 'transacted_by_emp_id': 2,
            })
            ledger_entries_raw.append({
                'sys_date': date(2023, 4, 18), 'sys_time': '11:45:00',
                'processed_by_emp_id': 4, 'accepted_qty': 0, 'dept_id': 3, 'wo_no': 'WO-004', 'issued_qty': 20.000,
                'trans_for': 'WIS No[901]', 'trans_of': 'WIS No[901]', 'transacted_by_emp_id': 4,
            })
        
        final_ledger_entries = []
        for entry_data in ledger_entries_raw:
            # Resolve foreign key IDs to display names
            processed_by_name_obj = Employee.objects.filter(emp_id=entry_data['processed_by_emp_id'], comp_id=comp_id).first()
            processed_by_name = str(processed_by_name_obj) if processed_by_name_obj else 'NA'
            
            transacted_by_name_obj = Employee.objects.filter(emp_id=entry_data['transacted_by_emp_id'], comp_id=comp_id).first()
            transacted_by_name = str(transacted_by_name_obj) if transacted_by_name_obj else 'NA'

            department_name_obj = BusinessGroup.objects.filter(id=entry_data['dept_id']).first()
            department_name = str(department_name_obj) if department_name_obj else 'NA'

            # Filter by date range (dates are converted to actual date objects)
            if from_date <= entry_data['sys_date'] <= to_date:
                final_ledger_entries.append(StockLedgerEntry(
                    sys_date=format_date_dmy(entry_data['sys_date']),
                    sys_time=entry_data['sys_time'],
                    processed_by_name=processed_by_name,
                    accepted_qty=entry_data['accepted_qty'],
                    department_name=department_name,
                    wo_no=entry_data['wo_no'],
                    issued_qty=entry_data['issued_qty'],
                    trans_for=entry_data['trans_for'],
                    trans_of=entry_data['trans_of'],
                    transacted_by_name=transacted_by_name,
                ))
        
        # Sort the ledger entries by SortDateTime (descending) as in C#
        final_ledger_entries.sort(key=lambda x: x.sort_datetime, reverse=True)
        
        return final_ledger_entries

    def _get_opening_closing_qty(self, item_id, comp_id, fin_year_id, from_date, to_date):
        """
        Calculates opening, total received, total issued, and closing quantities.
        This replicates the complex C# logic involving financial years and various _QTY functions.
        """
        open_qty = Decimal('0.000')
        closing_qty = Decimal('0.000')
        total_received_qty = Decimal('0.000')
        total_issued_qty = Decimal('0.000')

        item = Item.objects.filter(id=item_id, comp_id=comp_id).first()
        current_fin_year = FinancialYear.objects.filter(fin_year_id=fin_year_id, comp_id=comp_id).first()
        
        if not item or not current_fin_year:
            logger.warning(f"Item or FinancialYear not found for ItemId:{item_id}, CompId:{comp_id}, FinYearId:{fin_year_id}")
            return open_qty, total_received_qty, total_issued_qty, closing_qty

        opening_date_of_fin_year = current_fin_year.fin_year_from

        # Replicate C# opening quantity logic
        if from_date == opening_date_of_fin_year:
            open_qty = item.opening_bal_qty
        else:
            # Calculate opening quantity for the given 'from_date'
            # This involves summing transactions from `opening_date_of_fin_year`
            # up to `from_date - 1 day`.
            
            # This is where the C# `fun.GQN_SPRQTY`, `fun.MRQN_QTY`, `fun.MIN_IssuQTY`, etc.
            # would be called for the period up to `from_date - 1 day`.
            # For this example, we use a simplified calculation.
            prev_day = from_date - timedelta(days=1)
            
            # Placeholder for historical received/issued quantities (pre-from_date)
            # In a real scenario, these would be complex ORM aggregations.
            historical_received_sum = Decimal('0.000')
            historical_issued_sum = Decimal('0.000')

            # Iterate through the conceptual ledger entries from the start of the financial year
            # up to the day before the 'from_date'
            # This logic would be implemented by calling `self.get_stock_ledger_details`
            # for the `(opening_date_of_fin_year, prev_day)` range.
            historical_entries = self.get_stock_ledger_details(item_id, comp_id, fin_year_id, 
                                                                format_date_dmy(opening_date_of_fin_year), 
                                                                format_date_dmy(prev_day))
            
            historical_received_sum = sum(entry.accepted_qty for entry in historical_entries)
            historical_issued_sum = sum(entry.issued_qty for entry in historical_entries)

            open_qty = item.opening_bal_qty + historical_received_sum - historical_issued_sum

        # Calculate total received and issued quantities for the current `from_date` to `to_date` range
        current_period_ledger_entries = self.get_stock_ledger_details(
            item_id, comp_id, fin_year_id, format_date_dmy(from_date), format_date_dmy(to_date)
        )
        total_received_qty = sum(entry.accepted_qty for entry in current_period_ledger_entries)
        total_issued_qty = sum(entry.issued_qty for entry in current_period_ledger_entries)

        # Calculate closing quantity
        closing_qty = open_qty + total_received_qty - total_issued_qty
        
        return round(open_qty, 5), round(total_received_qty, 5), round(total_issued_qty, 5), round(closing_qty, 5)

class Item(models.Model):
    """Maps to tblDG_Item_Master, the main item table."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    description = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True) # Spec Sheet file name
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True) # Image file name
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    opening_bal_qty = models.DecimalField(db_column='OpeningBalQty', max_digits=18, decimal_places=5, default=Decimal('0.000'))

    objects = StockLedgerManager() # Attach our custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.description}"

    def get_image_url(self):
        """Returns the URL for downloading the item image."""
        if self.file_name:
            return f"/inventory_reports/download/item/{self.id}/image/"
        return None

    def get_spec_sheet_url(self):
        """Returns the URL for downloading the item specification sheet."""
        if self.att_name:
            return f"/inventory_reports/download/item/{self.id}/spec_sheet/"
        return None

class ItemMasterClone(models.Model):
    """Maps to tblDG_Item_Master_Clone for historical item opening balances."""
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='clones')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    opening_qty = models.DecimalField(db_column='OpeningQty', max_digits=18, decimal_places=5, default=Decimal('0.000'))
    opening_date = models.DateField(db_column='OpeningDate')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master_Clone'
        verbose_name = 'Item Master Clone'
        verbose_name_plural = 'Item Master Clones'
        unique_together = (('item', 'fin_year', 'comp_id'),)

    def __str__(self):
        return f"Clone for {self.item.item_code} ({self.fin_year})"

```

#### 4.2 Forms

The original ASP.NET page primarily retrieves data via query parameters and session, and does not have a user-editable form for ledger entries. Therefore, no Django form is needed for this specific page. Date range selection will be handled directly in the template with Alpine.js binding to HTMX.

#### 4.3 Views (`inventory_reports/views.py`)

The views are kept thin, delegating all business logic and data aggregation to the `StockLedgerManager` in the `models.py` file.

```python
from django.views.generic import DetailView
from django.shortcuts import get_object_or_404, render
from django.http import HttpResponse
from django.contrib import messages
from .models import Item, format_date_dmy # Import format_date_dmy for default date
from datetime import datetime, date
import os # Required for file system operations

class StockLedgerDetailView(DetailView):
    """
    Displays the stock ledger details for a specific item.
    This view delegates complex data aggregation to the Item model's custom manager.
    """
    model = Item
    template_name = 'inventory_reports/stock_ledger/detail.html'
    context_object_name = 'item'
    pk_url_kwarg = 'item_id' # Expects item_id from URL

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Session/Query string parameters, defaulting for robust operation
        comp_id = self.request.session.get('compid', 1) 
        fin_year_id = self.request.session.get('finyear', 1) 
        
        # Dates from query parameters, using default if not provided
        today = date.today()
        default_from_date = today.replace(day=1) # First day of current month
        default_to_date = today 
        
        from_date_str = self.request.GET.get('fd', format_date_dmy(default_from_date))
        to_date_str = self.request.GET.get('td', format_date_dmy(default_to_date))
        
        item = self.get_object() # The Item instance to display
        
        # Retrieve ledger entries and summary quantities using the custom manager.
        # All heavy lifting (SQL queries, calculations) is done in the model layer.
        ledger_entries_data = Item.objects.get_stock_ledger_details(
            item_id=item.id,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            from_date_str=from_date_str,
            to_date_str=to_date_str
        )
        
        open_qty, total_received_qty, total_issued_qty, closing_qty = Item.objects._get_opening_closing_qty(
            item_id=item.id,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            from_date=datetime.strptime(from_date_str, "%d-%m-%Y").date(),
            to_date=datetime.strptime(to_date_str, "%d-%m-%Y").date()
        )

        context['from_date_str'] = from_date_str
        context['to_date_str'] = to_date_str
        context['ledger_entries'] = ledger_entries_data
        context['opening_qty'] = open_qty
        context['total_received_qty'] = total_received_qty
        context['total_issued_qty'] = total_issued_qty
        context['closing_qty'] = closing_qty
        
        return context

class StockLedgerTablePartialView(StockLedgerDetailView):
    """
    Renders only the stock ledger table partial for HTMX requests.
    Inherits context logic from StockLedgerDetailView.
    """
    template_name = 'inventory_reports/stock_ledger/_stock_ledger_table.html'

def download_item_file(request, item_id, file_type):
    """
    Handles downloading item image or specification sheet files.
    This replaces the C# Response.Redirect to DownloadFile.aspx.
    """
    item = get_object_or_404(Item, id=item_id)
    
    file_path = None
    file_name_on_disk = None
    content_type = 'application/octet-stream' # Default content type

    # In a real scenario, ensure settings.MEDIA_ROOT is configured and files exist.
    # For this example, assuming files are directly named as in DB and stored in MEDIA_ROOT.
    from django.conf import settings
    media_root = settings.MEDIA_ROOT # Ensure MEDIA_ROOT is defined in Django settings

    if file_type == 'image' and item.file_name:
        file_name_on_disk = item.file_name
        file_path = os.path.join(media_root, 'item_images', file_name_on_disk) # Adjust path as needed
        content_type = 'image/jpeg' # Adjust based on actual image type
    elif file_type == 'spec_sheet' and item.att_name:
        file_name_on_disk = item.att_name
        file_path = os.path.join(media_root, 'spec_sheets', file_name_on_disk) # Adjust path as needed
        content_type = 'application/pdf' # Adjust based on actual doc type

    if file_path and os.path.exists(file_path):
        with open(file_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
            return response
    else:
        messages.error(request, f"Requested file not found for {file_type}: {file_name_on_disk or 'N/A'}.")
        return HttpResponse("File not found or invalid request.", status=404)

def stock_ledger_print_view(request, item_id, from_date_str, to_date_str):
    """
    Placeholder for the print functionality. This view would generate a print-friendly
    HTML page or a PDF report (e.g., using WeasyPrint or ReportLab).
    """
    item = get_object_or_404(Item, id=item_id)
    
    # Retrieve ledger entries and summary quantities as done in StockLedgerDetailView
    comp_id = request.session.get('compid', 1) 
    fin_year_id = request.session.get('finyear', 1) 
    
    ledger_entries = Item.objects.get_stock_ledger_details(
        item_id=item.id,
        comp_id=comp_id,
        fin_year_id=fin_year_id,
        from_date_str=from_date_str,
        to_date_str=to_date_str
    )
    
    open_qty, total_received_qty, total_issued_qty, closing_qty = Item.objects._get_opening_closing_qty(
        item_id=item.id,
        comp_id=comp_id,
        fin_year_id=fin_year_id,
        from_date=datetime.strptime(from_date_str, "%d-%m-%Y").date(),
        to_date=datetime.strptime(to_date_str, "%d-%m-%Y").date()
    )

    context = {
        'item': item,
        'from_date_str': from_date_str,
        'to_date_str': to_date_str,
        'ledger_entries': ledger_entries,
        'opening_qty': open_qty,
        'total_received_qty': total_received_qty,
        'total_issued_qty': total_issued_qty,
        'closing_qty': closing_qty,
    }
    return render(request, 'inventory_reports/stock_ledger/print.html', context) # This template needs to be created
```

#### 4.4 Templates

Templates will follow DRY principles, extending `core/base.html` and using partials for HTMX-driven content.

**`inventory_reports/stock_ledger/detail.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Stock Ledger for Item: <span class="text-indigo-600">{{ item.item_code }}</span></h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 text-sm">
            <div>
                <span class="font-bold text-gray-700">Item Code:</span> <span id="Lblitem">{{ item.item_code }}</span>
            </div>
            <div>
                <span class="font-bold text-gray-700">Unit:</span> <span id="LblUnit">{{ item.unit.symbol }}</span>
            </div>
            <div>
                <span class="font-bold text-gray-700">Image:</span> 
                {% if item.file_name %}
                <a href="{{ item.get_image_url }}" class="text-blue-600 hover:underline" target="_blank">View</a>
                {% else %}
                N/A
                {% endif %}
            </div>
            <div>
                <span class="font-bold text-gray-700">Description:</span> <span id="LblDesc">{{ item.description }}</span>
            </div>
            <div>
                <span class="font-bold text-gray-700">Spec. Sheet:</span> 
                {% if item.att_name %}
                <a href="{{ item.get_spec_sheet_url }}" class="text-blue-600 hover:underline" target="_blank">View</a>
                {% else %}
                N/A
                {% endif %}
            </div>
        </div>

        <div class="flex flex-wrap items-center space-x-4 mb-6" 
             x-data="{ fromDate: '{{ from_date_str }}', toDate: '{{ to_date_str }}' }">
            <label for="from_date" class="font-bold text-gray-700">From Date:</label>
            <input type="text" id="from_date" class="form-input block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                   x-model="fromDate"
                   x-init="flatpickr($el, { dateFormat: 'd-m-Y' });"
                   placeholder="DD-MM-YYYY">
            
            <label for="to_date" class="font-bold text-gray-700">To Date:</label>
            <input type="text" id="to_date" class="form-input block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                   x-model="toDate"
                   x-init="flatpickr($el, { dateFormat: 'd-m-Y' });"
                   placeholder="DD-MM-YYYY">
            
            <button class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded mt-4 md:mt-0"
                    id="apply_filters"
                    hx-get="{% url 'stock_ledger_table' item_id=item.id %}"
                    hx-target="#stockLedgerTable-container"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    _="on click set @hx-get to `{% url 'stock_ledger_table' item_id=item.id %}?fd=${fromDate}&td=${toDate}` then htmx.trigger(this, 'click')">
                Apply Filters
            </button>
        </div>

        <div id="stockLedgerTable-container"
             hx-trigger="load, reloadStockLedger from:body"
             hx-get="{% url 'stock_ledger_table' item_id=item.id %}?fd={{ from_date_str }}&td={{ to_date_str }}"
             hx-swap="innerHTML">
            <!-- DataTables will be loaded here via HTMX -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
                <p class="mt-2 text-gray-600">Loading Stock Ledger...</p>
            </div>
        </div>

        <div class="bg-gray-50 p-4 rounded-lg mt-6">
            <h3 class="text-lg font-bold text-gray-800 mb-3">Summary Quantities</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="flex justify-between items-center">
                    <span class="font-bold text-green-700">Opening Qty:</span> 
                    <span class="font-bold text-gray-900" id="lblopeningQty">{{ opening_qty|floatformat:"3" }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="font-bold text-purple-700">Total Received Qty:</span> 
                    <span class="font-bold text-gray-900" id="lblRqty">{{ total_received_qty|floatformat:"3" }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="font-bold text-purple-700">Total Issued Qty:</span> 
                    <span class="font-bold text-gray-900" id="lblIqty">{{ total_issued_qty|floatformat:"3" }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="font-bold text-green-700">Closing Qty:</span> 
                    <span class="font-bold text-gray-900" id="lblclosingQty">{{ closing_qty|floatformat:"3" }}</span>
                </div>
            </div>
        </div>

        <div class="mt-8 flex justify-center space-x-4">
            <a href="{% url 'stock_ledger_print' item_id=item.id from_date_str=from_date_str to_date_str=to_date_str %}" target="_blank"
               class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md">
                Print
            </a>
            <a href="{% url 'stock_ledger_list_all_items' %}" 
               class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-6 rounded-md shadow-md">
                Cancel
            </a>
        </div>

    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here for the date inputs,
        // as flatpickr handles it. x-model syncs the value.
    });

    // Re-initialize DataTable when HTMX swaps the table container
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'stockLedgerTable-container') {
            // Ensure DataTable is destroyed before re-initializing if it exists
            if ($.fn.DataTable.isDataTable('#stockLedgerTable')) {
                $('#stockLedgerTable').DataTable().destroy();
            }
            $('#stockLedgerTable').DataTable({
                "pageLength": 14, // Matching ASP.NET GridView PageSize
                "lengthMenu": [[10, 14, 25, 50, -1], [10, 14, 25, 50, "All"]],
                "order": [[12, "desc"]] // Sort by SortDateTime column (index 12)
            });
        }
    });
</script>
{% endblock %}
```

**`inventory_reports/stock_ledger/_stock_ledger_table.html`** (Partial for HTMX)

```html
<div class="overflow-x-auto shadow-md rounded-lg">
    <table id="stockLedgerTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trans. by</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Proc. by</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WONo</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trans. For</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trans. Of</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rece. Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Qty</th>
                <!-- Hidden columns for DataTables internal sorting (0-indexed column 11 and 12) -->
                <th class="hidden">Seconds</th>
                <th class="hidden">SortDateTime</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in ledger_entries %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ entry.sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ entry.sys_time }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.transacted_by_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.processed_by_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ entry.department_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ entry.wo_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.trans_for }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.trans_of }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ entry.accepted_qty|floatformat:"3" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ entry.issued_qty|floatformat:"3" }}</td>
                <td class="hidden">{{ entry.seconds }}</td>
                <td class="hidden">{{ entry.sort_datetime }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" class="py-8 px-4 text-center text-lg text-maroon font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script. This runs AFTER the HTMX swap. -->
<script>
    $(document).ready(function() {
        // Ensure DataTable is destroyed before re-initializing if it exists from a previous HTMX swap
        if ($.fn.DataTable.isDataTable('#stockLedgerTable')) {
            $('#stockLedgerTable').DataTable().destroy();
        }
        $('#stockLedgerTable').DataTable({
            "pageLength": 14,
            "lengthMenu": [[10, 14, 25, 50, -1], [10, 14, 25, 50, "All"]],
            "order": [[12, "desc"]] // Sort by SortDateTime column (0-indexed is 12)
        });
    });
</script>
```

**`inventory_reports/stock_ledger/print.html`** (Placeholder for Print View)

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Ledger Print - {{ item.item_code }}</title>
    <style>
        /* Add print-specific CSS here */
        body { font-family: sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .summary-box { border: 1px solid #ccc; padding: 15px; margin-top: 20px; background-color: #f9f9f9; }
        .summary-row { display: flex; justify-content: space-between; margin-bottom: 5px; }
        .summary-label { font-weight: bold; }
    </style>
</head>
<body>
    <h1>Stock Ledger Report</h1>
    <p><strong>Item Code:</strong> {{ item.item_code }}</p>
    <p><strong>Description:</strong> {{ item.description }}</p>
    <p><strong>Unit:</strong> {{ item.unit.symbol }}</p>
    <p><strong>Period:</strong> {{ from_date_str }} to {{ to_date_str }}</p>

    <h2>Transaction Details</h2>
    <table>
        <thead>
            <tr>
                <th>SN</th>
                <th>Date</th>
                <th>Time</th>
                <th>Trans. by</th>
                <th>Proc. by</th>
                <th>BG Group</th>
                <th>WONo</th>
                <th>Trans. For</th>
                <th>Trans. Of</th>
                <th>Rece. Qty</th>
                <th>Issued Qty</th>
            </tr>
        </thead>
        <tbody>
            {% for entry in ledger_entries %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ entry.sys_date }}</td>
                <td>{{ entry.sys_time }}</td>
                <td>{{ entry.transacted_by_name }}</td>
                <td>{{ entry.processed_by_name }}</td>
                <td>{{ entry.department_name }}</td>
                <td>{{ entry.wo_no }}</td>
                <td>{{ entry.trans_for }}</td>
                <td>{{ entry.trans_of }}</td>
                <td style="text-align: right;">{{ entry.accepted_qty|floatformat:"3" }}</td>
                <td style="text-align: right;">{{ entry.issued_qty|floatformat:"3" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" style="text-align: center;">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="summary-box">
        <h3>Summary Quantities</h3>
        <div class="summary-row">
            <span class="summary-label">Opening Qty:</span> <span>{{ opening_qty|floatformat:"3" }}</span>
        </div>
        <div class="summary-row">
            <span class="summary-label">Total Received Qty:</span> <span>{{ total_received_qty|floatformat:"3" }}</span>
        </div>
        <div class="summary-row">
            <span class="summary-label">Total Issued Qty:</span> <span>{{ total_issued_qty|floatformat:"3" }}</span>
        </div>
        <div class="summary-row">
            <span class="summary-label">Closing Qty:</span> <span>{{ closing_qty|floatformat:"3" }}</span>
        </div>
    </div>
</body>
</html>
```

#### 4.5 URLs (`inventory_reports/urls.py`)

URL patterns for accessing the stock ledger details, HTMX partials, file downloads, and print views.

```python
from django.urls import path
from .views import (
    StockLedgerDetailView,
    StockLedgerTablePartialView,
    download_item_file,
    stock_ledger_print_view
)
# Assuming a root list view for items also exists in another app or here
# from your_inventory_app.views import ItemListView 

# Placeholder for ItemListView if it doesn't exist in the same app or context
# In a real project, this would point to your main item listing view.
from django.views.generic import TemplateView
class ItemListView(TemplateView):
    template_name = "inventory_reports/item_list_placeholder.html" # Create a simple placeholder template

urlpatterns = [
    # Main Stock Ledger Detail View for a specific item and date range
    # Example URL: /inventory_reports/stock-ledger/item/1/?fd=01-01-2023&td=31-01-2023
    path('stock-ledger/item/<int:item_id>/', StockLedgerDetailView.as_view(), name='stock_ledger_detail'),
    
    # HTMX endpoint for the ledger table partial (loaded into main detail page)
    # This receives 'fd' and 'td' query parameters from the client-side JavaScript/HTMX
    path('stock-ledger/item/<int:item_id>/table/', StockLedgerTablePartialView.as_view(), name='stock_ledger_table'),

    # URL for downloading item images and spec sheets
    # Example URL: /inventory_reports/download/item/1/image/
    path('download/item/<int:item_id>/<str:file_type>/', download_item_file, name='download_item_file'),
    
    # URL for the print-friendly version of the stock ledger
    # Example URL: /inventory_reports/stock-ledger/item/1/print/01-01-2023/31-01-2023/
    path('stock-ledger/item/<int:item_id>/print/<str:from_date_str>/<str:to_date_str>/', stock_ledger_print_view, name='stock_ledger_print'),

    # Placeholder for the "Cancel" button's destination
    # In a real application, this would link to your main inventory item list.
    path('items/', ItemListView.as_view(), name='stock_ledger_list_all_items'), 
]

# IMPORTANT: Remember to include this app's URLs in your Django project's main urls.py
# Example: path('inventory_reports/', include('inventory_reports.urls')),
```

#### 4.6 Tests (`inventory_reports/tests.py`)

Comprehensive unit tests for models and integration tests for views, ensuring functionality and adherence to the "fat model, thin view" principle.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, datetime, time
from decimal import Decimal
import os # For file download tests
from django.conf import settings
from django.core.files.storage import default_storage

from .models import (
    Item, Unit, Employee, BusinessGroup, FinancialYear, ItemMasterClone, 
    StockLedgerEntry, format_date_dmy, format_short_datetime
)

class StockLedgerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required test data for foreign keys and main item
        cls.comp_id = 1
        cls.fin_year_id = 1
        cls.current_fin_year_from = date(2023, 4, 1)
        cls.current_fin_year_to = date(2024, 3, 31)

        FinancialYear.objects.create(
            fin_year_id=cls.fin_year_id,
            comp_id=cls.comp_id,
            fin_year_from=cls.current_fin_year_from,
            fin_year_to=cls.current_fin_year_to
        )
        cls.unit = Unit.objects.create(id=1, symbol='PCS')
        cls.employee1 = Employee.objects.create(emp_id=1, comp_id=cls.comp_id, title='Mr', employee_name='John Doe')
        cls.employee2 = Employee.objects.create(emp_id=2, comp_id=cls.comp_id, title='Ms', employee_name='Jane Smith')
        cls.employee3 = Employee.objects.create(emp_id=3, comp_id=cls.comp_id, title='Dr', employee_name='Alice Brown')
        cls.employee4 = Employee.objects.create(emp_id=4, comp_id=cls.comp_id, title='Mx', employee_name='Chris Green')
        cls.business_group_prod = BusinessGroup.objects.create(id=1, symbol='Production')
        cls.business_group_sales = BusinessGroup.objects.create(id=2, symbol='Sales')
        cls.business_group_rd = BusinessGroup.objects.create(id=3, symbol='R&D')


        cls.item = Item.objects.create(
            id=1,
            item_code='ITEM001',
            description='Test Item Description',
            unit=cls.unit,
            att_name='spec_sheet_sample.pdf',
            file_name='item_image_sample.jpg',
            fin_year=FinancialYear.objects.get(fin_year_id=cls.fin_year_id),
            comp_id=cls.comp_id,
            opening_bal_qty=Decimal('100.000')
        )
        ItemMasterClone.objects.create(
            item=cls.item,
            comp_id=cls.comp_id,
            fin_year=FinancialYear.objects.get(fin_year_id=cls.fin_year_id),
            opening_qty=Decimal('100.000'),
            opening_date=cls.current_fin_year_from
        )
    
    def test_item_creation(self):
        obj = Item.objects.get(id=1)
        self.assertEqual(obj.item_code, 'ITEM001')
        self.assertEqual(obj.description, 'Test Item Description')
        self.assertEqual(obj.unit.symbol, 'PCS')
        self.assertEqual(obj.opening_bal_qty, Decimal('100.000'))
        self.assertEqual(obj.comp_id, self.comp_id)
        
    def test_item_file_urls(self):
        obj = Item.objects.get(id=1)
        self.assertIn('/inventory_reports/download/item/1/image/', obj.get_image_url())
        self.assertIn('/inventory_reports/download/item/1/spec_sheet/', obj.get_spec_sheet_url())

    def test_stock_ledger_entry_dataclass(self):
        # Test the StockLedgerEntry data class and its calculations
        entry = StockLedgerEntry(
            sys_date="01-01-2023", sys_time="12:00:00", processed_by_name="P1", accepted_qty=10,
            department_name="DeptA", wo_no="WO1", issued_qty=5, trans_for="TFor", trans_of="TOf",
            transacted_by_name="T1"
        )
        self.assertEqual(entry.accepted_qty, Decimal('10.000'))
        self.assertEqual(entry.issued_qty, Decimal('5.000'))
        self.assertEqual(entry.seconds, 12 * 3600) # 12 hours * 3600 seconds/hour
        self.assertEqual(entry.sort_datetime, "2023-01-01 12:00:00")

    def test_stock_ledger_manager_get_details_dummy_data(self):
        # Test that the manager correctly retrieves and formats dummy data
        from_date_str = date(2023, 4, 15).strftime("%d-%m-%Y")
        to_date_str = date(2023, 4, 20).strftime("%d-%m-%Y")
        
        ledger_entries = Item.objects.get_stock_ledger_details(
            item_id=self.item.id,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            from_date_str=from_date_str,
            to_date_str=to_date_str
        )
        
        self.assertIsInstance(ledger_entries, list)
        self.assertEqual(len(ledger_entries), 4) # Based on dummy data in model
        
        first_entry = ledger_entries[0]
        self.assertIsInstance(first_entry, StockLedgerEntry)
        self.assertEqual(first_entry.accepted_qty, Decimal('0.000')) # Last entry in dummy data is issued
        self.assertEqual(first_entry.issued_qty, Decimal('20.000'))
        self.assertEqual(first_entry.transacted_by_name, 'Mx. Chris Green') # Based on employee ID 4
        self.assertEqual(first_entry.sort_datetime, '2023-04-18 11:45:00') # Verify sort order

    def test_stock_ledger_manager_opening_closing_qty(self):
        from_date = date(2023, 4, 1) # Start of financial year
        to_date = date(2023, 4, 30)
        
        open_qty, total_received, total_issued, closing_qty = Item.objects._get_opening_closing_qty(
            item_id=self.item.id,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            from_date=from_date,
            to_date=to_date
        )
        
        # Test opening_qty when from_date is start of financial year
        self.assertEqual(open_qty, Decimal('100.000')) # Expected from initial opening balance
        
        # Total received/issued for the period based on dummy data in get_stock_ledger_details (April 1 to April 30)
        # Dummy data received: 100.500 + 75.000 = 175.500
        # Dummy data issued: 50.250 + 20.000 = 70.250
        self.assertEqual(total_received, Decimal('175.500'))
        self.assertEqual(total_issued, Decimal('70.250'))
        
        # Closing qty = Opening + Received - Issued = 100 + 175.500 - 70.250 = 205.250
        self.assertEqual(closing_qty, Decimal('205.250'))

    def test_stock_ledger_manager_opening_closing_qty_mid_year_start(self):
        # Test with a from_date NOT at the start of the financial year
        # This tests the historical aggregation part of _get_opening_closing_qty
        
        # Set up some dummy historical transactions that would be picked up by the 'historical_entries' logic
        # For simplicity, we'll manually add entries that fall *before* the new `from_date`
        # but after the financial year start.
        # In a real test, you'd create instances of the actual transaction models.

        # Modify get_stock_ledger_details to simulate historical entries
        original_get_stock_ledger_details = Item.objects.get_stock_ledger_details
        def mock_get_stock_ledger_details(self, item_id, comp_id, fin_year_id, fd_str, td_str):
            mock_from_date = datetime.strptime(fd_str, "%d-%m-%Y").date()
            mock_to_date = datetime.strptime(td_str, "%d-%m-%Y").date()
            
            mock_entries = []
            if item_id == self.model.objects.get(id=1).id:
                # Entries for historical period (e.g., April 1 to April 14, 2023)
                if mock_from_date <= date(2023, 4, 10) <= mock_to_date:
                    mock_entries.append({
                        'sys_date': date(2023, 4, 10), 'sys_time': '09:00:00', 'processed_by_emp_id': 1,
                        'accepted_qty': 20.000, 'dept_id': 1, 'wo_no': 'WO-H1', 'issued_qty': 0,
                        'trans_for': 'HIST_MRN', 'trans_of': 'HIST_MRQN', 'transacted_by_emp_id': 2,
                    })
                if mock_from_date <= date(2023, 4, 12) <= mock_to_date:
                    mock_entries.append({
                        'sys_date': date(2023, 4, 12), 'sys_time': '16:00:00', 'processed_by_emp_id': 3,
                        'accepted_qty': 0, 'dept_id': 2, 'wo_no': 'WO-H2', 'issued_qty': 10.000,
                        'trans_for': 'HIST_MIN', 'trans_of': 'HIST_MRS', 'transacted_by_emp_id': 3,
                    })
                
                # Entries for current period (e.g., April 15 to April 20, 2023) - as per original dummy data
                if mock_from_date <= date(2023, 4, 15) <= mock_to_date:
                    mock_entries.append({
                        'sys_date': date(2023, 4, 15), 'sys_time': '10:30:00', 'processed_by_emp_id': 1, 'accepted_qty': 100.500, 'dept_id': 1, 'wo_no': 'WO-001', 'issued_qty': 0, 'trans_for': 'MRN No[123]', 'trans_of': 'MRQN No[456]', 'transacted_by_emp_id': 2,
                    })
                if mock_from_date <= date(2023, 4, 16) <= mock_to_date:
                    mock_entries.append({
                        'sys_date': date(2023, 4, 16), 'sys_time': '14:00:00', 'processed_by_emp_id': 3, 'accepted_qty': 0, 'dept_id': 2, 'wo_no': 'WO-002', 'issued_qty': 50.250, 'trans_for': 'MRS No[789]', 'trans_of': 'MIN No[012]', 'transacted_by_emp_id': 3,
                    })
                if mock_from_date <= date(2023, 4, 17) <= mock_to_date:
                    mock_entries.append({
                        'sys_date': date(2023, 4, 17), 'sys_time': '09:15:00', 'processed_by_emp_id': 1, 'accepted_qty': 75.000, 'dept_id': 1, 'wo_no': 'WO-003', 'issued_qty': 0, 'trans_for': 'PO No[345]', 'trans_of': 'GQN No[678]', 'transacted_by_emp_id': 2,
                    })
                if mock_from_date <= date(2023, 4, 18) <= mock_to_date:
                    mock_entries.append({
                        'sys_date': date(2023, 4, 18), 'sys_time': '11:45:00', 'processed_by_emp_id': 4, 'accepted_qty': 0, 'dept_id': 3, 'wo_no': 'WO-004', 'issued_qty': 20.000, 'trans_for': 'WIS No[901]', 'trans_of': 'WIS No[901]', 'transacted_by_emp_id': 4,
                    })

            final_entries = []
            for entry_data in mock_entries:
                processed_by_name_obj = Employee.objects.filter(emp_id=entry_data['processed_by_emp_id'], comp_id=comp_id).first()
                processed_by_name = str(processed_by_name_obj) if processed_by_name_obj else 'NA'
                transacted_by_name_obj = Employee.objects.filter(emp_id=entry_data['transacted_by_emp_id'], comp_id=comp_id).first()
                transacted_by_name = str(transacted_by_name_obj) if transacted_by_name_obj else 'NA'
                department_name_obj = BusinessGroup.objects.filter(id=entry_data['dept_id']).first()
                department_name = str(department_name_obj) if department_name_obj else 'NA'

                final_entries.append(StockLedgerEntry(
                    sys_date=format_date_dmy(entry_data['sys_date']),
                    sys_time=entry_data['sys_time'],
                    processed_by_name=processed_by_name,
                    accepted_qty=entry_data['accepted_qty'],
                    department_name=department_name,
                    wo_no=entry_data['wo_no'],
                    issued_qty=entry_data['issued_qty'],
                    trans_for=entry_data['trans_for'],
                    trans_of=entry_data['trans_of'],
                    transacted_by_name=transacted_by_name,
                ))
            final_entries.sort(key=lambda x: x.sort_datetime, reverse=True)
            return final_entries

        # Temporarily patch the manager's method
        Item.objects.get_stock_ledger_details = mock_get_stock_ledger_details.__get__(Item.objects, Item.objects.__class__)

        from_date = date(2023, 4, 15) # New start date, mid-financial year
        to_date = date(2023, 4, 20)
        
        open_qty, total_received, total_issued, closing_qty = Item.objects._get_opening_closing_qty(
            item_id=self.item.id,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            from_date=from_date,
            to_date=to_date
        )

        # Expected historical (April 1 to April 14):
        # Received: 20.000
        # Issued: 10.000
        # Opening for current period (April 15): 100 (initial) + 20 (hist_rec) - 10 (hist_iss) = 110.000
        self.assertEqual(open_qty, Decimal('110.000')) 
        
        # Expected current period (April 15 to April 20) from mock_get_stock_ledger_details:
        # Received: 100.500 + 75.000 = 175.500
        # Issued: 50.250 + 20.000 = 70.250
        self.assertEqual(total_received, Decimal('175.500'))
        self.assertEqual(total_issued, Decimal('70.250'))
        
        # Closing qty = Opening (current period) + Received (current period) - Issued (current period)
        # = 110.000 + 175.500 - 70.250 = 215.250
        self.assertEqual(closing_qty, Decimal('215.250'))

        # Restore original method
        Item.objects.get_stock_ledger_details = original_get_stock_ledger_details

class StockLedgerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 1
        FinancialYear.objects.create(fin_year_id=cls.fin_year_id, comp_id=cls.comp_id, fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31))
        cls.unit = Unit.objects.create(id=1, symbol='PCS')
        cls.item = Item.objects.create(
            id=1,
            item_code='TESTITEM',
            description='Test Description',
            unit=cls.unit,
            att_name='spec_sheet.pdf', # Used for download test
            file_name='item_image.jpg', # Used for download test
            fin_year=FinancialYear.objects.get(fin_year_id=cls.fin_year_id),
            comp_id=cls.comp_id,
            opening_bal_qty=Decimal('100.000')
        )
    
    def setUp(self):
        self.client = Client()
        # Ensure session variables are set if your views rely on them
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()
    
    def test_stock_ledger_detail_view_get(self):
        today = date.today()
        from_date_str = format_date_dmy(today.replace(day=1))
        to_date_str = format_date_dmy(today)
        
        url = reverse('stock_ledger_detail', kwargs={'item_id': self.item.id})
        response = self.client.get(url, {'fd': from_date_str, 'td': to_date_str})
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/stock_ledger/detail.html')
        self.assertIn('item', response.context)
        self.assertIn('ledger_entries', response.context)
        self.assertIn('opening_qty', response.context)
        self.assertIn('closing_qty', response.context)
        self.assertContains(response, self.item.item_code)
        self.assertContains(response, self.item.description)
        self.assertContains(response, self.item.unit.symbol)
        
    def test_stock_ledger_table_partial_view_htmx(self):
        today = date.today()
        from_date_str = format_date_dmy(today.replace(day=1))
        to_date_str = format_date_dmy(today)
        
        url = reverse('stock_ledger_table', kwargs={'item_id': self.item.id})
        # Simulate HTMX request by adding HX-Request header
        response = self.client.get(url, {'fd': from_date_str, 'td': to_date_str}, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/stock_ledger/_stock_ledger_table.html')
        self.assertIn('ledger_entries', response.context) # Check if context contains ledger entries
        self.assertContains(response, '<table id="stockLedgerTable"') # Verify it's the partial table

    def test_download_item_image(self):
        # Create a dummy file in a test-specific media root
        test_media_root = os.path.join(settings.BASE_DIR, 'test_media')
        os.makedirs(os.path.join(test_media_root, 'item_images'), exist_ok=True)
        dummy_file_path = os.path.join(test_media_root, 'item_images', self.item.file_name)
        with open(dummy_file_path, 'wb') as f:
            f.write(b'dummy image content')
        
        # Temporarily override settings.MEDIA_ROOT
        original_media_root = settings.MEDIA_ROOT
        settings.MEDIA_ROOT = test_media_root
        
        try:
            url = reverse('download_item_file', kwargs={'item_id': self.item.id, 'file_type': 'image'})
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response['Content-Type'], 'image/jpeg')
            self.assertEqual(response.content, b'dummy image content')
            self.assertIn(f'attachment; filename="{self.item.file_name}"', response['Content-Disposition'])
        finally:
            settings.MEDIA_ROOT = original_media_root # Restore original settings
            if os.path.exists(test_media_root):
                import shutil
                shutil.rmtree(test_media_root) # Clean up test media directory

    def test_download_item_spec_sheet_not_found(self):
        # Test case where the file does not exist
        test_media_root = os.path.join(settings.BASE_DIR, 'test_media_nonexistent')
        # Ensure directory doesn't exist or is empty
        if os.path.exists(test_media_root):
            import shutil
            shutil.rmtree(test_media_root)
        os.makedirs(test_media_root, exist_ok=True)

        original_media_root = settings.MEDIA_ROOT
        settings.MEDIA_ROOT = test_media_root # Temporarily override settings.MEDIA_ROOT
        
        try:
            url = reverse('download_item_file', kwargs={'item_id': self.item.id, 'file_type': 'spec_sheet'})
            response = self.client.get(url)
            self.assertEqual(response.status_code, 404)
            self.assertContains(response, "File not found or invalid request.")
        finally:
            settings.MEDIA_ROOT = original_media_root # Restore original settings
            if os.path.exists(test_media_root):
                import shutil
                shutil.rmtree(test_media_root)

    def test_stock_ledger_print_view(self):
        today = date.today()
        from_date_str = format_date_dmy(today.replace(day=1))
        to_date_str = format_date_dmy(today)
        
        url = reverse('stock_ledger_print', kwargs={
            'item_id': self.item.id,
            'from_date_str': from_date_str,
            'to_date_str': to_date_str
        })
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/stock_ledger/print.html')
        self.assertIn('item', response.context)
        self.assertContains(response, self.item.item_code)
        self.assertContains(response, 'Stock Ledger Report') # Specific text for print template

```

### Step 5: HTMX and Alpine.js Integration

*   **Date Filtering:** The `from_date` and `to_date` inputs use Alpine.js `x-model` to bind their values. An "Apply Filters" button uses HTMX `hx-get` to dynamically update the `stockLedgerTable-container`. The `hx-get` URL is constructed using Alpine.js values via `_=` (hyperscript), ensuring the correct date range is passed.
*   **Dynamic Table Reload:** The `stockLedgerTable-container` `div` uses `hx-trigger="load, reloadStockLedger from:body"` to load the initial table content and to listen for custom events (e.g., if another part of the app needs to refresh the ledger).
*   **DataTables Initialization:** The `_stock_ledger_table.html` partial includes a `<script>` tag that re-initializes DataTables after HTMX swaps the content. This ensures proper functionality after dynamic updates.
*   **File Downloads:** Image and Spec Sheet links are direct `<a>` tags targeting new tabs (`target="_blank"`), handled by dedicated Django views.
*   **Print Button:** A standard `<a>` tag linked to the `stock_ledger_print` URL, also opening in a new tab.

### Final Notes

*   **Database Connection:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database (e.g., using `django-mssql-backend` or similar). Remember `managed = False` in models.
*   **Media Files:** Configure `MEDIA_ROOT` and `MEDIA_URL` in `settings.py` for serving downloadable files (images, spec sheets). You'll need to create corresponding directories (e.g., `item_images`, `spec_sheets`) within your `MEDIA_ROOT` and place the actual files there.
*   **Session Management:** The original ASP.NET code relies heavily on `Session["compid"]` and `Session["finyear"]`. Ensure these are properly handled in Django (e.g., via Django's session middleware or by passing them as URL parameters if appropriate for your application's security model). The example assumes they are available in `request.session`.
*   **`ItemListView` Placeholder:** The "Cancel" button links to `stock_ledger_list_all_items`. This is a placeholder URL and view; you would integrate it with your actual item listing page.
*   **Comprehensive Model Mapping:** The current `StockLedgerManager.get_stock_ledger_details` and `_get_opening_closing_qty` contain dummy data and simplified logic. A real AI-assisted migration would involve meticulous translation of all the complex SQL queries and C# data processing into robust Django ORM queries and aggregations.
*   **Error Handling and Edge Cases:** Production-ready code would require more extensive error handling, validation, and consideration for edge cases beyond what's demonstrated here.
*   **Frontend Libraries:** Ensure DataTables (jQuery based), HTMX, Alpine.js, and Flatpickr CDN links are correctly included in your `core/base.html` template.