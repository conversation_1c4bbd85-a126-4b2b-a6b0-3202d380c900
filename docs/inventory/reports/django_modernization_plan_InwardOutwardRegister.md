## ASP.NET to Django Conversion Script: Inward/Outward Register

This document outlines a comprehensive modernization plan to transition the existing ASP.NET Inward/Outward Register functionality to a modern Django-based solution. Our approach prioritizes automation, leverages contemporary Django patterns, and focuses on delivering tangible business value through improved performance, maintainability, and user experience.

The core of this modernization involves moving from a tightly coupled ASP.NET Web Forms architecture with server-side controls and direct database interaction in code-behind, to a clean Django application. We will implement a 'Fat Model, Thin View' architecture, utilize HTMX and Alpine.js for dynamic frontend interactions, and DataTables for enhanced data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables/views and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code utilizes multiple SQL `SELECT` queries against various `View_..._InOutReg` views. These views serve as the direct data source for the reports. We will map Django models to these views for read-only access.

**Inferred Database Views and Columns:**

*   **View Name:** `View_MRS_InOutReg`
    *   **Columns:** `MRSNo` (string), `SysDate` (date), `Amt` (decimal), `FinYearId` (integer)
*   **View Name:** `View_MIN_InOutReg_Details`
    *   **Columns:** `MINNo` (string), `SysDate` (date), `Amt` (decimal), `FinYearId` (integer)
*   **View Name:** `View_WIS_InOutReg`
    *   **Columns:** `WISNo` (string), `SysDate` (date), `Amt` (decimal), `FinYearId` (integer)
*   **View Name:** `View_GIN_InOutReg_Details`
    *   **Columns:** `GINNo` (string), `GINDate` (date), `Amt` (decimal), `FinYearId` (integer)
*   **View Name:** `View_GRR_InOutReg_Details`
    *   **Columns:** `GRRNo` (string), `GRRDate` (date), `Amt` (decimal), `FinYearId` (integer)
*   **View Name:** `View_GSN_InOutReg_Details`
    *   **Columns:** `GSNNo` (string), `GSNDate` (date), `Amt` (decimal), `FinYearId` (integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET page `InwardOutwardRegister.aspx` serves as a reporting interface. Its primary function is to:

*   **Read (List & Filter):** Fetch and display aggregated inventory movement data (GIN, GRR, GSN, MRS, MIN, WIS) based on a user-specified date range (`TxtFromDt`, `TxtToDt`) and a financial year (`FinYearId`) from the session.
*   **Aggregate:** Calculate and display the total amount (`Amt`) for each category of transaction within the filtered date range.
*   **No CRUD Operations:** There is no functionality to create, update, or delete individual entries through this specific page. It is a purely read-only reporting module.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET frontend consists of:

*   **Date Range Input:** Two `asp:TextBox` controls (`TxtFromDt`, `TxtToDt`) integrated with `AjaxControlToolkit.CalendarExtender` for date selection. These will be converted to standard HTML date inputs or custom date pickers using Alpine.js.
*   **Search Button:** An `asp:Button` (`Button1`) which triggers the data retrieval and display logic. This will be replaced by an HTMX-driven form submission.
*   **Tabbed Interface:** An `AjaxControlToolkit.TabContainer` with multiple `TabPanel`s. Each tab (`GIN/GRR/GSN`, `MRS/MIN`, `WIS`) will contain one or more data grids. In Django, this will be represented by HTML tabs, likely managed by Alpine.js for client-side state, with content loaded dynamically via HTMX.
*   **Data Grids:** Multiple `asp:GridView` controls (`GridView1` through `GridView6`) are used to display the report data. These will be replaced by DataTables for enhanced client-side features like sorting, searching, and pagination.
*   **Total Labels:** `asp:Label` controls (`lblGINTot`, `lblGRRTot`, etc.) display the sum of amounts for each grid. These totals will be calculated in Django models/views and displayed dynamically.
*   **Styling:** Existing `Css/styles.css`, `Css/StyleSheet.css`, and `Css/yui-datatable.css` will be replaced by Tailwind CSS for a consistent, modern design.

---

### Step 4: Generate Django Code

We will create a new Django app, e.g., `inventory_reports`, to house this functionality.

#### 4.1 Models (`inventory_reports/models.py`)

**Task:** Create Django models based on the identified database views. These models are designed for read-only access to the aggregated data provided by the views. Each model includes a static method to fetch filtered data and compute totals, adhering to the "fat model" principle.

```python
from django.db import models
from django.db.models import Sum
from django.db.models.functions import Coalesce
from datetime import date

# Base model for common fields and report generation logic
class BaseRegisterEntry(models.Model):
    # Common fields inferred from all views and used in queries
    Amt = models.DecimalField(db_column='Amt', max_digits=18, decimal_places=2, default=0.00)
    FinYearid = models.IntegerField(db_column='FinYearid', default=0)

    class Meta:
        abstract = True  # This model will not create a database table

    @classmethod
    def get_report_data(cls, from_date: date, to_date: date, fin_year_id: int):
        """
        Fetches filtered records and calculates the total amount for the report.
        This method embodies the 'fat model' principle by centralizing business logic.
        """
        date_field_name = 'SysDate' # Default, overridden by specific models if needed
        # Determine the date field name dynamically if different from 'SysDate'
        if 'GIN' in cls._meta.db_table:
            date_field_name = 'GINDate'
        elif 'GRR' in cls._meta.db_table:
            date_field_name = 'GRRDate'
        elif 'GSN' in cls._meta.db_table:
            date_field_name = 'GSNDate'

        filter_kwargs = {
            f'{date_field_name}__range': (from_date, to_date),
            'FinYearid': fin_year_id
        }
        
        # Order by the transaction number field (e.g., MRSNo, MINNo, etc.) in descending order
        # We need to dynamically get this field name
        transaction_no_field = None
        for field in cls._meta.get_fields():
            if field.name.endswith('No') and isinstance(field, models.CharField):
                transaction_no_field = field.name
                break
        
        if transaction_no_field:
            records = cls.objects.filter(**filter_kwargs).order_by(f'-{transaction_no_field}')
        else:
            records = cls.objects.filter(**filter_kwargs) # Fallback if no specific 'No' field found

        total_amount = records.aggregate(total=Coalesce(Sum('Amt'), 0.00))['total']
        return records, total_amount

class MRS_Register(BaseRegisterEntry):
    MRSNo = models.CharField(db_column='MRSNo', max_length=50) # Assuming max_length
    SysDate = models.DateField(db_column='SysDate') # Renamed from RDate for clarity based on C#
    
    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'View_MRS_InOutReg'
        verbose_name = 'Material Return Summary'
        verbose_name_plural = 'Material Return Summaries'
        ordering = ['-MRSNo'] # Default order for listing

    def __str__(self):
        return f'MRS {self.MRSNo} on {self.SysDate}'

class MIN_Register(BaseRegisterEntry):
    MINNo = models.CharField(db_column='MINNo', max_length=50)
    SysDate = models.DateField(db_column='SysDate')
    
    class Meta:
        managed = False
        db_table = 'View_MIN_InOutReg_Details'
        verbose_name = 'Material Issue Note'
        verbose_name_plural = 'Material Issue Notes'
        ordering = ['-MINNo']

    def __str__(self):
        return f'MIN {self.MINNo} on {self.SysDate}'

class WIS_Register(BaseRegisterEntry):
    WISNo = models.CharField(db_column='WISNo', max_length=50)
    SysDate = models.DateField(db_column='SysDate')
    
    class Meta:
        managed = False
        db_table = 'View_WIS_InOutReg'
        verbose_name = 'Work In Progress Issue'
        verbose_name_plural = 'Work In Progress Issues'
        ordering = ['-WISNo']

    def __str__(self):
        return f'WIS {self.WISNo} on {self.SysDate}'

class GIN_Register(BaseRegisterEntry):
    GINNo = models.CharField(db_column='GINNo', max_length=50)
    GINDate = models.DateField(db_column='GINDate') # Specific date field for GIN
    
    class Meta:
        managed = False
        db_table = 'View_GIN_InOutReg_Details'
        verbose_name = 'Goods Inward Note'
        verbose_name_plural = 'Goods Inward Notes'
        ordering = ['-GINNo']

    def __str__(self):
        return f'GIN {self.GINNo} on {self.GINDate}'

class GRR_Register(BaseRegisterEntry):
    GRRNo = models.CharField(db_column='GRRNo', max_length=50)
    GRRDate = models.DateField(db_column='GRRDate') # Specific date field for GRR
    
    class Meta:
        managed = False
        db_table = 'View_GRR_InOutReg_Details'
        verbose_name = 'Goods Received Register'
        verbose_name_plural = 'Goods Received Registers'
        ordering = ['-GRRNo']

    def __str__(self):
        return f'GRR {self.GRRNo} on {self.GRRDate}'

class GSN_Register(BaseRegisterEntry):
    GSNNo = models.CharField(db_column='GSNNo', max_length=50)
    GSNDate = models.DateField(db_column='GSNDate') # Specific date field for GSN
    
    class Meta:
        managed = False
        db_table = 'View_GSN_InOutReg_Details'
        verbose_name = 'Goods Sent Note'
        verbose_name_plural = 'Goods Sent Notes'
        ordering = ['-GSNNo']

    def __str__(self):
        return f'GSN {self.GSNNo} on {self.GSNDate}'

```

#### 4.2 Forms (`inventory_reports/forms.py`)

**Task:** Define a Django form for capturing the date range for filtering.

```python
from django import forms
from datetime import date, timedelta

class DateRangeForm(forms.Form):
    """
    Form for selecting a date range for the Inward/Outward Register report.
    Includes default values and client-side styling for date inputs.
    """
    from_date = forms.DateField(
        label="From Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-model': 'fromDate', # Alpine.js binding
            'hx-get': "{% url 'inventory_reports:inward_outward_report_tables' %}",
            'hx-target': "#report-tables-container",
            'hx-trigger': "change delay:500ms", # Trigger update on change with debounce
            'hx-swap': "innerHTML"
        }),
        initial=date.today() - timedelta(days=30), # Default to last 30 days
        input_formats=['%Y-%m-%d', '%d-%m-%Y'] # Allow both standard and original format
    )
    to_date = forms.DateField(
        label="To Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-model': 'toDate', # Alpine.js binding
            'hx-get': "{% url 'inventory_reports:inward_outward_report_tables' %}",
            'hx-target': "#report-tables-container",
            'hx-trigger': "change delay:500ms",
            'hx-swap': "innerHTML"
        }),
        initial=date.today(),
        input_formats=['%Y-%m-%d', '%d-%m-%Y']
    )

    def clean(self):
        """Custom validation to ensure from_date is not after to_date."""
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            raise forms.ValidationError("From Date cannot be after To Date.")
        return cleaned_data

```

#### 4.3 Views (`inventory_reports/views.py`)

**Task:** Implement the main view for the report page and HTMX-specific views for dynamically loading report tables. These views are kept thin, delegating data retrieval and aggregation to the models.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import render
from django.http import HttpResponseBadRequest
from datetime import date
from .models import MRS_Register, MIN_Register, WIS_Register, GIN_Register, GRR_Register, GSN_Register
from .forms import DateRangeForm

class InwardOutwardRegisterView(TemplateView):
    """
    Main view for the Inward/Outward Register page.
    Renders the initial page with the date range form.
    """
    template_name = 'inventory_reports/inward_outward_register.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with default dates for the initial page load
        context['form'] = DateRangeForm(initial={
            'from_date': date.today().replace(day=1), # Default to start of current month
            'to_date': date.today()
        })
        # Placeholder for data on initial load. HTMX will fetch actual data.
        context['report_data'] = self._get_report_data(
            context['form'].initial['from_date'],
            context['form'].initial['to_date']
        )
        return context

    def _get_report_data(self, from_date: date, to_date: date):
        """Helper to fetch all report data. This is internal to keep view thin."""
        # Assume FinYearid is from session, similar to ASP.NET. For demo, using a dummy value.
        # In a real app, this would be retrieved from request.session or user profile.
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not in session

        # Call the fat model methods to get data and totals
        mrs_records, mrs_total = MRS_Register.get_report_data(from_date, to_date, fin_year_id)
        min_records, min_total = MIN_Register.get_report_data(from_date, to_date, fin_year_id)
        wis_records, wis_total = WIS_Register.get_report_data(from_date, to_date, fin_year_id)
        gin_records, gin_total = GIN_Register.get_report_data(from_date, to_date, fin_year_id)
        grr_records, grr_total = GRR_Register.get_report_data(from_date, to_date, fin_year_id)
        gsn_records, gsn_total = GSN_Register.get_report_data(from_date, to_date, fin_year_id)

        return {
            'mrs': {'records': mrs_records, 'total': mrs_total},
            'min': {'records': min_records, 'total': min_total},
            'wis': {'records': wis_records, 'total': wis_total},
            'gin': {'records': gin_records, 'total': gin_total},
            'grr': {'records': grr_records, 'total': grr_total},
            'gsn': {'records': gsn_records, 'total': gsn_total},
        }

class InwardOutwardReportTablesPartialView(View):
    """
    HTMX endpoint to dynamically load or refresh the report tables
    based on the selected date range.
    """
    def get(self, request, *args, **kwargs):
        form = DateRangeForm(request.GET)
        if form.is_valid():
            from_date = form.cleaned_data['from_date']
            to_date = form.cleaned_data['to_date']
            
            # Fetch report data using the helper method
            report_data = InwardOutwardRegisterView()._get_report_data(from_date, to_date)
            
            # Render only the partial template containing the tables
            return render(request, 'inventory_reports/_report_tables_partial.html', {
                'report_data': report_data
            })
        else:
            # Handle invalid form data, e.g., show errors or return empty state
            return HttpResponseBadRequest("Invalid date range provided.")

```

#### 4.4 Templates

**Task:** Create main template (`inward_outward_register.html`) and partial templates (`_report_tables_partial.html`, `_table_panel.html`) for dynamic updates.

**`inventory_reports/templates/inventory_reports/inward_outward_register.html`**
This is the main page template. It sets up the date range form and the container for the HTMX-loaded report tables.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Inward/Outward Register</h2>
        
        <div x-data="{ fromDate: '{{ form.from_date.value|date:"Y-m-d" }}', toDate: '{{ form.to_date.value|date:"Y-m-d" }}' }" class="space-y-4">
            <div class="flex flex-wrap items-center gap-4">
                <div class="flex-grow">
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.from_date.label }}
                    </label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                    {% endif %}
                </div>
                <div class="flex-grow">
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.to_date.label }}
                    </label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                    {% endif %}
                </div>
                <!-- The search button is implicitly handled by hx-trigger on date inputs -->
                <!-- <button
                    type="submit"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                    hx-post="{% url 'inventory_reports:inward_outward_report_tables' %}"
                    hx-target="#report-tables-container"
                    hx-swap="innerHTML">
                    Search
                </button> -->
            </div>
            {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mt-2">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Container for HTMX loaded report tables -->
    <div id="report-tables-container"
         hx-get="{% url 'inventory_reports:inward_outward_report_tables' %}?from_date={{ form.from_date.value|date:"Y-m-d" }}&to_date={{ form.to_date.value|date:"Y-m-d" }}"
         hx-trigger="load, reloadReports from:body"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Ensure DataTables are re-initialized after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'report-tables-container') {
            // Re-initialize DataTables for all tables within the container
            document.querySelectorAll('.data-table').forEach(table => {
                if ($.fn.DataTable.isDataTable(table)) {
                    $(table).DataTable().destroy();
                }
                $(table).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "autoWidth": false, // Adjusts column width automatically
                    "responsive": true // Makes table responsive for smaller screens
                });
            });
        }
    });

    document.addEventListener('alpine:init', () => {
        Alpine.data('tabs', () => ({
            activeTab: 'gin_grr_gsn', // Default active tab
            
            setActiveTab(tabId) {
                this.activeTab = tabId;
            },
            isActive(tabId) {
                return this.activeTab === tabId;
            }
        }));
    });
</script>
{% endblock %}

```

**`inventory_reports/templates/inventory_reports/_report_tables_partial.html`**
This partial template contains the tabbed structure and conditionally renders the specific report tables. It's fetched by HTMX when the page loads or date range changes.

```html
<div x-data="tabs" class="bg-white shadow-md rounded-lg p-6">
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button @click="setActiveTab('gin_grr_gsn')" :class="{'border-blue-500 text-blue-600': isActive('gin_grr_gsn'), 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': !isActive('gin_grr_gsn')}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                GIN/GRR/GSN
            </button>
            <button @click="setActiveTab('mrs_min')" :class="{'border-blue-500 text-blue-600': isActive('mrs_min'), 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': !isActive('mrs_min')}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                MRS/MIN
            </button>
            <button @click="setActiveTab('wis')" :class="{'border-blue-500 text-blue-600': isActive('wis'), 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': !isActive('wis')}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                WIS
            </button>
        </nav>
    </div>

    <div class="mt-6">
        <div x-show="isActive('gin_grr_gsn')" x-transition:enter.duration.300ms>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {% include 'inventory_reports/_table_panel.html' with title="GIN" records=report_data.gin.records total=report_data.gin.total model_name_plural="gin" pk_field="GINNo" date_field="GINDate" %}
                {% include 'inventory_reports/_table_panel.html' with title="GRR" records=report_data.grr.records total=report_data.grr.total model_name_plural="grr" pk_field="GRRNo" date_field="GRRDate" %}
                {% include 'inventory_reports/_table_panel.html' with title="GSN" records=report_data.gsn.records total=report_data.gsn.total model_name_plural="gsn" pk_field="GSNNo" date_field="GSNDate" %}
            </div>
        </div>

        <div x-show="isActive('mrs_min')" x-transition:enter.duration.300ms>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {% include 'inventory_reports/_table_panel.html' with title="MRS" records=report_data.mrs.records total=report_data.mrs.total model_name_plural="mrs" pk_field="MRSNo" date_field="SysDate" %}
                {% include 'inventory_reports/_table_panel.html' with title="MIN" records=report_data.min.records total=report_data.min.total model_name_plural="min" pk_field="MINNo" date_field="SysDate" %}
            </div>
        </div>

        <div x-show="isActive('wis')" x-transition:enter.duration.300ms>
            <div class="grid grid-cols-1 gap-6">
                {% include 'inventory_reports/_table_panel.html' with title="WIS" records=report_data.wis.records total=report_data.wis.total model_name_plural="wis" pk_field="WISNo" date_field="SysDate" %}
            </div>
        </div>
    </div>
</div>

```

**`inventory_reports/templates/inventory_reports/_table_panel.html`**
This reusable partial template renders a single DataTables grid and its total.

```html
<div class="bg-gray-50 rounded-lg shadow-sm p-4">
    <h3 class="text-md font-semibold text-gray-700 mb-3 border-b pb-2">
        {{ title }}
    </h3>
    <div class="overflow-x-auto h-96"> {# Fixed height with auto scroll #}
        <table id="{{ model_name_plural }}-table" class="min-w-full divide-y divide-gray-200 data-table">
            <thead class="bg-gray-100 sticky top-0 z-10">
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ title }} No</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for record in records %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ record|get_attr:date_field|date:"d-m-Y" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ record|get_attr:pk_field }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ record.Amt|floatformat:2 }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="4" class="py-4 text-center text-sm text-gray-500">No records found for this period.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="text-right mt-3 pt-3 border-t border-gray-200">
        <span class="font-bold text-gray-800">Total: </span>
        <span class="font-bold text-blue-600 text-lg">{{ total|floatformat:2 }}</span>
    </div>
</div>

```
**`inventory_reports/templatetags/custom_filters.py`**
A custom template filter is needed to dynamically access attribute values by string name.

```python
from django import template

register = template.Library()

@register.filter
def get_attr(obj, attr_name):
    """
    Allows dynamic attribute access in Django templates.
    Usage: {{ object|get_attr:"attribute_name_string" }}
    """
    return getattr(obj, attr_name, None)

```
Remember to add `{% load custom_filters %}` at the top of templates where `get_attr` is used.

#### 4.5 URLs (`inventory_reports/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import InwardOutwardRegisterView, InwardOutwardReportTablesPartialView

app_name = 'inventory_reports'

urlpatterns = [
    path('inward-outward-register/', InwardOutwardRegisterView.as_view(), name='inward_outward_register'),
    path('inward-outward-register/tables/', InwardOutwardReportTablesPartialView.as_view(), name='inward_outward_report_tables'),
]

```
And remember to include these URLs in your project's main `urls.py`:
`path('inventory/', include('inventory_reports.urls')),`

#### 4.6 Tests (`inventory_reports/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from decimal import Decimal
from unittest.mock import patch, MagicMock

# Import all models to test their get_report_data method
from .models import MRS_Register, MIN_Register, WIS_Register, GIN_Register, GRR_Register, GSN_Register
from .forms import DateRangeForm

class RegisterModelTest(TestCase):
    """
    Unit tests for the report models and their get_report_data method.
    We mock the .objects.filter().order_by() and .aggregate() calls
    since these models are managed=False and rely on external views.
    """

    def setUp(self):
        # Mocking for common data that get_report_data would return
        self.mock_records = [
            MagicMock(Amt=Decimal('100.00'), SysDate=date(2023, 1, 1), MRSNo='MRS001'),
            MagicMock(Amt=Decimal('150.00'), SysDate=date(2023, 1, 2), MRSNo='MRS002'),
        ]
        self.mock_total = Decimal('250.00')
        self.from_date = date(2023, 1, 1)
        self.to_date = date(2023, 1, 31)
        self.fin_year_id = 1

    @patch('inventory_reports.models.MRS_Register.objects')
    def test_mrs_register_get_report_data(self, mock_objects):
        mock_objects.filter.return_value.order_by.return_value.aggregate.return_value = {'total': self.mock_total}
        mock_objects.filter.return_value.order_by.return_value = self.mock_records

        records, total = MRS_Register.get_report_data(self.from_date, self.to_date, self.fin_year_id)

        mock_objects.filter.assert_called_once_with(
            SysDate__range=(self.from_date, self.to_date),
            FinYearid=self.fin_year_id
        )
        mock_objects.filter.return_value.order_by.assert_called_once_with('-MRSNo')
        self.assertEqual(records, self.mock_records)
        self.assertEqual(total, self.mock_total)

    @patch('inventory_reports.models.GIN_Register.objects')
    def test_gin_register_get_report_data(self, mock_objects):
        # Specific test for GIN_Register which uses GINDate
        mock_objects.filter.return_value.order_by.return_value.aggregate.return_value = {'total': self.mock_total}
        mock_objects.filter.return_value.order_by.return_value = self.mock_records

        records, total = GIN_Register.get_report_data(self.from_date, self.to_date, self.fin_year_id)

        mock_objects.filter.assert_called_once_with(
            GINDate__range=(self.from_date, self.to_date), # Check for GINDate
            FinYearid=self.fin_year_id
        )
        mock_objects.filter.return_value.order_by.assert_called_once_with('-GINNo')
        self.assertEqual(records, self.mock_records)
        self.assertEqual(total, self.mock_total)

    # Add similar tests for MIN_Register, WIS_Register, GRR_Register, GSN_Register
    @patch('inventory_reports.models.MIN_Register.objects')
    def test_min_register_get_report_data(self, mock_objects):
        mock_objects.filter.return_value.order_by.return_value.aggregate.return_value = {'total': self.mock_total}
        mock_objects.filter.return_value.order_by.return_value = self.mock_records
        MIN_Register.get_report_data(self.from_date, self.to_date, self.fin_year_id)
        mock_objects.filter.assert_called_once_with(SysDate__range=(self.from_date, self.to_date), FinYearid=self.fin_year_id)
        mock_objects.filter.return_value.order_by.assert_called_once_with('-MINNo')

    @patch('inventory_reports.models.WIS_Register.objects')
    def test_wis_register_get_report_data(self, mock_objects):
        mock_objects.filter.return_value.order_by.return_value.aggregate.return_value = {'total': self.mock_total}
        mock_objects.filter.return_value.order_by.return_value = self.mock_records
        WIS_Register.get_report_data(self.from_date, self.to_date, self.fin_year_id)
        mock_objects.filter.assert_called_once_with(SysDate__range=(self.from_date, self.to_date), FinYearid=self.fin_year_id)
        mock_objects.filter.return_value.order_by.assert_called_once_with('-WISNo')

    @patch('inventory_reports.models.GRR_Register.objects')
    def test_grr_register_get_report_data(self, mock_objects):
        mock_objects.filter.return_value.order_by.return_value.aggregate.return_value = {'total': self.mock_total}
        mock_objects.filter.return_value.order_by.return_value = self.mock_records
        GRR_Register.get_report_data(self.from_date, self.to_date, self.fin_year_id)
        mock_objects.filter.assert_called_once_with(GRRDate__range=(self.from_date, self.to_date), FinYearid=self.fin_year_id)
        mock_objects.filter.return_value.order_by.assert_called_once_with('-GRRNo')

    @patch('inventory_reports.models.GSN_Register.objects')
    def test_gsn_register_get_report_data(self, mock_objects):
        mock_objects.filter.return_value.order_by.return_value.aggregate.return_value = {'total': self.mock_total}
        mock_objects.filter.return_value.order_by.return_value = self.mock_records
        GSN_Register.get_report_data(self.from_date, self.to_date, self.fin_year_id)
        mock_objects.filter.assert_called_once_with(GSNDate__range=(self.from_date, self.to_date), FinYearid=self.fin_year_id)
        mock_objects.filter.return_value.order_by.assert_called_once_with('-GSNNo')


class DateRangeFormTest(TestCase):
    """Unit tests for the DateRangeForm."""

    def test_valid_form(self):
        form = DateRangeForm({'from_date': '2023-01-01', 'to_date': '2023-01-31'})
        self.assertTrue(form.is_valid())

    def test_invalid_date_order(self):
        form = DateRangeForm({'from_date': '2023-01-31', 'to_date': '2023-01-01'})
        self.assertFalse(form.is_valid())
        self.assertIn("From Date cannot be after To Date.", form.non_field_errors())

    def test_missing_dates(self):
        form = DateRangeForm({})
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)
        self.assertIn('to_date', form.errors)

class InwardOutwardRegisterViewsTest(TestCase):
    """Integration tests for the views."""

    def setUp(self):
        self.client = Client()
        self.main_url = reverse('inventory_reports:inward_outward_register')
        self.tables_partial_url = reverse('inventory_reports:inward_outward_report_tables')

        # Mock all get_report_data methods for view tests to isolate view logic
        self.patcher_mrs = patch('inventory_reports.models.MRS_Register.get_report_data')
        self.patcher_min = patch('inventory_reports.models.MIN_Register.get_report_data')
        self.patcher_wis = patch('inventory_reports.models.WIS_Register.get_report_data')
        self.patcher_gin = patch('inventory_reports.models.GIN_Register.get_report_data')
        self.patcher_grr = patch('inventory_reports.models.GRR_Register.get_report_data')
        self.patcher_gsn = patch('inventory_reports.models.GSN_Register.get_report_data')

        self.mock_mrs = self.patcher_mrs.start()
        self.mock_min = self.patcher_min.start()
        self.mock_wis = self.patcher_wis.start()
        self.mock_gin = self.patcher_gin.start()
        self.mock_grr = self.patcher_grr.start()
        self.mock_gsn = self.patcher_gsn.start()

        # Configure mocks to return dummy data
        dummy_records = [MagicMock(Amt=Decimal('100.00'), SysDate=date(2023, 1, 1), MRSNo='DUMMY001')]
        self.mock_mrs.return_value = (dummy_records, Decimal('100.00'))
        self.mock_min.return_value = (dummy_records, Decimal('100.00'))
        self.mock_wis.return_value = (dummy_records, Decimal('100.00'))
        self.mock_gin.return_value = (dummy_records, Decimal('100.00'))
        self.mock_grr.return_value = (dummy_records, Decimal('100.00'))
        self.mock_gsn.return_value = (dummy_records, Decimal('100.00'))

        self.client.session['finyear'] = 1 # Set dummy session data

    def tearDown(self):
        self.patcher_mrs.stop()
        self.patcher_min.stop()
        self.patcher_wis.stop()
        self.patcher_gin.stop()
        self.patcher_grr.stop()
        self.patcher_gsn.stop()

    def test_main_register_view_get(self):
        response = self.client.get(self.main_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/inward_outward_register.html')
        self.assertIn('form', response.context)
        self.assertIn('report_data', response.context)
        self.assertTrue(isinstance(response.context['form'], DateRangeForm))
        self.mock_mrs.assert_called() # Ensure that initial data load calls model methods

    def test_report_tables_partial_view_get_valid_data(self):
        response = self.client.get(self.tables_partial_url, {
            'from_date': '2023-01-01',
            'to_date': '2023-01-31'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/_report_tables_partial.html')
        self.assertIn('report_data', response.context)
        self.mock_mrs.assert_called_once() # Called for the HTMX request
        # Check some content
        self.assertContains(response, 'GIN/GRR/GSN')
        self.assertContains(response, 'Total:')

    def test_report_tables_partial_view_get_invalid_data(self):
        response = self.client.get(self.tables_partial_url, {
            'from_date': '2023-01-31',
            'to_date': '2023-01-01'
        })
        self.assertEqual(response.status_code, 400) # Bad Request for invalid date range
        self.assertIn("Invalid date range provided.", response.content.decode())

    def test_report_tables_partial_view_htmx_trigger(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.tables_partial_url, {
            'from_date': '2023-01-01',
            'to_date': '2023-01-31'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/_report_tables_partial.html')
        # Since HTMX requests typically only swap content, we check for presence of partial content
        self.assertContains(response, '<div x-data="tabs" class="bg-white shadow-md rounded-lg p-6">')
        self.assertNotContains(response, '<!DOCTYPE html>') # Not a full page render


```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:**
    *   The date input fields (`from_date`, `to_date`) in `DateRangeForm` are configured with `hx-get`, `hx-target`, `hx-trigger`, and `hx-swap` attributes. This means that whenever a date is changed, an HTMX GET request is made to `inward_outward_report_tables/` endpoint.
    *   The `hx-target="#report-tables-container"` ensures that the response (the `_report_tables_partial.html`) replaces the content of the `report-tables-container` div, effectively refreshing all report tables without a full page reload.
    *   `hx-trigger="change delay:500ms"` provides a slight debounce to prevent excessive requests while typing or rapidly changing dates.
    *   The main container itself `report-tables-container` uses `hx-trigger="load, reloadReports from:body"` to load initial data when the page loads and to respond to custom events (e.g., if another part of the application needs to trigger a report refresh).
*   **Alpine.js for UI State Management:**
    *   Alpine.js (`x-data="tabs"`) is used in `_report_tables_partial.html` to manage the active state of the tabbed interface. This allows smooth client-side tab switching without server interaction.
    *   `x-model` directives can be added to form inputs if two-way data binding for client-side display or validation is desired (e.g., `x-model="fromDate"` on date inputs).
*   **DataTables for List Views:**
    *   Each `<table>` in `_table_panel.html` is given a class `data-table` and a unique ID (e.g., `gin-table`).
    *   A JavaScript block in `inward_outward_register.html` includes an HTMX `htmx:afterSwap` event listener. This listener ensures that after the `report-tables-container` is updated by HTMX, all `data-table` elements within it are re-initialized as DataTables, providing client-side searching, sorting, and pagination.
*   **No Full Page Reloads:** All interactions, including date range filtering and tab switching, are designed to work without full page reloads, providing a highly responsive user experience.

---

### Final Notes

*   **Replace Placeholders:** Ensure that `core/base.html` exists and contains necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS. The provided code assumes these are available globally.
*   **DRY Templates:** The use of `_report_tables_partial.html` and `_table_panel.html` demonstrates adherence to the DRY principle by reusing components.
*   **Fat Models, Thin Views:** Business logic (data retrieval and aggregation) is encapsulated within the `get_report_data` methods of the respective models, keeping the Django views concise and focused on handling HTTP requests and rendering templates.
*   **Comprehensive Tests:** The included unit tests for models (mocking external database calls) and integration tests for views (mocking model methods) ensure robust coverage and maintainability.
*   **Session Data (`FinYearId`):** The ASP.NET code retrieves `FinYearId` from `Session["finyear"]`. In Django, this would be retrieved from `request.session.get('finyear')` or a user's associated profile/company if part of the authentication system. For demonstration, a default value is used.
*   **Date Format:** The ASP.NET used `dd-MM-yyyy`. Django `forms.DateField` can handle multiple input formats. The `date` template filter (`|date:"d-m-Y"`) ensures output is in the desired format.
*   **Error Handling:** Basic error handling for invalid date ranges is included in the form and view. More sophisticated error messages or UI feedback could be added as needed.