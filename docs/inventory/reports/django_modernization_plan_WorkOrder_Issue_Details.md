## ASP.NET to Django Conversion Script: Work Order Issue Details Report

This document outlines a strategic plan to modernize your ASP.NET Work Order Issue Details report page into a highly efficient and maintainable Django application. Our approach focuses on leveraging AI-assisted automation to transform your existing functionality into a modern, performant, and user-friendly web experience.

The original ASP.NET page is primarily a report viewer, displaying detailed information about a specific Work Order Issue. In Django, we will replicate this by presenting the report data in an interactive web table using DataTables, with the capability to export to a printable format like PDF.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table` where direct table mapping is applicable.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Business Benefits of Modernization:

Transitioning this reporting functionality to Django offers several key advantages for your business:

1.  **Improved Performance & Scalability:** Django's efficient architecture and Python's power will lead to faster report generation and display, especially as data volumes grow.
2.  **Enhanced User Experience:** By using HTMX and Alpine.js, users will experience a smoother, more responsive interface without full page reloads, making data exploration intuitive.
3.  **Reduced Maintenance Costs:** Django's clear structure and "Don't Repeat Yourself" principles make the codebase easier to understand, debug, and extend, leading to lower long-term maintenance expenses.
4.  **Future-Proofing:** Moving away from legacy Crystal Reports to a modern, open-source stack like Django and Python ensures your reporting capabilities remain relevant and adaptable to future business needs.
5.  **Simplified Development:** The "Fat Model, Thin View" approach centralizes business logic, making it easier for developers to build new features and maintain existing ones.
6.  **Actionable Insights:** Presenting data in DataTables empowers users with immediate sorting, searching, and pagination, transforming static reports into dynamic analytical tools.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables, stored procedures, and their columns used to generate the report data in the ASP.NET code.

**Instructions:**

The ASP.NET code relies on several database components:
*   **Stored Procedure:** `WIS_WONo_Wise` is the primary data source, accepting `@CompId` and `@WONo`.
*   **Table for Rates:** `tblMM_Rate_Register` is queried to fetch item rates.
*   **Inferred Tables (based on `SqlDataReader` fields and helper functions):**
    *   `tblWorkOrder`: Contains information about the Work Order (`WONo`, and likely a `Title` as used by `fun.getProjectTitle`).
    *   `tblInventoryItem`: Contains item details (`ItemId`, `ItemCode`, `ManfDesc`, `UOMBasic`).
    *   `Company` (conceptual table): Stores company details (`CompId`, `Name`, `Address`) as used by `fun.getCompany` and `fun.CompAdd`.

The report output schema (the `DataTable` built in C#) consists of the following conceptual fields:
*   `ItemCode` (string)
*   `ManfDesc` (string)
*   `UOM` (string, derived from `UOMBasic`)
*   `Qty` (double, calculated by `fun.AllComponentBOMQty`)
*   `WONo` (string)
*   `CompId` (integer)
*   `IssueQty` (double, calculated by `fun.CalWISQty`)
*   `Rate` (double, calculated from `tblMM_Rate_Register`)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic from the ASP.NET code.

**Instructions:**

The ASP.NET page `WorkOrder_Issue_Details.aspx` performs a complex "Read" operation to generate a report.
*   **Read Operation:**
    *   Retrieves `Company ID`, `Financial Year ID`, and `Work Order Number` from session and query string.
    *   Executes a stored procedure (`WIS_WONo_Wise`) to get base item details for a specific Work Order.
    *   For each item, it performs further calculations:
        *   `AllComponentBOMQty`: Calculates Bill of Material quantity for components.
        *   `CalWISQty`: Calculates Work Order Issue Quantity.
        *   Rate calculation: Queries `tblMM_Rate_Register` to get item rates, applying discounts.
    *   Aggregates this data into a structured format (the `DataTable`).
    *   Populates a Crystal Report viewer with this aggregated data and additional parameters like Company Name, Address, and Project Title.
*   **Navigation:** The "Cancel" button (`Button1_Click`) simply redirects the user to another page (`WorkOrder_Issue.aspx`).
*   **No Direct CRUD:** This specific page does not perform Create, Update, or Delete operations on the Work Order Issue details themselves. It is a read-only report display. Any CRUD operations would apply to the underlying Work Order or Inventory Item entities on separate pages.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to inform Django template design.

**Instructions:**

*   **`CR:CrystalReportViewer`:** This is the central component, responsible for rendering the report. In Django, this will be replaced by a dynamic HTML table rendered using server-side data and enhanced with client-side DataTables.
*   **`asp:Button ID="Button2" Text="Cancel"`:** This button triggers a page redirect. In Django, this will be a simple `<a>` tag or an HTMX-powered button to navigate back to the Work Order list page.
*   The page uses a master page (`MasterPage.master`) for layout, indicating that Django templates will extend a `base.html` for consistent branding and navigation.
*   The `loadingNotifier.js` script suggests a need for client-side feedback during data loading, which HTMX handles elegantly with its built-in indicators.

### Step 4: Generate Django Code

We will create a Django application named `inventory_reports` to house this functionality.

#### 4.1 Models

**Task:** Create Django models to represent the underlying database tables and a conceptual model for the report's output structure.

**Instructions:**

For the underlying database tables (`tblWorkOrder`, `tblInventoryItem`, `tblMM_Rate_Register`, and `Company`), we will define models with `managed = False` to ensure Django uses your existing database schema without attempting to create/modify tables. These models will host the business logic from `clsFunctions`.

For the report itself, we introduce `WorkOrderIssueReportItem`. This model is *conceptual* and does not map directly to a single `db_table` with `managed=False`. Instead, it represents the *schema of the derived report data* that will be constructed dynamically by a dedicated report service method. This allows us to use Django's `ListView` pattern for displaying the report items.

**File: `inventory_reports/models.py`**

```python
from django.db import models, connection
from django.db.models.manager import BaseManager

class WorkOrder(models.Model):
    # Assuming primary key is 'WONo' based on usage, or an auto-incrementing ID.
    # We'll use an integer primary key here, and assume WONo is another field.
    # Adjust field types and names based on your actual database schema.
    # For simplicity, assume WONo is a CharField.
    # If the WONo is your actual primary key, adjust accordingly (primary_key=True).
    wo_id = models.AutoField(db_column='WOId', primary_key=True) # Assuming an auto-incrementing PK
    wono = models.CharField(db_column='WONo', max_length=50, unique=True)
    title = models.CharField(db_column='Title', max_length=255, blank=True, null=True)
    # Add other fields from tblWorkOrder if necessary

    class Meta:
        managed = False  # Important: Django won't manage this table's schema
        db_table = 'tblWorkOrder'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.wono} - {self.title or 'No Title'}"

    @classmethod
    def get_project_title(cls, wono_value):
        """Replicates fun.getProjectTitle."""
        try:
            return cls.objects.get(wono=wono_value).title
        except cls.DoesNotExist:
            return "Unknown Project"

    @classmethod
    def get_work_order_issue_report_data(cls, company_id, work_order_no, financial_year_id):
        """
        Replicates the data aggregation logic from the ASP.NET code-behind's Page_Init.
        This method will call other 'clsFunctions' equivalents.
        """
        report_data = []
        with connection.cursor() as cursor:
            # Replicate WIS_WONo_Wise stored procedure call
            # This is a simplified example. You might need to adjust based on the exact SP logic.
            cursor.execute("EXEC WIS_WONo_Wise @CompId=%s, @WONo=%s", [company_id, work_order_no])
            
            # Fetch all results from the stored procedure
            # Assuming WIS_WONo_Wise returns ItemId, ItemCode, ManfDesc, UOMBasic
            columns = [col[0] for col in cursor.description]
            sp_results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        for rdr_row in sp_results:
            item_id = str(rdr_row.get("ItemId")) # Ensure ItemId is string as used in C#
            item_code = rdr_row.get("ItemCode", "")
            manf_desc = rdr_row.get("ManfDesc", "")
            uom_basic = rdr_row.get("UOMBasic", "")

            # Replicate fun.AllComponentBOMQty
            li_qty = InventoryItem.calculate_all_component_bom_qty(
                company_id, work_order_no, item_id, financial_year_id
            )

            # Replicate fun.CalWISQty
            issue_qty = InventoryItem.calculate_wis_qty(
                str(company_id), work_order_no, item_id
            )

            # Replicate rate calculation from tblMM_Rate_Register
            rate_obj = RateRegister.get_max_rate_with_discount(company_id, item_id)
            item_rate = rate_obj.rate if rate_obj else 0.0

            report_data.append({
                "item_code": item_code,
                "manf_desc": manf_desc,
                "uom": uom_basic, # Directly mapping UOMBasic to UOM as seen in C#
                "qty": li_qty,
                "wono": work_order_no,
                "comp_id": company_id,
                "issue_qty": issue_qty,
                "rate": item_rate,
            })
        return report_data


class InventoryItem(models.Model):
    # Assuming ItemId is the primary key and ItemCode, ManfDesc, UOMBasic are fields
    item_id = models.IntegerField(db_column='ItemId', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=20)
    # Add other fields from tblInventoryItem

    class Meta:
        managed = False
        db_table = 'tblInventoryItem'
        verbose_name = 'Inventory Item'
        verbose_name_plural = 'Inventory Items'

    def __str__(self):
        return self.item_code

    @staticmethod
    def calculate_all_component_bom_qty(comp_id, wono, item_id, fin_year_id):
        """
        Replicates fun.AllComponentBOMQty.
        This is a placeholder; actual implementation depends on the original fun logic.
        Likely involves complex queries on BOM-related tables.
        """
        # Placeholder implementation - replace with actual logic
        # Example: return some value based on complex BOM structure
        print(f"DEBUG: Calling AllComponentBOMQty for CompId:{comp_id}, WONo:{wono}, ItemId:{item_id}, FinYearId:{fin_year_id}")
        return 10.0 # Dummy value

    @staticmethod
    def calculate_wis_qty(comp_id_str, wono, item_id):
        """
        Replicates fun.CalWISQty.
        This is a placeholder; actual implementation depends on the original fun logic.
        Likely involves queries on work order issue tables.
        """
        # Placeholder implementation - replace with actual logic
        print(f"DEBUG: Calling CalWISQty for CompId:{comp_id_str}, WONo:{wono}, ItemId:{item_id}")
        return 5.0 # Dummy value


class RateRegister(models.Model):
    # Assuming composite primary key or a unique ID.
    # Based on the SQL, CompId, ItemId, Rate, Discount are columns.
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming an auto-incrementing PK
    comp_id = models.IntegerField(db_column='CompId')
    item = models.ForeignKey(InventoryItem, db_column='ItemId', on_delete=models.DO_NOTHING, related_name='rates')
    rate = models.FloatField(db_column='Rate')
    discount = models.FloatField(db_column='Discount')
    # Add other fields from tblMM_Rate_Register

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'
        # Consider unique_together if (CompId, ItemId) is unique, or order_by for max rate.

    def __str__(self):
        return f"Comp: {self.comp_id}, Item: {self.item.item_code}, Rate: {self.rate}"

    @classmethod
    def get_max_rate_with_discount(cls, company_id, item_id):
        """
        Replicates the SQL: max(Rate-(Rate*(Discount/100))) As rate
        from tblMM_Rate_Register where CompId='X' And ItemId='Y'
        """
        # This assumes the 'rate' in the C# code is the result of the calculation.
        # It's better to fetch the latest relevant rate.
        # Assuming a scenario where higher discount might mean a lower final rate.
        # Or, the max rate after discount.
        # The C# code uses 'max(Rate-(Rate*(Discount/100)))'. Let's find the max of that.
        try:
            # We need to calculate the effective rate in Python and find the max
            # If the table has an effective date, we'd order by date and pick the latest.
            # Without it, 'max(rate)' is ambiguous if multiple rates exist.
            # Assuming the C# query implies the most recent effective rate for the item/company.
            # For simplicity, we fetch all applicable rates and find the max calculated rate.
            
            # This is a simplification; in a real scenario, you'd likely sort by a date field
            # or version to get the 'latest' or 'most relevant' rate.
            rates = cls.objects.filter(
                comp_id=company_id,
                item__item_id=item_id
            ).values('rate', 'discount')

            if not rates:
                return None

            max_effective_rate = 0.0
            best_rate_obj = None
            
            for rate_data in rates:
                effective_rate = rate_data['rate'] - (rate_data['rate'] * (rate_data['discount'] / 100.0))
                if effective_rate > max_effective_rate:
                    max_effective_rate = effective_rate
                    # Create a dummy object to hold the calculated rate, for consistency
                    # In a real app, you might just return the float or an instance.
                    best_rate_obj = type('RateResult', (object,), {'rate': effective_rate})()
            return best_rate_obj
            
        except Exception as e:
            print(f"Error fetching rate from RateRegister: {e}")
            return None


class Company(models.Model):
    # Assuming primary key 'CompId' and fields for Name and Address
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)
    address = models.TextField(db_column='Address')
    # Add other fields from your Company table

    class Meta:
        managed = False
        db_table = 'Company' # Or whatever your company table is named
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

    @classmethod
    def get_company_name(cls, comp_id):
        """Replicates fun.getCompany."""
        try:
            return cls.objects.get(comp_id=comp_id).name
        except cls.DoesNotExist:
            return "Unknown Company"

    @classmethod
    def get_company_address(cls, comp_id):
        """Replicates fun.CompAdd."""
        try:
            return cls.objects.get(comp_id=comp_id).address
        except cls.DoesNotExist:
            return "N/A Address"

# --- Conceptual Model for Report Output ---
# This model represents the structure of a single row in the final report.
# It is NOT linked to a physical database table (no managed=False here),
# as its data is dynamically generated.
class WorkOrderIssueReportItem(models.Model):
    item_code = models.CharField(max_length=50)
    manf_desc = models.CharField(max_length=255)
    uom = models.CharField(max_length=20)
    qty = models.FloatField()
    wono = models.CharField(max_length=50)
    comp_id = models.IntegerField()
    issue_qty = models.FloatField()
    rate = models.FloatField()

    class Meta:
        # This model is not managed by Django's migrations, nor mapped to a single table.
        # It's a conceptual representation of the report's output schema.
        abstract = True # Mark as abstract so Django doesn't create a table
        verbose_name = 'Work Order Issue Report Item'
        verbose_name_plural = 'Work Order Issue Report Items'

    def __str__(self):
        return f"{self.item_code} for WO {self.wono}"

    # Business logic methods related to a single report item (if any) go here.
    # E.g., calculate_line_total if not done during data generation.
    def get_line_total(self):
        return self.issue_qty * self.rate

```

#### 4.2 Forms

**Task:** Define Django forms if user input is required for CRUD operations.

**Instructions:**

For this specific ASP.NET page, there are no user input forms for Work Order Issue Report *items* (e.g., to create or edit a report line). The page is purely for viewing. Therefore, we do not need to generate a `ModelForm` for `WorkOrderIssueReportItem`. If there were underlying CRUD operations for `WorkOrder` or `InventoryItem` entities, their respective forms would be defined here.

**File: `inventory_reports/forms.py`**
(This file can be empty for this specific report page, or contain forms for related entities if they were part of the migration scope).

```python
# No forms required for this read-only report view.
# Forms would be defined here for CRUD operations on related entities like WorkOrder or InventoryItem.
```

#### 4.3 Views

**Task:** Implement the report display using Django Class-Based Views.

**Instructions:**

We will use a `ListView` to display the `WorkOrderIssueReportItem` instances, even though they are derived dynamically. The `get_queryset` method will be overridden to fetch and construct this report data. We will also add a partial view for HTMX to refresh the table.

**File: `inventory_reports/views.py`**

```python
from django.views.generic import ListView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from .models import WorkOrder, Company, WorkOrderIssueReportItem # Import WorkOrderIssueReportItem for type hinting/structure representation

class WorkOrderIssueReportItemListView(ListView):
    """
    Displays the detailed items for a specific Work Order Issue Report.
    This view generates report data dynamically, not from a direct database table.
    """
    model = WorkOrderIssueReportItem # Placeholder for the conceptual model
    template_name = 'inventory_reports/workorderissuereportitem/report.html'
    context_object_name = 'report_items' # Renamed from generic 'object_list'

    def get_queryset(self):
        """
        Fetches and constructs the report data for the given Work Order Number.
        This method replaces the data preparation logic in the ASP.NET Page_Init.
        """
        work_order_no = self.kwargs['wono']
        # Retrieve CompId and FinYearId from session, similar to ASP.NET.
        # In a real Django app, you'd likely pass these as URL parameters or use a user profile.
        # For demonstration, we'll use dummy values or assume they are available.
        # DUMMY VALUES - REPLACE WITH ACTUAL SESSION/USER DATA RETRIEVAL
        company_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        financial_year_id = self.request.session.get('finyear', 1) # Default to 1 if not in session
        
        # Use the static method on WorkOrder model to get the aggregated report data
        raw_report_data = WorkOrder.get_work_order_issue_report_data(
            company_id, work_order_no, financial_year_id
        )
        
        # Convert raw dictionaries to WorkOrderIssueReportItem instances
        # (This is a conceptual conversion for ListView compatibility)
        report_items = []
        for data in raw_report_data:
            # Create a dummy instance to match the model structure for template rendering
            item = WorkOrderIssueReportItem()
            item.item_code = data.get('item_code')
            item.manf_desc = data.get('manf_desc')
            item.uom = data.get('uom')
            item.qty = data.get('qty')
            item.wono = data.get('wono')
            item.comp_id = data.get('comp_id')
            item.issue_qty = data.get('issue_qty')
            item.rate = data.get('rate')
            report_items.append(item)
            
        return report_items

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        work_order_no = self.kwargs['wono']
        company_id = self.request.session.get('compid', 1)

        context['page_title'] = "Work Order Issue Print Details"
        context['work_order_number'] = work_order_no
        context['company_name'] = Company.get_company_name(company_id)
        context['company_address'] = Company.get_company_address(company_id)
        context['project_title'] = WorkOrder.get_project_title(work_order_no)
        
        # Add a placeholder for potential export functionality
        # context['export_pdf_url'] = reverse_lazy('workorder_issue_report_pdf', kwargs={'wono': work_order_no})
        
        return context

class WorkOrderIssueReportItemTablePartialView(WorkOrderIssueReportItemListView):
    """
    Renders only the table portion of the report for HTMX updates.
    Inherits data fetching logic from the main ListView.
    """
    template_name = 'inventory_reports/workorderissuereportitem/_report_table.html'

    def get_context_data(self, **kwargs):
        """
        Ensure context includes what the partial template needs (e.g., report_items).
        """
        context = super().get_context_data(**kwargs)
        return context

# NOTE: No CreateView, UpdateView, DeleteView for WorkOrderIssueReportItem
# as this specific page is a read-only report viewer.
# CRUD operations would apply to the underlying WorkOrder or InventoryItem entities.
```

#### 4.4 Templates

**Task:** Create templates for the report display, using HTMX for dynamic loading and DataTables for interactive lists.

**Instructions:**

All templates will extend `core/base.html` and use Tailwind CSS for styling. The main report template will load the table content dynamically via HTMX.

**File: `inventory_reports/templates/inventory_reports/workorderissuereportitem/report.html`**

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold text-gray-800">{{ page_title }}</h2>
            <button 
                class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'inventory_reports:workorder_issue_list_page' %}'">
                Cancel
            </button>
        </div>

        <div class="mb-4 text-gray-700">
            <p><strong>Company:</strong> {{ company_name }}</p>
            <p><strong>Address:</strong> {{ company_address|linebreaksbr }}</p>
            <p><strong>Work Order No:</strong> {{ work_order_number }}</p>
            <p><strong>Project Title:</strong> {{ project_title }}</p>
        </div>

        <h3 class="text-xl font-semibold mb-4 text-gray-700">Items Issued</h3>
        
        <div id="workOrderIssueReportItemTable-container"
             hx-trigger="load, refreshReportItems from:body"
             hx-get="{% url 'inventory_reports:workorder_issue_report_table' wono=work_order_number %}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading report data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js for general UI state management if needed (e.g. modals)
    // For this report view, Alpine.js might not be strictly necessary for interactions
    // unless you add dynamic filtering or export options.
    document.addEventListener('alpine:init', () => {
        // x-data="{ open: false }" ...
    });
</script>
{% endblock %}
```

**File: `inventory_reports/templates/inventory_reports/workorderissuereportitem/_report_table.html`**

```html
<div class="overflow-x-auto rounded-lg shadow-sm border border-gray-200">
    <table id="workOrderIssueReportItemTable" class="min-w-full divide-y divide-gray-200 bg-white">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.N.</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manufacturer Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty (BOM)</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Qty</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for item in report_items %}
            <tr class="hover:bg-gray-50">
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ item.item_code }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ item.manf_desc }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ item.uom }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ item.qty|floatformat:2 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ item.issue_qty|floatformat:2 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ item.rate|floatformat:2 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ item.get_line_total|floatformat:2 }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-sm text-gray-500">No issue details found for this Work Order.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables
    $(document).ready(function() {
        $('#workOrderIssueReportItemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true,
            "responsive": true
        });
    });
</script>
```
**Note:** The `hx-trigger="refreshReportItems from:body"` is a generic trigger name. In a full application, specific triggers (e.g., `woIssueReportUpdated`) would be used to refresh the table after a relevant update.

#### 4.5 URLs

**Task:** Define URL patterns for the report views.

**Instructions:**

We will create two URL patterns: one for the main report page and another for the HTMX-loaded table partial.

**File: `inventory_reports/urls.py`**

```python
from django.urls import path
from .views import WorkOrderIssueReportItemListView, WorkOrderIssueReportItemTablePartialView

app_name = 'inventory_reports' # Namespace for URLs

urlpatterns = [
    # Main Work Order Issue Report page (displays for a specific WO)
    path('workorder_issue_report/<str:wono>/', WorkOrderIssueReportItemListView.as_view(), name='workorder_issue_report_detail'),
    
    # HTMX endpoint to get only the table data for dynamic updates
    path('workorder_issue_report/<str:wono>/table/', WorkOrderIssueReportItemTablePartialView.as_view(), name='workorder_issue_report_table'),

    # Placeholder for a dummy list page that might navigate to detail
    path('workorder_issue/', TemplateView.as_view(template_name='inventory_reports/workorder_issue_list.html'), name='workorder_issue_list_page'),
    # You would typically have a real WorkOrder list view here for the "Cancel" button to go back to.
]
```
**File: `inventory_reports/templates/inventory_reports/workorder_issue_list.html`**
(This is a placeholder for the page the "Cancel" button navigates to)
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Work Order Issues List (Placeholder)</h2>
    <p class="text-gray-700 mb-4">This page would typically list all Work Orders. For demonstration, click the link below to view a sample report.</p>
    <a href="{% url 'inventory_reports:workorder_issue_report_detail' wono='WO12345' %}" 
       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
       View Sample Work Order Issue Report (WO12345)
    </a>
</div>
{% endblock %}
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the models (especially the data retrieval logic) and views.

**Instructions:**

Include unit tests for the `WorkOrder.get_work_order_issue_report_data` method and other helper methods in the `Company`, `InventoryItem`, and `RateRegister` models. Add integration tests for the `WorkOrderIssueReportItemListView` to ensure it renders correctly and provides the expected data.

**File: `inventory_reports/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from django.db import connection

from .models import WorkOrder, InventoryItem, RateRegister, Company, WorkOrderIssueReportItem

# Mock the database connection for models that are not managed by Django
# This is crucial for models with managed=False if you're not connecting to a real DB
class UnmanagedModelSetupMixin(TestCase):
    def setUp(self):
        super().setUp()
        # Mock database cursor for unmanaged models where queries are done via raw SQL/SP
        self.mock_cursor = MagicMock()
        self.mock_db_connection = patch.object(connection, 'cursor', return_value=self.mock_cursor)
        self.mock_db_connection.start()
        # Mocking fetchall, description for WIS_WONo_Wise results
        self.mock_cursor.return_value.__enter__.return_value.description = [
            ('ItemId', None), ('ItemCode', None), ('ManfDesc', None), ('UOMBasic', None)
        ]
        self.mock_cursor.return_value.__enter__.return_value.fetchall.return_value = [
            (1, 'ITEM001', 'Widget A', 'PCS'),
            (2, 'ITEM002', 'Gizmo B', 'KG'),
        ]

        # Populate some dummy data for mock models where ORM is used for fetching
        # These are usually created in a real database, but for testing, we can simulate them
        # or use a test database if we remove managed=False during tests.
        # For simplicity, if managed=False, we mock the ORM calls if needed for specific tests.
        
        # We need mock data for these classes to work with ORM queries.
        # Since they are managed=False, Django's default test database setup doesn't populate them.
        # We either need to insert data directly into test DB (complex) or mock ORM queries.
        # For this example, we'll mock the ORM manager calls if they would hit the DB.
        
        # --- Mock objects that WorkOrder.get_work_order_issue_report_data relies on ---
        # Mocking InventoryItem.calculate_all_component_bom_qty and InventoryItem.calculate_wis_qty
        self.patch_bom_qty = patch.object(InventoryItem, 'calculate_all_component_bom_qty', return_value=10.0)
        self.patch_wis_qty = patch.object(InventoryItem, 'calculate_wis_qty', return_value=5.0)
        self.patch_bom_qty.start()
        self.patch_wis_qty.start()

        # Mocking RateRegister.get_max_rate_with_discount
        self.mock_rate_register_result = type('RateResult', (object,), {'rate': 100.0})() # Dummy object
        self.patch_rate = patch.object(RateRegister, 'get_max_rate_with_discount', return_value=self.mock_rate_register_result)
        self.patch_rate.start()

        # Mocking Company.get_company_name and Company.get_company_address
        self.patch_company_name = patch.object(Company, 'get_company_name', return_value='Test Company')
        self.patch_company_address = patch.object(Company, 'get_company_address', return_value='123 Test St')
        self.patch_company_name.start()
        self.patch_company_address.start()

        # Mocking WorkOrder.get_project_title
        self.patch_project_title = patch.object(WorkOrder, 'get_project_title', return_value='Test Project')
        self.patch_project_title.start()

    def tearDown(self):
        super().tearDown()
        self.mock_db_connection.stop()
        self.patch_bom_qty.stop()
        self.patch_wis_qty.stop()
        self.patch_rate.stop()
        self.patch_company_name.stop()
        self.patch_company_address.stop()
        self.patch_project_title.stop()


class WorkOrderReportModelLogicTest(UnmanagedModelSetupMixin):

    def test_get_work_order_issue_report_data(self):
        comp_id = 1
        wono = "WO-TEST-001"
        fin_year_id = 2023

        # Configure the mock cursor's fetchall to return specific data
        self.mock_cursor.return_value.__enter__.return_value.fetchall.return_value = [
            (101, 'P1', 'Product Alpha', 'Units'),
            (102, 'P2', 'Product Beta', 'Each'),
        ]

        report_data = WorkOrder.get_work_order_issue_report_data(comp_id, wono, fin_year_id)

        self.assertEqual(len(report_data), 2)
        
        # Verify the call to the stored procedure
        self.mock_cursor.return_value.__enter__.return_value.execute.assert_called_with(
            "EXEC WIS_WONo_Wise @CompId=%s, @WONo=%s", [comp_id, wono]
        )

        # Verify the structure and values of the first item
        item1 = report_data[0]
        self.assertEqual(item1['item_code'], 'P1')
        self.assertEqual(item1['manf_desc'], 'Product Alpha')
        self.assertEqual(item1['uom'], 'Units')
        self.assertEqual(item1['qty'], 10.0) # From mocked calculate_all_component_bom_qty
        self.assertEqual(item1['issue_qty'], 5.0) # From mocked calculate_wis_qty
        self.assertEqual(item1['rate'], 100.0) # From mocked get_max_rate_with_discount
        self.assertEqual(item1['wono'], wono)
        self.assertEqual(item1['comp_id'], comp_id)

        # Verify calls to helper functions
        InventoryItem.calculate_all_component_bom_qty.assert_any_call(comp_id, wono, '101', fin_year_id)
        InventoryItem.calculate_wis_qty.assert_any_call(str(comp_id), wono, '101')
        RateRegister.get_max_rate_with_discount.assert_any_call(comp_id, '101')


class WorkOrderIssueReportItemViewsTest(UnmanagedModelSetupMixin):
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.test_wono = "WO-VIEWS-001"
        self.test_comp_id = 1
        self.test_fin_year_id = 2023
        self.client.session['compid'] = self.test_comp_id
        self.client.session['finyear'] = self.test_fin_year_id

    def test_report_detail_view_get(self):
        url = reverse('inventory_reports:workorder_issue_report_detail', kwargs={'wono': self.test_wono})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/workorderissuereportitem/report.html')
        self.assertIn('report_items', response.context)
        self.assertIn('page_title', response.context)
        self.assertIn('company_name', response.context)
        self.assertIn('work_order_number', response.context)
        
        self.assertEqual(response.context['work_order_number'], self.test_wono)
        self.assertEqual(response.context['company_name'], 'Test Company') # From mock
        self.assertEqual(len(response.context['report_items']), 2) # From mock SP results

    def test_report_detail_view_context_data(self):
        url = reverse('inventory_reports:workorder_issue_report_detail', kwargs={'wono': self.test_wono})
        response = self.client.get(url)
        
        self.assertEqual(response.context['page_title'], "Work Order Issue Print Details")
        self.assertEqual(response.context['work_order_number'], self.test_wono)
        self.assertEqual(response.context['company_name'], 'Test Company')
        self.assertEqual(response.context['company_address'], '123 Test St')
        self.assertEqual(response.context['project_title'], 'Test Project')

    def test_report_table_partial_view_htmx(self):
        url = reverse('inventory_reports:workorder_issue_report_table', kwargs={'wono': self.test_wono})
        # Simulate an HTMX request by adding the HX-Request header
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/workorderissuereportitem/_report_table.html')
        self.assertIn('report_items', response.context)
        self.assertContains(response, 'ITEM001') # Check for content from mock data
        self.assertContains(response, 'Gizmo B')

    def test_report_data_empty(self):
        # Configure mock cursor to return no rows
        self.mock_cursor.return_value.__enter__.return_value.fetchall.return_value = []
        
        url = reverse('inventory_reports:workorder_issue_report_detail', kwargs={'wono': 'NON_EXISTENT_WO'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No issue details found for this Work Order.') # Check for empty message
        self.assertEqual(len(response.context['report_items']), 0)

    def test_cancel_button_redirect(self):
        # This tests the client-side navigation of the cancel button on the template
        # The Python test can only verify the URL, not the client-side JS redirect
        # We ensure the URL for 'workorder_issue_list_page' is resolvable
        resolved_url = reverse('inventory_reports:workorder_issue_list_page')
        self.assertEqual(resolved_url, '/inventory_reports/workorder_issue/')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated templates (`report.html` and `_report_table.html`) already incorporate HTMX and Alpine.js principles:

*   **HTMX for Dynamic Updates:**
    *   The `div` with `id="workOrderIssueReportItemTable-container"` uses `hx-get` to fetch the table content from `{% url 'inventory_reports:workorder_issue_report_table' wono=work_order_number %}`.
    *   `hx-trigger="load, refreshReportItems from:body"` ensures the table loads on page load and can be refreshed by a custom event (e.g., after an underlying data change elsewhere in the system, which would require an `HX-Trigger` header in that other view's response).
    *   `hx-swap="innerHTML"` replaces the entire content of the container with the new table.
*   **Alpine.js for UI State Management:**
    *   A basic `alpine:init` block is included in `base.html` (implied) and `report.html`. While not extensively used for this read-only report, it sets the stage for adding client-side state management (e.g., for toggling filters, managing an "Export" dropdown, or modal dialogues if they were part of the future scope).
*   **DataTables for List Views:**
    *   The `_report_table.html` partial directly initializes DataTables on the `workOrderIssueReportItemTable` upon loading. This provides out-of-the-box sorting, searching, and pagination without custom JavaScript.
*   **No Full Page Reloads:** All data fetches for the table are done via HTMX, preventing full page reloads and providing a snappier user experience.
*   **`HX-Trigger` Responses:** While this specific report view doesn't involve CRUD that *triggers* updates to other parts of the system, the concept of using `HX-Trigger` in `HttpResponse` headers (as shown in the general template for `CreateView`, `UpdateView`, `DeleteView`) is crucial for other parts of the application where data changes require UI refreshes. For instance, if Work Order items could be edited on *another* page, that page's update view would send an `HX-Trigger: 'refreshReportItems'` to ensure this report page updates automatically.

### Final Notes

*   **Placeholders:** Remember to replace placeholder values like `WONo='WO12345'` in URLs with actual dynamic values from your application's logic (e.g., extracted from the URL, session, or user input).
*   **Business Logic:** The `calculate_all_component_bom_qty` and `calculate_wis_qty` methods in `InventoryItem` and the rate calculation in `RateRegister` are critical. Their placeholder implementations must be replaced with the exact logic from your original `clsFunctions` class. This is where AI-assisted code translation can be highly beneficial for complex legacy functions.
*   **Security:** Ensure proper authentication and authorization are implemented in Django (e.g., using `@login_required` or `LoginRequiredMixin` on views) to control access to this report, mirroring your ASP.NET security model. The C# code uses `Session["compid"]` and `Session["finyear"]`, implying session management for user context. Django's session framework can handle this.
*   **Error Handling:** The original ASP.NET code had a generic `try-catch` block. In Django, robust error handling, logging, and user-friendly messages are essential for production applications.
*   **PDF Export:** To fully replace the Crystal Report functionality, you would implement a separate Django view (e.g., `WorkOrderIssueReportPdfView`) that uses a library like ReportLab or WeasyPrint to generate a PDF based on the same `report_items` data, and link an "Export to PDF" button to this view.