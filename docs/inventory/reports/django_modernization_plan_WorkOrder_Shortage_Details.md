## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The provided ASP.NET code is a Crystal Report viewer page, designed to display a "WorkOrder Shortage Print Details" report. This isn't a standard CRUD (Create, Read, Update, Delete) page for a single entity, but rather a complex data aggregation and presentation view. Our modernization plan will focus on recreating this reporting functionality in Django, prioritizing a clean, maintainable, and performable solution that adheres to the fat model/thin view principle.

Since this is a report display, the traditional Django Forms (for user input to create/update records) are not applicable. The core logic involves querying multiple tables, performing calculations, and presenting the results in a tabular format. The calculations, originally within `clsFunctions` and the `Page_Init` method, will be encapsulated within a dedicated "service" layer in Django, accessed by a lean view.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in generating the report.

**Analysis:** The C# code interacts with several tables and calculates a temporary `DataTable`.

*   **`tblDG_BOM_Master`**: This table seems to hold Bill of Materials (BOM) information per Work Order.
    *   Columns identified: `ItemId` (FK to `tblDG_Item_Master`), `WONo` (Work Order Number), `CompId` (Company ID).
    *   Inferred column: `Quantity` (for `fun.AllComponentBOMQty` to sum up).
    *   Assumed Primary Key: `Id` (integer).
*   **`tblDG_Item_Master`**: Contains master data for items.
    *   Columns identified: `Id` (Primary Key), `ManfDesc` (Manufacturer Description), `UOMBasic` (Unit of Measure ID, FK to `Unit_Master`), `ItemCode`.
*   **`Unit_Master`**: Stores Unit of Measure details.
    *   Columns identified: `Id` (Primary Key), `Symbol`.
*   **`tblMM_Rate_Register`**: Stores item rates and discounts.
    *   Columns identified: `CompId`, `ItemId` (FK to `tblDG_Item_Master`), `Rate`, `Discount`.
    *   Assumed Primary Key: `Id` (integer).
*   **Implied `WorkOrderItemIssue` (or similar)**: The function `fun.CalWISQty` suggests a table tracking issued quantities for work orders and items.
    *   Inferred columns: `CompId`, `WONo`, `ItemId`, `IssuedQty`.
    *   Assumed Primary Key: `Id` (integer).
*   **Report Output Structure (similar to `DT`):**
    *   `ItemCode`
    *   `ManfDesc`
    *   `UOM` (Symbol)
    *   `Qty` (Calculated total BOM quantity)
    *   `WONo`
    *   `CompId`
    *   `IssueQty` (Calculated shortage quantity, `liQty - issued_qty`)
    *   `Rate` (Calculated from `tblMM_Rate_Register`)

### Step 2: Identify Backend Functionality

**Task:** Determine the core business logic and data flow.

**Analysis:**
This page's primary function is **reporting**.
1.  **Input Parameters:** `WONo` (Work Order Number) and `Key` (session key, which we'll abstract away in Django by directly passing data). `CompId` (Company ID) and `FinYearId` (Financial Year ID) are from session.
2.  **Data Retrieval & Aggregation:**
    *   Retrieves distinct `ItemId`, `ManfDesc`, `UOMBasic`, `ItemCode` for a given `WONo` and `CompId` from `tblDG_BOM_Master`, `tblDG_Item_Master`, `Unit_Master`.
    *   For each item, it calculates:
        *   `liQty` (Total BOM quantity): Inferred from `fun.AllComponentBOMQty` – sums quantities from `tblDG_BOM_Master`.
        *   `ShortQty` (Shortage Quantity): `liQty - fun.CalWISQty` – `CalWISQty` determines already issued quantity.
        *   `Rate`: Fetches max rate (minus discount) from `tblMM_Rate_Register`.
    *   Filters out rows where `ShortQty` is not greater than 0.
3.  **Report Parameters:** Fetches `Company`, `Address`, `Title` (Project Title for `WONo`) using `fun.getCompany`, `fun.CompAdd`, `fun.getProjectTitle`.
4.  **Presentation:** Displays the processed data, formatted for a report.
5.  **Navigation:** A "Cancel" button redirects to `WorkOrder_Issue.aspx`.

**Key Logic Points to Migrate:**
*   `fun.AllComponentBOMQty` calculation.
*   `fun.CalWISQty` calculation.
*   Rate calculation from `tblMM_Rate_Register`.
*   Fetching Company, Address, and Project Title.
*   Filtering rows based on `ShortQty > 0`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
*   `CrystalReportViewer`: This is the primary display component. In Django, this will be replaced by a standard HTML `<table>` that leverages DataTables for client-side features like search, sort, and pagination.
*   `asp:Panel`: Used for scrolling the report. This will be handled by CSS and DataTables.
*   `asp:Button ID="Button2"`: A simple "Cancel" button that redirects. This will be a standard HTML link or button that triggers a redirect.

The overall layout is a centered table with a header. Tailwind CSS will be used for styling.

### Step 4: Generate Django Code

We will create a Django app named `reports` for this functionality.

#### 4.1 Models (reports/models.py)

We define models for the underlying database tables. These will be `managed = False` as they map to existing database schemas. We also define a simple Python class to represent the computed report row, as it's not directly stored in the database.

```python
from django.db import models

# Assumed models for the existing database tables
# Replace field types and constraints as per your actual database schema

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic') # FK to Unit_Master.Id
    item_code = models.CharField(db_column='ItemCode', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class BOMMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId') # FK to tblDG_Item_Master.Id
    wo_no = models.CharField(db_column='WONo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    quantity = models.FloatField(db_column='Quantity', default=0.0) # Inferred quantity field

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'
        unique_together = (('item', 'wo_no', 'comp_id'),) # Assuming unique BOM entries

    def __str__(self):
        return f"{self.wo_no} - {self.item.item_code}"

class RateRegister(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId') # FK to tblDG_Item_Master.Id
    rate = models.FloatField(db_column='Rate')
    discount = models.FloatField(db_column='Discount')

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'

    def __str__(self):
        return f"Rate for {self.item.item_code} @ {self.rate}"

# This model is inferred for 'fun.CalWISQty'. You might need to adjust its fields
# based on your actual Work Order Issue table.
class WorkOrderItemIssue(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    issued_qty = models.FloatField(db_column='IssuedQty', default=0.0)
    fin_year_id = models.IntegerField(db_column='FinYearId', default=0) # Added for consistency

    class Meta:
        managed = False
        db_table = 'tblWO_Item_Issue' # Placeholder table name
        verbose_name = 'Work Order Item Issue'
        verbose_name_plural = 'Work Order Item Issues'

    def __str__(self):
        return f"Issue for {self.wo_no} - {self.item.item_code}: {self.issued_qty}"

# --- Python class to represent a single row in the final report ---
# This is NOT a Django model, but a simple data structure.
class WorkOrderShortageReportRow:
    def __init__(self, item_code, manf_desc, uom, bom_qty, wo_no, comp_id, issue_qty, rate):
        self.item_code = item_code
        self.manf_desc = manf_desc
        self.uom = uom
        self.bom_qty = bom_qty
        self.wo_no = wo_no
        self.comp_id = comp_id
        self.issue_qty = issue_qty
        self.rate = rate
```

#### 4.2 Forms (reports/forms.py)

Since this is a report display page, there are no forms for user input (Create/Update operations). The `forms.py` file can be empty or omitted.

```python
# reports/forms.py
# This file is intentionally left minimal as no user input forms are required for this report.
from django import forms
```

#### 4.3 Views (reports/views.py)

To maintain "thin views" and abstract complex calculations, we'll introduce a `WorkOrderShortageService` class. The view will primarily orchestrate calling this service and rendering the template.

```python
import math
from django.views.generic import TemplateView
from django.db.models import Sum, Max
from django.shortcuts import redirect
from django.urls import reverse_lazy

# Import models
from .models import ItemMaster, BOMMaster, RateRegister, WorkOrderItemIssue, WorkOrderShortageReportRow

# --- Service Layer for Business Logic ---
# This class encapsulates the complex calculations and data retrieval,
# adhering to the "Fat Model" principle by keeping logic separate from views.
class WorkOrderShortageService:
    def get_all_component_bom_qty(self, comp_id, wo_no, item_id, fin_year_id):
        """
        Simulates fun.AllComponentBOMQty.
        Calculates the total BOM quantity for a given item in a work order.
        """
        # Note: fin_year_id is not directly used in the provided BOM query,
        # but kept for potential future filtering if BOMs are year-specific.
        total_qty = BOMMaster.objects.filter(
            comp_id=comp_id,
            wo_no=wo_no,
            item_id=item_id
        ).aggregate(Sum('quantity'))['quantity__sum']
        return total_qty if total_qty is not None else 0.0

    def calculate_work_order_issued_qty(self, comp_id, wo_no, item_id):
        """
        Simulates fun.CalWISQty.
        Calculates the total issued quantity for a given item in a work order.
        """
        total_issued_qty = WorkOrderItemIssue.objects.filter(
            comp_id=comp_id,
            wo_no=wo_no,
            item_id=item_id
        ).aggregate(Sum('issued_qty'))['issued_qty__sum']
        return total_issued_qty if total_issued_qty is not None else 0.0

    def get_company_info(self, comp_id):
        """Simulates fun.getCompany and fun.CompAdd."""
        # Placeholder: In a real ERP, this would come from a CompanyMaster model
        # or configuration.
        company_details = {
            1: {"name": "Example Co.", "address": "123 Business St, City, Country"},
            # Add more companies
        }
        return company_details.get(comp_id, {"name": "Unknown Company", "address": "N/A"})

    def get_project_title(self, wo_no):
        """Simulates fun.getProjectTitle."""
        # Placeholder: In a real ERP, this would come from a WorkOrderMaster model
        # or similar.
        project_titles = {
            "WO-2023-001": "Production Order for Widgets",
            "WO-2023-002": "Assembly of Gadgets",
        }
        return project_titles.get(wo_no, f"Work Order: {wo_no}")

    def get_work_order_shortage_report_data(self, comp_id, wo_no, fin_year_id):
        """
        Generates the detailed work order shortage report data.
        This method encapsulates the main logic from the ASP.NET Page_Init.
        """
        report_rows = []

        # Step 1: Get distinct items from BOM for the given WO and Company
        bom_items_query = BOMMaster.objects.filter(
            comp_id=comp_id,
            wo_no=wo_no
        ).select_related('item__uom_basic').distinct('item__id') # Distinct by ItemId

        # Step 2: Process each distinct item to calculate shortage details
        for bom_entry in bom_items_query:
            item_id = bom_entry.item.id
            item_code = bom_entry.item.item_code
            manf_desc = bom_entry.item.manf_desc
            uom_symbol = bom_entry.item.uom_basic.symbol if bom_entry.item.uom_basic else "N/A"

            # Calculate total BOM quantity for this item
            li_qty = self.get_all_component_bom_qty(comp_id, wo_no, item_id, fin_year_id)

            # Calculate issued quantity for this item
            issued_qty = self.calculate_work_order_issued_qty(str(comp_id), wo_no, str(item_id))

            # Calculate shortage quantity
            short_qty = round((li_qty - issued_qty), 5) # Math.Round((liQty-fun.CalWISQty(...)),5);

            # Get maximum rate (minus discount) for this item
            rate_data = RateRegister.objects.filter(
                comp_id=comp_id,
                item_id=item_id
            ).annotate(
                calculated_rate=Max(models.F('rate') - (models.F('rate') * models.F('discount') / 100))
            ).values('calculated_rate').first()

            rate = rate_data['calculated_rate'] if rate_data and rate_data['calculated_rate'] is not None else 0.0

            # Only add to report if ShortQty > 0
            if short_qty > 0:
                report_rows.append(
                    WorkOrderShortageReportRow(
                        item_code=item_code,
                        manf_desc=manf_desc,
                        uom=uom_symbol,
                        bom_qty=li_qty,
                        wo_no=wo_no,
                        comp_id=comp_id,
                        issue_qty=short_qty, # This is ShortQty in ASP.NET code
                        rate=rate
                    )
                )
        return report_rows

# --- Django View ---
class WorkOrderShortageReportView(TemplateView):
    template_name = 'reports/workorder_shortage_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve parameters from URL query string
        wo_no = self.request.GET.get('wono')
        
        # Retrieve company and financial year IDs from session
        # For demonstration, using dummy values if session is not set or not available.
        # In a real application, ensure these session variables are correctly managed.
        comp_id = self.request.session.get('compid', 1)  # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 2023) # Default to 2023 if not in session
        
        report_service = WorkOrderShortageService()

        if wo_no:
            context['report_data'] = report_service.get_work_order_shortage_report_data(
                comp_id, wo_no, fin_year_id
            )
            # Add report parameters
            company_info = report_service.get_company_info(comp_id)
            context['company_name'] = company_info['name']
            context['company_address'] = company_info['address']
            context['project_title'] = report_service.get_project_title(wo_no)
            context['wo_no'] = wo_no
            context['comp_id'] = comp_id
            context['fin_year_id'] = fin_year_id
        else:
            context['report_data'] = []
            context['error_message'] = "Work Order Number (wono) is missing from the URL."

        return context

# View for the "Cancel" button, simply redirects to the Work Order Issue list.
# This assumes 'workorder_issue_list' is a valid URL name in your Django project.
def cancel_report_view(request):
    return redirect(reverse_lazy('workorder_issue_list')) # Adjust this URL name as needed
```

#### 4.4 Templates (reports/templates/reports/workorder_shortage_report.html)

This template will display the generated report data in an interactive table using DataTables.

```html
{% extends 'core/base.html' %}

{% block title %}WorkOrder Shortage Print Details - AutoERP{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col items-center justify-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">WorkOrder Shortage Print Details</h2>
        <div class="text-center mt-2 text-gray-600">
            {% if company_name %}<p>{{ company_name }}</p>{% endif %}
            {% if company_address %}<p>{{ company_address }}</p>{% endif %}
            {% if wo_no %}<p>Work Order: {{ wo_no }}</p>{% endif %}
            {% if project_title %}<p>{{ project_title }}</p>{% endif %}
        </div>
    </div>
    
    {% if error_message %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ error_message }}</span>
        </div>
    {% elif report_data %}
        <div class="overflow-x-auto shadow-md rounded-lg">
            <table id="shortageReportTable" class="min-w-full bg-white divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                        <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                        <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                        <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Shortage Qty</th>
                        <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for row in report_data %}
                    <tr>
                        <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.item_code }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.manf_desc }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.uom }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500 text-right">{{ row.bom_qty|floatformat:2 }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500 text-right">{{ row.issue_qty|floatformat:2 }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500 text-right">{{ row.rate|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <p class="text-center text-gray-600">No shortage details found for the provided Work Order. Please check the 'wono' parameter.</p>
    {% endif %}

    <div class="mt-8 flex justify-center">
        <a href="{% url 'workorder_issue_list' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS & CSS (Assuming these are loaded via base.html or CDN in settings) -->
<!-- Example if loaded here (but better in base.html) -->
<!-- <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css"> -->
<!-- <script type="text/javascript" src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script> -->

<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('shortageReportTable')) {
            new DataTable('#shortageReportTable', {
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "ordering": true,
                "searching": true
            });
        }
    });

    // Alpine.js component initialization if needed for more complex UI states
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js logic needed for this simple report display
    });
</script>
{% endblock %}
```

#### 4.5 URLs (reports/urls.py)

```python
from django.urls import path
from .views import WorkOrderShortageReportView, cancel_report_view

urlpatterns = [
    # URL for the work order shortage report, accepting 'wono' as a query parameter
    # Example: /reports/workorder-shortage/?wono=WO-2023-001
    path('workorder-shortage/', WorkOrderShortageReportView.as_view(), name='workorder_shortage_report'),
    
    # URL for the cancel button redirection
    path('workorder-issue-list/', cancel_report_view, name='workorder_issue_list'), # Placeholder for original redirect target
]
```
**Important:** You will need to include these URLs in your project's main `urls.py`:
`path('reports/', include('reports.urls')),`

#### 4.6 Tests (reports/tests.py)

Comprehensive unit tests for the models and the `WorkOrderShortageService` logic are crucial, along with integration tests for the view.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from unittest.mock import patch, MagicMock

# Import models and service
from .models import UnitMaster, ItemMaster, BOMMaster, RateRegister, WorkOrderItemIssue, WorkOrderShortageReportRow
from .views import WorkOrderShortageService

class ReportModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for managed=False models
        # Ensure 'id' is explicitly set as it's not auto-incrementing with managed=False
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')

        cls.item_raw_mat = ItemMaster.objects.create(id=101, item_code='RM001', manf_desc='Raw Material A', uom_basic=cls.unit_kg)
        cls.item_comp_a = ItemMaster.objects.create(id=102, item_code='COMP001', manf_desc='Component A', uom_basic=cls.unit_ea)
        cls.item_comp_b = ItemMaster.objects.create(id=103, item_code='COMP002', manf_desc='Component B', uom_basic=cls.unit_ea)

        cls.comp_id_1 = 1
        cls.fin_year_id_2023 = 2023
        cls.wo_no_test = "WO-TEST-001"
        cls.wo_no_no_shortage = "WO-TEST-002"

        # BOM entries for WO-TEST-001
        BOMMaster.objects.create(id=1, item=cls.item_comp_a, wo_no=cls.wo_no_test, comp_id=cls.comp_id_1, quantity=10.0)
        BOMMaster.objects.create(id=2, item=cls.item_comp_b, wo_no=cls.wo_no_test, comp_id=cls.comp_id_1, quantity=5.0)

        # BOM entries for WO-TEST-002 (no shortage expected)
        BOMMaster.objects.create(id=3, item=cls.item_comp_a, wo_no=cls.wo_no_no_shortage, comp_id=cls.comp_id_1, quantity=10.0)
        BOMMaster.objects.create(id=4, item=cls.item_comp_b, wo_no=cls.wo_no_no_shortage, comp_id=cls.comp_id_1, quantity=5.0)

        # Rate Register entries
        RateRegister.objects.create(id=1, item=cls.item_comp_a, comp_id=cls.comp_id_1, rate=50.0, discount=10.0) # Net 45.0
        RateRegister.objects.create(id=2, item=cls.item_comp_b, comp_id=cls.comp_id_1, rate=100.0, discount=0.0) # Net 100.0

        # Work Order Item Issues
        WorkOrderItemIssue.objects.create(id=1, item=cls.item_comp_a, wo_no=cls.wo_no_test, comp_id=cls.comp_id_1, issued_qty=2.0) # Shortage for COMP001
        WorkOrderItemIssue.objects.create(id=2, item=cls.item_comp_b, wo_no=cls.wo_no_test, comp_id=cls.comp_id_1, issued_qty=5.0) # No shortage for COMP002

        WorkOrderItemIssue.objects.create(id=3, item=cls.item_comp_a, wo_no=cls.wo_no_no_shortage, comp_id=cls.comp_id_1, issued_qty=10.0) # Full issue for COMP001
        WorkOrderItemIssue.objects.create(id=4, item=cls.item_comp_b, wo_no=cls.wo_no_no_shortage, comp_id=cls.comp_id_1, issued_qty=5.0) # Full issue for COMP002


    def test_unit_master_creation(self):
        unit = UnitMaster.objects.get(id=1)
        self.assertEqual(unit.symbol, 'EA')
        self.assertEqual(str(unit), 'EA')

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=101)
        self.assertEqual(item.item_code, 'RM001')
        self.assertEqual(item.uom_basic.symbol, 'KG')
        self.assertEqual(str(item), 'RM001')

    def test_bom_master_creation(self):
        bom = BOMMaster.objects.get(id=1)
        self.assertEqual(bom.item.item_code, 'COMP001')
        self.assertEqual(bom.wo_no, self.wo_no_test)
        self.assertEqual(bom.quantity, 10.0)
        self.assertEqual(str(bom), f"{self.wo_no_test} - COMP001")

    def test_rate_register_creation(self):
        rate_entry = RateRegister.objects.get(id=1)
        self.assertEqual(rate_entry.item.item_code, 'COMP001')
        self.assertEqual(rate_entry.rate, 50.0)
        self.assertEqual(rate_entry.discount, 10.0)
        self.assertEqual(str(rate_entry), "Rate for COMP001 @ 50.0")

    def test_work_order_item_issue_creation(self):
        issue = WorkOrderItemIssue.objects.get(id=1)
        self.assertEqual(issue.item.item_code, 'COMP001')
        self.assertEqual(issue.issued_qty, 2.0)
        self.assertEqual(str(issue), f"Issue for {self.wo_no_test} - COMP001: 2.0")

class WorkOrderShortageServiceTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Re-use setup data from ReportModelsTest for service logic
        ReportModelsTest.setUpTestData()
        cls.service = WorkOrderShortageService()
        cls.comp_id = ReportModelsTest.comp_id_1
        cls.fin_year_id = ReportModelsTest.fin_year_id_2023
        cls.wo_no_test = ReportModelsTest.wo_no_test
        cls.wo_no_no_shortage = ReportModelsTest.wo_no_no_shortage
        cls.item_comp_a_id = ReportModelsTest.item_comp_a.id
        cls.item_comp_b_id = ReportModelsTest.item_comp_b.id

    def test_get_all_component_bom_qty(self):
        # Item COMP001 in WO-TEST-001 has 10.0 qty in BOM
        qty_a = self.service.get_all_component_bom_qty(self.comp_id, self.wo_no_test, self.item_comp_a_id, self.fin_year_id)
        self.assertEqual(qty_a, 10.0)

        # Item COMP002 in WO-TEST-001 has 5.0 qty in BOM
        qty_b = self.service.get_all_component_bom_qty(self.comp_id, self.wo_no_test, self.item_comp_b_id, self.fin_year_id)
        self.assertEqual(qty_b, 5.0)

        # Non-existent item/WO
        qty_non_existent = self.service.get_all_component_bom_qty(self.comp_id, "NON-EXISTENT-WO", 999, self.fin_year_id)
        self.assertEqual(qty_non_existent, 0.0)

    def test_calculate_work_order_issued_qty(self):
        # Item COMP001 in WO-TEST-001 has 2.0 issued
        issued_qty_a = self.service.calculate_work_order_issued_qty(str(self.comp_id), self.wo_no_test, str(self.item_comp_a_id))
        self.assertEqual(issued_qty_a, 2.0)

        # Item COMP002 in WO-TEST-001 has 5.0 issued
        issued_qty_b = self.service.calculate_work_order_issued_qty(str(self.comp_id), self.wo_no_test, str(self.item_comp_b_id))
        self.assertEqual(issued_qty_b, 5.0)
        
        # Non-existent item/WO
        issued_qty_non_existent = self.service.calculate_work_order_issued_qty(str(self.comp_id), "NON-EXISTENT-WO", str(999))
        self.assertEqual(issued_qty_non_existent, 0.0)

    def test_get_company_info(self):
        company_info = self.service.get_company_info(self.comp_id)
        self.assertEqual(company_info['name'], 'Example Co.')
        self.assertEqual(company_info['address'], '123 Business St, City, Country')
        
        unknown_company = self.service.get_company_info(99)
        self.assertEqual(unknown_company['name'], 'Unknown Company')

    def test_get_project_title(self):
        title = self.service.get_project_title(self.wo_no_test)
        self.assertEqual(title, 'Production Order for Widgets')

        unknown_title = self.service.get_project_title("WO-XYZ")
        self.assertEqual(unknown_title, "Work Order: WO-XYZ")

    def test_get_work_order_shortage_report_data(self):
        report_data = self.service.get_work_order_shortage_report_data(self.comp_id, self.wo_no_test, self.fin_year_id)
        self.assertEqual(len(report_data), 1) # Only COMP001 should have shortage

        # Verify data for COMP001
        comp_a_row = next((r for r in report_data if r.item_code == 'COMP001'), None)
        self.assertIsNotNone(comp_a_row)
        self.assertEqual(comp_a_row.manf_desc, 'Component A')
        self.assertEqual(comp_a_row.uom, 'EA')
        self.assertEqual(comp_a_row.bom_qty, 10.0)
        self.assertEqual(comp_a_row.issue_qty, 8.0) # 10 (BOM) - 2 (Issued) = 8
        self.assertEqual(comp_a_row.rate, 45.0) # 50 - (50*0.10)

        # Verify no shortage for WO-TEST-002
        report_data_no_shortage = self.service.get_work_order_shortage_report_data(self.comp_id, self.wo_no_no_shortage, self.fin_year_id)
        self.assertEqual(len(report_data_no_shortage), 0)

        # Test with non-existent WO
        report_data_empty = self.service.get_work_order_shortage_report_data(self.comp_id, "NON-EXISTENT", self.fin_year_id)
        self.assertEqual(len(report_data_empty), 0)


class WorkOrderShortageReportViewTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Ensure base data is set up for view tests
        ReportModelsTest.setUpTestData()

    def setUp(self):
        # Set session data needed by the view
        session = self.client.session
        session['compid'] = ReportModelsTest.comp_id_1
        session['finyear'] = ReportModelsTest.fin_year_id_2023
        session.save()

    def test_report_view_success(self):
        # Test with valid WO number
        response = self.client.get(reverse('workorder_shortage_report'), {'wono': ReportModelsTest.wo_no_test})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/workorder_shortage_report.html')
        self.assertIn('report_data', response.context)
        self.assertEqual(len(response.context['report_data']), 1)
        self.assertContains(response, 'COMP001')
        self.assertContains(response, 'WorkOrder Shortage Print Details')
        self.assertContains(response, 'Example Co.')
        self.assertContains(response, 'Production Order for Widgets')

    def test_report_view_no_wono(self):
        # Test without WO number in query string
        response = self.client.get(reverse('workorder_shortage_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/workorder_shortage_report.html')
        self.assertIn('error_message', response.context)
        self.assertContains(response, 'Work Order Number (wono) is missing from the URL.')
        self.assertEqual(len(response.context['report_data']), 0)

    def test_report_view_no_shortage_data(self):
        # Test with WO number that has no shortage
        response = self.client.get(reverse('workorder_shortage_report'), {'wono': ReportModelsTest.wo_no_no_shortage})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/workorder_shortage_report.html')
        self.assertIn('report_data', response.context)
        self.assertEqual(len(response.context['report_data']), 0)
        self.assertContains(response, 'No shortage details found')

    def test_cancel_report_view_redirect(self):
        # Test the cancel button redirect
        response = self.client.get(reverse('workorder_issue_list'))
        self.assertEqual(response.status_code, 302) # Expecting a redirect
        self.assertRedirects(response, reverse('workorder_issue_list')) # Verify redirect target
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
For this specific report display page, HTMX and Alpine.js are primarily used for client-side interactivity and data table features, rather than dynamic form submissions (which are not present here).

*   **HTMX:** Not directly used for *loading* the initial report data as it's a full page load based on URL parameters. However, if there were dynamic filters on the page, HTMX would be used to refresh the table content without a full page reload. For the "Cancel" button, a simple `<a>` tag with a redirect is sufficient, as in the original ASP.NET code.
*   **Alpine.js:** Can be used for minor UI state management, e.g., showing/hiding filter panels, but not strictly necessary for this static report. The `DOMContentLoaded` listener is used to initialize DataTables.
*   **DataTables:** Essential for making the HTML table interactive (search, sort, pagination). The template (`workorder_shortage_report.html`) includes the necessary JavaScript to initialize DataTables on the report table. CDN links for DataTables CSS and JS should be in `core/base.html`.

**Implementation Details:**
*   The `workorder_shortage_report.html` template structures the report data within a standard `<table>` tag.
*   The `extra_js` block in the template includes the JavaScript code to initialize DataTables.
*   The table is given an `id="shortageReportTable"` for DataTables to target.
*   The layout is responsive using Tailwind CSS classes.

## Final Notes

*   **Placeholders:** Replace `tblWO_Item_Issue` with the actual database table name for Work Order Item Issues if it differs. Adjust `workorder_issue_list` URL name in `urls.py` and the template if your actual Work Order Issue page has a different URL name.
*   **Error Handling:** The C# code had a generic `try-catch` block. In Django, errors should be handled gracefully, potentially with custom error pages or logging, and user-friendly messages displayed. The provided code includes a basic `error_message` in the context.
*   **Security:** Ensure session handling for `compid` and `finyear` is secure in a production environment (e.g., proper authentication and authorization checks before retrieving session data).
*   **Performance:** For very large reports, consider server-side processing with DataTables or optimized database queries. The current approach fetches all relevant data for the specific WO.
*   **Report Export:** If the Crystal Reports output was used for PDF/Excel exports, this functionality would need to be re-implemented in Django using libraries like `Reportlab` for PDF or `openpyxl` for Excel. This plan focuses on the in-browser display.
*   **Logging:** Implement proper Django logging for issues, similar to the `catch (Exception ex)` in ASP.NET.