## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code for "Dashboard.aspx" and its code-behind file are minimal, essentially serving as a placeholder page within a master page structure. There is no explicit business logic, database interaction, or UI components defined within these files.

Given that the original module is `Module_Inventory_Reports_Dashboard`, we will infer the need for an `InventoryItem` model to demonstrate a common dashboard scenario involving listing and managing inventory data. This allows us to provide a comprehensive, functional example adhering to all specified Django modernization principles.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since no explicit database schema or SQL commands are present in the provided ASP.NET code, we infer a likely scenario for an "Inventory" module. We will assume a database table named `tbl_inventory_item` which stores details about inventory items.

**Inferred Schema:**
*   **TABLE_NAME**: `tbl_inventory_item`
*   **MODEL_NAME**: `InventoryItem`
*   **Columns (and inferred Django Field Types):**
    *   `ItemName` (VARCHAR) -> `item_name` (CharField)
    *   `SKU` (VARCHAR, UNIQUE) -> `sku` (CharField)
    *   `Quantity` (INT) -> `quantity` (IntegerField)
    *   `Price` (DECIMAL) -> `price` (DecimalField)
    *   `LastUpdated` (DATETIME) -> `last_updated` (DateTimeField)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Due to the minimal nature of the provided ASP.NET files, no specific backend functionality or CRUD operations are visible. However, for a complete dashboard experience and to showcase modern Django patterns, we will implement full CRUD (Create, Read, Update, Delete) for the inferred `InventoryItem` model.

*   **Create:** Functionality to add new inventory items.
*   **Read:** Display a list of all inventory items on the dashboard. This will be the primary function of the "Dashboard" view.
*   **Update:** Ability to modify existing inventory item details.
*   **Delete:** Functionality to remove inventory items.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET file only defines content placeholders (`<asp:Content>`). To provide a practical Django equivalent for a dashboard, we will infer the following UI components:

*   **Data Table:** A main list view (equivalent to an ASP.NET GridView) to display inventory items with client-side searching, sorting, and pagination using DataTables.
*   **Add/Edit Form (Modal):** A modal form for creating new inventory items and editing existing ones. These forms will be loaded dynamically using HTMX.
*   **Delete Confirmation (Modal):** A modal dialog for confirming item deletions, also loaded dynamically via HTMX.
*   **Buttons:** Action buttons for "Add New Item," "Edit," and "Delete" for each item, leveraging HTMX to trigger modal interactions.

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
We define the `InventoryItem` model, mapping to the `tbl_inventory_item` table. Business logic for an inventory item, such as calculating its total value, is encapsulated within the model following the "fat model" principle.

**File:** `inventory_reports/models.py`

```python
from django.db import models

class InventoryItem(models.Model):
    item_name = models.CharField(db_column='ItemName', max_length=255, verbose_name='Item Name')
    sku = models.CharField(db_column='SKU', max_length=100, unique=True, verbose_name='SKU')
    quantity = models.IntegerField(db_column='Quantity', default=0, verbose_name='Quantity on Hand')
    price = models.DecimalField(db_column='Price', max_digits=10, decimal_places=2, default=0.00, verbose_name='Unit Price')
    last_updated = models.DateTimeField(db_column='LastUpdated', auto_now=True, verbose_name='Last Updated')

    class Meta:
        managed = False  # Set to True if Django should manage the table (create/alter)
        db_table = 'tbl_inventory_item'
        verbose_name = 'Inventory Item'
        verbose_name_plural = 'Inventory Items'
        ordering = ['item_name'] # Default ordering for list views

    def __str__(self):
        return self.item_name

    def calculate_total_value(self):
        """
        Business logic: Calculates the total value of this inventory item.
        """
        return self.quantity * self.price

    def is_low_stock(self, threshold=10):
        """
        Business logic: Checks if the item quantity is below a given threshold.
        """
        return self.quantity < threshold

```

### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` is created for `InventoryItem`, including all necessary fields. Tailwind CSS classes are applied via widgets for consistent styling.

**File:** `inventory_reports/forms.py`

```python
from django import forms
from .models import InventoryItem

class InventoryItemForm(forms.ModelForm):
    class Meta:
        model = InventoryItem
        fields = ['item_name', 'sku', 'quantity', 'price']
        widgets = {
            'item_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'sku': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'price': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
        
    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity < 0:
            raise forms.ValidationError("Quantity cannot be negative.")
        return quantity

    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price is not None and price < 0:
            raise forms.ValidationError("Price cannot be negative.")
        return price
```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Class-Based Views (CBVs) are used for all CRUD operations, keeping them concise (thin views) by delegating business logic to the model. An additional `InventoryItemTablePartialView` is created to serve the DataTables content via HTMX. Success messages are added, and HTMX-specific responses (HX-Trigger) are included for seamless updates.

**File:** `inventory_reports/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import InventoryItem
from .forms import InventoryItemForm

class InventoryItemListView(ListView):
    model = InventoryItem
    template_name = 'inventory_reports/inventoryitem/list.html'
    context_object_name = 'inventory_items' # Renamed for clarity

class InventoryItemTablePartialView(ListView):
    """
    View to render only the inventory items table, intended for HTMX requests.
    """
    model = InventoryItem
    template_name = 'inventory_reports/inventoryitem/_inventoryitem_table.html'
    context_object_name = 'inventory_items'

class InventoryItemCreateView(CreateView):
    model = InventoryItem
    form_class = InventoryItemForm
    template_name = 'inventory_reports/inventoryitem/form.html'
    success_url = reverse_lazy('inventoryitem_list') # Not strictly used with HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Inventory Item added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX, triggering a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInventoryItemList'
                }
            )
        return response

class InventoryItemUpdateView(UpdateView):
    model = InventoryItem
    form_class = InventoryItemForm
    template_name = 'inventory_reports/inventoryitem/form.html'
    success_url = reverse_lazy('inventoryitem_list') # Not strictly used with HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Inventory Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX, triggering a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInventoryItemList'
                }
            )
        return response

class InventoryItemDeleteView(DeleteView):
    model = InventoryItem
    template_name = 'inventory_reports/inventoryitem/confirm_delete.html'
    success_url = reverse_lazy('inventoryitem_list') # Not strictly used with HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Inventory Item deleted successfully.')
        if request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX, triggering a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInventoryItemList'
                }
            )
        return response
```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates are designed for HTMX-driven interactions. The main `list.html` includes a modal structure, and the actual table, form, and delete confirmation are rendered as partials (`_*.html`) loaded into the modal or main content area via HTMX. DataTables initialization is included in the table partial.

**File:** `inventory_reports/templates/inventory_reports/inventoryitem/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Inventory Items Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200"
            hx-get="{% url 'inventoryitem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Item
        </button>
    </div>

    <div id="inventoryitemTable-container"
         hx-trigger="load, refreshInventoryItemList from:body"
         hx-get="{% url 'inventoryitem_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-xl">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Inventory Data...</p>
        </div>
    </div>

    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on keydown from document if key is 'Escape' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 my-8 relative max-h-[90vh] overflow-y-auto">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // Currently, it's used for modal toggling directly via HTMX's _ syntax.
    });
</script>
{% endblock %}
```

**File:** `inventory_reports/templates/inventory_reports/inventoryitem/_inventoryitem_table.html`

```html
<table id="inventoryitemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Value</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in inventory_items %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.item_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.sku }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.quantity }}</td>
            <td class="py-3 px-4 whitespace-nowrap">${{ obj.price|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap font-medium text-blue-600">${{ obj.calculate_total_value|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.last_updated|date:"Y-m-d H:i" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                    class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-md hover:bg-gray-100 transition duration-150"
                    hx-get="{% url 'inventoryitem_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-gray-100 transition duration-150"
                    hx-get="{% url 'inventoryitem_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-6 text-center text-gray-500">No inventory items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization
    $(document).ready(function() {
        $('#inventoryitemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "search": "Filter results:",
                "lengthMenu": "Show _MENU_ entries"
            }
        });
    });
</script>
```

**File:** `inventory_reports/templates/inventory_reports/inventoryitem/_inventoryitem_form.html`

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Inventory Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="json-enc" hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal }">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {% for field in form %}
            <div class="col-span-1">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors|striptags }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Item
            </button>
        </div>
    </form>
</div>
```

**File:** `inventory_reports/templates/inventory_reports/inventoryitem/_inventoryitem_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete "{{ object.item_name }}" (SKU: {{ object.sku }})?</p>
    <p class="text-red-600 font-medium">This action cannot be undone.</p>

    <div class="mt-8 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'inventoryitem_delete' object.pk %}"
            hx-swap="none"
            hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal }"
            class="inline-flex justify-center py-2 px-5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Confirm Delete
        </button>
    </div>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main list view, CRUD operations, and the HTMX-specific partial table view. This modularizes the URLs within the `inventory_reports` app.

**File:** `inventory_reports/urls.py`

```python
from django.urls import path
from .views import (
    InventoryItemListView,
    InventoryItemCreateView,
    InventoryItemUpdateView,
    InventoryItemDeleteView,
    InventoryItemTablePartialView,
)

urlpatterns = [
    path('inventory/', InventoryItemListView.as_view(), name='inventoryitem_list'),
    path('inventory/table/', InventoryItemTablePartialView.as_view(), name='inventoryitem_table'),
    path('inventory/add/', InventoryItemCreateView.as_view(), name='inventoryitem_add'),
    path('inventory/edit/<int:pk>/', InventoryItemUpdateView.as_view(), name='inventoryitem_edit'),
    path('inventory/delete/<int:pk>/', InventoryItemDeleteView.as_view(), name='inventoryitem_delete'),
]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests cover the `InventoryItem` model's properties and business logic methods. Integration tests verify the functionality of all views, including HTMX interactions, ensuring proper responses and data manipulation.

**File:** `inventory_reports/tests.py`

```python
from decimal import Decimal
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import InventoryItem
from .forms import InventoryItemForm

class InventoryItemModelTest(TestCase):
    """
    Unit tests for the InventoryItem model and its methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects used by all test methods
        cls.item1 = InventoryItem.objects.create(
            item_name='Laptop Pro',
            sku='LTP-001',
            quantity=50,
            price=Decimal('1200.00')
        )
        cls.item2 = InventoryItem.objects.create(
            item_name='Wireless Mouse',
            sku='WM-002',
            quantity=5,
            price=Decimal('25.50')
        )

    def test_inventory_item_creation(self):
        """Test that an InventoryItem can be created correctly."""
        self.assertEqual(self.item1.item_name, 'Laptop Pro')
        self.assertEqual(self.item1.sku, 'LTP-001')
        self.assertEqual(self.item1.quantity, 50)
        self.assertEqual(self.item1.price, Decimal('1200.00'))
        self.assertIsNotNone(self.item1.last_updated)

    def test_item_name_label(self):
        """Test verbose_name for item_name field."""
        field_label = self.item1._meta.get_field('item_name').verbose_name
        self.assertEqual(field_label, 'Item Name')

    def test_sku_uniqueness(self):
        """Test that SKU field enforces uniqueness."""
        with self.assertRaises(Exception): # IntegrityError or ValidationError depending on DB
            InventoryItem.objects.create(
                item_name='Another Laptop',
                sku='LTP-001', # Duplicate SKU
                quantity=10,
                price=Decimal('1100.00')
            )

    def test_calculate_total_value_method(self):
        """Test the calculate_total_value method."""
        self.assertEqual(self.item1.calculate_total_value(), Decimal('60000.00')) # 50 * 1200
        self.assertEqual(self.item2.calculate_total_value(), Decimal('127.50'))   # 5 * 25.50

    def test_is_low_stock_method(self):
        """Test the is_low_stock method."""
        self.assertFalse(self.item1.is_low_stock(threshold=10)) # 50 is not < 10
        self.assertTrue(self.item2.is_low_stock(threshold=10))  # 5 is < 10
        self.assertFalse(self.item2.is_low_stock(threshold=5))  # 5 is not < 5 (strictly less)

    def test_str_method(self):
        """Test the __str__ method returns item name."""
        self.assertEqual(str(self.item1), 'Laptop Pro')

class InventoryItemViewsTest(TestCase):
    """
    Integration tests for InventoryItem CRUD views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.item = InventoryItem.objects.create(
            item_name='Test Item',
            sku='TI-001',
            quantity=10,
            price=Decimal('100.00')
        )
        cls.list_url = reverse('inventoryitem_list')
        cls.add_url = reverse('inventoryitem_add')
        cls.edit_url = reverse('inventoryitem_edit', args=[cls.item.pk])
        cls.delete_url = reverse('inventoryitem_delete', args=[cls.item.pk])
        cls.table_partial_url = reverse('inventoryitem_table')

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test the list view displays items."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/inventoryitem/list.html')
        self.assertContains(response, 'Test Item')
        self.assertContains(response, 'TI-001')

    def test_table_partial_view_get_htmx(self):
        """Test the HTMX partial for the table content."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_partial_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/inventoryitem/_inventoryitem_table.html')
        self.assertContains(response, 'Test Item')
        self.assertContains(response, '<table id="inventoryitemTable"') # Verify DataTables table is present

    def test_create_view_get(self):
        """Test GET request to create form."""
        response = self.client.get(self.add_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/inventoryitem/form.html')
        self.assertIsInstance(response.context['form'], InventoryItemForm)
        self.assertContains(response, 'Add Inventory Item')

    def test_create_view_post_valid(self):
        """Test POST request to create new item with valid data."""
        data = {
            'item_name': 'New Item',
            'sku': 'NI-002',
            'quantity': 20,
            'price': '50.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.add_url, data, **headers)
        # HTMX success should return 204 No Content
        self.assertEqual(response.status_code, 204)
        self.assertTrue(InventoryItem.objects.filter(item_name='New Item').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInventoryItemList')

    def test_create_view_post_invalid(self):
        """Test POST request to create new item with invalid data (e.g., negative quantity)."""
        data = {
            'item_name': 'Invalid Item',
            'sku': 'II-003',
            'quantity': -5, # Invalid quantity
            'price': '10.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.add_url, data, **headers)
        # Invalid form submission via HTMX should return 200 with form errors
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Quantity cannot be negative.')
        self.assertFalse(InventoryItem.objects.filter(item_name='Invalid Item').exists())

    def test_update_view_get(self):
        """Test GET request to update form."""
        response = self.client.get(self.edit_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/inventoryitem/form.html')
        self.assertContains(response, 'Edit Inventory Item')
        self.assertContains(response, 'Test Item') # Pre-filled data

    def test_update_view_post_valid(self):
        """Test POST request to update existing item with valid data."""
        updated_data = {
            'item_name': 'Updated Test Item',
            'sku': 'TI-001', # SKU must remain unique
            'quantity': 15,
            'price': '110.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.edit_url, updated_data, **headers)
        self.assertEqual(response.status_code, 204)
        self.item.refresh_from_db()
        self.assertEqual(self.item.item_name, 'Updated Test Item')
        self.assertEqual(self.item.quantity, 15)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInventoryItemList')

    def test_update_view_post_invalid(self):
        """Test POST request to update existing item with invalid data."""
        updated_data = {
            'item_name': 'Updated Test Item',
            'sku': 'TI-001',
            'quantity': -1, # Invalid quantity
            'price': '110.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.edit_url, updated_data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Quantity cannot be negative.')
        self.item.refresh_from_db() # Ensure item was not updated
        self.assertEqual(self.item.quantity, 10)

    def test_delete_view_get(self):
        """Test GET request to delete confirmation."""
        response = self.client.get(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/inventoryitem/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.item.item_name)

    def test_delete_view_post_htmx(self):
        """Test POST request to delete item."""
        # Create another item to ensure only one is deleted
        InventoryItem.objects.create(item_name='Another Item', sku='AI-001', quantity=1, price=1.00)
        self.assertEqual(InventoryItem.objects.count(), 2)

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.delete_url, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(InventoryItem.objects.filter(pk=self.item.pk).exists())
        self.assertEqual(InventoryItem.objects.count(), 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInventoryItemList')

    def test_delete_non_existent_item(self):
        """Test deleting a non-existent item results in 404."""
        non_existent_url = reverse('inventoryitem_delete', args=[99999])
        response = self.client.post(non_existent_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404)

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
HTMX and Alpine.js are foundational to the frontend.

*   **HTMX for CRUD:** All forms (add, edit, delete) are submitted via HTMX (`hx-post`). The `hx-target` and `hx-swap` attributes on the buttons and forms ensure that modals are populated and forms submit without full page reloads.
*   **HTMX for List Refresh:** After a successful create, update, or delete operation, the server responds with an `HX-Trigger: refreshInventoryItemList` header. The `inventory_reports/inventoryitem/list.html` template listens for this trigger (`hx-trigger="load, refreshInventoryItemList from:body"`) on the `inventoryitemTable-container` div, automatically re-fetching and re-rendering the DataTables partial.
*   **Alpine.js for Modals:** The `_` (hyperscript) syntax is used to manage the visibility of the modal (`add .is-active to #modal`, `remove .is-active from #modal`). This is a concise way to handle client-side UI state without writing explicit JavaScript functions. The `hx-on::after-request` on forms also uses this for modal closing after submission.
*   **DataTables:** The `_inventoryitem_table.html` partial includes the JavaScript to initialize DataTables on the rendered table. This ensures features like client-side search, sort, and pagination work seamlessly. The `$(document).ready()` ensures the script runs after the partial is loaded by HTMX.
*   **DRY CDN Links:** Assume that `core/base.html` already includes the necessary CDN links for HTMX, Alpine.js, jQuery, and DataTables.

## Final Notes

*   This comprehensive plan converts the conceptual "Dashboard" into a functional, modern Django application, focusing on the Inventory module as inferred from the original ASP.NET path.
*   Placeholders have been replaced with concrete values and examples suitable for an inventory management system.
*   The architecture strictly adheres to "Fat Model, Thin View" principles, robust testing, and modern frontend techniques (HTMX, Alpine.js, DataTables) without reliance on complex custom JavaScript.
*   This structure promotes maintainability, scalability, and an improved user experience, all while being amenable to AI-assisted automation for actual code generation and refactoring.