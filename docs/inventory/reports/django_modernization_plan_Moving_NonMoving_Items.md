## ASP.NET to Django Conversion Script: Moving Non-Moving Items Report

This document outlines the modernization plan for the ASP.NET "Moving Non-Moving Items" report functionality to a modern Django-based solution. The focus is on a strategic, automation-driven transition, emphasizing business value, improved maintainability, and enhanced user experience through contemporary web technologies.

This ASP.NET page primarily serves as an **input form for a report generation**, not a typical CRUD (Create, Read, Update, Delete) interface for a specific data entity like "Moving Non-Moving Items." Therefore, the Django solution will reflect this by focusing on a single form view, leveraging Django's robust form handling and routing to a report display page (which is out of scope for this specific conversion but implied by the original `Response.Redirect`).

### Business Value & Outcomes:

1.  **Enhanced User Experience:** Replacing outdated ASP.NET controls with modern HTML5 inputs, coupled with HTMX for dynamic interactions and Alpine.js for lightweight UI state management, provides a smoother, more responsive user interface.
2.  **Improved Maintainability:** Transitioning to Django's clear Model-View-Template (MVT) architecture, fat models, and thin views significantly reduces complexity, making the codebase easier to understand, debug, and extend.
3.  **Future-Proofing:** Adopting Django 5.0+ and modern frontend tools ensures the application is built on current, actively supported technologies, reducing technical debt and enabling easier integration with future systems.
4.  **Cost Efficiency:** AI-assisted automation, guided by this plan, minimizes manual coding effort, accelerating the migration process and reducing development costs.
5.  **Scalability & Performance:** Django's robust design and Python's ecosystem provide a strong foundation for scaling the application as business needs grow, with improved performance over legacy ASP.NET.
6.  **Security:** Django's built-in security features, such as CSRF protection and secure session management, enhance the application's overall security posture compared to manual implementations in legacy systems.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination (note: not directly applicable to this form page, but critical for the subsequent report display)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns relevant to the ASP.NET code.

Based on the ASP.NET code-behind, the application interacts with the following tables:

*   **`tblFinancial_master`**: Used to retrieve the financial year's `FinYearFrom` and `FinYearTo` dates.
    *   Columns inferred: `FinYearId`, `CompId`, `FinYearFrom` (Date), `FinYearTo` (Date).
*   **`tblDG_Category_Master`**: Used to populate the `DrpCategory` dropdown.
    *   Columns inferred: `CId`, `Symbol` (String), `CName` (String).

### Step 2: Identify Backend Functionality

Task: Determine the operations performed by the ASP.NET code.

This ASP.NET page is primarily a **data input and redirection mechanism**:

*   **Read (Data for Form Population):**
    *   Reads `FinYearFrom` and `FinYearTo` from `tblFinancial_master` based on `Session["compid"]` and `Session["finyear"]` to display the current financial year range and set the default "From Date".
    *   Reads `CId`, `Symbol`, `CName` from `tblDG_Category_Master` to populate the "Category" dropdown list.
*   **Validation:**
    *   Performs client-side (via validators) and server-side date validation (`TxtTodate` >= `Txtfromdate`, `Txtfromdate` >= `lblFromDate`).
*   **Redirection (Report Generation):**
    *   Upon successful validation, it collects all form parameters (`from_date`, `to_date`, `category`, `po_rate`, `item_type`) and redirects to `Moving_NonMoving_Items_Details.aspx` with these parameters encoded in the URL. This implies the actual "report" is generated on a separate page.

There are **no direct CRUD operations** (Create, Update, Delete) on "Moving Non-Moving Items" from this page; it only gathers parameters for a report.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

*   `lblFromDate`, `lblToDate`: Display labels for the financial year dates.
*   `Txtfromdate`, `TxtTodate`: Textboxes for date input, with calendar extenders (date pickers). These will be HTML5 `input type="date"` or a simple Alpine.js date picker.
*   `DrpCategory`: Dropdown for selecting a category. This will be an HTML `select` element populated dynamically.
*   `RadRate`: Radio button list for "PO Rate" (Max, Min, Average, Latest). This will be HTML `input type="radio"` group.
*   `RadMovingItem`: Radio button list for "Moving Items" or "Non-Moving Items". This will also be an HTML `input type="radio"` group.
*   `BtnView`: Submit button.
*   `lblMessage`: Label to display error messages.

### Step 4: Generate Django Code

We will structure the Django application (e.g., named `inventory`) to manage this functionality.

#### 4.1 Models (`inventory/models.py`)

These models will represent the underlying database tables used for lookup data. They are set to `managed = False` as they map to existing tables.

```python
from django.db import models
from datetime import date

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master to retrieve financial year dates.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_from = models.DateField(db_column='FinYearFrom')
    fin_year_to = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"FY {self.fin_year_from.year}-{self.fin_year_to.year} (Comp: {self.comp_id})"

    @classmethod
    def get_active_financial_year(cls, comp_id, fin_year_id):
        """
        Retrieves the financial year based on component and financial year ID.
        This simulates fetching the active financial year from session context.
        """
        try:
            return cls.objects.get(comp_id=comp_id, fin_year_id=fin_year_id)
        except cls.DoesNotExist:
            return None

class Category(models.Model):
    """
    Maps to tblDG_Category_Master to populate the category dropdown.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50) # Assuming max_length based on typical usage
    cname = models.CharField(db_column='CName', max_length=100) # Assuming max_length based on typical usage

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}"

```

#### 4.2 Forms (`inventory/forms.py`)

A standard Django `forms.Form` will be used as this page is for parameter collection, not directly for a specific model's CRUD. The form will handle validation and dynamic population of choices.

```python
from django import forms
from .models import Category
from datetime import date

class MovingNonMovingItemsForm(forms.Form):
    """
    Form to capture parameters for the Moving Non-Moving Items report.
    """
    # Choices for PO Rate Radio Button List
    PO_RATE_CHOICES = [
        ('0', 'Max'),
        ('1', 'Min'),
        ('2', 'Average'),
        ('3', 'Latest'),
    ]

    # Choices for Moving/Non-Moving Items Radio Button List
    ITEM_TYPE_CHOICES = [
        ('0', 'Moving Items'),
        ('1', 'Non-Moving Items'),
    ]

    from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # HTML5 date picker
        }),
        required=True,
        error_messages={'required': 'From Date is required.'}
    )
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # HTML5 date picker
        }),
        required=True,
        error_messages={'required': 'To Date is required.'}
    )
    category = forms.ChoiceField(
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-10'
        }),
        required=True, # Though ASP.NET allows "Select", we'll make it required or set a default
    )
    po_rate = forms.ChoiceField(
        choices=PO_RATE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'space-x-4'}),
        initial='0', # 'Max' is selected by default in ASP.NET
        required=True
    )
    item_type = forms.ChoiceField(
        choices=ITEM_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'space-x-4'}),
        initial='0', # 'Moving Items' is selected by default in ASP.NET
        required=True
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically populate category dropdown from the database
        self.fields['category'].choices = [('', 'Select')] + \
                                          [(str(c.cid), f"[{c.symbol}] - {c.cname}")
                                           for c in Category.objects.all().order_by('cname')]

    def clean(self):
        """
        Custom validation for date range and against financial year dates.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        # Simulate financial year dates from context (replace with actual session/user-specific logic)
        # For demonstration, hardcoding placeholder values, but in a real app these come from request.session
        # or user profile lookup.
        # This will be passed from the view's get_initial() or context.
        financial_year_start = self.initial.get('financial_year_start_date')
        financial_year_end = self.initial.get('financial_year_end_date')

        if from_date and to_date:
            if from_date > to_date:
                self.add_error('from_date', 'From date should be Less than or Equal to To Date!')
        
        # ASP.NET original logic: from_date >= financial_year_start, and from_date should not be Less than Opening Date
        # The logic `Convert.ToDateTime(fun.FromDate(lblFromDate.Text)) <= Convert.ToDateTime(fun.FromDate(Txtfromdate.Text))`
        # means Txtfromdate must be >= lblFromDate.
        if from_date and financial_year_start:
            if from_date < financial_year_start:
                self.add_error('from_date', 'From date should not be Less than Opening Date!')

        return cleaned_data

```

#### 4.3 Views (`inventory/views.py`)

A single `FormView` will manage displaying the form, handling initial data, and processing submissions. The view will be thin, delegating validation to the form.

```python
from django.views.generic import FormView
from django.urls import reverse_lazy
from django.shortcuts import redirect
from django.contrib import messages
from django.http import HttpResponse
from .forms import MovingNonMovingItemsForm
from .models import FinancialYear
from datetime import date # To handle date objects

class MovingNonMovingItemsFormView(FormView):
    """
    Handles the display and submission of the Moving Non-Moving Items report parameters form.
    """
    template_name = 'inventory/moving_non_moving_items/form.html'
    form_class = MovingNonMovingItemsForm

    def get_initial(self):
        """
        Populate initial form data and financial year dates from session/database.
        Simulates ASP.NET Session["compid"] and Session["finyear"].
        """
        initial = super().get_initial()
        
        # TODO: Replace with actual session/user data retrieval
        # For demonstration, using placeholder values for comp_id and fin_year_id.
        # In a real Django app, these would come from request.session, request.user, etc.
        # Example: comp_id = self.request.session.get('compid', 1)
        #          fin_year_id = self.request.session.get('finyear', 1)
        comp_id = 1 # Placeholder: Get from request.session or user profile
        fin_year_id = 1 # Placeholder: Get from request.session or user profile

        fin_year = FinancialYear.get_active_financial_year(comp_id, fin_year_id)
        if fin_year:
            initial['financial_year_start_date'] = fin_year.fin_year_from
            initial['financial_year_end_date'] = fin_year.fin_year_to
            # Set initial 'from_date' to financial year start date as per ASP.NET
            initial['from_date'] = fin_year.fin_year_from
        else:
            # Fallback if no financial year found (e.g., provide today's date)
            initial['financial_year_start_date'] = date.today()
            initial['financial_year_end_date'] = date.today()
            initial['from_date'] = date.today()

        initial['to_date'] = date.today() # Default to current date for 'To Date'
        return initial

    def get_context_data(self, **kwargs):
        """
        Add financial year label dates to the context for display.
        """
        context = super().get_context_data(**kwargs)
        # Use initial data to get financial year dates for display
        fin_year_start = self.get_initial().get('financial_year_start_date')
        fin_year_end = self.get_initial().get('financial_year_end_date')

        context['lbl_from_date'] = fin_year_start.strftime('%d-%m-%Y') if fin_year_start else ''
        context['lbl_to_date'] = fin_year_end.strftime('%d-%m-%Y') if fin_year_end else ''
        return context

    def form_valid(self, form):
        """
        Handle successful form submission by redirecting with parameters.
        Simulates the Response.Redirect in ASP.NET.
        """
        from_date = form.cleaned_data['from_date'].strftime('%Y-%m-%d')
        to_date = form.cleaned_data['to_date'].strftime('%Y-%m-%d')
        category_id = form.cleaned_data['category']
        po_rate = form.cleaned_data['po_rate']
        item_type = form.cleaned_data['item_type']

        # The original ASP.NET used encryption and random keys for URL parameters.
        # In Django, passing parameters directly is standard, and security should be handled
        # by authentication/authorization on the target view (e.g., 'report_details').
        # We will pass clean, readable parameters.

        query_params = {
            'Cid': category_id,
            'RadVal': po_rate,
            'FDate': from_date,
            'TDate': to_date,
            'OpeningDt': self.get_initial().get('financial_year_start_date').strftime('%Y-%m-%d'),
            'RPTHeader': item_type,
            # 'Key': getRandomKey (Django does not need this for security; handled by session/auth)
        }
        
        # Prepare URL for the report details page. This URL would need to be defined
        # in the Django app that handles the report generation.
        # Example: reverse_lazy('inventory:moving_non_moving_items_details')
        # For demonstration, we'll use a placeholder URL.
        report_url = reverse_lazy('inventory:moving_non_moving_items_details')

        # If it's an HTMX request, we can use HX-Redirect header
        if self.request.headers.get('HX-Request'):
            response = HttpResponse(status=204) # No content
            # HTMX will follow this header to redirect the browser
            response['HX-Redirect'] = f"{report_url}?{'&'.join(f'{k}={v}' for k, v in query_params.items())}"
            messages.success(self.request, 'Report parameters submitted successfully. Redirecting...')
            return response
        else:
            # For non-HTMX requests, perform a standard Django redirect
            messages.success(self.request, 'Report parameters submitted successfully. Redirecting...')
            return redirect(f"{report_url}?{'&'.join(f'{k}={v}' for k, v in query_params.items())}")

    def form_invalid(self, form):
        """
        Handle invalid form submission by re-rendering the form with errors.
        This allows HTMX to swap the content with validation messages.
        """
        messages.error(self.request, 'Please correct the errors below.')
        return self.render_to_response(self.get_context_data(form=form))

```

#### 4.4 Templates (`inventory/templates/inventory/moving_non_moving_items/form.html`)

This template will display the form for inputting report parameters. It will leverage HTMX for dynamic form submission feedback and Alpine.js for any simple UI state (though minimal here).

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-2xl">
    <div class="bg-white shadow-lg rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-4">Moving Non-Moving Items</h2>

        <div class="mb-4 text-gray-600">
            <span class="font-semibold">Financial Year:</span> 
            From Date: <span class="font-bold text-indigo-700">{{ lbl_from_date }}</span> 
            &nbsp;To: <span class="font-bold text-indigo-700">{{ lbl_to_date }}</span>
        </div>

        <form hx-post="{% url 'inventory:moving_non_moving_items_form' %}" 
              hx-swap="outerHTML" 
              hx-target="#form-container">
            {% csrf_token %}
            
            <div id="form-container" class="space-y-6">
                <!-- Message display area -->
                {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                    <div class="p-3 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% endif %}">
                        {{ message }}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Date Inputs -->
                <div class="flex flex-wrap -mx-2">
                    <div class="w-full md:w-1/2 px-2 mb-4">
                        <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                        {{ form.from_date }}
                        {% if form.from_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                        {% endif %}
                    </div>
                    <div class="w-full md:w-1/2 px-2 mb-4">
                        <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                        {{ form.to_date }}
                        {% if form.to_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Category Dropdown -->
                <div class="mb-4">
                    <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    {{ form.category }}
                    {% if form.category.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>
                    {% endif %}
                </div>

                <!-- PO Rate Radio Buttons -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">PO Rate</label>
                    <div class="flex items-center space-x-6">
                        {% for radio in form.po_rate %}
                        <div class="flex items-center">
                            {{ radio.tag }}
                            <label for="{{ radio.id_for_label }}" class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</label>
                        </div>
                        {% endfor %}
                    </div>
                    {% if form.po_rate.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.po_rate.errors }}</p>
                    {% endif %}
                </div>

                <!-- Moving/Non-Moving Items Radio Buttons -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"></label> {# Empty label to match original layout #}
                    <div class="flex items-center space-x-6">
                        {% for radio in form.item_type %}
                        <div class="flex items-center">
                            {{ radio.tag }}
                            <label for="{{ radio.id_for_label }}" class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</label>
                        </div>
                        {% endfor %}
                    </div>
                    {% if form.item_type.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.item_type.errors }}</p>
                    {% endif %}
                </div>

                <!-- Submit Button -->
                <div class="flex justify-start">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Proceed
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is not strictly necessary for this form as HTMX handles submission and validation messages.
    // However, if custom client-side date pickers or other interactive UI elements were needed, Alpine.js would be used here.
    // Example for a simple Alpine component (if you were to manage form state or dynamic elements):
    document.addEventListener('alpine:init', () => {
        Alpine.data('formController', () => ({
            // Example of a data property:
            // message: '',
            // showMessage: false,
            // Example of an action:
            // init() { /* component initialization */ },
            // submitForm() { /* handle form submission if not using hx-post directly on form */ }
        }));
    });
</script>
{% endblock %}

```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL pattern for the report parameter form. A placeholder URL for the "details" page is also included.

```python
from django.urls import path
from .views import MovingNonMovingItemsFormView

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    path('moving-non-moving-items/', MovingNonMovingItemsFormView.as_view(), name='moving_non_moving_items_form'),
    # Placeholder for the actual report details page (Moving_NonMoving_Items_Details.aspx equivalent)
    # This URL would lead to a view that processes the parameters and displays the report,
    # likely using DataTables as per the AutoERP guidelines.
    path('moving-non-moving-items/details/', MovingNonMovingItemsFormView.as_view(), name='moving_non_moving_items_details'), # Placeholder
]

```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive unit tests for models and integration tests for the form and view.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import FinancialYear, Category
from .forms import MovingNonMovingItemsForm
from datetime import date, timedelta
from unittest.mock import patch # For mocking session data if needed

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.fin_year_1 = FinancialYear.objects.create(
            fin_year_id=1, comp_id=1, fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31)
        )
        cls.fin_year_2 = FinancialYear.objects.create(
            fin_year_id=2, comp_id=1, fin_year_from=date(2024, 4, 1), fin_year_to=date(2025, 3, 31)
        )
        cls.fin_year_diff_comp = FinancialYear.objects.create(
            fin_year_id=1, comp_id=2, fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31)
        )

    def test_financial_year_creation(self):
        self.assertEqual(self.fin_year_1.comp_id, 1)
        self.assertEqual(self.fin_year_1.fin_year_from, date(2023, 4, 1))
        self.assertEqual(self.fin_year_1.fin_year_to, date(2024, 3, 31))

    def test_financial_year_str(self):
        expected_str = "FY 2023-2024 (Comp: 1)"
        self.assertEqual(str(self.fin_year_1), expected_str)

    def test_get_active_financial_year_found(self):
        fin_year = FinancialYear.get_active_financial_year(comp_id=1, fin_year_id=1)
        self.assertEqual(fin_year, self.fin_year_1)

    def test_get_active_financial_year_not_found(self):
        fin_year = FinancialYear.get_active_financial_year(comp_id=99, fin_year_id=99)
        self.assertIsNone(fin_year)

class CategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.category_1 = Category.objects.create(cid=101, symbol='ELE', cname='Electronics')
        cls.category_2 = Category.objects.create(cid=102, symbol='TEX', cname='Textiles')

    def test_category_creation(self):
        self.assertEqual(self.category_1.symbol, 'ELE')
        self.assertEqual(self.category_1.cname, 'Electronics')

    def test_category_str(self):
        expected_str = "[ELE] - Electronics"
        self.assertEqual(str(self.category_1), expected_str)

class MovingNonMovingItemsFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create categories for form choices
        Category.objects.create(cid=1, symbol='CAT_A', cname='Category A')
        Category.objects.create(cid=2, symbol='CAT_B', cname='Category B')

    def setUp(self):
        self.financial_year_start = date(2023, 1, 1)
        self.financial_year_end = date(2023, 12, 31)
        self.initial_data = {
            'financial_year_start_date': self.financial_year_start,
            'financial_year_end_date': self.financial_year_end
        }

    def test_form_valid_data(self):
        form = MovingNonMovingItemsForm(
            data={
                'from_date': '2023-01-15',
                'to_date': '2023-03-30',
                'category': '1', # Corresponds to Category A
                'po_rate': '0', # Max
                'item_type': '0', # Moving Items
            },
            initial=self.initial_data
        )
        self.assertTrue(form.is_valid())

    def test_form_invalid_from_date_after_to_date(self):
        form = MovingNonMovingItemsForm(
            data={
                'from_date': '2023-03-30',
                'to_date': '2023-01-15',
                'category': '1',
                'po_rate': '0',
                'item_type': '0',
            },
            initial=self.initial_data
        )
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)
        self.assertEqual(form.errors['from_date'], ['From date should be Less than or Equal to To Date!'])

    def test_form_invalid_from_date_less_than_financial_year_start(self):
        form = MovingNonMovingItemsForm(
            data={
                'from_date': '2022-12-31', # Before 2023-01-01
                'to_date': '2023-01-15',
                'category': '1',
                'po_rate': '0',
                'item_type': '0',
            },
            initial=self.initial_data
        )
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)
        self.assertEqual(form.errors['from_date'], ['From date should not be Less than Opening Date!'])

    def test_form_required_fields(self):
        form = MovingNonMovingItemsForm(data={}, initial=self.initial_data)
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)
        self.assertIn('to_date', form.errors)
        self.assertIn('category', form.errors)
        self.assertIn('po_rate', form.errors)
        self.assertIn('item_type', form.errors)

    def test_category_choices_populated(self):
        form = MovingNonMovingItemsForm(initial=self.initial_data)
        self.assertIn(('', 'Select'), form.fields['category'].choices)
        self.assertIn(('1', '[CAT_A] - Category A'), form.fields['category'].choices)
        self.assertIn(('2', '[CAT_B] - Category B'), form.fields['category'].choices)


class MovingNonMovingItemsFormViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup data for models
        cls.fin_year = FinancialYear.objects.create(
            fin_year_id=1, comp_id=1, fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31)
        )
        Category.objects.create(cid=1, symbol='CAT_A', cname='Category A')
        Category.objects.create(cid=2, symbol='CAT_B', cname='Category B')

    def setUp(self):
        self.client = Client()
        self.url = reverse('inventory:moving_non_moving_items_form')
        self.success_redirect_url_base = reverse('inventory:moving_non_moving_items_details')

    @patch('inventory.views.FinancialYear.get_active_financial_year')
    def test_get_request_renders_form_with_initial_data(self, mock_get_fin_year):
        mock_get_fin_year.return_value = self.fin_year
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/moving_non_moving_items/form.html')
        self.assertIsInstance(response.context['form'], MovingNonMovingItemsForm)
        self.assertEqual(response.context['lbl_from_date'], '01-04-2023')
        self.assertEqual(response.context['lbl_to_date'], '31-03-2024')
        self.assertEqual(response.context['form'].initial['from_date'], date(2023, 4, 1))

    @patch('inventory.views.FinancialYear.get_active_financial_year')
    def test_post_request_valid_data_redirects(self, mock_get_fin_year):
        mock_get_fin_year.return_value = self.fin_year
        data = {
            'from_date': '2023-05-01',
            'to_date': '2023-06-30',
            'category': '1',
            'po_rate': '0',
            'item_type': '0',
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, 302) # Expect redirect
        expected_query = 'Cid=1&RadVal=0&FDate=2023-05-01&TDate=2023-06-30&OpeningDt=2023-04-01&RPTHeader=0'
        self.assertEqual(response.url, f"{self.success_redirect_url_base}?{expected_query}")
        # Check messages framework
        messages = list(response._request.session.pop('_messages'))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Report parameters submitted successfully. Redirecting...')

    @patch('inventory.views.FinancialYear.get_active_financial_year')
    def test_post_request_invalid_data_re_renders_form(self, mock_get_fin_year):
        mock_get_fin_year.return_value = self.fin_year
        data = {
            'from_date': '2023-07-01', # from_date > to_date
            'to_date': '2023-06-01',
            'category': '1',
            'po_rate': '0',
            'item_type': '0',
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, 200) # Should re-render the form
        self.assertTemplateUsed(response, 'inventory/moving_non_moving_items/form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('from_date', response.context['form'].errors)
        messages = list(response._request.session.pop('_messages'))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')


    @patch('inventory.views.FinancialYear.get_active_financial_year')
    def test_htmx_post_request_valid_data(self, mock_get_fin_year):
        mock_get_fin_year.return_value = self.fin_year
        data = {
            'from_date': '2023-05-01',
            'to_date': '2023-06-30',
            'category': '1',
            'po_rate': '0',
            'item_type': '0',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.url, data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success with no content
        expected_query = 'Cid=1&RadVal=0&FDate=2023-05-01&TDate=2023-06-30&OpeningDt=2023-04-01&RPTHeader=0'
        self.assertEqual(response['HX-Redirect'], f"{self.success_redirect_url_base}?{expected_query}")
        messages = list(self.client.session.pop('_messages')) # Messages are set on session
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Report parameters submitted successfully. Redirecting...')

    @patch('inventory.views.FinancialYear.get_active_financial_year')
    def test_htmx_post_request_invalid_data(self, mock_get_fin_year):
        mock_get_fin_year.return_value = self.fin_year
        data = {
            'from_date': '2023-07-01', # from_date > to_date
            'to_date': '2023-06-01',
            'category': '1',
            'po_rate': '0',
            'item_type': '0',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.url, data, **headers)
        self.assertEqual(response.status_code, 200) # Should re-render partial HTML
        self.assertTemplateUsed(response, 'inventory/moving_non_moving_items/form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('from_date', response.context['form'].errors)
        messages = list(self.client.session.pop('_messages'))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:**
    *   The `form` element uses `hx-post` to submit data to the same URL (`{% url 'inventory:moving_non_moving_items_form' %}`).
    *   `hx-swap="outerHTML"` and `hx-target="#form-container"` are used so that upon invalid submission, the entire form (including potential messages) is swapped with the updated form containing validation errors, providing immediate feedback without a full page reload.
    *   On successful submission, the `HX-Redirect` header is sent (status 204), prompting HTMX to navigate the browser to the report details page with the query parameters.
    *   Success/error messages are handled by Django's messages framework, which renders within the `form.html` template.

*   **Alpine.js:**
    *   For this specific form, Alpine.js is not strictly required as HTMX handles the primary dynamic interactions (form submission, validation feedback, redirection).
    *   Its use is noted in the `extra_js` block within the template, indicating where it *would* be integrated if more complex client-side UI logic (e.g., custom date pickers, conditional field visibility based on user input, or advanced form state management) were necessary. The provided template uses HTML5 `type="date"` for simplicity, which browsers handle natively.

*   **DataTables:**
    *   DataTables are *not* used on this `MovingNonMovingItemsFormView` as its purpose is to collect report parameters, not to display tabular data.
    *   As per AutoERP guidelines, DataTables would be integral to the `Moving_NonMoving_Items_Details.aspx` equivalent page (e.g., `moving-non-moving-items/details/`), which would display the actual report data in a searchable, sortable, and paginated table.

### Final Notes

*   **Placeholders:** The `comp_id` and `fin_year_id` in the `get_initial` method of `MovingNonMovingItemsFormView` are placeholders. In a real application, these would be dynamically retrieved from the user's session (`request.session`) or their user profile, reflecting the active company and financial year for the logged-in user.
*   **Security of URL Parameters:** The original ASP.NET code used encryption (`fun.Encrypt`) and URL encoding for report parameters. In Django, the standard practice is to pass raw, clear parameters in the URL and rely on Django's robust authentication and authorization mechanisms (e.g., permissions, decorators like `@login_required`) on the *target* view (`moving_non_moving_items_details`) to secure the data. This approach is more transparent and leverages Django's built-in security features.
*   **Report Details Page (`moving_non_moving_items_details`):** The conversion plan for this page focuses solely on the input form. The subsequent `moving_non_moving_items_details` page would require its own analysis, likely involving a Django `ListView` or a custom view that fetches data based on the submitted parameters and renders it using DataTables for effective presentation.
*   **Tailwind CSS:** The provided HTML templates include Tailwind CSS classes (`block`, `w-full`, `px-3`, `py-2`, `border`, `rounded-md`, `shadow-sm`, `focus:outline-none`, `focus:ring-indigo-500`, etc.) to ensure the modernized UI component is visually consistent with the AutoERP guidelines.