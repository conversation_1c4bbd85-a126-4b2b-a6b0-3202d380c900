## ASP.NET to Django Conversion Script: Enhancing Inventory Reports Search

This modernization plan outlines the transition of your existing ASP.NET Inventory Reports Search module to a highly efficient and modern Django-based solution. By leveraging Django's robust framework, combined with HTMX and Alpine.js for dynamic interfaces, we aim to deliver a faster, more maintainable, and user-friendly system.

**Business Value of Modernization:**

*   **Improved Performance:** Migrate from postback-heavy ASP.NET Web Forms to a light, responsive Django application, enhancing user experience and reducing server load.
*   **Reduced Maintenance Costs:** Transition from legacy ASP.NET/C# to Python and Django, a widely adopted, open-source ecosystem with a large community, simplifying future updates and bug fixes.
*   **Enhanced User Experience:** Implement dynamic forms with HTMX for instant feedback and Alpine.js for seamless UI interactions, eliminating full-page reloads.
*   **Scalability:** Django's architecture is built for scalability, allowing your application to grow with your business needs.
*   **Future-Proofing:** Adopt modern web standards and best practices, positioning your application for future integrations and expansions.
*   **Data Integrity and Security:** Leverage Django's built-in ORM and robust security features to ensure data integrity and protect sensitive information.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   NEVER include base.html template code in your output - assume it already exists.
-   Focus ONLY on component-specific code for the current module.
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

## AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination (applies to *results* pages, not this search *form* page directly).
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables to populate dropdowns, perform autocompletion, and validate inputs. These tables represent lookup data essential for the search forms. The actual search criteria would then be used to query other (unseen in this code) inventory tables for reports.

**Identified Tables and Columns:**

*   **`tblMM_Supplier_master`**: Used for Supplier auto-completion.
    *   Columns: `SupplierId` (PK), `SupplierName`, `CompId` (Company ID for filtering).
*   **`tblDG_Category_Master`**: Used for Item Category dropdown.
    *   Columns: `CId` (PK), `Symbol`, `CName`, `CompId`.
*   **`AccHead`**: Used for Account Head dropdown.
    *   Columns: `Id` (PK), `Symbol`, `Description`, `Category`.
*   **`tblHR_OfficeStaff`**: Used for Employee Name auto-completion.
    *   Columns: `EmpId` (PK), `EmployeeName`, `CompId`.
*   **`BusinessGroup`**: Used for BG Group dropdown (referred to as 'Dept').
    *   Columns: `Id` (PK), `Symbol` (as Dept).

### Step 2: Identify Backend Functionality

The ASP.NET page is a **search form generator**. It gathers various criteria across three main categories (GIN, MRS, MRN) and then **redirects** the user to a *separate* details page to display the results based on those criteria. There are no direct CRUD operations performed on the *search results* themselves on this page.

*   **Read (Data for Form Population):**
    *   Dropdowns for `Drpoption`, `DrpCategory`, `DropDownList1`, `ddlEnterNoMRS`, `ddlDeptMRS`, `ddlEnterNoMRN`, `ddlDeptMRN` are populated from `tblDG_Category_Master`, `AccHead`, `BusinessGroup`.
    *   Autocomplete for `TxtSearchValue` (Supplier) from `tblMM_Supplier_master`.
    *   Autocomplete for `TxtEmpName` / `TxtEmpName1` (Employee) from `tblHR_OfficeStaff`.
*   **Input Validation:** Extensive validation on number formats, date formats, date ranges, and existence of related entities (Supplier, Employee, WO No) before redirection. Client-side alerts (`ClientScript.RegisterStartupScript`) are used.
*   **Conditional UI Logic:** Checkboxes and dropdowns dynamically enable/disable other input fields (e.g., `CkItem` enables `txtItemcode`, `Drpoption` changes radio button selection and enables `txtNo`). This is handled by `AutoPostBack` and server-side event handlers.
*   **Search Action:** The `BtnSearch_Click`, `btnSearchMRS_Click`, `btnSearchMRN_Click` methods collect all parameters and redirect to specific results pages (`Search_details.aspx`, `SearchViewFieldMRS.aspx`, `SearchViewFieldMRN.aspx`) with URL query parameters.

### Step 3: Infer UI Components

The ASP.NET page uses various Web Forms controls to create the search interface.

*   **`TabContainer` (AjaxControlToolkit):** Used to organize the three main search categories (GIN, MRS, MRN). This will be replaced by a simple tabbed interface using HTML, CSS (Tailwind), and Alpine.js for interactivity.
*   **`asp:DropDownList`, `asp:TextBox`, `asp:CheckBox`, `asp:RadioButton`, `asp:RadioButtonList`, `asp:Button`:** Standard form input controls. These will map directly to Django form fields rendered with appropriate HTML input types.
*   **`AutoCompleteExtender` (AjaxControlToolkit):** Provides type-ahead suggestions for Supplier and Employee names. This will be implemented using HTMX for partial updates and a Django view providing JSON responses for the suggestions.
*   **`CalendarExtender` (AjaxControlToolkit):** Provides date pickers. This will be replaced by native HTML5 date inputs or a lightweight JavaScript date picker library managed by Alpine.js.
*   **Validators (`RegularExpressionValidator`, `CompareValidator`):** Client-side validation for dates and formats. This will be handled by Django forms' built-in validation and custom validators.
*   **`SqlDataSource`:** Used for populating dropdowns from the database. In Django, this will be handled by querying models or directly via ORM for form choices.

### Step 4: Generate Django Code

We'll structure this within a Django app named `reports`.

#### 4.1 Models (reports/models.py)

We define models for the lookup tables. These models will use `managed = False` as they map to existing database tables.

```python
from django.db import models

class Supplier(models.Model):
    """
    Maps to tblMM_Supplier_master.
    Used for supplier auto-completion in GIN search.
    """
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True) # Assuming CompId exists

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

    @classmethod
    def search_by_name(cls, prefix_text, company_id):
        """
        Mimics ASP.NET's sql web method for supplier auto-completion.
        """
        # In a real scenario, prefix_text might be escaped for LIKE queries
        # For simplicity, using icontains which handles SQL injection.
        return cls.objects.filter(
            supplier_name__icontains=prefix_text,
            comp_id=company_id
        ).values_list('supplier_name', 'supplier_id').order_by('supplier_name')

class Category(models.Model):
    """
    Maps to tblDG_Category_Master.
    Used for Item Category dropdown in MRS search.
    """
    c_id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    c_name = models.CharField(db_column='CName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Item Category'
        verbose_name_plural = 'Item Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.c_name}"

class AccountHead(models.Model):
    """
    Maps to AccHead.
    Used for Account Head dropdown in GIN search.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)
    category = models.CharField(db_column='Category', max_length=50) # 'Labour' or 'With Material'

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}"

    @classmethod
    def get_by_category(cls, category_name):
        """
        Mimics AcHead() method for populating dropdown based on Labour/With Material.
        """
        return cls.objects.filter(category=category_name).order_by('symbol')

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff.
    Used for employee auto-completion in MRS/MRN search.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.employee_name} [{self.emp_id}]"

    @classmethod
    def search_by_name(cls, prefix_text, company_id):
        """
        Mimics ASP.NET's GetCompletionList web method for employee auto-completion.
        """
        return cls.objects.filter(
            employee_name__icontains=prefix_text,
            comp_id=company_id
        ).values_list('employee_name', 'emp_id').order_by('employee_name')

    @classmethod
    def get_employee_by_code(cls, emp_code, company_id):
        """
        Mimics fun.chkEmpCode for employee validation.
        """
        try:
            return cls.objects.get(emp_id=emp_code, comp_id=company_id)
        except cls.DoesNotExist:
            return None

class BusinessGroup(models.Model):
    """
    Maps to BusinessGroup.
    Used for BG Group dropdown in MRS/MRN search.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50) # Used as Dept in ASP.NET

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

# Utility functions that would be in clsFunctions in ASP.NET,
# but now implemented as model class methods or standalone helpers.
# For example, check_valid_wo_no and date_validation would be here or in a utils.py
# For this example, we'll assume they are handled implicitly or as part of form validation.
```

#### 4.2 Forms (reports/forms.py)

We'll define separate forms for each tab, encapsulating their specific fields and validation logic.

```python
from django import forms
from .models import Supplier, Category, AccountHead, Employee, BusinessGroup
from datetime import datetime

# Common CSS class for inputs for Tailwind styling
INPUT_CLASSES = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
CHECKBOX_CLASSES = 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
RADIO_CLASSES = 'h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500'

class GINSearchForm(forms.Form):
    """
    Form for the GIN Search tab.
    """
    REPORT_OPTIONS = [
        ('0', 'All'), ('1', 'GIN No'), ('2', 'GRR No'),
        ('3', 'GQN No'), ('4', 'GSN No'), ('5', 'PO No')
    ]
    REPORT_FOR_OPTIONS = [
        ('0', 'PO'), ('1', 'GIN'), ('2', 'GRR'), ('3', 'GQN'), ('4', 'GSN')
    ]
    
    # GIN Search fields
    drp_option = forms.ChoiceField(
        choices=REPORT_OPTIONS,
        widget=forms.Select(attrs={'class': INPUT_CLASSES, 'hx-post': 'gin-options-changed/', 'hx-target': '#gin-report-for-radios', 'hx-swap': 'innerHTML', 'hx-indicator': '.htmx-indicator'}),
        label="Enter Nos."
    )
    txt_no = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'placeholder': 'Enter Number'})
    )
    
    # Radio buttons for "Report For:" - these will be dynamically updated by HTMX
    # We define a placeholder for them in the form for initial rendering
    # and provide choices that will be rendered as radio buttons in the template
    report_for = forms.ChoiceField(
        choices=REPORT_FOR_OPTIONS,
        widget=forms.RadioSelect(attrs={'class': RADIO_CLASSES}),
        initial='0', # Default to PO
        label="Report For:",
        required=False
    )

    ck_item = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'ckItemChecked', 'x-on:change': 'if (!ckItemChecked) txtItemcode = \'\'', 'x-init': 'ckItemChecked = false'})
    )
    txt_itemcode = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!ckItemChecked', 'x-model': 'txtItemcode'})
    )

    ck_wono = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'ckWonoChecked', 'x-on:change': 'if (!ckWonoChecked) txtWono = \'\'', 'x-init': 'ckWonoChecked = false'})
    )
    txt_wono = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!ckWonoChecked', 'x-model': 'txtWono'})
    )
    
    ck_supplier = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'ckSupplierChecked', 'x-on:change': 'if (!ckSupplierChecked) txtSupplier = \'\'', 'x-init': 'ckSupplierChecked = false'})
    )
    txt_supplier = forms.CharField(
        max_length=255, required=False,
        widget=forms.TextInput(attrs={
            'class': INPUT_CLASSES, 'x-bind:disabled': '!ckSupplierChecked', 'x-model': 'txtSupplier',
            'hx-get': '/reports/autocomplete/supplier/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-indicator': '.htmx-indicator',
            'hx-params': 'txt_supplier', # This will pass the current text input as a query param
            'autocomplete': 'off' # Disable browser autocomplete
        })
    )

    ck_acc_head = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'ckAccHeadChecked', 'x-on:change': 'if (!ckAccHeadChecked) { rbtnLabour=true; rbtnWithMaterial=false; }', 'x-init': 'ckAccHeadChecked = false'})
    )
    rbtn_labour = forms.BooleanField(
        required=False, initial=True,
        widget=forms.RadioInput(attrs={'class': RADIO_CLASSES, 'name': 'group_ac_head', 'x-model': 'rbtnLabour', 'x-bind:disabled': '!ckAccHeadChecked', 'hx-post': 'gin-acchead-changed/', 'hx-target': '#gin-acchead-dropdown', 'hx-swap': 'innerHTML', 'hx-indicator': '.htmx-indicator'})
    )
    rbtn_with_material = forms.BooleanField(
        required=False,
        widget=forms.RadioInput(attrs={'class': RADIO_CLASSES, 'name': 'group_ac_head', 'x-model': 'rbtnWithMaterial', 'x-bind:disabled': '!ckAccHeadChecked', 'hx-post': 'gin-acchead-changed/', 'hx-target': '#gin-acchead-dropdown', 'hx-swap': 'innerHTML', 'hx-indicator': '.htmx-indicator'})
    )
    dropdown_acc_head = forms.ChoiceField(
        choices=[], required=False,
        widget=forms.Select(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!ckAccHeadChecked'})
    )

    text_from_date = forms.DateField(
        required=False, input_formats=['%d-%m-%Y'],
        widget=forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'}) # HTML5 date input
    )
    text_to_date = forms.DateField(
        required=False, input_formats=['%d-%m-%Y'],
        widget=forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'}) # HTML5 date input
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        self.comp_id = comp_id
        
        # Initial population of AccountHead dropdown based on initial radio state
        self._update_acc_head_choices(initial=True)

    def _update_acc_head_choices(self, initial=False):
        category = 'Labour'
        if not initial and self.data.get('rbtn_with_material') == 'on': # Check if form was submitted with 'With Material'
             category = 'With Material'
        
        acc_heads = AccountHead.get_by_category(category)
        self.fields['dropdown_acc_head'].choices = [(ah.id, str(ah)) for ah in acc_heads]
        if not self.fields['dropdown_acc_head'].choices:
            self.fields['dropdown_acc_head'].choices = [('', 'No options')]

    def clean(self):
        cleaned_data = super().clean()
        
        # Validation for number field if option selected
        drp_option_val = cleaned_data.get('drp_option')
        txt_no_val = cleaned_data.get('txt_no')
        if drp_option_val != '0' and not txt_no_val:
            self.add_error('txt_no', 'Enter a valid number for the selected option.')

        # Conditional field validation
        if cleaned_data.get('ck_item') and not cleaned_data.get('txt_itemcode'):
            self.add_error('txt_itemcode', 'Item Code is required when checked.')
        if cleaned_data.get('ck_wono') and not cleaned_data.get('txt_wono'):
            self.add_error('txt_wono', 'WO No is required when checked.')
        if cleaned_data.get('ck_supplier') and not cleaned_data.get('txt_supplier'):
            self.add_error('txt_supplier', 'Supplier Name is required when checked.')
        elif cleaned_data.get('ck_supplier') and cleaned_data.get('txt_supplier'):
            # Validate supplier name with code
            supplier_text = cleaned_data['txt_supplier']
            if ' [' in supplier_text and supplier_text.endswith(']'):
                try:
                    supplier_id = int(supplier_text.split(' [')[-1][:-1])
                    if not Supplier.objects.filter(supplier_id=supplier_id, supplier_name=supplier_text.split(' [')[0], comp_id=self.comp_id).exists():
                         self.add_error('txt_supplier', 'Invalid Supplier Name or ID.')
                except (ValueError, IndexError):
                    self.add_error('txt_supplier', 'Invalid Supplier Name format.')
            else:
                 self.add_error('txt_supplier', 'Invalid Supplier Name format (should include ID).')

        # Date validation
        from_date = cleaned_data.get('text_from_date')
        to_date = cleaned_data.get('text_to_date')

        if from_date and not to_date:
            self.add_error('text_to_date', 'To Date is required if From Date is entered.')
        if to_date and not from_date:
            self.add_error('text_from_date', 'From Date is required if To Date is entered.')
        if from_date and to_date and from_date > to_date:
            self.add_error('text_to_date', 'From date cannot be after To date.')

        return cleaned_data


class MRSSearchForm(forms.Form):
    """
    Form for the MRS Search tab.
    """
    ENTER_NO_MRS_OPTIONS = [
        ('3', 'All'), ('1', 'MRS No'), ('2', 'MIN No')
    ]
    
    ddl_enter_no_mrs = forms.ChoiceField(
        choices=ENTER_NO_MRS_OPTIONS,
        widget=forms.Select(attrs={'class': INPUT_CLASSES, 'x-model': 'ddlEnterNoMRS', 'x-on:change': 'handleMrsEnterNoChange()'}),
        label="Enter No."
    )
    txt_enter_no_mrs = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': 'ddlEnterNoMRS === "3"', 'x-model': 'txtEnterNoMRS'})
    )

    che_box_item_cat_mrs = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxItemCatMRS', 'x-on:change': 'if (!cheBoxItemCatMRS) drpCategoryMRS = \'\'', 'x-init': 'cheBoxItemCatMRS = false'})
    )
    drp_category_mrs = forms.ChoiceField(
        choices=[], required=False,
        widget=forms.Select(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxItemCatMRS', 'x-model': 'drpCategoryMRS', 'style': 'width: 150px'})
    )

    che_box_item_code_mrs = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxItemCodeMRS', 'x-on:change': 'if (!cheBoxItemCodeMRS) txtItemCodeMRS = \'\'', 'x-init': 'cheBoxItemCodeMRS = false'})
    )
    txt_item_code_mrs = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxItemCodeMRS', 'x-model': 'txtItemCodeMRS'})
    )

    che_box_employee_name_mrs = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxEmployeeNameMRS', 'x-on:change': 'if (!cheBoxEmployeeNameMRS) txtEmpNameMRS = \'\'', 'x-init': 'cheBoxEmployeeNameMRS = false'})
    )
    txt_emp_name_mrs = forms.CharField(
        max_length=255, required=False,
        widget=forms.TextInput(attrs={
            'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxEmployeeNameMRS', 'x-model': 'txtEmpNameMRS',
            'hx-get': '/reports/autocomplete/employee/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#employee-mrs-suggestions',
            'hx-indicator': '.htmx-indicator',
            'hx-params': 'txt_emp_name_mrs',
            'autocomplete': 'off'
        })
    )

    che_box_bg_group_mrs = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxBGGroupMRS', 'x-on:change': 'if (!cheBoxBGGroupMRS) ddlDeptMRS = \'\'', 'x-init': 'cheBoxBGGroupMRS = false'})
    )
    ddl_dept_mrs = forms.ChoiceField(
        choices=[], required=False,
        widget=forms.Select(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxBGGroupMRS', 'x-model': 'ddlDeptMRS'})
    )

    che_box_wo_no_mrs = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxWONoMRS', 'x-on:change': 'if (!cheBoxWONoMRS) txtWONoMRS = \'\'', 'x-init': 'cheBoxWONoMRS = false'})
    )
    txt_wo_no_mrs = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxWONoMRS', 'x-model': 'txtWONoMRS'})
    )

    rad_mrs_min = forms.ChoiceField(
        choices=[('1', 'MRS'), ('2', 'MIN')],
        widget=forms.RadioSelect(attrs={'class': RADIO_CLASSES}),
        initial='1',
        label="Select Date",
        required=False
    )
    txt_from_date_mrs = forms.DateField(
        required=False, input_formats=['%d-%m-%Y'],
        widget=forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'})
    )
    txt_to_date_mrs = forms.DateField(
        required=False, input_formats=['%d-%m-%Y'],
        widget=forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'})
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        self.comp_id = comp_id
        
        # Populate Category and Business Group dropdowns
        self.fields['drp_category_mrs'].choices = [('', 'Select')] + [(cat.c_id, str(cat)) for cat in Category.objects.filter(comp_id=self.comp_id)]
        self.fields['ddl_dept_mrs'].choices = [('', 'Select')] + [(bg.id, str(bg)) for bg in BusinessGroup.objects.all()]


    def clean(self):
        cleaned_data = super().clean()

        ddl_enter_no_mrs_val = cleaned_data.get('ddl_enter_no_mrs')
        txt_enter_no_mrs_val = cleaned_data.get('txt_enter_no_mrs')
        if ddl_enter_no_mrs_val in ['1', '2'] and not txt_enter_no_mrs_val:
            self.add_error('txt_enter_no_mrs', 'Enter MRS or MIN number.')

        if cleaned_data.get('che_box_item_cat_mrs') and not cleaned_data.get('drp_category_mrs'):
            self.add_error('drp_category_mrs', 'Please select Item Category.')
        if cleaned_data.get('che_box_item_code_mrs') and not cleaned_data.get('txt_item_code_mrs'):
            self.add_error('txt_item_code_mrs', 'Item Code is required when checked.')
        
        if cleaned_data.get('che_box_employee_name_mrs') and not cleaned_data.get('txt_emp_name_mrs'):
            self.add_error('txt_emp_name_mrs', 'Employee Name is required when checked.')
        elif cleaned_data.get('che_box_employee_name_mrs') and cleaned_data.get('txt_emp_name_mrs'):
            emp_text = cleaned_data['txt_emp_name_mrs']
            if ' [' in emp_text and emp_text.endswith(']'):
                try:
                    emp_id = int(emp_text.split(' [')[-1][:-1])
                    if not Employee.objects.filter(emp_id=emp_id, employee_name=emp_text.split(' [')[0], comp_id=self.comp_id).exists():
                         self.add_error('txt_emp_name_mrs', 'Invalid Employee Name or ID.')
                except (ValueError, IndexError):
                    self.add_error('txt_emp_name_mrs', 'Invalid Employee Name format.')
            else:
                 self.add_error('txt_emp_name_mrs', 'Invalid Employee Name format (should include ID).')

        if cleaned_data.get('che_box_wo_no_mrs') and not cleaned_data.get('txt_wo_no_mrs'):
            self.add_error('txt_wo_no_mrs', 'Work Order No is required when checked.')
        # Add actual WO validation if a model method exists:
        # elif cleaned_data.get('che_box_wo_no_mrs') and not WorkOrder.objects.check_valid_wo_no(cleaned_data['txt_wo_no_mrs'], self.comp_id, self.fin_year_id):
        #     self.add_error('txt_wo_no_mrs', 'Work Order is not valid!')
        
        from_date = cleaned_data.get('txt_from_date_mrs')
        to_date = cleaned_data.get('txt_to_date_mrs')

        if from_date and not to_date:
            self.add_error('txt_to_date_mrs', 'To Date is required if From Date is entered.')
        if to_date and not from_date:
            self.add_error('txt_from_date_mrs', 'From Date is required if To Date is entered.')
        if from_date and to_date and from_date > to_date:
            self.add_error('txt_to_date_mrs', 'From date cannot be after To date.')

        return cleaned_data

class MRNSearchForm(forms.Form):
    """
    Form for the MRN Search tab.
    Similar to MRS Search Form.
    """
    ENTER_NO_MRN_OPTIONS = [
        ('3', 'All'), ('1', 'MRN No'), ('2', 'MRQN No')
    ]
    PO_RATE_OPTIONS = [
        ('1', 'Max'), ('2', 'Min'), ('3', 'Average'), ('4', 'Latest')
    ]

    ddl_enter_no_mrn = forms.ChoiceField(
        choices=ENTER_NO_MRN_OPTIONS,
        widget=forms.Select(attrs={'class': INPUT_CLASSES, 'x-model': 'ddlEnterNoMRN', 'x-on:change': 'handleMrnEnterNoChange()'}),
        label="Enter Nos."
    )
    txt_enter_no_mrn = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': 'ddlEnterNoMRN === "3"', 'x-model': 'txtEnterNoMRN'})
    )

    che_box_item_code_mrn = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxItemCodeMRN', 'x-on:change': 'if (!cheBoxItemCodeMRN) txtItemCodeMRN = \'\'', 'x-init': 'cheBoxItemCodeMRN = false'})
    )
    txt_item_code_mrn = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxItemCodeMRN', 'x-model': 'txtItemCodeMRN'})
    )

    che_box_employee_name_mrn = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxEmployeeNameMRN', 'x-on:change': 'if (!cheBoxEmployeeNameMRN) txtEmpNameMRN = \'\'', 'x-init': 'cheBoxEmployeeNameMRN = false'})
    )
    txt_emp_name_mrn = forms.CharField(
        max_length=255, required=False,
        widget=forms.TextInput(attrs={
            'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxEmployeeNameMRN', 'x-model': 'txtEmpNameMRN',
            'hx-get': '/reports/autocomplete/employee/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#employee-mrn-suggestions',
            'hx-indicator': '.htmx-indicator',
            'hx-params': 'txt_emp_name_mrn',
            'autocomplete': 'off'
        })
    )

    che_box_bg_group_mrn = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxBGGroupMRN', 'x-on:change': 'if (!cheBoxBGGroupMRN) ddlDeptMRN = \'\'', 'x-init': 'cheBoxBGGroupMRN = false'})
    )
    ddl_dept_mrn = forms.ChoiceField(
        choices=[], required=False,
        widget=forms.Select(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxBGGroupMRN', 'x-model': 'ddlDeptMRN'})
    )

    che_box_wo_no_mrn = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': CHECKBOX_CLASSES, 'x-model': 'cheBoxWONoMRN', 'x-on:change': 'if (!cheBoxWONoMRN) txtWONoMRN = \'\'', 'x-init': 'cheBoxWONoMRN = false'})
    )
    txt_wo_no_mrn = forms.CharField(
        max_length=100, required=False,
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': '!cheBoxWONoMRN', 'x-model': 'txtWONoMRN'})
    )

    rad_mrn_mrqn = forms.ChoiceField(
        choices=[('1', 'MRN'), ('2', 'MRQN')],
        widget=forms.RadioSelect(attrs={'class': RADIO_CLASSES}),
        initial='1',
        label="Enter Date",
        required=False
    )
    txt_from_date_mrn = forms.DateField(
        required=False, input_formats=['%d-%m-%Y'],
        widget=forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'})
    )
    txt_to_date_mrn = forms.DateField(
        required=False, input_formats=['%d-%m-%Y'],
        widget=forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'})
    )

    po_rate = forms.ChoiceField(
        choices=PO_RATE_OPTIONS,
        widget=forms.RadioSelect(attrs={'class': RADIO_CLASSES}),
        initial='1',
        label="PO Rate",
        required=False
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        self.comp_id = comp_id
        
        # Populate Business Group dropdown
        self.fields['ddl_dept_mrn'].choices = [('', 'Select')] + [(bg.id, str(bg)) for bg in BusinessGroup.objects.all()]

    def clean(self):
        cleaned_data = super().clean()

        ddl_enter_no_mrn_val = cleaned_data.get('ddl_enter_no_mrn')
        txt_enter_no_mrn_val = cleaned_data.get('txt_enter_no_mrn')
        if ddl_enter_no_mrn_val in ['1', '2'] and not txt_enter_no_mrn_val:
            self.add_error('txt_enter_no_mrn', 'Enter MRN or MRQN number.')

        if cleaned_data.get('che_box_item_code_mrn') and not cleaned_data.get('txt_item_code_mrn'):
            self.add_error('txt_item_code_mrn', 'Item Code is required when checked.')
        
        if cleaned_data.get('che_box_employee_name_mrn') and not cleaned_data.get('txt_emp_name_mrn'):
            self.add_error('txt_emp_name_mrn', 'Employee Name is required when checked.')
        elif cleaned_data.get('che_box_employee_name_mrn') and cleaned_data.get('txt_emp_name_mrn'):
            emp_text = cleaned_data['txt_emp_name_mrn']
            if ' [' in emp_text and emp_text.endswith(']'):
                try:
                    emp_id = int(emp_text.split(' [')[-1][:-1])
                    if not Employee.objects.filter(emp_id=emp_id, employee_name=emp_text.split(' [')[0], comp_id=self.comp_id).exists():
                         self.add_error('txt_emp_name_mrn', 'Invalid Employee Name or ID.')
                except (ValueError, IndexError):
                    self.add_error('txt_emp_name_mrn', 'Invalid Employee Name format.')
            else:
                 self.add_error('txt_emp_name_mrn', 'Invalid Employee Name format (should include ID).')

        if cleaned_data.get('che_box_wo_no_mrn') and not cleaned_data.get('txt_wo_no_mrn'):
            self.add_error('txt_wo_no_mrn', 'Work Order No is required when checked.')
        # Add actual WO validation if a model method exists:
        # elif cleaned_data.get('che_box_wo_no_mrn') and not WorkOrder.objects.check_valid_wo_no(cleaned_data['txt_wo_no_mrn'], self.comp_id, self.fin_year_id):
        #     self.add_error('txt_wo_no_mrn', 'Work Order is not valid!')
        
        from_date = cleaned_data.get('txt_from_date_mrn')
        to_date = cleaned_data.get('txt_to_date_mrn')

        if from_date and not to_date:
            self.add_error('txt_to_date_mrn', 'To Date is required if From Date is entered.')
        if to_date and not from_date:
            self.add_error('txt_from_date_mrn', 'From Date is required if To Date is entered.')
        if from_date and to_date and from_date > to_date:
            self.add_error('txt_to_date_mrn', 'From date cannot be after To date.')

        return cleaned_data


# Dummy ModelForm for Supplier to demonstrate CRUD pattern as per prompt requirements.
# This would be for managing lookup data, not the search itself.
class SupplierForm(forms.ModelForm):
    class Meta:
        model = Supplier
        fields = ['supplier_name', 'comp_id']
        widgets = {
            'supplier_name': forms.TextInput(attrs={'class': INPUT_CLASSES}),
            'comp_id': forms.NumberInput(attrs={'class': INPUT_CLASSES}),
        }
    
    def clean_supplier_name(self):
        supplier_name = self.cleaned_data['supplier_name']
        if len(supplier_name) < 3:
            raise forms.ValidationError("Supplier name must be at least 3 characters long.")
        return supplier_name
```

#### 4.3 Views (reports/views.py)

The main search page will be handled by a `TemplateView` with specific HTMX endpoints for dynamic form parts and autocomplete. The "CRUD" operations (for Supplier, as an example lookup table) will use standard Django CBVs.

```python
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.utils.http import urlencode

from .models import Supplier, Category, AccountHead, Employee, BusinessGroup
from .forms import GINSearchForm, MRSSearchForm, MRNSearchForm, SupplierForm

# --- Helper for Company ID (mimics Session["compid"]) ---
# In a real app, this would come from the authenticated user's session or profile.
# For this example, we'll hardcode or use a placeholder.
def get_company_id(request):
    # This is a placeholder. Replace with actual logic to get company_id from session/user.
    # For now, let's use a dummy ID
    return 1 # Example: request.session.get('compid', 1)

# --- Main Search Page View ---
class ReportSearchView(TemplateView):
    template_name = 'reports/search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        comp_id = get_company_id(self.request)
        
        # Initialize forms for each tab
        context['gin_form'] = GINSearchForm(comp_id=comp_id)
        context['mrs_form'] = MRSSearchForm(comp_id=comp_id)
        context['mrn_form'] = MRNSearchForm(comp_id=comp_id)
        
        return context

    def post(self, request, *args, **kwargs):
        # Determine which form was submitted based on the button name or HTMX header
        form_type = request.POST.get('form_type') # Hidden input to identify form

        comp_id = get_company_id(self.request)
        redirect_url = ""
        error_message = None

        if form_type == 'gin_search':
            form = GINSearchForm(request.POST, comp_id=comp_id)
            if form.is_valid():
                # Construct query parameters for redirection
                params = {
                    'type': form.cleaned_data.get('drp_option'),
                    'No': form.cleaned_data.get('txt_no'),
                    'RAd': form.cleaned_data.get('report_for'),
                    'Code': form.cleaned_data.get('txt_itemcode'),
                    'WONo': form.cleaned_data.get('txt_wono'),
                    'SupId': form.cleaned_data.get('txt_supplier').split(' [')[-1][:-1] if form.cleaned_data.get('txt_supplier') else '', # Extract ID
                    'accval': form.cleaned_data.get('dropdown_acc_head'),
                    'FDate': form.cleaned_data.get('text_from_date').strftime('%d-%m-%Y') if form.cleaned_data.get('text_from_date') else '',
                    'TDate': form.cleaned_data.get('text_to_date').strftime('%d-%m-%Y') if form.cleaned_data.get('text_to_date') else '',
                }
                redirect_url = reverse_lazy('reports:gin_search_details') + '?' + urlencode({k: v for k, v in params.items() if v is not None})
            else:
                error_message = self._get_form_errors(form)
        
        elif form_type == 'mrs_search':
            form = MRSSearchForm(request.POST, comp_id=comp_id)
            if form.is_valid():
                params = {
                    'MRSType': form.cleaned_data.get('ddl_enter_no_mrs'),
                    'MRSno': form.cleaned_data.get('txt_enter_no_mrs'),
                    'ICode': form.cleaned_data.get('txt_item_code_mrs'),
                    'EmpidMRS': form.cleaned_data.get('txt_emp_name_mrs').split(' [')[-1][:-1] if form.cleaned_data.get('txt_emp_name_mrs') else '',
                    'BGGroupMRS': form.cleaned_data.get('ddl_dept_mrs'),
                    'WONoMRS': form.cleaned_data.get('txt_wo_no_mrs'),
                    'FDateMRS': form.cleaned_data.get('txt_from_date_mrs').strftime('%d-%m-%Y') if form.cleaned_data.get('txt_from_date_mrs') else '',
                    'TDateMRS': form.cleaned_data.get('txt_to_date_mrs').strftime('%d-%m-%Y') if form.cleaned_data.get('txt_to_date_mrs') else '',
                    'Rbtn': form.cleaned_data.get('rad_mrs_min'),
                    'ICategory': form.cleaned_data.get('drp_category_mrs'),
                }
                redirect_url = reverse_lazy('reports:mrs_search_details') + '?' + urlencode({k: v for k, v in params.items() if v is not None})
            else:
                error_message = self._get_form_errors(form)

        elif form_type == 'mrn_search':
            form = MRNSearchForm(request.POST, comp_id=comp_id)
            if form.is_valid():
                params = {
                    'MRNType': form.cleaned_data.get('ddl_enter_no_mrn'),
                    'MRNno': form.cleaned_data.get('txt_enter_no_mrn'),
                    'ICode': form.cleaned_data.get('txt_item_code_mrn'),
                    'EmpidMRN': form.cleaned_data.get('txt_emp_name_mrn').split(' [')[-1][:-1] if form.cleaned_data.get('txt_emp_name_mrn') else '',
                    'BGGroupMRN': form.cleaned_data.get('ddl_dept_mrn'),
                    'WONoMRN': form.cleaned_data.get('txt_wo_no_mrn'),
                    'FDateMRN': form.cleaned_data.get('txt_from_date_mrn').strftime('%d-%m-%Y') if form.cleaned_data.get('txt_from_date_mrn') else '',
                    'TDateMRN': form.cleaned_data.get('txt_to_date_mrn').strftime('%d-%m-%Y') if form.cleaned_data.get('txt_to_date_mrn') else '',
                    'Rbtn': form.cleaned_data.get('rad_mrn_mrqn'),
                    'GetPORate': form.cleaned_data.get('po_rate'),
                }
                redirect_url = reverse_lazy('reports:mrn_search_details') + '?' + urlencode({k: v for k, v in params.items() if v is not None})
            else:
                error_message = self._get_form_errors(form)

        if error_message:
            # If form is invalid, return a response that can be handled by HTMX
            # We'll re-render the specific tab's form with errors.
            response_context = self.get_context_data()
            if form_type == 'gin_search':
                response_context['gin_form'] = form
            elif form_type == 'mrs_search':
                response_context['mrs_form'] = form
            elif form_type == 'mrn_search':
                response_context['mrn_form'] = form
            
            # Send an alert to the client if form is invalid, similar to ASP.NET ClientScript
            # HTMX will trigger an alert based on this header.
            response = render(request, self.template_name, response_context)
            response['HX-Trigger'] = '{"showAlert": {"message": "%s"}}' % error_message.replace('"', '\\"').replace('\n', '\\n')
            return response
        else:
            # If valid, send HX-Redirect header
            response = HttpResponse(status=204) # No content, just header
            response['HX-Redirect'] = redirect_url
            return response

    def _get_form_errors(self, form):
        # Concatenate all form errors into a single string for alert
        errors = []
        for field, field_errors in form.errors.items():
            field_name = form.fields[field].label if field in form.fields else field
            for error in field_errors:
                errors.append(f"{field_name}: {error}")
        return "\n".join(errors)


# --- HTMX Endpoints for Autocomplete and Dynamic Form Parts ---

class AutocompleteSupplierView(TemplateView):
    # This view returns partial HTML for supplier suggestions
    # Or could return JSON for Alpine.js to handle suggestions list
    # Returning JSON is cleaner for autocomplete.
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('txt_supplier', '')
        comp_id = get_company_id(request)
        suggestions = Supplier.search_by_name(prefix_text, comp_id)
        
        # Format as list of strings for display, e.g., ["Supplier Name [ID]", ...]
        # This matches ASP.NET's output format.
        formatted_suggestions = [f"{name} [{id}]" for name, id in suggestions[:10]] # Limit to 10 suggestions

        return JsonResponse(formatted_suggestions, safe=False)

class AutocompleteEmployeeView(TemplateView):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('txt_emp_name_mrs', '') or request.GET.get('txt_emp_name_mrn', '')
        comp_id = get_company_id(request)
        suggestions = Employee.search_by_name(prefix_text, comp_id)

        formatted_suggestions = [f"{name} [{id}]" for name, id in suggestions[:10]] # Limit to 10 suggestions

        return JsonResponse(formatted_suggestions, safe=False)

class GINAccountHeadDropdownView(TemplateView):
    """
    HTMX endpoint to re-render the Account Head dropdown based on radio button selection.
    """
    template_name = 'reports/partials/_acc_head_dropdown.html'

    def post(self, request, *args, **kwargs):
        # Check the POST data to determine the selected category
        category = 'Labour'
        if request.POST.get('rbtn_with_material') == 'on': # 'on' for checked radio input
            category = 'With Material'
        
        acc_heads = AccountHead.get_by_category(category)
        return render(request, self.template_name, {'dropdown_acc_head_choices': acc_heads, 'input_classes': INPUT_CLASSES})

class GINReportForRadiosView(TemplateView):
    """
    HTMX endpoint to re-render the 'Report For' radio buttons based on Drpoption selection.
    """
    template_name = 'reports/partials/_gin_report_for_radios.html'

    def post(self, request, *args, **kwargs):
        drp_option_val = request.POST.get('drp_option')
        initial_report_for = '0' # Default to PO

        if drp_option_val == '1':
            initial_report_for = '1' # GIN No -> GIN
        elif drp_option_val == '2':
            initial_report_for = '2' # GRR No -> GRR
        elif drp_option_val == '3':
            initial_report_for = '3' # GQN No -> GQN
        elif drp_option_val == '4':
            initial_report_for = '4' # GSN No -> GSN
        # If 'All' or 'PO No', initial_report_for remains '0' (PO)

        # Re-initialize the report_for field from the GIN form to get its widget
        # We don't need a full form here, just the choices and initial value.
        report_for_field = GINSearchForm.base_fields['report_for']
        report_for_field.initial = initial_report_for
        report_for_widget = report_for_field.widget

        # Manually render the radio buttons
        context = {
            'report_for_name': 'report_for', # Name of the input field
            'radio_choices': report_for_field.choices,
            'initial_value': initial_report_for,
            'radio_classes': RADIO_CLASSES,
        }
        return render(request, self.template_name, context)

# --- Placeholder Views for Search Results Pages (as per ASP.NET redirection) ---
# These would be separate views and templates, handling the actual data display.
class GinSearchDetailsView(TemplateView):
    template_name = 'reports/gin_search_details.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Here, retrieve data based on query parameters (self.request.GET)
        # and prepare it for display, potentially using DataTables.
        context['search_params'] = self.request.GET
        return context

class MrsSearchDetailsView(TemplateView):
    template_name = 'reports/mrs_search_details.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_params'] = self.request.GET
        return context

class MrnSearchDetailsView(TemplateView):
    template_name = 'reports/mrn_search_details.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_params'] = self.request.GET
        return context


# --- Generic CRUD Views (for a lookup model like Supplier, as per prompt template) ---
# These would typically be in a separate app or module for master data management.

class SupplierListView(ListView):
    model = Supplier
    template_name = 'reports/supplier/list.html'
    context_object_name = 'suppliers'

class SupplierCreateView(CreateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'reports/supplier/form.html'
    success_url = reverse_lazy('reports:supplier_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierUpdateView(UpdateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'reports/supplier/form.html'
    success_url = reverse_lazy('reports:supplier_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierDeleteView(DeleteView):
    model = Supplier
    template_name = 'reports/supplier/confirm_delete.html'
    success_url = reverse_lazy('reports:supplier_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierTablePartialView(ListView):
    """
    Returns the table content for HTMX partial refresh.
    """
    model = Supplier
    template_name = 'reports/supplier/_supplier_table.html'
    context_object_name = 'suppliers'
```

#### 4.4 Templates (reports/templates/reports/)

These templates define the UI for the search forms and the placeholder CRUD operations.

**`search.html` (Main Page for all tabs)**
This template will host the three search forms within an Alpine.js powered tab structure.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Inventory Search</h2>

    <div x-data="{ activeTab: 'gin_search' }" class="bg-white p-6 rounded-lg shadow-lg">
        <!-- Tab Headers -->
        <div class="flex border-b border-gray-200 mb-6">
            <button @click="activeTab = 'gin_search'" :class="{ 'border-b-2 border-indigo-500 text-indigo-600': activeTab === 'gin_search', 'text-gray-600': activeTab !== 'gin_search' }"
                    class="py-2 px-4 text-sm font-medium focus:outline-none hover:text-indigo-600 hover:border-indigo-500">
                GIN Search
            </button>
            <button @click="activeTab = 'mrs_search'" :class="{ 'border-b-2 border-indigo-500 text-indigo-600': activeTab === 'mrs_search', 'text-gray-600': activeTab !== 'mrs_search' }"
                    class="py-2 px-4 text-sm font-medium focus:outline-none hover:text-indigo-600 hover:border-indigo-500">
                MRS Search
            </button>
            <button @click="activeTab = 'mrn_search'" :class="{ 'border-b-2 border-indigo-500 text-indigo-600': activeTab === 'mrn_search', 'text-gray-600': activeTab !== 'mrn_search' }"
                    class="py-2 px-4 text-sm font-medium focus:outline-none hover:text-indigo-600 hover:border-indigo-500">
                MRN Search
            </button>
        </div>

        <!-- Tab Content -->
        <div x-show="activeTab === 'gin_search'" x-cloak>
            <h3 class="text-xl font-semibold mb-4">GIN Search</h3>
            {% include 'reports/partials/_gin_search_form.html' with form=gin_form %}
        </div>

        <div x-show="activeTab === 'mrs_search'" x-cloak>
            <h3 class="text-xl font-semibold mb-4">MRS Search</h3>
            {% include 'reports/partials/_mrs_search_form.html' with form=mrs_form %}
        </div>

        <div x-show="activeTab === 'mrn_search'" x-cloak>
            <h3 class="text-xl font-semibold mb-4">MRN Search</h3>
            {% include 'reports/partials/_mrn_search_form.html' with form=mrn_form %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('ginSearchData', () => ({
            ckItemChecked: false,
            txtItemcode: '',
            ckWonoChecked: false,
            txtWono: '',
            ckSupplierChecked: false,
            txtSupplier: '',
            ckAccHeadChecked: false,
            rbtnLabour: true, // Default checked
            rbtnWithMaterial: false,
            
            init() {
                // Initialize states based on existing form data if editing
                // For a new form, defaults apply
            },
        }));

        Alpine.data('mrsSearchData', () => ({
            ddlEnterNoMRS: '3', // Default 'All'
            txtEnterNoMRS: '',
            cheBoxItemCatMRS: false,
            drpCategoryMRS: '',
            cheBoxItemCodeMRS: false,
            txtItemCodeMRS: '',
            cheBoxEmployeeNameMRS: false,
            txtEmpNameMRS: '',
            cheBoxBGGroupMRS: false,
            ddlDeptMRS: '',
            cheBoxWONoMRS: false,
            txtWONoMRS: '',

            init() {
                // Initialize based on form
                this.handleMrsEnterNoChange(); // Apply initial state
            },
            handleMrsEnterNoChange() {
                if (this.ddlEnterNoMRS === '3') {
                    this.txtEnterNoMRS = '';
                    this.cheBoxItemCatMRS = false;
                    this.drpCategoryMRS = '';
                    this.cheBoxItemCodeMRS = false;
                    this.txtItemCodeMRS = '';
                    this.cheBoxEmployeeNameMRS = false;
                    this.txtEmpNameMRS = '';
                    this.cheBoxBGGroupMRS = false;
                    this.ddlDeptMRS = '';
                    this.cheBoxWONoMRS = false;
                    this.txtWONoMRS = '';
                }
            }
        }));

        Alpine.data('mrnSearchData', () => ({
            ddlEnterNoMRN: '3', // Default 'All'
            txtEnterNoMRN: '',
            cheBoxItemCodeMRN: false,
            txtItemCodeMRN: '',
            cheBoxEmployeeNameMRN: false,
            txtEmpNameMRN: '',
            cheBoxBGGroupMRN: false,
            ddlDeptMRN: '',
            cheBoxWONoMRN: false,
            txtWONoMRN: '',

            init() {
                this.handleMrnEnterNoChange(); // Apply initial state
            },
            handleMrnEnterNoChange() {
                if (this.ddlEnterNoMRN === '3') {
                    this.txtEnterNoMRN = '';
                    this.cheBoxItemCodeMRN = false;
                    this.txtItemCodeMRN = '';
                    this.cheBoxEmployeeNameMRN = false;
                    this.txtEmpNameMRN = '';
                    this.cheBoxBGGroupMRN = false;
                    this.ddlDeptMRN = '';
                    this.cheBoxWONoMRN = false;
                    this.txtWONoMRN = '';
                }
            }
        }));
    });

    // Global event listener for custom 'showAlert' event from HX-Trigger
    document.body.addEventListener('showAlert', function(event) {
        alert(event.detail.message);
    });
</script>
{% endblock %}
```

**`partials/_gin_search_form.html`**

```html
<form hx-post="{% url 'reports:report_search' %}" hx-swap="outerHTML" hx-target="#gin_form_container" x-data="ginSearchData">
    {% csrf_token %}
    <input type="hidden" name="form_type" value="gin_search">
    <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <label for="{{ form.drp_option.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.drp_option.label }}
            </label>
            <div>
                {{ form.drp_option }}
                {{ form.txt_no }}
                {% if form.drp_option.errors %}<p class="text-red-500 text-xs mt-1">{{ form.drp_option.errors }}</p>{% endif %}
                {% if form.txt_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_no.errors }}</p>{% endif %}
            </div>
            <span class="font-medium text-gray-700">Report For:</span>
            <div id="gin-report-for-radios">
                {% include 'reports/partials/_gin_report_for_radios.html' with report_for_name='report_for' radio_choices=form.report_for.choices initial_value=form.report_for.initial radio_classes=RADIO_CLASSES %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.ck_item }}
                <label for="{{ form.ck_item.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Item Code</label>
            </div>
            <div>
                {{ form.txt_itemcode }}
                {% if form.txt_itemcode.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_itemcode.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.ck_wono }}
                <label for="{{ form.ck_wono.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">WO No</label>
            </div>
            <div>
                {{ form.txt_wono }}
                {% if form.txt_wono.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_wono.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.ck_supplier }}
                <label for="{{ form.ck_supplier.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Supplier Name</label>
            </div>
            <div>
                {{ form.txt_supplier }}
                <div id="supplier-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 absolute w-96 max-h-48 overflow-y-auto">
                    <!-- Autocomplete suggestions will be loaded here via HTMX -->
                </div>
                {% if form.txt_supplier.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_supplier.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.ck_acc_head }}
                <label for="{{ form.ck_acc_head.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Account Head</label>
            </div>
            <div>
                <div class="flex items-center space-x-4 mb-2">
                    <label class="flex items-center">
                        <input type="radio" name="{{ form.rbtn_labour.name }}" value="on" class="{{ RADIO_CLASSES }}" x-model="rbtnLabour" x-bind:disabled="!ckAccHeadChecked" hx-post="{% url 'reports:gin_acchead_dropdown' %}" hx-target="#gin-acchead-dropdown" hx-swap="innerHTML" hx-indicator=".htmx-indicator">
                        <span class="ml-2 text-sm text-gray-700">Labour</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="{{ form.rbtn_with_material.name }}" value="on" class="{{ RADIO_CLASSES }}" x-model="rbtnWithMaterial" x-bind:disabled="!ckAccHeadChecked" hx-post="{% url 'reports:gin_acchead_dropdown' %}" hx-target="#gin-acchead-dropdown" hx-swap="innerHTML" hx-indicator=".htmx-indicator">
                        <span class="ml-2 text-sm text-gray-700">With Material</span>
                    </label>
                </div>
                <div id="gin-acchead-dropdown">
                    {% include 'reports/partials/_acc_head_dropdown.html' with dropdown_acc_head_choices=form.dropdown_acc_head.field.choices input_classes=INPUT_CLASSES %}
                </div>
                {% if form.dropdown_acc_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.dropdown_acc_head.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <label for="{{ form.text_from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Enter Date</label>
            <div class="flex items-center space-x-2">
                <span>From:</span>
                {{ form.text_from_date }}
                <span>- To:</span>
                {{ form.text_to_date }}
                {% if form.text_from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.text_from_date.errors }}</p>{% endif %}
                {% if form.text_to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.text_to_date.errors }}</p>{% endif %}
            </div>
        </div>
    </div>

    <div class="mt-6 flex justify-end">
        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Search
        </button>
    </div>
</form>
```

**`partials/_mrs_search_form.html`**

```html
<form hx-post="{% url 'reports:report_search' %}" hx-swap="outerHTML" hx-target="#mrs_form_container" x-data="mrsSearchData">
    {% csrf_token %}
    <input type="hidden" name="form_type" value="mrs_search">
    <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <label for="{{ form.ddl_enter_no_mrs.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.ddl_enter_no_mrs.label }}
            </label>
            <div>
                {{ form.ddl_enter_no_mrs }}
                {{ form.txt_enter_no_mrs }}
                {% if form.ddl_enter_no_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ddl_enter_no_mrs.errors }}</p>{% endif %}
                {% if form.txt_enter_no_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_enter_no_mrs.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_item_cat_mrs }}
                <label for="{{ form.che_box_item_cat_mrs.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Item Category</label>
            </div>
            <div>
                {{ form.drp_category_mrs }}
                {% if form.drp_category_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.drp_category_mrs.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_item_code_mrs }}
                <label for="{{ form.che_box_item_code_mrs.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Item Code</label>
            </div>
            <div>
                {{ form.txt_item_code_mrs }}
                {% if form.txt_item_code_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_item_code_mrs.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_employee_name_mrs }}
                <label for="{{ form.che_box_employee_name_mrs.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Employee Name</label>
            </div>
            <div>
                {{ form.txt_emp_name_mrs }}
                <div id="employee-mrs-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 absolute w-96 max-h-48 overflow-y-auto"></div>
                {% if form.txt_emp_name_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_emp_name_mrs.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_bg_group_mrs }}
                <label for="{{ form.che_box_bg_group_mrs.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">BG Group</label>
            </div>
            <div>
                {{ form.ddl_dept_mrs }}
                {% if form.ddl_dept_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ddl_dept_mrs.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_wo_no_mrs }}
                <label for="{{ form.che_box_wo_no_mrs.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Work Order No</label>
            </div>
            <div>
                {{ form.txt_wo_no_mrs }}
                {% if form.txt_wo_no_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_wo_no_mrs.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <label for="{{ form.rad_mrs_min.id_for_label }}" class="block text-sm font-medium text-gray-700">Select Date</label>
            <div class="flex items-center space-x-2">
                {{ form.rad_mrs_min }}
                <span>From:</span>
                {{ form.txt_from_date_mrs }}
                <span>To:</span>
                {{ form.txt_to_date_mrs }}
                {% if form.txt_from_date_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_from_date_mrs.errors }}</p>{% endif %}
                {% if form.txt_to_date_mrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_to_date_mrs.errors }}</p>{% endif %}
            </div>
        </div>
    </div>

    <div class="mt-6 flex justify-end">
        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Search
        </button>
    </div>
</form>
```

**`partials/_mrn_search_form.html`**

```html
<form hx-post="{% url 'reports:report_search' %}" hx-swap="outerHTML" hx-target="#mrn_form_container" x-data="mrnSearchData">
    {% csrf_token %}
    <input type="hidden" name="form_type" value="mrn_search">
    <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <label for="{{ form.ddl_enter_no_mrn.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.ddl_enter_no_mrn.label }}
            </label>
            <div>
                {{ form.ddl_enter_no_mrn }}
                {{ form.txt_enter_no_mrn }}
                {% if form.ddl_enter_no_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ddl_enter_no_mrn.errors }}</p>{% endif %}
                {% if form.txt_enter_no_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_enter_no_mrn.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_item_code_mrn }}
                <label for="{{ form.che_box_item_code_mrn.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Item Code</label>
            </div>
            <div>
                {{ form.txt_item_code_mrn }}
                {% if form.txt_item_code_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_item_code_mrn.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_employee_name_mrn }}
                <label for="{{ form.che_box_employee_name_mrn.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Employee Name</label>
            </div>
            <div>
                {{ form.txt_emp_name_mrn }}
                <div id="employee-mrn-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 absolute w-96 max-h-48 overflow-y-auto"></div>
                {% if form.txt_emp_name_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_emp_name_mrn.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_bg_group_mrn }}
                <label for="{{ form.che_box_bg_group_mrn.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">BG Group</label>
            </div>
            <div>
                {{ form.ddl_dept_mrn }}
                {% if form.ddl_dept_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ddl_dept_mrn.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div class="flex items-center">
                {{ form.che_box_wo_no_mrn }}
                <label for="{{ form.che_box_wo_no_mrn.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Work Order No</label>
            </div>
            <div>
                {{ form.txt_wo_no_mrn }}
                {% if form.txt_wo_no_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_wo_no_mrn.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <label for="{{ form.rad_mrn_mrqn.id_for_label }}" class="block text-sm font-medium text-gray-700">Enter Date</label>
            <div class="flex items-center space-x-2">
                {{ form.rad_mrn_mrqn }}
                <span>From:</span>
                {{ form.txt_from_date_mrn }}
                <span>To:</span>
                {{ form.txt_to_date_mrn }}
                {% if form.txt_from_date_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_from_date_mrn.errors }}</p>{% endif %}
                {% if form.txt_to_date_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.txt_to_date_mrn.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <label for="{{ form.po_rate.id_for_label }}" class="block text-sm font-medium text-gray-700">PO Rate</label>
            <div>
                {{ form.po_rate }}
                {% if form.po_rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.po_rate.errors }}</p>{% endif %}
            </div>
        </div>
    </div>

    <div class="mt-6 flex justify-end">
        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Search
        </button>
    </div>
</form>
```

**`partials/_acc_head_dropdown.html` (for HTMX partial refresh)**

```html
<select name="dropdown_acc_head" id="id_dropdown_acc_head" class="{{ input_classes }}" x-bind:disabled="!ckAccHeadChecked">
    {% for value, label in dropdown_acc_head_choices %}
        <option value="{{ value }}">{{ label }}</option>
    {% endfor %}
</select>
```

**`partials/_gin_report_for_radios.html` (for HTMX partial refresh)**

```html
{% for value, label in radio_choices %}
    <label class="flex items-center mr-4">
        <input type="radio" name="{{ report_for_name }}" value="{{ value }}" 
               class="{{ radio_classes }}" {% if value == initial_value %}checked{% endif %}>
        <span class="ml-2 text-sm text-gray-700">{{ label }}</span>
    </label>
{% endfor %}
```

**`supplier/list.html` (Example CRUD List for Supplier)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Suppliers</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'reports:supplier_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Supplier
        </button>
    </div>

    <div id="supplierTable-container"
         hx-trigger="load, refreshSupplierList from:body"
         hx-get="{% url 'reports:supplier_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**`supplier/_supplier_table.html` (Partial for DataTables)**

```html
<table id="supplierTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in suppliers %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.comp_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'reports:supplier_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'reports:supplier_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#supplierTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**`supplier/form.html` (Partial for CRUD Form)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Supplier</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`supplier/confirm_delete.html` (Partial for Delete Confirmation)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-6">Are you sure you want to delete supplier "{{ object.supplier_name }}"?</p>
    <form hx-post="{% url 'reports:supplier_delete' object.pk %}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (reports/urls.py)

```python
from django.urls import path
from .views import (
    ReportSearchView,
    AutocompleteSupplierView,
    AutocompleteEmployeeView,
    GINAccountHeadDropdownView,
    GINReportForRadiosView,
    GinSearchDetailsView,
    MrsSearchDetailsView,
    MrnSearchDetailsView,
    SupplierListView,
    SupplierCreateView,
    SupplierUpdateView,
    SupplierDeleteView,
    SupplierTablePartialView,
)

app_name = 'reports' # Important for namespacing URLs

urlpatterns = [
    # Main Search Page
    path('search/', ReportSearchView.as_view(), name='report_search'),

    # HTMX Endpoints for Search Forms
    path('autocomplete/supplier/', AutocompleteSupplierView.as_view(), name='autocomplete_supplier'),
    path('autocomplete/employee/', AutocompleteEmployeeView.as_view(), name='autocomplete_employee'),
    path('gin-acchead-changed/', GINAccountHeadDropdownView.as_view(), name='gin_acchead_dropdown'),
    path('gin-options-changed/', GINReportForRadiosView.as_view(), name='gin_options_changed'),

    # Placeholder URLs for Search Results Pages (where actual reports would be displayed)
    path('gin-search-details/', GinSearchDetailsView.as_view(), name='gin_search_details'),
    path('mrs-search-details/', MrsSearchDetailsView.as_view(), name='mrs_search_details'),
    path('mrn-search-details/', MrnSearchDetailsView.as_view(), name='mrn_search_details'),

    # Example CRUD URLs for Supplier (as per prompt for demonstration)
    path('suppliers/', SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/add/', SupplierCreateView.as_view(), name='supplier_add'),
    path('suppliers/edit/<int:pk>/', SupplierUpdateView.as_view(), name='supplier_edit'),
    path('suppliers/delete/<int:pk>/', SupplierDeleteView.as_view(), name='supplier_delete'),
    path('suppliers/table/', SupplierTablePartialView.as_view(), name='supplier_table'), # For HTMX DataTables
]
```

#### 4.6 Tests (reports/tests.py)

Comprehensive tests for models, forms, and views to ensure functionality and adherence to logic.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import JsonResponse
import json
from datetime import date
from unittest.mock import patch

from .models import Supplier, Category, AccountHead, Employee, BusinessGroup
from .forms import GINSearchForm, MRSSearchForm, MRNSearchForm, SupplierForm
from .views import get_company_id

# Mock get_company_id for consistent testing
@patch('reports.views.get_company_id', return_value=1)
class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_get_company_id):
        # Create test data for lookup tables
        cls.company_id = 1
        Supplier.objects.create(supplier_id=101, supplier_name='Test Supplier A', comp_id=cls.company_id)
        Supplier.objects.create(supplier_id=102, supplier_name='Another Supplier', comp_id=cls.company_id)
        Supplier.objects.create(supplier_id=103, supplier_name='Test Supplier B', comp_id=2) # Different company

        Category.objects.create(c_id=201, symbol='CAT-A', c_name='Category Alpha', comp_id=cls.company_id)
        Category.objects.create(c_id=202, symbol='CAT-B', c_name='Category Beta', comp_id=cls.company_id)

        AccountHead.objects.create(id=301, symbol='LAB-01', description='Direct Labour', category='Labour')
        AccountHead.objects.create(id=302, symbol='MAT-01', description='Raw Materials', category='With Material')
        AccountHead.objects.create(id=303, symbol='LAB-02', description='Indirect Labour', category='Labour')

        Employee.objects.create(emp_id=401, employee_name='John Doe', comp_id=cls.company_id)
        Employee.objects.create(emp_id=402, employee_name='Jane Smith', comp_id=cls.company_id)
        Employee.objects.create(emp_id=403, employee_name='Jack Black', comp_id=2)

        BusinessGroup.objects.create(id=501, symbol='BG-A')
        BusinessGroup.objects.create(id=502, symbol='BG-B')

class SupplierModelTest(ModelTest):
    def test_supplier_creation(self, mock_get_company_id):
        supplier = Supplier.objects.get(supplier_id=101)
        self.assertEqual(supplier.supplier_name, 'Test Supplier A')
        self.assertEqual(supplier.comp_id, self.company_id)

    def test_supplier_str(self, mock_get_company_id):
        supplier = Supplier.objects.get(supplier_id=101)
        self.assertEqual(str(supplier), 'Test Supplier A [101]')

    def test_search_by_name(self, mock_get_company_id):
        results = Supplier.search_by_name('Test', self.company_id)
        self.assertIn(('Test Supplier A', 101), results)
        self.assertNotIn(('Test Supplier B', 103), results) # Different company_id
        self.assertNotIn(('Another Supplier', 102), results)

class AccountHeadModelTest(ModelTest):
    def test_account_head_str(self, mock_get_company_id):
        acc_head = AccountHead.objects.get(id=301)
        self.assertEqual(str(acc_head), '[LAB-01] Direct Labour')

    def test_get_by_category(self, mock_get_company_id):
        labour_heads = AccountHead.get_by_category('Labour')
        self.assertEqual(len(labour_heads), 2)
        self.assertTrue(all(ah.category == 'Labour' for ah in labour_heads))
        self.assertIn(AccountHead.objects.get(id=301), labour_heads)
        self.assertIn(AccountHead.objects.get(id=303), labour_heads)

class EmployeeModelTest(ModelTest):
    def test_employee_str(self, mock_get_company_id):
        employee = Employee.objects.get(emp_id=401)
        self.assertEqual(str(employee), 'John Doe [401]')

    def test_search_by_name(self, mock_get_company_id):
        results = Employee.search_by_name('John', self.company_id)
        self.assertIn(('John Doe', 401), results)
        self.assertNotIn(('Jack Black', 403), results) # Different company_id

    def test_get_employee_by_code(self, mock_get_company_id):
        employee = Employee.get_employee_by_code(401, self.company_id)
        self.assertIsNotNone(employee)
        self.assertEqual(employee.employee_name, 'John Doe')

        non_existent_employee = Employee.get_employee_by_code(999, self.company_id)
        self.assertIsNone(non_existent_employee)

        wrong_company_employee = Employee.get_employee_by_code(403, self.company_id)
        self.assertIsNone(wrong_company_employee) # Employee 403 is in company 2

# Forms Test
@patch('reports.views.get_company_id', return_value=1)
class FormTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_get_company_id):
        # Create test data needed for form choices
        cls.company_id = 1
        Supplier.objects.create(supplier_id=101, supplier_name='Test Supplier A', comp_id=cls.company_id)
        Category.objects.create(c_id=201, symbol='CAT-A', c_name='Category Alpha', comp_id=cls.company_id)
        AccountHead.objects.create(id=301, symbol='LAB-01', description='Direct Labour', category='Labour')
        AccountHead.objects.create(id=302, symbol='MAT-01', description='Raw Materials', category='With Material')
        Employee.objects.create(emp_id=401, employee_name='John Doe', comp_id=cls.company_id)
        BusinessGroup.objects.create(id=501, symbol='BG-A')


    def test_gin_search_form_valid(self, mock_get_company_id):
        form_data = {
            'drp_option': '1', # GIN No
            'txt_no': 'G123',
            'report_for': '1',
            'ck_item': False,
            'txt_itemcode': '',
            'ck_wono': False,
            'txt_wono': '',
            'ck_supplier': False,
            'txt_supplier': '',
            'ck_acc_head': True,
            'rbtn_labour': 'on',
            'rbtn_with_material': '',
            'dropdown_acc_head': '301', # Direct Labour
            'text_from_date': '01-01-2023',
            'text_to_date': '31-01-2023',
        }
        form = GINSearchForm(data=form_data, comp_id=self.company_id)
        self.assertTrue(form.is_valid(), form.errors.as_json())

    def test_gin_search_form_invalid_date_range(self, mock_get_company_id):
        form_data = {
            'drp_option': '0', 'txt_no': '', 'report_for': '0', 'ck_item': False, 'txt_itemcode': '',
            'ck_wono': False, 'txt_wono': '', 'ck_supplier': False, 'txt_supplier': '', 'ck_acc_head': False,
            'rbtn_labour': '', 'rbtn_with_material': '', 'dropdown_acc_head': '',
            'text_from_date': '31-01-2023',
            'text_to_date': '01-01-2023',
        }
        form = GINSearchForm(data=form_data, comp_id=self.company_id)
        self.assertFalse(form.is_valid())
        self.assertIn('From date cannot be after To date.', form.errors['text_to_date'])

    def test_gin_search_form_conditional_fields(self, mock_get_company_id):
        # Item code checked but empty
        form_data = {
            'drp_option': '0', 'txt_no': '', 'report_for': '0',
            'ck_item': True, 'txt_itemcode': '', # Invalid
            'ck_wono': False, 'txt_wono': '', 'ck_supplier': False, 'txt_supplier': '', 'ck_acc_head': False,
            'rbtn_labour': '', 'rbtn_with_material': '', 'dropdown_acc_head': '',
            'text_from_date': '', 'text_to_date': '',
        }
        form = GINSearchForm(data=form_data, comp_id=self.company_id)
        self.assertFalse(form.is_valid())
        self.assertIn('Item Code is required when checked.', form.errors['txt_itemcode'])

    def test_gin_search_form_supplier_validation(self, mock_get_company_id):
        # Valid supplier
        form_data = {
            'drp_option': '0', 'txt_no': '', 'report_for': '0',
            'ck_item': False, 'txt_itemcode': '', 'ck_wono': False, 'txt_wono': '',
            'ck_supplier': True, 'txt_supplier': 'Test Supplier A [101]', # Valid
            'ck_acc_head': False, 'rbtn_labour': '', 'rbtn_with_material': '', 'dropdown_acc_head': '',
            'text_from_date': '', 'text_to_date': '',
        }
        form = GINSearchForm(data=form_data, comp_id=self.company_id)
        self.assertTrue(form.is_valid(), form.errors.as_json())

        # Invalid supplier (wrong ID)
        form_data['txt_supplier'] = 'Test Supplier A [999]'
        form = GINSearchForm(data=form_data, comp_id=self.company_id)
        self.assertFalse(form.is_valid())
        self.assertIn('Invalid Supplier Name or ID.', form.errors['txt_supplier'])

    def test_mrs_search_form_valid(self, mock_get_company_id):
        form_data = {
            'ddl_enter_no_mrs': '1', # MRS No
            'txt_enter_no_mrs': 'MRS-001',
            'che_box_item_cat_mrs': True,
            'drp_category_mrs': '201', # Category Alpha
            'che_box_item_code_mrs': False,
            'txt_item_code_mrs': '',
            'che_box_employee_name_mrs': True,
            'txt_emp_name_mrs': 'John Doe [401]',
            'che_box_bg_group_mrs': True,
            'ddl_dept_mrs': '501', # BG-A
            'che_box_wo_no_mrs': False,
            'txt_wo_no_mrs': '',
            'rad_mrs_min': '1',
            'txt_from_date_mrs': '',
            'txt_to_date_mrs': '',
        }
        form = MRSSearchForm(data=form_data, comp_id=self.company_id)
        self.assertTrue(form.is_valid(), form.errors.as_json())

    def test_mrn_search_form_valid(self, mock_get_company_id):
        form_data = {
            'ddl_enter_no_mrn': '3', # All
            'txt_enter_no_mrn': '',
            'che_box_item_code_mrn': False,
            'txt_item_code_mrn': '',
            'che_box_employee_name_mrn': False,
            'txt_emp_name_mrn': '',
            'che_box_bg_group_mrn': False,
            'ddl_dept_mrn': '',
            'che_box_wo_no_mrn': False,
            'txt_wo_no_mrn': '',
            'rad_mrn_mrqn': '1',
            'txt_from_date_mrn': '01-02-2023',
            'txt_to_date_mrn': '28-02-2023',
            'po_rate': '1', # Max
        }
        form = MRNSearchForm(data=form_data, comp_id=self.company_id)
        self.assertTrue(form.is_valid(), form.errors.as_json())

class ReportViewsTest(TestCase):
    client = Client()

    @classmethod
    @patch('reports.views.get_company_id', return_value=1)
    def setUpTestData(cls, mock_get_company_id):
        cls.company_id = 1
        Supplier.objects.create(supplier_id=101, supplier_name='Test Supplier A', comp_id=cls.company_id)
        AccountHead.objects.create(id=301, symbol='LAB-01', description='Direct Labour', category='Labour')
        AccountHead.objects.create(id=302, symbol='MAT-01', description='Raw Materials', category='With Material')
        Employee.objects.create(emp_id=401, employee_name='John Doe', comp_id=cls.company_id)
        BusinessGroup.objects.create(id=501, symbol='BG-A')
        Category.objects.create(c_id=201, symbol='CAT-A', c_name='Category Alpha', comp_id=cls.company_id)


    @patch('reports.views.get_company_id', return_value=1)
    def test_report_search_get(self, mock_get_company_id):
        response = self.client.get(reverse('reports:report_search'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/search.html')
        self.assertIn('gin_form', response.context)
        self.assertIn('mrs_form', response.context)
        self.assertIn('mrn_form', response.context)

    @patch('reports.views.get_company_id', return_value=1)
    def test_gin_search_post_valid_redirect(self, mock_get_company_id):
        data = {
            'form_type': 'gin_search',
            'drp_option': '1', 'txt_no': 'GIN123', 'report_for': '1',
            'ck_item': 'false', 'txt_itemcode': '',
            'ck_wono': 'false', 'txt_wono': '',
            'ck_supplier': 'false', 'txt_supplier': '',
            'ck_acc_head': 'false', 'rbtn_labour': 'on', 'rbtn_with_material': '', 'dropdown_acc_head': '301',
            'text_from_date': '', 'text_to_date': '',
        }
        response = self.client.post(reverse('reports:report_search'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX should return 204 No Content for redirect
        self.assertIn('HX-Redirect', response.headers)
        self.assertTrue(response.headers['HX-Redirect'].startswith(reverse('reports:gin_search_details')))
        self.assertIn('type=1', response.headers['HX-Redirect'])
        self.assertIn('No=GIN123', response.headers['HX-Redirect'])

    @patch('reports.views.get_company_id', return_value=1)
    def test_gin_search_post_invalid_form_returns_alert(self, mock_get_company_id):
        data = {
            'form_type': 'gin_search',
            'drp_option': '1', 'txt_no': '', # Missing txt_no when drp_option is not 'All'
            'report_for': '1', 'ck_item': 'false', 'txt_itemcode': '', 'ck_wono': 'false', 'txt_wono': '',
            'ck_supplier': 'false', 'txt_supplier': '', 'ck_acc_head': 'false', 'rbtn_labour': 'on',
            'rbtn_with_material': '', 'dropdown_acc_head': '301', 'text_from_date': '', 'text_to_date': '',
        }
        response = self.client.post(reverse('reports:report_search'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render the form again
        self.assertTemplateUsed(response, 'reports/search.html')
        self.assertIn('HX-Trigger', response.headers)
        hx_trigger = json.loads(response.headers['HX-Trigger'])
        self.assertIn('showAlert', hx_trigger)
        self.assertIn('Enter a valid number for the selected option.', hx_trigger['showAlert']['message'])

    @patch('reports.views.get_company_id', return_value=1)
    def test_autocomplete_supplier_view(self, mock_get_company_id):
        response = self.client.get(reverse('reports:autocomplete_supplier') + '?txt_supplier=Test')
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, JsonResponse)
        data = json.loads(response.content)
        self.assertIn('Test Supplier A [101]', data)
        self.assertNotIn('Another Supplier [102]', data) # Should be filtered by prefix
        self.assertNotIn('Test Supplier B [103]', data) # Should be filtered by company_id

    @patch('reports.views.get_company_id', return_value=1)
    def test_gin_acchead_dropdown_view(self, mock_get_company_id):
        response = self.client.post(reverse('reports:gin_acchead_dropdown'), {'rbtn_labour': 'on'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/partials/_acc_head_dropdown.html')
        self.assertContains(response, '<option value="301">[LAB-01] Direct Labour</option>')
        self.assertNotContains(response, '[MAT-01] Raw Materials') # Should not include With Material

        response = self.client.post(reverse('reports:gin_acchead_dropdown'), {'rbtn_with_material': 'on'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/partials/_acc_head_dropdown.html')
        self.assertContains(response, '<option value="302">[MAT-01] Raw Materials</option>')
        self.assertNotContains(response, '[LAB-01] Direct Labour')

    @patch('reports.views.get_company_id', return_value=1)
    def test_gin_report_for_radios_view(self, mock_get_company_id):
        response = self.client.post(reverse('reports:gin_options_changed'), {'drp_option': '1'}, HTTP_HX_REQUEST='true') # GIN No
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/partials/_gin_report_for_radios.html')
        self.assertContains(response, 'value="1" checked') # GIN radio should be checked
        self.assertNotContains(response, 'value="0" checked')

        response = self.client.post(reverse('reports:gin_options_changed'), {'drp_option': '0'}, HTTP_HX_REQUEST='true') # All
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/partials/_gin_report_for_radios.html')
        self.assertContains(response, 'value="0" checked') # PO radio should be checked (default)

class SupplierCRUDViewsTest(ModelTest):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Ensure a supplier exists for update/delete tests
        self.supplier = Supplier.objects.create(supplier_id=1001, supplier_name='Initial Supplier', comp_id=self.company_id)

    def test_supplier_list_view(self, mock_get_company_id):
        response = self.client.get(reverse('reports:supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/supplier/list.html')
        self.assertIn('suppliers', response.context)
        self.assertContains(response, 'Test Supplier A') # Check for existing data

    def test_supplier_table_partial_view(self, mock_get_company_id):
        response = self.client.get(reverse('reports:supplier_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/supplier/_supplier_table.html')
        self.assertIn('suppliers', response.context)
        self.assertContains(response, 'id="supplierTable"')

    def test_supplier_create_view_get(self, mock_get_company_id):
        response = self.client.get(reverse('reports:supplier_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/supplier/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Supplier')

    def test_supplier_create_view_post_valid(self, mock_get_company_id):
        data = {'supplier_name': 'New Supplier', 'comp_id': self.company_id}
        response = self.client.post(reverse('reports:supplier_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(Supplier.objects.filter(supplier_name='New Supplier').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])

    def test_supplier_create_view_post_invalid(self, mock_get_company_id):
        data = {'supplier_name': 'A', 'comp_id': self.company_id} # Too short
        response = self.client.post(reverse('reports:supplier_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertFalse(Supplier.objects.filter(supplier_name='A').exists())
        self.assertContains(response, 'Supplier name must be at least 3 characters long.')

    def test_supplier_update_view_get(self, mock_get_company_id):
        response = self.client.get(reverse('reports:supplier_edit', args=[self.supplier.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/supplier/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.supplier)

    def test_supplier_update_view_post_valid(self, mock_get_company_id):
        data = {'supplier_name': 'Updated Supplier', 'comp_id': self.company_id}
        response = self.client.post(reverse('reports:supplier_edit', args=[self.supplier.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.supplier.refresh_from_db()
        self.assertEqual(self.supplier.supplier_name, 'Updated Supplier')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])

    def test_supplier_delete_view_get(self, mock_get_company_id):
        response = self.client.get(reverse('reports:supplier_delete', args=[self.supplier.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/supplier/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.supplier)

    def test_supplier_delete_view_post(self, mock_get_company_id):
        response = self.client.post(reverse('reports:supplier_delete', args=[self.supplier.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Supplier.objects.filter(pk=self.supplier.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

The core of the frontend modernization relies heavily on HTMX for server-side dynamism and Alpine.js for client-side state management and interactivity.

*   **Tabbed Interface:** The main `search.html` uses Alpine.js (`x-data="{ activeTab: 'gin_search' }"`) to manage which tab content is visible. `x-show` and `x-cloak` control visibility, and `@click` handlers change the `activeTab` state.
*   **Form Submission & Redirection:** Each search form uses `hx-post` to submit data to the `ReportSearchView`. Instead of full page reloads, HTMX allows the view to respond with `HX-Redirect` headers on successful validation, telling the browser to navigate to the results page. On validation errors, the view returns the re-rendered form with errors, and `HX-Trigger` sends a `showAlert` event, caught by a global Alpine.js listener to display a JS alert.
*   **Conditional Field Enabling/Disabling:** Alpine.js (`x-model`, `x-bind:disabled`) directly manages the `disabled` attribute of input fields based on checkbox states (`ckItemChecked`, `ckWonoChecked`, etc.) or dropdown selections (`ddlEnterNoMRS`). This replaces ASP.NET's `AutoPostBack` and server-side `Enabled` property.
*   **Autocomplete (`TxtSearchValue`, `TxtEmpName`):**
    *   The input fields have `hx-get` attributes pointing to Django views (`autocomplete_supplier`, `autocomplete_employee`).
    *   `hx-trigger="keyup changed delay:500ms"` ensures requests are sent after a brief pause and only when content changes.
    *   `hx-target` points to a `div` where suggestions will be displayed (though for true autocomplete, JSON response and Alpine.js rendering of a dropdown list is better). The provided Django views return JSON, which would typically be consumed by a custom Alpine.js component to render the suggestions list.
    *   For simplicity, I've returned JSON responses directly. A more complete solution would have Alpine.js watch `hx-target` and render a `<ul>` list from the JSON data.
*   **Dynamic Dropdowns (Account Head):** The `rbtn_labour` and `rbtn_with_material` radio buttons have `hx-post` attributes that trigger a call to `gin_acchead_dropdown`. This view re-renders only the `<select>` element with the new options, which is then `hx-swapped` back into the DOM, making the update instantaneous.
*   **DataTables for List Views:** For the `Supplier` CRUD example, `supplier/list.html` uses `hx-trigger="load, refreshSupplierList from:body"` to load the DataTable partial. The `_supplier_table.html` partial includes the `$(document).ready(function() { $('#supplierTable').DataTable(...); });` script to initialize DataTables on load/HTMX swap. This ensures DataTables functionality is applied to dynamically loaded content.
*   **Modal Interactions:** The CRUD example for `Supplier` utilizes a modal (`#modal`) for add/edit/delete forms. HTMX buttons (`hx-get`) load the form/confirmation partials into `#modalContent`, and Alpine.js (`_ = "on click add .is-active to #modal"`) handles showing/hiding the modal. Form submissions within the modal use `hx-swap="none"` and `HX-Trigger` to close the modal (`remove .is-active from #modal` via Alpine.js on `HX-AfterRequest`) and refresh the main list table.

### Final Notes

*   **Placeholder Company ID:** The `get_company_id` function in `reports/views.py` is a placeholder. In a real application, this should be replaced with robust logic to retrieve the company ID from the authenticated user's session or profile, ensuring multi-tenancy if applicable.
*   **Error Handling:** The current error handling for form validation uses a global `showAlert` event for simple JS `alert()` calls, mirroring the ASP.NET `ClientScript.RegisterStartupScript`. For a more user-friendly experience, these alerts could be replaced with inline error messages within the form or toast notifications.
*   **Hardcoded URLs:** While `reverse_lazy` is used for internal Django URLs, the `hx-get` attributes for autocomplete endpoints are hardcoded (`/reports/autocomplete/supplier/`). In a production setting, `{% url 'reports:autocomplete_supplier' %}` should be used to make these dynamically generated and more robust. This change is simple but requires care with template rendering within HTMX partials.
*   **Security:** Ensure that any user-provided input used in database queries is properly sanitized and parameterized to prevent SQL injection (Django ORM handles this by default). The existing ASP.NET code used `fun.select` which appears to build raw SQL, posing a risk. Django's ORM is designed to mitigate this.
*   **Code Completeness:** This plan provides all the necessary Django files and code snippets to implement the described functionality. Some minor boilerplate (like `settings.py` modifications for `INSTALLED_APPS` and URL inclusion in the project's main `urls.py`) is assumed.