## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a strategic plan for migrating the provided ASP.NET application to a modern Django-based solution. The focus is on automated processes, clear communication for business stakeholders, and adherence to Django 5.0+ best practices, including "fat models, thin views," HTMX, Alpine.js, and DataTables.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and extends `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

The ASP.NET application `SearchViewFieldMRS.aspx` and its code-behind `SearchViewFieldMRS.aspx.cs` is a report generation tool. It allows users to dynamically select which columns to display from either a "Material Requisition Slip (MRS)" or "Material Issue Note (MIN)" dataset, apply various filters (e.g., MRS/MIN number, item code, date ranges), and then view the results in a grid or export them to Excel.

### Business Value of Django Modernization:

Migrating this reporting functionality to Django offers significant business advantages:

1.  **Enhanced User Experience:** By adopting HTMX and Alpine.js, the page will load faster and update dynamically without full page refreshes, providing a smoother and more responsive user interface. DataTables will enable efficient searching, sorting, and pagination of large datasets directly in the browser.
2.  **Improved Maintainability and Scalability:** Django's structured MVC-like architecture (MVT - Model-View-Template), clear separation of concerns ("fat models, thin views"), and Python's readability lead to code that is easier to understand, maintain, and extend. This reduces the time and cost associated with future enhancements and bug fixes.
3.  **Modern Technology Stack:** Leveraging Django, HTMX, and Alpine.js positions the application on a contemporary, widely supported technology stack, making it easier to find skilled developers and integrate with other modern systems.
4.  **Robustness and Security:** Django includes built-in security features against common web vulnerabilities (e.g., SQL injection, XSS), which are manually handled in the legacy ASP.NET code. This significantly improves the application's overall security posture.
5.  **Cost Efficiency:** Automating parts of the migration process reduces manual coding effort. The open-source nature of Django and associated libraries means no licensing fees, contributing to lower total cost of ownership.
6.  **Better Reporting Capabilities:** The current Excel export relies on an outdated method. Django can integrate with modern Python libraries (like `openpyxl`) to generate proper `.xlsx` files, offering more reliable and versatile export options.

---

## Conversion Steps:

For this specific migration, we will create a new Django application named `inventory_reports`.

## Step 1: Extract Database Schema

**Task:** Identify the database table/views and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with two primary database views:
*   `View_MRS_Item` (used when `MRSType` is 1 or 3)
*   `View_MIN_Item` (used when `MRSType` is 2)

Both views seem to share some common columns (`ItemCode`, `Description`, `UOM`, `CompId`, `BGId`, `EmpId`) but also have distinct fields. The `sp_columns` stored procedure is used to fetch column names dynamically, which is then refined by hardcoded logic to hide or rename columns.

**Inferred Columns (based on usage and dynamic SQL):**

**For `View_MRS_Item`:**
*   `SrNo` (dynamically generated in SQL, not a physical column)
*   `ItemCode` (string)
*   `Description` (string)
*   `UOM` (string)
*   `MRSDate` (string, `MM-DD-YYYY` or `DD-MM-YYYY` format, needs parsing)
*   `MRSNo` (string)
*   `BGGroup` (string)
*   `WONo` (string)
*   `ReqQty` (decimal)
*   `Remarks` (string)
*   `GenBy` (string)
*   `MINDate` (string, `MM-DD-YYYY` or `DD-MM-YYYY` format, needs parsing)
*   `MINNo` (string)
*   `MINQty` (decimal, aliased as 'Issue Qty' in UI)
*   `GenBy2` (string, aliased as 'Generated By' in UI)
*   `CompId` (integer)
*   `BGId` (integer)
*   `EmpId` (string)

**For `View_MIN_Item`:**
*   `SrNo` (dynamically generated in SQL, not a physical column)
*   `ItemCode` (string)
*   `Description` (string)
*   `UOM` (string)
*   `MINDate` (string, `MM-DD-YYYY` or `DD-MM-YYYY` format, needs parsing)
*   `MINNo` (string)
*   `IssueQty` (decimal)
*   `EmpName` (string, aliased as 'Generated By' in UI)
*   `MRSDate` (string, `MM-DD-YYYY` or `DD-MM-YYYY` format, needs parsing)
*   `MRSNo` (string)
*   `WONo` (string)
*   `BGGroup` (string)
*   `ReqQty` (decimal)
*   `Remarks` (string)
*   `EmpName2` (string, aliased as 'Generated By' in UI)
*   `CompId` (integer)
*   `BGId` (integer)
*   `EmpId` (string)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The ASP.NET code primarily performs **Read** operations.
*   **Create:** None identified.
*   **Read:** The core functionality is to read data from `View_MRS_Item` or `View_MIN_Item` based on user-selected columns and various query string filters, then display it in a `GridView`.
*   **Update:** None identified.
*   **Delete:** None identified.
*   **Additional Operations:**
    *   **Dynamic Column Selection:** User selects columns via checkboxes. This impacts the `SELECT` clause of the SQL query and the `GridView` columns.
    *   **Filtering:** Filters are applied based on `Request.QueryString` parameters (MRS/MIN number, item code, category, employee ID, BG Group, WO number, date ranges).
    *   **Export:** Data is exported to an Excel `.xls` file.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **`asp:CheckBox ID="checkAll"`:** Toggles selection of all report columns.
*   **`asp:CheckBoxList ID="chkFields"` and `ID="chkFields3"`:** These are the key controls for dynamic column selection. Their content changes based on the report `MRSType` (`flag` variable in C#).
*   **`asp:Button ID="btnSub"` (Show):** Triggers data retrieval and display in the grid.
*   **`asp:Button ID="btnExport"` (Export To Excel):** Triggers the Excel export functionality.
*   **`asp:Button ID="btnCancel"`:** Navigates back to `Search.aspx`.
*   **`asp:GridView ID="GridView1"`:** Displays the tabular report data. Columns are dynamically added to this control.
*   **`asp:Panel` controls:** Used for layout and scrolling.
*   **Hidden input fields (inferred):** The query string parameters (`MRSType`, `MRSno`, `ICode`, `FDateMRS`, `TDateMRS`, `Rbtn`, `EmpidMRS`, `BGGroupMRS`, `WONoMRS`, `ICategory`) behave as hidden inputs or form elements that influence the report. These need to be incorporated into Django's form/filter handling.
*   **Client-side JavaScript:** Custom JS for `GridView` scrolling and header fixing will be replaced by DataTables.

## Step 4: Generate Django Code

We will create a new Django app named `inventory_reports`.

**File Structure for `inventory_reports` app:**

```
inventory_reports/
├── __init__.py
├── models.py
├── forms.py
├── views.py
├── urls.py
├── tests.py
└── templates/
    └── inventory_reports/
        ├── report_search.html
        ├── _report_filters.html  # Partial for filter form
        ├── _column_selection.html # Partial for dynamic checkboxes
        └── _report_table.html    # Partial for DataTables
```

### 4.1 Models (`inventory_reports/models.py`)

The ASP.NET code uses two database views (`View_MRS_Item`, `View_MIN_Item`) and complex logic to define which columns are shown and their display names. To adhere to the "fat model" principle, we'll encapsulate this column metadata and the data retrieval logic within the models.

```python
from django.db import models
from datetime import datetime

# Helper class to define report column metadata
class ReportColumn:
    def __init__(self, db_field_name, display_name, mrs_default=False, min_default=False, 
                 is_mrs_specific=False, is_min_specific=False, mapped_to_mrs=None, mapped_to_min=None):
        """
        :param db_field_name: The actual field name in the Django model (and often DB view).
        :param display_name: The human-readable name to show in the UI.
        :param mrs_default: True if this column is selected by default for MRS reports.
        :param min_default: True if this column is selected by default for MIN reports.
        :param is_mrs_specific: True if this column only appears in View_MRS_Item.
        :param is_min_specific: True if this column only appears in View_MIN_Item.
        :param mapped_to_mrs: For fields that appear in chkFields3 on MRS (MIN related data),
                              this is the actual field name in MrsItem.
        :param mapped_to_min: For fields that appear in chkFields3 on MIN (MRS related data),
                              this is the actual field name in MinItem.
        """
        self.db_field_name = db_field_name
        self.display_name = display_name
        self.mrs_default = mrs_default
        self.min_default = min_default
        self.is_mrs_specific = is_mrs_specific
        self.is_min_specific = is_min_specific
        self.mapped_to_mrs = mapped_to_mrs # Actual field in MrsItem model for cross-report fields
        self.mapped_to_min = mapped_to_min # Actual field in MinItem model for cross-report fields

# Define all possible columns and their properties, aligning with ASP.NET logic
REPORT_COLUMNS_METADATA = [
    # Common fields (appear in both views under the same name)
    ReportColumn('sr_no', 'Sr No', mrs_default=True, min_default=True), # Dynamic, handled in post-processing
    ReportColumn('item_code', 'Item Code', mrs_default=True, min_default=True),
    ReportColumn('description', 'Description', mrs_default=True, min_default=True),
    ReportColumn('uom', 'UOM', mrs_default=True, min_default=True),

    # MRS-specific fields from chkFields (mrs_default=True reflects initial selection)
    ReportColumn('mrs_date', 'Date', mrs_default=True, is_mrs_specific=True), # MRS Date (main for MRS)
    ReportColumn('mrs_no', 'MRS No', mrs_default=True, is_mrs_specific=True),
    ReportColumn('bg_group', 'BG Group', mrs_default=True, is_mrs_specific=True),
    ReportColumn('wo_no', 'WO No', mrs_default=True, is_mrs_specific=True),
    ReportColumn('req_qty', 'Req Qty', mrs_default=True, is_mrs_specific=True),
    ReportColumn('remarks', 'Remarks', mrs_default=True, is_mrs_specific=True),
    ReportColumn('gen_by', 'Generated By', mrs_default=True, is_mrs_specific=True), # MRS generated by

    # MIN-specific fields from chkFields (min_default=True reflects initial selection)
    ReportColumn('min_date', 'Date', min_default=True, is_min_specific=True), # MIN Date (main for MIN)
    ReportColumn('min_no', 'MIN No', min_default=True, is_min_specific=True),
    ReportColumn('issue_qty', 'Issue Qty', min_default=True, is_min_specific=True), # In View_MIN_Item this is IssueQty
    ReportColumn('emp_name', 'Generated By', min_default=True, is_min_specific=True), # In View_MIN_Item this is EmpName

    # Cross-report fields (chkFields3 in ASP.NET)
    # These are MIN-related columns displayed in MRS report
    ReportColumn('min_date_mrs_rel', 'MIN Date', is_mrs_specific=True, mapped_to_mrs='min_date'), # `min_date` in MrsItem
    ReportColumn('min_no_mrs_rel', 'MIN No', is_mrs_specific=True, mapped_to_mrs='min_no'),
    ReportColumn('issue_qty_mrs_rel', 'Issue Qty (MIN)', is_mrs_specific=True, mapped_to_mrs='issue_qty'), # `issue_qty` (MINQty) in MrsItem
    ReportColumn('gen_by2_mrs_rel', 'MIN Generated By', is_mrs_specific=True, mapped_to_mrs='gen_by2'),

    # These are MRS-related columns displayed in MIN report
    ReportColumn('mrs_date_min_rel', 'MRS Date', is_min_specific=True, mapped_to_min='mrs_date'), # `mrs_date` in MinItem
    ReportColumn('mrs_no_min_rel', 'MRS No', is_min_specific=True, mapped_to_min='mrs_no'),
    ReportColumn('wo_no_min_rel', 'WO No', is_min_specific=True, mapped_to_min='wo_no'),
    ReportColumn('bg_group_min_rel', 'BG Group', is_min_specific=True, mapped_to_min='bg_group'),
    ReportColumn('req_qty_min_rel', 'Req Qty (MRS)', is_min_specific=True, mapped_to_min='req_qty'),
    ReportColumn('remarks_min_rel', 'Remarks (MRS)', is_min_specific=True, mapped_to_min='remarks'),
    ReportColumn('emp_name2_min_rel', 'MRS Generated By', is_min_specific=True, mapped_to_min='emp_name2'),
]

class ReportBase(models.Model):
    """Base model for common fields that exist in both MRS and MIN views."""
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # CompId is used for filtering
    bg_id = models.IntegerField(db_column='BGId', blank=True, null=True) # BGId is used for filtering, mapped to BGGroup
    emp_id = models.CharField(db_column='EmpId', max_length=50, blank=True, null=True) # EmpId for filtering

    class Meta:
        abstract = True
        managed = False # Crucial for mapping to existing DB views/tables

    @staticmethod
    def _format_date_for_display(date_str):
        """Formats a date string from DB (e.g., 'MM-DD-YYYY') to 'DD/MM/YYYY' for UI."""
        if not date_str:
            return ""
        try:
            # Attempt to parse common ASP.NET date string formats
            for fmt in ['%m-%d-%Y', '%d-%m-%Y']:
                try:
                    dt_obj = datetime.strptime(str(date_str), fmt)
                    return dt_obj.strftime('%d/%m/%Y')
                except ValueError:
                    continue
            return str(date_str) # Return original if no format matches
        except Exception:
            return str(date_str) # Fallback for unexpected errors

    @staticmethod
    def _format_date_for_db_query(date_obj):
        """Formats a date object or string into 'MM-DD-YYYY' for database query range."""
        if isinstance(date_obj, str):
            try:
                # Try to parse from YYYY-MM-DD (standard HTML date input) or MM/DD/YYYY
                dt_obj = datetime.strptime(date_obj, '%Y-%m-%d').date()
            except ValueError:
                try:
                    dt_obj = datetime.strptime(date_obj, '%m/%d/%Y').date()
                except ValueError:
                    return date_obj # Return as is if cannot parse
        elif isinstance(date_obj, datetime):
            dt_obj = date_obj.date()

        if hasattr(dt_obj, 'strftime'):
            return dt_obj.strftime('%m-%d-%Y')
        return None


class MrsItem(ReportBase):
    """Django Model for View_MRS_Item."""
    mrs_date = models.CharField(db_column='MRSDate', max_length=50, blank=True, null=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=4, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)
    gen_by = models.CharField(db_column='GenBy', max_length=255, blank=True, null=True)
    
    # MIN related fields that exist in View_MRS_Item
    min_date = models.CharField(db_column='MINDate', max_length=50, blank=True, null=True)
    min_no = models.CharField(db_column='MINNo', max_length=50, blank=True, null=True)
    issue_qty = models.DecimalField(db_column='MINQty', max_digits=18, decimal_places=4, blank=True, null=True) # Maps to MINQty
    gen_by2 = models.CharField(db_column='GenBy2', max_length=255, blank=True, null=True)

    class Meta(ReportBase.Meta):
        db_table = 'View_MRS_Item'
        verbose_name = 'MRS Item Report'
        verbose_name_plural = 'MRS Item Reports'


class MinItem(ReportBase):
    """Django Model for View_MIN_Item."""
    min_date = models.CharField(db_column='MINDate', max_length=50, blank=True, null=True)
    min_no = models.CharField(db_column='MINNo', max_length=50, blank=True, null=True)
    issue_qty = models.DecimalField(db_column='IssueQty', max_digits=18, decimal_places=4, blank=True, null=True) # Maps to IssueQty directly
    emp_name = models.CharField(db_column='EmpName', max_length=255, blank=True, null=True) # Maps to EmpName

    # MRS related fields that exist in View_MIN_Item
    mrs_date = models.CharField(db_column='MRSDate', max_length=50, blank=True, null=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    bg_group = models.CharField(db_column='BGGroup', max_length=255, blank=True, null=True)
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=4, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)
    emp_name2 = models.CharField(db_column='EmpName2', max_length=255, blank=True, null=True)

    class Meta(ReportBase.Meta):
        db_table = 'View_MIN_Item'
        verbose_name = 'MIN Item Report'
        verbose_name_plural = 'MIN Item Reports'


class ReportManager:
    """
    Handles fetching and processing report data for both MRS and MIN,
    emulating the dynamic query and column handling from ASP.NET code.
    This class embodies the 'fat model' approach by containing complex business logic.
    """
    @staticmethod
    def get_report_data(report_type, selected_column_keys, filter_params):
        """
        Fetches report data based on report_type, selected columns, and filters.
        :param report_type: Integer (1 for MRS, 2 for MIN, 3 for MRS - behaves like 1).
        :param selected_column_keys: List of selected column keys (e.g., ['item_code', 'mrs_date']).
        :param filter_params: Dictionary of filter parameters (from query string/form).
        :return: Tuple (list_of_dictionaries_data, list_of_headers_for_display, list_of_db_field_names_fetched)
        """
        model = None
        applicable_columns_metadata = []
        db_field_name_map = {} # Maps user-facing key to actual Django model field name
        
        if report_type in [1, 3]: # MRS Report
            model = MrsItem
            # Filter metadata to only MRS-relevant columns
            applicable_columns_metadata = [col for col in REPORT_COLUMNS_METADATA if not col.is_min_specific]
            # Build map for MRS report, handling cross-report fields
            for col in REPORT_COLUMNS_METADATA:
                if col.is_mrs_specific and col.mapped_to_mrs:
                    db_field_name_map[col.db_field_name] = col.mapped_to_mrs
                elif not col.is_min_specific: # Common fields or direct MRS fields
                    db_field_name_map[col.db_field_name] = col.db_field_name

        elif report_type == 2: # MIN Report
            model = MinItem
            # Filter metadata to only MIN-relevant columns
            applicable_columns_metadata = [col for col in REPORT_COLUMNS_METADATA if not col.is_mrs_specific]
            # Build map for MIN report, handling cross-report fields
            for col in REPORT_COLUMNS_METADATA:
                if col.is_min_specific and col.mapped_to_min:
                    db_field_name_map[col.db_field_name] = col.mapped_to_min
                elif not col.is_mrs_specific: # Common fields or direct MIN fields
                    db_field_name_map[col.db_field_name] = col.db_field_name
        else:
            return [], [], [] # Invalid report type

        if not model or not selected_column_keys:
            return [], [], []

        # Determine the actual database field names to fetch based on selected_column_keys
        db_fields_to_fetch = []
        for key in selected_column_keys:
            if key == 'sr_no': continue # SrNo is handled dynamically
            db_field = db_field_name_map.get(key)
            if db_field and hasattr(model, db_field): # Ensure the field exists on the model
                db_fields_to_fetch.append(db_field)
        
        # Remove duplicates while preserving order
        db_fields_to_fetch = list(dict.fromkeys(db_fields_to_fetch))

        if not db_fields_to_fetch:
            return [], [], [] # No valid fields selected to query

        queryset = model.objects.all()

        # Apply general filters present in ASP.NET's Page_Load
        # CompId is expected from Session in ASP.NET, here passed in filter_params
        if filter_params.get('comp_id'):
            queryset = queryset.filter(comp_id=filter_params['comp_id'])
        if filter_params.get('item_code_param'):
            queryset = queryset.filter(item_code=filter_params['item_code_param'])
        if filter_params.get('emp_id_param'):
            queryset = queryset.filter(emp_id=filter_params['emp_id_param'])
        if filter_params.get('bg_group_param'): # Query string name was BGGroupMRS, maps to BGId
            queryset = queryset.filter(bg_id=filter_params['bg_group_param'])
        if filter_params.get('wo_no_param'): # Query string name was WONoMRS
            queryset = queryset.filter(wo_no=filter_params['wo_no_param'])

        # Apply report-specific filters and date filters
        from_date = filter_params.get('from_date_param')
        to_date = filter_params.get('to_date_param')
        report_date_type = filter_params.get('report_date_type') # Rbtn in ASP.NET (1=MRSDate, 2=MINDate)

        if report_type in [1, 3]: # MRS Report specific filtering
            if filter_params.get('mrs_no_param'): # MRSno query string for MRS report type
                queryset = queryset.filter(mrs_no=filter_params['mrs_no_param'])
            if filter_params.get('item_category_param'): # ICategory from ASP.NET, assumed to map to 'item_category'
                # This field is not directly in MrsItem model based on inferred schema.
                # If CId is a different table, a join or a raw query might be needed.
                # For now, assuming it's a direct field or can be ignored if not present.
                # If View_MRS_Item includes 'CId' column, map it accordingly.
                # If not present in MrsItem model, this filter will raise an error.
                pass # queryset = queryset.filter(c_id=filter_params['item_category_param'])

            if from_date and to_date:
                from_date_db = model._format_date_for_db_query(from_date)
                to_date_db = model._format_date_for_db_query(to_date)
                if report_date_type == 'mrs':
                    queryset = queryset.filter(mrs_date__range=[from_date_db, to_date_db])
                elif report_date_type == 'min':
                    queryset = queryset.filter(min_date__range=[from_date_db, to_date_db])

        elif report_type == 2: # MIN Report specific filtering
            if filter_params.get('mrs_no_param'): # For MIN type, MRSno query string means MINNo
                queryset = queryset.filter(min_no=filter_params['mrs_no_param'])

            if from_date and to_date:
                from_date_db = model._format_date_for_db_query(from_date)
                to_date_db = model._format_date_for_db_query(to_date)
                if report_date_type == 'mrs':
                    queryset = queryset.filter(mrs_date__range=[from_date_db, to_date_db])
                elif report_date_type == 'min':
                    queryset = queryset.filter(min_date__range=[from_date_db, to_date_db])
        
        # Order by ItemCode for SrNo as in ASP.NET, if 'item_code' field exists
        if 'item_code' in [f.name for f in model._meta.get_fields()]:
            data = queryset.values(*db_fields_to_fetch).order_by('item_code')
        else: # Fallback if no item_code or for generic views
            data = queryset.values(*db_fields_to_fetch)
        
        processed_data = []
        display_headers = []
        
        # Build headers in the requested order and process data
        for col_key in selected_column_keys:
            col_meta = next((col for col in REPORT_COLUMNS_METADATA if col.db_field_name == col_key), None)
            if not col_meta:
                continue # Skip if column key is not recognized
            
            # Add header for display
            if col_key == 'sr_no':
                display_headers.append('Sr No')
            else:
                display_headers.append(col_meta.display_name)

        # Process each row for display (Sr No, date formatting, value mapping)
        for i, row in enumerate(data):
            processed_row_dict = {}
            if 'sr_no' in selected_column_keys:
                processed_row_dict['Sr No'] = i + 1 # Dynamic Sr No

            for col_key in selected_column_keys:
                if col_key == 'sr_no': continue # Already handled
                
                col_meta = next((col for col in REPORT_COLUMNS_METADATA if col.db_field_name == col_key), None)
                if not col_meta: continue

                actual_db_field = None
                if report_type in [1, 3]: # MRS
                    actual_db_field = col_meta.mapped_to_mrs if col_meta.mapped_to_mrs else col_meta.db_field_name
                elif report_type == 2: # MIN
                    actual_db_field = col_meta.mapped_to_min if col_meta.mapped_to_min else col_meta.db_field_name
                
                if actual_db_field and actual_db_field in row:
                    value = row[actual_db_field]
                    if actual_db_field in ['mrs_date', 'min_date']:
                        processed_row_dict[col_meta.display_name] = model._format_date_for_display(value)
                    else:
                        processed_row_dict[col_meta.display_name] = value
            processed_data.append(processed_row_dict)

        return processed_data, display_headers, db_fields_to_fetch # db_fields_to_fetch for raw export if needed

```

### 4.2 Forms (`inventory_reports/forms.py`)

We need a form to handle the main filters (report type, date range, etc.) and a dynamic way to present column selection.

```python
from django import forms
from .models import REPORT_COLUMNS_METADATA, ReportManager
from django.core.exceptions import ValidationError
from datetime import datetime

class ReportFilterForm(forms.Form):
    REPORT_TYPE_CHOICES = [
        ('1', 'MRS Report'), # MRSType 1 and 3 in ASP.NET are MRS
        ('2', 'MIN Report'),
    ]

    DATE_TYPE_CHOICES = [
        ('mrs', 'MRS Date'), # Rbtn 1
        ('min', 'MIN Date'), # Rbtn 2
    ]

    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'}),
        initial='1', # Default to MRS as per ASP.NET
        label='Report Type'
    )

    # These fields correspond to Request.QueryString parameters from ASP.NET
    # They are not always required and may be pre-filled from URL
    mrs_no_param = forms.CharField(
        label='MRS/MIN No', required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    item_code_param = forms.CharField(
        label='Item Code', required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    item_category_param = forms.CharField( # Maps to CId in ASP.NET
        label='Item Category', required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    emp_id_param = forms.CharField(
        label='Employee ID', required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    bg_group_param = forms.CharField( # Maps to BGId in models
        label='BG Group', required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    wo_no_param = forms.CharField(
        label='WO No', required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    # Date filters
    date_type = forms.ChoiceField(
        choices=DATE_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'}),
        initial='mrs',
        label='Date Type'
    )
    from_date_param = forms.DateField(
        label='From Date', required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    to_date_param = forms.DateField(
        label='To Date', required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # Dynamic column selection field (rendered as checkboxes in template)
    selected_columns = forms.MultipleChoiceField(
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'flex flex-wrap'}),
        label='Select columns to show in the GridView'
    )

    # Use a hidden field for CompId as it's from Session in ASP.NET
    comp_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)


    def __init__(self, *args, **kwargs):
        current_report_type = kwargs.pop('current_report_type', '1') # Default MRS
        initial_params = kwargs.get('initial', {})
        
        super().__init__(*args, **kwargs)

        # Update initial values for query parameters based on URL (GET request)
        if 'data' not in kwargs and initial_params: # If it's a GET request without POST data
            for field_name, value in initial_params.items():
                if field_name in self.fields:
                    self.fields[field_name].initial = value
            # Explicitly set report_type initial based on query string
            self.fields['report_type'].initial = str(initial_params.get('report_type_param', '1'))
            self.fields['date_type'].initial = 'mrs' if str(initial_params.get('date_type_param', '1')) == '1' else 'min'

        self._set_column_choices(current_report_type, initial_params.get('selected_columns', []))
        
        # Prefill column checkboxes based on ASP.NET logic if no selection provided
        if not self.is_bound and not initial_params.get('selected_columns'):
            default_selection = []
            for col in REPORT_COLUMNS_METADATA:
                if current_report_type in ['1', '3'] and col.mrs_default:
                    default_selection.append(col.db_field_name)
                elif current_report_type == '2' and col.min_default:
                    default_selection.append(col.db_field_name)
            self.fields['selected_columns'].initial = default_selection


    def _set_column_choices(self, report_type, current_selection_keys=None):
        """Sets the choices for the selected_columns field based on report type."""
        choices = []
        if report_type in ['1', '3']: # MRS report
            for col in REPORT_COLUMNS_METADATA:
                if not col.is_min_specific: # Include common and MRS-specific
                    choices.append((col.db_field_name, col.display_name))
        elif report_type == '2': # MIN report
            for col in REPORT_COLUMNS_METADATA:
                if not col.is_mrs_specific: # Include common and MIN-specific
                    choices.append((col.db_field_name, col.display_name))
        
        self.fields['selected_columns'].choices = choices

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date_param')
        to_date = cleaned_data.get('to_date_param')

        if from_date and to_date and from_date > to_date:
            self.add_error('from_date_param', 'From Date cannot be after To Date.')
            self.add_error('to_date_param', 'To Date cannot be before From Date.')

        # Set default CompId if not provided (emulating ASP.NET session)
        if 'comp_id' not in cleaned_data or cleaned_data['comp_id'] is None:
            # In a real scenario, this would come from the user's session/profile
            # For demonstration, setting a dummy ID.
            cleaned_data['comp_id'] = 1 
        
        return cleaned_data

```

### 4.3 Views (`inventory_reports/views.py`)

We'll use `TemplateView` for the main page and a custom `ReportTablePartialView` (derived from `View`) to handle HTMX requests for dynamic table content.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import render
from django.contrib import messages
from datetime import datetime

from .forms import ReportFilterForm
from .models import ReportManager, REPORT_COLUMNS_METADATA # Import necessary models and metadata

class ReportSearchView(TemplateView):
    template_name = 'inventory_reports/report_search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract filter parameters from URL query string
        filter_params = {
            'mrs_no_param': self.request.GET.get('MRSno'),
            'item_code_param': self.request.GET.get('ICode'),
            'item_category_param': self.request.GET.get('ICategory'), # Maps to CId in ASP.NET
            'emp_id_param': self.request.GET.get('EmpidMRS'),
            'bg_group_param': self.request.GET.get('BGGroupMRS'),
            'wo_no_param': self.request.GET.get('WONoMRS'),
            'from_date_param': self.request.GET.get('FDateMRS'),
            'to_date_param': self.request.GET.get('TDateMRS'),
            'date_type_param': self.request.GET.get('Rbtn'), # Rbtn 1=MRSDate, 2=MINDate
            'report_type_param': self.request.GET.get('MRSType'), # MRSType 1=MRS, 2=MIN, 3=MRS
            'comp_id': self.request.session.get('compid', 1), # Default compid to 1 if not in session
        }

        # Determine current report type from URL, default to MRS
        current_report_type = str(filter_params.get('report_type_param', '1')) # Default to MRS (flag 1)

        # Initialize form with data from query string
        form = ReportFilterForm(initial=filter_params, current_report_type=current_report_type)
        context['form'] = form
        context['report_type'] = current_report_type # For template logic
        context['report_columns_metadata'] = REPORT_COLUMNS_METADATA
        
        # Pass filter params to template for initial rendering of column checkboxes
        context['filter_params_from_url'] = filter_params

        # Prepare default selected columns for initial load based on report_type
        default_selected_columns_keys = []
        for col in REPORT_COLUMNS_METADATA:
            if current_report_type in ['1', '3'] and col.mrs_default and not col.is_min_specific:
                default_selected_columns_keys.append(col.db_field_name)
            elif current_report_type == '2' and col.min_default and not col.is_mrs_specific:
                default_selected_columns_keys.append(col.db_field_name)
            # Handle specific pre-selections based on ASP.NET's complex logic
            # For MRS (flag 1/3), ASP.NET also selected MINDate, MINNo, Issue Qty, GenBy2 from chkFields3
            if current_report_type in ['1', '3']:
                if col.db_field_name in ['min_date_mrs_rel', 'min_no_mrs_rel', 'issue_qty_mrs_rel', 'gen_by2_mrs_rel']:
                     # ASP.NET pre-selects these based on the `flag` type
                     # Replicating this exact selection here for MRS report type
                     default_selected_columns_keys.append(col.db_field_name)
        
        context['default_selected_columns_keys'] = default_selected_columns_keys

        return context

class ReportTablePartialView(View):
    """
    Handles HTMX requests to fetch and render the report table.
    This view is thin, delegating heavy lifting to ReportManager.
    """
    def get(self, request, *args, **kwargs):
        # This GET is primarily for HTMX initial load or reloads
        # Filters and selected columns come from query parameters if loaded via GET
        # For simplicity, we'll assume the main form's POST will drive the table.
        # If this is called via hx-get, it means we need to get params from request.GET
        return self._process_request(request)

    def post(self, request, *args, **kwargs):
        # This POST is triggered by the "Show" button via HTMX
        return self._process_request(request)

    def _process_request(self, request):
        report_data = []
        headers = []
        # Get data from POST for 'Show' button, or GET for initial load/refresh
        form_data_source = request.POST if request.method == 'POST' else request.GET

        form = ReportFilterForm(form_data_source, initial=request.GET.dict(), current_report_type=form_data_source.get('report_type'))
        
        if form.is_valid():
            cleaned_data = form.cleaned_data
            report_type = int(cleaned_data['report_type'])
            selected_columns_keys = cleaned_data['selected_columns']
            comp_id = cleaned_data['comp_id']

            # Prepare filter parameters dictionary for ReportManager
            filter_params = {
                'comp_id': comp_id,
                'mrs_no_param': cleaned_data.get('mrs_no_param'),
                'item_code_param': cleaned_data.get('item_code_param'),
                'item_category_param': cleaned_data.get('item_category_param'),
                'emp_id_param': cleaned_data.get('emp_id_param'),
                'bg_group_param': cleaned_data.get('bg_group_param'),
                'wo_no_param': cleaned_data.get('wo_no_param'),
                'from_date_param': cleaned_data.get('from_date_param'),
                'to_date_param': cleaned_data.get('to_date_param'),
                'report_date_type': cleaned_data.get('date_type'), # 'mrs' or 'min'
            }

            report_data, headers, db_fields_fetched = ReportManager.get_report_data(
                report_type, selected_columns_keys, filter_params
            )
            # Store report filters and selected columns in session for export
            request.session['report_filters'] = filter_params
            request.session['selected_report_columns'] = selected_columns_keys
            request.session['current_report_type'] = report_type

            if not report_data:
                messages.info(request, "No Records to Display.")

        else:
            # Form is not valid, e.g., date range error.
            # Render the form again, possibly with errors, or just show an empty table.
            # For simplicity, we'll return an empty table and let messages handle the error feedback.
            messages.error(request, "Please correct the errors in the form.")
            for field, errors in form.errors.items():
                 for error in errors:
                     messages.error(request, f"{form.fields[field].label}: {error}")


        context = {
            'report_data': report_data,
            'headers': headers,
            'report_data_json': report_data, # For DataTables initialization if needed
        }
        return render(request, 'inventory_reports/_report_table.html', context)


class ExportReportView(View):
    """Handles exporting the current report data to Excel."""
    def get(self, request, *args, **kwargs):
        report_filters = request.session.get('report_filters', {})
        selected_columns = request.session.get('selected_report_columns', [])
        current_report_type = request.session.get('current_report_type', 1)

        if not selected_columns:
            messages.error(request, "No columns selected for export. Please generate a report first.")
            return HttpResponse("No data to export. Please generate a report first.", status=400)

        report_data, display_headers, _ = ReportManager.get_report_data(
            current_report_type, selected_columns, report_filters
        )

        if not report_data:
            messages.info(request, "No records to export.")
            return HttpResponse("No records to export.", status=200)

        # Generate Excel (XLSX) file
        import io
        import openpyxl
        from openpyxl.styles import Font, Border, Side

        output = io.BytesIO()
        workbook = openpyxl.Workbook()
        sheet = workbook.active
        sheet.title = "Report"

        # Add headers
        header_font = Font(bold=True)
        thin_border = Border(left=Side(style='thin'),
                             right=Side(style='thin'),
                             top=Side(style='thin'),
                             bottom=Side(style='thin'))
        
        # Write headers
        for col_idx, header_text in enumerate(display_headers):
            cell = sheet.cell(row=1, column=col_idx + 1, value=header_text)
            cell.font = header_font
            cell.border = thin_border
            
        # Write data rows
        for row_idx, row_dict in enumerate(report_data):
            for col_idx, header_text in enumerate(display_headers):
                value = row_dict.get(header_text, '') # Get value using display header as key
                cell = sheet.cell(row=row_idx + 2, column=col_idx + 1, value=value)
                cell.border = thin_border

        # Adjust column widths (optional)
        for column in sheet.columns:
            max_length = 0
            column_name = column[0].value # Get the header name
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            sheet.column_dimensions[openpyxl.utils.get_column_letter(column[0].column)].width = adjusted_width

        workbook.save(output)
        output.seek(0)

        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

```

### 4.4 Templates (`inventory_reports/templates/inventory_reports/`)

**`report_search.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Inventory Reports</h2>

    {# Display Django messages #}
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="p-3 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'info' %}bg-blue-100 text-blue-700{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Filter Options</h3>
        
        {# HTMX Form for Filters and Column Selection #}
        {# This form will trigger updates to the column checkboxes and the report table #}
        <form id="report-filter-form" hx-post="{% url 'inventory_reports:report_table_partial' %}" 
              hx-target="#report-table-container" hx-swap="innerHTML" hx-indicator="#loading-indicator">
            {% csrf_token %}

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                {# Report Type Selection (Radio Buttons) #}
                <div class="col-span-full mb-4">
                    <label class="block text-sm font-medium text-gray-700">Report Type:</label>
                    <div class="mt-1 flex space-x-4" 
                         hx-get="{% url 'inventory_reports:column_selection_partial' %}" 
                         hx-target="#column-selection-container"
                         hx-vals="js:{ report_type: event.target.value }"
                         hx-trigger="change from:[name='report_type']">
                        {% for choice in form.report_type %}
                            <label class="inline-flex items-center">
                                {{ choice.tag }}
                                <span class="ml-2 text-gray-700">{{ choice.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                </div>

                {# Filter fields #}
                {% for field in form %}
                    {% if field.name not in "report_type,selected_columns,comp_id" %}
                    <div>
                        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ field.label }}
                        </label>
                        <div class="mt-1">
                            {{ field }}
                        </div>
                        {% if field.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                        {% endif %}
                    </div>
                    {% endif %}
                {% endfor %}
                
                {# Hidden CompId field #}
                {{ form.comp_id }}

            </div>

            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-lg font-semibold text-gray-700 mb-3">Select columns to show:</h4>
                <div id="column-selection-container" 
                     hx-trigger="load, change from:body" # Trigger on page load and on report type change
                     hx-get="{% url 'inventory_reports:column_selection_partial' %}" 
                     hx-target="#column-selection-container"
                     hx-swap="innerHTML"
                     hx-vals="js:{ report_type: document.querySelector('input[name=\"report_type\"]:checked')?.value || '{{ report_type }}', selected_columns: Array.from(document.querySelectorAll('input[name=\"selected_columns\"]:checked')).map(cb => cb.value) }"
                >
                    {# Initial column selection will be loaded here via HTMX #}
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading columns...</p>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Show Report
                </button>
                <a href="{% url 'inventory_reports:export_report' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    Export To Excel
                </a>
                <a href="/module/inventory/reports/search.aspx" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    {# Report Table Container #}
    <div class="bg-white shadow-md rounded-lg p-6 overflow-x-auto">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Report Data</h3>
        <div id="loading-indicator" class="htmx-indicator text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading report data...</p>
        </div>
        <div id="report-table-container">
            {# Report table will be loaded here via HTMX POST from the form #}
            {# Initial load will be empty or a default table #}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
{# Include DataTables and Alpine.js from base.html (not shown here) #}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Reinitialize DataTables if the table partial is loaded
        if (evt.target.id === 'report-table-container') {
            if ($.fn.DataTable.isDataTable('#reportTable')) {
                $('#reportTable').DataTable().destroy();
            }
            $('#reportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "scrollX": true
            });
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Initial load of column selection based on default report type
        htmx.trigger(document.getElementById('column-selection-container'), 'load');
        // Initial load of report table (will likely be empty until 'Show' is clicked)
        // Or if we want to auto-show, trigger the form submit:
        // htmx.trigger(document.getElementById('report-filter-form'), 'submit');
    });

    // Alpine.js for checkAll functionality
    document.addEventListener('alpine:init', () => {
        Alpine.data('columnCheckboxes', () => ({
            checkAll: false,
            init() {
                this.$watch('checkAll', (value) => {
                    document.querySelectorAll('input[name="selected_columns"]').forEach(checkbox => {
                        checkbox.checked = value;
                    });
                });
            },
            toggleCheckAll() {
                this.checkAll = !this.checkAll;
            }
        }));
    });
</script>
{% endblock %}
```

**`_report_filters.html` (Partial - Not used in this design, filters are in main form for simplicity)**
*   This partial would be used if the filter section itself needed dynamic updates. For this scenario, the main form handles all filtering.

**`_column_selection.html` (Partial for Dynamic Checkboxes)**

```html
{# This template is loaded via HTMX when report_type changes or on initial load #}
<div x-data="columnCheckboxes">
    <label class="inline-flex items-center cursor-pointer mb-3">
        <input type="checkbox" x-model="checkAll" class="form-checkbox h-5 w-5 text-indigo-600 rounded">
        <span class="ml-2 text-base font-semibold text-gray-800">Check All</span>
    </label>
    
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-x-4 gap-y-2">
        {% for choice_value, choice_label in form.selected_columns.field.choices %}
            {% comment %}
            `selected_columns` is a MultipleChoiceField. Its initial value should be set
            in the view/form based on `mrs_default`/`min_default` or query string.
            Django's default widget will render `checked` attribute correctly based on initial/bound data.
            {% endcomment %}
            {% for item in form.selected_columns %}
                {% if item.choice_value == choice_value %}
                    <label class="inline-flex items-center">
                        {{ item.tag }}
                        <span class="ml-2 text-gray-700">{{ item.choice_label }}</span>
                    </label>
                {% endif %}
            {% endfor %}
        {% endfor %}
    </div>
</div>
```

**`_report_table.html` (Partial for DataTables)**

```html
{# This template is loaded via HTMX into #report-table-container #}
<div class="overflow-x-auto">
    {% if report_data %}
    <table id="reportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                {% for header in headers %}
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ header }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row_dict in report_data %}
            <tr>
                {% for header in headers %}
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ row_dict|get_item:header }} {# Custom filter to get dict value by key #}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="text-center text-gray-600 py-8">No records to display based on current filters and column selection. Please adjust your criteria and try again.</p>
    {% endif %}
</div>

{% comment %}
    DataTables initialization will be handled by the JS in report_search.html
    which listens for htmx:afterSwap events and reinitializes.
    This ensures DataTables is applied only after the table HTML is loaded.
{% endcomment %}

{# Define a simple custom template filter to access dict items by key #}
{# (This filter should be defined in a `templatetags` directory within your app) #}
{% load inventory_report_filters %}
```

**`inventory_reports/templatetags/inventory_report_filters.py`:**

```python
from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    return dictionary.get(key)
```

### 4.5 URLs (`inventory_reports/urls.py`)

```python
from django.urls import path
from .views import ReportSearchView, ReportTablePartialView, ExportReportView
from django.views.decorators.csrf import csrf_exempt # For simplicity with HTMX POST, though csrf token should be used

app_name = 'inventory_reports'

urlpatterns = [
    # Main report page
    path('search-view-mrs/', ReportSearchView.as_view(), name='report_search_view'),
    
    # HTMX endpoint for the report table content (both GET for initial load and POST for submit)
    path('report-table-partial/', ReportTablePartialView.as_view(), name='report_table_partial'),
    
    # HTMX endpoint for dynamic column selection checkboxes
    path('column-selection-partial/', ReportSearchView.as_view(template_name='inventory_reports/_column_selection.html'), name='column_selection_partial'),

    # Endpoint for Excel export
    path('export-report/', ExportReportView.as_view(), name='export_report'),
]

```
**Add `inventory_reports.urls` to your project's main `urls.py`:**

```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('inventory/', include('inventory_reports.urls')), # Map the new app
    # Add other project paths
]
```

### 4.6 Tests (`inventory_reports/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date

# Mock the database views and their data
# In a real scenario, you'd set up a test database or use Django's fixtures
# For this example, we'll mock the querysets returned by the models.

mock_mrs_data = [
    {
        'item_code': 'ITEM001', 'description': 'Description 1', 'uom': 'KG', 'mrs_date': '01-01-2023',
        'mrs_no': 'MRS001', 'bg_group': 'BG1', 'wo_no': 'WO001', 'req_qty': 10.0, 'remarks': 'Test1',
        'gen_by': 'UserA', 'min_date': '01-02-2023', 'min_no': 'MIN001', 'issue_qty': 8.0,
        'gen_by2': 'UserB', 'comp_id': 1, 'bg_id': 10, 'emp_id': 'EMP001'
    },
    {
        'item_code': 'ITEM002', 'description': 'Description 2', 'uom': 'PCS', 'mrs_date': '01-03-2023',
        'mrs_no': 'MRS002', 'bg_group': 'BG2', 'wo_no': 'WO002', 'req_qty': 5.0, 'remarks': 'Test2',
        'gen_by': 'UserC', 'min_date': '01-04-2023', 'min_no': 'MIN002', 'issue_qty': 5.0,
        'gen_by2': 'UserD', 'comp_id': 1, 'bg_id': 20, 'emp_id': 'EMP002'
    },
]

mock_min_data = [
    {
        'item_code': 'ITEM003', 'description': 'Description 3', 'uom': 'MTR', 'min_date': '02-01-2023',
        'min_no': 'MIN003', 'issue_qty': 15.0, 'emp_name': 'UserE', 'mrs_date': '01-30-2023',
        'mrs_no': 'MRS003', 'wo_no': 'WO003', 'bg_group': 'BG3', 'req_qty': 20.0, 'remarks': 'Test3',
        'emp_name2': 'UserF', 'comp_id': 1, 'bg_id': 30, 'emp_id': 'EMP003'
    },
]


class ReportManagerTest(TestCase):
    @patch('inventory_reports.models.MrsItem.objects')
    @patch('inventory_reports.models.MinItem.objects')
    def test_get_mrs_report_data(self, MockMinItemObjects, MockMrsItemObjects):
        # Mocking MrsItem.objects.all().values().order_by()
        mock_queryset = MagicMock()
        mock_queryset.values.return_value = mock_queryset # Allow chaining .values().order_by()
        mock_queryset.order_by.return_value = mock_mrs_data # Return actual data for iteration
        MockMrsItemObjects.all.return_value = mock_queryset

        selected_columns = [
            'sr_no', 'item_code', 'mrs_date', 'mrs_no', 'min_no_mrs_rel', 'issue_qty_mrs_rel'
        ]
        filter_params = {'comp_id': 1}
        
        data, headers, db_fields_fetched = ReportManager.get_report_data(1, selected_columns, filter_params)
        
        self.assertGreater(len(data), 0)
        self.assertEqual(headers, ['Sr No', 'Item Code', 'Date', 'MRS No', 'MIN No', 'Issue Qty (MIN)'])
        self.assertEqual(data[0]['Sr No'], 1)
        self.assertEqual(data[0]['Item Code'], 'ITEM001')
        self.assertEqual(data[0]['Date'], '01/01/2023') # MRS Date formatted
        self.assertEqual(data[0]['MIN No'], 'MIN001')
        self.assertEqual(data[0]['Issue Qty (MIN)'], 8.0) # MINQty from MrsItem

        # Test filtering
        mock_queryset_filtered = MagicMock()
        mock_queryset_filtered.values.return_value = mock_queryset_filtered
        mock_queryset_filtered.order_by.return_value = [mock_mrs_data[0]] # Simulate filtered data
        MockMrsItemObjects.filter.return_value = mock_queryset_filtered

        filter_params_specific = {'comp_id': 1, 'mrs_no_param': 'MRS001'}
        data_filtered, _, _ = ReportManager.get_report_data(1, selected_columns, filter_params_specific)
        self.assertEqual(len(data_filtered), 1)
        self.assertEqual(data_filtered[0]['MRS No'], 'MRS001')
        MockMrsItemObjects.filter.assert_called_once_with(comp_id=1, mrs_no='MRS001')


    @patch('inventory_reports.models.MrsItem.objects')
    @patch('inventory_reports.models.MinItem.objects')
    def test_get_min_report_data(self, MockMinItemObjects, MockMrsItemObjects):
        # Mocking MinItem.objects.all().values().order_by()
        mock_queryset = MagicMock()
        mock_queryset.values.return_value = mock_queryset
        mock_queryset.order_by.return_value = mock_min_data
        MockMinItemObjects.all.return_value = mock_queryset

        selected_columns = [
            'sr_no', 'item_code', 'min_date', 'min_no', 'issue_qty', 'emp_name', 'mrs_no_min_rel'
        ]
        filter_params = {'comp_id': 1}
        
        data, headers, db_fields_fetched = ReportManager.get_report_data(2, selected_columns, filter_params)

        self.assertGreater(len(data), 0)
        self.assertEqual(headers, ['Sr No', 'Item Code', 'Date', 'MIN No', 'Issue Qty', 'Generated By', 'MRS No'])
        self.assertEqual(data[0]['Sr No'], 1)
        self.assertEqual(data[0]['Item Code'], 'ITEM003')
        self.assertEqual(data[0]['Date'], '01/02/2023') # MIN Date formatted
        self.assertEqual(data[0]['MRS No'], 'MRS003')

    def test_date_formatting(self):
        self.assertEqual(MrsItem._format_date_for_display('01-01-2023'), '01/01/2023')
        self.assertEqual(MrsItem._format_date_for_display('12-25-2022'), '25/12/2022') # Test DD-MM-YYYY format
        self.assertEqual(MrsItem._format_date_for_display(''), '')
        self.assertEqual(MrsItem._format_date_for_display(None), '')

        self.assertEqual(MrsItem._format_date_for_db_query(date(2023, 1, 1)), '01-01-2023')
        self.assertEqual(MrsItem._format_date_for_db_query('2023-01-01'), '01-01-2023')
        self.assertEqual(MrsItem._format_date_for_db_query('01/01/2023'), '01-01-2023')

class ReportViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('inventory_reports:report_search_view')
        self.table_partial_url = reverse('inventory_reports:report_table_partial')
        self.export_url = reverse('inventory_reports:export_report')
        self.client.session['compid'] = 1 # Mock session compid

    def test_report_search_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/report_search.html')
        self.assertIsInstance(response.context['form'], ReportFilterForm)
        self.assertEqual(response.context['report_type'], '1') # Default MRS

        # Test with query params
        response = self.client.get(f"{self.list_url}?MRSType=2&ICode=TEST123&FDateMRS=2023-01-01")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['form'].initial['item_code_param'], 'TEST123')
        self.assertEqual(response.context['report_type'], '2') # Changed to MIN

    @patch('inventory_reports.models.ReportManager.get_report_data')
    def test_report_table_partial_post(self, mock_get_report_data):
        mock_get_report_data.return_value = (
            [{'Sr No': 1, 'Item Code': 'ITEM001'}],
            ['Sr No', 'Item Code'],
            ['item_code']
        )
        
        post_data = {
            'report_type': '1',
            'selected_columns': ['sr_no', 'item_code'],
            'comp_id': '1', # As hidden input
            'mrs_no_param': '', 'item_code_param': '', 'item_category_param': '',
            'emp_id_param': '', 'bg_group_param': '', 'wo_no_param': '',
            'date_type': 'mrs', 'from_date_param': '', 'to_date_param': '',
        }
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX request
        response = self.client.post(self.table_partial_url, post_data, **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/_report_table.html')
        self.assertIn(b'ITEM001', response.content)
        mock_get_report_data.assert_called_once()
        self.assertTrue('report_filters' in self.client.session)
        self.assertTrue('selected_report_columns' in self.client.session)

    @patch('inventory_reports.models.ReportManager.get_report_data')
    def test_export_report_view(self, mock_get_report_data):
        mock_get_report_data.return_value = (
            [{'Sr No': 1, 'Item Code': 'ITEMX'}],
            ['Sr No', 'Item Code'],
            ['item_code']
        )
        
        # Simulate session state after generating a report
        session = self.client.session
        session['report_filters'] = {'comp_id': 1}
        session['selected_report_columns'] = ['sr_no', 'item_code']
        session['current_report_type'] = 1
        session.save()

        response = self.client.get(self.export_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertIn('attachment; filename="report_', response['Content-Disposition'])

    def test_export_report_view_no_data(self):
        # Simulate no session state or empty selection
        session = self.client.session
        if 'report_filters' in session: del session['report_filters']
        if 'selected_report_columns' in session: del session['selected_report_columns']
        session.save()

        response = self.client.get(self.export_url)
        self.assertEqual(response.status_code, 400) # Or 200 with no data message

    def test_form_validation_date_range(self):
        post_data = {
            'report_type': '1',
            'date_type': 'mrs',
            'from_date_param': '2023-01-05',
            'to_date_param': '2023-01-01', # Invalid date range
            'selected_columns': ['item_code'],
            'comp_id': '1',
        }
        form = ReportFilterForm(post_data)
        self.assertFalse(form.is_valid())
        self.assertIn('from_date_param', form.errors)
        self.assertIn('To Date cannot be before From Date.', form.errors['to_date_param'][0])
```

## Step 5: HTMX and Alpine.js Integration

The provided Django code includes HTMX attributes (`hx-post`, `hx-target`, `hx-swap`, `hx-get`, `hx-trigger`, `hx-vals`) for dynamic form submissions and partial content loading. Alpine.js is used for the `Check All` checkbox logic.

*   **Dynamic Report Table:** The `report-filter-form` submits via `hx-post` to `report_table_partial`, updating `#report-table-container` without a full page reload.
*   **Dynamic Column Checkboxes:** The `report_type` radio buttons trigger an `hx-get` to `column_selection_partial` to reload the `_column_selection.html` partial, updating the available checkboxes.
*   **DataTables:** JavaScript in `report_search.html` listens for `htmx:afterSwap` on the table container to re-initialize DataTables, ensuring client-side sorting, searching, and pagination.
*   **Modals:** While the original ASP.NET did not show modals, the provided `MODEL_NAME_LOWER` template examples use `modal` divs. This pattern is supported by HTMX/Alpine.js for future needs (e.g., if "View Details" were added). For this report, no explicit modals are needed or generated.
*   **`Check All`:** Alpine.js (`x-data="columnCheckboxes"`) handles the logic for the `checkAll` checkbox to select/deselect all `selected_columns` checkboxes.

## Final Notes

*   **Database Connection:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database (e.g., using `mssql-django` or `pyodbc`). The `managed = False` in models ensures Django does not attempt to create or modify these existing database views.
*   **User Session:** The `comp_id` is retrieved from `Session["compid"]` in ASP.NET. In Django, this should be managed via `request.session.get('compid')` after a user authenticates or sets their company context. For testing, it's hardcoded.
*   **`clsFunctions.FromDate`:** This function is replaced by `datetime` parsing and `_format_date_for_db_query` in the `ReportBase` model.
*   **Error Handling:** The ASP.NET code uses `try-catch` blocks that silently `catch (Exception ex) { }`. Django's `messages` framework and form validation are used for better user feedback, and uncaught exceptions will propagate, which is a more robust approach for debugging and production monitoring.
*   **Tailwind CSS:** The generated HTML templates include Tailwind CSS classes, assuming it's set up in your Django project.
*   **Scalability for Large Data:** For very large datasets, DataTables can use server-side processing mode. This would require the `ReportTablePartialView` to return JSON in a specific DataTables format (`hx-post="/api/report-data/"` and use `hx-target="#report-table"` where `#report-table` is the DataTable HTML, not a partial for it). However, for many typical reports, client-side DataTables with HTMX is sufficient and simpler.
*   **Refinement of Column Definitions:** The `REPORT_COLUMNS_METADATA` is a static list. In a real application, if these column sets change frequently, they might be stored in a configuration file or even a Django model for dynamic management via an admin interface. For this migration, the current approach directly mirrors ASP.NET's hardcoded logic.