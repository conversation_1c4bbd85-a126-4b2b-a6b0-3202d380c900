## ASP.NET to Django Conversion Script: ABC Analysis Report

This modernization plan focuses on transforming your legacy ASP.NET ABC Analysis report form into a robust, modern Django application. By leveraging Django's "fat model, thin view" pattern, HTMX for dynamic interactions, and Alpine.js for lightweight frontend logic, we will achieve a highly responsive and maintainable solution. Our approach prioritizes automation-driven conversion, ensuring a systematic transition with clear, actionable steps.

### Business Value of this Modernization:

*   **Enhanced User Experience:** Moving from traditional postbacks to HTMX provides a smoother, more responsive interface, akin to a single-page application without the complexity. Users will experience faster form submissions and validation feedback.
*   **Improved Maintainability & Scalability:** Django's structured architecture separates concerns clearly. Business logic resides in models, making your code easier to understand, test, and expand. This reduces the effort required for future enhancements and bug fixes.
*   **Reduced Development Costs:** By adopting modern, widely supported open-source technologies like Django, HTMX, and Alpine.js, you reduce reliance on proprietary frameworks and expand your talent pool, potentially lowering long-term development and maintenance expenses.
*   **Future-Proofing:** Legacy ASP.NET Web Forms can be challenging to integrate with modern web patterns. Django provides a solid foundation for future growth, including API development, mobile integration, and cloud deployment.
*   **Better Performance:** Optimized database queries and efficient server-side rendering (with partial updates via HTMX) lead to faster page loads and a more performant application overall.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination (where applicable, for report output)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns used by the ASP.NET code.

**Instructions:**
From the C# code-behind, we observe SQL queries interacting with the following tables:

*   `tblFinancial_master`: Used to retrieve financial year `From` and `To` dates based on `CompId` and `FinYearId`.
    *   **Inferred Columns:** `FinYearId` (PK), `CompId` (FK to a company table), `FinYearFrom` (date), `FinYearTo` (date).
*   `tblDG_Category_Master`: Used to populate the `DrpCategory` dropdown with category IDs and names.
    *   **Inferred Columns:** `CId` (PK), `Symbol` (string), `CName` (string).

### Step 2: Identify Backend Functionality

**Task:** Determine the operations and business logic within the ASP.NET code.

**Instructions:**

*   **Read (Data Population):**
    *   On page load, fetches the current financial year's start and end dates from `tblFinancial_master` using `CompId` and `FinYearId` from the user's session.
    *   Populates a dropdown list (`DrpCategory`) with categories from `tblDG_Category_Master`.
*   **Processing & Validation (Report Parameters):**
    *   When the "Proceed" button (`BtnView`) is clicked, it collects:
        *   Report "From Date" and "To Date".
        *   Selected "Category".
        *   Selected "PO Rate" option (Max, Min, Average, Latest).
        *   ABC Class percentages (A, B, C).
    *   **Validation Rules:**
        *   All percentage inputs (A, B, C) must be valid numbers.
        *   The sum of A, B, and C percentages must exactly equal 100%.
        *   "To Date" must be greater than or equal to "From Date".
        *   "From Date" must not be less than the Financial Year's "Opening Date".
        *   Both "From Date" and "To Date" must be valid dates.
*   **Navigation (Report Generation Trigger):**
    *   Upon successful validation, redirects to `ABCAnalysis_Details.aspx`, passing all collected and validated parameters (category ID, rate value, from date, to date, opening date, A, B, C percentages, and a random key) as URL-encoded and encrypted query string parameters.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their intended user interaction and roles.

**Instructions:**

*   **Display Labels:** `lblFromDate`, `lblToDate` display the financial year range.
*   **Date Inputs:** `Txtfromdate`, `TxtTodate` for selecting report date range. They include `CalendarExtender` for a date picker.
*   **Dropdown List:** `DrpCategory` for selecting a category, populated dynamically.
*   **Radio Button List:** `RadRate` for selecting a PO rate type (Max, Min, Average, Latest).
*   **Text Inputs (Numeric):** `TxtboxA`, `TxtboxB`, `TxtboxC` for entering percentage values, with default values and validation.
*   **Action Button:** `BtnView` triggers form submission and validation.
*   **Message Display:** `lblMessage` for showing validation errors or success messages.
*   **Styling:** Uses CSS classes like `box3`, `cal_Theme2`, `fontcsswhite`, `redbox`. These will be translated to Tailwind CSS classes.
*   **Client-side JS:** `loadingNotifier.js` suggests a loading indicator, which Alpine.js can manage. `StyleSheet.css` for general styling.

### Step 4: Generate Django Code

Given that this page is primarily a form for report parameters rather than a direct CRUD operation on a single model, we'll adapt the structure to best fit Django's paradigms. We will create two models for lookup data and a custom form for the report parameters.

**Application Name:** `inventory_reports`

#### 4.1 Models

**Task:** Create Django models for lookup data used by the form. Business logic specific to financial years or categories will be added as model methods.

**Instructions:**
-   `FinancialMaster` for `tblFinancial_master`.
-   `CategoryMaster` for `tblDG_Category_Master`.
-   Define `managed = False` and `db_table` to map to existing tables.

**`inventory_reports/models.py`**

```python
from django.db import models
from datetime import date

class FinancialMaster(models.Model):
    """
    Maps to tblFinancial_master to retrieve financial year details.
    """
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    compid = models.IntegerField(db_column='CompId') # Assuming CompId is an integer
    finyearfrom = models.DateField(db_column='FinYearFrom')
    finyearto = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"FY {self.finyearfrom.year}-{self.finyearto.year}"

    @classmethod
    def get_current_financial_year(cls, comp_id, fin_year_id):
        """
        Retrieves the financial year details for a given company and financial year ID.
        In a real application, you might infer fin_year_id based on current date or session.
        """
        try:
            return cls.objects.get(compid=comp_id, finyearid=fin_year_id)
        except cls.DoesNotExist:
            return None

class CategoryMaster(models.Model):
    """
    Maps to tblDG_Category_Master to retrieve product categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}"

    @classmethod
    def get_all_categories(cls):
        """
        Retrieves all available categories for the dropdown.
        """
        return cls.objects.all().order_by('cname')

    # Example of a business logic method if needed, e.g., to filter items by category
    def get_items_in_category(self):
        # Placeholder for actual logic, assuming an Item model exists
        # return Item.objects.filter(category=self)
        return []

```

#### 4.2 Forms

**Task:** Define a Django form for the ABC Analysis report parameters, including custom validation logic.

**Instructions:**
-   Create a plain `forms.Form` since it's not directly creating/updating a model instance.
-   Include all fields identified in the ASP.NET page.
-   Implement `clean()` methods for complex validations (date ranges, sum of percentages).
-   Add appropriate widgets with Tailwind CSS classes.

**`inventory_reports/forms.py`**

```python
from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import FinancialMaster, CategoryMaster

class ABCAnalysisForm(forms.Form):
    """
    Form for collecting parameters for the ABC Analysis report.
    """
    from_date = forms.DateField(
        label="From Date",
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input type
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-data': '', # For Alpine.js integration if needed (e.g., date picker)
            'x-init': 'new Pikaday({ field: $el, format: "DD-MM-YYYY" })' # Example with Pikaday
        }, format='%Y-%m-%d'),
        initial=timezone.now().date(),
        error_messages={'required': 'From Date is required.'}
    )
    to_date = forms.DateField(
        label="To Date",
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input type
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-data': '',
            'x-init': 'new Pikaday({ field: $el, format: "DD-MM-YYYY" })'
        }, format='%Y-%m-%d'),
        initial=timezone.now().date(),
        error_messages={'required': 'To Date is required.'}
    )
    category = forms.ModelChoiceField(
        queryset=CategoryMaster.get_all_categories(),
        empty_label="Select",
        label="Category",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-10',
            'hx-post': 'hx-post', # Example for htmx, though actual post is on submit
            'hx-target': '#abc-analysis-form-container', # Example for partial update, remove if not dynamic
            'hx-swap': 'outerHTML' # Example for partial update
        }),
        required=False # ASP.NET code commented out RequiredFieldValidator for category
    )
    po_rate = forms.ChoiceField(
        label="PO Rate",
        choices=[
            ('0', 'Max'),
            ('1', 'Min'),
            ('2', 'Average'),
            ('3', 'Latest'),
        ],
        widget=forms.RadioSelect(attrs={
            'class': 'space-x-4 flex mt-2' # Tailwind classes for horizontal radio buttons
        }),
        initial='0' # Selected="True" in ASP.NET
    )
    class_a_percentage = forms.DecimalField(
        label="A",
        min_value=0,
        max_value=100,
        decimal_places=2, # ASP.NET allows up to 3 decimal places for parsing, but usually 2 for percentages
        initial=70.00,
        widget=forms.NumberInput(attrs={
            'class': 'inline-block w-20 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'step': '0.01'
        }),
        error_messages={'required': 'Class A percentage is required.'}
    )
    class_b_percentage = forms.DecimalField(
        label="B",
        min_value=0,
        max_value=100,
        decimal_places=2,
        initial=20.00,
        widget=forms.NumberInput(attrs={
            'class': 'inline-block w-20 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'step': '0.01'
        }),
        error_messages={'required': 'Class B percentage is required.'}
    )
    class_c_percentage = forms.DecimalField(
        label="C",
        min_value=0,
        max_value=100,
        decimal_places=2,
        initial=10.00,
        widget=forms.NumberInput(attrs={
            'class': 'inline-block w-20 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'step': '0.01'
        }),
        error_messages={'required': 'Class C percentage is required.'}
    )

    def __init__(self, *args, **kwargs):
        self.comp_id = kwargs.pop('comp_id', None)
        self.fin_year_id = kwargs.pop('fin_year_id', None)
        self.financial_year = None
        if self.comp_id and self.fin_year_id:
            self.financial_year = FinancialMaster.get_current_financial_year(self.comp_id, self.fin_year_id)
        super().__init__(*args, **kwargs)

        if self.financial_year:
            self.fields['from_date'].initial = self.financial_year.finyearfrom
            self.fields['to_date'].initial = self.financial_year.finyearto
            # Update widget attrs to display initial value from financial year
            self.fields['from_date'].widget.attrs['value'] = self.financial_year.finyearfrom.strftime('%Y-%m-%d')
            self.fields['to_date'].widget.attrs['value'] = self.financial_year.finyearto.strftime('%Y-%m-%d')


    def clean(self):
        """
        Performs cross-field validation for dates and percentages.
        Corresponds to BtnView_Click logic in C#.
        """
        cleaned_data = super().clean()

        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        class_a = cleaned_data.get('class_a_percentage')
        class_b = cleaned_data.get('class_b_percentage')
        class_c = cleaned_data.get('class_c_percentage')

        # Date validations
        if from_date and to_date:
            if from_date > to_date:
                self.add_error('from_date', 'From date should be Less than or Equal to To Date!')
                self.add_error('to_date', 'To date should be Greater than or Equal to From Date!')
            if self.financial_year and from_date < self.financial_year.finyearfrom:
                self.add_error('from_date', f"From date should not be Less than Opening Date ({self.financial_year.finyearfrom.strftime('%d-%m-%Y')})!")

        # Percentage validation
        if class_a is not None and class_b is not None and class_c is not None:
            total_percentage = class_a + class_b + class_c
            # Using a small tolerance for floating point comparison
            if abs(total_percentage - 100.00) > 0.01:
                raise ValidationError("Total Percentage should be 100%")

        return cleaned_data

```

#### 4.3 Views

**Task:** Implement Django Class-Based Views to handle the form rendering, data retrieval, and submission logic.

**Instructions:**
-   Use `FormView` for the main `ABCAnalysisView` to manage the `ABCAnalysisForm`.
-   Retrieve `CompId` and `FinYearId` from session (simulate `Session["compid"]` and `Session["finyear"]`).
-   Pass financial year dates to the template for display.
-   Handle form submission, perform validation, and redirect.
-   Create a simple `TemplateView` for `ABCAnalysisDetailsView` as the report target.

**`inventory_reports/views.py`**

```python
from django.views.generic import FormView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import redirect
import secrets # For GetRandomAlphaNumeric equivalent

from .forms import ABCAnalysisForm
from .models import FinancialMaster

class ABCAnalysisView(FormView):
    """
    Handles the display and submission of the ABC Analysis report parameters form.
    Corresponds to ABCAnalysis.aspx.cs Page_Load and BtnView_Click.
    """
    template_name = 'inventory_reports/abc_analysis_form.html'
    form_class = ABCAnalysisForm
    # success_url will be dynamically generated in form_valid

    def get_form_kwargs(self):
        """
        Passes session data (comp_id, fin_year_id) to the form.
        """
        kwargs = super().get_form_kwargs()
        # Simulate session variables as seen in ASP.NET code
        # In a real Django app, user authentication and associated profiles would provide these.
        kwargs['comp_id'] = self.request.session.get('compid', 1)  # Defaulting to 1 for example
        kwargs['fin_year_id'] = self.request.session.get('finyear', 1) # Defaulting to 1 for example
        return kwargs

    def get_context_data(self, **kwargs):
        """
        Adds financial year details to the template context.
        Corresponds to setting lblFromDate and lblToDate.
        """
        context = super().get_context_data(**kwargs)
        form_kwargs = self.get_form_kwargs()
        financial_year = FinancialMaster.get_current_financial_year(
            form_kwargs['comp_id'], form_kwargs['fin_year_id']
        )
        context['financial_year'] = financial_year
        context['page_title'] = 'ABC Analysis Report'
        return context

    def form_valid(self, form):
        """
        Handles valid form submission. Redirects to the details page with parameters.
        Corresponds to the successful path within BtnView_Click.
        """
        # Parameters to pass to the report details page
        params = {
            'cid': form.cleaned_data.get('category').cid if form.cleaned_data.get('category') else 0,
            'radval': form.cleaned_data['po_rate'],
            'fdate': form.cleaned_data['from_date'].strftime('%d-%m-%Y'),
            'tdate': form.cleaned_data['to_date'].strftime('%d-%m-%Y'),
            'a': form.cleaned_data['class_a_percentage'],
            'b': form.cleaned_data['class_b_percentage'],
            'c': form.cleaned_data['class_c_percentage'],
            'key': secrets.token_urlsafe(16) # Equivalent to GetRandomAlphaNumeric
        }

        # The ASP.NET code encrypts params and puts them in URL.
        # For simplicity and clean URLs, it's often better to store complex data in session
        # or a temporary database entry and pass only an ID in the URL.
        # However, to directly mimic the ASP.NET behavior of passing many params:
        query_string = '&'.join([f"{key}={value}" for key, value in params.items()])
        
        # In a real app, if parameters are large or sensitive, use session:
        # self.request.session['abc_report_params'] = params
        # return redirect(reverse_lazy('inventory_reports:abc_analysis_details'))
        
        # Mimicking ASP.NET query string redirect
        redirect_url = reverse_lazy('inventory_reports:abc_analysis_details') + f'?{query_string}'

        messages.success(self.request, "ABC Analysis parameters submitted successfully. Generating report...")
        
        # HTMX re-renders the form with success message or redirects if needed.
        # Here we instruct HTMX to redirect the client, similar to Response.Redirect
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})
        
        return redirect(redirect_url)

    def form_invalid(self, form):
        """
        Handles invalid form submission. Re-renders the form with error messages.
        Corresponds to setting lblMessage.Text in C# for validation failures.
        """
        messages.error(self.request, "Please correct the errors below.")
        
        # If HTMX request, re-render only the form part with errors.
        if self.request.headers.get('HX-Request'):
            # The template itself should contain a conditional for errors
            # We don't want to just return a 200, we want to re-render the form.
            # HTMX will swap the updated form HTML back into the container.
            return self.render_to_response(self.get_context_data(form=form))
        
        return super().form_invalid(form)


class ABCAnalysisDetailsView(TemplateView):
    """
    Placeholder for the ABC Analysis report details page.
    Corresponds to ABCAnalysis_Details.aspx.
    """
    template_name = 'inventory_reports/abc_analysis_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Access parameters from request.GET
        context['report_params'] = {
            'category_id': self.request.GET.get('cid'),
            'po_rate_val': self.request.GET.get('radval'),
            'from_date_str': self.request.GET.get('fdate'),
            'to_date_str': self.request.GET.get('tdate'),
            'class_a': self.request.GET.get('a'),
            'class_b': self.request.GET.get('b'),
            'class_c': self.request.GET.get('c'),
            'key': self.request.GET.get('key')
        }
        # In a real report, you would fetch and process data here
        # Example: inventory_data = SomeManager.generate_report(context['report_params'])
        # context['inventory_data'] = inventory_data

        context['page_title'] = 'ABC Analysis Report Details'
        # DataTables would typically be initialized on the report data table in this template.
        return context

```

#### 4.4 Templates

**Task:** Create templates for the report form and the report details page. Use HTMX for dynamic interactions and extend the base template for consistency.

**Instructions:**
-   `abc_analysis_form.html`: Main form, extends `core/base.html`. Includes the form fields and a submit button.
-   `_abc_analysis_message.html`: A partial template to display messages.
-   `abc_analysis_details.html`: Placeholder for the report output.

**`inventory_reports/templates/inventory_reports/abc_analysis_form.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-center items-center mb-6">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-2xl">
            <h2 class="text-2xl font-bold text-center text-gray-800 mb-6 py-2 bg-gradient-to-r from-blue-600 to-blue-800 text-white rounded-t-lg">
                ABC Analysis
            </h2>

            <!-- Message Area (equivalent to lblMessage) -->
            <div id="message-container" class="mb-4">
                {% if messages %}
                    {% for message in messages %}
                        <div class="p-3 mb-3 rounded-md text-sm {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            </div>

            <form hx-post="{% url 'inventory_reports:abc_analysis_form' %}"
                  hx-target="#abc-analysis-form-container"
                  hx-swap="outerHTML"
                  id="abc-analysis-form-container">
                {% csrf_token %}
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <label class="w-1/3 text-left text-sm font-medium text-gray-700">Financial Year</label>
                        <div class="w-2/3 text-left text-base font-semibold text-gray-800">
                            From Date : <span id="lblFromDate">{{ financial_year.finyearfrom|date:"d-m-Y" }}</span>
                            &nbsp;To : <span id="lblToDate">{{ financial_year.finyearto|date:"d-m-Y" }}</span>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <label for="{{ form.from_date.id_for_label }}" class="w-1/3 text-left text-sm font-medium text-gray-700">From Date</label>
                        <div class="w-2/3 flex items-center space-x-2">
                            {{ form.from_date }}
                            <span class="text-red-500 text-xs">{{ form.from_date.errors }}</span>
                            <span> - </span>
                            <label for="{{ form.to_date.id_for_label }}" class="sr-only">To Date</label>
                            {{ form.to_date }}
                            <span class="text-red-500 text-xs">{{ form.to_date.errors }}</span>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <label for="{{ form.category.id_for_label }}" class="w-1/3 text-left text-sm font-medium text-gray-700">Category</label>
                        <div class="w-2/3">
                            {{ form.category }}
                            <span class="text-red-500 text-xs mt-1">{{ form.category.errors }}</span>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <label class="w-1/3 text-left text-sm font-medium text-gray-700">PO Rate</label>
                        <div class="w-2/3">
                            {{ form.po_rate }}
                            <span class="text-red-500 text-xs mt-1">{{ form.po_rate.errors }}</span>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <label class="w-1/3 text-left text-sm font-medium text-gray-700">Class</label>
                        <div class="w-2/3 flex items-center space-x-2">
                            <span class="font-bold">A :</span>
                            {{ form.class_a_percentage }} %
                            <span class="text-red-500 text-xs">{{ form.class_a_percentage.errors }}</span>
                            
                            <span class="font-bold">B :</span>
                            {{ form.class_b_percentage }} %
                            <span class="text-red-500 text-xs">{{ form.class_b_percentage.errors }}</span>
                            
                            <span class="font-bold">C :</span>
                            {{ form.class_c_percentage }} %
                            <span class="text-red-500 text-xs">{{ form.class_c_percentage.errors }}</span>
                        </div>
                    </div>
                    {% if form.non_field_errors %}
                        <div class="text-red-500 text-sm mt-2">
                            {% for error in form.non_field_errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mt-8 flex justify-center space-x-4">
                    <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out"
                            hx-indicator="#loading-spinner"
                            hx-on--after-request="if(event.detail.successful) { document.body.dispatchEvent(new CustomEvent('messages', { detail: { messages: event.detail.xhr.getResponseHeader('HX-Trigger') } })); }">
                        Proceed
                    </button>
                    <!-- Loading Spinner -->
                    <div id="loading-spinner" class="htmx-indicator ml-3">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <p class="mt-1 text-gray-600 text-sm">Processing...</p>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/pikaday/pikaday.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/pikaday/css/pikaday.css">

<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed for this simple form beyond what HTMX does.
        // Pikaday integration is handled by the x-init on the date inputs.
    });
</script>
{% endblock %}

```

**`inventory_reports/templates/inventory_reports/abc_analysis_details.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">ABC Analysis Report Details</h2>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Report Parameters:</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <p><strong>Category ID:</strong> {{ report_params.category_id }}</p>
            <p><strong>PO Rate Value:</strong> {{ report_params.po_rate_val }}</p>
            <p><strong>From Date:</strong> {{ report_params.from_date_str }}</p>
            <p><strong>To Date:</strong> {{ report_params.to_date_str }}</p>
            <p><strong>Class A %:</strong> {{ report_params.class_a }}</p>
            <p><strong>Class B %:</strong> {{ report_params.class_b }}</p>
            <p><strong>Class C %:</strong> {{ report_params.class_c }}</p>
            <p><strong>Key:</strong> {{ report_params.key }}</p>
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Report Data:</h3>
        <p class="text-gray-600 mb-4">
            <!-- This is where the actual report data would be loaded and displayed. -->
            <!-- For large datasets, this would be populated via a separate HTMX endpoint -->
            <!-- returning a partial template with a DataTable. -->
            This section would typically display the generated ABC Analysis report, perhaps in a sortable, filterable table using DataTables.
            Example:
            <div id="report-data-table-container">
                <table id="abcAnalysisDataTable" class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Name</th>
                            <th class="py-2 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Usage Value</th>
                            <th class="py-2 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Cumulative %</th>
                            <th class="py-2 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">ABC Class</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data rows loaded via HTMX from another endpoint -->
                        <tr><td>Example Item 1</td><td>$10,000</td><td>60%</td><td>A</td></tr>
                        <tr><td>Example Item 2</td><td>$1,000</td><td>80%</td><td>B</td></tr>
                        <tr><td>Example Item 3</td><td>$100</td><td>95%</td><td>C</td></tr>
                    </tbody>
                </table>
            </div>
        </p>
        <button onclick="history.back()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mt-4">
            Back to Report Selection
        </button>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTables if report data is loaded here
    // $(document).ready(function() {
    //     $('#abcAnalysisDataTable').DataTable({
    //         "pageLength": 10,
    //         "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    //     });
    // });
</script>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns to map incoming requests to the appropriate Django views.

**Instructions:**
-   Create a URL for the ABC Analysis form (`abc_analysis_form`).
-   Create a URL for the ABC Analysis report details page (`abc_analysis_details`).

**`inventory_reports/urls.py`**

```python
from django.urls import path
from .views import ABCAnalysisView, ABCAnalysisDetailsView

app_name = 'inventory_reports' # Namespace for URLs

urlpatterns = [
    path('abc-analysis/', ABCAnalysisView.as_view(), name='abc_analysis_form'),
    path('abc-analysis/details/', ABCAnalysisDetailsView.as_view(), name='abc_analysis_details'),
    # If the report data itself needs to be loaded dynamically via HTMX,
    # you might add a path like:
    # path('abc-analysis/data/', ABCAnalysisDataPartialView.as_view(), name='abc_analysis_data'),
]

```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the models and form, and integration tests for the views.

**Instructions:**
-   Test model methods and data retrieval.
-   Test form validation, including custom `clean()` methods.
-   Test view rendering, form submission (both valid and invalid), and redirects.
-   Include tests for HTMX interactions, verifying appropriate HTTP headers and content.

**`inventory_reports/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from .models import FinancialMaster, CategoryMaster
from .forms import ABCAnalysisForm

class FinancialMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.comp_id = 1
        cls.fin_year_id = 1
        cls.fin_year_from = date(2023, 4, 1)
        cls.fin_year_to = date(2024, 3, 31)
        FinancialMaster.objects.create(
            finyearid=cls.fin_year_id,
            compid=cls.comp_id,
            finyearfrom=cls.fin_year_from,
            finyearto=cls.fin_year_to
        )
        # Create another financial year for negative test cases
        FinancialMaster.objects.create(
            finyearid=2,
            compid=cls.comp_id,
            finyearfrom=date(2022, 4, 1),
            finyearto=date(2023, 3, 31)
        )

    def test_financial_master_creation(self):
        fin_year = FinancialMaster.objects.get(finyearid=self.fin_year_id)
        self.assertEqual(fin_year.compid, self.comp_id)
        self.assertEqual(fin_year.finyearfrom, self.fin_year_from)
        self.assertEqual(fin_year.finyearto, self.fin_year_to)

    def test_get_current_financial_year_method(self):
        fin_year = FinancialMaster.get_current_financial_year(self.comp_id, self.fin_year_id)
        self.assertIsNotNone(fin_year)
        self.assertEqual(fin_year.finyearid, self.fin_year_id)
        
        # Test for non-existent financial year
        non_existent_fin_year = FinancialMaster.get_current_financial_year(self.comp_id, 999)
        self.assertIsNone(non_existent_fin_year)

    def test_str_representation(self):
        fin_year = FinancialMaster.objects.get(finyearid=self.fin_year_id)
        self.assertEqual(str(fin_year), f"FY {self.fin_year_from.year}-{self.fin_year_to.year}")

class CategoryMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data
        CategoryMaster.objects.create(cid=1, symbol='CAT1', cname='Category One')
        CategoryMaster.objects.create(cid=2, symbol='CAT2', cname='Category Two')

    def test_category_master_creation(self):
        category = CategoryMaster.objects.get(cid=1)
        self.assertEqual(category.symbol, 'CAT1')
        self.assertEqual(category.cname, 'Category One')

    def test_get_all_categories_method(self):
        categories = CategoryMaster.get_all_categories()
        self.assertEqual(categories.count(), 2)
        self.assertEqual(categories.first().cname, 'Category One') # Ordered by cname

    def test_str_representation(self):
        category = CategoryMaster.objects.get(cid=1)
        self.assertEqual(str(category), "[CAT1] - Category One")

class ABCAnalysisFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 1
        cls.fin_year_from = date(2023, 4, 1)
        cls.fin_year_to = date(2024, 3, 31)
        FinancialMaster.objects.create(
            finyearid=cls.fin_year_id,
            compid=cls.comp_id,
            finyearfrom=cls.fin_year_from,
            finyearto=cls.fin_year_to
        )
        cls.category = CategoryMaster.objects.create(cid=1, symbol='TST', cname='Test Category')

    def test_valid_form_submission(self):
        form_data = {
            'from_date': (self.fin_year_from + timedelta(days=30)).strftime('%Y-%m-%d'),
            'to_date': (self.fin_year_from + timedelta(days=60)).strftime('%Y-%m-%d'),
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '70.00',
            'class_b_percentage': '20.00',
            'class_c_percentage': '10.00',
        }
        form = ABCAnalysisForm(data=form_data, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        self.assertTrue(form.is_valid(), form.errors.as_data()) # Print errors if invalid

    def test_invalid_dates_from_after_to(self):
        form_data = {
            'from_date': (self.fin_year_from + timedelta(days=60)).strftime('%Y-%m-%d'),
            'to_date': (self.fin_year_from + timedelta(days=30)).strftime('%Y-%m-%d'),
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '70.00',
            'class_b_percentage': '20.00',
            'class_c_percentage': '10.00',
        }
        form = ABCAnalysisForm(data=form_data, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        self.assertFalse(form.is_valid())
        self.assertIn('From date should be Less than or Equal to To Date!', form.errors['from_date'][0])

    def test_invalid_dates_from_before_financial_year(self):
        form_data = {
            'from_date': (self.fin_year_from - timedelta(days=1)).strftime('%Y-%m-%d'),
            'to_date': self.fin_year_to.strftime('%Y-%m-%d'),
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '70.00',
            'class_b_percentage': '20.00',
            'class_c_percentage': '10.00',
        }
        form = ABCAnalysisForm(data=form_data, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        self.assertFalse(form.is_valid())
        self.assertIn('From date should not be Less than Opening Date!', form.errors['from_date'][0])

    def test_invalid_percentages_sum(self):
        form_data = {
            'from_date': (self.fin_year_from + timedelta(days=30)).strftime('%Y-%m-%d'),
            'to_date': (self.fin_year_from + timedelta(days=60)).strftime('%Y-%m-%d'),
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '70.00',
            'class_b_percentage': '20.00',
            'class_c_percentage': '20.00', # Sums to 110
        }
        form = ABCAnalysisForm(data=form_data, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        self.assertFalse(form.is_valid())
        self.assertIn('Total Percentage should be 100%', form.non_field_errors()[0])

    def test_percentages_with_decimals(self):
        form_data = {
            'from_date': (self.fin_year_from + timedelta(days=30)).strftime('%Y-%m-%d'),
            'to_date': (self.fin_year_from + timedelta(days=60)).strftime('%Y-%m-%d'),
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '69.50',
            'class_b_percentage': '20.50',
            'class_c_percentage': '10.00', # Sums to 100
        }
        form = ABCAnalysisForm(data=form_data, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        self.assertTrue(form.is_valid(), form.errors.as_data())

class ABCAnalysisViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 1
        cls.fin_year_from = date(2023, 4, 1)
        cls.fin_year_to = date(2024, 3, 31)
        FinancialMaster.objects.create(
            finyearid=cls.fin_year_id,
            compid=cls.comp_id,
            finyearfrom=cls.fin_year_from,
            finyearto=cls.fin_year_to
        )
        cls.category = CategoryMaster.objects.create(cid=1, symbol='TST', cname='Test Category')

    def setUp(self):
        self.client = Client()
        # Simulate session variables
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_abc_analysis_form_view_get(self):
        response = self.client.get(reverse('inventory_reports:abc_analysis_form'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/abc_analysis_form.html')
        self.assertIsInstance(response.context['form'], ABCAnalysisForm)
        self.assertEqual(response.context['financial_year'].finyearfrom, self.fin_year_from)

    def test_abc_analysis_form_view_post_valid_htmx(self):
        form_data = {
            'from_date': (self.fin_year_from + timedelta(days=30)).strftime('%Y-%m-%d'),
            'to_date': (self.fin_year_from + timedelta(days=60)).strftime('%Y-%m-%d'),
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '70.00',
            'class_b_percentage': '20.00',
            'class_c_percentage': '10.00',
        }
        # Simulate HTMX request
        response = self.client.post(
            reverse('inventory_reports:abc_analysis_form'),
            form_data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204) # HTMX success code for redirect
        self.assertIn('HX-Redirect', response.headers)
        self.assertTrue(reverse('inventory_reports:abc_analysis_details') in response.headers['HX-Redirect'])
        self.assertContains(response, 'ABC Analysis parameters submitted successfully', status_code=204)

    def test_abc_analysis_form_view_post_valid_standard(self):
        form_data = {
            'from_date': (self.fin_year_from + timedelta(days=30)).strftime('%Y-%m-%d'),
            'to_date': (self.fin_year_from + timedelta(days=60)).strftime('%Y-%m-%d'),
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '70.00',
            'class_b_percentage': '20.00',
            'class_c_percentage': '10.00',
        }
        response = self.client.post(
            reverse('inventory_reports:abc_analysis_form'),
            form_data
        )
        self.assertEqual(response.status_code, 302) # Standard redirect
        self.assertTrue(reverse('inventory_reports:abc_analysis_details') in response.url)

    def test_abc_analysis_form_view_post_invalid_htmx(self):
        form_data = {
            'from_date': (self.fin_year_from + timedelta(days=60)).strftime('%Y-%m-%d'),
            'to_date': (self.fin_year_from + timedelta(days=30)).strftime('%Y-%m-%d'), # Invalid date range
            'category': self.category.cid,
            'po_rate': '0',
            'class_a_percentage': '70.00',
            'class_b_percentage': '20.00',
            'class_c_percentage': '10.00',
        }
        response = self.client.post(
            reverse('inventory_reports:abc_analysis_form'),
            form_data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200) # HTMX renders content with errors
        self.assertTemplateUsed(response, 'inventory_reports/abc_analysis_form.html')
        self.assertContains(response, 'From date should be Less than or Equal to To Date!')
        self.assertContains(response, 'Please correct the errors below.')


    def test_abc_analysis_details_view_get(self):
        # Example query parameters for the details page
        query_params = {
            'cid': '1',
            'radval': '0',
            'fdate': '01-01-2024',
            'tdate': '31-01-2024',
            'a': '70',
            'b': '20',
            'c': '10',
            'key': 'randomkey123'
        }
        url = f"{reverse('inventory_reports:abc_analysis_details')}?" + "&".join([f"{k}={v}" for k, v in query_params.items()])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/abc_analysis_details.html')
        self.assertIn('report_params', response.context)
        self.assertEqual(response.context['report_params']['category_id'], '1')
        self.assertContains(response, 'ABC Analysis Report Details')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Form Submission:** The main form in `abc_analysis_form.html` uses `hx-post` to submit the form data to the same URL.
    *   `hx-target="#abc-analysis-form-container"`: This tells HTMX to replace the entire form container with the response from the server. This is crucial for displaying validation errors (if `form_invalid` is called) or a success message/redirect (if `form_valid` is called).
    *   `hx-swap="outerHTML"`: Replaces the entire element targeted, ensuring the `message-container` and form itself are updated.
    *   `hx-on--after-request`: A custom event handler (using Alpine.js or plain JS) could listen for the `HX-Redirect` header to perform client-side redirects, or for `HX-Trigger` to fire custom events for messages. In our `form_valid`, we explicitly set `HX-Redirect` for the client to follow.
    *   `hx-indicator="#loading-spinner"`: Provides a visual loading indicator while the HTMX request is in progress.
*   **Alpine.js for UI State:**
    *   `x-data` and `x-init` attributes on date input fields demonstrate how Alpine.js could be used to initialize third-party date pickers (like Pikaday.js, which is small and easy to integrate) directly in the HTML without complex JavaScript.
    *   For this form, Alpine.js's primary role is minimal, but it provides a foundation for more complex UI behaviors if required (e.g., toggling elements, reactive display based on input).
*   **DataTables for Report Output:** While the `ABCAnalysis` page is an input form, the `ABCAnalysisDetailsView` is where the report output would reside.
    *   The `abc_analysis_details.html` template includes a placeholder for a table.
    *   If the report data is large, you would typically use HTMX to fetch the table content (`_abc_analysis_data_table.html` partial) from a dedicated `abc_analysis_data` URL endpoint, and then initialize DataTables on that fetched content using `$(document).ready` within a `script` tag inside the partial, ensuring DataTables is re-applied every time the partial is swapped.
    *   The `urlpatterns` section (`abc_analysis_data` example) demonstrates how such an endpoint would be structured.

### Final Notes

*   **Placeholders:** Replace `[TABLE_NAME]`, `[MODEL_NAME]`, `[FIELD]` etc., with actual values derived from your ASP.NET application. This plan provides the structure and most of the code, but data-specific names and types require direct mapping.
*   **DRY Templates:** The use of `{% extends 'core/base.html' %}` and the concept of partial templates for reusable components (like a potential `_messages.html` or `_report_table.html`) ensures that your UI code is modular and easy to maintain.
*   **Business Logic in Models:** All complex validation logic from `BtnView_Click` has been moved to the `clean()` method of the `ABCAnalysisForm`. Any data fetching logic that's not a simple `get()` is handled by class methods in `FinancialMaster` and `CategoryMaster`. This keeps `ABCAnalysisView` concise and focused solely on handling the HTTP request and response.
*   **Comprehensive Tests:** The provided tests cover model creation, model methods, form validation paths (valid and invalid), and view interactions, including HTMX requests. This ensures high code quality and confidence in the migration.
*   **Security for URL Parameters:** The original ASP.NET code encrypts URL parameters. For a production Django application, for sensitive or large data, passing parameters via the session or a temporary database record (and passing only a unique ID in the URL) is more secure and results in cleaner URLs than passing many raw values. The current implementation directly mimics the ASP.NET "pass-by-URL" for demonstration purposes.

This comprehensive plan provides a clear, actionable roadmap for modernizing your ASP.NET ABC Analysis report form to a maintainable, high-performance Django application.