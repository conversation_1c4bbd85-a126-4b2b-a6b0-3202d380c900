## ASP.NET to Django Conversion Script: Gate Pass Print Details

This document outlines a comprehensive plan for modernizing the provided ASP.NET Gate Pass Print Details module to a robust, scalable Django application. Our approach leverages AI-assisted automation, adhering to a "fat model, thin view" architecture, and utilizing HTMX, Alpine.js, and DataTables for a modern, dynamic user experience without complex JavaScript.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The C# code-behind reveals several interconnected tables used to generate the Gate Pass report:
-   `tblGate_Pass` (Master Gate Pass records)
-   `tblGatePass_Details` (Detail lines for Gate Passes)
-   `tblFinancial_master` (Financial Year details)
-   `tblGatePass_Reason` (Reasons for Gate Pass types)
-   `tblHR_OfficeStaff` (Employee information, including authorizers and session users)
-   `BusinessGroup` (Business group symbols associated with staff)
-   `Company` (Implied for `CompId`, `CompAdd` function)

**Inferred Tables and Key Columns:**

*   **`tblGate_Pass`**:
    *   `Id` (PK)
    *   `SessionId` (FK to `tblHR_OfficeStaff.EmpId` - Gate Pass creator)
    *   `CompId` (FK to `Company` table - Company ID)
    *   `SysDate` (System Date of entry)
    *   `Authorize` (Authorization status, e.g., 0/1)
    *   `EmpId` (FK to `tblHR_OfficeStaff.EmpId` - Self Employee ID)
    *   `FinYearId` (FK to `tblFinancial_master.FinYearId`)
    *   `GPNo` (Gate Pass Number)
    *   `AuthorizedBy` (FK to `tblHR_OfficeStaff.EmpId` - Authorizer)
    *   `AuthorizeDate`
    *   `AuthorizeTime`

*   **`tblGatePass_Details`**:
    *   `Id` (PK, referred to as `DId` in C#)
    *   `MId` (FK to `tblGate_Pass.Id`)
    *   `FromDate`
    *   `TypeOf` (Integer: 1 for WONo, 2 for Enquiry, 3 for Other)
    *   `FromTime`
    *   `ToTime`
    *   `Type` (FK to `tblGatePass_Reason.Id`)
    *   `TypeFor` (Associated value like WO number, Enquiry number, or descriptive text)
    *   `Reason` (Free text reason for pass)
    *   `Feedback`
    *   `OtherEId` (FK to `tblHR_OfficeStaff.EmpId` - Other Employee ID, e.g., visitor/non-self employee)
    *   `Place`
    *   `ContactPerson`
    *   `ContactNo`

*   **`tblFinancial_master`**:
    *   `FinYearId` (PK)
    *   `FinYear` (e.g., "2023-2024")

*   **`tblGatePass_Reason`**:
    *   `Id` (PK)
    *   `Reason` (Description of the pass type)

*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (PK)
    *   `Title` (e.g., "Mr.", "Ms.")
    *   `EmployeeName`
    *   `CompId` (FK to `Company` table - Employee's company)
    *   `BGGroup` (FK to `BusinessGroup.Id`)

*   **`BusinessGroup`**:
    *   `Id` (PK)
    *   `Symbol` (Business group symbol)

*   **`Company`** (Inferred from `fun.CompAdd(CompId)` and `tblHR_OfficeStaff.CompId`):
    *   `CompId` (PK)
    *   `CompanyName` (Inferred)
    *   `CompanyAddress` (Inferred, used by `fun.CompAdd`)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The provided ASP.NET code focuses exclusively on **Read** operations for displaying a Gate Pass report. There are no Create, Update, or Delete (CRUD) operations present for Gate Passes on this specific page.

*   **Read (Report Generation):**
    *   Fetches data from `tblGate_Pass` and `tblGatePass_Details` based on `CompId` and dynamic filter strings (`z`, `p`, `q`) from query parameters.
    *   Performs lookups for related data: Financial Year, Gate Pass Reason, Authorized By employee, self/other employee details (name, title, business group symbol).
    *   Applies conditional logic to format `TypeFor` (WONo, Enquiry, Other).
    *   Formats dates (`fun.FromDateDMY`, `fun.FromDate`) and authorization details.
    *   Consolidates all this information into a single `DataTable` (which is then used by Crystal Reports).
    *   Passes filter dates (`FDate`, `TDate`) and company address (`CompAdd`) as parameters to the Crystal Report.
    *   Session management is used to cache the `ReportDocument`.

**Conclusion:** The Django modernization will focus on building a robust "report viewing" interface, including filtering capabilities, and displaying the results using DataTables. A separate PDF export functionality could be added if full Crystal Report parity is required.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The ASP.NET UI is minimal and relies heavily on Crystal Reports for rendering.
-   `CR:CrystalReportViewer`: The primary control, responsible for displaying the generated report.
-   `CR:CrystalReportSource`: Defines the report file (`GatePass.rpt`).
-   `form1` (`runat="server"`): The standard ASP.NET form.
-   `script src="../../../Javascript/loadingNotifier.js"`: Suggests client-side loading indicators.

**Django Equivalent Components:**
-   **No direct Crystal Reports equivalent**: The report data will be fetched and presented in a structured HTML table, likely using DataTables.
-   **Filtering Mechanism**: A form with date inputs and potentially other fields will be needed to capture the `FDate`, `TDate`, and implied `z`, `p`, `q` filters.
-   **Dynamic Updates**: HTMX will be used to fetch and swap the report table based on filter changes.
-   **Loading Indicators**: HTMX's built-in `hx-indicator` or Alpine.js can handle loading states.

### Step 4: Generate Django Code

We will create a Django application named `hr_reports` to house this functionality.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema, focusing on the "fat model" principle by adding report data retrieval logic.

**File: `hr_reports/models.py`**

```python
from django.db import models
from django.db.models import F, Q, Value, CharField, DateField, IntegerField
from django.db.models.functions import Concat, Cast
from django.db.models import Case, When

# Helper for date formatting, replicating fun.FromDateDMY, fun.FromDate
class DateFormatUtils:
    @staticmethod
    def from_date_dmy(date_obj):
        """Replicates fun.FromDateDMY: Formats date as DD/MM/YYYY."""
        return date_obj.strftime("%d/%m/%Y") if date_obj else ""

    @staticmethod
    def from_date(date_obj):
        """Replicates fun.FromDate: Formats date (assuming YYYY-MM-DD for standard)."""
        return date_obj.strftime("%Y-%m-%d") if date_obj else ""

class Company(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='CompanyAddress', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Company'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.comp_id}"

    def get_full_address(self):
        """Replicates fun.CompAdd(CompId)."""
        return self.address or "Company Address Not Available"

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class GatePassReason(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    reason_text = models.CharField(db_column='Reason', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblGatePass_Reason'
        verbose_name = 'Gate Pass Reason'
        verbose_name_plural = 'Gate Pass Reasons'

    def __str__(self):
        return self.reason_text

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class HRStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=10)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', null=True)
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Staff'
        verbose_name_plural = 'HR Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"

    def get_full_name(self):
        return f"{self.title}. {self.employee_name}"

class GatePass(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.ForeignKey(HRStaff, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='created_gatepasses', null=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    sys_date = models.DateField(db_column='SysDate')
    authorize_status = models.IntegerField(db_column='Authorize')
    emp_id_self = models.ForeignKey(HRStaff, on_delete=models.DO_NOTHING, db_column='EmpId', null=True, related_name='self_gatepasses')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', null=True)
    gp_no = models.CharField(db_column='GPNo', max_length=100)
    authorized_by = models.ForeignKey(HRStaff, on_delete=models.DO_NOTHING, db_column='AuthorizedBy', null=True, related_name='authorized_gatepasses')
    authorize_date = models.DateField(db_column='AuthorizeDate', null=True)
    authorize_time = models.CharField(db_column='AuthorizeTime', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblGate_Pass'
        verbose_name = 'Gate Pass'
        verbose_name_plural = 'Gate Passes'

    def __str__(self):
        return self.gp_no

class GatePassDetailQuerySet(models.QuerySet):
    def get_report_data(self, company_id, fdate, tdate, additional_filters=None):
        """
        Fetches and processes Gate Pass report data, replicating the logic from the ASP.NET code-behind.
        
        Args:
            company_id (int): The company ID to filter by.
            fdate (str): From date string (e.g., 'YYYY-MM-DD').
            tdate (str): To date string (e.g., 'YYYY-MM-DD').
            additional_filters (dict, optional): Dictionary of Django ORM filters 
                                               to replicate 'z', 'p', 'q' query parameters.
                                               Example: {'master_id__gp_no__icontains': '123'}.
        Returns:
            QuerySet: A QuerySet of dictionaries with all processed report columns.
        """
        
        qs = self.select_related(
            'master_id',
            'master_id__session_id',
            'master_id__company',
            'master_id__emp_id_self',
            'master_id__financial_year',
            'master_id__authorized_by',
            'master_id__authorized_by__company', # Added for complete joins if needed
            'pass_type',
            'emp_id_other',
            'emp_id_other__business_group',
            'master_id__emp_id_self__business_group'
        ).filter(master_id__company_id=company_id)

        # Apply date filters
        if fdate:
            qs = qs.filter(from_date__gte=fdate)
        if tdate:
            qs = qs.filter(from_date__lte=tdate)
            
        # Apply additional filters (replicates 'z', 'p', 'q')
        if additional_filters:
            qs = qs.filter(**additional_filters)
            
        # Order by master_id (tblGate_Pass.Id) as in original
        qs = qs.order_by('master_id__id')

        # Annotate fields to replicate complex logic and format data
        qs = qs.annotate(
            # Replicate TypeOf logic
            display_type_for=Case(
                When(type_of=1, then=Concat(Value('WONo :'), 'type_for', output_field=CharField())),
                When(type_of=2, then=Concat(Value('Enquiry :'), 'type_for', output_field=CharField())),
                When(type_of=3, then=F('type_for')),
                default=F('type_for'),
                output_field=CharField()
            ),
            # Authorized By display
            authorized_by_display=Case(
                When(master_id__authorize_status=1, 
                    then=Concat(F('master_id__authorized_by__title'), Value('. '), F('master_id__authorized_by__employee_name'), output_field=CharField())),
                default=Value(''),
                output_field=CharField()
            ),
            # Authorized Date display (will be formatted in template or Python)
            authorized_date_raw=Case(
                When(master_id__authorize_status=1, then=F('master_id__authorize_date')),
                default=Value(None, output_field=DateField()), # Use None for null dates
                output_field=DateField()
            ),
            # Authorized Time display
            authorized_time_display=Case(
                When(master_id__authorize_status=1, then=F('master_id__authorize_time')),
                default=Value(''),
                output_field=CharField()
            ),
            # Employee involved display (SelfEId or OtherEId combined)
            employee_involved_name=Case(
                When(Q(master_id__emp_id_self__isnull=False), then=Concat(F('master_id__emp_id_self__title'), Value('. '), F('master_id__emp_id_self__employee_name'), output_field=CharField())),
                When(Q(emp_id_other__isnull=False), then=Concat(F('emp_id_other__title'), Value('. '), F('emp_id_other__employee_name'), output_field=CharField())),
                default=Value(''),
                output_field=CharField()
            ),
            # Business Group Symbol for the involved employee
            business_group_symbol=Case(
                When(Q(master_id__emp_id_self__isnull=False), then=F('master_id__emp_id_self__business_group__symbol')),
                When(Q(emp_id_other__isnull=False), then=F('emp_id_other__business_group__symbol')),
                default=Value(''),
                output_field=CharField()
            ),
            # Requesting Employee (SessionId user)
            requesting_employee_name=Concat(F('master_id__session_id__title'), Value('. '), F('master_id__session_id__employee_name'), output_field=CharField()),
            
            # Additional fields needed for the report layout or original data mapping
            gp_sys_date_raw=F('master_id__sys_date'), # For fun.FromDateDMY
            pass_from_date_raw=F('from_date'), # For fun.FromDate
            gate_pass_no=F('master_id__gp_no'),
            gate_pass_master_id=F('master_id__id'),
            financial_year_name=F('master_id__financial_year__fin_year'),
            reason_type_text=F('pass_type__reason_text'),
            company_id_val=F('master_id__company_id'),
            # Placeholder for 'ToDate' if not directly mapped to a DB column in report
            to_date_placeholder=Value(''), 
        )
        
        # Select the specific fields to match the columns added to `dt` in the C# code,
        # with appropriate renames for clarity in Django.
        return qs.values(
            'gate_pass_master_id', # Id
            'gp_sys_date_raw',     # FinYear (mapped to SysDate for consistency with C# dt[1])
            'gate_pass_no',        # GPNo
            'pass_from_date_raw',  # FromDate
            'from_time',           # FromTime
            'to_time',             # ToTime
            'reason_type_text',    # Type
            'display_type_for',    # TypeFor
            'reason_text',         # Reason
            'authorized_by_display', # AuthorizedBy
            'authorized_date_raw',   # AuthorizeDate
            'authorized_time_display', # AuthorizeTime
            'feedback',            # Feedback
            'id',                  # DId
            'employee_involved_name', # SelfEId (combined self/other)
            'master_id__authorize_status', # Authorize
            'place',               # Place
            'contact_person',      # ContactPerson
            'contact_no',          # ContactNo
            'requesting_employee_name', # Empother (the Sessionid user)
            'company_id_val',      # CompId
            'business_group_symbol'# ToDate (This was dr[21] for BGGroup in C#)
        )

class GatePassDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master_id = models.ForeignKey(GatePass, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    from_date = models.DateField(db_column='FromDate')
    type_of = models.IntegerField(db_column='TypeOf')
    from_time = models.CharField(db_column='FromTime', max_length=50)
    to_time = models.CharField(db_column='ToTime', max_length=50)
    pass_type = models.ForeignKey(GatePassReason, on_delete=models.DO_NOTHING, db_column='Type', null=True)
    type_for = models.CharField(db_column='TypeFor', max_length=255)
    reason_text = models.TextField(db_column='Reason')
    feedback = models.TextField(db_column='Feedback', null=True, blank=True)
    emp_id_other = models.ForeignKey(HRStaff, on_delete=models.DO_NOTHING, db_column='OtherEId', null=True, related_name='other_gatepass_details')
    place = models.CharField(db_column='Place', max_length=255, null=True, blank=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, null=True, blank=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, null=True, blank=True)

    objects = GatePassDetailQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = 'tblGatePass_Details'
        verbose_name = 'Gate Pass Detail'
        verbose_name_plural = 'Gate Pass Details'

    def __str__(self):
        return f"Detail for GP {self.master_id.gp_no} (ID: {self.id})"

    # This method is primarily for single object display, for report the manager method is used.
    def get_type_for_display(self):
        if self.type_of == 1:
            return f"WONo: {self.type_for}"
        elif self.type_of == 2:
            return f"Enquiry: {self.type_for}"
        elif self.type_of == 3:
            return self.type_for
        return self.type_for # Default fallback
```

#### 4.2 Forms

**Task:** Define a Django form for filtering the Gate Pass report.

**File: `hr_reports/forms.py`**

```python
from django import forms
import datetime

class GatePassReportFilterForm(forms.Form):
    # Defaulting to a reasonable date range for demonstration
    fdate = forms.DateField(
        label="From Date",
        initial=datetime.date.today() - datetime.timedelta(days=30),
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    tdate = forms.DateField(
        label="To Date",
        initial=datetime.date.today(),
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    # Placeholder for 'z', 'p', 'q' filters. These would be more specific fields in a real app.
    # For now, a generic text search or dropdowns can represent them.
    # We will assume 'z', 'p', 'q' correspond to optional filters like GPNo, Employee, etc.
    gp_no = forms.CharField(
        label="Gate Pass No.",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Filter by GP No.'
        })
    )
    # Add other filter fields as necessary based on what z, p, q actually filter.
    # For example:
    # employee_name = forms.CharField(label="Employee Name", required=False, widget=forms.TextInput(attrs={'class': '...'}))
    # reason_type = forms.ModelChoiceField(queryset=GatePassReason.objects.all(), label="Reason Type", required=False, empty_label="All", widget=forms.Select(attrs={'class': '...'}))

    def clean(self):
        cleaned_data = super().clean()
        fdate = cleaned_data.get('fdate')
        tdate = cleaned_data.get('tdate')

        if fdate and tdate and fdate > tdate:
            self.add_error('fdate', "From Date cannot be after To Date.")
            self.add_error('tdate', "To Date cannot be before From Date.")

        return cleaned_data

    def get_additional_filters(self):
        """Constructs a dictionary of ORM filters from form fields."""
        filters = {}
        if self.is_valid():
            if self.cleaned_data.get('gp_no'):
                filters['master_id__gp_no__icontains'] = self.cleaned_data['gp_no']
            # Add more filters here based on additional_filters fields
            # E.g., if 'employee_name' was a field:
            # if self.cleaned_data.get('employee_name'):
            #    filters['master_id__session_id__employee_name__icontains'] = self.cleaned_data['employee_name']
        return filters
```

#### 4.3 Views

**Task:** Implement report display using TemplateView and a ListView for HTMX data.

**File: `hr_reports/views.py`**

```python
from django.views.generic import TemplateView, ListView
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.db.models import Q # For complex queries like z, p, q
from .models import GatePassDetail, DateFormatUtils # Import the utility
from .forms import GatePassReportFilterForm
import datetime

class GatePassReportView(TemplateView):
    """
    Main view for displaying the Gate Pass Report page with a filter form.
    """
    template_name = 'hr_reports/gatepass_report/report_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the form with default values or values from GET request on page load
        context['filter_form'] = GatePassReportFilterForm(self.request.GET or None)
        return context

class GatePassReportTablePartialView(ListView):
    """
    View to serve the Gate Pass report table data via HTMX.
    This view will handle the data retrieval and rendering of the partial table template.
    """
    model = GatePassDetail
    template_name = 'hr_reports/gatepass_report/_gatepass_report_table.html'
    context_object_name = 'gatepass_report_items'

    def get_queryset(self):
        # Default company ID (should come from user session/profile in a real app)
        # For demonstration, use a placeholder. In ASP.NET, it was Session["compid"].
        # Assuming current user's company or a default.
        # Placeholder: Assume CompId=1 for now.
        company_id = self.request.user.comp_id if self.request.user.is_authenticated and hasattr(self.request.user, 'comp_id') else 1
        
        form = GatePassReportFilterForm(self.request.GET)
        report_data = []

        if form.is_valid():
            fdate = form.cleaned_data['fdate']
            tdate = form.cleaned_data['tdate']
            additional_filters = form.get_additional_filters()

            # Retrieve processed report data using the fat model manager method
            # This is where the complex SQL query and C# data processing logic is replicated.
            report_data = GatePassDetail.objects.get_report_data(
                company_id=company_id, 
                fdate=fdate, 
                tdate=tdate,
                additional_filters=additional_filters
            )
            
            # Post-processing for date formatting that couldn't be done directly in ORM
            # This replicates fun.FromDateDMY and fun.FromDate
            for item in report_data:
                # Format SysDate for display
                item['sys_date_formatted_display'] = DateFormatUtils.from_date_dmy(item['gp_sys_date_raw'])
                # Format FromDate for display
                item['from_date_formatted_display'] = DateFormatUtils.from_date(item['pass_from_date_raw'])
                # Format AuthorizeDate for display
                item['authorized_date_display_formatted'] = DateFormatUtils.from_date_dmy(item['authorized_date_raw'])
                # Add company address as a report parameter, fetched via the Company model method
                if 'company_id_val' in item and item['company_id_val']:
                    company = Company.objects.filter(comp_id=item['company_id_val']).first()
                    item['company_address'] = company.get_full_address() if company else "N/A"
                else:
                    item['company_address'] = "N/A"
                
                # Remove raw date fields that were only for ORM processing
                item.pop('gp_sys_date_raw', None)
                item.pop('pass_from_date_raw', None)
                item.pop('authorized_date_raw', None)

        # Convert QuerySet of dicts to a list of dicts for template
        return list(report_data)

    def render_to_response(self, context, **response_kwargs):
        # HTMX requests should only render the partial template
        return super().render_to_response(context, **response_kwargs)

```

#### 4.4 Templates

**Task:** Create templates for the report view, including a main page and a partial for the DataTables content.

**File: `hr_reports/templates/hr_reports/gatepass_report/report_list.html`**

```html
{% extends 'core/base.html' %}
{% load tailwind_filters %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Gate Pass Report</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Filter Report</h3>
        <form hx-get="{% url 'gatepass_report_table' %}" 
              hx-target="#gatepassReportTable-container" 
              hx-swap="innerHTML"
              hx-indicator="#loadingIndicator"
              class="space-y-4">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {{ filter_form|crispy }} {# Render form fields with Tailwind CSS via crispy-forms #}
            </div>

            <div class="flex justify-end mt-6">
                <button 
                    type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
    
    <div id="gatepassReportTable-container" 
         hx-trigger="load delay:10ms, filterFormSubmit from:#filter-form" {# Load on page load, and on form submit #}
         hx-get="{% url 'gatepass_report_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center p-8 bg-white shadow-md rounded-lg">
            <div id="loadingIndicator" class="htmx-indicator inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="htmx-indicator mt-4 text-gray-600 text-lg">Loading Gate Pass Report...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management.
        // For example, if you had complex toggles or dynamic elements beyond HTMX.
    });

    // Helper to re-initialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'gatepassReportTable-container') {
            const table = $('#gatePassReportTable');
            if (table.length && !$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": true,
                    "order": [[ 0, "asc" ]] // Example default sort
                });
            }
        }
    });

    // Initial load for DataTables, if not triggered by hx-trigger="load"
    $(document).ready(function() {
        // The hx-trigger="load" on #gatepassReportTable-container handles initial data load
        // so no direct DataTable init needed here for the main table.
        // This block can be used for other page-level JS if necessary.
    });
</script>
{% endblock %}
```

**File: `hr_reports/templates/hr_reports/gatepass_report/_gatepass_report_table.html`**

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="gatePassReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GP No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type For</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feedback</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Self/Other Emp</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if gatepass_report_items %}
                {% for item in gatepass_report_items %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.gate_pass_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.sys_date_formatted_display }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.from_date_formatted_display }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.from_time }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.to_time }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.reason_type_text }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.display_type_for }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.reason_text }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.authorized_by_display }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.authorized_date_display_formatted }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.authorized_time_display }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.feedback }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.employee_involved_name }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.place }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.contact_person }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.contact_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.requesting_employee_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.business_group_symbol }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="19" class="py-4 px-4 text-center text-sm text-gray-500">No Gate Pass records found for the selected criteria.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- This script block will be executed after HTMX swaps the content -->
<script>
    $(document).ready(function() {
        // Ensure DataTables is initialized only once for this table
        if (!$.fn.DataTable.isDataTable('#gatePassReportTable')) {
            $('#gatePassReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true,
                "order": [[ 0, "asc" ]] 
            });
        }
    });
</script>
```

#### 4.5 URLs

**Task:** Define URL patterns for the report views.

**File: `hr_reports/urls.py`**

```python
from django.urls import path
from .views import GatePassReportView, GatePassReportTablePartialView

urlpatterns = [
    path('gatepass-report/', GatePassReportView.as_view(), name='gatepass_report_list'),
    path('gatepass-report/table/', GatePassReportTablePartialView.as_view(), name='gatepass_report_table'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the models and views.

**File: `hr_reports/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from unittest.mock import patch
from .models import Company, FinancialYear, GatePassReason, BusinessGroup, HRStaff, GatePass, GatePassDetail, DateFormatUtils

class ModelTestCases(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for testing
        cls.company = Company.objects.create(comp_id=1, name='Test Company', address='123 Test St')
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, fin_year='2024-2025')
        cls.reason_type_personal = GatePassReason.objects.create(id=1, reason_text='Personal Visit')
        cls.reason_type_official = GatePassReason.objects.create(id=2, reason_text='Official Business')
        cls.business_group_hr = BusinessGroup.objects.create(id=1, symbol='HR')
        cls.business_group_ops = BusinessGroup.objects.create(id=2, symbol='OPS')

        cls.hr_staff_creator = HRStaff.objects.create(emp_id='EMP001', title='Mr', employee_name='John Doe', company=cls.company, business_group=cls.business_group_hr)
        cls.hr_staff_authorizer = HRStaff.objects.create(emp_id='EMP002', title='Ms', employee_name='Jane Smith', company=cls.company, business_group=cls.business_group_hr)
        cls.hr_staff_self_emp = HRStaff.objects.create(emp_id='EMP003', title='Dr', employee_name='Alice Brown', company=cls.company, business_group=cls.business_group_ops)
        cls.hr_staff_other_emp = HRStaff.objects.create(emp_id='EMP004', title='Mx', employee_name='Bob White', company=cls.company, business_group=cls.business_group_ops)

        # Create GatePass instances
        cls.gatepass_1 = GatePass.objects.create(
            id=1,
            session_id=cls.hr_staff_creator,
            company=cls.company,
            sys_date=date(2024, 1, 1),
            authorize_status=1,
            emp_id_self=cls.hr_staff_self_emp,
            financial_year=cls.fin_year,
            gp_no='GP/001/2024',
            authorized_by=cls.hr_staff_authorizer,
            authorize_date=date(2024, 1, 2),
            authorize_time='10:00 AM'
        )
        cls.gatepass_2 = GatePass.objects.create(
            id=2,
            session_id=cls.hr_staff_creator,
            company=cls.company,
            sys_date=date(2024, 1, 15),
            authorize_status=0, # Not authorized
            emp_id_self=None,
            financial_year=cls.fin_year,
            gp_no='GP/002/2024',
            authorized_by=None,
            authorize_date=None,
            authorize_time=None
        )

        # Create GatePassDetail instances
        cls.detail_1 = GatePassDetail.objects.create(
            id=101,
            master_id=cls.gatepass_1,
            from_date=date(2024, 1, 1),
            type_of=1, # WONo
            from_time='09:00 AM',
            to_time='05:00 PM',
            pass_type=cls.reason_type_official,
            type_for='WO-12345',
            reason_text='Site visit for project X',
            feedback='Completed successfully',
            emp_id_other=None,
            place='Client Site A',
            contact_person='Mr. Client',
            contact_no='9876543210'
        )
        cls.detail_2 = GatePassDetail.objects.create(
            id=102,
            master_id=cls.gatepass_2,
            from_date=date(2024, 1, 15),
            type_of=3, # Other
            from_time='02:00 PM',
            to_time='04:00 PM',
            pass_type=cls.reason_type_personal,
            type_for='Personal errand',
            reason_text='Family emergency',
            feedback=None,
            emp_id_other=cls.hr_staff_other_emp,
            place='Home',
            contact_person='Family Member',
            contact_no='0123456789'
        )
        cls.detail_3 = GatePassDetail.objects.create(
            id=103,
            master_id=cls.gatepass_1,
            from_date=date(2024, 1, 3),
            type_of=2, # Enquiry
            from_time='10:00 AM',
            to_time='11:00 AM',
            pass_type=cls.reason_type_official,
            type_for='ENQ-54321',
            reason_text='Customer inquiry',
            feedback='Resolved',
            emp_id_other=None,
            place='Office',
            contact_person=None,
            contact_no=None
        )

    def test_gatepass_report_data_retrieval(self):
        """
        Test the get_report_data method on GatePassDetail manager for correct data and annotations.
        """
        fdate = date(2024, 1, 1)
        tdate = date(2024, 1, 31)
        company_id = self.company.comp_id

        report_data = GatePassDetail.objects.get_report_data(company_id, fdate, tdate)
        
        # Ensure we get 3 records
        self.assertEqual(len(report_data), 3)

        # Test detail_1 (authorized, self_emp, WO-type)
        item1 = next(item for item in report_data if item['gate_pass_master_id'] == self.gatepass_1.id and item['id'] == self.detail_1.id)
        self.assertIsNotNone(item1)
        self.assertEqual(item1['gate_pass_no'], 'GP/001/2024')
        self.assertEqual(item1['display_type_for'], 'WONo :WO-12345')
        self.assertEqual(item1['authorized_by_display'], 'Ms. Jane Smith')
        self.assertEqual(item1['employee_involved_name'], 'Dr. Alice Brown') # Should be self_emp here
        self.assertEqual(item1['business_group_symbol'], 'OPS') # BG for self_emp
        self.assertEqual(item1['requesting_employee_name'], 'Mr. John Doe')
        self.assertEqual(item1['reason_type_text'], 'Official Business')
        self.assertEqual(item1['master_id__authorize_status'], 1)
        self.assertEqual(item1['place'], 'Client Site A')
        
        # Test detail_2 (unauthorized, other_emp, Other-type)
        item2 = next(item for item in report_data if item['gate_pass_master_id'] == self.gatepass_2.id and item['id'] == self.detail_2.id)
        self.assertIsNotNone(item2)
        self.assertEqual(item2['gate_pass_no'], 'GP/002/2024')
        self.assertEqual(item2['display_type_for'], 'Personal errand') # TypeOf 3
        self.assertEqual(item2['authorized_by_display'], '') # Not authorized
        self.assertEqual(item2['authorized_date_raw'], None)
        self.assertEqual(item2['employee_involved_name'], 'Mx. Bob White') # Should be other_emp here
        self.assertEqual(item2['business_group_symbol'], 'OPS') # BG for other_emp
        self.assertEqual(item2['feedback'], None)
        self.assertEqual(item2['master_id__authorize_status'], 0)


        # Test detail_3 (authorized, self_emp, Enquiry-type)
        item3 = next(item for item in report_data if item['gate_pass_master_id'] == self.gatepass_1.id and item['id'] == self.detail_3.id)
        self.assertIsNotNone(item3)
        self.assertEqual(item3['display_type_for'], 'Enquiry :ENQ-54321')
        self.assertEqual(item3['authorized_by_display'], 'Ms. Jane Smith') # From master_id
        self.assertEqual(item3['employee_involved_name'], 'Dr. Alice Brown') # From master_id's emp_id_self

    def test_gatepass_report_data_filtering(self):
        fdate = date(2024, 1, 10)
        tdate = date(2024, 1, 20)
        company_id = self.company.comp_id

        report_data = GatePassDetail.objects.get_report_data(company_id, fdate, tdate)
        self.assertEqual(len(report_data), 1) # Only detail_2 (from_date 2024-01-15) should be in range
        self.assertEqual(report_data[0]['gate_pass_no'], 'GP/002/2024')

        # Test additional filters (z, p, q)
        additional_filters = {'gate_pass_no__icontains': '001'}
        report_data_filtered_gp = GatePassDetail.objects.get_report_data(company_id, fdate=None, tdate=None, additional_filters=additional_filters)
        self.assertEqual(len(report_data_filtered_gp), 2) # GP/001 has two details
        self.assertTrue(all(item['gate_pass_no'] == 'GP/001/2024' for item in report_data_filtered_gp))

    def test_date_format_utilities(self):
        test_date = date(2024, 1, 25)
        self.assertEqual(DateFormatUtils.from_date_dmy(test_date), '25/01/2024')
        self.assertEqual(DateFormatUtils.from_date(test_date), '2024-01-25')
        self.assertEqual(DateFormatUtils.from_date_dmy(None), '')
        self.assertEqual(DateFormatUtils.from_date(None), '')

    def test_company_get_full_address(self):
        self.assertEqual(self.company.get_full_address(), '123 Test St')
        comp_no_address = Company.objects.create(comp_id=2, name='No Address Co', address=None)
        self.assertEqual(comp_no_address.get_full_address(), 'Company Address Not Available')


class GatePassReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup minimal data for views tests
        cls.company = Company.objects.create(comp_id=1, name='Test Company', address='123 Test St')
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, fin_year='2024-2025')
        cls.reason_type = GatePassReason.objects.create(id=1, reason_text='Official')
        cls.bg_group = BusinessGroup.objects.create(id=1, symbol='Admin')
        cls.hr_staff = HRStaff.objects.create(emp_id='EMP001', title='Mr', employee_name='Test User', company=cls.company, business_group=cls.bg_group)
        cls.gatepass = GatePass.objects.create(
            id=1, session_id=cls.hr_staff, company=cls.company, sys_date=date.today(),
            authorize_status=1, emp_id_self=cls.hr_staff, financial_year=cls.fin_year,
            gp_no='TEST/001', authorized_by=cls.hr_staff, authorize_date=date.today(), authorize_time='09:00 AM'
        )
        cls.detail = GatePassDetail.objects.create(
            id=101, master_id=cls.gatepass, from_date=date.today(), type_of=3, from_time='09:00 AM',
            to_time='05:00 PM', pass_type=cls.reason_type, type_for='Meeting', reason_text='Business Meeting',
            feedback='Successful', place='Office', contact_person='Self', contact_no='123'
        )

    def setUp(self):
        self.client = Client()
        # Mock user authentication if your app requires it for company_id
        # For simplicity, if `request.user.comp_id` is used, ensure `User` model has `comp_id` or mock it.
        # Here we mock it for the test user to provide comp_id
        self.user = self.hr_staff # Reusing HRStaff as a simple test user
        self.user.comp_id = self.company.comp_id 
        self.client.force_login(self.user) # Requires user to be authenticated

    def test_report_list_view_get(self):
        response = self.client.get(reverse('gatepass_report_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/gatepass_report/report_list.html')
        self.assertIn('filter_form', response.context)
        self.assertIsInstance(response.context['filter_form'], GatePassReportFilterForm)

    @patch('hr_reports.models.GatePassDetail.objects.get_report_data')
    def test_report_table_partial_view_get(self, mock_get_report_data):
        # Mock the get_report_data to control test data
        mock_get_report_data.return_value = [
            {
                'gate_pass_master_id': 1, 'gp_sys_date_raw': date(2024, 1, 1), 'gate_pass_no': 'TEST/001',
                'pass_from_date_raw': date(2024, 1, 1), 'from_time': '09:00 AM', 'to_time': '05:00 PM',
                'reason_type_text': 'Official', 'display_type_for': 'Meeting', 'reason_text': 'Business Meeting',
                'authorized_by_display': 'Mr. Test User', 'authorized_date_raw': date(2024, 1, 1),
                'authorized_time_display': '09:00 AM', 'feedback': 'Successful', 'id': 101,
                'employee_involved_name': 'Mr. Test User', 'master_id__authorize_status': 1,
                'place': 'Office', 'contact_person': 'Self', 'contact_no': '123',
                'requesting_employee_name': 'Mr. Test User', 'company_id_val': 1, 'business_group_symbol': 'Admin'
            }
        ]

        # Simulate an HTMX request by adding HX-Request header
        response = self.client.get(reverse('gatepass_report_table'), {
            'fdate': date.today().strftime('%Y-%m-%d'),
            'tdate': date.today().strftime('%Y-%m-%d'),
        }, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/gatepass_report/_gatepass_report_table.html')
        self.assertIn('gatepass_report_items', response.context)
        self.assertEqual(len(response.context['gatepass_report_items']), 1)
        self.assertContains(response, 'TEST/001') # Check if report data is in response

        mock_get_report_data.assert_called_once()
        # Verify the arguments passed to the mocked method
        call_args = mock_get_report_data.call_args[1]
        self.assertEqual(call_args['company_id'], self.company.comp_id)
        self.assertEqual(call_args['fdate'], date.today())
        self.assertEqual(call_args['tdate'], date.today())
        self.assertEqual(call_args['additional_filters'], {})

    def test_report_table_partial_view_no_filters(self):
        # Ensure it handles no initial filter data gracefully
        response = self.client.get(reverse('gatepass_report_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/gatepass_report/_gatepass_report_table.html')
        self.assertIn('gatepass_report_items', response.context)
        # Should return data if form defaults are valid and there's data in DB
        self.assertGreaterEqual(len(response.context['gatepass_report_items']), 1)

    def test_report_table_partial_view_invalid_filters(self):
        response = self.client.get(reverse('gatepass_report_table'), {
            'fdate': (date.today() + timedelta(days=5)).strftime('%Y-%m-%d'), # Invalid date range
            'tdate': date.today().strftime('%Y-%m-%d'),
        }, HTTP_HX_REQUEST='true')
        
        # If form is invalid, queryset should be empty as per get_queryset logic
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/gatepass_report/_gatepass_report_table.html')
        self.assertIn('gatepass_report_items', response.context)
        self.assertEqual(len(response.context['gatepass_report_items']), 0)
        self.assertContains(response, "No Gate Pass records found")

    @patch('hr_reports.models.GatePassDetail.objects.get_report_data')
    def test_report_table_partial_view_with_gp_no_filter(self, mock_get_report_data):
        mock_get_report_data.return_value = [
             {
                'gate_pass_master_id': 1, 'gp_sys_date_raw': date(2024, 1, 1), 'gate_pass_no': 'TEST/001',
                'pass_from_date_raw': date(2024, 1, 1), 'from_time': '09:00 AM', 'to_time': '05:00 PM',
                'reason_type_text': 'Official', 'display_type_for': 'Meeting', 'reason_text': 'Business Meeting',
                'authorized_by_display': 'Mr. Test User', 'authorized_date_raw': date(2024, 1, 1),
                'authorized_time_display': '09:00 AM', 'feedback': 'Successful', 'id': 101,
                'employee_involved_name': 'Mr. Test User', 'master_id__authorize_status': 1,
                'place': 'Office', 'contact_person': 'Self', 'contact_no': '123',
                'requesting_employee_name': 'Mr. Test User', 'company_id_val': 1, 'business_group_symbol': 'Admin'
            }
        ]
        response = self.client.get(reverse('gatepass_report_table'), {
            'fdate': date.today().strftime('%Y-%m-%d'),
            'tdate': date.today().strftime('%Y-%m-%d'),
            'gp_no': '001' # Simulate 'z','p','q' filter
        }, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        mock_get_report_data.assert_called_once()
        call_args = mock_get_report_data.call_args[1]
        self.assertEqual(call_args['additional_filters'], {'master_id__gp_no__icontains': '001'})

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Filters and Table Updates**:
    -   The main `report_list.html` contains a filter form (`GatePassReportFilterForm`).
    -   This form uses `hx-get="{% url 'gatepass_report_table' %}"`, `hx-target="#gatepassReportTable-container"`, and `hx-swap="innerHTML"`.
    -   When the "Apply Filters" button is clicked (or implicitly on form submission), HTMX makes an AJAX GET request to `gatepass_report_table` URL, passing the form data as query parameters.
    -   The `GatePassReportTablePartialView` renders `_gatepass_report_table.html` with the filtered data.
    -   HTMX then swaps this new HTML content into the `#gatepassReportTable-container` div.
    -   `hx-trigger="load delay:10ms, filterFormSubmit from:#filter-form"` on the container ensures the table loads automatically on page load and refreshes when the form is submitted.
    -   A loading indicator (`hx-indicator="#loadingIndicator"`) is used to show a spinner during data fetching.
-   **DataTables for List Views**:
    -   The `_gatepass_report_table.html` partial contains a `<table>` with `id="gatePassReportTable"`.
    -   A `<script>` block within this partial (or registered globally) is responsible for initializing DataTables on this `<table>` element *after* HTMX swaps the content. This is crucial for DataTables to work correctly with dynamic content. The `htmx:afterSwap` event listener handles this re-initialization.
    -   DataTables provides client-side searching, sorting, and pagination without requiring further server-side interaction.
-   **Alpine.js for UI State**:
    -   Currently, Alpine.js is noted for general UI state management but not strictly required for this specific report page, as HTMX handles the primary dynamic interactions. If more complex client-side interactions were required (e.g., dynamic modal toggles, complex form field dependencies), Alpine.js would be integrated via `x-data` attributes directly in the HTML. The `base.html` would include Alpine.js.
-   **No Full Page Reloads**: All filtering and table updates are handled by HTMX, ensuring a smooth, single-page application-like experience.

## Final Notes

-   **Placeholders**: All `[PLACEHOLDER]` values have been replaced with inferred or example Django-specific names (e.g., `[MODEL_NAME_LOWER]` became `gatepass_report`).
-   **DRY Templates**: Templates are kept DRY by using a `_gatepass_report_table.html` partial for the dynamic table content and extending `core/base.html`.
-   **Fat Model, Thin View**: The `get_report_data` method within the `GatePassDetailQuerySet` encapsulates all the complex data retrieval, joining, and conditional logic that was previously in the ASP.NET code-behind. Views (`GatePassReportView`, `GatePassReportTablePartialView`) remain concise, primarily handling rendering and delegation to the model.
-   **Comprehensive Tests**: Unit tests for model methods (`get_report_data`) and integration tests for views (GET requests for the main page and HTMX-triggered table updates) are provided to ensure functionality and maintainability.
-   **Business Value**: This modernization provides a significantly improved user experience with faster, dynamic report loading. The backend is more maintainable due to clear separation of concerns (ORM logic in models, presentation logic in templates). The use of open-source, modern frameworks like Django, HTMX, and DataTables reduces vendor lock-in and facilitates future development and scaling compared to legacy Crystal Reports and ASP.NET Web Forms. The automated migration strategy minimizes manual effort and potential for human error.