## ASP.NET to Django Conversion Script: Staff Print Module

This modernization plan outlines the strategic transition of your ASP.NET `OfficeStaff_Print` module to a robust, scalable Django application. Our approach focuses on leveraging AI-assisted automation, adhering to modern Django best practices, and utilizing a cutting-edge front-end stack to deliver a superior user experience with minimal manual coding.

**Business Value Proposition:**
Migrating to Django will provide:
*   **Enhanced Performance & Scalability:** Django's architecture is built for high performance and can easily scale to meet growing business demands.
*   **Reduced Development Costs:** By adopting modern frameworks and automation strategies, future development and maintenance costs will be significantly lowered.
*   **Improved User Experience:** HTMX and Alpine.js deliver dynamic, responsive interfaces without complex JavaScript, leading to faster interactions and a more fluid user experience.
*   **Future-Proof Technology:** Moving away from legacy ASP.NET ensures your application remains compatible with modern web standards and security protocols.
*   **Simplified Operations:** A unified Python stack simplifies deployment, monitoring, and debugging.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code primarily interacts with staff data. Based on the `GridView` columns and the `GetCompletionList` WebMethod, the core table appears to be `tblHR_OfficeStaff`. Filtering also involves `tblHR_Departments` and `BusinessGroup`. The `Sp_Staff_Grid_Print` stored procedure suggests a combined data source, but for Django modeling, we'll represent the underlying tables.

**Inferred Database Schema:**

*   **Main Entity:** `tblHR_OfficeStaff` (represents individual staff members)
*   **Related Entities (for lookups/filters):**
    *   `tblHR_Departments` (for department names)
    *   `BusinessGroup` (for business group names/symbols)

**Inferred Columns:**

*   **`tblHR_OfficeStaff`:**
    *   `EmpId` (Primary Key, likely string/varchar as `fun.getCode` is used)
    *   `EmployeeName` (varchar)
    *   `Department` (integer, likely FK to `tblHR_Departments.Id`)
    *   `BGGroup` (integer, likely FK to `BusinessGroup.Id`)
    *   `Designation` (varchar)
    *   `MobileNo` (varchar)
    *   `JoiningDate` (datetime)
    *   `ResignationDate` (datetime)
    *   `FinYear` (varchar/int, from session)
    *   `CompId` (int, from session)
    *   `UserID` (integer, visible=false in GridView, likely an internal ID)

*   **`tblHR_Departments`:**
    *   `Id` (Primary Key, int)
    *   `Description` (varchar)

*   **`BusinessGroup`:**
    *   `Id` (Primary Key, int)
    *   `Symbol` (varchar)

### Step 2: Identify Backend Functionality

The ASP.NET page `OfficeStaff_Print.aspx` provides the following functionalities:

*   **Read (R):** Displaying a paginated list of office staff.
*   **Search/Filter:** Users can filter the staff list by:
    *   Employee Name (with autocomplete)
    *   Department Name
    *   Business Group
*   **Export:** Exporting the current filtered staff data to an Excel file.
*   **Select/Details Navigation:** Clicking a "select" link on a staff record redirects to a details page (`OfficeStaff_Print_Details.aspx`). In Django, this will be handled via HTMX to load a details view into a modal.

**Validation Logic:**
*   Input validation: Implicit validation for search terms (e.g., if fields are empty, no filter is applied).
*   Export: Checks if data exists before exporting.

### Step 3: Infer UI Components

The ASP.NET controls will be mapped to Django templates with HTMX, Alpine.js, and DataTables.

*   `DrpField` (Dropdown): `select` element in Django template.
*   `TxtMrs` (TextBox for Dept/BG search): `input type="text"` in Django template.
*   `TxtEmpName` (TextBox for Employee Name with AutoComplete): `input type="text"` with HTMX `hx-get` to an autocomplete endpoint.
*   `AutoCompleteExtender`: Replaced by HTMX and a Django view providing suggestions.
*   `Button1` (Search Button): `button` with HTMX `hx-post` or `hx-get` to refresh the table.
*   `btnExportToExcel` (Export Button): `button` linked to a Django view that serves an Excel file.
*   `GridView2` (Data Grid): `table` element initialized with DataTables for client-side functionality.
*   `LinkButton` for "select": `button` or `a` tag with HTMX `hx-get` to load a detail view into a modal.

---

### Step 4: Generate Django Code

We will create a new Django application, e.g., `hr_staff`.

#### 4.1 Models (`hr_staff/models.py`)

This section defines the Django models that map to your existing database tables. We set `managed = False` to ensure Django doesn't try to create or alter these tables, as they are already managed by your legacy system.

```python
from django.db import models
from django.db.models import Q
from django.core.exceptions import ValidationError

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class OfficeStaffManager(models.Manager):
    """
    Custom manager for OfficeStaff to encapsulate complex search logic.
    This replaces the C# binddata method's SQL generation.
    """
    def search_staff(self, search_field, search_term, comp_id, fin_year):
        staff_query = self.get_queryset().filter(comp_id=comp_id, fin_year=fin_year)

        if search_term:
            if search_field == '0':  # Employee Name
                # Assumes EmpId is directly searchable or mapped from a combined format.
                # In ASP.NET, fun.getCode was used. Here we assume search_term is the actual EmpId or employee name.
                # If EmpId is part of "EmployeeName [EmpId]" format, client-side would parse it.
                # For simplicity, we search by EmpId directly here or EmployeeName containing the term.
                staff_query = staff_query.filter(Q(employee_name__icontains=search_term) | Q(emp_id__iexact=search_term))
            elif search_field == '1':  # Department Name
                # Find department ID from description
                try:
                    dept = Department.objects.get(description__iexact=search_term)
                    staff_query = staff_query.filter(department=dept)
                except Department.DoesNotExist:
                    staff_query = staff_query.none() # No results if department not found
            elif search_field == '2':  # BG Group
                # Find business group ID from symbol
                try:
                    bg_group = BusinessGroup.objects.get(symbol__iexact=search_term)
                    staff_query = staff_query.filter(bg_group=bg_group)
                except BusinessGroup.DoesNotExist:
                    staff_query = staff_query.none() # No results if business group not found
        
        return staff_query.select_related('department', 'bg_group').order_by('emp_id')

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is unique and primary key
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', blank=True, null=True)
    bg_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroup', blank=True, null=True)
    designation = models.CharField(db_column='Designation', max_length=255, blank=True, null=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=20, blank=True, null=True)
    joining_date = models.DateField(db_column='JoiningDate', blank=True, null=True)
    resignation_date = models.DateField(db_column='ResignationDate', blank=True, null=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True) # Assuming string based on Session["finyear"]
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming integer based on Session["compid"]
    user_id = models.IntegerField(db_column='UserID', blank=True, null=True) # Hidden in GridView

    objects = OfficeStaffManager()

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.employee_name} ({self.emp_id})"

    def get_absolute_url(self):
        # This is for standard Django redirects, but HTMX often changes this behavior
        from django.urls import reverse
        return reverse('officestaff_detail', kwargs={'pk': self.pk})

    # Example of a business logic method in the model (Fat Model)
    def is_active(self):
        return self.resignation_date is None or self.resignation_date > models.functions.Now()

    def get_display_department(self):
        return self.department.description if self.department else 'N/A'

    def get_display_business_group(self):
        return self.bg_group.symbol if self.bg_group else 'N/A'

```

#### 4.2 Forms (`hr_staff/forms.py`)

This section defines the forms for staff search and for general CRUD operations on staff members.

```python
from django import forms
from .models import OfficeStaff, Department, BusinessGroup

class StaffSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'Dept Name'),
        ('2', 'BG Group'),
    ]
    
    # Initial 'Select' option from ASP.NET is not directly mapped here
    # It will be handled by UI logic in Alpine.js / HTMX to toggle input visibility
    
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Search By"
    )
    
    search_term_employee = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'id': 'TxtEmpName', # Matches ASP.NET ID for mental mapping
            'hx-get': '/hr_staff/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        }),
        label="" # Label handled by placeholder or layout
    )
    
    search_term_other = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search term',
            'id': 'TxtMrs', # Matches ASP.NET ID for mental mapping
        }),
        label=""
    )

class OfficeStaffForm(forms.ModelForm):
    class Meta:
        model = OfficeStaff
        fields = [
            'emp_id', 'employee_name', 'department', 'bg_group',
            'designation', 'mobile_no', 'joining_date', 'resignation_date'
        ]
        widgets = {
            'emp_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bg_group': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'designation': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mobile_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'joining_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'resignation_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
        labels = {
            'emp_id': 'Employee ID',
            'employee_name': 'Employee Name',
            'department': 'Department',
            'bg_group': 'Business Group',
        }
        
    def clean_emp_id(self):
        emp_id = self.cleaned_data['emp_id']
        # Example validation: ensure emp_id is unique if creating new
        if self.instance.pk is None and OfficeStaff.objects.filter(emp_id=emp_id).exists():
            raise forms.ValidationError("An employee with this ID already exists.")
        return emp_id

```

#### 4.3 Views (`hr_staff/views.py`)

Django Class-Based Views (CBVs) are used for common CRUD operations. All business logic for data retrieval and manipulation is delegated to the `OfficeStaff` model or its manager, keeping views thin.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from tablib import Dataset # For Excel export

from .models import OfficeStaff, Department, BusinessGroup
from .forms import OfficeStaffForm, StaffSearchForm

# Assuming Company ID and Financial Year are accessible, perhaps from session or a middleware
# For demonstration, we'll use placeholder values.
# In a real app, this would be from request.user, session, or a site-wide config.
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR = '2023-2024'

class OfficeStaffListView(ListView):
    model = OfficeStaff
    template_name = 'hr_staff/officestaff/list.html'
    context_object_name = 'staff_list' # Renamed for clarity in template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = StaffSearchForm(self.request.GET)
        return context

    # This view primarily renders the page structure; the table content is loaded via HTMX
    # The actual data filtering happens in the _OfficeStaffTablePartialView
    def get_queryset(self):
        # Return an empty queryset for the initial load, as data is fetched by HTMX
        return OfficeStaff.objects.none()

class OfficeStaffTablePartialView(ListView):
    model = OfficeStaff
    template_name = 'hr_staff/officestaff/_staff_table.html'
    context_object_name = 'staff_list'

    def get_queryset(self):
        form = StaffSearchForm(self.request.GET)
        search_field = form.cleaned_data.get('search_field') if form.is_valid() else '0' # Default to Emp Name
        search_term = form.cleaned_data.get('search_term_employee') if form.is_valid() and search_field == '0' else \
                      form.cleaned_data.get('search_term_other') if form.is_valid() else ''

        # Replace with actual session/user data in production
        comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        fin_year = self.request.session.get('finyear', DEFAULT_FIN_YEAR)
        
        return OfficeStaff.objects.search_staff(search_field, search_term, comp_id, fin_year)

class OfficeStaffCreateView(CreateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr_staff/officestaff/form.html'
    success_url = reverse_lazy('officestaff_list') # Redirect to list view

    def form_valid(self, form):
        # Add session-dependent fields before saving
        form.instance.comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        form.instance.fin_year = self.request.session.get('finyear', DEFAULT_FIN_FINYEAR)
        response = super().form_valid(form)
        messages.success(self.request, 'Office Staff added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX success
                headers={
                    'HX-Trigger': 'refreshStaffList' # Custom event to trigger table refresh
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, if form is invalid, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class OfficeStaffUpdateView(UpdateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr_staff/officestaff/form.html'
    success_url = reverse_lazy('officestaff_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Office Staff updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshStaffList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class OfficeStaffDeleteView(DeleteView):
    model = OfficeStaff
    template_name = 'hr_staff/officestaff/confirm_delete.html'
    success_url = reverse_lazy('officestaff_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Office Staff deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshStaffList'
                }
            )
        return response

# View for displaying staff details in a modal (when 'select' is clicked)
class OfficeStaffDetailView(View):
    def get(self, request, pk):
        staff_member = OfficeStaff.objects.get(pk=pk)
        return render(request, 'hr_staff/officestaff/_staff_detail_modal.html', {'staff': staff_member})


class StaffAutocompleteView(View):
    """
    Provides autocomplete suggestions for employee names.
    This replaces the C# GetCompletionList WebMethod.
    """
    def get(self, request):
        query = request.GET.get('q', '')
        # Replace with actual session/user data in production
        comp_id = request.session.get('compid', DEFAULT_COMP_ID)

        suggestions = OfficeStaff.objects.filter(
            Q(employee_name__icontains=query) | Q(emp_id__icontains=query),
            comp_id=comp_id
        ).values('employee_name', 'emp_id')[:10] # Limit to 10 suggestions

        # Format as "EmployeeName [EmpId]" similar to ASP.NET output
        formatted_suggestions = [f"{s['employee_name']} [{s['emp_id']}]" for s in suggestions]
        
        # HTMX expects HTML for direct swap into a target, not JSON for a dropdown
        # So we render a small HTML snippet with suggestions
        return render(request, 'hr_staff/officestaff/_autocomplete_suggestions.html', {'suggestions': formatted_suggestions})

class StaffExportExcelView(View):
    """
    Handles exporting filtered staff data to Excel.
    Replaces btnExportToExcel_Click.
    """
    def get(self, request):
        form = StaffSearchForm(request.GET)
        search_field = form.cleaned_data.get('search_field') if form.is_valid() else '0'
        search_term = form.cleaned_data.get('search_term_employee') if form.is_valid() and search_field == '0' else \
                      form.cleaned_data.get('search_term_other') if form.is_valid() else ''

        comp_id = request.session.get('compid', DEFAULT_COMP_ID)
        fin_year = request.session.get('finyear', DEFAULT_FIN_YEAR)

        staff_data = OfficeStaff.objects.search_staff(search_field, search_term, comp_id, fin_year)

        if not staff_data.exists():
            messages.warning(request, "No records to export.")
            # For HTMX, could return an empty response with trigger for a message
            # For direct GET, redirect back or show message on next load
            return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'}) # Example HTMX response for message

        dataset = Dataset()
        # Define headers based on GridView
        headers = [
            'SN', 'EmpId', 'Employee Name', 'Department Name', 'BG Group', 
            'Designation', 'Mobile No', 'Joining Date', 'Resignation Date', 'Fin Year'
        ]
        dataset.headers = headers

        for i, staff in enumerate(staff_data):
            dataset.append([
                i + 1, # SN
                staff.emp_id,
                staff.employee_name,
                staff.get_display_department(), # Use model method for display
                staff.get_display_business_group(), # Use model method for display
                staff.designation,
                staff.mobile_no,
                staff.joining_date.strftime('%Y-%m-%d') if staff.joining_date else '',
                staff.resignation_date.strftime('%Y-%m-%d') if staff.resignation_date else '',
                staff.fin_year
            ])

        response = HttpResponse(dataset.export('xlsx'), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="Staff_Details.xlsx"'
        return response

```

#### 4.4 Templates (`hr_staff/templates/hr_staff/officestaff/`)

**`list.html`**
This is the main page for listing staff, containing the search form and a container where the staff table will be loaded via HTMX.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Staff - Print</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'officestaff_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Staff
        </button>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form id="staff-search-form" hx-get="{% url 'officestaff_table' %}" hx-target="#staffTable-container" hx-swap="innerHTML" hx-trigger="submit">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div x-data="{ selectedField: '{{ search_form.search_field.value|default:'0' }}' }" class="flex items-center space-x-4 w-full">
                    <div class="flex-shrink-0">
                        <label for="{{ search_form.search_field.id_for_label }}" class="sr-only">Search By</label>
                        <select id="{{ search_form.search_field.id_for_label }}" name="{{ search_form.search_field.name }}" 
                                x-model="selectedField"
                                class="{{ search_form.search_field.field.widget.attrs.class }}">
                            {% for value, label in search_form.search_field.field.choices %}
                                <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="relative flex-grow">
                        <template x-if="selectedField === '0'">
                            <div>
                                <label for="{{ search_form.search_term_employee.id_for_label }}" class="sr-only">Employee Name</label>
                                <input type="text" name="{{ search_form.search_term_employee.name }}" 
                                       id="{{ search_form.search_term_employee.id_for_label }}" 
                                       class="{{ search_form.search_term_employee.field.widget.attrs.class }}" 
                                       placeholder="{{ search_form.search_term_employee.field.widget.attrs.placeholder }}"
                                       hx-get="{{ search_form.search_term_employee.field.widget.attrs.hx-get }}"
                                       hx-trigger="{{ search_form.search_term_employee.field.widget.attrs.hx-trigger }}"
                                       hx-target="{{ search_form.search_term_employee.field.widget.attrs.hx-target }}"
                                       hx-swap="{{ search_form.search_term_employee.field.widget.attrs.hx-swap }}"
                                       autocomplete="{{ search_form.search_term_employee.field.widget.attrs.autocomplete }}"
                                       value="{{ search_form.search_term_employee.value|default:'' }}">
                                <div id="autocomplete-results" class="absolute z-10 w-full bg-white border border-gray-300 mt-1 rounded-md shadow-lg overflow-hidden"></div>
                            </div>
                        </template>
                        <template x-if="selectedField !== '0'">
                            <div>
                                <label for="{{ search_form.search_term_other.id_for_label }}" class="sr-only">Search Term</label>
                                <input type="text" name="{{ search_form.search_term_other.name }}" 
                                       id="{{ search_form.search_term_other.id_for_label }}" 
                                       class="{{ search_form.search_term_other.field.widget.attrs.class }}" 
                                       placeholder="Enter search term"
                                       value="{{ search_form.search_term_other.value|default:'' }}">
                            </div>
                        </template>
                    </div>
                    
                    <div class="flex-shrink-0 flex space-x-2">
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                            Search
                        </button>
                        <a href="{% url 'officestaff_export_excel' %}" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                            Export
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <div id="staffTable-container"
         hx-trigger="load, refreshStaffList from:body, submit from:#staff-search-form"
         hx-get="{% url 'officestaff_table' %}"
         hx-target="#staffTable-container"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Staff Data...</p>
        </div>
    </div>
    
    <!-- Modal for forms and details -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is initialized automatically when included via base.html
    // You might add specific Alpine.js component data if needed here,
    // though the current design uses it for simple UI state (like dropdown-based input visibility).
    document.addEventListener('alpine:init', () => {
        // Any global Alpine data or components
    });

    // Custom event listener for messages, triggered by HX-Trigger on success/error
    document.body.addEventListener('showMessage', function() {
        // You would typically have a dedicated message display component here
        // For demonstration, a simple alert
        alert("Operation completed successfully or encountered an issue. Please check the console for details.");
    });

    // Handle autocomplete selection and clear suggestions
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'autocomplete-results') {
            document.querySelectorAll('#autocomplete-results div[data-value]').forEach(item => {
                item.addEventListener('click', function() {
                    document.getElementById('TxtEmpName').value = this.dataset.value;
                    document.getElementById('autocomplete-results').innerHTML = ''; // Clear suggestions
                });
            });
        }
    });

</script>
{% endblock %}

```

**`_staff_table.html`**
This partial template renders the DataTable. It's designed to be swapped in via HTMX.

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="officeStaffTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EmpId</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resignation Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for staff in staff_list %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.emp_id }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.employee_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.get_display_department }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.get_display_business_group }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.designation }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.mobile_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.joining_date|date:"Y-m-d"|default:'' }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ staff.resignation_date|date:"Y-m-d"|default:'' }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs"
                        hx-get="{% url 'officestaff_detail' pk=staff.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        View Details
                    </button>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs"
                        hx-get="{% url 'officestaff_edit' pk=staff.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md text-xs"
                        hx-get="{% url 'officestaff_delete' pk=staff.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 px-6 text-center text-sm text-gray-500">No staff records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized ONLY ONCE for the table after it's swapped in.
    // Destroy existing instance if it exists to prevent re-initialization errors.
    if ($.fn.DataTable.isDataTable('#officeStaffTable')) {
        $('#officeStaffTable').DataTable().destroy();
    }
    $(document).ready(function() {
        $('#officeStaffTable').DataTable({
            "pageLength": 20, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "pagingType": "simple_numbers", // or "full_numbers"
            "autoWidth": false,
            "responsive": true
        });
    });
</script>
```

**`_form.html`**
This partial template is used for both creating and updating staff records within a modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Office Staff</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="json-enc">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-md shadow-sm">
                Save Staff
            </button>
        </div>
    </form>
</div>
```

**`_confirm_delete.html`**
This partial template provides a delete confirmation dialog within a modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete staff member: **{{ object.employee_name }} ({{ object.emp_id }})**?</p>
    <form hx-post="{% url 'officestaff_delete' pk=object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-md shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`_autocomplete_suggestions.html`**
This partial template renders the autocomplete suggestions.

```html
{% if suggestions %}
    {% for suggestion in suggestions %}
        <div class="p-2 hover:bg-gray-100 cursor-pointer text-gray-800" data-value="{{ suggestion }}">{{ suggestion }}</div>
    {% endfor %}
{% else %}
    <div class="p-2 text-gray-500">No suggestions</div>
{% endif %}
```

**`_staff_detail_modal.html`**
This partial template displays staff details within a modal when 'select' is clicked.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Staff Details: {{ staff.employee_name }}</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-gray-700">
        <div>
            <p><strong class="font-medium">Employee ID:</strong> {{ staff.emp_id }}</p>
            <p><strong class="font-medium">Employee Name:</strong> {{ staff.employee_name }}</p>
            <p><strong class="font-medium">Department:</strong> {{ staff.get_display_department }}</p>
            <p><strong class="font-medium">Business Group:</strong> {{ staff.get_display_business_group }}</p>
        </div>
        <div>
            <p><strong class="font-medium">Designation:</strong> {{ staff.designation|default:"N/A" }}</p>
            <p><strong class="font-medium">Mobile No:</strong> {{ staff.mobile_no|default:"N/A" }}</p>
            <p><strong class="font-medium">Joining Date:</strong> {{ staff.joining_date|date:"Y-m-d"|default:"N/A" }}</p>
            <p><strong class="font-medium">Resignation Date:</strong> {{ staff.resignation_date|date:"Y-m-d"|default:"N/A" }}</p>
        </div>
    </div>
    <div class="mt-8 flex justify-end">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md shadow-sm"
            _="on click remove .is-active from #modal">
            Close
        </button>
    </div>
</div>
```

#### 4.5 URLs (`hr_staff/urls.py`)

This file defines the URL patterns for your Django application, mapping URLs to views.

```python
from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffTablePartialView, 
    OfficeStaffCreateView, OfficeStaffUpdateView, OfficeStaffDeleteView,
    OfficeStaffDetailView, StaffAutocompleteView, StaffExportExcelView
)

urlpatterns = [
    # Main Staff List Page
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    
    # HTMX partial for the staff table (used for initial load and search/refresh)
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    
    # CRUD operations
    path('staff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('staff/edit/<str:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'), # pk is str (EmpId)
    path('staff/delete/<str:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'), # pk is str (EmpId)
    
    # Detail view (for "select" action in modal)
    path('staff/detail/<str:pk>/', OfficeStaffDetailView.as_view(), name='officestaff_detail'), # pk is str (EmpId)

    # Autocomplete endpoint for employee name search
    path('staff/autocomplete/', StaffAutocompleteView.as_view(), name='officestaff_autocomplete'),
    
    # Export to Excel
    path('staff/export/', StaffExportExcelView.as_view(), name='officestaff_export_excel'),
]

```

**Important:** Remember to include this `hr_staff/urls.py` in your project's main `urls.py`:
`path('hr/', include('hr_staff.urls')),`

#### 4.6 Tests (`hr_staff/tests.py`)

Comprehensive tests for models and views ensure the reliability and correctness of your migrated application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import OfficeStaff, Department, BusinessGroup
from unittest.mock import patch, MagicMock
from django.http import HttpResponse

# Constants for default session values in tests
TEST_COMP_ID = 1
TEST_FIN_YEAR = '2023-2024'

class DepartmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a dummy department entry
        Department.objects.create(id=1, description='IT')
        Department.objects.create(id=2, description='HR')

    def test_department_creation(self):
        dept = Department.objects.get(id=1)
        self.assertEqual(dept.description, 'IT')
        self.assertEqual(str(dept), 'IT')
        self.assertEqual(dept._meta.db_table, 'tblHR_Departments')

    def test_department_verbose_name(self):
        self.assertEqual(Department._meta.verbose_name, 'Department')
        self.assertEqual(Department._meta.verbose_name_plural, 'Departments')

class BusinessGroupModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a dummy business group entry
        BusinessGroup.objects.create(id=10, symbol='BG1')
        BusinessGroup.objects.create(id=11, symbol='BG2')

    def test_business_group_creation(self):
        bg = BusinessGroup.objects.get(id=10)
        self.assertEqual(bg.symbol, 'BG1')
        self.assertEqual(str(bg), 'BG1')
        self.assertEqual(bg._meta.db_table, 'BusinessGroup')

    def test_business_group_verbose_name(self):
        self.assertEqual(BusinessGroup._meta.verbose_name, 'Business Group')
        self.assertEqual(BusinessGroup._meta.verbose_name_plural, 'Business Groups')

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create related objects
        dept = Department.objects.create(id=1, description='IT')
        bg = BusinessGroup.objects.create(id=10, symbol='BG1')

        # Create test staff data
        OfficeStaff.objects.create(
            emp_id='EMP001',
            employee_name='John Doe',
            department=dept,
            bg_group=bg,
            designation='Developer',
            mobile_no='1234567890',
            joining_date='2020-01-01',
            resignation_date=None,
            fin_year=TEST_FIN_YEAR,
            comp_id=TEST_COMP_ID,
            user_id=101
        )
        OfficeStaff.objects.create(
            emp_id='EMP002',
            employee_name='Jane Smith',
            department=Department.objects.create(id=2, description='HR'),
            bg_group=BusinessGroup.objects.create(id=11, symbol='BG2'),
            designation='HR Manager',
            mobile_no='0987654321',
            joining_date='2019-05-15',
            resignation_date='2023-12-31',
            fin_year=TEST_FIN_YEAR,
            comp_id=TEST_COMP_ID,
            user_id=102
        )

    def test_officestaff_creation(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(staff.employee_name, 'John Doe')
        self.assertEqual(staff.department.description, 'IT')
        self.assertEqual(staff.bg_group.symbol, 'BG1')
        self.assertEqual(staff.fin_year, TEST_FIN_YEAR)
        self.assertEqual(staff.comp_id, TEST_COMP_ID)
        self.assertEqual(str(staff), 'John Doe (EMP001)')

    def test_officestaff_db_table_and_managed(self):
        self.assertEqual(OfficeStaff._meta.db_table, 'tblHR_OfficeStaff')
        self.assertFalse(OfficeStaff._meta.managed)

    def test_is_active_method(self):
        staff_active = OfficeStaff.objects.get(emp_id='EMP001')
        staff_inactive = OfficeStaff.objects.get(emp_id='EMP002')
        self.assertTrue(staff_active.is_active())
        self.assertFalse(staff_inactive.is_active())

    def test_get_display_department(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(staff.get_display_department(), 'IT')
        staff_no_dept = OfficeStaff.objects.create(
            emp_id='EMP003', employee_name='No Dept', department=None,
            fin_year=TEST_FIN_YEAR, comp_id=TEST_COMP_ID
        )
        self.assertEqual(staff_no_dept.get_display_department(), 'N/A')

    def test_get_display_business_group(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(staff.get_display_business_group(), 'BG1')
        staff_no_bg = OfficeStaff.objects.create(
            emp_id='EMP004', employee_name='No BG', bg_group=None,
            fin_year=TEST_FIN_YEAR, comp_id=TEST_COMP_ID
        )
        self.assertEqual(staff_no_bg.get_display_business_group(), 'N/A')
    
    def test_search_staff_by_employee_name(self):
        # Case-insensitive partial match
        staff_list = OfficeStaff.objects.search_staff('0', 'john', TEST_COMP_ID, TEST_FIN_YEAR)
        self.assertEqual(staff_list.count(), 1)
        self.assertEqual(staff_list.first().emp_id, 'EMP001')

        # Search by emp_id
        staff_list = OfficeStaff.objects.search_staff('0', 'EMP001', TEST_COMP_ID, TEST_FIN_YEAR)
        self.assertEqual(staff_list.count(), 1)
        self.assertEqual(staff_list.first().employee_name, 'John Doe')

    def test_search_staff_by_department_name(self):
        staff_list = OfficeStaff.objects.search_staff('1', 'IT', TEST_COMP_ID, TEST_FIN_YEAR)
        self.assertEqual(staff_list.count(), 1)
        self.assertEqual(staff_list.first().employee_name, 'John Doe')

        staff_list_not_found = OfficeStaff.objects.search_staff('1', 'NonExistent', TEST_COMP_ID, TEST_FIN_YEAR)
        self.assertEqual(staff_list_not_found.count(), 0)

    def test_search_staff_by_business_group_symbol(self):
        staff_list = OfficeStaff.objects.search_staff('2', 'BG1', TEST_COMP_ID, TEST_FIN_YEAR)
        self.assertEqual(staff_list.count(), 1)
        self.assertEqual(staff_list.first().employee_name, 'John Doe')

        staff_list_not_found = OfficeStaff.objects.search_staff('2', 'NonExistentBG', TEST_COMP_ID, TEST_FIN_YEAR)
        self.assertEqual(staff_list_not_found.count(), 0)

    def test_search_staff_no_term(self):
        staff_list = OfficeStaff.objects.search_staff('0', '', TEST_COMP_ID, TEST_FIN_YEAR)
        self.assertEqual(staff_list.count(), 2) # Both EMP001 and EMP002

class OfficeStaffViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        dept = Department.objects.create(id=1, description='IT')
        bg = BusinessGroup.objects.create(id=10, symbol='BG1')
        OfficeStaff.objects.create(
            emp_id='EMP001', employee_name='John Doe', department=dept, bg_group=bg,
            designation='Developer', mobile_no='1234567890', joining_date='2020-01-01',
            fin_year=TEST_FIN_YEAR, comp_id=TEST_COMP_ID
        )
        OfficeStaff.objects.create(
            emp_id='EMP002', employee_name='Jane Smith', department=dept, bg_group=bg,
            designation='QA', mobile_no='0987654321', joining_date='2021-01-01',
            fin_year=TEST_FIN_YEAR, comp_id=TEST_COMP_ID
        )

    def setUp(self):
        self.client = Client()
        # Mock session for compid and finyear
        session = self.client.session
        session['compid'] = TEST_COMP_ID
        session['finyear'] = TEST_FIN_YEAR
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('officestaff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/list.html')
        self.assertIn('search_form', response.context)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('officestaff_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/_staff_table.html')
        self.assertIn('staff_list', response.context)
        self.assertEqual(len(response.context['staff_list']), 2) # All staff members

    def test_table_partial_view_search_employee_name(self):
        response = self.client.get(reverse('officestaff_table'), {'search_field': '0', 'search_term_employee': 'John'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['staff_list']), 1)
        self.assertEqual(response.context['staff_list'][0].employee_name, 'John Doe')

    def test_table_partial_view_search_department_name(self):
        response = self.client.get(reverse('officestaff_table'), {'search_field': '1', 'search_term_other': 'IT'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['staff_list']), 2) # Both John and Jane are in IT

    def test_create_view_get(self):
        response = self.client.get(reverse('officestaff_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        new_dept = Department.objects.create(id=3, description='Sales')
        new_bg = BusinessGroup.objects.create(id=12, symbol='SALES')
        data = {
            'emp_id': 'EMP003',
            'employee_name': 'Alice Brown',
            'department': new_dept.id,
            'bg_group': new_bg.id,
            'designation': 'Sales Rep',
            'mobile_no': '1112223333',
            'joining_date': '2022-03-01',
            # comp_id and fin_year are set by view from session
        }
        response = self.client.post(reverse('officestaff_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(OfficeStaff.objects.filter(emp_id='EMP003').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStaffList')

    def test_create_view_post_invalid(self):
        data = {
            'emp_id': 'EMP001', # Duplicate emp_id
            'employee_name': 'Duplicate Guy',
            'department': Department.objects.first().id,
            'bg_group': BusinessGroup.objects.first().id,
            'joining_date': '2020-01-01',
        }
        response = self.client.post(reverse('officestaff_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'hr_staff/officestaff/form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('emp_id', response.context['form'].errors)

    def test_update_view_get(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        response = self.client.get(reverse('officestaff_edit', args=[staff.emp_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.emp_id, 'EMP001')

    def test_update_view_post_success(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        data = {
            'emp_id': staff.emp_id, # EmpId shouldn't change
            'employee_name': 'John Doe Updated',
            'department': staff.department.id,
            'bg_group': staff.bg_group.id,
            'designation': 'Senior Developer',
            'mobile_no': '1234567890',
            'joining_date': '2020-01-01',
        }
        response = self.client.post(reverse('officestaff_edit', args=[staff.emp_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        staff.refresh_from_db()
        self.assertEqual(staff.employee_name, 'John Doe Updated')
        self.assertEqual(staff.designation, 'Senior Developer')
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        response = self.client.get(reverse('officestaff_delete', args=[staff.emp_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].emp_id, 'EMP001')

    def test_delete_view_post_success(self):
        staff_to_delete = OfficeStaff.objects.get(emp_id='EMP002')
        response = self.client.post(reverse('officestaff_delete', args=[staff_to_delete.emp_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(OfficeStaff.objects.filter(emp_id='EMP002').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStaffList')

    def test_autocomplete_staff_name(self):
        response = self.client.get(reverse('officestaff_autocomplete'), {'q': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/_autocomplete_suggestions.html')
        self.assertIn('John Doe [EMP001]', response.content.decode())

    def test_autocomplete_staff_name_no_results(self):
        response = self.client.get(reverse('officestaff_autocomplete'), {'q': 'xyz'})
        self.assertEqual(response.status_code, 200)
        self.assertIn('No suggestions', response.content.decode())

    @patch('hr_staff.views.Dataset') # Mock tablib.Dataset
    def test_export_excel_view(self, MockDataset):
        mock_dataset_instance = MockDataset.return_value
        mock_dataset_instance.export.return_value = b'excel_data'

        response = self.client.get(reverse('officestaff_export_excel'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="Staff_Details.xlsx"')
        self.assertEqual(response.content, b'excel_data')
        MockDataset.assert_called_once()
        mock_dataset_instance.append.assert_called()
        mock_dataset_instance.export.assert_called_once_with('xlsx')

    @patch('hr_staff.views.messages') # Mock Django messages
    def test_export_excel_view_no_data(self, mock_messages):
        # Temporarily delete existing staff to simulate no data
        OfficeStaff.objects.all().delete()
        response = self.client.get(reverse('officestaff_export_excel'))
        self.assertEqual(response.status_code, 204) # HTMX no content
        mock_messages.warning.assert_called_once_with(self.client.request, "No records to export.")
        self.assertIn('HX-Trigger', response.headers) # HX-Trigger for message

    def test_officestaff_detail_view(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        response = self.client.get(reverse('officestaff_detail', args=[staff.emp_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/_staff_detail_modal.html')
        self.assertIn('staff', response.context)
        self.assertEqual(response.context['staff'].emp_id, 'EMP001')

```

### Step 5: HTMX and Alpine.js Integration

The provided templates and views demonstrate the seamless integration:

*   **HTMX for Dynamic Updates:**
    *   The main list page (`list.html`) uses `hx-get` to load the table content from `{% url 'officestaff_table' %}` on `load` and `refreshStaffList` events.
    *   The search form uses `hx-get` on `submit` to update the `staffTable-container`.
    *   CRUD operations (Add, Edit, Delete) are triggered by `hx-get` on buttons, loading forms/confirmations into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) return `status=204` with an `HX-Trigger: refreshStaffList` header on success, prompting the table to reload without a full page refresh. Invalid forms re-render the form with errors.
    *   Autocomplete uses `hx-get` on `keyup` to fetch suggestions, which are then rendered into a `div` below the input.
*   **Alpine.js for UI State:**
    *   Used in `list.html` to toggle the visibility of the search input fields (`search_term_employee` vs `search_term_other`) based on the `DrpField` selection (`selectedField`). This eliminates the need for server-side postbacks for simple UI changes.
    *   The modal (`#modal`) uses Alpine.js's `x-data` and `_` (hyperscript) for showing/hiding based on click events.
*   **DataTables for List Views:**
    *   The `_staff_table.html` partial initializes jQuery DataTables on the `<table>` element with specific options for pagination, length menu, etc. The `$(document).ready` is inside the partial, ensuring it runs *after* HTMX has swapped in the new table content. A check for `$.fn.DataTable.isDataTable` and `destroy()` prevents re-initialization issues.
*   **No Additional JavaScript:** All interactions are handled directly by HTMX attributes or minimal Alpine.js, fulfilling the requirement of avoiding custom JavaScript beyond these two libraries and DataTables.
*   **DRY Template Inheritance:** All templates explicitly extend `core/base.html` (not provided in this output as per instructions) ensuring common layouts, CSS (Tailwind), and CDN includes (HTMX, Alpine.js, jQuery for DataTables, DataTables itself) are managed centrally.

### Final Notes

*   **Placeholders:** Replace `DEFAULT_COMP_ID` and `DEFAULT_FIN_YEAR` with actual values derived from your authentication and session management system in Django.
*   **Error Handling:** The current setup uses Django's `messages` framework. For production, you might want a more sophisticated way to display these messages dynamically using HTMX/Alpine.js (e.g., a toast notification component).
*   **`fun.getCode` replacement:** The C# `fun.getCode` function likely handled extracting employee IDs from combined strings (e.g., "Employee Name [EmpId]"). In the Django approach, the `autocomplete_staff_name` view provides the formatted string, and client-side logic (e.g., Alpine.js or simple JS event listeners added after HTMX swap) would typically parse this back to just the `EmpId` if it needs to be sent to the server for filtering, or the `OfficeStaff.objects.search_staff` method handles both `employee_name` and `emp_id` in its `Q` object.
*   **Export Library:** `tablib` is recommended for Excel export. Ensure it is installed (`pip install tablib`).
*   **Security:** This plan assumes basic Django security measures (CSRF protection, etc.). In a real-world scenario, you'd integrate proper authentication and authorization (`@login_required`, `PermissionRequiredMixin`) into your views.