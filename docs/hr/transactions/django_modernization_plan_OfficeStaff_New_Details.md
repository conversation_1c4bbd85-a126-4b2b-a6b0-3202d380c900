## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code, particularly the `SqlDataSource` components and the `BtnSubmit_Click` method's `INSERT` statement, the primary table for staff details is `tblHR_OfficeStaff`. Several other tables are used for lookup data.

**Primary Table:** `tblHR_OfficeStaff`

**Key Columns from `tblHR_OfficeStaff` (and inferred data types for Django):**

*   `OfferId`: Integer (from `Request.QueryString`)
*   `EmpId`: String (auto-generated, e.g., 'CMP0001')
*   `SysDate`: Date (creation date)
*   `SysTime`: Time (creation time)
*   `FinYearId`: Integer (from session)
*   `CompId`: Integer (from session)
*   `SessionId`: String (user who created, from session `username`)
*   `Title`: String (e.g., 'Mr', 'Mrs', 'Miss')
*   `EmployeeName`: String
*   `SwapCardNo`: Integer (Foreign Key to `tblHR_SwapCard.Id`)
*   `Department`: Integer (Foreign Key to `tblHR_Departments.Id`)
*   `BGGroup`: Integer (Foreign Key to `BusinessGroup.Id`)
*   `DirectorsName`: Integer (Foreign Key to `tblHR_OfficeStaff.UserId` - assumed to be `Id` for self-reference)
*   `DeptHead`: Integer (Foreign Key to `tblHR_OfficeStaff.UserId`)
*   `GroupLeader`: Integer (Foreign Key to `tblHR_OfficeStaff.UserId`)
*   `Designation`: Integer (Foreign Key to `tblHR_Designation.Id`)
*   `Grade`: Integer (Foreign Key to `tblHR_Grade.Id`)
*   `MobileNo`: Integer (Foreign Key to `tblHR_CoporateMobileNo.Id`)
*   `ContactNo`: String
*   `CompanyEmail`: String (Company email)
*   `EmailId1`: String (ERP email)
*   `ExtensionNo`: Integer (Foreign Key to `tblHR_IntercomExt.Id`)
*   `JoiningDate`: Date
*   `ResignationDate`: Date
*   `PhotoFileName`: String
*   `PhotoSize`: Integer
*   `PhotoContentType`: String
*   `PhotoData`: Binary (file content)
*   `PermanentAddress`: String
*   `CorrespondenceAddress`: String
*   `EmailId2`: String (Personal email)
*   `DateOfBirth`: Date
*   `Gender`: String ('M', 'F')
*   `MartialStatus`: Integer (1=Married, 0=Unmarried)
*   `BloodGroup`: String
*   `Height`: String (Likely a decimal, but captured as string in ASP.NET)
*   `Weight`: String (Likely a decimal, but captured as string in ASP.NET)
*   `PhysicallyHandycapped`: Integer (1=Yes, 0=No)
*   `Religion`: String
*   `Cast`: String
*   `EducationalQualification`: String
*   `AdditionalQualification`: String
*   `LastCompanyName`: String
*   `WorkingDuration`: String
*   `TotalExperience`: String
*   `CVFileName`: String
*   `CVSize`: Integer
*   `CVContentType`: String
*   `CVData`: Binary (file content)
*   `CurrentCTC`: String (Likely a decimal, but captured as string in ASP.NET)
*   `BankAccountNo`: String
*   `PFNo`: String
*   `PANNo`: String
*   `PassPortNo`: String
*   `ExpiryDate`: Date (for Passport)
*   `AdditionalInformation`: String

**Lookup Tables Identified:**

*   `tblHR_Designation`: Id, Type, Symbol
*   `tblHR_Departments`: Id, Description, Symbol
*   `tblHR_SwapCard`: Id, SwapCardNo
*   `tblHR_CoporateMobileNo`: Id, MobileNo
*   `BusinessGroup`: Id, Name, Symbol
*   `tblHR_Grade`: Id, Description, Symbol
*   `tblHR_IntercomExt`: Id, ExtNo
*   `tblHR_Offer_Master`: OfferId, Title, EmployeeName (Used for initial population)
*   `tblCompany_master`: CompId, Prefix (Used for EmpId generation)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET page primarily implements a **Create** operation for new staff members and a **Read** operation for populating dropdowns and initial data.

*   **Create**: Triggered by the `BtnSubmit_Click` event. It inserts a new record into `tblHR_OfficeStaff` with all the collected data from the multiple tabs. It also handles file uploads for photo and CV.
*   **Read**:
    *   On `Page_Load`, it retrieves `EmpId` prefix from `tblCompany_master` and the last `EmpId` from `tblHR_OfficeStaff` to generate a new `EmpId`.
    *   It populates initial `TxtEmpName` and `DrpEmpTitle` from `tblHR_Offer_Master` using the `OfferId` from the query string.
    *   Dropdowns (`DrpDesignation`, `DrpDepartment`, `DrpSwapcardNo`, `DrpMobileNo`, `DrpDirectorName`, `DrpBGGroup`, `DrpGroupLeader`, `DrpDeptHead`, `DrpGrade`, `DrpExtensionNo`) are populated directly from `SqlDataSource` components or custom SQL queries, fetching lookup data and filtering out already used `SwapCardNo` and `MobileNo`.
*   **Update**: Not explicitly found in the provided code, but typically implied for a "Details" page. For a full CRUD, this would be a likely next step. We will include it in the Django plan for completeness as per modernization best practices.
*   **Delete**: Not explicitly found. We will include it for full CRUD.
*   **Validation Logic**: Extensive client-side (`RequiredFieldValidator`, `RegularExpressionValidator`) and server-side validation (`fun.EmailValidation`, `fun.DateValidation`, checks for empty strings on required fields) are present and need to be replicated in Django forms.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The UI uses a tabbed interface (`AjaxControlToolkit:TabContainer`) to organize input fields for staff details.

*   **Main Structure**: A `TabContainer` with four `TabPanel`s: "Official Info", "Personal Info", "Edu. Quali. & Work Experience", and "Others".
*   **Input Controls**:
    *   `asp:TextBox`: Used for employee name, contact number, emails, addresses, dates, height, weight, religion, cast, qualifications, company name, experience, CTC, bank details, passport info, and additional information.
    *   `asp:DropDownList`: Used for employee title, designation, department, swap card number, director/group leader/department head, business group, grade, corporate mobile number, extension number, gender, and blood group. These are mostly data-bound to SQL queries.
    *   `asp:RadioButton`: Used for Martial Status (Married/Unmarried) and Physically Handicapped (Yes/No).
    *   `asp:Label`: Used for field labels and to display auto-generated `EmpId` and `OfferId`.
    *   `cc1:CalendarExtender`: Provides date picker functionality for date input fields.
    *   `asp:FileUpload`: For uploading employee photo and CV.
*   **Action Buttons**:
    *   `btnnxt`, `btnNext2`, `btnNext3`: Navigate between tabs. These perform client-side tab switching with `TabContainer1.ActiveTab = TabContainer1.Tabs[index]`.
    *   `BtnSubmit`: Submits the entire form.
    *   `BtnCancel`, `BtnCancel0`, `BtnCancel1`, `BtnCancel2`: Redirect back to `OfficeStaff_New.aspx`.
*   **Validation Controls**: `asp:RequiredFieldValidator` and `asp:RegularExpressionValidator` are used for client-side and server-side validation.

**Django Equivalent Strategy:**

*   The tabbed UI will be managed using Alpine.js for visual state and `x-show` directives within a single Django form template.
*   HTMX will be used for form submission and validation feedback, allowing partial updates without full page reloads.
*   All input controls (`TextBox`, `DropDownList`, `RadioButton`, `FileUpload`) will be replaced by Django form fields with appropriate widgets, styled using Tailwind CSS.
*   Date pickers will be integrated via Alpine.js or a small HTMX-compatible library.
*   File uploads will use Django `FileField` or `ImageField`.
*   Validation will be handled by Django Forms' built-in validation and custom validators.
*   The DataTables library will replace any implied list views and add robust client-side features.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `hr_staff`, to house the modernization of this ASP.NET module.

#### 4.1 Models

Task: Create Django models based on the database schema, including lookup tables.

## Instructions:

The `tblHR_OfficeStaff` table will be mapped to the `OfficeStaff` model. All lookup tables will also have corresponding models to facilitate foreign key relationships. The auto-generation of `EmpId` will be handled in the `OfficeStaff` model's `save` method.

```python
# hr_staff/models.py

from django.db import models
from django.conf import settings
from django.utils import timezone
import os
import re

# Helper models for lookup tables (managed=False to map to existing DB)

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.type} - {self.symbol}" if self.type and self.symbol else f"Designation {self.id}"

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return f"{self.description} - {self.symbol}" if self.description and self.symbol else f"Department {self.id}"

class SwapCard(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    swap_card_no = models.CharField(db_column='SwapCardNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_SwapCard'
        verbose_name = 'Swap Card'
        verbose_name_plural = 'Swap Cards'

    def __str__(self):
        return self.swap_card_no or f"Swap Card {self.id}"

class CorporateMobileNo(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        return self.mobile_no or f"Mobile No {self.id}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return f"{self.name} - {self.symbol}" if self.name and self.symbol else f"Business Group {self.id}"

class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return f"{self.description} - {self.symbol}" if self.description and self.symbol else f"Grade {self.id}"

class IntercomExtension(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ext_no = models.CharField(db_column='ExtNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_IntercomExt'
        verbose_name = 'Intercom Extension'
        verbose_name_plural = 'Intercom Extensions'

    def __str__(self):
        return self.ext_no or f"Extension {self.id}"

class OfferMaster(models.Model):
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    # Add other fields from tblHR_Offer_Master if needed

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Master'
        verbose_name_plural = 'Offer Masters'

    def __str__(self):
        return self.employee_name or f"Offer {self.offer_id}"

class CompanyMaster(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    prefix = models.CharField(db_column='Prefix', max_length=50, blank=True, null=True)
    # Add other fields from tblCompany_master if needed

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

    def __str__(self):
        return self.prefix or f"Company {self.comp_id}"


# Main Staff Model
class OfficeStaff(models.Model):
    # System fields (from ASP.NET context)
    offer = models.ForeignKey(OfferMaster, models.DO_NOTHING, db_column='OfferId', blank=True, null=True)
    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True, blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True) # Needs to be linked to a FinYear model
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Needs to be linked to a Company model
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # User ID who created

    # Official Info
    title = models.CharField(db_column='Title', max_length=10, default='Mr')
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    swap_card = models.ForeignKey(SwapCard, models.DO_NOTHING, db_column='SwapCardNo', blank=True, null=True)
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroup', blank=True, null=True)
    
    # Self-referential or user lookups
    director_name = models.ForeignKey('self', models.DO_NOTHING, db_column='DirectorsName', related_name='director_of', blank=True, null=True)
    dept_head = models.ForeignKey('self', models.DO_NOTHING, db_column='DeptHead', related_name='dept_head_of', blank=True, null=True)
    group_leader = models.ForeignKey('self', models.DO_NOTHING, db_column='GroupLeader', related_name='group_leader_of', blank=True, null=True)

    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    grade = models.ForeignKey(Grade, models.DO_NOTHING, db_column='Grade', blank=True, null=True)
    corporate_mobile_no = models.ForeignKey(CorporateMobileNo, models.DO_NOTHING, db_column='MobileNo', blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    company_email = models.CharField(db_column='CompanyEmail', max_length=255, blank=True, null=True)
    erp_email = models.CharField(db_column='EmailId1', max_length=255, blank=True, null=True)
    extension_no = models.ForeignKey(IntercomExtension, models.DO_NOTHING, db_column='ExtensionNo', blank=True, null=True)
    joining_date = models.DateField(db_column='JoiningDate', blank=True, null=True)
    resignation_date = models.DateField(db_column='ResignationDate', blank=True, null=True)

    # Photo & CV (will be handled by Django's FileField/ImageField)
    # The ASP.NET code stores actual binary data in DB. Django will store paths.
    # If the original DB stores binary, you might need a custom storage backend or migration script.
    # For modernization, we assume file storage on disk and only path in DB.
    photo_file_name = models.CharField(db_column='PhotoFileName', max_length=255, blank=True, null=True)
    photo_size = models.IntegerField(db_column='PhotoSize', blank=True, null=True)
    photo_content_type = models.CharField(db_column='PhotoContentType', max_length=100, blank=True, null=True)
    # photo_data = models.BinaryField(db_column='PhotoData', blank=True, null=True) # If storing binary
    photo = models.ImageField(upload_to='staff_photos/', db_column='PhotoData', blank=True, null=True) # Storing file path

    cv_file_name = models.CharField(db_column='CVFileName', max_length=255, blank=True, null=True)
    cv_size = models.IntegerField(db_column='CVSize', blank=True, null=True)
    cv_content_type = models.CharField(db_column='CVContentType', max_length=100, blank=True, null=True)
    # cv_data = models.BinaryField(db_column='CVData', blank=True, null=True) # If storing binary
    cv = models.FileField(upload_to='staff_cvs/', db_column='CVData', blank=True, null=True) # Storing file path


    # Personal Info
    permanent_address = models.TextField(db_column='PermanentAddress', blank=True, null=True)
    correspondence_address = models.TextField(db_column='CorrespondenceAddress', blank=True, null=True)
    personal_email = models.CharField(db_column='EmailId2', max_length=255, blank=True, null=True)
    date_of_birth = models.DateField(db_column='DateOfBirth', blank=True, null=True)
    gender = models.CharField(db_column='Gender', max_length=10, blank=True, null=True)
    martial_status = models.IntegerField(db_column='MartialStatus', blank=True, null=True) # 0 for Unmarried, 1 for Married
    blood_group = models.CharField(db_column='BloodGroup', max_length=10, blank=True, null=True)
    height = models.CharField(db_column='Height', max_length=50, blank=True, null=True) # Storing as CharField based on ASP.NET
    weight = models.CharField(db_column='Weight', max_length=50, blank=True, null=True) # Storing as CharField based on ASP.NET
    physically_handycapped = models.IntegerField(db_column='PhysicallyHandycapped', blank=True, null=True) # 0 for No, 1 for Yes
    religion = models.CharField(db_column='Religion', max_length=100, blank=True, null=True)
    cast = models.CharField(db_column='Cast', max_length=100, blank=True, null=True)

    # Educational Qualification & Work Experience
    educational_qualification = models.TextField(db_column='EducationalQualification', blank=True, null=True)
    additional_qualification = models.TextField(db_column='AdditionalQualification', blank=True, null=True)
    last_company_name = models.CharField(db_column='LastCompanyName', max_length=255, blank=True, null=True)
    working_duration = models.CharField(db_column='WorkingDuration', max_length=100, blank=True, null=True)
    total_experience = models.CharField(db_column='TotalExperience', max_length=100, blank=True, null=True)

    # Others
    current_ctc = models.CharField(db_column='CurrentCTC', max_length=50, blank=True, null=True) # Storing as CharField based on ASP.NET
    bank_account_no = models.CharField(db_column='BankAccountNo', max_length=50, blank=True, null=True)
    pf_no = models.CharField(db_column='PFNo', max_length=50, blank=True, null=True)
    pan_no = models.CharField(db_column='PANNo', max_length=50, blank=True, null=True)
    passport_no = models.CharField(db_column='PassPortNo', max_length=50, blank=True, null=True)
    passport_expiry_date = models.DateField(db_column='ExpiryDate', blank=True, null=True)
    additional_information = models.TextField(db_column='AdditionalInformation', blank=True, null=True)


    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name or f"Staff {self.pk}"

    def _generate_emp_id(self, comp_id):
        """
        Generates the next EmpId based on company prefix and last EmpId.
        This logic replicates the ASP.NET code-behind's custom ID generation.
        Assumes `CompanyMaster` is configured and accessible.
        """
        try:
            company_prefix = CompanyMaster.objects.get(comp_id=comp_id).prefix
        except CompanyMaster.DoesNotExist:
            company_prefix = "CMP" # Default if not found

        last_emp_id_obj = OfficeStaff.objects.filter(comp_id=comp_id).order_by('-id').first()
        if last_emp_id_obj and last_emp_id_obj.emp_id:
            match = re.search(r'\d+', last_emp_id_obj.emp_id)
            if match:
                last_num_str = match.group(0)
                next_num = int(last_num_str) + 1
                return f"{company_prefix}{next_num:04d}"
        return f"{company_prefix}0001"

    def save(self, *args, **kwargs):
        if not self.pk and not self.emp_id: # Only generate EmpId for new objects
            # Assuming comp_id is set before saving for new records
            # This requires comp_id to be passed to the form/view
            # For demonstration, we'll assume it's available or set a default.
            # In a real system, you'd link this to the logged-in user's company.
            current_comp_id = self.comp_id if self.comp_id else 1 # Default comp_id if not set
            self.emp_id = self._generate_emp_id(current_comp_id)

        if not self.sys_date:
            self.sys_date = timezone.localdate()
        if not self.sys_time:
            self.sys_time = timezone.localtime().time()
        
        # Handle file uploads if PhotoData and CVData fields are used for file paths
        # This assumes the original PhotoData/CVData columns are now storing file paths.
        # If they originally stored binary, a data migration would be needed.
        if self.photo and not isinstance(self.photo, str): # Check if it's a new file object
            self.photo_file_name = os.path.basename(self.photo.name)
            self.photo_size = self.photo.size
            self.photo_content_type = self.photo.file.content_type
        if self.cv and not isinstance(self.cv, str): # Check if it's a new file object
            self.cv_file_name = os.path.basename(self.cv.name)
            self.cv_size = self.cv.size
            self.cv_content_type = self.cv.file.content_type

        super().save(*args, **kwargs)

```

#### 4.2 Forms

Task: Define a Django form for user input, including handling dropdown population from related models.

## Instructions:

A single `ModelForm` for `OfficeStaff` will be created. Dropdown choices will be dynamically set in the form's `__init__` method using querysets. File fields and date fields will use appropriate widgets.

```python
# hr_staff/forms.py

from django import forms
from django.core.validators import RegexValidator
from .models import (
    OfficeStaff, Designation, Department, SwapCard, CorporateMobileNo,
    BusinessGroup, Grade, IntercomExtension, OfferMaster
)

class OfficeStaffForm(forms.ModelForm):
    # Custom choices for fields that were hardcoded or had specific filtering logic
    GENDER_CHOICES = [
        ('Select', 'Select'), # Mimic InitialValue="Select"
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    BLOOD_GROUP_CHOICES = [
        ('Select', 'Select'),
        ('Not Known', 'Not Known'),
        ('O+', 'O+'), ('O-', 'O-'),
        ('A+', 'A+'), ('A-', 'A-'),
        ('B+', 'B+'), ('B-', 'B-'),
        ('AB+', 'AB+'), ('AB-', 'AB-'),
    ]
    
    TITLE_CHOICES = [
        ('Mr', 'Mr'),
        ('Mrs', 'Mrs'),
        ('Miss', 'Miss'),
    ]

    # Redefine fields to use specific widgets and initial data logic
    title = forms.ChoiceField(choices=TITLE_CHOICES)
    joining_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off', # Prevent browser autocomplete
        }),
        required=True
    )
    resignation_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off',
        }),
        required=False
    )
    date_of_birth = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off',
        }),
        required=True
    )
    passport_expiry_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off',
        }),
        required=False
    )

    # Use BooleanField for radio buttons
    martial_status = forms.BooleanField(
        widget=forms.RadioSelect(choices=[(True, 'Married'), (False, 'Unmarried')]),
        required=False,
        initial=False # Default to Unmarried
    )
    physically_handycapped = forms.BooleanField(
        widget=forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')]),
        required=False,
        initial=False # Default to No
    )

    gender = forms.ChoiceField(
        choices=GENDER_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    blood_group = forms.ChoiceField(
        choices=BLOOD_GROUP_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # File fields
    photo = forms.ImageField(required=False)
    cv = forms.FileField(required=False)

    class Meta:
        model = OfficeStaff
        # Exclude auto-generated or system fields for direct form input
        exclude = [
            'offer', 'emp_id', 'sys_date', 'sys_time', 'fin_year_id',
            'comp_id', 'session_id', 'photo_file_name', 'photo_size', 'photo_content_type',
            'cv_file_name', 'cv_size', 'cv_content_type',
        ]
        # Map fields to their corresponding widgets with Tailwind CSS classes
        widgets = {
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'erp_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'permanent_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'correspondence_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'personal_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'height': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'weight': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'religion': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cast': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'educational_qualification': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'additional_qualification': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'last_company_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'working_duration': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_experience': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'current_ctc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_account_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'passport_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'additional_information': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'photo': forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}),
            'cv': forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}),
        }
        labels = {
            'erp_email': 'ERP-Mail',
            'personal_email': 'Personal Email',
            'passport_expiry_date': 'Passport Expiry Date',
            'contact_no': 'Contact No.',
            'corporate_mobile_no': 'Corp. Mobile No.',
            'swap_card': 'Swap Card No.',
            'business_group': 'Under BG',
            'director_name': 'Director of Dept.',
            'group_leader': 'Group Leader',
            'dept_head': 'Dept. Head',
            'joining_date': 'Joining Date',
            'resignation_date': 'Resignation Date',
            'date_of_birth': 'Date Of Birth',
            'martial_status': 'Martial Status',
            'physically_handycapped': 'Physically Handicapped',
            'educational_qualification': 'Educational Qualification',
            'additional_qualification': 'Additional Qualification',
            'last_company_name': 'Last Company Name',
            'total_experience': 'Total Experience',
            'working_duration': 'Working Duration',
            'current_ctc': 'Current CTC',
            'bank_account_no': 'Bank Acc No.',
            'pf_no': 'PF No.',
            'pan_no': 'PAN No.',
            'passport_no': 'Passport No.',
            'additional_information': 'Additional Information',
            'photo': 'Upload Photo',
            'cv': 'Upload CV',
        }

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None) # Pass comp_id from view
        offer_id = kwargs.pop('offer_id', None) # Pass offer_id from view
        super().__init__(*args, **kwargs)

        # Dynamically populate dropdowns
        self.fields['designation'].queryset = Designation.objects.all()
        self.fields['department'].queryset = Department.objects.all()
        self.fields['business_group'].queryset = BusinessGroup.objects.all()
        self.fields['grade'].queryset = Grade.objects.all()
        self.fields['extension_no'].queryset = IntercomExtension.objects.all()

        # Specific query for available swap cards
        # Mimic ASP.NET logic: 'Id=1' OR NOT IN (selected)
        used_swap_cards = OfficeStaff.objects.filter(comp_id=comp_id, swap_card__isnull=False).values_list('swap_card__id', flat=True)
        self.fields['swap_card'].queryset = SwapCard.objects.filter(
            forms.Q(id=1) | ~forms.Q(id__in=used_swap_cards)
        )
        self.fields['swap_card'].empty_label = "Select" # Mimic InitialValue="Select"

        # Specific query for available corporate mobile numbers
        # Mimic ASP.NET logic: 'Id=1' OR NOT IN (selected)
        used_mobile_nos = OfficeStaff.objects.filter(comp_id=comp_id, corporate_mobile_no__isnull=False).values_list('corporate_mobile_no__id', flat=True)
        self.fields['corporate_mobile_no'].queryset = CorporateMobileNo.objects.filter(
            forms.Q(id=1) | ~forms.Q(id__in=used_mobile_nos)
        )
        self.fields['corporate_mobile_no'].empty_label = "Select" # Mimic InitialValue="Select"

        # Dynamically populate Director, Dept Head, Group Leader based on Designation
        # Assuming Designation.id 2, 3 for Directors, 7 for Group Leaders, etc.
        # This will need an actual mapping for Designation IDs from tblHR_Designation.
        self.fields['director_name'].queryset = OfficeStaff.objects.filter(
            designation__id__in=[2, 3] # Assuming 2 & 3 are director designations
        )
        self.fields['director_name'].empty_label = "Select"

        self.fields['group_leader'].queryset = OfficeStaff.objects.filter(
            designation__id=7 # Assuming 7 is group leader designation
        )
        self.fields['group_leader'].empty_label = "Select"
        
        self.fields['dept_head'].queryset = OfficeStaff.objects.exclude(id=1) # Exclude user with ID 1
        self.fields['dept_head'].empty_label = "Select"

        # Set initial values for new staff from OfferMaster
        if offer_id and not self.instance.pk: # Only for new instances if offer_id is provided
            try:
                offer = OfferMaster.objects.get(offer_id=offer_id)
                self.fields['employee_name'].initial = offer.employee_name
                self.fields['title'].initial = offer.title
            except OfferMaster.DoesNotExist:
                pass # Offer not found, proceed without initial values

        # Set empty labels for all Foreign Key fields that are not initially required
        for field_name in ['designation', 'department', 'swap_card', 'corporate_mobile_no',
                           'business_group', 'director_name', 'dept_head', 'group_leader',
                           'grade', 'extension_no']:
            if self.fields[field_name].empty_label is None:
                self.fields[field_name].empty_label = "Select"

    def clean(self):
        cleaned_data = super().clean()
        
        # Replicate ASP.NET Email Validation
        company_email = cleaned_data.get('company_email')
        erp_email = cleaned_data.get('erp_email')
        personal_email = cleaned_data.get('personal_email')

        email_regex = r'^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$'
        if company_email and not RegexValidator(email_regex)(company_email):
            self.add_error('company_email', 'Enter a valid company email address.')
        if erp_email and not RegexValidator(email_regex)(erp_email):
            self.add_error('erp_email', 'Enter a valid ERP email address.')
        if personal_email and not RegexValidator(email_regex)(personal_email):
            self.add_error('personal_email', 'Enter a valid personal email address.')

        # Replicate ASP.NET Date Validation (format dd-MM-yyyy)
        # Django DateField handles basic date format validation, but specific regex
        # validation for 'dd-MM-yyyy' could be added if needed, though typically not for DateField.
        # Ensure dates are valid and not future if applicable (e.g. DOB)

        return cleaned_data

```

#### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views will be kept thin, with business logic primarily in the model's `save` method and form's `clean` method. HTMX will be used for all dynamic content loading and form submissions within modals. The multi-tab ASP.NET form is handled as one large form submission in Django, with client-side Alpine.js managing the visible tabs.

```python
# hr_staff/views.py

from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.contrib.auth.mixins import LoginRequiredMixin # Add for production usage

from .models import OfficeStaff, OfferMaster, CompanyMaster
from .forms import OfficeStaffForm

# Helper to get current company ID and session ID (replace with actual session/user logic)
# In a real app, comp_id and session_id would come from request.user profile or session.
def get_current_context_data(request):
    comp_id = request.session.get('compid', 1) # Default to 1, replace with actual
    fin_year_id = request.session.get('finyear', 1) # Default to 1, replace with actual
    session_id = request.session.get('username', 'system') # Default to 'system', replace with actual
    return {'comp_id': comp_id, 'fin_year_id': fin_year_id, 'session_id': session_id}


class OfficeStaffListView(LoginRequiredMixin, ListView):
    model = OfficeStaff
    template_name = 'hr_staff/officestaff/list.html'
    context_object_name = 'office_staff_list' # Renamed for clarity

    def get_queryset(self):
        # Filter by company ID if applicable, similar to ASP.NET where `CompId` is used
        # In a real ERP, this would depend on user's assigned company.
        comp_id = self.request.session.get('compid', 1)
        return OfficeStaff.objects.filter(comp_id=comp_id).order_by('employee_name')

class OfficeStaffTablePartialView(LoginRequiredMixin, ListView):
    model = OfficeStaff
    template_name = 'hr_staff/officestaff/_officestaff_table.html'
    context_object_name = 'office_staff_list'

    def get_queryset(self):
        comp_id = self.request.session.get('compid', 1)
        return OfficeStaff.objects.filter(comp_id=comp_id).order_by('employee_name')


class OfficeStaffCreateView(LoginRequiredMixin, CreateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr_staff/officestaff/form.html'
    success_url = reverse_lazy('officestaff_list') # Will be handled by HTMX response

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass comp_id and offer_id to the form for dynamic queryset filtering and initial data
        context_data = get_current_context_data(self.request)
        kwargs['comp_id'] = context_data['comp_id']
        kwargs['offer_id'] = self.request.GET.get('OfferId') # Get OfferId from query string
        return kwargs

    def form_valid(self, form):
        with transaction.atomic():
            # Set system fields before saving
            context_data = get_current_context_data(self.request)
            form.instance.comp_id = context_data['comp_id']
            form.instance.fin_year_id = context_data['fin_year_id']
            form.instance.session_id = context_data['session_id']
            form.instance.offer = OfferMaster.objects.get(offer_id=self.request.GET.get('OfferId')) if self.request.GET.get('OfferId') else None
            
            # If photo/cv files are present, assign them to model instance
            if self.request.FILES.get('photo'):
                form.instance.photo = self.request.FILES['photo']
            if self.request.FILES.get('cv'):
                form.instance.cv = self.request.FILES['cv']

            response = super().form_valid(form)
            messages.success(self.request, 'Office Staff added successfully.')
            
            if self.request.headers.get('HX-Request'):
                # For HTMX, send a 204 No Content and trigger client-side events
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshOfficeStaffList', # Custom event to refresh list
                        'HX-Redirect': reverse_lazy('officestaff_list') # Redirect after successful save if needed
                    }
                )
            return response

    def form_invalid(self, form):
        # For HTMX, return the form with errors
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # HTMX will swap this back into the modal
        return response


class OfficeStaffUpdateView(LoginRequiredMixin, UpdateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr_staff/officestaff/form.html'
    success_url = reverse_lazy('officestaff_list') # Will be handled by HTMX response

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        context_data = get_current_context_data(self.request)
        kwargs['comp_id'] = context_data['comp_id'] # Needed for dynamic lookups
        kwargs['offer_id'] = self.object.offer.offer_id if self.object.offer else None # Pass existing offer_id
        return kwargs

    def form_valid(self, form):
        with transaction.atomic():
            # Update system fields if necessary (e.g., last modified by)
            # form.instance.session_id = self.request.session.get('username') # Example update
            
            # Handle file uploads (photo and cv) for update
            if self.request.FILES.get('photo'):
                form.instance.photo = self.request.FILES['photo']
            if self.request.FILES.get('cv'):
                form.instance.cv = self.request.FILES['cv']

            response = super().form_valid(form)
            messages.success(self.request, 'Office Staff updated successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshOfficeStaffList',
                        'HX-Redirect': reverse_lazy('officestaff_list')
                    }
                )
            return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class OfficeStaffDeleteView(LoginRequiredMixin, DeleteView):
    model = OfficeStaff
    template_name = 'hr_staff/officestaff/confirm_delete.html'
    success_url = reverse_lazy('officestaff_list') # Will be handled by HTMX response

    def delete(self, request, *args, **kwargs):
        # Before deleting, consider if there are any related records in other tables
        # that might need to be updated (e.g., setting SwapCardNo/MobileNo to "Not Applicable" for resignation)
        # This logic should typically be in the model or a service layer.
        instance = self.get_object()
        # Example: If ResignationDate is set, clear related SwapCard/MobileNo as per ASP.NET comment
        # This would be complex to infer accurately from the ASP.NET code for DELETE,
        # but the ASP.NET comment suggests this happens on resignation date *input*, not deletion.
        # For a delete operation, it's typically a hard delete of the staff record.

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Office Staff deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOfficeStaffList'
                }
            )
        return response

```

#### 4.4 Templates

Task: Create templates for each view, incorporating HTMX and Alpine.js.

## Instructions:

The tabbed interface will be managed client-side using Alpine.js within a single form template. HTMX will handle modal loading and table refreshes.

```html
{# hr_staff/officestaff/list.html #}

{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Office Staff List</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'officestaff_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Staff
        </button>
    </div>
    
    <div id="officestaffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body"
         hx-get="{% url 'officestaff_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading staff data...</p>
        </div>
    </div>
    
    <!-- Modal for forms (add/edit/delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 opacity-0 pointer-events-none"
         _="on .is-active add .opacity-100 remove .pointer-events-none, on click if event.target.id == 'modal' remove .is-active from me transition-opacity duration-300 remove .opacity-100 then add .pointer-events-none">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform scale-95 transition-transform duration-300"
             _="on .is-active add .scale-100">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<!-- DataTables JS & CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<!-- Flatpickr for date inputs -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Initialize DataTables if the swapped content contains the table
        if ($(event.detail.target).is('#officestaffTable-container')) {
            $('#officestaffTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before re-initializing
            });
        }

        // Initialize Flatpickr for date inputs within the modal after it's loaded
        if ($(event.detail.target).is('#modalContent')) {
            flatpickr(".datepicker", {
                dateFormat: "d-m-Y", // Match ASP.NET format
                altInput: true,
                altFormat: "d-m-Y",
            });
        }
    });

    // Close modal on successful HTMX form submission
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) { // No Content, indicates successful action
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
                modal.classList.remove('opacity-100');
                modal.classList.add('pointer-events-none');
            }
        }
    });

    // Reinitialize Alpine.js on HTMX swaps for new content
    document.body.addEventListener('htmx:afterSwap', function (event) {
        if (event.detail.target.closest('[x-data]')) {
            Alpine.initTree(event.detail.target);
        }
    });
</script>
{% endblock %}

```

```html
{# hr_staff/officestaff/_officestaff_table.html #}

<div class="overflow-x-auto">
    <table id="officestaffTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th class="py-3 px-4 border-b border-gray-200">SN</th>
                <th class="py-3 px-4 border-b border-gray-200">Emp ID</th>
                <th class="py-3 px-4 border-b border-gray-200">Employee Name</th>
                <th class="py-3 px-4 border-b border-gray-200">Designation</th>
                <th class="py-3 px-4 border-b border-gray-200">Department</th>
                <th class="py-3 px-4 border-b border-gray-200">Joining Date</th>
                <th class="py-3 px-4 border-b border-gray-200">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for staff in office_staff_list %}
            <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-4 text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.emp_id }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.title }} {{ staff.employee_name }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.designation.type|default:'N/A' }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.department.description|default:'N/A' }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.joining_date|date:"d-m-Y"|default:'N/A' }}</td>
                <td class="py-3 px-4 text-sm text-gray-700 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'officestaff_edit' staff.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'officestaff_delete' staff.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center text-gray-500">No staff records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization will be handled by the parent list.html via htmx:afterSwap #}

```

```html
{# hr_staff/officestaff/form.html #}

<div class="p-6" x-data="{ activeTab: 1 }"> {# Alpine.js for tab management #}
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Staff Details</h3>
    <form hx-post="{{ request.path }}" hx-encoding="multipart/form-data"> {# multipart for file uploads #}
        {% csrf_token %}
        
        <div class="mb-6 border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button type="button" @click="activeTab = 1" :class="{'border-blue-500 text-blue-600': activeTab === 1, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 1}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Official Info
                </button>
                <button type="button" @click="activeTab = 2" :class="{'border-blue-500 text-blue-600': activeTab === 2, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 2}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Personal Info
                </button>
                <button type="button" @click="activeTab = 3" :class="{'border-blue-500 text-blue-600': activeTab === 3, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 3}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Edu. Quali. & Work Experience
                </button>
                <button type="button" @click="activeTab = 4" :class="{'border-blue-500 text-blue-600': activeTab === 4, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 4}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Others
                </button>
            </nav>
        </div>

        <div x-show="activeTab === 1" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Official Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name of Employee</label>
                    <div class="mt-1 flex items-center space-x-2">
                        {{ form.title }}
                        {{ form.employee_name }}
                    </div>
                    {% if form.title.errors %}<p class="text-red-500 text-xs mt-1">{{ form.title.errors }}</p>{% endif %}
                    {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">Designation</label>
                    <div class="mt-1">{{ form.designation }}</div>
                    {% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">Department</label>
                    <div class="mt-1">{{ form.department }}</div>
                    {% if form.department.errors %}<p class="text-red-500 text-xs mt-1">{{ form.department.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.swap_card.id_for_label }}" class="block text-sm font-medium text-gray-700">Swap Card No</label>
                    <div class="mt-1">{{ form.swap_card }}</div>
                    {% if form.swap_card.errors %}<p class="text-red-500 text-xs mt-1">{{ form.swap_card.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.director_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Director of Dept.</label>
                    <div class="mt-1">{{ form.director_name }}</div>
                    {% if form.director_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.director_name.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">Under BG</label>
                    <div class="mt-1">{{ form.business_group }}</div>
                    {% if form.business_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.business_group.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.group_leader.id_for_label }}" class="block text-sm font-medium text-gray-700">Group Leader</label>
                    <div class="mt-1">{{ form.group_leader }}</div>
                    {% if form.group_leader.errors %}<p class="text-red-500 text-xs mt-1">{{ form.group_leader.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.dept_head.id_for_label }}" class="block text-sm font-medium text-gray-700">Dept. Head</label>
                    <div class="mt-1">{{ form.dept_head }}</div>
                    {% if form.dept_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.dept_head.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.corporate_mobile_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Corp. Mobile No.</label>
                    <div class="mt-1">{{ form.corporate_mobile_no }}</div>
                    {% if form.corporate_mobile_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.corporate_mobile_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.grade.id_for_label }}" class="block text-sm font-medium text-gray-700">Grade</label>
                    <div class="mt-1">{{ form.grade }}</div>
                    {% if form.grade.errors %}<p class="text-red-500 text-xs mt-1">{{ form.grade.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No.</label>
                    <div class="mt-1">{{ form.contact_no }}</div>
                    {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.company_email.id_for_label }}" class="block text-sm font-medium text-gray-700">Company E-Mail</label>
                    <div class="mt-1">{{ form.company_email }}</div>
                    {% if form.company_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.company_email.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.erp_email.id_for_label }}" class="block text-sm font-medium text-gray-700">ERP-Mail</label>
                    <div class="mt-1">{{ form.erp_email }}</div>
                    {% if form.erp_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.erp_email.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.extension_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Extension No.</label>
                    <div class="mt-1">{{ form.extension_no }}</div>
                    {% if form.extension_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.extension_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.joining_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Joining Date</label>
                    <div class="mt-1">{{ form.joining_date }}</div>
                    {% if form.joining_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.joining_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.resignation_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Resignation Date</label>
                    <div class="mt-1">{{ form.resignation_date }}</div>
                    {% if form.resignation_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.resignation_date.errors }}</p>{% endif %}
                    <p class="text-red-500 text-xs mt-1">* Reset Swap Card No and Corp. Mobile No. to Not Applicable.</p>
                </div>
            </div>
            <div class="mt-6 flex justify-end">
                <button type="button" @click="activeTab = 2" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Next
                </button>
            </div>
        </div>

        <div x-show="activeTab === 2" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Personal Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div class="col-span-2">
                    <label for="{{ form.permanent_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Permanent Address</label>
                    <div class="mt-1">{{ form.permanent_address }}</div>
                    {% if form.permanent_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.permanent_address.errors }}</p>{% endif %}
                </div>
                <div class="col-span-2">
                    <label for="{{ form.correspondence_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Correspondence Address</label>
                    <div class="mt-1">{{ form.correspondence_address }}</div>
                    {% if form.correspondence_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.correspondence_address.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.personal_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-Mail</label>
                    <div class="mt-1">{{ form.personal_email }}</div>
                    {% if form.personal_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.personal_email.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700">Date Of Birth</label>
                    <div class="mt-1">{{ form.date_of_birth }}</div>
                    {% if form.date_of_birth.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_of_birth.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.gender.id_for_label }}" class="block text-sm font-medium text-gray-700">Gender</label>
                    <div class="mt-1">{{ form.gender }}</div>
                    {% if form.gender.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gender.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.martial_status.id_for_label }}" class="block text-sm font-medium text-gray-700">Martial Status</label>
                    <div class="mt-1">{{ form.martial_status }}</div>
                    {% if form.martial_status.errors %}<p class="text-red-500 text-xs mt-1">{{ form.martial_status.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.blood_group.id_for_label }}" class="block text-sm font-medium text-gray-700">Blood Group</label>
                    <div class="mt-1">{{ form.blood_group }}</div>
                    {% if form.blood_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.blood_group.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.height.id_for_label }}" class="block text-sm font-medium text-gray-700">Height</label>
                    <div class="mt-1">{{ form.height }}</div>
                    {% if form.height.errors %}<p class="text-red-500 text-xs mt-1">{{ form.height.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.weight.id_for_label }}" class="block text-sm font-medium text-gray-700">Weight</label>
                    <div class="mt-1">{{ form.weight }}</div>
                    {% if form.weight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.weight.errors }}</p>{% endif %}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Physically Handicapped</label>
                    <div class="mt-1">{{ form.physically_handycapped }}</div>
                    {% if form.physically_handycapped.errors %}<p class="text-red-500 text-xs mt-1">{{ form.physically_handycapped.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.religion.id_for_label }}" class="block text-sm font-medium text-gray-700">Religion</label>
                    <div class="mt-1">{{ form.religion }}</div>
                    {% if form.religion.errors %}<p class="text-red-500 text-xs mt-1">{{ form.religion.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.cast.id_for_label }}" class="block text-sm font-medium text-gray-700">Cast</label>
                    <div class="mt-1">{{ form.cast }}</div>
                    {% if form.cast.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cast.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6 flex justify-between">
                <button type="button" @click="activeTab = 1" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Previous
                </button>
                <button type="button" @click="activeTab = 3" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Next
                </button>
            </div>
        </div>

        <div x-show="activeTab === 3" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Educational Qualification & Work Experience</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.educational_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">Educational Qualification</label>
                    <div class="mt-1">{{ form.educational_qualification }}</div>
                    {% if form.educational_qualification.errors %}<p class="text-red-500 text-xs mt-1">{{ form.educational_qualification.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.additional_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">Additional Qualification</label>
                    <div class="mt-1">{{ form.additional_qualification }}</div>
                    {% if form.additional_qualification.errors %}<p class="text-red-500 text-xs mt-1">{{ form.additional_qualification.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.last_company_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Last Company Name</label>
                    <div class="mt-1">{{ form.last_company_name }}</div>
                    {% if form.last_company_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.last_company_name.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.total_experience.id_for_label }}" class="block text-sm font-medium text-gray-700">Total Experience</label>
                    <div class="mt-1">{{ form.total_experience }}</div>
                    {% if form.total_experience.errors %}<p class="text-red-500 text-xs mt-1">{{ form.total_experience.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.working_duration.id_for_label }}" class="block text-sm font-medium text-gray-700">Working Duration</label>
                    <div class="mt-1">{{ form.working_duration }}</div>
                    {% if form.working_duration.errors %}<p class="text-red-500 text-xs mt-1">{{ form.working_duration.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6 flex justify-between">
                <button type="button" @click="activeTab = 2" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Previous
                </button>
                <button type="button" @click="activeTab = 4" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Next
                </button>
            </div>
        </div>

        <div x-show="activeTab === 4" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Others</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.current_ctc.id_for_label }}" class="block text-sm font-medium text-gray-700">Current CTC</label>
                    <div class="mt-1">{{ form.current_ctc }}</div>
                    {% if form.current_ctc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.current_ctc.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.bank_account_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Acc No.</label>
                    <div class="mt-1">{{ form.bank_account_no }}</div>
                    {% if form.bank_account_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_account_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.pf_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PF No.</label>
                    <div class="mt-1">{{ form.pf_no }}</div>
                    {% if form.pf_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PAN No.</label>
                    <div class="mt-1">{{ form.pan_no }}</div>
                    {% if form.pan_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pan_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.passport_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Passport No.</label>
                    <div class="mt-1">{{ form.passport_no }}</div>
                    {% if form.passport_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.passport_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.passport_expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Expiry Date</label>
                    <div class="mt-1">{{ form.passport_expiry_date }}</div>
                    {% if form.passport_expiry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.passport_expiry_date.errors }}</p>{% endif %}
                </div>
                <div class="md:col-span-2">
                    <label for="{{ form.additional_information.id_for_label }}" class="block text-sm font-medium text-gray-700">Additional Information</label>
                    <div class="mt-1">{{ form.additional_information }}</div>
                    {% if form.additional_information.errors %}<p class="text-red-500 text-xs mt-1">{{ form.additional_information.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.photo.id_for_label }}" class="block text-sm font-medium text-gray-700">Upload Photo</label>
                    <div class="mt-1">{{ form.photo }}</div>
                    {% if form.photo.errors %}<p class="text-red-500 text-xs mt-1">{{ form.photo.errors }}</p>{% endif %}
                    {% if form.instance.photo %}
                        <p class="text-xs text-gray-500 mt-1">Current: <a href="{{ form.instance.photo.url }}" target="_blank">View Photo</a></p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.cv.id_for_label }}" class="block text-sm font-medium text-gray-700">Upload CV</label>
                    <div class="mt-1">{{ form.cv }}</div>
                    {% if form.cv.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cv.errors }}</p>{% endif %}
                    {% if form.instance.cv %}
                        <p class="text-xs text-gray-500 mt-1">Current: <a href="{{ form.instance.cv.url }}" target="_blank">View CV</a></p>
                    {% endif %}
                </div>
            </div>
            
            <div class="mt-6 flex justify-between">
                <button type="button" @click="activeTab = 3" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Previous
                </button>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                        onclick="return confirm('Are you sure you want to submit?')"> {# Mimic ASP.NET confirmation #}
                    Submit
                </button>
            </div>
        </div>

        {# Display non-field errors #}
        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mt-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}

    </form>
</div>
```

```html
{# hr_staff/officestaff/confirm_delete.html #}

<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the staff record for <strong>{{ object.employee_name }} ({{ object.emp_id }})</strong>?</p>
    
    <form hx-post="{% url 'officestaff_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URLs will follow Django's conventional patterns for RESTful-like access to resources.

```python
# hr_staff/urls.py

from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffCreateView,
    OfficeStaffUpdateView, OfficeStaffDeleteView,
    OfficeStaffTablePartialView
)

urlpatterns = [
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'), # HTMX partial
    path('staff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('staff/edit/<int:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    path('staff/delete/<int:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'),
]

```

#### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `OfficeStaff` model will verify custom logic like `EmpId` generation. Integration tests for views will ensure proper rendering, form submission, and HTMX interaction.

```python
# hr_staff/tests.py

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock
from .models import (
    OfficeStaff, Designation, Department, SwapCard, CorporateMobileNo,
    BusinessGroup, Grade, IntercomExtension, OfferMaster, CompanyMaster
)
from .forms import OfficeStaffForm
from django.core.files.uploadedfile import SimpleUploadedFile
import os

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup necessary lookup data
        Designation.objects.create(id=1, type='Default', symbol='DEF')
        Designation.objects.create(id=2, type='Director', symbol='DIR')
        Designation.objects.create(id=3, type='Director 2', symbol='DIR2')
        Designation.objects.create(id=7, type='Group Leader', symbol='GL')
        Department.objects.create(id=1, description='HR', symbol='HRD')
        SwapCard.objects.create(id=1, swap_card_no='N/A') # Required by query logic
        SwapCard.objects.create(id=101, swap_card_no='SC001')
        CorporateMobileNo.objects.create(id=1, mobile_no='N/A') # Required by query logic
        CorporateMobileNo.objects.create(id=201, mobile_no='MOB001')
        BusinessGroup.objects.create(id=1, name='Main Group', symbol='MG')
        Grade.objects.create(id=1, description='Entry Level', symbol='EL')
        IntercomExtension.objects.create(id=1, ext_no='1000')
        OfferMaster.objects.create(offer_id=1, title='Mr', employee_name='John Doe')
        CompanyMaster.objects.create(comp_id=1, prefix='XYZ')

        # Create a sample staff for self-referential Fk testing
        OfficeStaff.objects.create(
            id=1, # Important for self-referencing (UserId!=1 logic)
            employee_name='Admin User',
            comp_id=1,
            designation_id=1,
            title='Mr'
        )

        OfficeStaff.objects.create(
            id=10,
            employee_name='Test Director',
            comp_id=1,
            designation_id=2,
            title='Mr'
        )
        OfficeStaff.objects.create(
            id=11,
            employee_name='Test Group Leader',
            comp_id=1,
            designation_id=7,
            title='Mr'
        )


    def test_office_staff_creation(self):
        staff = OfficeStaff.objects.create(
            employee_name='Jane Doe',
            title='Miss',
            designation_id=1,
            department_id=1,
            joining_date='2023-01-15',
            comp_id=1,
            fin_year_id=1,
            session_id='testuser'
        )
        self.assertEqual(staff.employee_name, 'Jane Doe')
        self.assertEqual(staff.title, 'Miss')
        self.assertIsNotNone(staff.pk)
        self.assertIsNotNone(staff.sys_date)
        self.assertIsNotNone(staff.sys_time)
        self.assertEqual(staff.comp_id, 1)

    def test_emp_id_generation(self):
        # Ensure ID generation picks up from last created if existing
        OfficeStaff.objects.create(
            employee_name='Existing User',
            title='Mr',
            comp_id=1,
            emp_id='XYZ0005', # Manually set an existing one
            designation_id=1,
            department_id=1,
            joining_date='2023-01-01'
        )
        new_staff = OfficeStaff.objects.create(
            employee_name='New User',
            title='Mr',
            comp_id=1,
            designation_id=1,
            department_id=1,
            joining_date='2023-01-02'
        )
        self.assertEqual(new_staff.emp_id, 'XYZ0006')

        # Test generation for the first record in a company
        CompanyMaster.objects.create(comp_id=2, prefix='ABC')
        first_staff_new_comp = OfficeStaff.objects.create(
            employee_name='First New User',
            title='Mr',
            comp_id=2,
            designation_id=1,
            department_id=1,
            joining_date='2023-01-03'
        )
        self.assertEqual(first_staff_new_comp.emp_id, 'ABC0001')

    def test_file_upload_attributes_on_save(self):
        # Create dummy file
        mock_photo_content = b'dummy_photo_data'
        mock_cv_content = b'dummy_cv_data'
        photo_file = SimpleUploadedFile("test_photo.jpg", mock_photo_content, content_type="image/jpeg")
        cv_file = SimpleUploadedFile("test_cv.pdf", mock_cv_content, content_type="application/pdf")

        staff = OfficeStaff.objects.create(
            employee_name='File User',
            title='Mr',
            designation_id=1,
            department_id=1,
            joining_date='2023-01-15',
            comp_id=1,
            fin_year_id=1,
            session_id='fileuser',
            photo=photo_file,
            cv=cv_file
        )

        self.assertEqual(staff.photo_file_name, "test_photo.jpg")
        self.assertEqual(staff.photo_size, len(mock_photo_content))
        self.assertEqual(staff.photo_content_type, "image/jpeg")
        self.assertTrue(os.path.exists(staff.photo.path)) # Ensure file is saved

        self.assertEqual(staff.cv_file_name, "test_cv.pdf")
        self.assertEqual(staff.cv_size, len(mock_cv_content))
        self.assertEqual(staff.cv_content_type, "application/pdf")
        self.assertTrue(os.path.exists(staff.cv.path)) # Ensure file is saved

        # Clean up files after test
        staff.photo.delete()
        staff.cv.delete()


class OfficeStaffViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Setup necessary lookup data for views
        Designation.objects.create(id=1, type='Default', symbol='DEF')
        Designation.objects.create(id=2, type='Director', symbol='DIR')
        Designation.objects.create(id=3, type='Director 2', symbol='DIR2')
        Designation.objects.create(id=7, type='Group Leader', symbol='GL')
        Department.objects.create(id=1, description='HR', symbol='HRD')
        SwapCard.objects.create(id=1, swap_card_no='Not Applicable')
        SwapCard.objects.create(id=101, swap_card_no='SC001')
        CorporateMobileNo.objects.create(id=1, mobile_no='Not Applicable')
        CorporateMobileNo.objects.create(id=201, mobile_no='MOB001')
        BusinessGroup.objects.create(id=1, name='Main Group', symbol='MG')
        Grade.objects.create(id=1, description='Entry Level', symbol='EL')
        IntercomExtension.objects.create(id=1, ext_no='1000')
        OfferMaster.objects.create(offer_id=1, title='Mr', employee_name='John Doe')
        CompanyMaster.objects.create(comp_id=1, prefix='XYZ')

        # Create a sample staff for list/edit/delete tests
        self.staff1 = OfficeStaff.objects.create(
            employee_name='Jane Doe',
            title='Miss',
            designation_id=1,
            department_id=1,
            joining_date='2023-01-15',
            comp_id=1,
            fin_year_id=1,
            session_id='testuser',
            emp_id='XYZ0001' # Ensure emp_id is set for existing objects
        )
        self.staff2 = OfficeStaff.objects.create(
            employee_name='Mike Smith',
            title='Mr',
            designation_id=1,
            department_id=1,
            joining_date='2023-02-20',
            comp_id=1,
            fin_year_id=1,
            session_id='testuser',
            emp_id='XYZ0002'
        )

        # Mock user authentication for LoginRequiredMixin
        self.client.force_login(MagicMock(is_authenticated=True))
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 1
        self.client.session['username'] = 'testuser'

    def test_list_view(self):
        response = self.client.get(reverse('officestaff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/list.html')
        self.assertContains(response, 'Office Staff List')

    def test_table_partial_view(self):
        response = self.client.get(reverse('officestaff_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/_officestaff_table.html')
        self.assertContains(response, self.staff1.employee_name)
        self.assertContains(response, self.staff2.employee_name)
        self.assertContains(response, 'id="officestaffTable"')

    def test_create_view_get(self):
        response = self.client.get(reverse('officestaff_add'), HTTP_HX_REQUEST='true', data={'OfferId': 1})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/form.html')
        self.assertContains(response, 'Add Staff Details')
        self.assertContains(response, 'John Doe') # Initial value from OfferMaster
        self.assertContains(response, '<select name="title">')

    def test_create_view_post_success(self):
        # Create dummy file for upload
        photo_file = SimpleUploadedFile("test_photo.jpg", b"photo_content", content_type="image/jpeg")
        cv_file = SimpleUploadedFile("test_cv.pdf", b"cv_content", content_type="application/pdf")

        data = {
            'title': 'Mr',
            'employee_name': 'New Staff Member',
            'designation': 1,
            'department': 1,
            'swap_card': 1, # 'Not Applicable'
            'corporate_mobile_no': 1, # 'Not Applicable'
            'business_group': 1,
            'grade': 1,
            'extension_no': 1,
            'contact_no': '1234567890',
            'company_email': '<EMAIL>',
            'erp_email': '<EMAIL>',
            'joining_date': '01-03-2024',
            'permanent_address': '123 Main St',
            'correspondence_address': '123 Main St',
            'personal_email': '<EMAIL>',
            'date_of_birth': '01-01-1990',
            'gender': 'M',
            'martial_status': False, # Unmarried
            'blood_group': 'O+',
            'height': '170cm',
            'weight': '70kg',
            'physically_handycapped': False, # No
            'religion': 'Christian',
            'cast': 'General',
            'educational_qualification': 'B.Tech',
            'additional_qualification': 'MBA',
            'last_company_name': 'Old Company',
            'working_duration': '2 years',
            'total_experience': '5 years',
            'current_ctc': '500000',
            'bank_account_no': '12345',
            'pf_no': 'PF123',
            'pan_no': 'PAN123',
            'passport_no': 'PASS123',
            'passport_expiry_date': '01-01-2030',
            'additional_information': 'Some extra info',
            'photo': photo_file,
            'cv': cv_file,
        }
        
        response = self.client.post(reverse('officestaff_add'), data, HTTP_HX_REQUEST='true', data={'OfferId': 1})
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(OfficeStaff.objects.filter(employee_name='New Staff Member').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshOfficeStaffList', response.headers['HX-Trigger'])

        # Verify file presence (cleanup is needed if files are actually written to disk)
        new_staff = OfficeStaff.objects.get(employee_name='New Staff Member')
        self.assertIsNotNone(new_staff.photo)
        self.assertIsNotNone(new_staff.cv)
        new_staff.photo.delete(save=False)
        new_staff.cv.delete(save=False)


    def test_create_view_post_invalid(self):
        data = {
            'employee_name': '', # Missing required field
            'joining_date': 'invalid-date', # Invalid date format
            'company_email': 'invalid-email', # Invalid email
            'title': 'Mr' # Required choice field
        }
        response = self.client.post(reverse('officestaff_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid company email address.')
        self.assertContains(response, 'Enter a valid date.')
        self.assertTemplateUsed(response, 'hr_staff/officestaff/form.html')


    def test_update_view_get(self):
        response = self.client.get(reverse('officestaff_edit', args=[self.staff1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/form.html')
        self.assertContains(response, 'Edit Staff Details')
        self.assertContains(response, self.staff1.employee_name)

    def test_update_view_post_success(self):
        data = {
            'title': self.staff1.title,
            'employee_name': 'Jane Doe Updated',
            'designation': self.staff1.designation.pk,
            'department': self.staff1.department.pk,
            'swap_card': self.staff1.swap_card.pk if self.staff1.swap_card else 1,
            'corporate_mobile_no': self.staff1.corporate_mobile_no.pk if self.staff1.corporate_mobile_no else 1,
            'business_group': self.staff1.business_group.pk if self.staff1.business_group else 1,
            'grade': self.staff1.grade.pk if self.staff1.grade else 1,
            'extension_no': self.staff1.extension_no.pk if self.staff1.extension_no else 1,
            'contact_no': '0987654321',
            'company_email': '<EMAIL>',
            'erp_email': '<EMAIL>',
            'joining_date': self.staff1.joining_date.strftime('%d-%m-%Y'),
            'permanent_address': self.staff1.permanent_address or 'Updated Address',
            'correspondence_address': self.staff1.correspondence_address or 'Updated Address',
            'personal_email': self.staff1.personal_email or '<EMAIL>',
            'date_of_birth': self.staff1.date_of_birth.strftime('%d-%m-%Y') if self.staff1.date_of_birth else '01-01-1990',
            'gender': self.staff1.gender or 'F',
            'martial_status': str(self.staff1.martial_status) if self.staff1.martial_status is not None else False,
            'blood_group': self.staff1.blood_group or 'A+',
            'height': self.staff1.height or '160cm',
            'weight': self.staff1.weight or '60kg',
            'physically_handycapped': str(self.staff1.physically_handycapped) if self.staff1.physically_handycapped is not None else False,
            'religion': self.staff1.religion or 'Hindu',
            'cast': self.staff1.cast or 'OBC',
            'educational_qualification': self.staff1.educational_qualification or 'M.Sc',
            'additional_qualification': self.staff1.additional_qualification or 'PMP',
            'last_company_name': self.staff1.last_company_name or 'New Company',
            'working_duration': self.staff1.working_duration or '3 years',
            'total_experience': self.staff1.total_experience or '7 years',
            'current_ctc': self.staff1.current_ctc or '600000',
            'bank_account_no': self.staff1.bank_account_no or '67890',
            'pf_no': self.staff1.pf_no or 'PF456',
            'pan_no': self.staff1.pan_no or 'PAN456',
            'passport_no': self.staff1.passport_no or 'PASS456',
            'passport_expiry_date': self.staff1.passport_expiry_date.strftime('%d-%m-%Y') if self.staff1.passport_expiry_date else '01-01-2035',
            'additional_information': self.staff1.additional_information or 'Updated info',
        }
        response = self.client.post(reverse('officestaff_edit', args=[self.staff1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.staff1.refresh_from_db()
        self.assertEqual(self.staff1.employee_name, 'Jane Doe Updated')
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        response = self.client.get(reverse('officestaff_delete', args=[self.staff1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/confirm_delete.html')
        self.assertContains(response, f'delete the staff record for {self.staff1.employee_name}')

    def test_delete_view_post_success(self):
        response = self.client.post(reverse('officestaff_delete', args=[self.staff1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(OfficeStaff.objects.filter(pk=self.staff1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshOfficeStaffList', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided Django templates (`list.html`, `_officestaff_table.html`, `form.html`, `confirm_delete.html`) already integrate HTMX for dynamic content loading, form submissions, and modal management. Alpine.js is used within `form.html` to handle the tab switching logic.

*   **HTMX Usage**:
    *   `officestaffTable-container` in `list.html` uses `hx-get` to load `_officestaff_table.html` and `hx-trigger="load, refreshOfficeStaffList from:body"` for initial load and post-CRUD updates.
    *   "Add", "Edit", "Delete" buttons in `list.html` and `_officestaff_table.html` use `hx-get` to load the respective forms (`form.html` or `confirm_delete.html`) into the `#modalContent` div.
    *   Form submissions (`form.html`, `confirm_delete.html`) use `hx-post` and `hx-swap="none"` to prevent modal closure on errors, relying on the 204 status and `HX-Trigger` for success to close the modal and refresh the list.
*   **Alpine.js Usage**:
    *   The `form.html` template uses `x-data="{ activeTab: 1 }"` to manage the active tab state.
    *   Tab buttons use `@click="activeTab = N"` to change the state.
    *   Tab content divs use `x-show="activeTab === N"` to conditionally display content.
    *   Modal hide/show logic in `list.html` is handled by Alpine.js and inline `_` attributes (hyperscript).
*   **DataTables**: The `_officestaff_table.html` includes the basic HTML structure for a DataTables table. The actual DataTables initialization (`$('#officestaffTable').DataTable()`) is placed within the `htmx:afterSwap` event listener in `list.html` to ensure it runs *after* the table content is loaded into the DOM by HTMX.
*   **Date Pickers**: `flatpickr` is integrated to provide date picker functionality to inputs with the `datepicker` class, initialized on `htmx:afterSwap` for content loaded into the modal.

This setup ensures a highly interactive and responsive user experience without traditional full-page reloads, mirroring the dynamic behavior of the original ASP.NET Web Forms application while leveraging modern web technologies.

## Final Notes

*   **Placeholders**: `comp_id`, `fin_year_id`, `session_id`, and `OfferId` currently have hardcoded defaults or are taken directly from `request.session` / `request.GET`. In a production environment, these would be derived from a proper authentication/authorization system (e.g., Django's `request.user` object and a user profile linked to companies/financial years).
*   **File Storage**: The migration assumes changing the `PhotoData` and `CVData` columns from binary data to file paths. This might require a one-time data migration script to extract existing binary data from the old database and save them as files on the new Django server's media storage, then update the database columns with the new file paths.
*   **User Roles/Permissions**: The original ASP.NET code likely had role-based access. This Django plan uses `LoginRequiredMixin` as a basic security measure. A full modernization would involve implementing a robust Django permission system.
*   **Error Handling/Messaging**: Django's messages framework is used for success messages. More granular error handling, including display of validation errors, is built into the form rendering.
*   **Scalability**: The "Fat Model, Thin View" approach promotes better maintainability and scalability by centralizing business logic. HTMX and Alpine.js reduce server load by minimizing full-page reloads and simplifying frontend development.
*   **Database Migrations**: After setting up `managed = False` models, you should use `python manage.py inspectdb` to verify field mappings and then `python manage.py makemigrations --empty <appname>` and manually create an empty migration to import the initial schema using `RunSQL` if you are truly working with an existing legacy database without `managed=False`. However, `managed=False` is typically preferred for direct connection to existing databases.
*   **Dependencies**: Ensure `django-crispy-forms` (or similar if using helper layout), `django-htmx`, `django-alpinejs` (if used directly), `flatpickr` and `DataTables` (via CDN or npm) are installed and configured in your Django project.