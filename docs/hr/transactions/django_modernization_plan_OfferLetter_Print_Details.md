## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for HR Offer/Increment Letter Printing

This document outlines a modernization plan for transitioning the `OfferLetter_Print_Details.aspx` ASP.NET application to a modern Django-based solution. The focus is on leveraging AI-assisted automation for complex logic translation, adhering to Django 5.0+ best practices, and ensuring a clean, maintainable architecture with a strong emphasis on business value.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (`core/base.html`).
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (for list views).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Business Value Proposition for Django Modernization:

Migrating this critical HR document generation system from ASP.NET to Django offers significant business benefits:

1.  **Reduced Operational Risk:** Moving away from legacy Crystal Reports and ASP.NET Web Forms mitigates the risk associated with outdated technologies, lack of support, and increasing maintenance costs.
2.  **Enhanced Maintainability & Scalability:** Django's structured, Python-based architecture and clear separation of concerns (models for business logic, views for coordination) make the application easier to understand, maintain, and extend. This allows your team to adapt faster to new business requirements.
3.  **Improved Agility & Development Speed:** Python and Django's robust ecosystem, coupled with modern frontend approaches like HTMX and Alpine.js, enable faster development cycles for future enhancements, reducing time-to-market for new HR features.
4.  **Cost Efficiency:** Python is an open-source language, and Django is a free framework, eliminating licensing costs associated with proprietary technologies. Cloud-native deployment options are also more accessible and cost-effective.
5.  **Modern User Experience:** While the original page primarily focused on printing, a Django re-implementation can easily incorporate interactive elements, responsive design, and a smoother user flow, enhancing the overall user experience for HR personnel. This includes using DataTables for easy navigation of reports and HTMX for dynamic interactions without full page reloads.
6.  **Automation Readiness:** The complex calculation logic currently embedded in C# functions is a prime candidate for AI-assisted translation to Python. This dramatically reduces manual coding effort, accelerates the migration, and minimizes human error in recreating intricate business rules.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns based on the ASP.NET code's SQL queries.

**Instructions:**
The ASP.NET code interacts with several HR-related tables. We will define Django models for each of these. Data types are inferred from usage (e.g., `Convert.ToInt32`, `Convert.ToDouble`, `ToString()`) and typical HR system requirements.

**Identified Tables and Inferred Columns:**

*   **`tblHR_Offer_Master`**:
    *   `OfferId` (PK, int)
    *   `SysDate` (DateTime)
    *   `CompId` (int, FK to Company)
    *   `Title` (string)
    *   `EmployeeName` (string)
    *   `StaffType` (int, FK to StaffType config)
    *   `TypeOf` (int, FK to Type config)
    *   `salary` (double)
    *   `DutyHrs` (string)
    *   `OTHrs` (string)
    *   `OverTime` (string)
    *   `ContactNo` (string)
    *   `EmailId` (string)
    *   `InterviewedBy` (int, FK to tblHR_OfficeStaff.EmpId)
    *   `AuthorizedBy` (int, FK to tblHR_OfficeStaff.EmpId)
    *   `ReferenceBy` (string)
    *   `Designation` (int, FK to tblHR_Designation.Id)
    *   `ExGratia` (double)
    *   `VehicleAllowance` (double)
    *   `LTA` (double)
    *   `Loyalty` (double)
    *   `PaidLeaves` (double)
    *   `Remarks` (string)
    *   `HeaderText` (string)
    *   `FooterText` (string)
    *   `Bonus` (double)
    *   `AttBonusPer1` (double)
    *   `AttBonusPer2` (double)
    *   `PFEmployee` (double)
    *   `PFCompany` (double)
    *   `Increment` (int, acts as a flag/counter)
    *   `IncrementForTheYear` (string)
    *   `EffectFrom` (DateTime)

*   **`tblHR_Increment_Master`**:
    *   `Id` (PK, int)
    *   `OfferId` (int, FK to tblHR_Offer_Master.OfferId)
    *   `SysDate` (DateTime)
    *   `CompId` (int, FK to Company)
    *   `Title` (string)
    *   `EmployeeName` (string)
    *   `StaffType` (int, FK to StaffType config)
    *   `TypeOf` (int, FK to Type config)
    *   `salary` (double)
    *   `DutyHrs` (string)
    *   `OTHrs` (string)
    *   `OverTime` (string)
    *   `ContactNo` (string)
    *   `EmailId` (string)
    *   `InterviewedBy` (int, FK to tblHR_OfficeStaff.EmpId)
    *   `AuthorizedBy` (int, FK to tblHR_OfficeStaff.EmpId)
    *   `ReferenceBy` (string)
    *   `Designation` (int, FK to tblHR_Designation.Id)
    *   `ExGratia` (double)
    *   `VehicleAllowance` (double)
    *   `LTA` (double)
    *   `Loyalty` (double)
    *   `PaidLeaves` (double)
    *   `Remarks` (string)
    *   `HeaderText` (string)
    *   `FooterText` (string)
    *   `Bonus` (double)
    *   `AttBonusPer1` (double)
    *   `AttBonusPer2` (double)
    *   `PFEmployee` (double)
    *   `PFCompany` (double)
    *   `Increment` (int, increment counter)
    *   `IncrementForTheYear` (string)
    *   `EffectFrom` (DateTime)

*   **`tblHR_Offer_Accessories`**:
    *   `Id` (PK, int)
    *   `MId` (int, FK to tblHR_Offer_Master.OfferId)
    *   `Perticulars` (string)
    *   `Qty` (double)
    *   `Amount` (double)
    *   `IncludesIn` (string, e.g., "1" for CTC, "2" for TakeHome, "3" for Both)

*   **`tblHR_Increment_Accessories`**:
    *   `Id` (PK, int)
    *   `MId` (int, FK to tblHR_Increment_Master.Id)
    *   `OfferMId` (int, FK to tblHR_Offer_Master.OfferId, used for filtering)
    *   `Perticulars` (string)
    *   `Qty` (double)
    *   `Amount` (double)
    *   `IncludesIn` (string, e.g., "1" for CTC, "2" for TakeHome, "3" for Both)

*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (PK, int)
    *   `CompId` (int, FK to Company)
    *   `Title` (string)
    *   `EmployeeName` (string)

*   **`tblHR_Designation`**:
    *   `Id` (PK, int)
    *   `Type` (string)

*   **`tblFinancial_master`**:
    *   `FinYearId` (PK, int)
    *   `FinYear` (string)

*   **`tblHR_Grade`**:
    *   `Id` (PK, int)
    *   `Symbol` (string)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
This page's primary function is **report generation and printing**, not standard CRUD for master data.

*   **Read/Retrieve:** Fetches detailed HR offer/increment data based on `OfferId` and an `Increment` flag.
*   **Complex Calculations:** Applies extensive business rules to calculate various salary components (Basic, DA, HRA, Conveyance, Education, MedicalAllowance, Attendance Bonus, PF, Gratuity, Take Home, CTC) for both the current offer/increment and, in the case of an increment letter, the previous offer/increment. This logic is encapsulated in `fun.Offer_Cal`, `fun.Pf_Cal`, `fun.PTax_Cal`, `fun.Gratuity_Cal`. **This is the most critical part for AI-assisted translation.**
*   **Data Aggregation:** Gathers accessory data (`tblHR_Offer_Accessories`, `tblHR_Increment_Accessories`) and incorporates it into the report calculations.
*   **Report Rendering:** Uses Crystal Reports to visualize the aggregated and calculated data.
*   **Navigation:** Provides a "Cancel" button for redirection to other HR modules.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The UI is minimal, focused solely on displaying the Crystal Report viewer and a cancel button.

*   **CrystalReportViewer:** This is the main component. In Django, this will be replaced by server-side PDF generation (e.g., using `WeasyPrint` or `ReportLab`) or rendering the report data directly into an HTML template for browser printing. For structured documents like offer letters, PDF generation is highly recommended for layout control.
*   **asp:Button (Cancel):** A simple button for navigation.

**Modernization Approach for UI:**

Instead of direct report viewing, we will propose:
1.  A **list view** of generated/existing offer/increment letters (using DataTables for search/sort). This provides the "list" aspect requested.
2.  A **detail/print view** that, upon selection, generates the specific offer/increment letter (as a PDF) and offers it for download or display.

### Step 4: Generate Django Code

**Assumed Django App Name:** `hr`

#### 4.1 Models (`hr/models.py`)

**Task:** Create Django models based on the identified database schema. These models will also encapsulate the business logic for salary calculations.

**Instructions:**
-   Define fields with appropriate Django field types.
-   Use `db_column` if field names differ from column names.
-   Set `managed = False` and `db_table` in the `Meta` class.
-   **Critical for "Fat Model":** Embed the complex calculation logic (`Offer_Cal`, `Pf_Cal`, `PTax_Cal`, `Gratuity_Cal`) directly into model methods, or in a dedicated `services.py` module called by the models, ensuring separation of concerns.
    *   **AI Automation Focus:** The translation of `fun.Offer_Cal`, `fun.Pf_Cal`, `fun.PTax_Cal`, `fun.Gratuity_Cal` and the intricate salary calculation logic from C# to Python is a prime candidate for AI-assisted code generation. An AI would analyze the C# function bodies, understand their parameters, formulas, and dependencies, and produce equivalent, optimized Python code. For this example, placeholder methods are used, but the expectation is an automated translation for real-world complexity.

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal, ROUND_HALF_UP # For precise decimal calculations

# --- Helper Functions (AI-Assisted Translation Target for clsFunctions) ---
# These functions would be the direct Python translation of clsFunctions.cs
# For demonstration, simplified versions are provided.
# In a real scenario, AI would parse the C# logic for accurate replication.

def _offer_cal(gross_salary, component_type, calculation_mode, staff_type):
    """
    Simulates fun.Offer_Cal(GrossSalary, Type, Mode, StaffType).
    component_type: 1=Basic, 2=DA, 3=HRA, 4=Conveyance, 5=Education, 6=MedicalAllowance
    calculation_mode: 1=PerMonth, 2=PerAnnum
    staff_type: (used for different calculation rules)
    """
    # Placeholder for complex C# logic. AI would translate this accurately.
    if staff_type == 5: # Assuming a specific staff type has no allowances
        return Decimal('0.00')

    if component_type == 1: # Basic
        val = gross_salary * Decimal('0.40') # Example: 40% of gross
    elif component_type == 2: # DA
        val = gross_salary * Decimal('0.10') # Example: 10% of gross
    elif component_type == 3: # HRA
        val = gross_salary * Decimal('0.20') # Example: 20% of gross
    elif component_type == 4: # Conveyance
        val = Decimal('1600.00') # Example fixed
    elif component_type == 5: # Education
        val = Decimal('100.00') # Example fixed
    elif component_type == 6: # MedicalAllowance
        val = Decimal('1250.00') # Example fixed
    else:
        val = Decimal('0.00')

    return val * (Decimal('12.00') if calculation_mode == 2 else Decimal('1.00'))

def _pf_cal(gross_salary, pf_type, pf_percentage):
    """
    Simulates fun.Pf_Cal(GrossSalary, PfType, PfPercentage).
    pf_type: 1=Employee, 2=Company
    """
    # Placeholder for PF calculation. AI would translate exact rules (e.g., max cap).
    if pf_percentage == 0:
        return Decimal('0.00')
    return (gross_salary * Decimal(str(pf_percentage))) / Decimal('100.00')

def _ptax_cal(taxable_income, marital_status="0"): # Marital status not used in original, assumed fixed
    """
    Simulates fun.PTax_Cal(TaxableIncome, MaritalStatus).
    Placeholder for Professional Tax calculation. This is highly location-specific.
    """
    if taxable_income < 10000:
        return Decimal('0.00')
    elif taxable_income < 15000:
        return Decimal('150.00')
    else:
        return Decimal('200.00')
    
def _gratuity_cal(gross_salary, calculation_mode, type_of):
    """
    Simulates fun.Gratuity_Cal(GrossSalary, Mode, TypeOf).
    Placeholder for Gratuity calculation.
    """
    # Assuming standard gratuity formula (15 days' wages for every completed year of service)
    # This requires 'years of service' which is not in the current context, so a simplified example.
    if type_of == 1: # Example: Assuming certain 'TypeOf' is eligible
        val = (gross_salary / Decimal('26.00')) * Decimal('15.00') # 15 days of salary
        return val * (Decimal('12.00') if calculation_mode == 2 else Decimal('1.00'))
    return Decimal('0.00')


# --- Core Models ---

# Assume a Company model exists for CompId
class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompName', max_length=255) # Assuming CompanyName exists
    # Add other company fields if needed

    class Meta:
        managed = False
        db_table = 'tblCompany' # Example table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.type

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    
    # Assuming Grade and FinYearId are stored here for Offer Letter Year/Grade logic
    grade = models.ForeignKey('Grade', models.DO_NOTHING, db_column='Grade', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"

class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.symbol

class OfferMaster(models.Model):
    # OfferId, SysDate, CompId, Title, EmployeeName, StaffType, TypeOf, salary, DutyHrs, OTHrs, OverTime, ContactNo, EmailId, InterviewedBy, AuthorizedBy, ReferenceBy, Designation, ExGratia, VehicleAllowance, LTA, Loyalty, PaidLeaves, Remarks, HeaderText, FooterText, Bonus, AttBonusPer1, AttBonusPer2, PFEmployee, PFCompany, Increment, IncrementForTheYear, EffectFrom
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    staff_type = models.IntegerField(db_column='StaffType')
    type_of = models.IntegerField(db_column='TypeOf')
    salary = models.DecimalField(db_column='salary', max_digits=18, decimal_places=2) # Gross Salary
    duty_hrs = models.CharField(db_column='DutyHrs', max_length=50, blank=True, null=True)
    ot_hrs = models.CharField(db_column='OTHrs', max_length=50, blank=True, null=True)
    over_time = models.CharField(db_column='OverTime', max_length=50, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    email_id = models.CharField(db_column='EmailId', max_length=255, blank=True, null=True)
    interviewed_by = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='InterviewedBy', related_name='offers_interviewed', blank=True, null=True)
    authorized_by = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='AuthorizedBy', related_name='offers_authorized', blank=True, null=True)
    reference_by = models.CharField(db_column='ReferenceBy', max_length=255, blank=True, null=True)
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    ex_gratia = models.DecimalField(db_column='ExGratia', max_digits=18, decimal_places=2, default=0)
    vehicle_allowance = models.DecimalField(db_column='VehicleAllowance', max_digits=18, decimal_places=2, default=0)
    lta = models.DecimalField(db_column='LTA', max_digits=18, decimal_places=2, default=0)
    loyalty = models.DecimalField(db_column='Loyalty', max_digits=18, decimal_places=2, default=0)
    paid_leaves = models.DecimalField(db_column='PaidLeaves', max_digits=18, decimal_places=2, default=0)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    header_text = models.TextField(db_column='HeaderText', blank=True, null=True)
    footer_text = models.TextField(db_column='FooterText', blank=True, null=True)
    bonus = models.DecimalField(db_column='Bonus', max_digits=18, decimal_places=2, default=0)
    att_bonus_per1 = models.DecimalField(db_column='AttBonusPer1', max_digits=5, decimal_places=2, default=0)
    att_bonus_per2 = models.DecimalField(db_column='AttBonusPer2', max_digits=5, decimal_places=2, default=0)
    pf_employee_per = models.DecimalField(db_column='PFEmployee', max_digits=5, decimal_places=2, default=0)
    pf_company_per = models.DecimalField(db_column='PFCompany', max_digits=5, decimal_places=2, default=0)
    increment_count = models.IntegerField(db_column='Increment', default=0) # Tracks number of increments
    increment_for_the_year = models.CharField(db_column='IncrementForTheYear', max_length=50, blank=True, null=True)
    effect_from = models.DateField(db_column='EffectFrom', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Letter'
        verbose_name_plural = 'Offer Letters'

    def __str__(self):
        return f"Offer ID: {self.offer_id} for {self.employee_name}"

    def calculate_salary_components(self):
        """
        Calculates all salary components based on the OfferMaster data.
        This method replaces a significant portion of the C# calculation logic.
        AI would precisely translate the original `dt` column calculations.
        Returns a dictionary of calculated values.
        """
        gross_salary = self.salary
        staff_type = self.staff_type
        type_of = self.type_of

        # Base components
        basic_per_month = _offer_cal(gross_salary, 1, 1, staff_type)
        basic_per_annum = _offer_cal(gross_salary, 1, 2, staff_type)
        da_per_month = _offer_cal(gross_salary, 2, 1, type_of)
        da_per_annum = _offer_cal(gross_salary, 2, 2, type_of)
        hra_per_month = _offer_cal(gross_salary, 3, 1, type_of)
        hra_per_annum = _offer_cal(gross_salary, 3, 2, type_of)
        conveyance_per_month = _offer_cal(gross_salary, 4, 1, type_of)
        conveyance_per_annum = _offer_cal(gross_salary, 4, 2, type_of)
        education_per_month = _offer_cal(gross_salary, 5, 1, type_of)
        education_per_annum = _offer_cal(gross_salary, 5, 2, type_of)
        medical_allowance_per_month = _offer_cal(gross_salary, 6, 1, type_of)
        medical_allowance_per_annum = _offer_cal(gross_salary, 6, 2, type_of)

        # Bonuses and PF
        att_bonus1_per_month = Decimal('0.00')
        att_bonus2_per_month = Decimal('0.00')
        pfe_per_month = Decimal('0.00')
        pfc_per_month = Decimal('0.00')
        gratuity_per_month = Decimal('0.00')
        
        if staff_type != 5: # Assuming StaffType '5' is special (e.g., consultants)
            att_bonus1_per_month = (gross_salary * self.att_bonus_per1) / Decimal('100.00')
            att_bonus2_per_month = (gross_salary * self.att_bonus_per2) / Decimal('100.00')
            pfe_per_month = _pf_cal(gross_salary, 1, self.pf_employee_per)
            pfc_per_month = _pf_cal(gross_salary, 2, self.pf_company_per)
            gratuity_per_month = _gratuity_cal(gross_salary, 1, type_of)

        # Tax calculations
        ptax_income_base = gross_salary + self.bonus + self.ex_gratia
        ptax = _ptax_cal(ptax_income_base)

        # Accessories Amount (Offer-specific)
        accessories = self.offeraccessory_set.all()
        accessories_amt_ctc = Decimal('0.00')
        accessories_amt_take_home = Decimal('0.00')
        accessories_amt_both = Decimal('0.00')

        for acc in accessories:
            total_acc_amt = acc.qty * acc.amount
            if acc.includes_in == '1': # CTC
                accessories_amt_ctc += total_acc_amt
            elif acc.includes_in == '2': # TakeHome
                accessories_amt_take_home += total_acc_amt
            elif acc.includes_in == '3': # Both
                accessories_amt_both += total_acc_amt

        # Take Home Salary
        th_base = gross_salary + self.ex_gratia + accessories_amt_take_home + accessories_amt_both
        take_home_inr = (th_base - (pfe_per_month + ptax)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        take_home_with_attend1 = (take_home_inr + att_bonus1_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        take_home_with_attend2 = (take_home_inr + att_bonus2_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        # CTC (Cost to Company)
        ctc_base = (gross_salary + self.bonus + self.loyalty + self.lta + gratuity_per_month +
                    pfc_per_month + self.ex_gratia + accessories_amt_ctc + accessories_amt_both)
        ctc_inr = ctc_base.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        ctc_with_attend1 = (ctc_inr + att_bonus1_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        ctc_with_attend2 = (ctc_inr + att_bonus2_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        # Return results in a structured dictionary
        return {
            'offer_id': self.offer_id,
            'company_id': self.company_id,
            'employee_full_name': f"{self.title or ''} {self.employee_name}".strip(),
            'staff_type': self.staff_type,
            'type_of': self.type_of,
            'gross_salary': gross_salary,
            'duty_hrs': self.duty_hrs,
            'ot_hrs': self.ot_hrs,
            'over_time': self.over_time,
            'interviewed_by': self.interviewed_by.title_employee_name if self.interviewed_by else '',
            'authorized_by': self.authorized_by.title_employee_name if self.authorized_by else '',
            'reference_by': self.reference_by,
            'designation_type': self.designation.type if self.designation else '',
            'ex_gratia_monthly': self.ex_gratia,
            'ex_gratia_annual': self.ex_gratia * 12,
            'vehicle_allowance_monthly': self.vehicle_allowance,
            'vehicle_allowance_annual': self.vehicle_allowance * 12,
            'lta_monthly': self.lta,
            'lta_annual': self.lta * 12,
            'loyalty_monthly': self.loyalty,
            'loyalty_annual': self.loyalty * 12,
            'paid_leaves': self.paid_leaves,
            'remarks': self.remarks,
            'header_text': self.header_text,
            'footer_text': self.footer_text,
            'sys_date_formatted': self.sys_date.strftime('%d/%m/%Y'),

            'per_month': gross_salary,
            'per_month_annual': gross_salary * 12,
            'basic_monthly': basic_per_month,
            'basic_annual': basic_per_annum,
            'da_monthly': da_per_month,
            'da_annual': da_per_annum,
            'hra_monthly': hra_per_month,
            'hra_annual': hra_per_annum,
            'conveyance_monthly': conveyance_per_month,
            'conveyance_annual': conveyance_per_annum,
            'education_monthly': education_per_month,
            'education_annual': education_per_annum,
            'medical_allowance_monthly': medical_allowance_per_month,
            'medical_allowance_annual': medical_allowance_per_annum,

            'att_bonus1_monthly': att_bonus1_per_month,
            'att_bonus1_annual': att_bonus1_per_month * 12,
            'att_bonus2_monthly': att_bonus2_per_month,
            'att_bonus2_annual': att_bonus2_per_month * 12,
            'pf_employee_monthly': pfe_per_month,
            'pf_employee_annual': pfe_per_month * 12,
            'pf_company_monthly': pfc_per_month,
            'pf_company_annual': pfc_per_month * 12,
            'bonus_monthly': self.bonus,
            'bonus_annual': self.bonus * 12,
            'gratuity_monthly': gratuity_per_month,
            'gratuity_annual': gratuity_per_month * 12,
            'ptax': ptax,
            'pf_employee_percent': self.pf_employee_per,
            'pf_company_percent': self.pf_company_per,
            'att_bonus_per1_str': str(self.att_bonus_per1),
            'att_bonus_per2_str': str(self.att_bonus_per2),

            'take_home_inr': take_home_inr,
            'take_home_with_attend1': take_home_with_attend1,
            'take_home_with_attend2': take_home_with_attend2,
            'ctc_inr': ctc_inr,
            'ctc_inr_annual': ctc_inr * 12,
            'ctc_with_attend1': ctc_with_attend1,
            'ctc_with_attend1_annual': ctc_with_attend1 * 12,
            'ctc_with_attend2': ctc_with_attend2,
            'ctc_with_attend2_annual': ctc_with_attend2 * 12,

            'accessories_offer': [{'perticulars': acc.perticulars, 'amount': acc.qty * acc.amount} for acc in accessories]
        }

class OfferAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    offer_master = models.ForeignKey(OfferMaster, models.DO_NOTHING, db_column='MId')
    perticulars = models.CharField(db_column='Perticulars', max_length=255)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=2)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)
    includes_in = models.CharField(db_column='IncludesIn', max_length=10) # 1=CTC, 2=TakeHome, 3=Both

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'Offer Accessory'
        verbose_name_plural = 'Offer Accessories'

    def __str__(self):
        return f"{self.perticulars} for Offer {self.offer_master.offer_id}"

class IncrementMaster(models.Model):
    # Id, OfferId, SysDate, CompId, Title, EmployeeName, StaffType, TypeOf, salary, DutyHrs, OTHrs, OverTime, ContactNo, EmailId, InterviewedBy, AuthorizedBy, ReferenceBy, Designation, ExGratia, VehicleAllowance, LTA, Loyalty, PaidLeaves, Remarks, HeaderText, FooterText, Bonus, AttBonusPer1, AttBonusPer2, PFEmployee, PFCompany, Increment, IncrementForTheYear, EffectFrom
    id = models.IntegerField(db_column='Id', primary_key=True)
    offer_master = models.ForeignKey(OfferMaster, models.DO_NOTHING, db_column='OfferId') # Link to the base offer
    sys_date = models.DateField(db_column='SysDate')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    staff_type = models.IntegerField(db_column='StaffType')
    type_of = models.IntegerField(db_column='TypeOf')
    salary = models.DecimalField(db_column='salary', max_digits=18, decimal_places=2) # Gross Salary for this increment
    duty_hrs = models.CharField(db_column='DutyHrs', max_length=50, blank=True, null=True)
    ot_hrs = models.CharField(db_column='OTHrs', max_length=50, blank=True, null=True)
    over_time = models.CharField(db_column='OverTime', max_length=50, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    email_id = models.CharField(db_column='EmailId', max_length=255, blank=True, null=True)
    interviewed_by = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='InterviewedBy', related_name='increments_interviewed', blank=True, null=True)
    authorized_by = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='AuthorizedBy', related_name='increments_authorized', blank=True, null=True)
    reference_by = models.CharField(db_column='ReferenceBy', max_length=255, blank=True, null=True)
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    ex_gratia = models.DecimalField(db_column='ExGratia', max_digits=18, decimal_places=2, default=0)
    vehicle_allowance = models.DecimalField(db_column='VehicleAllowance', max_digits=18, decimal_places=2, default=0)
    lta = models.DecimalField(db_column='LTA', max_digits=18, decimal_places=2, default=0)
    loyalty = models.DecimalField(db_column='Loyalty', max_digits=18, decimal_places=2, default=0)
    paid_leaves = models.DecimalField(db_column='PaidLeaves', max_digits=18, decimal_places=2, default=0)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    header_text = models.TextField(db_column='HeaderText', blank=True, null=True)
    footer_text = models.TextField(db_column='FooterText', blank=True, null=True)
    bonus = models.DecimalField(db_column='Bonus', max_digits=18, decimal_places=2, default=0)
    att_bonus_per1 = models.DecimalField(db_column='AttBonusPer1', max_digits=5, decimal_places=2, default=0)
    att_bonus_per2 = models.DecimalField(db_column='AttBonusPer2', max_digits=5, decimal_places=2, default=0)
    pf_employee_per = models.DecimalField(db_column='PFEmployee', max_digits=5, decimal_places=2, default=0)
    pf_company_per = models.DecimalField(db_column='PFCompany', max_digits=5, decimal_places=2, default=0)
    increment_level = models.IntegerField(db_column='Increment', default=0) # e.g., 1st increment, 2nd increment
    increment_for_the_year = models.CharField(db_column='IncrementForTheYear', max_length=50, blank=True, null=True)
    effect_from = models.DateField(db_column='EffectFrom', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Master'
        verbose_name = 'Increment Letter'
        verbose_name_plural = 'Increment Letters'
        unique_together = ('offer_master', 'increment_level') # An offer can have multiple increments

    def __str__(self):
        return f"Increment Level {self.increment_level} for Offer {self.offer_master.offer_id} ({self.employee_name})"

    def calculate_salary_components(self):
        """
        Calculates all salary components based on the IncrementMaster data.
        This method replaces a significant portion of the C# calculation logic.
        AI would precisely translate the original `dt1` column calculations.
        Returns a dictionary of calculated values.
        """
        gross_salary = self.salary
        staff_type = self.staff_type
        type_of = self.type_of

        # Base components
        basic_per_month = _offer_cal(gross_salary, 1, 1, staff_type)
        da_per_month = _offer_cal(gross_salary, 2, 1, type_of)
        hra_per_month = _offer_cal(gross_salary, 3, 1, type_of)
        conveyance_per_month = _offer_cal(gross_salary, 4, 1, type_of)
        education_per_month = _offer_cal(gross_salary, 5, 1, type_of)
        medical_allowance_per_month = _offer_cal(gross_salary, 6, 1, type_of)

        # Bonuses and PF
        att_bonus1_per_month = Decimal('0.00')
        att_bonus2_per_month = Decimal('0.00')
        pfe_per_month = Decimal('0.00')
        pfc_per_month = Decimal('0.00')
        gratuity_per_month = Decimal('0.00')
        
        if staff_type != 5:
            att_bonus1_per_month = (gross_salary * self.att_bonus_per1) / Decimal('100.00')
            att_bonus2_per_month = (gross_salary * self.att_bonus_per2) / Decimal('100.00')
            pfe_per_month = _pf_cal(gross_salary, 1, self.pf_employee_per)
            pfc_per_month = _pf_cal(gross_salary, 2, self.pf_company_per)
            gratuity_per_month = _gratuity_cal(gross_salary, 1, type_of)

        # Tax calculations
        ptax_income_base = gross_salary + self.bonus + self.ex_gratia
        ptax = _ptax_cal(ptax_income_base)

        # Accessories Amount (Increment-specific)
        accessories = self.incrementaccessory_set.all()
        accessories_amt_ctc = Decimal('0.00')
        accessories_amt_take_home = Decimal('0.00')
        accessories_amt_both = Decimal('0.00')

        for acc in accessories:
            total_acc_amt = acc.qty * acc.amount
            if acc.includes_in == '1': # CTC
                accessories_amt_ctc += total_acc_amt
            elif acc.includes_in == '2': # TakeHome
                accessories_amt_take_home += total_acc_amt
            elif acc.includes_in == '3': # Both
                accessories_amt_both += total_acc_amt

        # Take Home Salary
        th_base = gross_salary + self.ex_gratia + accessories_amt_take_home + accessories_amt_both
        take_home_inr = (th_base - (pfe_per_month + ptax)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        take_home_with_attend1 = (take_home_inr + att_bonus1_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        take_home_with_attend2 = (take_home_inr + att_bonus2_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        # CTC (Cost to Company)
        ctc_base = (gross_salary + self.bonus + self.loyalty + self.lta + gratuity_per_month +
                    pfc_per_month + self.ex_gratia + accessories_amt_ctc + accessories_amt_both)
        ctc_inr = ctc_base.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        ctc_with_attend1 = (ctc_inr + att_bonus1_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        ctc_with_attend2 = (ctc_inr + att_bonus2_per_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        return {
            'offer_id': self.offer_master.offer_id, # Base offer ID
            'current_id': self.id, # This increment's ID
            'company_id': self.company_id,
            'employee_full_name': f"{self.title or ''} {self.employee_name}".strip(),
            'staff_type': self.staff_type,
            'type_of': self.type_of,
            'gross_salary': gross_salary,
            'duty_hrs': self.duty_hrs,
            'ot_hrs': self.ot_hrs,
            'over_time': self.over_time,
            'interviewed_by': self.interviewed_by.title_employee_name if self.interviewed_by else '',
            'authorized_by': self.authorized_by.title_employee_name if self.authorized_by else '',
            'reference_by': self.reference_by,
            'designation_type': self.designation.type if self.designation else '',
            'ex_gratia_monthly': self.ex_gratia,
            'vehicle_allowance_monthly': self.vehicle_allowance,
            'lta_monthly': self.lta,
            'loyalty_monthly': self.loyalty,
            'paid_leaves': self.paid_leaves,
            'remarks': self.remarks,
            'header_text': self.header_text,
            'footer_text': self.footer_text,
            'sys_date_formatted': self.sys_date.strftime('%d/%m/%Y'),
            'increment_for_the_year': self.increment_for_the_year,
            'effect_from_formatted': self.effect_from.strftime('%d/%m/%Y') if self.effect_from else '',

            'per_month': gross_salary,
            'per_month_annual': gross_salary * 12,
            'basic_monthly': basic_per_month,
            'basic_annual': basic_per_month * 12, # Annual calculated from monthly
            'da_monthly': da_per_month,
            'da_annual': da_per_month * 12,
            'hra_monthly': hra_per_month,
            'hra_annual': hra_per_month * 12,
            'conveyance_monthly': conveyance_per_month,
            'conveyance_annual': conveyance_per_month * 12,
            'education_monthly': education_per_month,
            'education_annual': education_per_month * 12,
            'medical_allowance_monthly': medical_allowance_per_month,
            'medical_allowance_annual': medical_allowance_per_month * 12,

            'att_bonus1_monthly': att_bonus1_per_month,
            'att_bonus1_annual': att_bonus1_per_month * 12,
            'att_bonus2_monthly': att_bonus2_per_month,
            'att_bonus2_annual': att_bonus2_per_month * 12,
            'pf_employee_monthly': pfe_per_month,
            'pf_employee_annual': pfe_per_month * 12,
            'pf_company_monthly': pfc_per_month,
            'pf_company_annual': pfc_per_month * 12,
            'bonus_monthly': self.bonus,
            'bonus_annual': self.bonus * 12,
            'gratuity_monthly': gratuity_per_month,
            'gratuity_annual': gratuity_per_month * 12,
            'ptax': ptax,
            'pf_employee_percent': self.pf_employee_per,
            'pf_company_percent': self.pf_company_per,
            'att_bonus_per1_str': str(self.att_bonus_per1),
            'att_bonus_per2_str': str(self.att_bonus_per2),

            'take_home_inr': take_home_inr,
            'take_home_with_attend1': take_home_with_attend1,
            'take_home_with_attend2': take_home_with_attend2,
            'ctc_inr': ctc_inr,
            'ctc_inr_annual': ctc_inr * 12,
            'ctc_with_attend1': ctc_with_attend1,
            'ctc_with_attend1_annual': ctc_with_attend1 * 12,
            'ctc_with_attend2': ctc_with_attend2,
            'ctc_with_attend2_annual': ctc_with_attend2 * 12,

            'accessories_increment': [{'perticulars': acc.perticulars, 'amount': acc.qty * acc.amount} for acc in accessories]
        }

class IncrementAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    increment_master = models.ForeignKey(IncrementMaster, models.DO_NOTHING, db_column='MId')
    offer_master_id = models.IntegerField(db_column='OfferMId') # Used for lookups, not a direct FK in DB
    perticulars = models.CharField(db_column='Perticulars', max_length=255)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=2)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)
    includes_in = models.CharField(db_column='IncludesIn', max_length=10) # 1=CTC, 2=TakeHome, 3=Both

    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Accessories'
        verbose_name = 'Increment Accessory'
        verbose_name_plural = 'Increment Accessories'

    def __str__(self):
        return f"{self.perticulars} for Increment {self.increment_master.id}"

```

#### 4.2 Forms (`hr/forms.py`)

**Task:** Since the original page is for *printing* a report, not for *editing* an offer, standard ModelForms for CRUD operations are not directly applicable to the main functionality.
However, we assume there's a need to *list* offers/increments, and potentially filter them. A simple form for filtering criteria could be useful. For the purpose of this specific conversion (printing details), no traditional form is needed for the `OfferLetter_Print_Details` page itself.

If we consider the upstream pages (e.g., `OfferLetter_Print.aspx`) that list these, a filter form might be relevant. We'll omit a form for the *print details* page as per the direct code analysis.

#### 4.3 Views (`hr/views.py`)

**Task:** Implement a list view for navigating offers/increments and a detail view for generating the actual report.

**Instructions:**
-   Use `ListView` for displaying all offers/increments.
-   Use a custom `View` for handling the report generation, delegating complex data retrieval and calculation to the models.
-   The report view should dynamically serve a PDF response using `WeasyPrint` (recommended for its HTML/CSS to PDF capabilities).
-   Keep views thin (5-15 lines) by offloading business logic to models.

```python
import os
from django.conf import settings
from django.views.generic import ListView, View
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.http import HttpResponse, HttpResponseRedirect
from django.template.loader import render_to_string
from django.contrib import messages

# WeasyPrint for PDF generation
from weasyprint import HTML, CSS

from .models import OfferMaster, IncrementMaster, OfficeStaff, Designation, FinancialYear, Grade

# Helper function to get fully qualified employee name
def get_employee_full_name(staff_id, company_id):
    staff = OfficeStaff.objects.filter(emp_id=staff_id, company_id=company_id).first()
    return f"{staff.title}. {staff.employee_name}" if staff else "N/A"

# Helper function to get designation type
def get_designation_type(designation_id):
    designation = Designation.objects.filter(id=designation_id).first()
    return designation.type if designation else "N/A"

class ReportListView(ListView):
    """
    Displays a list of Offer Letters that can be selected for printing/viewing.
    This replaces the 'selection' aspect before printing.
    """
    model = OfferMaster
    template_name = 'hr/report/list.html'
    context_object_name = 'offers'

    def get_queryset(self):
        # Example: Filter by current user's company (from session)
        company_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        return OfferMaster.objects.filter(company_id=company_id).order_by('-sys_date', '-offer_id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add any other context needed for the list view, e.g., filters
        return context

class OfferLetterPrintView(View):
    """
    Generates and serves the Offer Letter (PDF) for a given Offer ID.
    This replaces the Crystal Report Viewer for offer letters.
    """
    template_name = 'hr/report/offer_letter_content.html' # HTML template for PDF generation

    def get(self, request, offer_id, *args, **kwargs):
        offer = get_object_or_404(OfferMaster, offer_id=offer_id)
        
        # Ensure correct company context, if needed
        # company_id = request.session.get('compid')
        # if company_id and offer.company_id != company_id:
        #     messages.error(request, "Access denied to this offer letter.")
        #     return HttpResponseRedirect(reverse('hr:report_list'))

        # Delegate complex calculations to the model
        calculated_data = offer.calculate_salary_components()
        
        # Fetch related staff for InterviewedBy/AuthorizedBy
        if offer.interviewed_by:
            calculated_data['interviewed_by_full_name'] = offer.interviewed_by.__str__()
        if offer.authorized_by:
            calculated_data['authorized_by_full_name'] = offer.authorized_by.__str__()

        # Context for the HTML template
        context = {
            'offer_data': calculated_data,
            'is_offer_letter': True,
            'current_date': timezone.now().strftime('%d/%m/%Y'),
        }
        
        html_string = render_to_string(self.template_name, context)

        # Generate PDF using WeasyPrint
        pdf_file = HTML(string=html_string, base_url=request.build_absolute_uri('/')).write_pdf(
            stylesheets=[
                CSS(string='@page { size: A4; margin: 2cm; }'),
                # Link to static CSS if available, e.g., CSS(filename=os.path.join(settings.STATIC_ROOT, 'css/report.css'))
                # For this example, we'll assume basic styling in template or inline
            ]
        )

        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="offer_letter_{offer_id}.pdf"'
        return response

class IncrementLetterPrintView(View):
    """
    Generates and serves the Increment Letter (PDF) for a given Offer ID and Increment Level.
    This replaces the Crystal Report Viewer for increment letters.
    """
    template_name = 'hr/report/increment_letter_content.html' # HTML template for PDF generation

    def get(self, request, offer_id, increment_level, *args, **kwargs):
        # Retrieve the current increment data
        current_increment = get_object_or_404(IncrementMaster, offer_master__offer_id=offer_id, increment_level=increment_level)

        # Delegate complex calculations to the model for current increment
        current_data = current_increment.calculate_salary_components()

        # Retrieve previous increment data if applicable (similar to C# logic)
        previous_increment_level = increment_level - 1
        previous_data = None
        if previous_increment_level == 0: # This refers to the original offer
            previous_offer = get_object_or_404(OfferMaster, offer_id=offer_id)
            previous_data = previous_offer.calculate_salary_components()
        elif previous_increment_level > 0:
            previous_increment = IncrementMaster.objects.filter(
                offer_master__offer_id=offer_id, 
                increment_level=previous_increment_level
            ).first()
            if previous_increment:
                previous_data = previous_increment.calculate_salary_components()

        # Fetch additional context for previous data display (e.g., offer year, grade)
        # This logic is extracted from the ASP.NET code (`DSDesOF`, `DSFin`, `DSGrade`)
        offer_staff = OfficeStaff.objects.filter(offer_master=current_increment.offer_master).first()
        if offer_staff:
            current_data['offer_year'] = offer_staff.fin_year.fin_year if offer_staff.fin_year else 'N/A'
            current_data['grade_symbol'] = offer_staff.grade.symbol if offer_staff.grade else 'N/A'
        else:
            current_data['offer_year'] = 'N/A'
            current_data['grade_symbol'] = 'N/A'

        context = {
            'current_data': current_data,
            'previous_data': previous_data,
            'is_increment_letter': True,
            'current_date': timezone.now().strftime('%d/%m/%Y'),
        }

        html_string = render_to_string(self.template_name, context)

        pdf_file = HTML(string=html_string, base_url=request.build_absolute_uri('/')).write_pdf(
            stylesheets=[CSS(string='@page { size: A4; margin: 2cm; }')]
        )

        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="increment_letter_{offer_id}_inc{increment_level}.pdf"'
        return response

class ReportCancelView(View):
    """
    Handles the Cancel button's redirection logic.
    The original logic depends on 'Type', 'EType', 'MonthId', 'BGGroupId', 'Key1'.
    This view maps that to modern Django URL patterns.
    """
    def get(self, request, *args, **kwargs):
        report_type = request.GET.get('T') # Corresponds to Type in ASP.NET
        etype = request.GET.get('EType')
        month_id = request.GET.get('MonthId')
        bggroup_id = request.GET.get('BGGroupId')
        key1 = request.GET.get('Key1') # Not used in Django redirects

        # Based on the ASP.NET switch-case
        if report_type == '1':
            return redirect(reverse('hr:offer_letter_list_main')) # A general list page for offer letters
        elif report_type == '2':
            # Redirect to Salary SAPL Neha Summary
            return redirect(reverse('hr:salary_sapl_neha_summary', kwargs={
                'etype': etype, 'month_id': month_id, 'bggroup_id': bggroup_id
            }))
        elif report_type == '3':
            # Redirect to All Month Summary Report
            return redirect(reverse('hr:all_month_summary_report', kwargs={
                'etype': etype, 'month_id': month_id, 'bggroup_id': bggroup_id
            }))
        elif report_type == '4':
            # Redirect to Consolidated Summary Report
            return redirect(reverse('hr:consolidated_summary_report', kwargs={
                'etype': etype, 'month_id': month_id, 'bggroup_id': bggroup_id
            }))
        else:
            messages.info(request, "No specific redirect path found. Returning to report list.")
            return redirect(reverse('hr:report_list'))

```

#### 4.4 Templates (`hr/templates/hr/report/`)

**Task:** Create templates for the report list view and the content for PDF generation.

**Instructions:**
-   `list.html` for `ReportListView` using DataTables and HTMX.
-   `offer_letter_content.html` for `OfferLetterPrintView` (this is the content that WeasyPrint will convert to PDF).
-   `increment_letter_content.html` for `IncrementLetterPrintView`.
-   Emphasize DRY template inheritance.

**`hr/templates/hr/report/list.html`**
This template provides the initial selection interface using DataTables.

```html
{% extends 'core/base.html' %}

{% block title %}HR Reports List{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">HR Offer & Increment Letters</h2>
    
    <div id="report-table-container"
         hx-trigger="load, refreshReportList from:body"
         hx-get="{% url 'hr:report_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading reports...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state
    });
</script>
{% endblock %}
```

**`hr/templates/hr/report/_report_table_partial.html`**
This partial template is loaded via HTMX for the DataTables view.

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="offerReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer ID</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Salary (Monthly)</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for offer in offers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.offer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.title }} {{ offer.employee_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">₹{{ offer.salary|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.sys_date|date:"d M, Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'hr:offer_letter_print' offer.offer_id %}" target="_blank"
                       class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M11 3a1 1 0 100 2h6a1 1 0 100-2h-6zM11 7a1 1 0 100 2h6a1 1 0 100-2h-6zM11 11a1 1 0 100 2h6a1 1 0 100-2h-6zM13 15a1 1 0 100 2h4a1 1 0 100-2h-4zM7 3h1a1 1 0 011 1v12a1 1 0 01-1 1H7a1 1 0 01-1-1V4a1 1 0 011-1z" clip-rule="evenodd" fill-rule="evenodd"></path>
                        </svg>
                        View Offer
                    </a>
                    {% if offer.increment_count > 0 %}
                    <div x-data="{ open: false }" class="relative inline-block text-left ml-2">
                        <button type="button" @click="open = !open"
                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                            aria-expanded="true" aria-haspopup="true">
                            View Increments ({{ offer.increment_count }})
                            <svg class="-mr-1 ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false"
                            class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                            role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
                            <div class="py-1" role="none">
                                {% for inc in offer.incrementmaster_set.all %}
                                <a href="{% url 'hr:increment_letter_print' offer.offer_id inc.increment_level %}" target="_blank"
                                   class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem" tabindex="-1">
                                    Increment {{ inc.increment_level }} ({{ inc.increment_for_the_year }})
                                </a>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-3 px-4 text-center text-gray-500">No offer letters found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#offerReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>
```

**`hr/templates/hr/report/offer_letter_content.html`**
This template contains the structure and data presentation for the Offer Letter, which will be rendered into a PDF. Tailwind CSS classes are used for general styling, which WeasyPrint can interpret.

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offer Letter - {{ offer_data.employee_full_name }}</title>
    <!-- Tailwind CSS CDN - WeasyPrint can process this for basic styling -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        /* Custom styles for print, if necessary, to override Tailwind defaults or fine-tune layout */
        body { font-family: 'Arial', sans-serif; font-size: 10pt; line-height: 1.4; }
        .page-break { page-break-before: always; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #e2e8f0; padding: 0.5rem; text-align: left; }
        .text-right-print { text-align: right; } /* Specific class for print alignment */
    </style>
</head>
<body class="bg-white text-gray-900">
    <div class="container mx-auto p-6">
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold mb-2">OFFER LETTER</h1>
            <p class="text-sm text-gray-600">Date: {{ current_date }}</p>
        </header>

        <section class="mb-8">
            <p class="mb-4">To,</p>
            <p class="font-semibold">{{ offer_data.employee_full_name }}</p>
            <p class="mb-4">Subject: Offer of Employment as {{ offer_data.designation_type }}</p>

            <p class="mb-4">Dear {{ offer_data.employee_full_name }},</p>
            <div class="prose max-w-none text-sm">
                {{ offer_data.header_text|safe|linebreaksbr }}
            </div>
        </section>

        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4">Salary Structure:</h3>
            <table class="w-full text-sm mb-6">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-2 px-3">Particulars</th>
                        <th class="py-2 px-3 text-right">Monthly (INR)</th>
                        <th class="py-2 px-3 text-right">Annual (INR)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td class="font-medium">Gross Salary</td><td class="text-right-print">{{ offer_data.gross_salary|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.per_month_annual|floatformat:2 }}</td></tr>
                    <tr><td>Basic</td><td class="text-right-print">{{ offer_data.basic_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.basic_annual|floatformat:2 }}</td></tr>
                    <tr><td>DA</td><td class="text-right-print">{{ offer_data.da_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.da_annual|floatformat:2 }}</td></tr>
                    <tr><td>HRA</td><td class="text-right-print">{{ offer_data.hra_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.hra_annual|floatformat:2 }}</td></tr>
                    <tr><td>Conveyance</td><td class="text-right-print">{{ offer_data.conveyance_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.conveyance_annual|floatformat:2 }}</td></tr>
                    <tr><td>Education Allowance</td><td class="text-right-print">{{ offer_data.education_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.education_annual|floatformat:2 }}</td></tr>
                    <tr><td>Medical Allowance</td><td class="text-right-print">{{ offer_data.medical_allowance_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.medical_allowance_annual|floatformat:2 }}</td></tr>
                    {% if offer_data.att_bonus1_monthly > 0 %}
                    <tr><td>Attendance Bonus ({{ offer_data.att_bonus_per1_str }}%)</td><td class="text-right-print">{{ offer_data.att_bonus1_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.att_bonus1_annual|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if offer_data.att_bonus2_monthly > 0 %}
                    <tr><td>Attendance Bonus 2 ({{ offer_data.att_bonus_per2_str }}%)</td><td class="text-right-print">{{ offer_data.att_bonus2_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.att_bonus2_annual|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if offer_data.ex_gratia_monthly > 0 %}
                    <tr><td>Ex Gratia</td><td class="text-right-print">{{ offer_data.ex_gratia_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.ex_gratia_annual|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if offer_data.vehicle_allowance_monthly > 0 %}
                    <tr><td>Vehicle Allowance</td><td class="text-right-print">{{ offer_data.vehicle_allowance_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.vehicle_allowance_annual|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if offer_data.lta_monthly > 0 %}
                    <tr><td>LTA</td><td class="text-right-print">{{ offer_data.lta_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.lta_annual|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if offer_data.loyalty_monthly > 0 %}
                    <tr><td>Loyalty Benefit</td><td class="text-right-print">{{ offer_data.loyalty_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.loyalty_annual|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% for acc in offer_data.accessories_offer %}
                    <tr><td>{{ acc.perticulars }}</td><td class="text-right-print">{{ acc.amount|floatformat:2 }}</td><td class="text-right-print">{{ (acc.amount * 12)|floatformat:2 }}</td></tr>
                    {% endfor %}
                    <tr><td class="font-medium">PF (Employee - {{ offer_data.pf_employee_percent }}%)</td><td class="text-right-print">{{ offer_data.pf_employee_monthly|floatformat:2 }}</td><td class="text-right-print">{{ offer_data.pf_employee_annual|floatformat:2 }}</td></tr>
                    <tr><td class="font-medium">Professional Tax</td><td class="text-right-print">{{ offer_data.ptax|floatformat:2 }}</td><td class="text-right-print">{{ (offer_data.ptax * 12)|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Net Take Home Salary</td><td class="text-right-print font-bold">{{ offer_data.take_home_inr|floatformat:2 }}</td><td class="text-right-print font-bold">{{ (offer_data.take_home_inr * 12)|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Net Take Home with Att. Bonus 1</td><td class="text-right-print font-bold">{{ offer_data.take_home_with_attend1|floatformat:2 }}</td><td class="text-right-print font-bold">{{ (offer_data.take_home_with_attend1 * 12)|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Net Take Home with Att. Bonus 2</td><td class="text-right-print font-bold">{{ offer_data.take_home_with_attend2|floatformat:2 }}</td><td class="text-right-print font-bold">{{ (offer_data.take_home_with_attend2 * 12)|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Cost to Company (CTC)</td><td class="text-right-print font-bold">{{ offer_data.ctc_inr|floatformat:2 }}</td><td class="text-right-print font-bold">{{ offer_data.ctc_inr_annual|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">CTC with Att. Bonus 1</td><td class="text-right-print font-bold">{{ offer_data.ctc_with_attend1|floatformat:2 }}</td><td class="text-right-print font-bold">{{ offer_data.ctc_with_attend1_annual|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">CTC with Att. Bonus 2</td><td class="text-right-print font-bold">{{ offer_data.ctc_with_attend2|floatformat:2 }}</td><td class="text-right-print font-bold">{{ offer_data.ctc_with_attend2_annual|floatformat:2 }}</td></tr>
                </tbody>
            </table>
        </section>

        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4">Terms and Conditions:</h3>
            <div class="prose max-w-none text-sm">
                {{ offer_data.remarks|safe|linebreaksbr }}
            </div>
        </section>

        <footer class="mt-8 text-center text-sm text-gray-600">
            <div class="prose max-w-none text-sm">
                {{ offer_data.footer_text|safe|linebreaksbr }}
            </div>
            <p class="mt-4">Authorized By: {{ offer_data.authorized_by_full_name }}</p>
            <p>Interviewed By: {{ offer_data.interviewed_by_full_name }}</p>
        </footer>
    </div>
</body>
</html>
```

**`hr/templates/hr/report/increment_letter_content.html`**
This template renders the Increment Letter, including comparisons between current and previous salary structures.

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Increment Letter - {{ current_data.employee_full_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Arial', sans-serif; font-size: 10pt; line-height: 1.4; }
        .page-break { page-break-before: always; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #e2e8f0; padding: 0.5rem; text-align: left; }
        .text-right-print { text-align: right; }
    </style>
</head>
<body class="bg-white text-gray-900">
    <div class="container mx-auto p-6">
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold mb-2">INCREMENT LETTER</h1>
            <p class="text-sm text-gray-600">Date: {{ current_date }}</p>
        </header>

        <section class="mb-8">
            <p class="mb-4">To,</p>
            <p class="font-semibold">{{ current_data.employee_full_name }}</p>
            <p class="mb-4">Subject: Increment in Salary for the year {{ current_data.increment_for_the_year }}</p>
            <p class="mb-4">Effective From: {{ current_data.effect_from_formatted }}</p>

            <p class="mb-4">Dear {{ current_data.employee_full_name }},</p>
            <div class="prose max-w-none text-sm">
                {{ current_data.header_text|safe|linebreaksbr }}
            </div>
        </section>

        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4">Revised Salary Structure:</h3>
            <table class="w-full text-sm mb-6">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-2 px-3">Particulars</th>
                        <th class="py-2 px-3 text-right">Previous Monthly (INR)</th>
                        <th class="py-2 px-3 text-right">Current Monthly (INR)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td class="font-medium">Gross Salary</td>
                        <td class="text-right-print">{{ previous_data.gross_salary|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.gross_salary|floatformat:2 }}</td></tr>
                    <tr><td>Basic</td>
                        <td class="text-right-print">{{ previous_data.basic_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.basic_monthly|floatformat:2 }}</td></tr>
                    <tr><td>DA</td>
                        <td class="text-right-print">{{ previous_data.da_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.da_monthly|floatformat:2 }}</td></tr>
                    <tr><td>HRA</td>
                        <td class="text-right-print">{{ previous_data.hra_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.hra_monthly|floatformat:2 }}</td></tr>
                    <tr><td>Conveyance</td>
                        <td class="text-right-print">{{ previous_data.conveyance_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.conveyance_monthly|floatformat:2 }}</td></tr>
                    <tr><td>Education Allowance</td>
                        <td class="text-right-print">{{ previous_data.education_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.education_monthly|floatformat:2 }}</td></tr>
                    <tr><td>Medical Allowance</td>
                        <td class="text-right-print">{{ previous_data.medical_allowance_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.medical_allowance_monthly|floatformat:2 }}</td></tr>
                    {% if current_data.att_bonus1_monthly > 0 %}
                    <tr><td>Attendance Bonus ({{ current_data.att_bonus_per1_str }}%)</td>
                        <td class="text-right-print">{{ previous_data.att_bonus1_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.att_bonus1_monthly|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if current_data.att_bonus2_monthly > 0 %}
                    <tr><td>Attendance Bonus 2 ({{ current_data.att_bonus_per2_str }}%)</td>
                        <td class="text-right-print">{{ previous_data.att_bonus2_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.att_bonus2_monthly|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if current_data.ex_gratia_monthly > 0 %}
                    <tr><td>Ex Gratia</td>
                        <td class="text-right-print">{{ previous_data.ex_gratia_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.ex_gratia_monthly|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if current_data.vehicle_allowance_monthly > 0 %}
                    <tr><td>Vehicle Allowance</td>
                        <td class="text-right-print">{{ previous_data.vehicle_allowance_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.vehicle_allowance_monthly|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if current_data.lta_monthly > 0 %}
                    <tr><td>LTA</td>
                        <td class="text-right-print">{{ previous_data.lta_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.lta_monthly|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% if current_data.loyalty_monthly > 0 %}
                    <tr><td>Loyalty Benefit</td>
                        <td class="text-right-print">{{ previous_data.loyalty_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.loyalty_monthly|floatformat:2 }}</td></tr>
                    {% endif %}
                    {% for acc in current_data.accessories_increment %}
                    <tr><td>{{ acc.perticulars }}</td>
                        <td class="text-right-print">{% if previous_data %}{{ previous_data.accessories_increment|get_item_by_key:'perticulars' acc.perticulars 'amount'|floatformat:2 }}{% else %}N/A{% endif %}</td>
                        <td class="text-right-print">{{ acc.amount|floatformat:2 }}</td></tr>
                    {% endfor %}
                    <tr><td class="font-medium">PF (Employee - {{ current_data.pf_employee_percent }}%)</td>
                        <td class="text-right-print">{{ previous_data.pf_employee_monthly|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.pf_employee_monthly|floatformat:2 }}</td></tr>
                    <tr><td class="font-medium">Professional Tax</td>
                        <td class="text-right-print">{{ previous_data.ptax|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print">{{ current_data.ptax|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Net Take Home Salary</td>
                        <td class="text-right-print font-bold">{{ previous_data.take_home_inr|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print font-bold">{{ current_data.take_home_inr|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Net Take Home with Att. Bonus 1</td>
                        <td class="text-right-print font-bold">{{ previous_data.take_home_with_attend1|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print font-bold">{{ current_data.take_home_with_attend1|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Net Take Home with Att. Bonus 2</td>
                        <td class="text-right-print font-bold">{{ previous_data.take_home_with_attend2|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print font-bold">{{ current_data.take_home_with_attend2|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">Cost to Company (CTC)</td>
                        <td class="text-right-print font-bold">{{ previous_data.ctc_inr|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print font-bold">{{ current_data.ctc_inr|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">CTC with Att. Bonus 1</td>
                        <td class="text-right-print font-bold">{{ previous_data.ctc_with_attend1|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print font-bold">{{ current_data.ctc_with_attend1|floatformat:2 }}</td></tr>
                    <tr><td class="font-bold">CTC with Att. Bonus 2</td>
                        <td class="text-right-print font-bold">{{ previous_data.ctc_with_attend2|floatformat:2 if previous_data else 'N/A' }}</td>
                        <td class="text-right-print font-bold">{{ current_data.ctc_with_attend2|floatformat:2 }}</td></tr>
                </tbody>
            </table>
        </section>

        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4">Terms and Conditions:</h3>
            <div class="prose max-w-none text-sm">
                {{ current_data.remarks|safe|linebreaksbr }}
            </div>
        </section>

        <footer class="mt-8 text-center text-sm text-gray-600">
            <div class="prose max-w-none text-sm">
                {{ current_data.footer_text|safe|linebreaksbr }}
            </div>
            <p class="mt-4">Authorized By: {{ current_data.authorized_by_full_name }}</p>
            <p>Interviewed By: {{ current_data.interviewed_by_full_name }}</p>
        </footer>
    </div>
</body>
</html>
```
**Note:** For `increment_letter_content.html`, a custom Django template filter `get_item_by_key` might be needed to safely access nested dictionaries/lists for `previous_data.accessories_increment`. This needs to be registered in `hr/templatetags/hr_filters.py`.

**`hr/templatetags/hr_filters.py`** (Create this file and `__init__.py` in `hr/templatetags/`)
```python
from django import template

register = template.Library()

@register.filter
def get_item_by_key(data_list, key_value, key_name='perticulars', return_key='amount'):
    """
    Looks up an item in a list of dictionaries by a given key/value pair
    and returns a specified value from that dictionary.
    Useful for accessing accessories in previous_data.
    Example: {{ previous_data.accessories_increment|get_item_by_key:'Perticulars' 'amount' }}
    """
    if not isinstance(data_list, list):
        return None
    for item in data_list:
        if isinstance(item, dict) and item.get(key_name) == key_value:
            return item.get(return_key)
    return None

```
Remember to add `'hr.templatetags'` to `INSTALLED_APPS` (though Django usually finds them if they're correctly placed) and `{% load hr_filters %}` in the template.

#### 4.5 URLs (`hr/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import (
    ReportListView, OfferLetterPrintView, 
    IncrementLetterPrintView, ReportCancelView,
)

app_name = 'hr' # Namespace for HR app URLs

urlpatterns = [
    # Main Report List View
    path('reports/', ReportListView.as_view(), name='report_list'),
    path('reports/table-partial/', ReportListView.as_view(), name='report_table_partial'), # HTMX partial load

    # Report Print Views
    path('offer-letter/print/<int:offer_id>/', OfferLetterPrintView.as_view(), name='offer_letter_print'),
    path('increment-letter/print/<int:offer_id>/<int:increment_level>/', IncrementLetterPrintView.as_view(), name='increment_letter_print'),

    # Cancel Button Redirects (mapping to example dummy views/paths)
    path('reports/cancel/', ReportCancelView.as_view(), name='report_cancel'),
    
    # Dummy paths for cancel redirects (these would be implemented as actual views/modules)
    path('hr/offer-letter-list/', ReportListView.as_view(), name='offer_letter_list_main'), # Example redirect from type=1
    path('hr/salary-summary/<str:etype>/<int:month_id>/<int:bggroup_id>/', View.as_view(), name='salary_sapl_neha_summary'), # Placeholder
    path('hr/all-month-summary/<str:etype>/<int:month_id>/<int:bggroup_id>/', View.as_view(), name='all_month_summary_report'), # Placeholder
    path('hr/consolidated-summary/<str:etype>/<int:month_id>/<int:bggroup_id>/', View.as_view(), name='consolidated_summary_report'), # Placeholder
]
```
**Note on dummy paths:** The `View.as_view()` placeholders would be replaced by actual views for those summary reports once those modules are migrated.

#### 4.6 Tests (`hr/tests.py`)

**Task:** Write comprehensive tests for the models and views, ensuring good test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal

# Import all models
from .models import (
    Company, FinancialYear, Designation, OfficeStaff, Grade,
    OfferMaster, OfferAccessory, IncrementMaster, IncrementAccessory,
    _offer_cal, _pf_cal, _ptax_cal, _gratuity_cal # Test helper functions directly
)

class HRCalculationHelpersTest(TestCase):
    def test_offer_cal_basic(self):
        # Test basic calculation
        gross = Decimal('10000.00')
        staff_type = 1
        self.assertEqual(_offer_cal(gross, 1, 1, staff_type), Decimal('4000.00')) # Basic monthly
        self.assertEqual(_offer_cal(gross, 1, 2, staff_type), Decimal('48000.00')) # Basic annual

    def test_offer_cal_fixed_allowance(self):
        gross = Decimal('10000.00')
        type_of = 1
        self.assertEqual(_offer_cal(gross, 4, 1, type_of), Decimal('1600.00')) # Conveyance monthly

    def test_offer_cal_staff_type_5(self):
        # StaffType 5 should return 0 for allowances
        gross = Decimal('10000.00')
        staff_type = 5
        self.assertEqual(_offer_cal(gross, 1, 1, staff_type), Decimal('0.00'))
        self.assertEqual(_offer_cal(gross, 3, 1, staff_type), Decimal('0.00'))

    def test_pf_cal(self):
        gross = Decimal('15000.00')
        pf_perc = Decimal('12.00')
        self.assertEqual(_pf_cal(gross, 1, pf_perc), Decimal('1800.00')) # Employee PF
        self.assertEqual(_pf_cal(gross, 2, pf_perc), Decimal('1800.00')) # Company PF
        self.assertEqual(_pf_cal(gross, 1, Decimal('0.00')), Decimal('0.00'))

    def test_ptax_cal(self):
        self.assertEqual(_ptax_cal(Decimal('5000.00')), Decimal('0.00'))
        self.assertEqual(_ptax_cal(Decimal('12000.00')), Decimal('150.00'))
        self.assertEqual(_ptax_cal(Decimal('20000.00')), Decimal('200.00'))

    def test_gratuity_cal(self):
        gross = Decimal('26000.00')
        type_of = 1
        self.assertEqual(_gratuity_cal(gross, 1, type_of), Decimal('15000.00')) # Monthly
        self.assertEqual(_gratuity_cal(gross, 2, type_of), Decimal('180000.00')) # Annual
        self.assertEqual(_gratuity_cal(gross, 1, 99), Decimal('0.00')) # Ineligible type

class HRModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.financial_year = FinancialYear.objects.create(id=1, fin_year='2023-2024')
        cls.designation = Designation.objects.create(id=101, type='Software Engineer')
        cls.interviewer = OfficeStaff.objects.create(emp_id=1, company=cls.company, title='Mr', employee_name='John Doe')
        cls.authorizer = OfficeStaff.objects.create(emp_id=2, company=cls.company, title='Ms', employee_name='Jane Smith')
        cls.grade = Grade.objects.create(id=1, symbol='A')

        cls.offer = OfferMaster.objects.create(
            offer_id=1,
            sys_date=timezone.now().date(),
            company=cls.company,
            title='Mr',
            employee_name='Alice Wonderland',
            staff_type=1,
            type_of=1,
            salary=Decimal('50000.00'),
            duty_hrs='9',
            ot_hrs='2',
            over_time='Approved',
            contact_no='1234567890',
            email_id='<EMAIL>',
            interviewed_by=cls.interviewer,
            authorized_by=cls.authorizer,
            reference_by='Referral X',
            designation=cls.designation,
            ex_gratia=Decimal('1000.00'),
            vehicle_allowance=Decimal('500.00'),
            lta=Decimal('800.00'),
            loyalty=Decimal('200.00'),
            paid_leaves=Decimal('15.00'),
            remarks='Standard offer terms apply.',
            header_text='Header content.',
            footer_text='Footer content.',
            bonus=Decimal('5000.00'),
            att_bonus_per1=Decimal('5.00'),
            att_bonus_per2=Decimal('10.00'),
            pf_employee_per=Decimal('12.00'),
            pf_company_per=Decimal('12.00'),
            increment_count=0,
            increment_for_the_year=None,
            effect_from=None
        )
        OfferAccessory.objects.create(
            id=1, offer_master=cls.offer, perticulars='Laptop', qty=1, amount=Decimal('50000.00'), includes_in='1' # CTC
        )
        OfferAccessory.objects.create(
            id=2, offer_master=cls.offer, perticulars='Mobile', qty=1, amount=Decimal('10000.00'), includes_in='2' # Take Home
        )

        cls.increment = IncrementMaster.objects.create(
            id=1,
            offer_master=cls.offer,
            sys_date=timezone.now().date(),
            company=cls.company,
            title='Mr',
            employee_name='Alice Wonderland',
            staff_type=1,
            type_of=1,
            salary=Decimal('60000.00'), # Increased salary
            duty_hrs='9',
            ot_hrs='2',
            over_time='Approved',
            contact_no='1234567890',
            email_id='<EMAIL>',
            interviewed_by=cls.interviewer,
            authorized_by=cls.authorizer,
            reference_by='Referral X',
            designation=cls.designation,
            ex_gratia=Decimal('1200.00'),
            vehicle_allowance=Decimal('600.00'),
            lta=Decimal('900.00'),
            loyalty=Decimal('250.00'),
            paid_leaves=Decimal('15.00'),
            remarks='Revised terms.',
            header_text='Increment Header.',
            footer_text='Increment Footer.',
            bonus=Decimal('6000.00'),
            att_bonus_per1=Decimal('5.00'),
            att_bonus_per2=Decimal('10.00'),
            pf_employee_per=Decimal('12.00'),
            pf_company_per=Decimal('12.00'),
            increment_level=1,
            increment_for_the_year='2024-2025',
            effect_from=timezone.now().date()
        )
        IncrementAccessory.objects.create(
            id=1, increment_master=cls.increment, offer_master_id=cls.offer.offer_id,
            perticulars='Revised Laptop', qty=1, amount=Decimal('60000.00'), includes_in='1'
        )

    def test_offer_master_creation(self):
        self.assertEqual(self.offer.employee_name, 'Alice Wonderland')
        self.assertEqual(self.offer.salary, Decimal('50000.00'))
        self.assertEqual(self.offer.offeraccessory_set.count(), 2)

    def test_offer_master_salary_calculation(self):
        calculated_data = self.offer.calculate_salary_components()
        self.assertAlmostEqual(calculated_data['gross_salary'], Decimal('50000.00'))
        self.assertAlmostEqual(calculated_data['basic_monthly'], Decimal('20000.00')) # 50000 * 0.4
        self.assertAlmostEqual(calculated_data['att_bonus1_monthly'], Decimal('2500.00')) # 50000 * 0.05
        self.assertAlmostEqual(calculated_data['pf_employee_monthly'], Decimal('6000.00')) # 50000 * 0.12
        self.assertAlmostEqual(calculated_data['ptax'], Decimal('200.00'))
        
        # Verify accessories are included
        laptop_accessory = next((acc for acc in calculated_data['accessories_offer'] if acc['perticulars'] == 'Laptop'), None)
        self.assertIsNotNone(laptop_accessory)
        self.assertEqual(laptop_accessory['amount'], Decimal('50000.00'))

        # Gross + ExGratia + Accessories_TakeHome + Accessories_Both - (PFE + PTax)
        expected_th = (Decimal('50000.00') + Decimal('1000.00') + Decimal('10000.00') + Decimal('0.00')) - (Decimal('6000.00') + Decimal('200.00'))
        self.assertAlmostEqual(calculated_data['take_home_inr'], expected_th)

        # Gross + Bonus + Loyalty + LTA + Gratuity + PFC + ExGratia + Acc_CTC + Acc_Both
        expected_ctc_base = (Decimal('50000.00') + Decimal('5000.00') + Decimal('200.00') + Decimal('800.00') + Decimal('15000.00') +
                             Decimal('6000.00') + Decimal('1000.00') + Decimal('50000.00') + Decimal('0.00'))
        self.assertAlmostEqual(calculated_data['ctc_inr'], expected_ctc_base)

    def test_increment_master_creation(self):
        self.assertEqual(self.increment.employee_name, 'Alice Wonderland')
        self.assertEqual(self.increment.salary, Decimal('60000.00'))
        self.assertEqual(self.increment.increment_level, 1)
        self.assertEqual(self.increment.incrementaccessory_set.count(), 1)

    def test_increment_master_salary_calculation(self):
        calculated_data = self.increment.calculate_salary_components()
        self.assertAlmostEqual(calculated_data['gross_salary'], Decimal('60000.00'))
        self.assertAlmostEqual(calculated_data['basic_monthly'], Decimal('24000.00')) # 60000 * 0.4
        self.assertAlmostEqual(calculated_data['pf_employee_monthly'], Decimal('7200.00')) # 60000 * 0.12

        # Verify accessories are included
        laptop_accessory = next((acc for acc in calculated_data['accessories_increment'] if acc['perticulars'] == 'Revised Laptop'), None)
        self.assertIsNotNone(laptop_accessory)
        self.assertEqual(laptop_accessory['amount'], Decimal('60000.00'))

class HRViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.financial_year = FinancialYear.objects.create(id=1, fin_year='2023-2024')
        cls.designation = Designation.objects.create(id=101, type='Software Engineer')
        cls.interviewer = OfficeStaff.objects.create(emp_id=1, company=cls.company, title='Mr', employee_name='John Doe')
        cls.authorizer = OfficeStaff.objects.create(emp_id=2, company=cls.company, title='Ms', employee_name='Jane Smith')
        cls.grade = Grade.objects.create(id=1, symbol='A')

        cls.offer1 = OfferMaster.objects.create(
            offer_id=1, sys_date=timezone.now().date(), company=cls.company, title='Mr', employee_name='Alice One',
            staff_type=1, type_of=1, salary=Decimal('50000.00'), interviewed_by=cls.interviewer, authorized_by=cls.authorizer,
            designation=cls.designation, ex_gratia=0, vehicle_allowance=0, lta=0, loyalty=0, paid_leaves=0, bonus=0,
            att_bonus_per1=0, att_bonus_per2=0, pf_employee_per=0, pf_company_per=0, increment_count=0
        )
        cls.offer2 = OfferMaster.objects.create(
            offer_id=2, sys_date=timezone.now().date(), company=cls.company, title='Ms', employee_name='Bob Two',
            staff_type=1, type_of=1, salary=Decimal('45000.00'), interviewed_by=cls.interviewer, authorized_by=cls.authorizer,
            designation=cls.designation, ex_gratia=0, vehicle_allowance=0, lta=0, loyalty=0, paid_leaves=0, bonus=0,
            att_bonus_per1=0, att_bonus_per2=0, pf_employee_per=0, pf_company_per=0, increment_count=1
        )
        cls.increment1_for_offer2 = IncrementMaster.objects.create(
            id=1, offer_master=cls.offer2, sys_date=timezone.now().date(), company=cls.company,
            title='Ms', employee_name='Bob Two', staff_type=1, type_of=1, salary=Decimal('55000.00'),
            interviewed_by=cls.interviewer, authorized_by=cls.authorizer, designation=cls.designation,
            ex_gratia=0, vehicle_allowance=0, lta=0, loyalty=0, paid_leaves=0, bonus=0,
            att_bonus_per1=0, att_bonus_per2=0, pf_employee_per=0, pf_company_per=0,
            increment_level=1, increment_for_the_year='2024-2025', effect_from=timezone.now().date()
        )

    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.company.id # Simulate session company ID

    def test_report_list_view(self):
        response = self.client.get(reverse('hr:report_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/report/list.html')
        self.assertIn('offers', response.context)
        self.assertEqual(response.context['offers'].count(), 2)
        self.assertContains(response, 'Alice One')
        self.assertContains(response, 'Bob Two')

    def test_report_table_partial_view(self):
        response = self.client.get(reverse('hr:report_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/report/_report_table_partial.html')
        self.assertIn('offers', response.context)
        self.assertEqual(response.context['offers'].count(), 2)

    def test_offer_letter_print_view(self):
        response = self.client.get(reverse('hr:offer_letter_print', args=[self.offer1.offer_id]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertTrue('Content-Disposition' in response)
        self.assertIn(f'filename="offer_letter_{self.offer1.offer_id}.pdf"', response['Content-Disposition'])

    def test_offer_letter_print_view_not_found(self):
        response = self.client.get(reverse('hr:offer_letter_print', args=[999]))
        self.assertEqual(response.status_code, 404)

    def test_increment_letter_print_view(self):
        response = self.client.get(reverse('hr:increment_letter_print', args=[self.offer2.offer_id, self.increment1_for_offer2.increment_level]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertTrue('Content-Disposition' in response)
        self.assertIn(f'filename="increment_letter_{self.offer2.offer_id}_inc{self.increment1_for_offer2.increment_level}.pdf"', response['Content-Disposition'])

    def test_increment_letter_print_view_not_found(self):
        response = self.client.get(reverse('hr:increment_letter_print', args=[self.offer2.offer_id, 999]))
        self.assertEqual(response.status_code, 404)
        response = self.client.get(reverse('hr:increment_letter_print', args=[999, 1]))
        self.assertEqual(response.status_code, 404)

    def test_report_cancel_view(self):
        # Test case 1: Type = 1
        response = self.client.get(reverse('hr:report_cancel') + '?T=1')
        self.assertRedirects(response, reverse('hr:offer_letter_list_main'))

        # Test case 2: Type = 2
        response = self.client.get(reverse('hr:report_cancel') + '?T=2&EType=EMP1&MonthId=1&BGGroupId=10')
        self.assertRedirects(response, reverse('hr:salary_sapl_neha_summary', kwargs={'etype': 'EMP1', 'month_id': 1, 'bggroup_id': 10}))

        # Test case 3: Type = unknown
        response = self.client.get(reverse('hr:report_cancel') + '?T=99')
        self.assertRedirects(response, reverse('hr:report_list'))
        
        # Test HTMX interaction (though cancel is a redirect, not typical HTMX swap)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr:report_cancel') + '?T=1', follow=True, **headers)
        self.assertEqual(response.status_code, 200) # Should follow the redirect and render target page

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **DataTables Initialization:** The `_report_table_partial.html` includes JavaScript to initialize DataTables. This ensures client-side sorting, searching, and pagination without full page reloads.
-   **HTMX for Dynamic Content:** The `report_list.html` uses `hx-get` and `hx-trigger="load, refreshReportList from:body"` to load the DataTable content dynamically after the initial page load or when a custom event `refreshReportList` is triggered. This event can be triggered from other parts of the application (e.g., after an offer/increment is created/updated on a different page, assuming those pages also use HTMX for their CRUD).
-   **Alpine.js for UI State:** Alpine.js is used in the `_report_table_partial.html` for the "View Increments" dropdown. This shows how Alpine.js handles simple UI state (like `open: false`) directly in HTML, keeping JavaScript minimal and localized to the component.
-   **PDF Generation (Non-HTMX):** The actual report printing links (`href`) open in a new tab (`target="_blank"`) because they serve a PDF file directly, which is a standard browser behavior for documents, not typically an HTMX swap.

**Final Notes:**

-   **Placeholders:** Replace dummy values and `N/A` with actual data or appropriate fallbacks after the full data migration. The `_offer_cal`, `_pf_cal`, `_ptax_cal`, `_gratuity_cal` functions in `models.py` are simplified representations of the original C# `clsFunctions` logic. **AI-assisted automation is crucial here to translate the precise mathematical and conditional logic from the C# source.**
-   **Scalability for Reports:** For very complex or pixel-perfect reports, a dedicated reporting solution might be considered, but `WeasyPrint` often suffices for business documents and integrates seamlessly with Django templates.
-   **Error Handling and User Feedback:** Implement more robust error handling and user feedback beyond simple `messages.error` (e.g., custom error pages, more detailed messages).
-   **Authentication/Authorization:** This plan assumes existing user authentication. Ensure Django's authentication system and permission checks are integrated into views (e.g., `@login_required` decorator or `LoginRequiredMixin`).
-   **Configuration:** Ensure `django-weasyprint` and `django-crispy-forms` (if using forms) are installed and configured in `settings.py`. Ensure static files are properly collected if using custom CSS for PDFs.
-   **DRY Principle:** The use of model methods for calculations and partial templates for repeatable UI components adheres to the DRY principle.