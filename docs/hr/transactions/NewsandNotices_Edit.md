## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Analyzing the `SqlDataSource1` and the C# code-behind, we identify the primary table involved and its relevant columns.

*   **Primary Table:** `tblHR_News_Notices`
*   **Columns for `tblHR_News_Notices`:**
    *   `Id` (Primary Key, integer)
    *   `Title` (string)
    *   `FromDate` (string, stores date in a non-standard format, converted to `DD-MM-YYYY` by the SQL query)
    *   `ToDate` (string, stores date in a non-standard format, converted to `DD-MM-YYYY` by the SQL query)
    *   `FileName` (string)
    *   `FinYearId` (integer, used as a foreign key to `tblFinancial_master`)
    *   `CompId` (integer, used as a filter based on session)

*   **Auxiliary Table:** `tblFinancial_master` (for `FinYear` lookup)
*   **Columns for `tblFinancial_master`:**
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (string, e.g., "2023-2024")

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code (`NewsandNotices_Edit.aspx` and its code-behind) primarily focuses on the **Read** operation, displaying a list of news and notices.

*   **Create:** No explicit create functionality is present on this specific page. However, for a complete modernization, we will include a Django `CreateView`.
*   **Read (List):** The `GridView2` populates a list of news and notices from `tblHR_News_Notices`. It filters records based on `CompId` (from session) and orders them by `Id` in descending order. It also performs a lookup to display the `FinYear` from `tblFinancial_master` using `FinYearId`. This will be handled by a Django `ListView`.
*   **Update:** No direct update functionality is present on this page. The "Select" link navigates to `NewsandNotices_Edit_Details.aspx`, implying a separate page for editing. For a complete modernization, we will include a Django `UpdateView`.
*   **Delete:** No explicit delete functionality is present on this page. For a complete modernization, we will include a Django `DeleteView`.
*   **Validation Logic:** No explicit validation logic is visible on this list page. Any validation would typically occur on the detail/edit page.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **`GridView2`**: This control is used for displaying a paginated list of "News & Notices". In Django, this will be replaced by an HTML `<table>` structured for DataTables, populated by a Django `ListView` and rendered as an HTMX partial.
    *   **Columns:** `SN` (row number), `HyperLinkField` (for "Select" to detail view), `Fin Year`, `Id` (hidden), `Title`, `From Date`, `To Date`. These will be mapped to table columns.
    *   **Pagination:** `AllowPaging="True"`, `PageSize="20"`. This will be handled by DataTables on the client-side.
*   **`SqlDataSource1`**: This component handles data retrieval. In Django, the Django ORM and `ListView` will replace this.
*   **Styling:** `CssClass="yui-datatable-theme"`. This will be replaced by Tailwind CSS styling and DataTables default styling.
*   **Navigation Links:** The `HyperLinkField` linking to `NewsandNotices_Edit_Details.aspx` will be replaced by Django URLs to an `UpdateView` and integrated with HTMX modals.

## Step 4: Generate Django Code

We will create a Django application named `hr` (derived from `Module_HR`).

### 4.1 Models

Task: Create Django models based on the database schema.

**File: `hr/models.py`**

```python
from django.db import models
from datetime import datetime

class FinancialYear(models.Model):
    # This model represents the tblFinancial_master table
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class NewsNotice(models.Model):
    # This model represents the tblHR_News_Notices table
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the primary key in DB
    title = models.CharField(db_column='Title', max_length=255)
    # Storing dates as CharField because the original DB stores them as strings
    fromdate_str = models.CharField(db_column='FromDate', max_length=50)
    todate_str = models.CharField(db_column='ToDate', max_length=50)
    filename = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    # ForeignKey to FinancialYear for the FinYearId lookup
    finyear = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='news_notices')
    compid = models.IntegerField(db_column='CompId') # Company ID filter from session

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblHR_News_Notices'
        verbose_name = 'News and Notice'
        verbose_name_plural = 'News and Notices'
        # Matches the 'order by Id desc' from the original SQL query
        ordering = ['-id']

    def __str__(self):
        return self.title

    # --- Fat Model: Business Logic Methods ---
    @property
    def from_date_display(self):
        """Converts the 'FromDate' string from DB ('DD-MM-YYYY') to a readable format."""
        try:
            # The SQL query formats the date as 'DD-MM-YYYY'
            return datetime.strptime(self.fromdate_str, '%d-%m-%Y').strftime('%d %b %Y')
        except (ValueError, TypeError):
            return self.fromdate_str # Return raw string if parsing fails

    @property
    def to_date_display(self):
        """Converts the 'ToDate' string from DB ('DD-MM-YYYY') to a readable format."""
        try:
            # The SQL query formats the date as 'DD-MM-YYYY'
            return datetime.strptime(self.todate_str, '%d-%m-%Y').strftime('%d %b %Y')
        except (ValueError, TypeError):
            return self.todate_str # Return raw string if parsing fails

    @property
    def is_current(self):
        """Checks if the news item is currently active based on its dates."""
        today = datetime.now().date()
        try:
            from_date = datetime.strptime(self.fromdate_str, '%d-%m-%Y').date()
            to_date = datetime.strptime(self.todate_str, '%d-%m-%Y').date()
            return from_date <= today <= to_date
        except (ValueError, TypeError):
            return False # Cannot determine if active if dates are invalid

    @classmethod
    def get_news_for_company(cls, comp_id):
        """Fetches news and notices for a specific company ID."""
        return cls.objects.filter(compid=comp_id)

```

### 4.2 Forms

Task: Define a Django form for user input.

**File: `hr/forms.py`**

```python
from django import forms
from .models import NewsNotice, FinancialYear

class NewsNoticeForm(forms.ModelForm):
    # If FinYear needs to be selected by the user, provide a dropdown
    finyear = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(),
        empty_label="Select Financial Year",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    class Meta:
        model = NewsNotice
        fields = ['title', 'fromdate_str', 'todate_str', 'filename', 'finyear'] # 'compid' would be set by view/middleware
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fromdate_str': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'}),
            'todate_str': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'}),
            'filename': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'fromdate_str': 'From Date (DD-MM-YYYY)',
            'todate_str': 'To Date (DD-MM-YYYY)',
            'finyear': 'Financial Year',
        }

    def clean(self):
        cleaned_data = super().clean()
        from_date_str = cleaned_data.get('fromdate_str')
        to_date_str = cleaned_data.get('todate_str')

        # Custom validation for date format
        try:
            if from_date_str:
                datetime.strptime(from_date_str, '%d-%m-%Y')
        except ValueError:
            self.add_error('fromdate_str', 'Invalid date format. Please use DD-MM-YYYY.')
        
        try:
            if to_date_str:
                datetime.strptime(to_date_str, '%d-%m-%Y')
        except ValueError:
            self.add_error('todate_str', 'Invalid date format. Please use DD-MM-YYYY.')

        return cleaned_data
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

**File: `hr/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import NewsNotice
from .forms import NewsNoticeForm

# Helper to get current company ID (Placeholder: Replace with actual logic)
# In a real application, this would come from the logged-in user's profile,
# a session variable, or a URL parameter.
def get_current_company_id(request):
    # Example: If company ID is stored in session (similar to ASP.NET's Session["compid"])
    # return request.session.get('compid', 1) # Default to 1 if not found
    # Example: If UserProfile has a company_id field
    # if request.user.is_authenticated:
    #     return request.user.profile.company_id
    return 1 # Defaulting to 1 for demonstration, replace with actual logic.

class NewsNoticeListView(ListView):
    """
    Displays the main page for News and Notices, which will load the table via HTMX.
    """
    model = NewsNotice
    template_name = 'hr/newsnotice/list.html'
    context_object_name = 'newsnotices' # Not directly used for the main list, but good practice

    def get_queryset(self):
        # This queryset will only be used if not using HTMX to load the table.
        # For HTMX, NewsNoticeTablePartialView handles the data.
        comp_id = get_current_company_id(self.request)
        return NewsNotice.get_news_for_company(comp_id)

class NewsNoticeTablePartialView(ListView):
    """
    HTMX-driven view to load the News and Notices table content.
    This replaces the GridView's data binding logic.
    """
    model = NewsNotice
    template_name = 'hr/newsnotice/_newsnotice_table.html'
    context_object_name = 'newsnotices'

    def get_queryset(self):
        # Filter by company ID, similar to original ASP.NET code
        comp_id = get_current_company_id(self.request)
        return NewsNotice.get_news_for_company(comp_id)

    # Views for CRUD operations, kept thin with logic in the model/form
class NewsNoticeCreateView(CreateView):
    model = NewsNotice
    form_class = NewsNoticeForm
    template_name = 'hr/newsnotice/_newsnotice_form.html'
    # No direct success_url for HTMX, as we return 204 or HX-Trigger
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pre-populate compid for new objects based on current company
        # if self.request.method == 'GET':
        #     kwargs['initial'] = {'compid': get_current_company_id(self.request)}
        return kwargs

    def form_valid(self, form):
        # Set compid before saving
        form.instance.compid = get_current_company_id(self.request)
        response = super().form_valid(form)
        messages.success(self.request, 'News & Notice added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList'
                }
            )
        return HttpResponseRedirect(self.get_success_url()) # Fallback for non-HTMX
    
    def get_success_url(self):
        return reverse_lazy('newsnotice_list') # Standard Django redirect

class NewsNoticeUpdateView(UpdateView):
    model = NewsNotice
    form_class = NewsNoticeForm
    template_name = 'hr/newsnotice/_newsnotice_form.html'
    # No direct success_url for HTMX
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'News & Notice updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList'
                }
            )
        return HttpResponseRedirect(self.get_success_url()) # Fallback for non-HTMX
    
    def get_success_url(self):
        return reverse_lazy('newsnotice_list') # Standard Django redirect

class NewsNoticeDeleteView(DeleteView):
    model = NewsNotice
    template_name = 'hr/newsnotice/_newsnotice_confirm_delete.html'
    # No direct success_url for HTMX
    
    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        self.object.delete()
        messages.success(self.request, 'News & Notice deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList'
                }
            )
        return HttpResponseRedirect(self.get_success_url()) # Fallback for non-HTMX
    
    def get_success_url(self):
        return reverse_lazy('newsnotice_list') # Standard Django redirect

```

### 4.4 Templates

Task: Create templates for each view.

**File: `hr/templates/hr/newsnotice/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">News and Notices</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'newsnotice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then remove .hidden from #modal">
            Add New News & Notice
        </button>
    </div>
    
    <div id="newsnoticeTable-container"
         hx-trigger="load, refreshNewsNoticeList from:body"
         hx-get="{% url 'newsnotice_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading News and Notices...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .flex from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed (e.g., for complex UI states within the modal)
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js needed for this simple modal, HTMX handles it.
        // But if more complex client-side state is required, it would go here.
    });

    // Event listener for closing modal on successful form submission (handled by HX-Trigger)
    document.body.addEventListener('refreshNewsNoticeList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
            document.getElementById('modalContent').innerHTML = ''; // Clear modal content
        }
    });

</script>
{% endblock %}
```

**File: `hr/templates/hr/newsnotice/_newsnotice_table.html`**

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="newsnoticeTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Financial Year</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">From Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">To Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for newsnotice in newsnotices %}
            <tr class="hover:bg-gray-50">
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.title }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.finyear.finyear }}</td> {# Accessing related object's field #}
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.from_date_display }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.to_date_display }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_edit' newsnotice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_delete' newsnotice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center text-red-600 font-bold">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables Initialization Script -->
<script>
    $(document).ready(function() {
        $('#newsnoticeTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "ordering": true, // Enable sorting
            "searching": true // Enable searching
        });
    });
</script>
```

**File: `hr/templates/hr/newsnotice/_newsnotice_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} News & Notice</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                Save
                <span id="loadingIndicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

**File: `hr/templates/hr/newsnotice/_newsnotice_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the news item: "{{ newsnotice.title }}"?</p>
    
    <div class="mt-6 flex justify-end space-x-4">
        <button 
            type="button" 
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
            _="on click remove .flex from #modal then add .hidden to #modal">
            Cancel
        </button>
        <button 
            type="button"
            hx-delete="{% url 'newsnotice_delete' newsnotice.pk %}"
            hx-swap="none"
            hx-indicator="#loadingDelete"
            class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
            Delete
            <span id="loadingDelete" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
        </button>
    </div>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

**File: `hr/urls.py`**

```python
from django.urls import path
from .views import (
    NewsNoticeListView, NewsNoticeCreateView, NewsNoticeUpdateView, 
    NewsNoticeDeleteView, NewsNoticeTablePartialView
)

urlpatterns = [
    path('newsnotice/', NewsNoticeListView.as_view(), name='newsnotice_list'),
    path('newsnotice/table/', NewsNoticeTablePartialView.as_view(), name='newsnotice_table'), # For HTMX partial load
    path('newsnotice/add/', NewsNoticeCreateView.as_view(), name='newsnotice_add'),
    path('newsnotice/edit/<int:pk>/', NewsNoticeUpdateView.as_view(), name='newsnotice_edit'),
    path('newsnotice/delete/<int:pk>/', NewsNoticeDeleteView.as_view(), name='newsnotice_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

**File: `hr/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import NewsNotice, FinancialYear
from datetime import datetime
import json

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a financial year for related tests
        FinancialYear.objects.create(finyearid=101, finyear='2023-2024')
        FinancialYear.objects.create(finyearid=102, finyear='2024-2025')

    def test_financial_year_creation(self):
        fy = FinancialYear.objects.get(finyearid=101)
        self.assertEqual(fy.finyear, '2023-2024')
        self.assertEqual(str(fy), '2023-2024')

class NewsNoticeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test financial year
        fy = FinancialYear.objects.create(finyearid=101, finyear='2023-2024')
        # Create test data for NewsNotice
        NewsNotice.objects.create(
            id=1,
            title='Test News Item',
            fromdate_str='01-01-2024',
            todate_str='31-12-2024',
            filename='test_news.pdf',
            finyear=fy,
            compid=1
        )
        NewsNotice.objects.create(
            id=2,
            title='Old News',
            fromdate_str='01-01-2023',
            todate_str='31-01-2023',
            filename='old_news.doc',
            finyear=fy,
            compid=1
        )
        NewsNotice.objects.create(
            id=3,
            title='Another Company News',
            fromdate_str='01-01-2024',
            todate_str='31-12-2024',
            filename='company2_news.pdf',
            finyear=fy,
            compid=2
        )

    def test_news_notice_creation(self):
        news = NewsNotice.objects.get(id=1)
        self.assertEqual(news.title, 'Test News Item')
        self.assertEqual(news.fromdate_str, '01-01-2024')
        self.assertEqual(news.todate_str, '31-12-2024')
        self.assertEqual(news.filename, 'test_news.pdf')
        self.assertEqual(news.finyear.finyear, '2023-2024')
        self.assertEqual(news.compid, 1)

    def test_from_date_display_property(self):
        news = NewsNotice.objects.get(id=1)
        self.assertEqual(news.from_date_display, '01 Jan 2024')

    def test_to_date_display_property(self):
        news = NewsNotice.objects.get(id=1)
        self.assertEqual(news.to_date_display, '31 Dec 2024')

    def test_invalid_date_display_property(self):
        news_invalid_date = NewsNotice.objects.create(
            id=4,
            title='Invalid Date News',
            fromdate_str='invalid-date',
            todate_str='another-invalid',
            filename='invalid.pdf',
            finyear=FinancialYear.objects.get(finyearid=101),
            compid=1
        )
        self.assertEqual(news_invalid_date.from_date_display, 'invalid-date')
        self.assertEqual(news_invalid_date.to_date_display, 'another-invalid')

    def test_is_current_property(self):
        news_active = NewsNotice.objects.get(id=1)
        # Mock datetime.now() for stable testing if current date falls within the range
        # For this test, assuming the current date is within 01-01-2024 and 31-12-2024
        from unittest.mock import patch
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2024, 6, 15)
            mock_datetime.strptime = datetime.strptime # ensure strptime still works
            mock_datetime.date.return_value = datetime(2024, 6, 15).date()
            self.assertTrue(news_active.is_current)
        
        news_inactive = NewsNotice.objects.get(id=2)
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2024, 6, 15)
            mock_datetime.strptime = datetime.strptime
            mock_datetime.date.return_value = datetime(2024, 6, 15).date()
            self.assertFalse(news_inactive.is_current)

    def test_get_news_for_company_method(self):
        company1_news = NewsNotice.get_news_for_company(1)
        self.assertEqual(company1_news.count(), 2)
        self.assertIn(NewsNotice.objects.get(id=1), company1_news)
        self.assertIn(NewsNotice.objects.get(id=2), company1_news)
        self.assertNotIn(NewsNotice.objects.get(id=3), company1_news)

class NewsNoticeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        fy = FinancialYear.objects.create(finyearid=101, finyear='2023-2024')
        NewsNotice.objects.create(
            id=1, title='News 1', fromdate_str='01-01-2024', todate_str='31-12-2024', filename='file1.pdf', finyear=fy, compid=1
        )
        NewsNotice.objects.create(
            id=2, title='News 2', fromdate_str='01-01-2024', todate_str='31-12-2024', filename='file2.pdf', finyear=fy, compid=1
        )
        NewsNotice.objects.create(
            id=3, title='News for Company 2', fromdate_str='01-01-2024', todate_str='31-12-2024', filename='file3.pdf', finyear=fy, compid=2
        )

    def setUp(self):
        self.client = Client()
        # Mock get_current_company_id to always return 1 for these tests
        from unittest.mock import patch
        self.patcher = patch('hr.views.get_current_company_id', return_value=1)
        self.mock_get_company_id = self.patcher.start()
        
    def tearDown(self):
        self.patcher.stop()

    def test_list_view(self):
        response = self.client.get(reverse('newsnotice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/newsnotice/list.html')
        # Check if basic content is present (HTMX will load table later)
        self.assertContains(response, 'News and Notices')
        self.assertContains(response, 'Add New News & Notice')

    def test_table_partial_view(self):
        response = self.client.get(reverse('newsnotice_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/newsnotice/_newsnotice_table.html')
        # Check if data for company 1 is rendered
        self.assertContains(response, 'News 1')
        self.assertContains(response, 'News 2')
        self.assertNotContains(response, 'News for Company 2') # Should be filtered out
        self.assertContains(response, 'id="newsnoticeTable"') # Check DataTables table ID

    def test_create_view_get(self):
        response = self.client.get(reverse('newsnotice_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/newsnotice/_newsnotice_form.html')
        self.assertContains(response, 'Add News & Notice')
        self.assertContains(response, '<form hx-post')

    def test_create_view_post_success(self):
        initial_count = NewsNotice.objects.count()
        data = {
            'title': 'New HTMX News',
            'fromdate_str': '01-06-2024',
            'todate_str': '30-06-2024',
            'filename': 'new_htmx.pdf',
            'finyear': FinancialYear.objects.get(finyearid=101).finyearid # Pass finyearid
        }
        response = self.client.post(reverse('newsnotice_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshNewsNoticeList')
        self.assertEqual(NewsNotice.objects.count(), initial_count + 1)
        self.assertTrue(NewsNotice.objects.filter(title='New HTMX News', compid=1).exists())
    
    def test_create_view_post_invalid(self):
        initial_count = NewsNotice.objects.count()
        data = {
            'title': 'Invalid Date News',
            'fromdate_str': '01/06/2024', # Invalid format
            'todate_str': '30-06-2024',
            'filename': 'invalid.pdf',
            'finyear': FinancialYear.objects.get(finyearid=101).finyearid
        }
        response = self.client.post(reverse('newsnotice_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertContains(response, 'Invalid date format. Please use DD-MM-YYYY.')
        self.assertEqual(NewsNotice.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        news = NewsNotice.objects.get(id=1)
        response = self.client.get(reverse('newsnotice_edit', args=[news.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/newsnotice/_newsnotice_form.html')
        self.assertContains(response, 'Edit News & Notice')
        self.assertContains(response, news.title) # Check if current data is pre-filled

    def test_update_view_post_success(self):
        news = NewsNotice.objects.get(id=1)
        new_title = 'Updated News Title'
        data = {
            'title': new_title,
            'fromdate_str': news.fromdate_str,
            'todate_str': news.todate_str,
            'filename': news.filename,
            'finyear': news.finyear.finyearid
        }
        response = self.client.post(reverse('newsnotice_edit', args=[news.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshNewsNoticeList')
        news.refresh_from_db()
        self.assertEqual(news.title, new_title)
        
    def test_delete_view_get(self):
        news = NewsNotice.objects.get(id=1)
        response = self.client.get(reverse('newsnotice_delete', args=[news.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/newsnotice/_newsnotice_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, news.title)

    def test_delete_view_post_success(self):
        news_to_delete = NewsNotice.objects.get(id=1)
        response = self.client.delete(reverse('newsnotice_delete', args=[news_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshNewsNoticeList')
        self.assertFalse(NewsNotice.objects.filter(id=1).exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for Dynamic Content:**
    *   The `list.html` template uses `hx-get="{% url 'newsnotice_table' %}"` to initially load and `hx-trigger="load, refreshNewsNoticeList from:body"` to dynamically refresh the table via the `NewsNoticeTablePartialView`. This completely replaces the ASP.NET GridView's server-side data binding and pagination.
    *   Add/Edit/Delete buttons use `hx-get` to load forms into the modal (`#modalContent`).
    *   Form submissions (`hx-post`, `hx-delete`) are handled by HTMX, which prevents full page reloads.
    *   `HX-Trigger: 'refreshNewsNoticeList'` header is sent on successful form submissions (Create, Update, Delete) to signal `list.html` to reload the table.
    *   HTMX indicators (`hx-indicator`) are used on submit buttons to show loading states.
*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses `_=` attributes (Alpine.js's imperative helper) to control its visibility.
        *   `on click add .flex to #modal then remove .hidden from #modal` opens the modal.
        *   `on click if event.target.id == 'modal' remove .flex from me then add .hidden to me` closes the modal if the overlay is clicked.
    *   The `refreshNewsNoticeList` event listener in `extra_js` closes the modal and clears its content after a successful HTMX operation, ensuring a clean state for subsequent interactions.
*   **DataTables for List Views:**
    *   The `_newsnotice_table.html` partial contains the `<table id="newsnoticeTable">`.
    *   A `<script>` block within this partial initializes DataTables on the `newsnoticeTable` ID, enabling client-side searching, sorting, and pagination. This eliminates the need for ASP.NET's server-side paging logic.
    *   CDN links for DataTables, HTMX, and Alpine.js are assumed to be present in `core/base.html`.
*   **No Custom JavaScript:** Beyond the DataTables initialization and simple Alpine.js for modal toggling, no complex custom JavaScript is required, adhering to the HTMX-first principle.
*   **DRY Template Inheritance:** All templates correctly extend `core/base.html` or are partials (`_*.html`) designed for HTMX insertion, preventing code duplication.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the News and Notices functionality from ASP.NET to Django. It emphasizes automated approaches by defining clear structures that can be generated and managed programmatically. The focus on `Fat Models`, `Thin Views`, `HTMX`, `Alpine.js`, and `DataTables` ensures a modern, efficient, and user-friendly solution that significantly reduces development and maintenance overhead compared to the legacy ASP.NET application. The structured approach facilitates understanding for non-technical stakeholders and enables effective oversight of the modernization process.