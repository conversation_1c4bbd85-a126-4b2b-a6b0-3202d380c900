## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables. The primary table is `tblHR_OfficeStaff`, and it uses several lookup tables for related information such as financial year, department, business group, and designation.

*   **Primary Table:** `tblHR_OfficeStaff`
    *   **Columns:** `UserID` (likely primary key), `EmpId`, `Title`, `EmployeeName`, `FinYearId` (Foreign Key), `JoiningDate`, `Department` (Foreign Key), `MobileNo` (Foreign Key, though commented out in ASP.NET), `ResignationDate`, `BGGroup` (Foreign Key), `Designation` (Foreign Key), `CompId`.
*   **Lookup Tables:**
    *   `tblFinancial_master`: `FinYearId` (Primary Key), `FinYear`. Used for financial year information.
    *   `tblHR_Departments`: `Id` (Primary Key), `Description`. Used for department names.
    *   `BusinessGroup`: `Id` (Primary Key), `Symbol`. Used for business group names.
    *   `tblHR_Designation`: `Id` (Primary Key), `Symbol`, `Type`. Used for designation names.
    *   `tblHR_CoporateMobileNo`: `Id` (Primary Key), `MobileNo`. (Not actively used in the provided C# but present in ASPX comments).

### Step 2: Identify Backend Functionality

The ASP.NET page `Salary_New.aspx` primarily serves as a search and listing interface for employee salary information.

*   **Read (Display List):** The core functionality is to display a paginated list of employee records from `tblHR_OfficeStaff` in a `GridView`. This list includes details like Employee Name, Department Name, BG Group, and Designation, which are pulled from related lookup tables.
*   **Search/Filter:** Users can search for employees based on:
    *   Employee Name (with an autocomplete feature).
    *   Department Name.
    *   Business Group.
    The search criteria dynamically adjust the data displayed.
*   **Pagination:** The `GridView` supports pagination.
*   **Autocomplete (Employee Name):** A server-side method (`GetCompletionList`) provides suggestions for employee names as the user types.
*   **Navigation:** A "Select" hyperlink for each employee leads to a `Salary_New_Details.aspx` page, implying detailed CRUD operations might be performed there. For this modernization plan, we will assume generic CRUD operations for the `OfficeStaff` model on the main page for completeness, as per instructions.

### Step 3: Infer UI Components

The ASP.NET page uses standard Web Forms controls:

*   **Dropdown (`DrpField`):** For selecting the search criteria type (Employee Name, Department, BG Group).
*   **Text Boxes (`TxtMrs`, `TxtEmpName`):** For entering search values. Their visibility is toggled based on the selected search field. `TxtEmpName` has an `AutoCompleteExtender`.
*   **Button (`Button1`):** Triggers the search operation.
*   **Data Grid (`GridView2`):** Displays the tabular data, supports pagination, and has a "Select" column to navigate to details.
*   **Label (`Label2`):** For displaying messages to the user.

### Step 4: Generate Django Code

We will create a Django application, let's call it `hr`, to house these components.

#### 4.1 Models (`hr/models.py`)

We will define Django models for the primary table (`tblHR_OfficeStaff`) and the lookup tables (`tblFinancial_master`, `tblHR_Departments`, `BusinessGroup`, `tblHR_Designation`). We will use `managed = False` and `db_table` to map them directly to the existing database tables. The `OfficeStaff` model will include a class method for optimized data retrieval, addressing the N+1 query issue seen in the original C# code.

```python
from django.db import models
from django.db.models import Q # Used for complex lookups

class FinancialYear(models.Model):
    # Assuming FinYearId is the primary key as it's used to lookup FinYear
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class Department(models.Model):
    # Assuming Id is the primary key
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class BusinessGroup(models.Model):
    # Assuming Id is the primary key
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Designation(models.Model):
    # Assuming Id is the primary key
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    type = models.CharField(db_column='Type', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.symbol} - {self.type}" if self.type else self.symbol

class OfficeStaff(models.Model):
    # UserID is likely the primary key for tblHR_OfficeStaff
    userid = models.IntegerField(db_column='UserID', primary_key=True) 
    empid = models.CharField(db_column='EmpId', max_length=50, unique=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255)
    # Using ForeignKey for related tables to enable select_related
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='staff_members')
    joiningdate = models.DateField(db_column='JoiningDate', blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.DO_NOTHING, db_column='Department', blank=True, null=True, related_name='staff_members')
    # MobileNo would be a ForeignKey to tblHR_CoporateMobileNo if used
    resignationdate = models.DateField(db_column='ResignationDate', blank=True, null=True)
    bggroup = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', blank=True, null=True, related_name='staff_members')
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation', blank=True, null=True, related_name='staff_members')
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff Member'
        verbose_name_plural = 'Office Staff Members'

    def __str__(self):
        return f"{self.title or ''} {self.employeename}".strip()

    @classmethod
    def get_filtered_staff(cls, comp_id, fy_id, search_field, search_value):
        """
        Retrieves office staff members based on search criteria.
        Optimized with select_related to avoid N+1 queries by joining lookup tables.
        The original ASP.NET code joined these in a loop, leading to many database calls.
        """
        # Start with all active employees for the company and financial year
        # Assuming ResignationDate='' in ASP.NET means NULL in DB for not resigned
        staff_query = cls.objects.filter(
            compid=comp_id,
            finyearid__finyearid__lte=fy_id, # Filter by financial year ID
            resignationdate__isnull=True # Assumes NULL for no resignation
        ).exclude(userid=1).order_by('-userid').select_related(
            'finyearid', 'department', 'bggroup', 'designation'
        )

        if search_value:
            if search_field == '0': # Employee Name
                # The ASP.NET fun.getCode(TxtEmpName.Text) suggests EmpId is part of the text,
                # like "Name [EmpId]". We'll parse this for exact EmpId search.
                import re
                match = re.search(r'\[(.*?)\]$', search_value)
                if match:
                    emp_id_code = match.group(1)
                    staff_query = staff_query.filter(empid=emp_id_code)
                else: # Fallback: if no ID, search by name partial match
                    staff_query = staff_query.filter(
                        Q(employeename__icontains=search_value) | Q(title__icontains=search_value)
                    )
            elif search_field == '1': # Dept Name
                staff_query = staff_query.filter(department__description__icontains=search_value)
            elif search_field == '2': # BG Group
                staff_query = staff_query.filter(bggroup__symbol__icontains=search_value)
        
        return staff_query

    @classmethod
    def get_employee_autocomplete_list(cls, prefix_text, comp_id):
        """
        Provides a list of employee names and IDs for autocomplete, filtered by company.
        """
        employees = cls.objects.filter(
            compid=comp_id,
            resignationdate__isnull=True, # Active employees
            Q(employeename__icontains=prefix_text) | Q(title__icontains=prefix_text) # Case-insensitive partial match
        ).values('empid', 'employeename', 'title').order_by('employeename')
        
        results = []
        for emp in employees:
            full_name = f"{emp['title'] or ''} {emp['employeename']}".strip()
            results.append(f"{full_name} [{emp['empid']}]")
        
        return sorted(results) # Sort the results for consistent autocomplete behavior
```

#### 4.2 Forms (`hr/forms.py`)

We will create two forms: `OfficeStaffForm` for typical CRUD operations (even though the original code doesn't show them, it's good practice for the detail page) and `OfficeStaffSearchForm` to manage the search fields and their values.

```python
from django import forms
from .models import OfficeStaff, Department, BusinessGroup, FinancialYear, Designation

class OfficeStaffForm(forms.ModelForm):
    class Meta:
        model = OfficeStaff
        # Include relevant fields for a hypothetical add/edit form
        fields = ['empid', 'title', 'employeename', 'finyearid', 'joiningdate', 
                  'department', 'bggroup', 'designation', 'resignationdate', 'compid']
        widgets = {
            'empid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employeename': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finyearid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'joiningdate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bggroup': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'designation': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'resignationdate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'empid': 'Employee ID',
            'employeename': 'Employee Name',
            'finyearid': 'Financial Year',
            'joiningdate': 'Joining Date',
            'department': 'Department Name',
            'bggroup': 'BG Group',
            'designation': 'Designation',
            'resignationdate': 'Resignation Date',
            'compid': 'Company ID',
        }

class OfficeStaffSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'Dept Name'),
        ('2', 'BG Group'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={'class': 'block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        initial='0',
        label="" # Hide label as it's part of the visual layout
    )
    search_text_mrs = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter search value', 'x-show': "searchField !== '0'", 'x-model': 'searchTextMrs'})
    )
    search_text_empname = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Employee Name', 'x-show': "searchField === '0'", 'x-model': 'searchTextEmpName', 'hx-get': "/hr/autocomplete_employees/", 'hx-trigger': "keyup changed delay:500ms", 'hx-target': "#autocomplete-results", 'hx-indicator': "#autocomplete-spinner"}),
    )
    
    # Hidden input to store the actual selected employee name (from autocomplete)
    # This is for form submission, the visible input is for user typing and HTMX
    selected_emp_id = forms.CharField(widget=forms.HiddenInput(), required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate choices for dropdowns from models if they were not ForeignKeys in OfficeStaff
        # For ForeignKeys, Django automatically handles choices.
        # This form is for search, so it doesn't need to bind to an OfficeStaff instance.
```

#### 4.3 Views (`hr/views.py`)

We will use Django Class-Based Views (CBVs) for the list and hypothetical CRUD operations, along with a custom view for the HTMX-powered DataTables partial and autocomplete. User session data (compid, finyear) will be retrieved from the request.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, get_object_or_404
from .models import OfficeStaff
from .forms import OfficeStaffForm, OfficeStaffSearchForm
import json
import re # For parsing EmpId from autocomplete string

class OfficeStaffListView(ListView):
    model = OfficeStaff
    template_name = 'hr/officestaff/list.html'
    context_object_name = 'officestaff_list' # Renamed for clarity
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        # Fetch current user's company ID and financial year ID from session
        # Assume session keys 'compid' and 'finyear' are set similar to ASP.NET
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not found
        fy_id = self.request.session.get('finyear', 2024) # Default to 2024 if not found (assuming numeric year)

        # Get search parameters from GET request
        search_field = self.request.GET.get('search_field', '0')
        search_value = self.request.GET.get('search_text_mrs')
        search_emp_name = self.request.GET.get('search_text_empname')
        
        # Use the correct search value based on search_field
        if search_field == '0':
            # For employee name search, use search_emp_name field
            search_value_to_use = search_emp_name
        else:
            # For department/BG group, use search_text_mrs
            search_value_to_use = search_value
        
        # Call the model's fat method to get filtered staff
        return OfficeStaff.get_filtered_staff(comp_id, fy_id, search_field, search_value_to_use)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the search form to the template
        initial_search_field = self.request.GET.get('search_field', '0')
        initial_search_text_mrs = self.request.GET.get('search_text_mrs', '')
        initial_search_text_empname = self.request.GET.get('search_text_empname', '')
        context['search_form'] = OfficeStaffSearchForm(initial={
            'search_field': initial_search_field,
            'search_text_mrs': initial_search_text_mrs,
            'search_text_empname': initial_search_text_empname,
        })
        # Pass the message from query string (if any), similar to ASP.NET Label2
        if 'msg' in self.request.GET:
            messages.info(self.request, self.request.GET['msg'])

        return context

# This view is for rendering the DataTables partial via HTMX
class OfficeStaffTablePartialView(OfficeStaffListView):
    template_name = 'hr/officestaff/_officestaff_table.html'

    def get(self, request, *args, **kwargs):
        # The parent ListView's get_queryset and get_context_data will be used
        # We just need to render the partial template
        self.object_list = self.get_queryset()
        context = self.get_context_data()
        return render(request, self.template_name, context)

class OfficeStaffCreateView(CreateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr/officestaff/form.html'
    success_url = reverse_lazy('officestaff_list') # Redirect to list view after success

    def get_initial(self):
        initial = super().get_initial()
        # Set default values for compid and finyearid if required during creation
        initial['compid'] = self.request.session.get('compid', 1)
        initial['finyearid'] = self.request.session.get('finyear', 2024) # Assuming FyId is just the year
        return initial

    def form_valid(self, form):
        # Example of business logic in model (though this form is generic, for a complex form, logic would go to model)
        response = super().form_valid(form)
        messages.success(self.request, 'Office Staff member added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshOfficeStaffList': None,
                        'showMessage': {'level': 'success', 'message': 'Office Staff member added successfully.'}
                    })
                }
            )
        return response

class OfficeStaffUpdateView(UpdateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr/officestaff/form.html'
    success_url = reverse_lazy('officestaff_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Office Staff member updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshOfficeStaffList': None,
                        'showMessage': {'level': 'success', 'message': 'Office Staff member updated successfully.'}
                    })
                }
            )
        return response

class OfficeStaffDeleteView(DeleteView):
    model = OfficeStaff
    template_name = 'hr/officestaff/confirm_delete.html'
    success_url = reverse_lazy('officestaff_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Office Staff member deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshOfficeStaffList': None,
                        'showMessage': {'level': 'success', 'message': 'Office Staff member deleted successfully.'}
                    })
                }
            )
        return response

class EmployeeAutocompleteView(View):
    """
    Handles the autocomplete suggestions for employee names.
    This replaces the ASP.NET GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        comp_id = request.session.get('compid', 1) # Get compid from session

        if prefix_text:
            suggestions = OfficeStaff.get_employee_autocomplete_list(prefix_text, comp_id)
            return JsonResponse(suggestions, safe=False)
        return JsonResponse([], safe=False)
```

#### 4.4 Templates (`hr/templates/hr/officestaff/`)

**`list.html` (Main List Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Payroll - New</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'officestaff_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Staff Member
        </button>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{ searchField: '{{ search_form.search_field.value|default:'0' }}', searchTextMrs: '{{ search_form.search_text_mrs.value|default:'' }}', searchTextEmpName: '{{ search_form.search_text_empname.value|default:'' }}' }">
        <form hx-get="{% url 'officestaff_table' %}" hx-target="#officestaffTable-container" hx-indicator="#loading-spinner" hx-swap="innerHTML">
            <div class="flex items-end space-x-4 mb-4">
                <div class="flex-grow">
                    <label for="{{ search_form.search_field.id_for_label }}" class="sr-only">Search By:</label>
                    {{ search_form.search_field|safe }}
                </div>
                
                <div class="flex-grow relative">
                    {{ search_form.search_text_mrs|safe }}
                    {{ search_form.search_text_empname|safe }}
                    
                    <div x-show="searchField === '0' && searchTextEmpName.length > 0" id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto">
                        <!-- Autocomplete suggestions will be loaded here via HTMX -->
                    </div>
                </div>
                
                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
            </div>
            <div class="text-center" id="loading-spinner" style="display: none;">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Searching...</p>
            </div>
        </form>
    </div>

    <div id="officestaffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body"
         hx-get="{% url 'officestaff_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Staff Members...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showMessage: false, message: '', level: '' }"
         @showMessage.window="showMessage = true; message = $event.detail.message; level = $event.detail.level; setTimeout(() => showMessage = false, 3000)">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full">
            <!-- Modal content loaded via HTMX -->
        </div>
        <!-- Toast Notification Area -->
        <template x-if="showMessage">
            <div :class="{ 'bg-green-500': level === 'success', 'bg-red-500': level === 'error', 'bg-blue-500': level === 'info' }"
                 class="fixed bottom-4 right-4 text-white px-4 py-2 rounded-md shadow-lg z-50 transition-transform transform translate-x-0"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-x-full"
                 x-transition:enter-end="opacity-100 translate-x-0"
                 x-transition:leave="transition ease-in duration-300"
                 x-transition:leave-start="opacity-100 translate-x-0"
                 x-transition:leave-end="opacity-0 translate-x-full">
                <span x-text="message"></span>
            </div>
        </template>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('officestaffList', () => ({
            searchField: '{{ search_form.search_field.value|default:'0' }}',
            searchTextMrs: '{{ search_form.search_text_mrs.value|default:'' }}',
            searchTextEmpName: '{{ search_form.search_text_empname.value|default:'' }}',
            selectedEmpId: '', // To hold the EmpId from autocomplete selection

            init() {
                // Initialize text box visibility based on initial searchField value
                this.$watch('searchField', value => {
                    if (value === '0') {
                        this.searchTextMrs = ''; // Clear other search box when switching to emp name
                    } else {
                        this.searchTextEmpName = ''; // Clear emp name search box when switching
                    }
                });

                // HTMX events for autocomplete selection
                this.$el.addEventListener('htmx:afterSwap', (event) => {
                    if (event.detail.target.id === 'autocomplete-results') {
                        // After autocomplete results load, attach click listener to each suggestion
                        event.detail.target.querySelectorAll('div').forEach(item => {
                            item.addEventListener('click', () => {
                                this.searchTextEmpName = item.innerText.trim();
                                this.selectedEmpId = item.dataset.empid; // Store the ID
                                event.detail.target.innerHTML = ''; // Clear suggestions
                            });
                        });
                    }
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`_officestaff_table.html` (Partial for DataTable)**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="officestaffTable" class="min-w-full bg-white text-sm">
        <thead>
            <tr>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Emp Id</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Emp Name</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Dept Name</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">BG Group</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in officestaff_list %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.empid }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.get_full_name }}</td> {# Assumes get_full_name method in model #}
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.department.description }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.bggroup.symbol }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.designation }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.finyearid.finyear }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-center whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-1"
                        hx-get="{% url 'officestaff_edit' obj.userid %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'officestaff_delete' obj.userid %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the table content
    $(document).ready(function() {
        $('#officestaffTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "pagingType": "full_numbers", // Enhanced pagination
            "responsive": true, // Make table responsive
            "searching": true, // Enable local searching (Django handles server-side filtering)
            "info": true, // Show table info
            "ordering": true // Enable ordering
        });
    });
</script>
```

**`_officestaff_form.html` (Partial for Add/Edit Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Office Staff Member</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.successful) { const modal = document.getElementById('modal'); modal.classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_officestaff_confirm_delete.html` (Partial for Delete Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the staff member: <strong>{{ object.employeename }} ({{ object.empid }})</strong>?</p>
    
    <form hx-post="{% url 'officestaff_delete' object.userid %}" hx-swap="none" hx-on::after-request="if(event.detail.successful) { const modal = document.getElementById('modal'); modal.classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`_employee_autocomplete_results.html` (Partial for Autocomplete)**

```html
{% for suggestion in suggestions %}
    <div class="cursor-pointer px-4 py-2 hover:bg-gray-100" data-empid="{{ suggestion|slice:'-1,'|slice:'1:-1' }}"> {# Extracts EmpId from "[EmpId]" #}
        {{ suggestion }}
    </div>
{% empty %}
    <div class="px-4 py-2 text-gray-500">No matches found.</div>
{% endfor %}
```

#### 4.5 URLs (`hr/urls.py`)

This file defines the URL patterns that map to our Django views.

```python
from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffCreateView, OfficeStaffUpdateView, OfficeStaffDeleteView,
    OfficeStaffTablePartialView, EmployeeAutocompleteView
)

urlpatterns = [
    path('officestaff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('officestaff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('officestaff/edit/<int:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    path('officestaff/delete/<int:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'),
    # HTMX-specific endpoints
    path('officestaff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    path('autocomplete_employees/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]
```

#### 4.6 Tests (`hr/tests.py`)

Comprehensive tests cover model methods and view functionality, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import OfficeStaff, FinancialYear, Department, BusinessGroup, Designation
import json
from datetime import date

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent lookup data
        cls.fin_year = FinancialYear.objects.create(finyearid=2024, finyear='2024-2025')
        cls.department = Department.objects.create(id=1, description='HR Dept')
        cls.biz_group = BusinessGroup.objects.create(id=1, symbol='CORP')
        cls.designation = Designation.objects.create(id=1, symbol='MGR', type='Manager')

        # Create test data for OfficeStaff
        cls.staff1 = OfficeStaff.objects.create(
            userid=101,
            empid='EMP001',
            title='Mr.',
            employeename='John Doe',
            finyearid=cls.fin_year,
            joiningdate=date(2020, 1, 1),
            department=cls.department,
            bggroup=cls.biz_group,
            designation=cls.designation,
            resignationdate=None, # Not resigned
            compid=1
        )
        cls.staff2 = OfficeStaff.objects.create(
            userid=102,
            empid='EMP002',
            title='Ms.',
            employeename='Jane Smith',
            finyearid=cls.fin_year,
            joiningdate=date(2021, 2, 1),
            department=cls.department,
            bggroup=cls.biz_group,
            designation=cls.designation,
            resignationdate=date(2023, 12, 31), # Resigned
            compid=1
        )
        cls.staff3 = OfficeStaff.objects.create(
            userid=103,
            empid='EMP003',
            title='Dr.',
            employeename='Alice Brown',
            finyearid=cls.fin_year,
            joiningdate=date(2022, 3, 1),
            department=cls.department,
            bggroup=cls.biz_group,
            designation=cls.designation,
            resignationdate=None,
            compid=2 # Different company
        )
        # Excluded UserID from ASP.NET code
        OfficeStaff.objects.create(
            userid=1,
            empid='SYS001',
            title='',
            employeename='System User',
            finyearid=cls.fin_year,
            joiningdate=date(2000, 1, 1),
            department=cls.department,
            bggroup=cls.biz_group,
            designation=cls.designation,
            resignationdate=None,
            compid=1
        )

    def test_officestaff_creation(self):
        self.assertEqual(self.staff1.employeename, 'John Doe')
        self.assertEqual(self.staff1.empid, 'EMP001')
        self.assertEqual(self.staff1.department.description, 'HR Dept')
        self.assertIsNone(self.staff1.resignationdate)

    def test_get_filtered_staff_no_search(self):
        # Only active staff for comp_id=1, excluding userid=1
        queryset = OfficeStaff.get_filtered_staff(comp_id=1, fy_id=2024, search_field='', search_value='')
        self.assertIn(self.staff1, queryset)
        self.assertNotIn(self.staff2, queryset) # Resigned
        self.assertNotIn(self.staff3, queryset) # Different company
        self.assertEqual(queryset.count(), 1) # Only staff1

    def test_get_filtered_staff_by_employee_name(self):
        queryset = OfficeStaff.get_filtered_staff(comp_id=1, fy_id=2024, search_field='0', search_value='John Doe [EMP001]')
        self.assertIn(self.staff1, queryset)
        self.assertEqual(queryset.count(), 1)

        # Test partial name search
        queryset_partial = OfficeStaff.get_filtered_staff(comp_id=1, fy_id=2024, search_field='0', search_value='john')
        self.assertIn(self.staff1, queryset_partial)
        self.assertEqual(queryset_partial.count(), 1)

    def test_get_filtered_staff_by_department_name(self):
        queryset = OfficeStaff.get_filtered_staff(comp_id=1, fy_id=2024, search_field='1', search_value='HR')
        self.assertIn(self.staff1, queryset)
        self.assertEqual(queryset.count(), 1)

    def test_get_filtered_staff_by_bg_group(self):
        queryset = OfficeStaff.get_filtered_staff(comp_id=1, fy_id=2024, search_field='2', search_value='CORP')
        self.assertIn(self.staff1, queryset)
        self.assertEqual(queryset.count(), 1)

    def test_get_employee_autocomplete_list(self):
        suggestions = OfficeStaff.get_employee_autocomplete_list(prefix_text='jo', comp_id=1)
        self.assertIn('Mr. John Doe [EMP001]', suggestions)
        self.assertNotIn('Ms. Jane Smith [EMP002]', suggestions) # Jane is resigned
        self.assertNotIn('Dr. Alice Brown [EMP003]', suggestions) # Different company
        self.assertEqual(len(suggestions), 1)

class OfficeStaffViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent lookup data
        cls.fin_year = FinancialYear.objects.create(finyearid=2024, finyear='2024-2025')
        cls.department = Department.objects.create(id=1, description='HR Dept')
        cls.biz_group = BusinessGroup.objects.create(id=1, symbol='CORP')
        cls.designation = Designation.objects.create(id=1, symbol='MGR', type='Manager')

        cls.staff1 = OfficeStaff.objects.create(
            userid=101,
            empid='EMP001',
            title='Mr.',
            employeename='John Doe',
            finyearid=cls.fin_year,
            joiningdate=date(2020, 1, 1),
            department=cls.department,
            bggroup=cls.biz_group,
            designation=cls.designation,
            resignationdate=None,
            compid=1
        )
        cls.staff_sys = OfficeStaff.objects.create(
            userid=1,
            empid='SYS001',
            title='',
            employeename='System User',
            finyearid=cls.fin_year,
            joiningdate=date(2000, 1, 1),
            department=cls.department,
            bggroup=cls.biz_group,
            designation=cls.designation,
            resignationdate=None,
            compid=1
        )

    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024 # Or a numeric year like 2024
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('officestaff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/list.html')
        self.assertIn('officestaff_list', response.context)
        self.assertEqual(response.context['officestaff_list'].count(), 1) # Only staff1 should be active and visible

    def test_list_view_get_with_search(self):
        response = self.client.get(reverse('officestaff_list'), {'search_field': '0', 'search_text_empname': 'John Doe [EMP001]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['officestaff_list'].count(), 1)
        self.assertEqual(response.context['officestaff_list'].first(), self.staff1)

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('officestaff_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/_officestaff_table.html')
        self.assertIn('officestaff_list', response.context)
        self.assertEqual(response.context['officestaff_list'].count(), 1)

    def test_create_view_get(self):
        response = self.client.get(reverse('officestaff_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_htmx(self):
        data = {
            'userid': 104,
            'empid': 'EMP004',
            'title': 'Eng.',
            'employeename': 'Robert Green',
            'finyearid': self.fin_year.finyearid,
            'joiningdate': '2023-05-15',
            'department': self.department.id,
            'bggroup': self.biz_group.id,
            'designation': self.designation.id,
            'resignationdate': '',
            'compid': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('officestaff_add'), data, headers=headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX
        self.assertTrue(OfficeStaff.objects.filter(empid='EMP004').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 0) # Messages not directly in 204, but via HX-Trigger

    def test_update_view_get(self):
        response = self.client.get(reverse('officestaff_edit', args=[self.staff1.userid]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.staff1)

    def test_update_view_post_htmx(self):
        updated_name = 'Johnny Doe'
        data = {
            'userid': self.staff1.userid,
            'empid': self.staff1.empid,
            'title': self.staff1.title,
            'employeename': updated_name,
            'finyearid': self.staff1.finyearid.finyearid,
            'joiningdate': self.staff1.joiningdate.isoformat(),
            'department': self.staff1.department.id,
            'bggroup': self.staff1.bggroup.id,
            'designation': self.staff1.designation.id,
            'resignationdate': '',
            'compid': self.staff1.compid
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('officestaff_edit', args=[self.staff1.userid]), data, headers=headers)
        self.assertEqual(response.status_code, 204)
        self.staff1.refresh_from_db()
        self.assertEqual(self.staff1.employeename, updated_name)

    def test_delete_view_get(self):
        response = self.client.get(reverse('officestaff_delete', args=[self.staff1.userid]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.staff1)

    def test_delete_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('officestaff_delete', args=[self.staff1.userid]), headers=headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(OfficeStaff.objects.filter(userid=self.staff1.userid).exists())

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'john'})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('Mr. John Doe [EMP001]', data)
        self.assertEqual(len(data), 1)

        response_empty = self.client.get(reverse('employee_autocomplete'), {'q': ''})
        self.assertEqual(response_empty.status_code, 200)
        self.assertEqual(response_empty.json(), [])

        response_no_match = self.client.get(reverse('employee_autocomplete'), {'q': 'xyz'})
        self.assertEqual(response_no_match.status_code, 200)
        self.assertEqual(response_no_match.json(), [])
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic updates:**
    *   The search form submits via `hx-get` to `{% url 'officestaff_table' %}` which updates only the `officestaffTable-container` div, eliminating full page reloads for search results.
    *   Add/Edit/Delete buttons use `hx-get` to load forms into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) return `204 No Content` along with `HX-Trigger` headers to refresh the main table (`refreshOfficeStaffList`) and display a success message via Alpine.js.
    *   Autocomplete is handled by `hx-get` on the employee name input, targeting `autocomplete-results`.
*   **Alpine.js for UI state management:**
    *   Used to manage the visibility of the two search textboxes (`TxtMrs` and `TxtEmpName`) based on the `DrpField` selection, mimicking the ASP.NET `DrpField_SelectedIndexChanged` behavior.
    *   Manages the modal's `is-active` class for showing/hiding.
    *   Handles dynamic toast notifications triggered by HTMX `HX-Trigger` events.
    *   Manages autocomplete selection, populating the input and storing the selected `EmpId`.
*   **DataTables for list views:**
    *   The `_officestaff_table.html` partial includes the JavaScript initialization for DataTables. It will automatically handle client-side sorting, searching (within the current loaded page), and pagination based on the data provided by the Django view. For larger datasets, server-side processing with DataTables could be integrated, but for the original `PageSize=20` and typical usage, client-side is often sufficient.
*   **DRY Template Inheritance:** All templates extend `core/base.html`, ensuring consistent layout and shared resources (like CDN links for DataTables, HTMX, Alpine.js, and Tailwind CSS).

This comprehensive plan provides a systematic approach to modernize the ASP.NET application to a robust Django-based solution, leveraging modern frontend techniques and optimizing backend performance.