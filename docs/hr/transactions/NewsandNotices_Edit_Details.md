## ASP.NET to Django Conversion Script: News and Notices Module

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

Instructions:
The ASP.NET code interacts with a SQL Server database. Based on the `fun.select` and `fun.update` calls, the primary table involved is `tblHR_News_Notices`.

Identified Table and Columns:
- **Table Name:** `tblHR_News_Notices`

- **Columns:**
    - `Id`: Primary Key, Integer (used for identifying individual records)
    - `Title`: Text (mapped from `TxtNewsTitle`)
    - `InDetails`: Text (mapped from `txtNews`, for description)
    - `FromDate`: Date (mapped from `TxtFromDate`, stored as formatted string in ASP.NET, will be `DateField` in Django)
    - `ToDate`: Date (mapped from `TxtToDate`, stored as formatted string in ASP.NET, will be `DateField` in Django)
    - `FileName`: Text (name of the uploaded file)
    - `FileSize`: Integer (size of the uploaded file in bytes)
    - `ContentType`: Text (MIME type of the uploaded file)
    - `FileData`: Binary Data (the actual content of the uploaded file, stored as BLOB)
    - `SysDate`: Date (system date of last update)
    - `SysTime`: Time (system time of last update)
    - `SessionId`: Text (identifies the user session/username who made the change)
    - `CompId`: Integer (company ID, from `Session["compid"]`)
    - `FinYearId`: Integer (financial year ID, from `Session["finyear"]`)
    - `Flag`: Integer (likely 0 or 1, indicating active/inactive based on `ToDate`)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

Instructions:
The ASP.NET page `NewsandNotices_Edit_Details.aspx` primarily handles the **Update** operation for an existing news or notice entry. It also supports **Reading** a single record and a specific **File Management** action.

-   **Read (R):**
    -   The `Page_Load` method, specifically within the `if (!IsPostBack)` block, performs a `SELECT` query to retrieve data for a specific `Id` and `CompId` from `tblHR_News_Notices`. This data is then used to populate the form fields (`TxtNewsTitle`, `txtNews`, `TxtFromDate`, `TxtToDate`) and to determine the visibility of file-related controls (`HyperLink1`, `ImageCross`, `FileUpload1`).

-   **Update (U):**
    -   The `BtnUpload_Click` method is triggered when the "Update" button is pressed. It constructs an `UPDATE` SQL command to modify existing news details (`Title`, `InDetails`, `FromDate`, `ToDate`, `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `Flag`).
    -   Crucially, it also handles file uploads. If a file is selected via `FileUpload1`, it reads the file's content, name, size, and content type, then performs a *second* `UPDATE` operation to store this file data directly in the database columns (`FileName`, `FileSize`, `ContentType`, `FileData`).

-   **File Deletion (Partial Update):**
    -   The `ImageCross_Click` method handles clearing the associated file. It performs an `UPDATE` operation that sets `FileName`, `FileSize`, and `ContentType` columns to empty or zero values for the specific record. This effectively "deletes" the file association from the database record without deleting the entire news item.

-   **Validation Logic:**
    -   ASP.NET `RequiredFieldValidator` and `RegularExpressionValidator` controls are used to ensure that title, description, and dates are present and in the correct format before submission. This will be replicated in Django forms.
    -   A `Flag` is set based on comparing the current date to the `ToDate`, indicating if the news item is active/expired. This business logic will be moved to the Django model.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

Instructions:
The ASP.NET page uses standard Web Forms controls for user input and interaction, arranged within HTML tables for layout.

-   **Text Input:**
    -   `TxtNewsTitle` (TextBox): For the news headline/title.
    -   `txtNews` (TextBox with `TextMode="MultiLine"`): For the detailed description of the news.

-   **Date Input with Calendar:**
    -   `TxtFromDate` (TextBox) and `TxtToDate` (TextBox): For specifying the display date range. These are enhanced with `AjaxControlToolkit.CalendarExtender` to provide a calendar pop-up, which will translate to a modern date picker (e.g., HTML5 `type="date"` or a simple JavaScript/HTMX based one).

-   **File Management:**
    -   `HyperLink1`: Displays the name of an already uploaded file, acting as a clickable link (presumably for download).
    -   `ImageCross` (ImageButton): A small image button to "cross out" or remove the currently attached file.
    -   `FileUpload1` (FileUpload): Allows users to select and upload a new file.

-   **Action Buttons:**
    -   `BtnUpload` (Button): Labeled "Update", triggers the form submission to save changes. Includes client-side `confirmationUpdate()` JavaScript.
    -   `BtnCancel` (Button): Labeled "Cancel", redirects back to a list/overview page.

-   **Validation Indicators:**
    -   `RequiredFieldValidator` and `RegularExpressionValidator` controls: Display `*` to indicate validation errors for required fields and date formats respectively.

-   **Styling & Layout:**
    -   Extensive use of inline `style` attributes and CSS classes (`box3`, `fontcsswhite`, `redbox`, `cal_Theme2`) from `StyleSheet.css`. This will be replaced with Tailwind CSS classes in Django templates.
    -   HTML `<table>` elements are used for page layout, which will be modernized using responsive Tailwind CSS `div` and flexbox/grid layouts.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `newsnotice`.

#### 4.1 Models (`newsnotice/models.py`)

Task: Create a Django model based on the database schema.

Instructions:
The `NewsNotice` model directly maps to the `tblHR_News_Notices` table. We are using `managed = False` because the table already exists. Business logic related to file handling and status flagging is encapsulated within the model.

```python
from django.db import models
from datetime import date
from django.utils import timezone

class NewsNotice(models.Model):
    # Primary Key
    id = models.IntegerField(db_column='Id', primary_key=True)

    # Core News/Notice Details
    title = models.CharField(db_column='Title', max_length=255, blank=False, null=False)
    in_details = models.TextField(db_column='InDetails', blank=False, null=False)
    from_date = models.DateField(db_column='FromDate', blank=False, null=False)
    to_date = models.DateField(db_column='ToDate', blank=False, null=False)

    # File Attachment Details (for BLOB storage as per original ASP.NET)
    # Modern approach would use FileField and store files on disk/cloud.
    # Storing files as BLOBs in the database is generally discouraged for performance and scalability.
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.BigIntegerField(db_column='FileSize', blank=True, null=True, default=0)
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)

    # System/Audit Information
    sys_date = models.DateField(db_column='SysDate', auto_now=True) # Automatically updates on save
    sys_time = models.TimeField(db_column='SysTime', auto_now=True) # Automatically updates on save
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # e.g., username
    comp_id = models.IntegerField(db_column='CompId', default=1) # Placeholder, in real app from session/user profile
    fin_year_id = models.IntegerField(db_column='FinYearId', default=1) # Placeholder, in real app from session/user profile

    # Status Flag
    # In ASP.NET code, Flag is 0 or 1. If currDate > ToDate, Flag is '1'.
    # This implies '1' means expired/inactive, '0' means active.
    # Using BooleanField where True = inactive/expired, False = active.
    flag = models.BooleanField(db_column='Flag', default=False) # False for active, True for inactive/expired

    class Meta:
        managed = False  # Important: Django won't create/manage this table.
        db_table = 'tblHR_News_Notices'
        verbose_name = 'News Notice'
        verbose_name_plural = 'News Notices'
        ordering = ['-from_date'] # Default ordering for list views

    def __str__(self):
        return self.title

    # Business Logic Methods (Fat Model)
    def update_details_and_status(self, title, in_details, from_date, to_date, session_id, comp_id, fin_year_id):
        """
        Updates the core text details and sets the flag status.
        This method centralizes the update logic from BtnUpload_Click.
        """
        self.title = title
        self.in_details = in_details
        self.from_date = from_date
        self.to_date = to_date
        self.session_id = session_id
        self.comp_id = comp_id
        self.fin_year_id = fin_year_id
        self._set_flag_status() # Update flag based on dates
        self.save()

    def process_and_save_file(self, uploaded_file):
        """
        Handles the uploaded file, reads its content, and updates the file-related fields.
        This replaces the FileUpload1 processing logic.
        """
        if uploaded_file:
            self.file_name = uploaded_file.name
            self.file_size = uploaded_file.size
            self.content_type = uploaded_file.content_type
            self.file_data = uploaded_file.read() # Read file content into binary field
            self.save(update_fields=['file_name', 'file_size', 'content_type', 'file_data'])
        else:
            # If no file is uploaded but one existed, perhaps it means to clear it (e.g. from a separate clear button)
            # This method specifically for upload. Clearing is handled by clear_attached_file.
            pass

    def clear_attached_file(self):
        """
        Clears the file attachment details for this news notice.
        This replaces the ImageCross_Click logic.
        """
        self.file_name = None
        self.file_size = 0
        self.content_type = None
        self.file_data = None
        self.save(update_fields=['file_name', 'file_size', 'content_type', 'file_data'])

    def _set_flag_status(self):
        """
        Sets the 'flag' field based on the current date relative to the 'to_date'.
        This replicates the Flag logic from BtnUpload_Click.
        """
        self.flag = date.today() > self.to_date # True if expired, False if active

    @property
    def is_active(self):
        """Convenience property to check if the news is currently active."""
        return not self.flag

    @property
    def has_file(self):
        """Convenience property to check if a file is attached."""
        return bool(self.file_name and self.file_data)

    def get_file_download_url(self):
        """
        Generates a URL for file download. Since FileData is stored as BLOB,
        this will point to a specific view that serves the file.
        """
        if self.has_file:
            from django.urls import reverse
            return reverse('newsnotice_download_file', args=[self.pk])
        return None
```

#### 4.2 Forms (`newsnotice/forms.py`)

Task: Define a Django form for user input.

Instructions:
A `ModelForm` is created for `NewsNotice`. A separate `FileField` is added for handling file uploads, as `BinaryField` doesn't directly support file uploads in forms. Custom validation ensures date ordering.

```python
from django import forms
from .models import NewsNotice
from django.core.exceptions import ValidationError

class NewsNoticeForm(forms.ModelForm):
    # Add a FileField for upload, as BinaryField doesn't directly map to file uploads
    # This field is not bound to the model directly, will be processed manually in view/model.
    uploaded_file = forms.FileField(
        label="Upload File",
        required=False,
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 '
                     'file:rounded-md file:border-0 file:text-sm file:font-semibold '
                     'file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
        })
    )

    class Meta:
        model = NewsNotice
        fields = ['title', 'in_details', 'from_date', 'to_date'] # Exclude file fields for direct mapping, handle separately
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter news title'
            }),
            'in_details': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-32',
                'placeholder': 'Enter news description'
            }),
            'from_date': forms.DateInput(attrs={
                'type': 'date', # HTML5 date picker
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'to_date': forms.DateInput(attrs={
                'type': 'date', # HTML5 date picker
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }
        labels = {
            'title': 'Title',
            'in_details': 'Description',
            'from_date': 'Date on Display (From)',
            'to_date': 'Date on Display (To)',
        }

    def clean(self):
        """
        Custom validation for date range.
        Replicates implicit validation from ASP.NET for FromDate <= ToDate.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', ValidationError('To Date cannot be earlier than From Date.'))
        return cleaned_data

```

#### 4.3 Views (`newsnotice/views.py`)

Task: Implement CRUD operations using CBVs.

Instructions:
Views are kept very thin, delegating all business logic to the model. HTMX requests are handled with `HttpResponse(status=204)` and `HX-Trigger` headers for client-side updates without full page reloads. A custom view `NewsNoticeTablePartialView` is added to serve the DataTables content dynamically. A `NewsNoticeDownloadFileView` is added to serve the BLOB file data.

```python
from django.views.generic import ListView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import NewsNotice
from .forms import NewsNoticeForm
from django.utils import timezone
import mimetypes

class NewsNoticeListView(ListView):
    """
    Displays a list of all News Notices.
    Corresponds to the entry point/list overview from which the ASP.NET edit page is accessed.
    """
    model = NewsNotice
    template_name = 'newsnotice/list.html'
    context_object_name = 'newsnotices' # Plural for template iteration

    def get_queryset(self):
        # In a real system, you might filter by CompId, FinYearId from session/user profile.
        # For this example, returning all or filtering by a default CompId.
        # Assuming comp_id and fin_year_id are related to the current user's context.
        # For simplicity, let's filter by a hypothetical default for demonstration.
        # You would replace these with actual values from request.user or session.
        # Example: return NewsNotice.objects.filter(comp_id=self.request.session.get('compid', 1))
        return NewsNotice.objects.all().order_by('-from_date')

class NewsNoticeTablePartialView(ListView):
    """
    Renders only the table rows for News Notices, intended for HTMX requests.
    This allows dynamic refreshing of the table without reloading the entire page.
    """
    model = NewsNotice
    template_name = 'newsnotice/_newsnotice_table.html'
    context_object_name = 'newsnotices'

    def get_queryset(self):
        # Same queryset as ListView, but potentially optimized for partial rendering if needed.
        return NewsNotice.objects.all().order_by('-from_date')

class NewsNoticeUpdateView(UpdateView):
    """
    Handles the editing of an existing News Notice.
    Directly replaces the functionality of NewsandNotices_Edit_Details.aspx.cs.
    """
    model = NewsNotice
    form_class = NewsNoticeForm
    template_name = 'newsnotice/_newsnotice_form.html' # This is a partial template for modal
    success_url = reverse_lazy('newsnotice_list')

    def get_object(self, queryset=None):
        """
        Retrieves the object to be updated.
        Matches ASP.NET's `Request.QueryString["Id"]` to Django's `pk`.
        """
        pk = self.kwargs.get(self.pk_url_kwarg)
        # In ASP.NET, it also filtered by CompId. Add it if `CompId` is part of your routing/security.
        # obj = get_object_or_404(NewsNotice, pk=pk, comp_id=self.request.session.get('compid', 1))
        obj = get_object_or_404(NewsNotice, pk=pk)
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add file-related data to context for template display
        context['has_file'] = self.object.has_file
        context['current_file_name'] = self.object.file_name
        return context

    def form_valid(self, form):
        """
        Handles valid form submission. Business logic is delegated to the model.
        This combines the text and file update logic from BtnUpload_Click.
        """
        news_notice = form.instance
        # Assume session_id, comp_id, fin_year_id come from current user/session context
        # For example: self.request.user.username, self.request.session.get('compid', 1), self.request.session.get('finyear', 1)
        # For demonstration, using dummy values
        session_id = self.request.user.username if self.request.user.is_authenticated else "anonymous"
        comp_id = 1 # Replace with actual session/user data
        fin_year_id = 1 # Replace with actual session/user data

        # Update core details and set flag status via model method (Fat Model)
        news_notice.update_details_and_status(
            form.cleaned_data['title'],
            form.cleaned_data['in_details'],
            form.cleaned_data['from_date'],
            form.cleaned_data['to_date'],
            session_id, comp_id, fin_year_id
        )

        # Handle file upload if present
        uploaded_file = self.request.FILES.get('uploaded_file')
        news_notice.process_and_save_file(uploaded_file)

        messages.success(self.request, f'News Notice "{news_notice.title}" updated successfully.')

        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # and trigger a refresh event for the list table.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList'
                }
            )
        return super().form_valid(form) # Fallback for non-HTMX (will redirect)

class NewsNoticeClearFileView(View):
    """
    Handles clearing an attached file from a News Notice.
    Replaces the ImageCross_Click functionality.
    """
    def post(self, request, pk):
        news_notice = get_object_or_404(NewsNotice, pk=pk)
        # Add CompId filter if necessary for security
        # news_notice = get_object_or_404(NewsNotice, pk=pk, comp_id=request.session.get('compid', 1))

        news_notice.clear_attached_file() # Business logic in model
        messages.info(request, f'File for "{news_notice.title}" has been removed.')

        if request.headers.get('HX-Request'):
            # For HTMX, return 204 and trigger list refresh,
            # and potentially trigger a re-fetch of the form itself to update file display.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList, fileCleared' # 'fileCleared' can update the form
                }
            )
        # Fallback for non-HTMX, redirect to edit page to reflect changes
        return HttpResponseRedirect(reverse_lazy('newsnotice_edit', args=[pk]))

class NewsNoticeDownloadFileView(View):
    """
    View to serve the attached file (BLOB data) for a News Notice.
    This would be the target of the HyperLink1.
    """
    def get(self, request, pk):
        news_notice = get_object_or_404(NewsNotice, pk=pk)
        # Add CompId filter if necessary for security
        # news_notice = get_object_or_404(NewsNotice, pk=pk, comp_id=request.session.get('compid', 1))

        if not news_notice.has_file:
            raise Http404("No file attached to this news notice.")

        file_data = news_notice.file_data
        file_name = news_notice.file_name or f"news_notice_file_{news_notice.pk}"
        content_type = news_notice.content_type or mimetypes.guess_type(file_name)[0] or 'application/octet-stream'

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        response['Content-Length'] = news_notice.file_size
        return response

# Note: A NewsNoticeCreateView would be similar to NewsNoticeUpdateView but without get_object
# and for a new instance. As the original code was for 'Edit', Create is omitted here.
# A NewsNoticeDeleteView would also be added if direct deletion of the news item is desired.
```

#### 4.4 Templates (`newsnotice/templates/newsnotice/`)

Task: Create templates for each view.

Instructions:
The templates are designed for HTMX partial loading. `list.html` provides the main page, `_newsnotice_table.html` is dynamically loaded for the DataTable, and `_newsnotice_form.html` is used for the modal.

**`newsnotice/list.html`**
This template sets up the main page, including the placeholder for the DataTable and the modal for form interactions.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">News & Notices - Edit</h2>
        <!-- The original ASP.NET page was an 'Edit' page.
             If there was a list page leading to this, we could have an 'Add' button here.
             For this conversion, we'll assume the primary action is editing existing entries.
             For demo purposes, we will add an "Add" button here for completeness,
             which would lead to a NewsNoticeCreateView.
             For simplicity, here we just show the list for existing ones.
        -->
        <!-- Example Add Button (if a CreateView was implemented) -->
        <!--
        <button
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'newsnotice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal">
            Add New News Notice
        </button>
        -->
    </div>

    <!-- This div will be updated by HTMX -->
    <div id="newsnoticeTable-container"
         hx-trigger="load, refreshNewsNoticeList from:body"
         hx-get="{% url 'newsnotice_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial Loading State -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading News Notices...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit) and confirmations -->
    <div id="modal"
         class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 transition-opacity duration-300 opacity-0 pointer-events-none"
         x-data="{ showModal: false }"
         x-show="showModal"
         @refreshNewsNoticeList.window="showModal = false"
         @fileCleared.window="showModal = false"
         @click.self="showModal = false">
        <div id="modalContent"
             class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95"
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             @click.away="showModal = false">
            <!-- Content loaded by HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS and jQuery (ensure jQuery is loaded before DataTables) -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css" rel="stylesheet">

<!-- Alpine.js (if not already in base.html) -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            if (modal) {
                // Use Alpine.js to show the modal
                const alpineInstance = Alpine.$data(modal);
                if (alpineInstance) {
                    alpineInstance.showModal = true;
                }
            }
        }
    });

    // Handle closing modal on successful form submission (via HTMX trigger)
    document.body.addEventListener('refreshNewsNoticeList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            const alpineInstance = Alpine.$data(modal);
            if (alpineInstance) {
                alpineInstance.showModal = false;
            }
        }
    });

    // Handle closing modal after file clear (via HTMX trigger)
    document.body.addEventListener('fileCleared', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            const alpineInstance = Alpine.$data(modal);
            if (alpineInstance) {
                alpineInstance.showModal = false;
            }
        }
    });
</script>
{% endblock %}
```

**`newsnotice/_newsnotice_table.html`**
This partial template contains the actual DataTables structure.

```html
<table id="newsNoticeTable" class="min-w-full bg-white border-collapse border border-gray-300 rounded-lg shadow-sm">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">From Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">To Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">File</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for notice in newsnotices %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.title }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.in_details|truncatechars:70 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.to_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                {% if notice.has_file %}
                    <a href="{{ notice.get_file_download_url }}" class="text-blue-600 hover:underline">{{ notice.file_name }}</a>
                {% else %}
                    No File
                {% endif %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                             {% if notice.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {% if notice.is_active %}Active{% else %}Expired{% endif %}
                </span>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'newsnotice_edit' notice.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal">
                    Edit
                </button>
                <!-- Add Delete button if direct deletion of news item is desired -->
                <!--
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out ml-2"
                    hx-get="{% url 'newsnotice_delete' notice.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal">
                    Delete
                </button>
                -->
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 text-center text-gray-500 text-sm">No news notices found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after content is loaded via HTMX
    // Ensure jQuery is loaded before this script block runs
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.DataTable !== 'undefined') {
        $(document).ready(function() {
            if (!$.fn.DataTable.isDataTable('#newsNoticeTable')) { // Prevent re-initialization
                $('#newsNoticeTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "ordering": true,
                    "info": true,
                    "searching": true,
                    "paging": true
                });
            }
        });
    } else {
        console.warn("jQuery or DataTables not loaded. DataTables might not initialize.");
    }
</script>
```

**`newsnotice/_newsnotice_form.html`**
This partial template renders the form for editing a News Notice within the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">Edit News Notice</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-encoding="multipart/form-data">
        {% csrf_token %}

        <div class="space-y-5">
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.title.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.title.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.in_details.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.in_details.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.in_details }}
                {% if form.in_details.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.in_details.errors }}</p>
                {% endif %}
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.from_date.label }}<span class="text-red-500">*</span>
                    </label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.to_date.label }}<span class="text-red-500">*</span>
                    </label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                    {% endif %}
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Upload File</label>
                {% if has_file %}
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-sm text-gray-800">{{ current_file_name }}</span>
                    <button
                        type="button"
                        class="text-red-500 hover:text-red-700 focus:outline-none"
                        title="Clear File"
                        hx-post="{% url 'newsnotice_clear_file' object.pk %}"
                        hx-confirm="Are you sure you want to remove the attached file?"
                        hx-swap="none"
                        hx-trigger="click"
                        _="on htmx:afterRequest remove .flex from #modal then trigger refreshNewsNoticeList">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                    <a href="{{ object.get_file_download_url }}" class="text-blue-600 hover:underline text-sm ml-2">Download Current File</a>
                </div>
                {% endif %}
                {{ form.uploaded_file }}
                {% if form.uploaded_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uploaded_file.errors }}</p>
                {% endif %}
            </div>
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .flex from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-indicator="#loadingSpinner"
                onclick="return confirm('Are you sure you want to update this News Notice?');" >
                Update
            </button>
            <div id="loadingSpinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
        {% if form.non_field_errors %}
        <div class="mt-4 text-red-500 text-sm">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}
    </form>
</div>
```

#### 4.5 URLs (`newsnotice/urls.py`)

Task: Define URL patterns for the views.

Instructions:
URL patterns are defined for the list view, the update form (using `pk` for the ID), the HTMX partial for the table, and the file clearing and download functionalities.

```python
from django.urls import path
from .views import (
    NewsNoticeListView,
    NewsNoticeUpdateView,
    NewsNoticeTablePartialView,
    NewsNoticeClearFileView,
    NewsNoticeDownloadFileView,
)

urlpatterns = [
    # Main list page for News Notices
    path('newsnotice/', NewsNoticeListView.as_view(), name='newsnotice_list'),

    # HTMX endpoint to load/refresh the DataTables content
    path('newsnotice/table/', NewsNoticeTablePartialView.as_view(), name='newsnotice_table'),

    # Edit an existing News Notice (replaces ASP.NET edit page)
    path('newsnotice/edit/<int:pk>/', NewsNoticeUpdateView.as_view(), name='newsnotice_edit'),

    # HTMX endpoint to clear/remove an attached file
    path('newsnotice/clear-file/<int:pk>/', NewsNoticeClearFileView.as_view(), name='newsnotice_clear_file'),

    # Endpoint to download the attached file (BLOB data)
    path('newsnotice/download-file/<int:pk>/', NewsNoticeDownloadFileView.as_view(), name='newsnotice_download_file'),

    # If an "Add" functionality was needed (CreateView):
    # path('newsnotice/add/', NewsNoticeCreateView.as_view(), name='newsnotice_add'),

    # If a "Delete" functionality was needed (DeleteView):
    # path('newsnotice/delete/<int:pk>/', NewsNoticeDeleteView.as_view(), name='newsnotice_delete'),
]
```

#### 4.6 Tests (`newsnotice/tests.py`)

Task: Write tests for the model and views.

Instructions:
Comprehensive unit tests for model methods and integration tests for all views are provided, ensuring good test coverage and verifying HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from io import BytesIO
from unittest.mock import patch
from .models import NewsNotice

class NewsNoticeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.news_notice_active = NewsNotice.objects.create(
            id=1,
            title='Active News',
            in_details='Details for active news.',
            from_date=date.today() - timedelta(days=5),
            to_date=date.today() + timedelta(days=5),
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            flag=False # Should be calculated by model method, but setting for initial state
        )
        cls.news_notice_expired = NewsNotice.objects.create(
            id=2,
            title='Expired Notice',
            in_details='Details for expired notice.',
            from_date=date.today() - timedelta(days=10),
            to_date=date.today() - timedelta(days=1),
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            flag=True # Should be calculated by model method
        )
        cls.news_notice_with_file = NewsNotice.objects.create(
            id=3,
            title='News with File',
            in_details='This news has an attachment.',
            from_date=date.today(),
            to_date=date.today() + timedelta(days=10),
            file_name='test_file.txt',
            file_size=12,
            content_type='text/plain',
            file_data=b'Hello, World!',
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            flag=False
        )

    def test_news_notice_creation(self):
        self.assertEqual(self.news_notice_active.title, 'Active News')
        self.assertEqual(self.news_notice_active.in_details, 'Details for active news.')
        self.assertTrue(self.news_notice_active.is_active)

    def test_title_label(self):
        field_label = self.news_notice_active._meta.get_field('title').verbose_name
        self.assertEqual(field_label, 'title') # Django default, custom label is in form

    def test_from_date_label(self):
        field_label = self.news_notice_active._meta.get_field('from_date').verbose_name
        self.assertEqual(field_label, 'from date') # Django default

    def test_flag_status_calculation(self):
        # Test active status
        self.news_notice_active._set_flag_status()
        self.assertFalse(self.news_notice_active.flag)
        self.assertTrue(self.news_notice_active.is_active)

        # Test expired status
        self.news_notice_expired._set_flag_status()
        self.assertTrue(self.news_notice_expired.flag)
        self.assertFalse(self.news_notice_expired.is_active)

    def test_update_details_and_status(self):
        new_title = 'Updated Title'
        new_details = 'Updated Details'
        new_from_date = date.today()
        new_to_date = date.today() + timedelta(days=2)
        self.news_notice_active.update_details_and_status(
            new_title, new_details, new_from_date, new_to_date, 'newuser', 2, 2025
        )
        self.news_notice_active.refresh_from_db()
        self.assertEqual(self.news_notice_active.title, new_title)
        self.assertEqual(self.news_notice_active.in_details, new_details)
        self.assertEqual(self.news_notice_active.from_date, new_from_date)
        self.assertEqual(self.news_notice_active.to_date, new_to_date)
        self.assertEqual(self.news_notice_active.session_id, 'newuser')
        self.assertEqual(self.news_notice_active.comp_id, 2)
        self.assertEqual(self.news_notice_active.fin_year_id, 2025)
        # Verify flag status after update (should still be active)
        self.assertFalse(self.news_notice_active.flag)

    def test_process_and_save_file(self):
        file_content = b"This is a new file content."
        mock_file = BytesIO(file_content)
        mock_file.name = "new_document.pdf"
        mock_file.size = len(file_content)
        mock_file.content_type = "application/pdf"

        self.news_notice_active.process_and_save_file(mock_file)
        self.news_notice_active.refresh_from_db()

        self.assertEqual(self.news_notice_active.file_name, "new_document.pdf")
        self.assertEqual(self.news_notice_active.file_size, len(file_content))
        self.assertEqual(self.news_notice_active.content_type, "application/pdf")
        self.assertEqual(self.news_notice_active.file_data, file_content)
        self.assertTrue(self.news_notice_active.has_file)

    def test_clear_attached_file(self):
        self.news_notice_with_file.clear_attached_file()
        self.news_notice_with_file.refresh_from_db()

        self.assertIsNone(self.news_notice_with_file.file_name)
        self.assertEqual(self.news_notice_with_file.file_size, 0)
        self.assertIsNone(self.news_notice_with_file.content_type)
        self.assertIsNone(self.news_notice_with_file.file_data)
        self.assertFalse(self.news_notice_with_file.has_file)

    def test_get_file_download_url(self):
        self.assertEqual(self.news_notice_with_file.get_file_download_url(), reverse('newsnotice_download_file', args=[self.news_notice_with_file.pk]))
        self.assertIsNone(self.news_notice_active.get_file_download_url())


class NewsNoticeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.news_notice_1 = NewsNotice.objects.create(
            id=10,
            title='First News',
            in_details='Details for first news.',
            from_date=date(2023, 1, 1),
            to_date=date(2023, 1, 31),
            session_id='user1',
            comp_id=1,
            fin_year_id=2023,
            flag=True # Should be expired
        )
        cls.news_notice_2 = NewsNotice.objects.create(
            id=11,
            title='Second News',
            in_details='Details for second news.',
            from_date=date(2024, 6, 1),
            to_date=date(2024, 6, 30),
            session_id='user1',
            comp_id=1,
            fin_year_id=2024,
            flag=False # Should be active
        )
        cls.news_notice_3 = NewsNotice.objects.create(
            id=12,
            title='File News',
            in_details='Details for news with file.',
            from_date=date(2024, 1, 1),
            to_date=date(2024, 12, 31),
            file_name='report.pdf',
            file_size=1024,
            content_type='application/pdf',
            file_data=b'%PDF-1.4...\n%%EOF',
            session_id='user2',
            comp_id=2, # Different company ID
            fin_year_id=2024,
            flag=False
        )

    def setUp(self):
        self.client = Client()
        # Mock user authentication if views require it
        # For simple demo, views don't enforce authentication beyond session_id logging.
        # If authentication is needed, add a dummy user:
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(username='testuser', password='password123')
        self.client.login(username='testuser', password='password123')


    def test_list_view_get(self):
        response = self.client.get(reverse('newsnotice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'newsnotice/list.html')
        self.assertContains(response, 'News & Notices - Edit') # Check for title
        # Test HTMX initial load
        self.assertContains(response, 'hx-get="/newsnotice/table/"')

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('newsnotice_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'newsnotice/_newsnotice_table.html')
        self.assertTrue('newsnotices' in response.context)
        self.assertEqual(len(response.context['newsnotices']), 3) # Should show all 3
        self.assertContains(response, self.news_notice_1.title)
        self.assertContains(response, self.news_notice_2.title)
        self.assertContains(response, self.news_notice_3.title)

    def test_update_view_get(self):
        response = self.client.get(reverse('newsnotice_edit', args=[self.news_notice_1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'newsnotice/_newsnotice_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, self.news_notice_1.title)
        self.assertContains(response, self.news_notice_1.in_details)

    def test_update_view_post_success(self):
        data = {
            'title': 'Updated News Title',
            'in_details': 'Updated details for the news.',
            'from_date': (date.today() - timedelta(days=1)).isoformat(),
            'to_date': (date.today() + timedelta(days=10)).isoformat(),
            'uploaded_file': '' # No file uploaded
        }
        response = self.client.post(reverse('newsnotice_edit', args=[self.news_notice_1.pk]), data)

        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX
        self.news_notice_1.refresh_from_db()
        self.assertEqual(self.news_notice_1.title, 'Updated News Title')
        self.assertEqual(self.news_notice_1.in_details, 'Updated details for the news.')
        self.assertFalse(self.news_notice_1.flag) # Should become active now

    def test_update_view_post_with_file_upload(self):
        file_content = b'This is a test file for upload.'
        uploaded_file = BytesIO(file_content)
        uploaded_file.name = 'new_uploaded_file.txt'
        uploaded_file.size = len(file_content)
        uploaded_file.content_type = 'text/plain'

        data = {
            'title': 'News with New File',
            'in_details': 'Details after file upload.',
            'from_date': (date.today() - timedelta(days=2)).isoformat(),
            'to_date': (date.today() + timedelta(days=8)).isoformat(),
            'uploaded_file': uploaded_file,
        }
        # Use multipart/form-data for file uploads
        response = self.client.post(reverse('newsnotice_edit', args=[self.news_notice_1.pk]), data, follow=True)

        self.assertEqual(response.status_code, 200) # Should redirect to list view after success
        self.news_notice_1.refresh_from_db()

        self.assertEqual(self.news_notice_1.title, 'News with New File')
        self.assertTrue(self.news_notice_1.has_file)
        self.assertEqual(self.news_notice_1.file_name, 'new_uploaded_file.txt')
        self.assertEqual(self.news_notice_1.file_size, len(file_content))
        self.assertEqual(self.news_notice_1.content_type, 'text/plain')
        self.assertEqual(self.news_notice_1.file_data, file_content)

    def test_update_view_post_invalid_data(self):
        data = {
            'title': '', # Missing required field
            'in_details': 'Invalid data test.',
            'from_date': (date.today() + timedelta(days=5)).isoformat(), # from_date after to_date
            'to_date': (date.today() - timedelta(days=5)).isoformat(),
        }
        response = self.client.post(reverse('newsnotice_edit', args=[self.news_notice_1.pk]), data)
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'To Date cannot be earlier than From Date.')

    def test_update_view_htmx_post_success(self):
        data = {
            'title': 'HTMX Updated Title',
            'in_details': 'Details for HTMX update.',
            'from_date': (date.today() - timedelta(days=1)).isoformat(),
            'to_date': (date.today() + timedelta(days=10)).isoformat(),
            'uploaded_file': ''
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('newsnotice_edit', args=[self.news_notice_2.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshNewsNoticeList')
        self.news_notice_2.refresh_from_db()
        self.assertEqual(self.news_notice_2.title, 'HTMX Updated Title')

    def test_clear_file_view_post(self):
        pk_with_file = self.news_notice_3.pk
        self.assertTrue(NewsNotice.objects.get(pk=pk_with_file).has_file)

        response = self.client.post(reverse('newsnotice_clear_file', args=[pk_with_file]))
        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX
        self.assertFalse(NewsNotice.objects.get(pk=pk_with_file).has_file)

    def test_clear_file_view_htmx_post(self):
        pk_with_file = self.news_notice_3.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('newsnotice_clear_file', args=[pk_with_file]), **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('refreshNewsNoticeList', response.headers['HX-Trigger'])
        self.assertFalse(NewsNotice.objects.get(pk=pk_with_file).has_file)

    def test_download_file_view(self):
        response = self.client.get(reverse('newsnotice_download_file', args=[self.news_notice_3.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="report.pdf"')
        self.assertEqual(response.content, b'%PDF-1.4...\n%%EOF')

    def test_download_file_view_no_file(self):
        response = self.client.get(reverse('newsnotice_download_file', args=[self.news_notice_1.pk]))
        self.assertEqual(response.status_code, 404)
```

### Step 5: HTMX and Alpine.js Integration

Instructions:
The provided Django code snippets and templates are already designed with HTMX and Alpine.js for a modern, dynamic user experience, directly replacing the functionality of the ASP.NET controls and postbacks.

-   **HTMX for Dynamic Updates:**
    -   The main `newsnotice/list.html` uses `hx-get` to load the table content from `{% url 'newsnotice_table' %}` upon page load and on `refreshNewsNoticeList` trigger.
    -   "Edit" buttons use `hx-get` to fetch the `_newsnotice_form.html` into the modal's `modalContent` div.
    -   Form submissions (`_newsnotice_form.html`) use `hx-post` to send data to the `NewsNoticeUpdateView`. Upon success, the view responds with `204 No Content` and an `HX-Trigger` header (`refreshNewsNoticeList`), which tells the `newsnoticeTable-container` to reload its content, thus refreshing the DataTables.
    -   The "Clear File" button uses `hx-post` to send a request to `NewsNoticeClearFileView`. Upon success, it also triggers `refreshNewsNoticeList` to update the list, and a `fileCleared` event to close the modal.
    -   `hx-swap="none"` on forms ensures the browser doesn't try to swap content on successful HTMX POST, relying instead on the `HX-Trigger` header.

-   **Alpine.js for UI State Management (Modals):**
    -   The modal in `list.html` uses `x-data="{ showModal: false }"` and `x-show="showModal"` to manage its visibility.
    -   Buttons that open the modal, such as the "Edit" button, use `_="on click add .flex to #modal then add .opacity-100 to #modal"` to make the modal visible and animate it.
    -   Alpine.js listens for custom events (`@refreshNewsNoticeList.window`, `@fileCleared.window`) to set `showModal = false`, automatically closing the modal after a successful form submission or file clear.
    -   `@click.self="showModal = false"` on the modal overlay allows clicking outside the form to close it.

-   **DataTables for List Views:**
    -   The `_newsnotice_table.html` partial includes the `<table>` element with `id="newsNoticeTable"`.
    -   A `<script>` block within this partial initializes DataTables on this table using jQuery, providing client-side search, sorting, and pagination. This script executes every time the partial is loaded via HTMX, ensuring DataTables is re-initialized correctly with the new data.

-   **Seamless Interactions:**
    -   All CRUD operations (Update, Clear File) are performed without full page reloads, providing a smooth, single-page application feel characteristic of modern web applications.
    -   Validation errors are shown directly within the form, thanks to Django forms' error rendering and HTMX's ability to swap partial content.

### Final Notes

This comprehensive plan details the migration of the ASP.NET News & Notices module to a modern Django application.

-   **Automation-Driven Approach:** The provided structure and code are designed to be easily generated and adapted by an AI-assisted automation tool. The clear separation of concerns (model, form, view, template, URL, test) facilitates modular generation and testing.
-   **Business Value:** This modernization improves user experience with faster, dynamic interactions (no full page reloads), a more maintainable codebase, and enhanced scalability. The shift to Django, HTMX, and Alpine.js reduces the need for complex front-end frameworks and large JavaScript bundles, simplifying development and maintenance while delivering a responsive interface.
-   **Future-Proofing:** Moving to Django ensures the application is built on a robust, actively maintained framework with a vast ecosystem, making it easier to add new features, integrate with other services, and scale in the future.
-   **Security:** Django's built-in security features (CSRF protection, ORM for SQL injection prevention) inherently improve the application's security posture compared to manual SQL string concatenation in legacy systems.
-   **Scalability:** The fat model/thin view architecture promotes a clean and scalable application design, making it easier to manage complexity as the application grows.

This plan provides a clear, actionable roadmap for transitioning the identified ASP.NET functionality to a modern Django solution.