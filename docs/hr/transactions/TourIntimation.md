## ASP.NET to Django Conversion Script: Tour Intimation Module Modernization Plan

This document outlines a strategic plan for migrating your existing ASP.NET 'Tour Intimation' module to a modern, robust, and scalable Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a smooth transition with minimal manual intervention and maximizing business value through improved efficiency and maintainability.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the core database tables and their columns involved in the Tour Intimation module.

**Analysis:**
The ASP.NET code interacts with several database tables:
- `tblACC_TourIntimation_Master`: The primary table for storing main tour intimation details.
- `tblACC_TourAdvance_Temp`: A temporary table used for staging advance transfer details before final submission.
- `tblACC_TourExpencessType`: A lookup table for types of expenses.
- `tblACC_TourAdvance_Details`: Stores specific expense amounts and remarks linked to a tour intimation.
- `tblACC_TourAdvance`: Stores advance amounts transferred to specific employees for a tour intimation.
- `tblHR_OfficeStaff`: Stores employee information, used for lookups and autocomplete.
- `BusinessGroup`: A lookup table for business groups.
- Implicitly, lookup tables for `Country`, `State`, and `City` based on dropdown functionality.

**Extracted Schema for Django Models:**

- **Primary Model:** `TourIntimation` (maps to `tblACC_TourIntimation_Master`)
- **Associated Models:**
    - `TourAdvanceTemp` (maps to `tblACC_TourAdvance_Temp`)
    - `TourExpenseType` (maps to `tblACC_TourExpencessType`)
    - `TourAdvanceDetail` (maps to `tblACC_TourAdvance_Details`)
    - `TourAdvanceTransfer` (maps to `tblACC_TourAdvance`)
- **Lookup Models:**
    - `Employee` (maps to `tblHR_OfficeStaff`)
    - `BusinessGroup` (maps to `BusinessGroup`)
    - `Country`, `State`, `City` (assuming a generic location hierarchy)

### Step 2: Identify Backend Functionality

**Task:** Determine the core business logic and data operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET code primarily handles the creation of a 'Tour Intimation' record, which is a complex transaction involving multiple related data entries. It also manages temporary "Advance Transfer" records dynamically before the final submission.

-   **Main Form Submission (Create):**
    -   Collects primary tour details (employee, project, dates, contact information).
    -   Generates a new `TINo` (Tour Intimation Number).
    -   Validates all input fields (required, date format, number format, WO number validity).
    -   Saves main details to `tblACC_TourIntimation_Master`.
    -   Iterates through `GridView2` (Advance Details) to save `Amount` and `Remarks` for each `ExpenseType` into `tblACC_TourAdvance_Details`.
    -   Transfers all temporary records from `tblACC_TourAdvance_Temp` into `tblACC_TourAdvance`, then clears the temporary table.
    -   Handles conditional visibility for Employee Name (Self/Others) and Work Order/Business Group selection.
    -   Manages dependent dropdowns for Country, State, and City.
    -   Provides client-side alerts for validation failures or invalid data.

-   **Temporary Advance Transfer Grid (CRUD on `tblACC_TourAdvance_Temp`):**
    -   **Add:** Allows adding new employee-amount-remarks entries to a temporary list. Includes employee autocomplete and validation. Prevents duplicate employee entries.
    -   **Edit:** Supports inline editing of existing temporary entries.
    -   **Delete:** Allows removing temporary entries.
    -   **Read:** Displays the temporary entries in a data grid, populating employee names from `tblHR_OfficeStaff`.
    -   All these operations are dynamic and update the grid without a full page refresh.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactive roles to design the new Django frontend.

**Analysis:**
The ASP.NET page uses a mix of standard form controls, custom controls, and data grids:

-   **Form Controls:**
    -   `RadioButtonList` for "Self/Others" employee selection and "WO No/BG Group" selection.
    -   `TextBox` for various text inputs (Project Name, No. of Days, Contact details, etc.).
    -   `DropDownList` for selecting Business Group, Country, State, and City.
    -   `CalendarExtender` for date input fields (`textStartDate`, `textEndDate`). These will be replaced by native HTML5 date inputs and potentially a lightweight JS date picker if needed, but HTMX/Alpine.js focus will prioritize simplicity.
    -   `TimeSelector` for time input. Similar to dates, simple HTML5 `type="time"` or Alpine.js managed selectors.
    -   `AutoCompleteExtender` for `EmpName` and `txtWONo`. This will be fully replaced by HTMX-driven autocomplete against Django endpoints.
    -   `RequiredFieldValidator` and `RegularExpressionValidator`: Client-side and server-side validation. Django forms will handle this comprehensively.
    -   `Panel` for scrollable content.

-   **Data Grids:**
    -   `GridView2` (Advance Details): Displays static expense types with editable amount/remarks. This will be a Django formset.
    -   `GridView1` (Advance Trans. To): A dynamic grid with inline CRUD operations. This is a perfect candidate for HTMX-driven partial updates using DataTables.

-   **Layout:**
    -   `TabContainer` (`AjaxControlToolkit`): For `Advance Details` and `Advance Trans. To`. This will be implemented using Alpine.js for tab switching.
    -   Standard HTML tables for overall layout. This will be replaced by modern responsive layout using Tailwind CSS.

---

### Step 4: Generate Django Code

We will structure the Django application as `hr` (for Human Resources).

#### 4.1 Models (hr/models.py)

We will define models for each identified database table, ensuring they map correctly and include methods for business logic.

```python
from django.db import models
from django.conf import settings
from django.utils import timezone
import datetime

# --- Lookup Models (often in a 'core' or 'lookup' app, but defined here for completeness) ---

class Employee(models.Model):
    # This maps to tblHR_OfficeStaff
    # Assuming EmpId is the primary key or unique identifier used in logic
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    
    # Assuming CompId and FinYearId exist in tblHR_OfficeStaff
    comp_id = models.IntegerField(db_column='CompId', default=1) 
    fin_year_id = models.IntegerField(db_column='FinYearId', default=1)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}. {self.employee_name} [{self.emp_id}]"

    # Business logic for getting EmpId from "Name [Id]" string
    @classmethod
    def get_emp_id_from_full_name(cls, full_name_with_id):
        if '[' in full_name_with_id and ']' in full_name_with_id:
            try:
                emp_id = full_name_with_id.split('[')[-1].replace(']', '').strip()
                return emp_id
            except IndexError:
                pass
        return None # Or raise an error if not found

    @classmethod
    def get_employee_by_code(cls, emp_code, comp_id, fin_year_id):
        # In original code, chkEmpCode returned 1 if valid, 0 otherwise.
        # This method returns the Employee object or None.
        try:
            return cls.objects.get(emp_id=emp_code, comp_id=comp_id, fin_year_id=fin_year_id)
        except cls.DoesNotExist:
            return None

class BusinessGroup(models.Model):
    # This maps to BusinessGroup
    # Assuming Id is PK and Symbol is the display name
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Country(models.Model):
    # Assuming a generic country table
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCore_Country' # Placeholder table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    # Assuming a generic state table
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=100)
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CountryId')

    class Meta:
        managed = False
        db_table = 'tblCore_State' # Placeholder table name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    # Assuming a generic city table
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=100)
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='StateId')

    class Meta:
        managed = False
        db_table = 'tblCore_City' # Placeholder table name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class TourExpenseType(models.Model):
    # Maps to tblACC_TourExpencessType
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_TourExpencessType'
        verbose_name = 'Tour Expense Type'
        verbose_name_plural = 'Tour Expense Types'

    def __str__(self):
        return self.terms

# --- Main Tour Intimation Models ---

class TourIntimation(models.Model):
    # Maps to tblACC_TourIntimation_Master
    # Primary Key
    id = models.AutoField(db_column='Id', primary_key=True)

    # System/Session Info
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=255, default='') # Maps to username
    comp_id = models.IntegerField(db_column='CompId', default=1) # Company ID
    fin_year_id = models.IntegerField(db_column='FinYearId', default=1) # Financial Year ID

    # Tour Intimation Number (TINo)
    ti_no = models.CharField(db_column='TINo', max_length=10, unique=True, null=True, blank=True) # D4 format

    # Employee Details
    employee_type = models.IntegerField(db_column='Type', default=0) # 0: Self, 1: Others
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId', to_field='emp_id', related_name='tour_intimations_as_requester') # EmpId as FK
    other_employee_emp_id = models.CharField(db_column='OtherEmpId', max_length=50, blank=True, null=True) # If employee_type is 'Others'

    # Work Order / Business Group
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroupId', blank=True, null=True)

    # Project and Tour Details
    project_name = models.CharField(db_column='ProjectName', max_length=255)
    tour_start_date = models.DateField(db_column='TourStartDate')
    tour_start_time = models.TimeField(db_column='TourStartTime')
    tour_end_date = models.DateField(db_column='TourEndDate')
    tour_end_time = models.TimeField(db_column='TourEndTime')
    no_of_days = models.IntegerField(db_column='NoOfDays')

    # Accommodation and Contact
    name_address_ser_provider = models.TextField(db_column='NameAddressSerProvider')
    contact_person = models.CharField(db_column='ContactPerson', max_length=255)
    contact_no = models.CharField(db_column='ContactNo', max_length=50)
    email = models.EmailField(db_column='Email', max_length=255, blank=True, null=True)

    # Place of Tour
    place_of_tour_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='PlaceOfTourCountry', related_name='tour_intimations_as_country')
    place_of_tour_state = models.ForeignKey(State, models.DO_NOTHING, db_column='PlaceOfTourState', related_name='tour_intimations_as_state')
    place_of_tour_city = models.ForeignKey(City, models.DO_NOTHING, db_column='PlaceOfTourCity', related_name='tour_intimations_as_city')

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation'
        verbose_name_plural = 'Tour Intimations'
        # Ordering can be added if there's a specific order, e.g., default_ordering = ['-sys_date', '-id']

    def __str__(self):
        return f"TI-{self.ti_no} ({self.employee.employee_name})"

    def save(self, *args, **kwargs):
        if not self.pk: # Only generate TINo for new records
            # This logic should be moved to a custom manager or a pre_save signal if it's transaction-critical
            last_ti = TourIntimation.objects.filter(
                comp_id=self.comp_id, 
                fin_year_id=self.fin_year_id
            ).order_by('-id').first() # Using -id assuming Id is sequential
            
            if last_ti and last_ti.ti_no:
                try:
                    last_num = int(last_ti.ti_no)
                    self.ti_no = f"{(last_num + 1):04d}"
                except ValueError:
                    self.ti_no = "0001" # Fallback
            else:
                self.ti_no = "0001"
        super().save(*args, **kwargs)

    # Example business logic method:
    def check_wo_validity(self, wo_no, comp_id, fin_year_id):
        # This would involve querying SD_Cust_WorkOrder_Master table
        # For simplicity, returning True
        return True # Placeholder for actual logic

    def calculate_duration(self):
        start_datetime = datetime.datetime.combine(self.tour_start_date, self.tour_start_time)
        end_datetime = datetime.datetime.combine(self.tour_end_date, self.tour_end_time)
        duration = end_datetime - start_datetime
        # Convert duration to days, hours, minutes or just total days
        return duration.days + duration.seconds / (24 * 3600) # Simple approximation

class TourAdvanceTemp(models.Model):
    # Maps to tblACC_TourAdvance_Temp
    # This is a temporary model, managed by the session/user for live grid interactions
    id = models.AutoField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=255) # Maps to username
    comp_id = models.IntegerField(db_column='CompId', default=1)
    fin_year_id = models.IntegerField(db_column='FinYearId', default=1)
    
    # Use ForeignKey for EmpId, as in original, to simplify lookups
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId', to_field='emp_id', related_name='tour_advance_temps')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance_Temp'
        verbose_name = 'Temporary Tour Advance'
        verbose_name_plural = 'Temporary Tour Advances'
        # Ensure unique per session/user per employee during temporary entry
        unique_together = (('session_id', 'comp_id', 'fin_year_id', 'employee'),)

    def __str__(self):
        return f"{self.employee.employee_name} - {self.amount}"

    @classmethod
    def clear_session_data(cls, session_id, comp_id, fin_year_id):
        # Business logic for clearing temporary data
        cls.objects.filter(session_id=session_id, comp_id=comp_id, fin_year_id=fin_year_id).delete()

class TourAdvanceDetail(models.Model):
    # Maps to tblACC_TourAdvance_Details
    id = models.AutoField(db_column='Id', primary_key=True)
    tour_intimation = models.ForeignKey(TourIntimation, models.DO_NOTHING, db_column='MId', related_name='advance_details')
    expense_type = models.ForeignKey(TourExpenseType, models.DO_NOTHING, db_column='ExpencessId', related_name='advance_details')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2) # Original used N2
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance_Details'
        verbose_name = 'Tour Advance Detail'
        verbose_name_plural = 'Tour Advance Details'

    def __str__(self):
        return f"{self.expense_type.terms}: {self.amount}"

class TourAdvanceTransfer(models.Model):
    # Maps to tblACC_TourAdvance
    id = models.AutoField(db_column='Id', primary_key=True)
    tour_intimation = models.ForeignKey(TourIntimation, models.DO_NOTHING, db_column='MId', related_name='advance_transfers')
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId', to_field='emp_id', related_name='tour_advance_transfers')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2) # Original used N2
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance'
        verbose_name = 'Tour Advance Transfer'
        verbose_name_plural = 'Tour Advance Transfers'

    def __str__(self):
        return f"Transfer to {self.employee.employee_name}: {self.amount}"

```

#### 4.2 Forms (hr/forms.py)

We'll define forms for the main tour intimation and for the temporary advance entries, along with a formset for the expense details.

```python
from django import forms
from django.forms import formset_factory
from .models import (
    TourIntimation, Employee, BusinessGroup, Country, State, City,
    TourAdvanceTemp, TourExpenseType, TourAdvanceDetail
)
import datetime

# Common Tailwind CSS classes for form inputs
INPUT_CLASSES = "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
RADIO_CLASSES = "h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"

class TourIntimationForm(forms.ModelForm):
    # Radio button fields
    employee_selection_type = forms.ChoiceField(
        choices=[('0', 'Self'), ('1', 'Others')],
        widget=forms.RadioSelect(attrs={'class': 'flex space-x-4'}),
        initial='0',
        label='Employee Type'
    )
    # The actual employee field. If 'Self', this is current user. If 'Others', selected via autocomplete.
    # It will be hidden/shown via Alpine.js.
    # Store employee_name for 'Others' via autocomplete, then resolve to emp_id in view/model logic.
    other_employee_name = forms.CharField(
        max_length=255, 
        required=False, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': INPUT_CLASSES, 
            'placeholder': 'Start typing employee name...',
            'hx-get': '/hr/employees/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off', # Disable browser autocomplete
            '@input': 'clearSelectedEmployee()', # Alpine.js
            'x-bind:disabled': 'employeeType === "0"' # Disabled if "Self"
        })
    )
    # Hidden input to store the resolved emp_id from autocomplete
    employee_emp_id = forms.CharField(
        max_length=50, 
        required=False, 
        widget=forms.HiddenInput(attrs={'x-model': 'selectedEmployeeId'})
    )


    wo_group_selection_type = forms.ChoiceField(
        choices=[('0', 'WO No'), ('1', 'BG Group')],
        widget=forms.RadioSelect(attrs={'class': 'flex space-x-4'}),
        initial='0',
        label='WO/BG Selection'
    )

    class Meta:
        model = TourIntimation
        fields = [
            # 'employee_selection_type', # Handled as extra field
            # 'other_employee_name', # Handled as extra field
            # 'employee_emp_id', # Handled as extra field
            'project_name',
            # 'wo_group_selection_type', # Handled as extra field
            'wo_no',
            'business_group',
            'place_of_tour_country',
            'place_of_tour_state',
            'place_of_tour_city',
            'tour_start_date',
            'tour_start_time',
            'tour_end_date',
            'tour_end_time',
            'no_of_days',
            'name_address_ser_provider',
            'contact_person',
            'contact_no',
            'email',
        ]
        widgets = {
            'project_name': forms.TextInput(attrs={'class': INPUT_CLASSES}),
            'wo_no': forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': 'woGroupType === "1"'}),
            'business_group': forms.Select(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': 'woGroupType === "0"'}),
            'place_of_tour_country': forms.Select(attrs={
                'class': INPUT_CLASSES,
                'hx-get': '/hr/locations/states/',
                'hx-target': '#id_place_of_tour_state',
                'hx-trigger': 'change',
                'hx-include': 'this', # Include selected country ID
                'hx-swap': 'outerHTML'
            }),
            'place_of_tour_state': forms.Select(attrs={
                'class': INPUT_CLASSES,
                'hx-get': '/hr/locations/cities/',
                'hx-target': '#id_place_of_tour_city',
                'hx-trigger': 'change',
                'hx-include': 'this', # Include selected state ID
                'hx-swap': 'outerHTML'
            }),
            'place_of_tour_city': forms.Select(attrs={'class': INPUT_CLASSES}),
            'tour_start_date': forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'}),
            'tour_start_time': forms.TimeInput(attrs={'class': INPUT_CLASSES, 'type': 'time'}),
            'tour_end_date': forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'}),
            'tour_end_time': forms.TimeInput(attrs={'class': INPUT_CLASSES, 'type': 'time'}),
            'no_of_days': forms.NumberInput(attrs={'class': INPUT_CLASSES}),
            'name_address_ser_provider': forms.Textarea(attrs={'class': INPUT_CLASSES, 'rows': 3}),
            'contact_person': forms.TextInput(attrs={'class': INPUT_CLASSES}),
            'contact_no': forms.TextInput(attrs={'class': INPUT_CLASSES}),
            'email': forms.EmailInput(attrs={'class': INPUT_CLASSES}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dynamic dropdowns
        self.fields['business_group'].queryset = BusinessGroup.objects.all()
        self.fields['place_of_tour_country'].queryset = Country.objects.all().order_by('name')
        
        # Initial population of state and city based on selected country/state for editing
        if self.instance.pk:
            if self.instance.place_of_tour_country:
                self.fields['place_of_tour_state'].queryset = State.objects.filter(country=self.instance.place_of_tour_country).order_by('name')
            if self.instance.place_of_tour_state:
                self.fields['place_of_tour_city'].queryset = City.objects.filter(state=self.instance.place_of_tour_state).order_by('name')
            
            # Set initial values for radio buttons if instance exists
            if self.instance.employee.emp_id == self.instance.session_id: # Assuming session_id is current user's emp_id for 'self'
                self.fields['employee_selection_type'].initial = '0'
                self.initial['other_employee_name'] = ''
                self.initial['employee_emp_id'] = self.instance.employee.emp_id
            else:
                self.fields['employee_selection_type'].initial = '1'
                self.initial['other_employee_name'] = str(self.instance.employee) # Display full name for others
                self.initial['employee_emp_id'] = self.instance.employee.emp_id

            if self.instance.wo_no and self.instance.wo_no != 'NA':
                self.fields['wo_group_selection_type'].initial = '0'
            elif self.instance.business_group:
                self.fields['wo_group_selection_type'].initial = '1'
        else:
            # For new forms, initialize state/city to empty
            self.fields['place_of_tour_state'].queryset = State.objects.none()
            self.fields['place_of_tour_city'].queryset = City.objects.none()

        # Add generic initial 'Select' option for all dropdowns
        self.fields['place_of_tour_country'].empty_label = 'Select'
        self.fields['place_of_tour_state'].empty_label = 'Select'
        self.fields['place_of_tour_city'].empty_label = 'Select'
        self.fields['business_group'].empty_label = 'Select'


    def clean(self):
        cleaned_data = super().clean()
        employee_selection_type = cleaned_data.get('employee_selection_type')
        other_employee_name = cleaned_data.get('other_employee_name')
        employee_emp_id = cleaned_data.get('employee_emp_id')
        wo_group_selection_type = cleaned_data.get('wo_group_selection_type')
        wo_no = cleaned_data.get('wo_no')
        business_group = cleaned_data.get('business_group')

        # Employee Validation
        if employee_selection_type == '1': # Others selected
            if not other_employee_name or not employee_emp_id:
                self.add_error('other_employee_name', 'Employee name and ID are required for "Others".')
            else:
                # Validate if the resolved emp_id actually corresponds to a valid employee
                if not Employee.get_employee_by_code(employee_emp_id, comp_id=self.instance.comp_id if self.instance.pk else 1, fin_year_id=self.instance.fin_year_id if self.instance.pk else 1):
                    self.add_error('other_employee_name', 'Invalid Employee selected.')

        # WO/BG Validation
        if wo_group_selection_type == '0': # WO No selected
            if not wo_no:
                self.add_error('wo_no', 'WO No is required.')
            # You might add `TourIntimation.check_wo_validity(wo_no, ...)` here for server-side check
            # if not TourIntimation.check_wo_validity(wo_no, self.instance.comp_id, self.instance.fin_year_id):
            #     self.add_error('wo_no', 'Entered WO No is not valid!')
            cleaned_data['business_group'] = None # Ensure BG is cleared
        else: # BG Group selected
            if not business_group:
                self.add_error('business_group', 'Business Group is required.')
            cleaned_data['wo_no'] = 'NA' # Ensure WO No is set to 'NA'

        # Date Validation (can be handled by Django's DateField, but adding specific checks for range/format if needed)
        start_date = cleaned_data.get('tour_start_date')
        end_date = cleaned_data.get('tour_end_date')
        if start_date and end_date and start_date > end_date:
            self.add_error('tour_end_date', 'End Date cannot be before Start Date.')
        
        # No of Days validation (original had RegEx for decimal, Django NumberInput covers basic numbers)
        no_of_days = cleaned_data.get('no_of_days')
        if no_of_days is not None and no_of_days < 0:
            self.add_error('no_of_days', 'Number of days cannot be negative.')

        # Required dropdowns
        if cleaned_data.get('place_of_tour_country') is None:
            self.add_error('place_of_tour_country', 'Country is required.')
        if cleaned_data.get('place_of_tour_state') is None:
            self.add_error('place_of_tour_state', 'State is required.')
        if cleaned_data.get('place_of_tour_city') is None:
            self.add_error('place_of_tour_city', 'City is required.')


        return cleaned_data


class TourAdvanceTempForm(forms.ModelForm):
    # Field to display and get Employee name from autocomplete
    employee_full_name = forms.CharField(
        max_length=255, 
        required=True, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': INPUT_CLASSES, 
            'placeholder': 'Start typing employee name...',
            'hx-get': '/hr/employees/autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-autocomplete-results-advance-temp',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off',
            '@input': 'clearSelectedEmployee()', # Alpine.js
        })
    )
    # Hidden field to store the actual emp_id from autocomplete
    employee_emp_id = forms.CharField(
        max_length=50, 
        required=True, 
        widget=forms.HiddenInput(attrs={'x-model': 'selectedEmployeeId'})
    )

    class Meta:
        model = TourAdvanceTemp
        fields = ['amount', 'remarks']
        widgets = {
            'amount': forms.NumberInput(attrs={'class': INPUT_CLASSES}),
            'remarks': forms.TextInput(attrs={'class': INPUT_CLASSES}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk: # For edit mode
            self.initial['employee_full_name'] = str(self.instance.employee)
            self.initial['employee_emp_id'] = self.instance.employee.emp_id
        
        # Remove direct employee field from here, use employee_full_name and employee_emp_id
        del self.fields['employee'] 

    def clean(self):
        cleaned_data = super().clean()
        employee_emp_id = cleaned_data.get('employee_emp_id')
        amount = cleaned_data.get('amount')

        if not employee_emp_id:
            self.add_error('employee_full_name', 'Employee is required.')
        else:
            # Resolve EmpId to Employee object
            try:
                # Assuming CompId and FinYearId are handled by the view or request context
                # For demo, using dummy defaults
                cleaned_data['employee'] = Employee.objects.get(emp_id=employee_emp_id, comp_id=1, fin_year_id=1) 
            except Employee.DoesNotExist:
                self.add_error('employee_full_name', 'Invalid Employee ID.')

        if amount is not None and amount <= 0:
            self.add_error('amount', 'Amount must be positive.')
            
        return cleaned_data

class TourExpenseDetailForm(forms.Form):
    # This form is for the individual row in GridView2, for which we input Amount and Remarks
    # Terms is read-only, passed via initial data
    expense_type_id = forms.IntegerField(widget=forms.HiddenInput(), required=False) # Store the ID
    terms = forms.CharField(max_length=255, widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'readonly': 'readonly'}), required=False)
    amount = forms.DecimalField(
        max_digits=18, decimal_places=2, required=False, 
        widget=forms.NumberInput(attrs={'class': INPUT_CLASSES, 'placeholder': 'Amount'})
    )
    remarks = forms.CharField(
        max_length=255, required=False, 
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'placeholder': 'Remarks'})
    )

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount is not None and amount < 0:
            raise forms.ValidationError("Amount cannot be negative.")
        return amount

# Formset for the Tour Expense Details Grid (GridView2 equivalent)
# This will be passed to the main TourIntimationCreateUpdateView
TourExpenseDetailFormSet = formset_factory(TourExpenseDetailForm, extra=0) # extra=0 as all rows are pre-populated from TourExpenseType

```

#### 4.3 Views (hr/views.py)

Views will be structured to handle the main form and all HTMX interactions for dynamic updates, keeping them minimal and delegating logic to models/forms.

```python
from django.views.generic import FormView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from django.shortcuts import render, get_object_or_404
from django.forms import formset_factory
from django.db.models import Q # For autocomplete search

from .models import (
    TourIntimation, Employee, BusinessGroup, TourAdvanceTemp,
    TourExpenseType, TourAdvanceDetail, TourAdvanceTransfer,
    Country, State, City
)
from .forms import (
    TourIntimationForm, TourAdvanceTempForm, TourExpenseDetailForm,
    TourExpenseDetailFormSet
)

# Constants for company and financial year IDs (replace with actual logic from user session)
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR_ID = 1

# --- Main Tour Intimation View ---

class TourIntimationCreateUpdateView(FormView):
    template_name = 'hr/tourintimation/create.html'
    form_class = TourIntimationForm
    success_url = reverse_lazy('tourintimation_create') # Redirects to self for clearing form/session data

    def get_initial(self):
        initial = super().get_initial()
        # Pre-populate employee_selection_type to 'Self' and set current user's EmpId
        if self.request.user.is_authenticated:
            # Assuming request.user.employee_profile.emp_id gives the current user's emp_id
            # For demonstration, let's use a dummy emp_id 'EMP001' or session_id
            current_user_emp_id = getattr(self.request.user, 'username', 'DUMMY_EMP_ID') # Placeholder
            initial['employee_selection_type'] = '0'
            initial['employee_emp_id'] = current_user_emp_id
            initial['other_employee_name'] = '' # Ensure this is empty for 'Self'
            
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pre-populate the Expense Details formset (GridView2 equivalent)
        expense_types = TourExpenseType.objects.all().order_by('id')
        initial_expense_data = []
        for expense_type in expense_types:
            # If editing, load existing amounts/remarks. For new, they are empty.
            # This would require linking to an existing TourIntimation instance for update scenario.
            # For this create view, it's always new.
            initial_expense_data.append({
                'expense_type_id': expense_type.id,
                'terms': expense_type.terms,
                'amount': None, # Start empty
                'remarks': '',   # Start empty
            })
        context['expense_formset'] = TourExpenseDetailFormSet(initial=initial_expense_data, prefix='expense')
        
        # Load temporary advance data for the current session/user
        session_id = getattr(self.request.user, 'username', self.request.session.session_key)
        context['advance_temps'] = TourAdvanceTemp.objects.filter(
            session_id=session_id,
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID
        ).order_by('-id') # Order by ID Desc as per original
        
        # Initialize forms for temporary advance operations (Add/Edit)
        context['advance_temp_form'] = TourAdvanceTempForm()
        
        return context

    def form_valid(self, form):
        user = self.request.user
        session_id = getattr(user, 'username', self.request.session.session_key) # Use username if logged in, else session key

        # Resolve employee based on selection type
        employee_selection_type = form.cleaned_data['employee_selection_type']
        
        employee_obj = None
        if employee_selection_type == '0': # Self
            # Assuming current user's emp_id is available
            # Placeholder: In a real app, this would be linked to User profile/Employee model
            current_user_emp_id = getattr(user, 'username', 'DUMMY_EMP_ID') 
            employee_obj = Employee.get_employee_by_code(current_user_emp_id, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
            if not employee_obj:
                form.add_error(None, 'Current user Employee profile not found.')
                return self.form_invalid(form)
            other_employee_emp_id = None # Clear this for 'Self'
        else: # Others
            other_employee_emp_id = form.cleaned_data['employee_emp_id']
            employee_obj = Employee.get_employee_by_code(other_employee_emp_id, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
            if not employee_obj:
                form.add_error('other_employee_name', 'Invalid Employee selected for "Others".')
                return self.form_invalid(form)


        # Validate WO/BG selection
        wo_group_selection_type = form.cleaned_data['wo_group_selection_type']
        wo_no = form.cleaned_data['wo_no']
        business_group = form.cleaned_data['business_group']

        # Ensure WO No and Business Group are correctly assigned based on radio button
        final_wo_no = None
        final_business_group = None

        if wo_group_selection_type == '0': # WO No selected
            final_wo_no = wo_no
        else: # BG Group selected
            final_business_group = business_group
            final_wo_no = 'NA' # As per original logic if BG group is selected

        # Process TourExpenseDetailFormSet
        expense_formset = TourExpenseDetailFormSet(self.request.POST, prefix='expense')
        if not expense_formset.is_valid():
            messages.error(self.request, 'Error in Advance Details. Please check entries.')
            return self.form_invalid(form)

        try:
            with transaction.atomic():
                # 1. Save main TourIntimation
                tour_intimation = form.save(commit=False)
                tour_intimation.session_id = session_id
                tour_intimation.comp_id = DEFAULT_COMP_ID
                tour_intimation.fin_year_id = DEFAULT_FIN_YEAR_ID
                tour_intimation.employee = employee_obj # Resolved Employee object
                tour_intimation.employee_type = int(employee_selection_type)
                tour_intimation.other_employee_emp_id = other_employee_emp_id
                tour_intimation.wo_no = final_wo_no
                tour_intimation.business_group = final_business_group
                tour_intimation.sys_date = timezone.now().date()
                tour_intimation.sys_time = timezone.now().time()

                tour_intimation.save()

                # 2. Save TourAdvanceDetails (from expense_formset / GridView2)
                for expense_form in expense_formset:
                    expense_type_id = expense_form.cleaned_data.get('expense_type_id')
                    amount = expense_form.cleaned_data.get('amount')
                    remarks = expense_form.cleaned_data.get('remarks')

                    if expense_type_id and amount is not None and amount > 0: # Only save if amount is entered and positive
                        expense_type = TourExpenseType.objects.get(id=expense_type_id)
                        TourAdvanceDetail.objects.create(
                            tour_intimation=tour_intimation,
                            expense_type=expense_type,
                            amount=amount,
                            remarks=remarks
                        )

                # 3. Transfer from TourAdvanceTemp to TourAdvanceTransfer (GridView1 data)
                temporary_advances = TourAdvanceTemp.objects.filter(
                    session_id=session_id,
                    comp_id=DEFAULT_COMP_ID,
                    fin_year_id=DEFAULT_FIN_YEAR_ID
                )
                for temp_advance in temporary_advances:
                    TourAdvanceTransfer.objects.create(
                        tour_intimation=tour_intimation,
                        employee=temp_advance.employee,
                        amount=temp_advance.amount,
                        remarks=temp_advance.remarks
                    )

                # 4. Clear temporary data for this session
                TourAdvanceTemp.clear_session_data(session_id, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)

                messages.success(self.request, 'Tour Intimation submitted successfully!')
                return super().form_valid(form)

        except Exception as e:
            messages.error(self.request, f'An error occurred during submission: {e}')
            return self.form_invalid(form)

# --- HTMX Endpoints for Autocomplete ---

class EmployeeAutocompleteView(ListView):
    model = Employee
    template_name = 'hr/partials/employee_autocomplete_results.html'
    context_object_name = 'employees'

    def get_queryset(self):
        query = self.request.GET.get('q', '')
        if query:
            # Search by EmpId or EmployeeName
            return Employee.objects.filter(
                Q(emp_id__icontains=query) | Q(employee_name__icontains=query)
            ).order_by('employee_name')[:10] # Limit results
        return Employee.objects.none()

# --- HTMX Endpoints for Chained Dropdowns (Location) ---

class StateDropdownView(ListView):
    model = State
    template_name = 'hr/partials/state_dropdown_options.html'
    context_object_name = 'states'

    def get_queryset(self):
        country_id = self.request.GET.get('place_of_tour_country')
        if country_id:
            return State.objects.filter(country_id=country_id).order_by('name')
        return State.objects.none()

class CityDropdownView(ListView):
    model = City
    template_name = 'hr/partials/city_dropdown_options.html'
    context_object_name = 'cities'

    def get_queryset(self):
        state_id = self.request.GET.get('place_of_tour_state')
        if state_id:
            return City.objects.filter(state_id=state_id).order_by('name')
        return City.objects.none()

# --- HTMX Endpoints for TourAdvanceTemp (GridView1 CRUD) ---

class TourAdvanceTempListView(ListView):
    model = TourAdvanceTemp
    template_name = 'hr/tourintimation/_advance_transfer_grid.html'
    context_object_name = 'advance_temps'

    def get_queryset(self):
        session_id = getattr(self.request.user, 'username', self.request.session.session_key)
        return TourAdvanceTemp.objects.filter(
            session_id=session_id,
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID
        ).order_by('-id')

class TourAdvanceTempCreateView(CreateView):
    model = TourAdvanceTemp
    form_class = TourAdvanceTempForm
    template_name = 'hr/partials/_blank_form.html' # A blank partial for hx-post
    # No success_url needed directly, as HTMX will handle triggering a refresh

    def form_valid(self, form):
        session_id = getattr(self.request.user, 'username', self.request.session.session_key)
        
        # Check for duplicate employee for this session
        existing_entry = TourAdvanceTemp.objects.filter(
            session_id=session_id,
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            employee=form.cleaned_data['employee'] # Check against the resolved employee object
        ).first()

        if existing_entry:
            # If duplicate, send a client-side alert via HTMX
            messages.error(self.request, 'Employee already exists in the advance transfer list.')
            return HttpResponse(status=400, headers={'HX-Trigger': 'showMessage'}) # Return 400 for form errors
        
        temp_advance = form.save(commit=False)
        temp_advance.session_id = session_id
        temp_advance.comp_id = DEFAULT_COMP_ID
        temp_advance.fin_year_id = DEFAULT_FIN_YEAR_ID
        temp_advance.save()
        
        messages.success(self.request, 'Advance entry added.')
        return HttpResponse(
            status=204, # No Content
            headers={'HX-Trigger': 'refreshAdvanceTempGrid, showMessage'}
        )

    def form_invalid(self, form):
        # Render the form again with errors
        messages.error(self.request, 'Please correct the errors in the advance entry form.')
        # Return the form partial with errors
        return render(self.request, 'hr/partials/_blank_form.html', {'form': form}, status=400) # Render form with errors

class TourAdvanceTempUpdateView(UpdateView):
    model = TourAdvanceTemp
    form_class = TourAdvanceTempForm
    template_name = 'hr/tourintimation/_advance_transfer_form_partial.html'
    context_object_name = 'temp_advance'

    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        return get_object_or_404(TourAdvanceTemp, pk=pk)

    def form_valid(self, form):
        session_id = getattr(self.request.user, 'username', self.request.session.session_key)

        # Check for duplicate employee if the employee is changed
        original_employee = self.get_object().employee
        new_employee = form.cleaned_data['employee']

        if original_employee != new_employee:
            existing_entry = TourAdvanceTemp.objects.filter(
                session_id=session_id,
                comp_id=DEFAULT_COMP_ID,
                fin_year_id=DEFAULT_FIN_YEAR_ID,
                employee=new_employee
            ).exclude(pk=self.object.pk).first() # Exclude current object

            if existing_entry:
                messages.error(self.request, 'Employee already exists in the advance transfer list.')
                return HttpResponse(status=400, headers={'HX-Trigger': 'showMessage'})

        form.save()
        messages.success(self.request, 'Advance entry updated.')
        return HttpResponse(
            status=204, # No Content
            headers={'HX-Trigger': 'refreshAdvanceTempGrid, showMessage'}
        )

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors in the advance entry form.')
        return render(self.request, self.template_name, {'form': form, 'temp_advance': self.object}, status=400)

class TourAdvanceTempDeleteView(DeleteView):
    model = TourAdvanceTemp
    template_name = 'hr/tourintimation/_advance_transfer_confirm_delete.html'
    context_object_name = 'temp_advance'

    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        return get_object_or_404(TourAdvanceTemp, pk=pk)

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        self.object.delete()
        messages.success(self.request, 'Advance entry deleted.')
        return HttpResponse(
            status=204, # No Content
            headers={'HX-Trigger': 'refreshAdvanceTempGrid, showMessage'}
        )

```

#### 4.4 Templates (hr/templates/hr/)

The templates are designed for HTMX and Alpine.js, extending `core/base.html` and using partials for dynamic updates.

**`hr/templates/hr/tourintimation/create.html`** (Main page for Tour Intimation form)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    employeeType: '{{ form.employee_selection_type.initial }}',
    woGroupType: '{{ form.wo_group_selection_type.initial }}',
    selectedEmployeeId: '{{ form.initial.employee_emp_id|default:"" }}',
    selectedEmployeeName: '{{ form.initial.other_employee_name|default:"" }}',
    clearSelectedEmployee() {
        this.selectedEmployeeId = '';
        this.selectedEmployeeName = '';
        document.getElementById('id_employee_emp_id').value = '';
    },
    selectEmployee(empId, empName) {
        this.selectedEmployeeId = empId;
        this.selectedEmployeeName = empName;
        document.getElementById('id_other_employee_name').value = empName; // Set visible text
        document.getElementById('id_employee_emp_id').value = empId;     // Set hidden ID
        document.getElementById('employee-autocomplete-results').innerHTML = ''; // Clear results
    }
}" x-init="
    $watch('employeeType', value => {
        if (value === '0') { // Self selected
            document.getElementById('id_other_employee_name').value = '';
            document.getElementById('id_employee_emp_id').value = '';
        }
    });
    // Initialize wo_no and business_group based on initial woGroupType
    $nextTick(() => {
        if (woGroupType === '0') { // WO No
            document.getElementById('id_business_group').value = '';
        } else { // BG Group
            document.getElementById('id_wo_no').value = '';
        }
    });
">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Create Tour Intimation</h2>

    <form method="post" class="space-y-6" novalidate>
        {% csrf_token %}
        
        <!-- Main Tour Details Section -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Tour Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                
                <!-- Employee Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Employee Selection</label>
                    <div class="flex items-center space-x-4">
                        {% for radio in form.employee_selection_type %}
                            <label class="inline-flex items-center">
                                <input type="radio" x-model="employeeType" name="{{ radio.name }}" value="{{ radio.choice_value }}" {% if radio.is_checked %}checked{% endif %} class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if form.employee_selection_type.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.employee_selection_type.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Employee Name for Others -->
                <div x-show="employeeType === '1'">
                    <label for="{{ form.other_employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.other_employee_name.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.other_employee_name }}
                    <input type="hidden" name="{{ form.employee_emp_id.name }}" id="{{ form.employee_emp_id.id_for_label }}" x-model="selectedEmployeeId">
                    <div id="employee-autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto"></div>
                    {% if form.other_employee_name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.other_employee_name.errors }}</p>
                    {% endif %}
                </div>

                <!-- Project Name -->
                <div>
                    <label for="{{ form.project_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.project_name.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.project_name }}
                    {% if form.project_name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.project_name.errors }}</p>
                    {% endif %}
                </div>

                <!-- WO No/BG Group Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">WO No / BG Group</label>
                    <div class="flex items-center space-x-4">
                        {% for radio in form.wo_group_selection_type %}
                            <label class="inline-flex items-center">
                                <input type="radio" x-model="woGroupType" name="{{ radio.name }}" value="{{ radio.choice_value }}" {% if radio.is_checked %}checked{% endif %} class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if form.wo_group_selection_type.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.wo_group_selection_type.errors }}</p>
                    {% endif %}
                </div>

                <!-- WO No -->
                <div x-show="woGroupType === '0'">
                    <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.wo_no.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.wo_no }}
                    {% if form.wo_no.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Business Group -->
                <div x-show="woGroupType === '1'">
                    <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.business_group.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.business_group }}
                    {% if form.business_group.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.business_group.errors }}</p>
                    {% endif %}
                </div>

                <!-- Place of Tour -->
                <div>
                    <label for="{{ form.place_of_tour_country.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Country <span class="text-red-500">*</span>
                    </label>
                    {{ form.place_of_tour_country }}
                    {% if form.place_of_tour_country.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_country.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.place_of_tour_state.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        State <span class="text-red-500">*</span>
                    </label>
                    {{ form.place_of_tour_state }}
                    {% if form.place_of_tour_state.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_state.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.place_of_tour_city.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        City <span class="text-red-500">*</span>
                    </label>
                    {{ form.place_of_tour_city }}
                    {% if form.place_of_tour_city.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_city.errors }}</p>
                    {% endif %}
                </div>

                <!-- Tour Start Date/Time -->
                <div>
                    <label for="{{ form.tour_start_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour Start Date <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_start_date }}
                    {% if form.tour_start_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_start_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.tour_start_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour Start Time <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_start_time }}
                    {% if form.tour_start_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_start_time.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Tour End Date/Time -->
                <div>
                    <label for="{{ form.tour_end_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour End Date <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_end_date }}
                    {% if form.tour_end_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_end_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.tour_end_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour End Time <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_end_time }}
                    {% if form.tour_end_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_end_time.errors }}</p>
                    {% endif %}
                </div>

                <!-- No. of Days -->
                <div>
                    <label for="{{ form.no_of_days.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.no_of_days.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.no_of_days }}
                    {% if form.no_of_days.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.no_of_days.errors }}</p>
                    {% endif %}
                </div>

                <!-- Name & Address of Accommodation Service Provider -->
                <div class="col-span-1 md:col-span-2">
                    <label for="{{ form.name_address_ser_provider.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.name_address_ser_provider.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.name_address_ser_provider }}
                    {% if form.name_address_ser_provider.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.name_address_ser_provider.errors }}</p>
                    {% endif %}
                </div>

                <!-- Contact Person -->
                <div>
                    <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.contact_person.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.contact_person }}
                    {% if form.contact_person.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Contact No -->
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.contact_no.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.contact_no }}
                    {% if form.contact_no.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>
                    {% endif %}
                </div>

                <!-- Email -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.email.label }}
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.email.errors }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Tabs Section (Advance Details & Advance Trans. To) -->
        <div x-data="{ activeTab: 'advanceDetails' }" class="bg-white shadow-md rounded-lg p-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button type="button" @click="activeTab = 'advanceDetails'" 
                            :class="activeTab === 'advanceDetails' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Details
                    </button>
                    <button type="button" @click="activeTab = 'advanceTransTo'" 
                            :class="activeTab === 'advanceTransTo' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Trans. To
                    </button>
                </nav>
            </div>

            <!-- Advance Details Tab Content -->
            <div x-show="activeTab === 'advanceDetails'" class="pt-6">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Advance Details</h3>
                <div class="overflow-x-auto rounded-md border border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{ expense_formset.management_form }}
                            {% for form in expense_formset %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ form.expense_type_id }}
                                    {{ form.terms }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ form.amount }}
                                    {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ form.remarks }}
                                    {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                            {% if expense_formset.non_field_errors %}
                                <tr><td colspan="4" class="px-6 py-4 text-red-500 text-sm">{{ expense_formset.non_field_errors }}</td></tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Advance Trans. To Tab Content -->
            <div x-show="activeTab === 'advanceTransTo'" class="pt-6">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Advance Trans. To</h3>
                
                <div id="advance-temp-grid-container"
                     hx-trigger="load, refreshAdvanceTempGrid from:body"
                     hx-get="{% url 'tourintimation_advance_temp_list' %}"
                     hx-swap="innerHTML">
                    <!-- Temporary Advance Grid will be loaded here via HTMX -->
                    <div class="text-center py-4">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-500">Loading Advance Transfers...</p>
                    </div>
                </div>

                <!-- Add New Advance Temp Form (initially hidden, shown in modal) -->
                <div id="advance-temp-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
                     _="on click if event.target.id == 'advance-temp-modal' remove .hidden from me">
                    <div id="advance-temp-modal-content" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full">
                        <!-- Content loaded by HTMX for Add/Edit/Delete -->
                    </div>
                </div>

            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center mt-6">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-300 ease-in-out">
                Submit Tour Intimation
            </button>
        </div>
        
        <!-- Form errors for non-field errors or errors from previous submissions -->
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

    </form>
</div>

<!-- Global message display (for Django messages and HTMX alerts) -->
<div id="messages-container" class="fixed top-4 right-4 z-50">
    {% for message in messages %}
        <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded relative mb-2" role="alert"
             x-data="{ show: true }" x-init="setTimeout(() => show = false, 5000)" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <strong class="font-bold">{{ message.tags|capfirst }}!</strong>
            <span class="block sm:inline">{{ message }}</span>
        </div>
    {% endfor %}
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is initialized globally via base.html
    // Specific functions for autocomplete
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'employee-autocomplete-results') {
            // Re-bind click event for dynamically loaded autocomplete results
            event.detail.target.querySelectorAll('.autocomplete-item').forEach(item => {
                item.addEventListener('click', function() {
                    const empId = this.dataset.empId;
                    const empName = this.dataset.empName;
                    Alpine.$data(document.getElementById('id_other_employee_name')).selectEmployee(empId, empName);
                });
            });
        }
        if (event.detail.target.id === 'employee-autocomplete-results-advance-temp') {
            event.detail.target.querySelectorAll('.autocomplete-item').forEach(item => {
                item.addEventListener('click', function() {
                    const empId = this.dataset.empId;
                    const empName = this.dataset.empName;
                    // Assuming advance-temp-form has a similar Alpine context or target specific elements
                    document.getElementById('id_employee_full_name').value = empName;
                    document.getElementById('id_employee_emp_id').value = empId;
                    document.getElementById('employee-autocomplete-results-advance-temp').innerHTML = ''; // Clear results
                });
            });
        }
    });

    // Custom event listener for showing messages globally via HTMX
    document.body.addEventListener('showMessage', function(event) {
        // HTMX will automatically add messages to the global Django message storage if returned via headers
        // This handler ensures immediate display if needed, but Django messages usually handle this.
    });

</script>
{% endblock %}
```

**`hr/templates/hr/partials/employee_autocomplete_results.html`** (Partial for Employee Autocomplete)

```html
{% if employees %}
    {% for employee in employees %}
        <div class="py-2 px-4 cursor-pointer hover:bg-gray-100 autocomplete-item"
             data-emp-id="{{ employee.emp_id }}"
             data-emp-name="{{ employee.employee_name }} [{{ employee.emp_id }}]">
            {{ employee.employee_name }} [{{ employee.emp_id }}]
        </div>
    {% endfor %}
{% else %}
    <div class="py-2 px-4 text-gray-500">No results found.</div>
{% endif %}
```

**`hr/templates/hr/partials/state_dropdown_options.html`** (Partial for Chained State Dropdown)

```html
<select name="place_of_tour_state" id="id_place_of_tour_state" 
        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        hx-get="{% url 'tourintimation_locations_cities' %}"
        hx-target="#id_place_of_tour_city"
        hx-trigger="change"
        hx-include="this"
        hx-swap="outerHTML">
    <option value="">Select</option>
    {% for state in states %}
        <option value="{{ state.id }}">{{ state.name }}</option>
    {% endfor %}
</select>
{% if form.place_of_tour_state.errors %}
    <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_state.errors }}</p>
{% endif %}
```

**`hr/templates/hr/partials/city_dropdown_options.html`** (Partial for Chained City Dropdown)

```html
<select name="place_of_tour_city" id="id_place_of_tour_city" 
        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    <option value="">Select</option>
    {% for city in cities %}
        <option value="{{ city.id }}">{{ city.name }}</option>
    {% endfor %}
</select>
{% if form.place_of_tour_city.errors %}
    <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_city.errors }}</p>
{% endif %}
```

**`hr/templates/hr/tourintimation/_advance_transfer_grid.html`** (Partial for Advance Transfer DataTables)

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <div class="p-4 flex justify-between items-center">
        <h4 class="text-md font-semibold text-gray-700">Current Advance Transfers</h4>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md"
            hx-get="{% url 'tourintimation_advance_temp_add' %}"
            hx-target="#advance-temp-modal-content"
            hx-trigger="click"
            _="on click add .is-active to #advance-temp-modal">
            Add New Advance
        </button>
    </div>
    <div class="p-4">
        <table id="advanceTempTable" class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                    <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                    <th class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if advance_temps %}
                    {% for temp in advance_temps %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ temp.employee.employee_name }} [{{ temp.employee.emp_id }}]</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ temp.amount|floatformat:2 }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ temp.remarks|default:"-" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <button 
                                    class="text-indigo-600 hover:text-indigo-900 mx-1"
                                    hx-get="{% url 'tourintimation_advance_temp_edit' temp.pk %}"
                                    hx-target="#advance-temp-modal-content"
                                    hx-trigger="click"
                                    _="on click add .is-active to #advance-temp-modal">
                                    Edit
                                </button>
                                <button 
                                    class="text-red-600 hover:text-red-900 mx-1"
                                    hx-get="{% url 'tourintimation_advance_temp_delete' temp.pk %}"
                                    hx-target="#advance-temp-modal-content"
                                    hx-trigger="click"
                                    _="on click add .is-active to #advance-temp-modal">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">No advance transfers added yet.</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<script>
    // Ensure DataTable is initialized only once or re-initialized correctly
    // Destroy existing DataTable if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#advanceTempTable')) {
        $('#advanceTempTable').DataTable().destroy();
    }
    // Initialize DataTables
    $(document).ready(function() {
        $('#advanceTempTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, -1], [5, 10, 25, "All"]],
            "searching": false, // Disable search for this simple temporary grid
            "paging": true,
            "info": false,
            "columnDefs": [
                { "orderable": false, "targets": [4] }, // Disable sorting for Actions column
                { "width": "5%", "targets": 0 },
                { "width": "30%", "targets": 1 },
                { "width": "15%", "targets": 2 },
                { "width": "40%", "targets": 3 },
                { "width": "10%", "targets": 4 }
            ]
        });
    });
</script>
```

**`hr/templates/hr/tourintimation/_advance_transfer_form_partial.html`** (Partial for Add/Edit Advance Transfer Form)

```html
<div x-data="{ selectedEmployeeId: '{{ form.initial.employee_emp_id|default:"" }}', selectedEmployeeName: '{{ form.initial.employee_full_name|default:"" }}' }" class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ temp_advance|yesno:'Edit,Add' }} Advance Transfer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('advance-temp-modal').classList.add('hidden'); } else { console.log('Error, modal stays open'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.employee_full_name.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.employee_full_name }}
                <input type="hidden" name="{{ form.employee_emp_id.name }}" id="{{ form.employee_emp_id.id_for_label }}" x-model="selectedEmployeeId">
                <div id="employee-autocomplete-results-advance-temp" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto"></div>
                {% if form.employee_full_name.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.amount.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.amount }}
                {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
            </div>
            
            <div>
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remarks.label }}
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click add .hidden to #advance-temp-modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`hr/templates/hr/tourintimation/_advance_transfer_confirm_delete.html`** (Partial for Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700">Are you sure you want to delete the advance entry for <strong>{{ temp_advance.employee.employee_name }} ({{ temp_advance.amount|floatformat:2 }})</strong>?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click add .hidden to #advance-temp-modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{% url 'tourintimation_advance_temp_delete' temp_advance.pk %}"
            hx-swap="none"
            hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('advance-temp-modal').classList.add('hidden'); }">
            Delete
        </button>
    </div>
</div>
```
**`hr/templates/hr/partials/_blank_form.html`** (Used by `TourAdvanceTempCreateView` to return errors only)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Add Advance Transfer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('advance-temp-modal').classList.add('hidden'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.employee_full_name.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.employee_full_name }}
                <input type="hidden" name="{{ form.employee_emp_id.name }}" id="{{ form.employee_emp_id.id_for_label }}" x-model="selectedEmployeeId">
                <div id="employee-autocomplete-results-advance-temp" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto"></div>
                {% if form.employee_full_name.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.amount.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.amount }}
                {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
            </div>
            
            <div>
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remarks.label }}
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click add .hidden to #advance-temp-modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (hr/urls.py)

Define URL patterns to map requests to the new Django views.

```python
from django.urls import path
from .views import (
    TourIntimationCreateUpdateView,
    EmployeeAutocompleteView,
    StateDropdownView, CityDropdownView,
    TourAdvanceTempListView, TourAdvanceTempCreateView, TourAdvanceTempUpdateView, TourAdvanceTempDeleteView
)

urlpatterns = [
    # Main Tour Intimation Form
    path('tourintimation/create/', TourIntimationCreateUpdateView.as_view(), name='tourintimation_create'),

    # HTMX Endpoints for Autocomplete
    path('employees/autocomplete/', EmployeeAutocompleteView.as_view(), name='employees_autocomplete'),

    # HTMX Endpoints for Chained Location Dropdowns
    path('locations/states/', StateDropdownView.as_view(), name='tourintimation_locations_states'),
    path('locations/cities/', CityDropdownView.as_view(), name='tourintimation_locations_cities'),

    # HTMX Endpoints for Temporary Advance Grid (CRUD)
    path('tourintimation/advance_temp/list/', TourAdvanceTempListView.as_view(), name='tourintimation_advance_temp_list'),
    path('tourintimation/advance_temp/add/', TourAdvanceTempCreateView.as_view(), name='tourintimation_advance_temp_add'),
    path('tourintimation/advance_temp/edit/<int:pk>/', TourAdvanceTempUpdateView.as_view(), name='tourintimation_advance_temp_edit'),
    path('tourintimation/advance_temp/delete/<int:pk>/', TourAdvanceTempDeleteView.as_view(), name='tourintimation_advance_temp_delete'),
]
```

#### 4.6 Tests (hr/tests.py)

Comprehensive tests ensure the reliability of the migration.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.test.client import RequestFactory
from django.http import HttpResponse

from .models import (
    Employee, BusinessGroup, Country, State, City, TourExpenseType,
    TourIntimation, TourAdvanceTemp, TourAdvanceDetail, TourAdvanceTransfer
)
from .forms import TourIntimationForm, TourAdvanceTempForm, TourExpenseDetailFormSet
from .views import DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID
import datetime

# Helper to add session to request for testing
def add_session_to_request(request):
    middleware = SessionMiddleware(lambda: HttpResponse())
    middleware.process_request(request)
    request.session.save()
    return request

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common lookup data
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra', country=cls.country)
        cls.city = City.objects.create(id=1, name='Mumbai', state=cls.state)
        cls.business_group = BusinessGroup.objects.create(id=101, symbol='DEV_BG')
        cls.employee_self = Employee.objects.create(
            emp_id='EMP001', employee_name='John Doe', title='Mr.',
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        cls.employee_other = Employee.objects.create(
            emp_id='EMP002', employee_name='Jane Smith', title='Ms.',
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        cls.expense_type_travel = TourExpenseType.objects.create(id=1, terms='Travel Expense')
        cls.expense_type_food = TourExpenseType.objects.create(id=2, terms='Food Expense')

    def test_employee_creation(self):
        emp = Employee.objects.get(emp_id='EMP001')
        self.assertEqual(emp.employee_name, 'John Doe')
        self.assertEqual(str(emp), 'Mr. John Doe [EMP001]')

    def test_employee_get_emp_id_from_full_name(self):
        full_name = 'Mr. John Doe [EMP001]'
        emp_id = Employee.get_emp_id_from_full_name(full_name)
        self.assertEqual(emp_id, 'EMP001')

        full_name_no_id = 'John Doe'
        emp_id_no_id = Employee.get_emp_id_from_full_name(full_name_no_id)
        self.assertIsNone(emp_id_no_id)

    def test_employee_get_employee_by_code(self):
        emp = Employee.get_employee_by_code('EMP001', DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertIsNotNone(emp)
        self.assertEqual(emp.employee_name, 'John Doe')

        emp_not_found = Employee.get_employee_by_code('NONEXISTENT', DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertIsNone(emp_not_found)

    def test_tour_intimation_creation_self(self):
        # Mock user to simulate session_id for 'Self'
        mock_user = self.employee_self
        mock_user.username = self.employee_self.emp_id # Assign username for session_id logic

        tour = TourIntimation.objects.create(
            session_id=mock_user.username,
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            employee_type=0, # Self
            employee=self.employee_self,
            project_name='Internal Project',
            wo_no='NA', # Not applicable if BG group is selected, but if WO No was primary, this would be it
            tour_start_date=datetime.date.today(),
            tour_start_time=datetime.time(9, 0),
            tour_end_date=datetime.date.today() + datetime.timedelta(days=2),
            tour_end_time=datetime.time(17, 0),
            no_of_days=3,
            name_address_ser_provider='Hotel XYZ, City',
            contact_person='John Doe',
            contact_no='**********',
            place_of_tour_country=self.country,
            place_of_tour_state=self.state,
            place_of_tour_city=self.city
        )
        self.assertIsNotNone(tour.pk)
        self.assertEqual(tour.ti_no, '0001')
        self.assertEqual(tour.employee, self.employee_self)

    def test_tour_intimation_creation_others(self):
        # Mock user to simulate session_id for 'Others'
        mock_user = self.employee_self
        mock_user.username = self.employee_self.emp_id # Assign username for session_id logic

        tour = TourIntimation.objects.create(
            session_id=mock_user.username,
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            employee_type=1, # Others
            employee=self.employee_other, # The employee for whom the intimation is
            project_name='External Project',
            business_group=self.business_group,
            tour_start_date=datetime.date.today(),
            tour_start_time=datetime.time(9, 0),
            tour_end_date=datetime.date.today() + datetime.timedelta(days=2),
            tour_end_time=datetime.time(17, 0),
            no_of_days=3,
            name_address_ser_provider='Guest House ABC, Town',
            contact_person='Jane Smith',
            contact_no='**********',
            place_of_tour_country=self.country,
            place_of_tour_state=self.state,
            place_of_tour_city=self.city
        )
        self.assertIsNotNone(tour.pk)
        self.assertEqual(tour.ti_no, '0002') # Should increment
        self.assertEqual(tour.employee, self.employee_other)

    def test_tour_advance_temp_crud(self):
        session_id = 'test_session_123'
        temp_advance = TourAdvanceTemp.objects.create(
            session_id=session_id,
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            employee=self.employee_other,
            amount=1500.00,
            remarks='Test advance'
        )
        self.assertEqual(TourAdvanceTemp.objects.count(), 1)
        self.assertEqual(temp_advance.employee.employee_name, 'Jane Smith')

        temp_advance.amount = 2000.00
        temp_advance.save()
        self.assertEqual(TourAdvanceTemp.objects.get(pk=temp_advance.pk).amount, 2000.00)

        temp_advance.delete()
        self.assertEqual(TourAdvanceTemp.objects.count(), 0)

    def test_clear_session_data(self):
        session_id = 'test_session_to_clear'
        TourAdvanceTemp.objects.create(session_id=session_id, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID, employee=self.employee_other, amount=100.00)
        TourAdvanceTemp.objects.create(session_id=session_id, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID, employee=self.employee_self, amount=200.00)
        
        self.assertEqual(TourAdvanceTemp.objects.filter(session_id=session_id).count(), 2)
        TourAdvanceTemp.clear_session_data(session_id, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(TourAdvanceTemp.objects.filter(session_id=session_id).count(), 0)

class ViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common lookup data
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra', country=cls.country)
        cls.city = City.objects.create(id=1, name='Mumbai', state=cls.state)
        cls.business_group = BusinessGroup.objects.create(id=101, symbol='DEV_BG')
        cls.employee_self = Employee.objects.create(
            emp_id='EMP001', employee_name='John Doe', title='Mr.',
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        cls.employee_other = Employee.objects.create(
            emp_id='EMP002', employee_name='Jane Smith', title='Ms.',
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        cls.expense_type_travel = TourExpenseType.objects.create(id=1, terms='Travel Expense')
        cls.expense_type_food = TourExpenseType.objects.create(id=2, terms='Food Expense')

    def setUp(self):
        self.client = Client()
        self.factory = RequestFactory()
        # Simulate logged-in user for session_id logic
        self.user = self.employee_self # Assign an employee instance to user
        self.user.username = self.employee_self.emp_id # Match username to emp_id for session_id logic

    def test_create_tour_intimation_get(self):
        request = self.factory.get(reverse('tourintimation_create'))
        request.user = self.user
        request = add_session_to_request(request) # Add session
        response = TourIntimationCreateUpdateView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/create.html')
        self.assertIn('form', response.context_data)
        self.assertIn('expense_formset', response.context_data)
        self.assertIn('advance_temps', response.context_data)

    def test_create_tour_intimation_post_self(self):
        initial_advance_temps_count = TourAdvanceTemp.objects.count()
        TourAdvanceTemp.objects.create(session_id=self.user.username, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID, employee=self.employee_other, amount=100)
        
        post_data = {
            'employee_selection_type': '0', # Self
            'project_name': 'New Self Project',
            'wo_group_selection_type': '0', # WO No
            'wo_no': 'WO12345',
            'place_of_tour_country': self.country.id,
            'place_of_tour_state': self.state.id,
            'place_of_tour_city': self.city.id,
            'tour_start_date': datetime.date.today().strftime('%Y-%m-%d'),
            'tour_start_time': '09:00',
            'tour_end_date': (datetime.date.today() + datetime.timedelta(days=2)).strftime('%Y-%m-%d'),
            'tour_end_time': '17:00',
            'no_of_days': 3,
            'name_address_ser_provider': 'Self Hotel',
            'contact_person': 'Self Contact',
            'contact_no': '**********',
            'email': '<EMAIL>',
            
            # Expense Formset data
            'expense-TOTAL_FORMS': '2',
            'expense-INITIAL_FORMS': '2',
            'expense-MIN_NUM_FORMS': '0',
            'expense-MAX_NUM_FORMS': '',
            'expense-0-expense_type_id': self.expense_type_travel.id,
            'expense-0-terms': self.expense_type_travel.terms,
            'expense-0-amount': '100.50',
            'expense-0-remarks': 'Travel to site',
            'expense-1-expense_type_id': self.expense_type_food.id,
            'expense-1-terms': self.expense_type_food.terms,
            'expense-1-amount': '50.25',
            'expense-1-remarks': 'Lunch',
        }
        
        request = self.factory.post(reverse('tourintimation_create'), post_data)
        request.user = self.user
        request = add_session_to_request(request)
        response = TourIntimationCreateUpdateView.as_view()(request)
        
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertEqual(TourIntimation.objects.count(), 1)
        self.assertEqual(TourAdvanceDetail.objects.count(), 2)
        self.assertEqual(TourAdvanceTransfer.objects.count(), 1) # Transferred from temp
        self.assertEqual(TourAdvanceTemp.objects.count(), 0) # Temp cleared

        new_ti = TourIntimation.objects.first()
        self.assertEqual(new_ti.employee, self.employee_self)
        self.assertEqual(new_ti.project_name, 'New Self Project')
        self.assertEqual(new_ti.wo_no, 'WO12345')

    def test_create_tour_intimation_post_others(self):
        post_data = {
            'employee_selection_type': '1', # Others
            'other_employee_name': f'{self.employee_other.employee_name} [{self.employee_other.emp_id}]',
            'employee_emp_id': self.employee_other.emp_id,
            'project_name': 'New Other Project',
            'wo_group_selection_type': '1', # BG Group
            'business_group': self.business_group.id,
            'place_of_tour_country': self.country.id,
            'place_of_tour_state': self.state.id,
            'place_of_tour_city': self.city.id,
            'tour_start_date': datetime.date.today().strftime('%Y-%m-%d'),
            'tour_start_time': '09:00',
            'tour_end_date': (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
            'tour_end_time': '18:00',
            'no_of_days': 2,
            'name_address_ser_provider': 'Other Accommodation',
            'contact_person': 'Other Person',
            'contact_no': '**********',
            'email': '<EMAIL>',

            # Expense Formset data (empty for this test)
            'expense-TOTAL_FORMS': '2',
            'expense-INITIAL_FORMS': '2',
            'expense-MIN_NUM_FORMS': '0',
            'expense-MAX_NUM_FORMS': '',
            'expense-0-expense_type_id': self.expense_type_travel.id,
            'expense-0-terms': self.expense_type_travel.terms,
            'expense-0-amount': '', # No amount
            'expense-0-remarks': '',
            'expense-1-expense_type_id': self.expense_type_food.id,
            'expense-1-terms': self.expense_type_food.terms,
            'expense-1-amount': '', # No amount
            'expense-1-remarks': '',
        }
        
        request = self.factory.post(reverse('tourintimation_create'), post_data)
        request.user = self.user
        request = add_session_to_request(request)
        response = TourIntimationCreateUpdateView.as_view()(request)
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(TourIntimation.objects.count(), 1)
        self.assertEqual(TourAdvanceDetail.objects.count(), 0) # No amounts submitted
        self.assertEqual(TourAdvanceTransfer.objects.count(), 0) # No temp advances created

        new_ti = TourIntimation.objects.first()
        self.assertEqual(new_ti.employee, self.employee_other)
        self.assertEqual(new_ti.business_group, self.business_group)
        self.assertEqual(new_ti.wo_no, 'NA') # Should be NA if BG selected


    def test_employee_autocomplete_view(self):
        request = self.factory.get(reverse('employees_autocomplete'), {'q': 'John'})
        response = EmployeeAutocompleteView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe')
        self.assertNotContains(response, 'Jane Smith')

    def test_state_dropdown_view(self):
        request = self.factory.get(reverse('tourintimation_locations_states'), {'place_of_tour_country': self.country.id})
        response = StateDropdownView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Maharashtra')

    def test_city_dropdown_view(self):
        request = self.factory.get(reverse('tourintimation_locations_cities'), {'place_of_tour_state': self.state.id})
        response = CityDropdownView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Mumbai')

    def test_advance_temp_list_view(self):
        TourAdvanceTemp.objects.create(session_id=self.user.username, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID, employee=self.employee_other, amount=100)
        request = self.factory.get(reverse('tourintimation_advance_temp_list'))
        request.user = self.user
        request = add_session_to_request(request)
        response = TourAdvanceTempListView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Jane Smith')
        self.assertContains(response, '100.00')

    def test_advance_temp_create_view(self):
        post_data = {
            'employee_full_name': f'{self.employee_other.employee_name} [{self.employee_other.emp_id}]',
            'employee_emp_id': self.employee_other.emp_id,
            'amount': '500.00',
            'remarks': 'New advance for Jane'
        }
        request = self.factory.post(reverse('tourintimation_advance_temp_add'), post_data)
        request.user = self.user
        request = add_session_to_request(request)
        request.META['HTTP_HX_REQUEST'] = 'true' # Simulate HTMX request
        
        response = TourAdvanceTempCreateView.as_view()(request)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(TourAdvanceTemp.objects.count(), 1)
        self.assertEqual(TourAdvanceTemp.objects.first().amount, 500.00)

    def test_advance_temp_create_view_duplicate_employee(self):
        TourAdvanceTemp.objects.create(session_id=self.user.username, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID, employee=self.employee_other, amount=100)
        
        post_data = {
            'employee_full_name': f'{self.employee_other.employee_name} [{self.employee_other.emp_id}]',
            'employee_emp_id': self.employee_other.emp_id,
            'amount': '200.00',
            'remarks': 'Duplicate test'
        }
        request = self.factory.post(reverse('tourintimation_advance_temp_add'), post_data)
        request.user = self.user
        request = add_session_to_request(request)
        request.META['HTTP_HX_REQUEST'] = 'true'
        
        response = TourAdvanceTempCreateView.as_view()(request)
        self.assertEqual(response.status_code, 400) # Form error
        self.assertTrue('HX-Trigger' in response.headers) # Should trigger message
        self.assertContains(response, 'Employee already exists in the advance transfer list.', status_code=400) # Check for message in rendered content for 400

    def test_advance_temp_update_view(self):
        temp_advance = TourAdvanceTemp.objects.create(session_id=self.user.username, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID, employee=self.employee_other, amount=100)
        
        post_data = {
            'employee_full_name': f'{self.employee_other.employee_name} [{self.employee_other.emp_id}]',
            'employee_emp_id': self.employee_other.emp_id,
            'amount': '250.00',
            'remarks': 'Updated advance'
        }
        request = self.factory.post(reverse('tourintimation_advance_temp_edit', args=[temp_advance.pk]), post_data)
        request.user = self.user
        request = add_session_to_request(request)
        request.META['HTTP_HX_REQUEST'] = 'true'
        
        response = TourAdvanceTempUpdateView.as_view()(request, pk=temp_advance.pk)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(TourAdvanceTemp.objects.get(pk=temp_advance.pk).amount, 250.00)

    def test_advance_temp_delete_view(self):
        temp_advance = TourAdvanceTemp.objects.create(session_id=self.user.username, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID, employee=self.employee_other, amount=100)
        
        request = self.factory.delete(reverse('tourintimation_advance_temp_delete', args=[temp_advance.pk]))
        request.user = self.user
        request = add_session_to_request(request)
        request.META['HTTP_HX_REQUEST'] = 'true'
        
        response = TourAdvanceTempDeleteView.as_view()(request, pk=temp_advance.pk)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(TourAdvanceTemp.objects.count(), 0)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **Main Form (hr/tourintimation/create.html):**
    -   Alpine.js manages the `employeeType` and `woGroupType` radio button states to show/hide the `other_employee_name` textbox and `wo_no`/`business_group` dropdown.
    -   Alpine.js `x-model` binds selected employee ID and name to hidden/visible fields, ensuring correct data submission.
    -   The main form is a standard Django form submission, but the HTMX modals for sub-forms provide a dynamic experience.
    -   `@click` handlers on dropdowns use HTMX `hx-get` to load updated `State` and `City` options dynamically.
    -   Global message display is handled by Django's `messages` framework and Alpine.js for transition/fade-out effects.

-   **Employee Autocomplete:**
    -   `hx-get` on `id_other_employee_name` (and `id_employee_full_name` in advance temp form) triggers an HTMX request to `employees_autocomplete/` on `keyup changed delay:500ms`.
    -   Results are swapped into `#employee-autocomplete-results` (`hr/partials/employee_autocomplete_results.html`).
    -   Alpine.js manages `selectedEmployeeId` and `selectedEmployeeName` for the chosen autocomplete option.

-   **Location Dropdowns (Country, State, City):**
    -   `hx-get` on `id_place_of_tour_country` triggers loading `States` (`hr/partials/state_dropdown_options.html`) into `id_place_of_tour_state` on `change`.
    -   `hx-get` on `id_place_of_tour_state` triggers loading `Cities` (`hr/partials/city_dropdown_options.html`) into `id_place_of_tour_city` on `change`.
    -   `hx-swap="outerHTML"` ensures the entire `<select>` element is replaced, effectively refreshing its options.

-   **Advance Transfer Grid (DataTables + HTMX):**
    -   The entire grid section (`#advance-temp-grid-container`) in `create.html` uses `hx-trigger="load, refreshAdvanceTempGrid from:body"` and `hx-get="{% url 'tourintimation_advance_temp_list' %}"` to load the initial grid and refresh it after any CRUD operation.
    -   `_advance_transfer_grid.html` contains the DataTables setup for client-side sorting/pagination.
    -   "Add New Advance" button, "Edit" buttons, and "Delete" buttons in the grid use `hx-get` to load the corresponding partial form (`_advance_transfer_form_partial.html` or `_advance_transfer_confirm_delete.html`) into a central modal (`#advance-temp-modal-content`).
    -   Alpine.js manages the visibility of the modal (`x-data` on `modal` and `on click` events).
    -   Form submissions (Add/Edit/Delete) within the modal use `hx-post`/`hx-delete` with `hx-swap="none"`.
    -   Crucially, `hx-on::after-request` is used to check for successful HTMX responses (status `204 No Content`) and hide the modal, while `HX-Trigger: refreshAdvanceTempGrid` header instructs the main page to re-load the grid. Error responses (e.g., `400 Bad Request` from `form_invalid`) render the form again within the modal, showing errors.

---

## Final Notes

This comprehensive plan details the migration of your ASP.NET Tour Intimation module to a modern Django application. By leveraging AI-assisted automation, the focus is shifted from manual coding to systematic conversion. The architecture emphasizes the "Fat Model, Thin View" principle, utilizing Django's powerful ORM and class-based views. The frontend incorporates HTMX and Alpine.js for highly interactive, dynamic user experiences without complex JavaScript frameworks, complemented by DataTables for efficient data presentation. All components are designed to be testable, maintainable, and adhere to industry best practices, setting a strong foundation for future modernization efforts.