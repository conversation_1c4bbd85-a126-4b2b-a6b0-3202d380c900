This comprehensive modernization plan outlines the transition of your ASP.NET application, specifically the "Office Staff - Edit Details" functionality, to a modern Django-based solution. Our approach prioritizes automation-driven strategies, ensuring a smooth and efficient conversion while adhering to the highest standards of maintainability and performance.

We will focus on transforming your existing logic into a Django ecosystem leveraging:
*   **Fat Models, Thin Views:** Business logic resides predominantly in Django models, keeping views concise and focused on handling requests and responses.
*   **HTMX + Alpine.js:** For dynamic, interactive user interfaces without relying on complex JavaScript frameworks. This allows for partial page updates and fluid user experience.
*   **DataTables:** For robust, client-side table functionalities like searching, sorting, and pagination on all list views.
*   **Tailwind CSS:** For efficient and highly customizable styling.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we've identified the primary table `tblHR_OfficeStaff` and several lookup tables used for dropdown lists. The `SELECT` statements and control bindings reveal the key columns and their relationships.

**Main Table:** `tblHR_OfficeStaff`
*   `UserID` (Primary Key, integer)
*   `EmpId` (String, unique identifier for employee)
*   `OfferId` (String)
*   `FinYearId` (Integer)
*   `CompId` (Integer)
*   `Title` (String, e.g., Mr, Mrs, Miss)
*   `EmployeeName` (String)
*   `SwapCardNo` (Integer/String, Foreign Key to `tblHR_SwapCard`)
*   `Department` (Integer, Foreign Key to `tblHR_Departments`)
*   `BGGroup` (Integer, Foreign Key to `BusinessGroup`)
*   `DirectorsName` (Integer, Foreign Key to `tblHR_OfficeStaff` - self-referential)
*   `DeptHead` (Integer, Foreign Key to `tblHR_OfficeStaff` - self-referential)
*   `GroupLeader` (Integer, Foreign Key to `tblHR_OfficeStaff` - self-referential)
*   `Designation` (Integer, Foreign Key to `tblHR_Designation`)
*   `Grade` (Integer, Foreign Key to `tblHR_Grade`)
*   `MobileNo` (Integer/String, Foreign Key to `tblHR_CoporateMobileNo`)
*   `ContactNo` (String)
*   `CompanyEmail` (String)
*   `EmailId1` (String, ERP Email)
*   `ExtensionNo` (Integer, Foreign Key to `tblHR_IntercomExt`)
*   `JoiningDate` (DateTime)
*   `ResignationDate` (DateTime, Nullable)
*   `PhotoFileName` (String, Nullable)
*   `PhotoSize` (Integer, Nullable)
*   `PhotoContentType` (String, Nullable)
*   `PhotoData` (Binary, Nullable - actual file content stored in DB)
*   `PermanentAddress` (String)
*   `CorrespondenceAddress` (String)
*   `EmailId2` (String, Personal Email)
*   `DateOfBirth` (DateTime)
*   `Gender` (String, e.g., M, F)
*   `MartialStatus` (Integer, 1 for Married, 0 for Unmarried)
*   `BloodGroup` (String)
*   `Height` (String)
*   `Weight` (String)
*   `PhysicallyHandycapped` (Integer, 1 for Yes, 0 for No)
*   `Religion` (String)
*   `Cast` (String)
*   `EducationalQualification` (String)
*   `AdditionalQualification` (String)
*   `LastCompanyName` (String)
*   `WorkingDuration` (String)
*   `TotalExperience` (String)
*   `CurrentCTC` (String)
*   `CVFileName` (String, Nullable)
*   `CVSize` (Integer, Nullable)
*   `CVContentType` (String, Nullable)
*   `CVData` (Binary, Nullable - actual file content stored in DB)
*   `BankAccountNo` (String)
*   `PFNo` (String)
*   `PANNo` (String)
*   `PassPortNo` (String)
*   `ExpiryDate` (DateTime, Nullable - for Passport)
*   `AdditionalInformation` (String)

**Lookup Tables:**
*   `tblHR_Designation`: `Id`, `Type`, `Symbol`
*   `tblHR_Departments`: `Id`, `Symbol`
*   `BusinessGroup`: `Id`, `Name`, `Symbol`
*   `tblHR_Grade`: `Id`, `Description`, `Symbol`
*   `tblHR_IntercomExt`: `Id`, `ExtNo`
*   `tblHR_SwapCard`: `Id`, `SwapCardNo`
*   `tblHR_CoporateMobileNo`: `Id`, `MobileNo`

### Step 2: Identify Backend Functionality

**Task:** Determine the operations and business logic from the ASP.NET code.

**Instructions:**
The ASP.NET page is primarily an **Update** (Edit) form for an existing employee.

*   **Read:** An employee's full details are read from `tblHR_OfficeStaff` based on `EmpId` (from query string) and `CompId` (from session). This data populates a multi-tab form. Dropdown lists are dynamically populated from various lookup tables, with specific filtering logic for "Swap Card No" and "Corporate Mobile No" to exclude already assigned items while allowing the current employee's assigned item to remain selected.
*   **Update:** On submitting the form, all collected data is validated and then updated in the `tblHR_OfficeStaff` table. This includes handling binary file uploads for photo and CV.
*   **File Management:** Separate actions are available to remove (set to NULL) the stored photo and CV files.
*   **Validation:** Extensive validation rules are applied: required fields, email format validation, and date format validation.
*   **Navigation:** Internal tab navigation ("Next" buttons) and external redirection ("Cancel" buttons).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django template mapping.

**Instructions:**
The ASP.NET UI is structured with a `TabContainer` containing multiple `TabPanel`s.

*   **Layout:** A multi-tab form layout will be recreated using Alpine.js for client-side tab switching.
*   **Input Fields:**
    *   `TextBox` controls (`TxtEmpName`, `TxtContactNo`, `TxtMail`, `TxtPermanentAddress`, etc.) will map to Django `forms.TextInput` or `forms.Textarea` widgets.
    *   `DropDownList` controls (`DrpEmpTitle`, `DrpDesignation`, `DrpDepartment`, etc.) will map to Django `forms.Select` widgets.
    *   `RadioButton` controls (`RdbtnMarried`, `RdbtnUnmarried`, `RdbtnYes`, `RdbtnNo`) will map to Django `forms.RadioSelect` widgets.
    *   `FileUpload` controls (`FileUploadPhoto`, `FileUploadControl`) will map to Django `forms.FileField` widgets.
*   **Date Pickers:** `CalendarExtender` will be replaced by HTML5 `type="date"` input with polyfills or a simple JS date picker if needed, but for this migration, relying on browser native date input is sufficient.
*   **Buttons:** `asp:Button` and `asp:ImageButton` will be replaced by standard HTML `<button>` elements, with `hx-post` or `hx-get` attributes for HTMX-driven interactions where appropriate.
*   **Data Display:** `asp:Label` controls will be replaced by direct Django template variable rendering.

### Step 4: Generate Django Code

We will structure this under a Django application named `hr`.

#### 4.1 Models

**Task:** Create Django models based on the extracted database schema.

**Instructions:**
We'll define models for the main `OfficeStaff` table and all associated lookup tables. Each model will have `managed = False` and `db_table` set to ensure it maps directly to your existing database schema. Business logic like email validation and file clearing will be placed within the `OfficeStaff` model, adhering to the "fat model" principle.

```python
# hr/models.py
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import io # Used for handling binary file data

# Helper models for dropdowns (lookup tables)
class Designation(models.Model):
    # Maps to tblHR_Designation: Id, Type, Symbol
    id = models.IntegerField(db_column='Id', primary_key=True)
    designation_type = models.CharField(db_column='Type', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.designation_type} - {self.symbol}" if self.designation_type and self.symbol else f"ID: {self.id}"


class Department(models.Model):
    # Maps to tblHR_Departments: Id, Symbol (used as DeptName)
    id = models.IntegerField(db_column='Id', primary_key=True)
    dept_name = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.dept_name


class BusinessGroup(models.Model):
    # Maps to BusinessGroup: Id, Name, Symbol
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return f"{self.name} - {self.symbol}"


class Grade(models.Model):
    # Maps to tblHR_Grade: Id, Description, Symbol
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return f"{self.description} - {self.symbol}" if self.description and self.symbol else f"ID: {self.id}"


class IntercomExtension(models.Model):
    # Maps to tblHR_IntercomExt: Id, ExtNo
    id = models.IntegerField(db_column='Id', primary_key=True)
    ext_no = models.CharField(db_column='ExtNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_IntercomExt'
        verbose_name = 'Intercom Extension'
        verbose_name_plural = 'Intercom Extensions'

    def __str__(self):
        return self.ext_no


class SwapCard(models.Model):
    # Maps to tblHR_SwapCard: Id, SwapCardNo
    id = models.IntegerField(db_column='Id', primary_key=True)
    swap_card_no = models.CharField(db_column='SwapCardNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_SwapCard'
        verbose_name = 'Swap Card'
        verbose_name_plural = 'Swap Cards'

    def __str__(self):
        return self.swap_card_no


class CorporateMobileNo(models.Model):
    # Maps to tblHR_CoporateMobileNo: Id, MobileNo
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile No'
        verbose_name_plural = 'Corporate Mobile Nos'

    def __str__(self):
        return self.mobile_no

# Main OfficeStaff model
class OfficeStaff(models.Model):
    # Primary Key field
    userid = models.IntegerField(db_column='UserID', primary_key=True)
    
    # Official Info Tab Fields
    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True, verbose_name='Employee ID')
    offer_id = models.CharField(db_column='OfferId', max_length=50, blank=True, null=True, verbose_name='Offer ID')
    fin_year_id = models.IntegerField(db_column='FinYearId', verbose_name='Financial Year ID')
    comp_id = models.IntegerField(db_column='CompId', verbose_name='Company ID')
    
    TITLE_CHOICES = [
        ('Mr', 'Mr'), ('Mrs', 'Mrs'), ('Miss', 'Miss')
    ]
    title = models.CharField(db_column='Title', max_length=10, choices=TITLE_CHOICES, verbose_name='Title')
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, verbose_name='Name of Employee')
    
    # Foreign key to SwapCard. Related_name for reverse relationships.
    swap_card_no = models.ForeignKey(
        SwapCard, 
        models.DO_NOTHING, 
        db_column='SwapCardNo', 
        blank=True, 
        null=True, 
        verbose_name='Swap Card No',
        related_name='staff_members' 
    )
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', verbose_name='Department')
    bg_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroup', verbose_name='Business Group')
    
    # Self-referential Foreign Keys for organizational roles (Directors, Dept Heads, Group Leaders)
    directors_name = models.ForeignKey('self', models.DO_NOTHING, db_column='DirectorsName', blank=True, null=True, related_name='director_of_staff', verbose_name='Director of Dept.')
    dept_head = models.ForeignKey('self', models.DO_NOTHING, db_column='DeptHead', blank=True, null=True, related_name='head_of_department_staff', verbose_name='Dept. Head')
    group_leader = models.ForeignKey('self', models.DO_NOTHING, db_column='GroupLeader', blank=True, null=True, related_name='leader_of_group_staff', verbose_name='Group Leader')
    
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', verbose_name='Designation')
    grade = models.ForeignKey(Grade, models.DO_NOTHING, db_column='Grade', verbose_name='Grade')
    
    # Foreign key to CorporateMobileNo
    mobile_no = models.ForeignKey(
        CorporateMobileNo, 
        models.DO_NOTHING, 
        db_column='MobileNo', 
        blank=True, 
        null=True, 
        verbose_name='Corporate Mobile No.',
        related_name='staff_corp_mobiles' 
    )
    contact_no = models.CharField(db_column='ContactNo', max_length=50, verbose_name='Contact No.')
    company_email = models.EmailField(db_column='CompanyEmail', max_length=255, verbose_name='Company E-Mail')
    erp_mail = models.EmailField(db_column='EmailId1', max_length=255, blank=True, null=True, verbose_name='ERP E-Mail')
    extension_no = models.ForeignKey(IntercomExtension, models.DO_NOTHING, db_column='ExtensionNo', blank=True, null=True, verbose_name='Extension No.')
    joining_date = models.DateField(db_column='JoiningDate', verbose_name='Joining Date')
    resignation_date = models.DateField(db_column='ResignationDate', blank=True, null=True, verbose_name='Resignation Date')
    
    # Binary fields for file storage (as in original database)
    photo_file_name = models.CharField(db_column='PhotoFileName', max_length=255, blank=True, null=True, verbose_name='Photo File Name')
    photo_size = models.IntegerField(db_column='PhotoSize', blank=True, null=True, verbose_name='Photo Size (bytes)')
    photo_content_type = models.CharField(db_column='PhotoContentType', max_length=255, blank=True, null=True, verbose_name='Photo Content Type')
    photo_data = models.BinaryField(db_column='PhotoData', blank=True, null=True, verbose_name='Photo Data')


    # Personal Info Tab Fields
    permanent_address = models.TextField(db_column='PermanentAddress', verbose_name='Permanent Address')
    correspondence_address = models.TextField(db_column='CorrespondenceAddress', verbose_name='Correspondence Address')
    personal_email = models.EmailField(db_column='EmailId2', max_length=255, blank=True, null=True, verbose_name='Personal E-Mail')
    date_of_birth = models.DateField(db_column='DateOfBirth', verbose_name='Date Of Birth')
    
    GENDER_CHOICES = [
        ('M', 'Male'), ('F', 'Female')
    ]
    gender = models.CharField(db_column='Gender', max_length=1, choices=GENDER_CHOICES, verbose_name='Gender')
    
    # BooleanField maps 0/1 to False/True.
    marital_status = models.BooleanField(db_column='MartialStatus', verbose_name='Martial Status') 
    
    BLOOD_GROUP_CHOICES = [
        ('Select', 'Select'), ('Not Known', 'Not Known'), ('O+', 'O+'), ('O-', 'O-'),
        ('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'),
        ('AB+', 'AB+'), ('AB-', 'AB-')
    ]
    blood_group = models.CharField(db_column='BloodGroup', max_length=20, choices=BLOOD_GROUP_CHOICES, verbose_name='Blood Group')
    height = models.CharField(db_column='Height', max_length=50, blank=True, null=True, verbose_name='Height') 
    weight = models.CharField(db_column='Weight', max_length=50, blank=True, null=True, verbose_name='Weight') 
    
    physically_handicapped = models.BooleanField(db_column='PhysicallyHandycapped', verbose_name='Physically Handicapped')
    religion = models.CharField(db_column='Religion', max_length=100, blank=True, null=True, verbose_name='Religion')
    caste = models.CharField(db_column='Cast', max_length=100, blank=True, null=True, verbose_name='Caste')

    # Edu. Quali. & Work Experience Tab Fields
    educational_qualification = models.CharField(db_column='EducationalQualification', max_length=255, verbose_name='Educational Qualification')
    additional_qualification = models.TextField(db_column='AdditionalQualification', blank=True, null=True, verbose_name='Additional Qualification')
    last_company_name = models.CharField(db_column='LastCompanyName', max_length=255, blank=True, null=True, verbose_name='Last Company Name')
    working_duration = models.CharField(db_column='WorkingDuration', max_length=100, blank=True, null=True, verbose_name='Working Duration')
    total_experience = models.CharField(db_column='TotalExperience', max_length=100, blank=True, null=True, verbose_name='Total Experience')

    # Others Tab Fields
    current_ctc = models.CharField(db_column='CurrentCTC', max_length=100, blank=True, null=True, verbose_name='Current CTC') 
    bank_account_no = models.CharField(db_column='BankAccountNo', max_length=100, blank=True, null=True, verbose_name='Bank Account No.')
    pf_no = models.CharField(db_column='PFNo', max_length=100, blank=True, null=True, verbose_name='PF No.')
    pan_no = models.CharField(db_column='PANNo', max_length=100, blank=True, null=True, verbose_name='PAN No.')
    passport_no = models.CharField(db_column='PassPortNo', max_length=100, blank=True, null=True, verbose_name='Passport No.')
    expiry_date = models.DateField(db_column='ExpiryDate', blank=True, null=True, verbose_name='Expiry Date (Passport)')
    additional_information = models.TextField(db_column='AdditionalInformation', blank=True, null=True, verbose_name='Additional Information')
    
    cv_file_name = models.CharField(db_column='CVFileName', max_length=255, blank=True, null=True, verbose_name='CV File Name')
    cv_size = models.IntegerField(db_column='CVSize', blank=True, null=True, verbose_name='CV Size (bytes)')
    cv_content_type = models.CharField(db_column='CVContentType', max_length=255, blank=True, null=True, verbose_name='CV Content Type')
    cv_data = models.BinaryField(db_column='CVData', blank=True, null=True, verbose_name='CV Data')

    class Meta:
        managed = False # Crucial for mapping to existing DB
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff Member'
        verbose_name_plural = 'Office Staff Members'

    def __str__(self):
        return f"{self.title} {self.employee_name} ({self.emp_id})"

    # Business logic methods (Fat Model approach)
    def get_full_name(self):
        """Returns the full name including title."""
        return f"{self.title} {self.employee_name}"

    def clean_photo(self):
        """Removes photo data by setting relevant fields to None."""
        self.photo_file_name = None
        self.photo_size = None
        self.photo_content_type = None
        self.photo_data = None
        self.save(update_fields=['photo_file_name', 'photo_size', 'photo_content_type', 'photo_data'])

    def clean_cv(self):
        """Removes CV data by setting relevant fields to None."""
        self.cv_file_name = None
        self.cv_size = None
        self.cv_content_type = None
        self.cv_data = None
        self.save(update_fields=['cv_file_name', 'cv_size', 'cv_content_type', 'cv_data'])
    
    def validate_email_fields(self):
        """
        Custom model-level validation for multiple email fields.
        This mimics the `fun.EmailValidation` logic from the ASP.NET code.
        """
        email_fields = [
            (self.company_email, 'company_email', 'Company Email'),
            (self.erp_mail, 'erp_mail', 'ERP Email'),
            (self.personal_email, 'personal_email', 'Personal Email')
        ]
        
        for email_value, field_name, friendly_name in email_fields:
            if email_value: 
                try:
                    # Leverage Django's built-in email validator
                    models.EmailField().clean(email_value) 
                except ValidationError:
                    raise ValidationError(
                        _('%(friendly_name)s is not a valid email address.'),
                        params={'friendly_name': friendly_name},
                        code='invalid_email'
                    )

    def save(self, *args, **kwargs):
        """Overrides save to include model-level validations."""
        self.validate_email_fields()
        super().save(*args, **kwargs)

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
We'll create a `ModelForm` for the `OfficeStaff` model. This form will handle field mapping, widget customization (applying Tailwind CSS classes), and custom validation logic, especially for the unique dropdown population requirements for Swap Card and Mobile No. File upload fields will be handled separately, and the existing binary data for files will be preserved if no new file is uploaded.

```python
# hr/forms.py
from django import forms
from .models import (
    OfficeStaff, Designation, Department, BusinessGroup, Grade, IntercomExtension, 
    SwapCard, CorporateMobileNo
)
import io

class OfficeStaffForm(forms.ModelForm):
    # Custom form fields for specific rendering or complex logic
    # ChoiceField for radio buttons and dropdowns with specific choices
    title = forms.ChoiceField(
        choices=OfficeStaff.TITLE_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Title"
    )
    gender = forms.ChoiceField(
        choices=OfficeStaff.GENDER_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Gender"
    )
    # Boolean fields rendered as radio buttons
    marital_status = forms.BooleanField(
        required=False, # Required is handled by model validation
        widget=forms.RadioSelect(choices=[(True, 'Married'), (False, 'Unmarried')]),
        label="Martial Status"
    )
    physically_handicapped = forms.BooleanField(
        required=False,
        widget=forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')]),
        label="Physically Handicapped"
    )
    blood_group = forms.ChoiceField(
        choices=OfficeStaff.BLOOD_GROUP_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Blood Group"
    )

    # File Upload Fields: These are not directly mapped to BinaryField in model
    # They are used to accept file uploads and then manual handling in clean()
    photo_file = forms.FileField(
        required=False, 
        label="Upload Photo", 
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
        })
    )
    cv_file = forms.FileField(
        required=False, 
        label="Upload CV", 
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
        })
    )

    # Hidden fields to indicate if existing binary data should be preserved if no new file is uploaded
    _photo_data_unchanged = forms.CharField(widget=forms.HiddenInput(), required=False)
    _cv_data_unchanged = forms.CharField(widget=forms.HiddenInput(), required=False)


    class Meta:
        model = OfficeStaff
        fields = [
            # Official Info Tab
            'emp_id', 'offer_id', 'title', 'employee_name', 'swap_card_no', 
            'department', 'bg_group', 'directors_name', 'dept_head', 'group_leader',
            'designation', 'grade', 'mobile_no', 'contact_no', 'company_email',
            'erp_mail', 'extension_no', 'joining_date', 'resignation_date',
            'photo_file', '_photo_data_unchanged', # Custom file fields, original model fields will be hidden
            # Personal Info Tab
            'permanent_address', 'correspondence_address', 'personal_email', 'date_of_birth',
            'gender', 'marital_status', 'blood_group', 'height', 'weight',
            'physically_handicapped', 'religion', 'caste',
            # Edu. Quali. & Work Experience Tab
            'educational_qualification', 'additional_qualification', 'last_company_name',
            'working_duration', 'total_experience',
            # Others Tab
            'current_ctc', 'bank_account_no', 'pf_no', 'pan_no', 'passport_no',
            'expiry_date', 'additional_information',
            'cv_file', '_cv_data_unchanged', # Custom file fields, original model fields will be hidden
        ]
        # Common CSS class for all text inputs and select elements
        common_input_attrs = {'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
        # Specific widgets with custom styles
        widgets = {
            'emp_id': forms.TextInput(attrs={**common_input_attrs, 'readonly': 'readonly'}),
            'offer_id': forms.TextInput(attrs={**common_input_attrs, 'readonly': 'readonly'}),
            'employee_name': forms.TextInput(attrs={**common_input_attrs, 'style': 'width:300px'}),
            'contact_no': forms.TextInput(attrs=common_input_attrs),
            'company_email': forms.EmailInput(attrs={**common_input_attrs, 'style': 'width:200px'}),
            'erp_mail': forms.EmailInput(attrs=common_input_attrs),
            'joining_date': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}), 
            'resignation_date': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}),
            'permanent_address': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:374px; height:60px'}),
            'correspondence_address': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:408px; height:60px'}),
            'personal_email': forms.EmailInput(attrs={**common_input_attrs, 'style': 'width:200px'}),
            'date_of_birth': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}),
            'height': forms.TextInput(attrs=common_input_attrs),
            'weight': forms.TextInput(attrs=common_input_attrs),
            'religion': forms.TextInput(attrs=common_input_attrs),
            'caste': forms.TextInput(attrs=common_input_attrs),
            'educational_qualification': forms.TextInput(attrs=common_input_attrs),
            'additional_qualification': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:336px; height:68px'}),
            'last_company_name': forms.TextInput(attrs={**common_input_attrs, 'style': 'width:250px'}),
            'working_duration': forms.TextInput(attrs=common_input_attrs),
            'total_experience': forms.TextInput(attrs=common_input_attrs),
            'current_ctc': forms.TextInput(attrs=common_input_attrs),
            'bank_account_no': forms.TextInput(attrs=common_input_attrs),
            'pf_no': forms.TextInput(attrs=common_input_attrs),
            'pan_no': forms.TextInput(attrs=common_input_attrs),
            'passport_no': forms.TextInput(attrs=common_input_attrs),
            'expiry_date': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}),
            'additional_information': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:325px; height:66px'}),

            # Foreign Key dropdowns apply common_input_attrs
            'designation': forms.Select(attrs={**common_input_attrs, 'style': 'width:180px'}),
            'department': forms.Select(attrs=common_input_attrs),
            'bg_group': forms.Select(attrs=common_input_attrs),
            'directors_name': forms.Select(attrs=common_input_attrs),
            'dept_head': forms.Select(attrs={**common_input_attrs, 'style': 'width:180px'}),
            'group_leader': forms.Select(attrs=common_input_attrs),
            'grade': forms.Select(attrs=common_input_attrs),
            'swap_card_no': forms.Select(attrs=common_input_attrs),
            'mobile_no': forms.Select(attrs=common_input_attrs),
            'extension_no': forms.Select(attrs=common_input_attrs),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get('instance')
        comp_id = instance.comp_id if instance else None # Assuming comp_id from the instance
        
        # Populate self-referential dropdowns (Directors, Dept Heads, Group Leaders)
        # Assuming they should show all staff members for selection within the same company
        if comp_id:
            staff_members_qs = OfficeStaff.objects.filter(comp_id=comp_id).order_by('employee_name')
            self.fields['directors_name'].queryset = staff_members_qs
            self.fields['dept_head'].queryset = staff_members_qs
            self.fields['group_leader'].queryset = staff_members_qs
        else:
            # If no instance (e.g., in a create view, though this is an edit form),
            # provide an empty queryset or a default
            self.fields['directors_name'].queryset = OfficeStaff.objects.none()
            self.fields['dept_head'].queryset = OfficeStaff.objects.none()
            self.fields['group_leader'].queryset = OfficeStaff.objects.none()
        
        # Logic for Swap Card No and Corporate Mobile No dropdowns
        # The ASP.NET logic was: "Id='1' OR Id='[current_id]' OR Id NOT IN (select [FK_column] from tblHR_OfficeStaff where [FK_column] is not null AND CompId='[CompId]')"
        # This translates to: Include 'Not Applicable' (assuming ID=1), the current staff's assigned one, and all other unassigned ones.
        
        current_swap_card_id = instance.swap_card_no.id if instance and instance.swap_card_no else None
        current_mobile_no_id = instance.mobile_no.id if instance and instance.mobile_no else None

        # Get IDs of swap cards currently assigned to other staff members in the same company
        used_swap_card_ids = OfficeStaff.objects.filter(
            comp_id=comp_id, 
            swap_card_no__isnull=False
        ).exclude(pk=instance.pk).values_list('swap_card_no', flat=True)
        
        # Construct queryset: ID=1 (NA) OR current staff's assigned ID OR IDs not in used list
        swap_card_qs = SwapCard.objects.filter(id=1) 
        if current_swap_card_id:
            swap_card_qs = swap_card_qs.union(SwapCard.objects.filter(id=current_swap_card_id))
        swap_card_qs = swap_card_qs.union(SwapCard.objects.exclude(id__in=used_swap_card_ids))
        self.fields['swap_card_no'].queryset = swap_card_qs.distinct().order_by('id')

        # Get IDs of corporate mobile numbers currently assigned to other staff members
        used_mobile_no_ids = OfficeStaff.objects.filter(
            comp_id=comp_id, 
            mobile_no__isnull=False
        ).exclude(pk=instance.pk).values_list('mobile_no', flat=True)

        # Construct queryset for mobile numbers
        mobile_no_qs = CorporateMobileNo.objects.filter(id=1) 
        if current_mobile_no_id:
            mobile_no_qs = mobile_no_qs.union(CorporateMobileNo.objects.filter(id=current_mobile_no_id))
        mobile_no_qs = mobile_no_qs.union(CorporateMobileNo.objects.exclude(id__in=used_mobile_no_ids))
        self.fields['mobile_no'].queryset = mobile_no_qs.distinct().order_by('id')

        # Set initial values for boolean fields (marital_status, physically_handicapped)
        # Django's form typically handles this, but explicit initial setting ensures correctness
        if instance:
            self.fields['marital_status'].initial = instance.marital_status
            self.fields['physically_handicapped'].initial = instance.physically_handicapped

        # For file fields, ensure that if no new file is uploaded, the existing binary data is retained
        # This is managed by passing `_photo_data_unchanged` and `_cv_data_unchanged` initial values
        if self.instance.pk:
            if self.instance.photo_data:
                self.initial['_photo_data_unchanged'] = 'True'
            if self.instance.cv_data:
                self.initial['_cv_data_unchanged'] = 'True'
            
    def clean(self):
        cleaned_data = super().clean()
        
        # Handle file uploads and binary data storage (Photo)
        photo_file = cleaned_data.get('photo_file')
        if photo_file:
            # Read the uploaded file into binary data
            photo_data = photo_file.read()
            cleaned_data['photo_file_name'] = photo_file.name
            cleaned_data['photo_size'] = photo_file.size
            cleaned_data['photo_content_type'] = photo_file.content_type
            cleaned_data['photo_data'] = photo_data
        elif self.initial.get('_photo_data_unchanged') == 'True' and not photo_file and self.instance.photo_data:
            # If no new file was uploaded AND there was existing data, retain it
            cleaned_data['photo_file_name'] = self.instance.photo_file_name
            cleaned_data['photo_size'] = self.instance.photo_size
            cleaned_data['photo_content_type'] = self.instance.photo_content_type
            cleaned_data['photo_data'] = self.instance.photo_data
        else:
            # If no new file and no existing data, or if `_photo_data_unchanged` is not 'True' (e.g., file removed)
            cleaned_data['photo_file_name'] = None
            cleaned_data['photo_size'] = None
            cleaned_data['photo_content_type'] = None
            cleaned_data['photo_data'] = None

        # Handle file uploads and binary data storage (CV)
        cv_file = cleaned_data.get('cv_file')
        if cv_file:
            cv_data = cv_file.read()
            cleaned_data['cv_file_name'] = cv_file.name
            cleaned_data['cv_size'] = cv_file.size
            cleaned_data['cv_content_type'] = cv_file.content_type
            cleaned_data['cv_data'] = cv_data
        elif self.initial.get('_cv_data_unchanged') == 'True' and not cv_file and self.instance.cv_data:
            cleaned_data['cv_file_name'] = self.instance.cv_file_name
            cleaned_data['cv_size'] = self.instance.cv_size
            cleaned_data['cv_content_type'] = self.instance.cv_content_type
            cleaned_data['cv_data'] = self.instance.cv_data
        else:
            cleaned_data['cv_file_name'] = None
            cleaned_data['cv_size'] = None
            cleaned_data['cv_content_type'] = None
            cleaned_data['cv_data'] = None

        return cleaned_data

```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
Views will be kept thin, delegating complex logic to the form's `__init__` and `clean` methods, or directly to model methods. We'll use an `UpdateView` for the main employee editing page and `View` classes for file deletion and download. HTMX will be used for partial updates and success triggers.

```python
# hr/views.py
from django.views.generic import ListView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse, Http404
from django.shortcuts import get_object_or_404, redirect
from django.db import IntegrityError, transaction
import mimetypes
import io

from .models import OfficeStaff
from .forms import OfficeStaffForm

# Define the base URL for redirection after successful operations.
# This assumes an existing list page for staff members.
STAFF_LIST_URL = reverse_lazy('hr:officestaff_list') 

class OfficeStaffListView(ListView):
    """
    Displays a list of all office staff members. 
    This view serves as the target for redirection after an edit operation 
    and demonstrates the DataTables integration for list views.
    """
    model = OfficeStaff
    template_name = 'hr/officestaff/list.html'
    context_object_name = 'officestaffs'

    def get_queryset(self):
        """
        Filters the queryset by company ID, retrieved from session, 
        mimicking the original ASP.NET behavior.
        """
        # Assuming 'compid' is stored in the user's session
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        return super().get_queryset().filter(comp_id=comp_id).order_by('employee_name')


class OfficeStaffTablePartialView(ListView):
    """
    Returns the HTML content for the office staff DataTable,
    designed to be loaded dynamically via HTMX.
    """
    model = OfficeStaff
    template_name = 'hr/officestaff/_officestaff_table.html'
    context_object_name = 'officestaffs'

    def get_queryset(self):
        """
        Provides the queryset for the DataTables partial, similar to the main list view.
        """
        comp_id = self.request.session.get('compid', 1) 
        return super().get_queryset().filter(comp_id=comp_id).order_by('employee_name')


class OfficeStaffUpdateView(UpdateView):
    """
    Handles the editing of an existing office staff member's details.
    This view directly replaces the functionality of OfficeStaff_Edit_Details.aspx.cs.
    """
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr/officestaff/edit.html'
    success_url = STAFF_LIST_URL 

    def get_object(self, queryset=None):
        """
        Retrieves the OfficeStaff instance to be edited.
        It uses 'EmpId' from the URL query string, as in the original ASP.NET.
        """
        emp_id = self.kwargs.get('emp_id') # Get emp_id from URL path
        if not emp_id:
            raise Http404("Employee ID not found in URL.")
        
        comp_id = self.request.session.get('compid', 1) # Get company ID from session

        obj = get_object_or_404(OfficeStaff, emp_id=emp_id, comp_id=comp_id)
        
        # Store initial tab index from session to restore user's last position
        # This will be passed to Alpine.js in the template
        self.initial_tab_index = self.request.session.get('tab_index', 0)
        return obj

    def get_context_data(self, **kwargs):
        """
        Adds additional data to the template context, such as current file names
        and the initial tab index for Alpine.js.
        """
        context = super().get_context_data(**kwargs)
        instance = self.object # The staff member object
        
        context['current_photo_file_name'] = instance.photo_file_name
        context['current_cv_file_name'] = instance.cv_file_name
        context['staff_userid'] = instance.userid # Pass PK for file deletion/download links
        context['initial_tab_index'] = self.initial_tab_index # For Alpine.js to set active tab

        return context

    def form_valid(self, form):
        """
        Handles valid form submissions. Saves the form and provides user feedback.
        Employs HTMX triggers for client-side updates without full page reloads.
        """
        try:
            # Use a transaction to ensure atomicity for database operations
            with transaction.atomic():
                response = super().form_valid(form)
                messages.success(self.request, 'Staff details updated successfully.')
                
                # For HTMX requests, return 204 No Content with a trigger header.
                # This informs the client-side to potentially refresh a list or close a modal.
                if self.request.headers.get('HX-Request'):
                    return HttpResponse(status=204, headers={'HX-Trigger': 'staffUpdated'})
                
                return response
        except IntegrityError as e:
            messages.error(self.request, f"A database error occurred: {e}")
            return self.form_invalid(form)
        except Exception as e:
            messages.error(self.request, f"An unexpected error occurred during update: {e}")
            return self.form_invalid(form)

    def form_invalid(self, form):
        """
        Handles invalid form submissions. Re-renders the form with error messages.
        For HTMX requests, the form partial is returned with errors.
        """
        messages.error(self.request, 'Please correct the errors below.')
        if self.request.headers.get('HX-Request'):
            # If HTMX, render the form again with errors (which HTMX will swap in)
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class FileDeleteView(View):
    """
    Handles the deletion of uploaded photo or CV files by setting their database fields to NULL.
    Corresponds to the ImageButton2_Click and ImageButton1_Click methods in ASP.NET.
    """
    def post(self, request, pk, file_type):
        """
        Processes POST requests to remove a file.
        Uses fat model methods (`clean_photo`, `clean_cv`) for business logic.
        """
        staff = get_object_or_404(OfficeStaff, userid=pk)
        
        if file_type == 'photo':
            staff.clean_photo() # Business logic handled by model method
            messages.success(request, 'Photo removed successfully.')
        elif file_type == 'cv':
            staff.clean_cv() # Business logic handled by model method
            messages.success(request, 'CV removed successfully.')
        else:
            raise Http404("Invalid file type for deletion.")

        # For HTMX, return 204 No Content and trigger an event to refresh the UI.
        # The template will re-fetch the form container, showing the updated state.
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'fileRemoved'})
        
        # If not an HTMX request, redirect back to the edit page
        return redirect('hr:officestaff_edit', emp_id=staff.emp_id)


class DownloadFileView(View):
    """
    Handles the download of the CV file stored in the database.
    Corresponds to the HyperLink1.NavigateUrl logic in ASP.NET.
    """
    def get(self, request, pk, file_type):
        """
        Retrieves binary file data and returns it as a FileResponse.
        """
        staff = get_object_or_404(OfficeStaff, userid=pk)

        # Only CV download is explicitly handled in the ASP.NET code
        if file_type == 'cv':
            file_data = staff.cv_data
            file_name = staff.cv_file_name
            content_type = staff.cv_content_type
        else:
            raise Http404("Invalid file type for download.")

        if not file_data:
            raise Http404("File not found for download.")
        
        # Guess content type if not explicitly stored or if it's generic
        if not content_type:
            content_type, _ = mimetypes.guess_type(file_name)
            if not content_type:
                content_type = 'application/octet-stream' # Default if type cannot be guessed

        # Use io.BytesIO to wrap binary data for FileResponse
        response = FileResponse(io.BytesIO(file_data), content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring DRY principles and HTMX/Alpine.js integration.

**Instructions:**
The main edit form (`edit.html`) will use Alpine.js for client-side tab management, eliminating the need for server-side tab logic. It will include placeholders for `form` errors and success messages (handled by `messages` framework and `base.html`). The list view will dynamically load its table content via HTMX, which will be rendered by a separate partial template (`_officestaff_table.html`) to enable DataTables. File upload and deletion interactions will be driven by HTMX.

```html
<!-- hr/officestaff/list.html (Main list page template, for DataTables) -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Office Staff Members</h2>
        {# A conceptual button for adding new staff, redirecting to a create view if implemented #}
        {# This specific ASP.NET page was for EDIT, not CREATE, but a list would have ADD #}
        <a href="#" 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
            Add New Staff Member
        </a>
    </div>
    
    {# Container for the DataTable, loaded via HTMX #}
    <div id="officestaffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'hr:officestaff_table' %}" {# URL to fetch the table partial #}
         hx-swap="innerHTML" {# Replaces the content inside this div #}
         class="min-h-[200px] flex items-center justify-center bg-white rounded-lg shadow-md">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading staff data...</p>
        </div>
    </div>
    
    {# Modal structure (managed by Alpine.js) for edit/delete forms #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false" {# Listens for 'close-modal' event to hide #}
         x-on:open-modal.window="showModal = true" {# Listens for 'open-modal' event to show #}
         x-on:click.self="showModal = false"> {# Click outside modal content to close #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-4 overflow-y-auto max-h-[90vh]" 
             x-show="showModal" 
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
             <!-- Content loaded by HTMX (e.g., _officestaff_form.html, _officestaff_confirm_delete.html) -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# JavaScript block for Alpine.js and HTMX event listeners #}
<script>
    // Listen for custom HTMX events to refresh the staff list or close modals
    document.body.addEventListener('staffUpdated', function() {
        // Trigger a refresh of the staff table after a successful update/create/delete
        htmx.trigger(document.body, 'refreshOfficeStaffList');
        // Close the modal
        htmx.trigger('#modal', 'close-modal');
    });

    // Initialize Alpine.js after DOM is fully loaded or when HTMX swaps content
    document.addEventListener('DOMContentLoaded', () => {
        if (typeof Alpine !== 'undefined') {
            Alpine.start();
        }
    });
</script>
{% endblock %}

```

```html
<!-- hr/officestaff/_officestaff_table.html (Partial template for DataTables, loaded via HTMX) -->
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="officestaffTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for staff in officestaffs %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.emp_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.get_full_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.department }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.designation }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.joining_date|date:"d-M-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    {# HTMX buttons to open edit/delete forms in a modal #}
                    <button 
                       hx-get="{% url 'hr:officestaff_edit' emp_id=staff.emp_id %}" 
                       hx-target="#modalContent" 
                       hx-trigger="click" 
                       _="on click send open-modal to #modal"
                       class="text-indigo-600 hover:text-indigo-900 mr-4 cursor-pointer focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md">Edit</button>
                    {# Assuming a delete view like OfficeStaffDeleteView exists, it would open a confirmation modal #}
                    {# The provided instructions require a DeleteView, so we'll link to it conceptually. #}
                    {# For this specific ASP.NET page (edit), delete wasn't a main button, but part of a list typically #}
                    <button 
                       hx-get="{% url 'hr:officestaff_delete' pk=staff.userid %}" {# Assuming userid as PK for delete #}
                       hx-target="#modalContent"
                       hx-trigger="click"
                       _="on click send open-modal to #modal"
                       class="text-red-600 hover:text-red-900 cursor-pointer focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-md">Delete</button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No staff members found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# Initialize DataTables on the loaded table #}
<script>
    $(document).ready(function() {
        $('#officestaffTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 10
        });
    });
</script>

```

```html
<!-- hr/officestaff/edit.html (Main template for editing staff details, includes Alpine.js for tabs) -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Staff - Edit Details: {{ object.get_full_name }}</h2>

    {# Alpine.js component for managing tab state #}
    <div x-data="{ 
        activeTab: {{ initial_tab_index }},
        currentPhotoFile: '{{ current_photo_file_name|default:'' }}', 
        currentCvFile: '{{ current_cv_file_name|default:'' }}', 
        staffUserId: '{{ staff_userid }}',
        init() {
            // Retrieve last active tab from session storage or default to 0
            const storedTabIndex = sessionStorage.getItem('staff_edit_active_tab');
            if (storedTabIndex !== null) {
                this.activeTab = parseInt(storedTabIndex);
            }
            // Watch for changes in activeTab and save to session storage
            this.$watch('activeTab', value => {
                sessionStorage.setItem('staff_edit_active_tab', value);
            });
        }
    }" 
    id="staffEditFormContainer"
    {# This div will be reloaded via HTMX when a file is removed to update display #}
    {# hx-target="this" hx-swap="outerHTML" is typically for dynamic loading of the form itself #}
    {# For a static page, direct JS refresh or HTMX triggering a refresh of the page is alternative #}
    {# To reload just the form part, we'd wrap the <form> in a hx-targetable div #}
    >
        
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button type="button" @click="activeTab = 0" :class="{'border-indigo-500 text-indigo-600': activeTab === 0, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 0}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Official Info
                </button>
                <button type="button" @click="activeTab = 1" :class="{'border-indigo-500 text-indigo-600': activeTab === 1, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 1}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Personal Info
                </button>
                <button type="button" @click="activeTab = 2" :class="{'border-indigo-500 text-indigo-600': activeTab === 2, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 2}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Edu. Quali. & Work Experience
                </button>
                <button type="button" @click="activeTab = 3" :class="{'border-indigo-500 text-indigo-600': activeTab === 3, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 3}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Others
                </button>
            </nav>
        </div>

        {# The entire form is wrapped to allow multipart/form-data for file uploads #}
        <form method="post" enctype="multipart/form-data" 
              hx-post="{% url 'hr:officestaff_edit' emp_id=object.emp_id %}" 
              hx-swap="none" {# Swap none on successful POST; HTMX will handle triggers/redirects #}
              hx-trigger="submit">
            {% csrf_token %}
            
            {# Display non-field errors and field-specific errors #}
            {% if form.errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
                <strong class="font-bold">Validation Error!</strong>
                <span class="block sm:inline">Please correct the following errors:</span>
                <ul class="mt-2 list-disc list-inside">
                    {% for field in form %}
                        {% if field.errors %}
                            <li>{{ field.label }}: {{ field.errors|join:", " }}</li>
                        {% endif %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}

            <!-- Tab Content Container -->
            <div class="mt-6 p-6 bg-white rounded-lg shadow-md">
                <!-- Official Info Tab Content -->
                <div x-show="activeTab === 0" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {# Emp Id and Offer Id are read-only #}
                        <div>
                            <label for="{{ form.emp_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.emp_id.label }}</label>
                            {{ form.emp_id }}
                        </div>
                        <div>
                            <label for="{{ form.offer_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.offer_id.label }}</label>
                            {{ form.offer_id }}
                        </div>
                        <div class="md:col-span-2 flex flex-wrap items-center space-x-2">
                            <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.title.label }}</label>
                            {{ form.title }}
                            <label for="{{ form.employee_name.id_for_label }}" class="sr-only">{{ form.employee_name.label }}</label> {# Screen reader only label #}
                            {{ form.employee_name }}
                            {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                        </div>
                        {# Remaining fields in Official Info #}
                        <div><label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.designation.label }}</label>{{ form.designation }}{% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.department.label }}</label>{{ form.department }}{% if form.department.errors %}<p class="text-red-500 text-xs mt-1">{{ form.department.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.swap_card_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.swap_card_no.label }}</label>{{ form.swap_card_no }}{% if form.swap_card_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.swap_card_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.directors_name.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.directors_name.label }}</label>{{ form.directors_name }}</div>
                        <div><label for="{{ form.bg_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.bg_group.label }}</label>{{ form.bg_group }}{% if form.bg_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bg_group.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.group_leader.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.group_leader.label }}</label>{{ form.group_leader }}</div>
                        <div><label for="{{ form.dept_head.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.dept_head.label }}</label>{{ form.dept_head }}</div>
                        <div><label for="{{ form.mobile_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.mobile_no.label }}</label>{{ form.mobile_no }}{% if form.mobile_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.mobile_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.grade.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.grade.label }}</label>{{ form.grade }}{% if form.grade.errors %}<p class="text-red-500 text-xs mt-1">{{ form.grade.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.contact_no.label }}</label>{{ form.contact_no }}{% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.company_email.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.company_email.label }}</label>{{ form.company_email }}{% if form.company_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.company_email.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.erp_mail.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.erp_mail.label }}</label>{{ form.erp_mail }}{% if form.erp_mail.errors %}<p class="text-red-500 text-xs mt-1">{{ form.erp_mail.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.extension_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.extension_no.label }}</label>{{ form.extension_no }}{% if form.extension_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.extension_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.joining_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.joining_date.label }}</label>{{ form.joining_date }}{% if form.joining_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.joining_date.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.resignation_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.resignation_date.label }}</label>{{ form.resignation_date }}{% if form.resignation_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.resignation_date.errors }}</p>{% endif %}
                            <p class="text-red-500 text-xs mt-1">* Reset Swap Card No and Corp. Mobile No. to Not Applicable.</p>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="button" @click="activeTab = 1" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Next</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>

                <!-- Personal Info Tab Content -->
                <div x-show="activeTab === 1" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="{{ form.permanent_address.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.permanent_address.label }}</label>
                            {{ form.permanent_address }}
                            {% if form.permanent_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.permanent_address.errors }}</p>{% endif %}
                        </div>
                        <div class="md:col-span-2">
                            <label for="{{ form.correspondence_address.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.correspondence_address.label }}</label>
                            {{ form.correspondence_address }}
                            {% if form.correspondence_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.correspondence_address.errors }}</p>{% endif %}
                        </div>
                        <div><label for="{{ form.personal_email.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.personal_email.label }}</label>{{ form.personal_email }}{% if form.personal_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.personal_email.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.date_of_birth.label }}</label>{{ form.date_of_birth }}{% if form.date_of_birth.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_of_birth.errors }}</p>{% endif %}</div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ form.gender.label }}</label>
                            <div class="flex space-x-4">
                                {% for radio in form.gender %}
                                    <label class="inline-flex items-center">
                                        {{ radio.tag }}
                                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                            {% if form.gender.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gender.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ form.marital_status.label }}</label>
                            <div class="flex space-x-4">
                                {% for radio in form.marital_status %}
                                    <label class="inline-flex items-center">
                                        {{ radio.tag }}
                                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                            {% if form.marital_status.errors %}<p class="text-red-500 text-xs mt-1">{{ form.marital_status.errors }}</p>{% endif %}
                        </div>
                        <div><label for="{{ form.blood_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.blood_group.label }}</label>{{ form.blood_group }}{% if form.blood_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.blood_group.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.height.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.height.label }}</label>{{ form.height }}{% if form.height.errors %}<p class="text-red-500 text-xs mt-1">{{ form.height.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.weight.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.weight.label }}</label>{{ form.weight }}{% if form.weight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.weight.errors }}</p>{% endif %}</div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ form.physically_handicapped.label }}</label>
                            <div class="flex space-x-4">
                                {% for radio in form.physically_handicapped %}
                                    <label class="inline-flex items-center">
                                        {{ radio.tag }}
                                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                            {% if form.physically_handicapped.errors %}<p class="text-red-500 text-xs mt-1">{{ form.physically_handicapped.errors }}</p>{% endif %}
                        </div>
                        <div><label for="{{ form.religion.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.religion.label }}</label>{{ form.religion }}{% if form.religion.errors %}<p class="text-red-500 text-xs mt-1">{{ form.religion.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.caste.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.caste.label }}</label>{{ form.caste }}{% if form.caste.errors %}<p class="text-red-500 text-xs mt-1">{{ form.caste.errors }}</p>{% endif %}</div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="button" @click="activeTab = 2" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Next</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>

                <!-- Edu. Quali. & Work Experience Tab Content -->
                <div x-show="activeTab === 2" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><label for="{{ form.educational_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.educational_qualification.label }}</label>{{ form.educational_qualification }}{% if form.educational_qualification.errors %}<p class="text-red-500 text-xs mt-1">{{ form.educational_qualification.errors }}</p>{% endif %}</div>
                        <div class="md:col-span-2"><label for="{{ form.additional_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.additional_qualification.label }}</label>{{ form.additional_qualification }}</div>
                        <div><label for="{{ form.last_company_name.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.last_company_name.label }}</label>{{ form.last_company_name }}</div>
                        <div><label for="{{ form.total_experience.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.total_experience.label }}</label>{{ form.total_experience }}</div>
                        <div><label for="{{ form.working_duration.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.working_duration.label }}</label>{{ form.working_duration }}</div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="button" @click="activeTab = 3" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Next</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>

                <!-- Others Tab Content -->
                <div x-show="activeTab === 3" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><label for="{{ form.current_ctc.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.current_ctc.label }}</label>{{ form.current_ctc }}</div>
                        <div><label for="{{ form.bank_account_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.bank_account_no.label }}</label>{{ form.bank_account_no }}</div>
                        <div><label for="{{ form.pf_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.pf_no.label }}</label>{{ form.pf_no }}</div>
                        <div><label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.pan_no.label }}</label>{{ form.pan_no }}</div>
                        <div><label for="{{ form.passport_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.passport_no.label }}</label>{{ form.passport_no }}</div>
                        <div><label for="{{ form.expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.expiry_date.label }}</label>{{ form.expiry_date }}{% if form.expiry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.expiry_date.errors }}</p>{% endif %}</div>
                        <div class="md:col-span-2"><label for="{{ form.additional_information.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.additional_information.label }}</label>{{ form.additional_information }}</div>
                        
                        {# Photo Upload/Management #}
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">{{ form.photo_file.label }}</label>
                            {% if current_photo_file_name %}
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="text-sm text-gray-900">{{ current_photo_file_name }}</span>
                                    {# HTMX button to trigger file deletion #}
                                    <button 
                                        type="button" 
                                        hx-post="{% url 'hr:file_delete' pk=staff_userid file_type='photo' %}" 
                                        hx-target="#staffEditFormContainer" {# Target the whole container to refresh #}
                                        hx-swap="outerHTML" {# Replace the whole container #}
                                        hx-confirm="Are you sure you want to remove the photo?"
                                        class="text-red-500 hover:text-red-700 text-xs focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg> Remove
                                    </button>
                                </div>
                                <span class="text-xs text-gray-500 block mt-1">Upload new file to replace.</span>
                            {% else %}
                                {{ form.photo_file }}
                            {% endif %}
                            {{ form._photo_data_unchanged }} {# Hidden field to preserve existing data #}
                        </div>

                        {# CV Upload/Management #}
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">{{ form.cv_file.label }}</label>
                            {% if current_cv_file_name %}
                                <div class="flex items-center space-x-2 mt-2">
                                    {# Link to download existing CV #}
                                    <a href="{% url 'hr:file_download' pk=staff_userid file_type='cv' %}" class="text-blue-600 hover:underline text-sm">{{ current_cv_file_name }}</a>
                                    {# HTMX button to trigger file deletion #}
                                    <button 
                                        type="button" 
                                        hx-post="{% url 'hr:file_delete' pk=staff_userid file_type='cv' %}" 
                                        hx-target="#staffEditFormContainer" {# Target the whole container to refresh #}
                                        hx-swap="outerHTML" {# Replace the whole container #}
                                        hx-confirm="Are you sure you want to remove the CV?"
                                        class="text-red-500 hover:text-red-700 text-xs focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg> Remove
                                    </button>
                                </div>
                                <span class="text-xs text-gray-500 block mt-1">Upload new file to replace.</span>
                            {% else %}
                                {{ form.cv_file }}
                            {% endif %}
                            {{ form._cv_data_unchanged }} {# Hidden field to preserve existing data #}
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="submit" class="redbox bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Update</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js initialization for tab management and HTMX event listeners #}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for the staff edit form
        Alpine.data('staffEditForm', () => ({
            activeTab: 0, // Default active tab
            // The init function runs when the component is initialized
            init() {
                // Restore last active tab from session storage if available
                const storedTabIndex = sessionStorage.getItem('staff_edit_active_tab');
                if (storedTabIndex !== null) {
                    this.activeTab = parseInt(storedTabIndex);
                } else {
                    // Otherwise, use the value passed from Django context (initial_tab_index)
                    this.activeTab = parseInt('{{ initial_tab_index }}');
                }

                // Watch for changes in activeTab and save to session storage
                this.$watch('activeTab', value => {
                    sessionStorage.setItem('staff_edit_active_tab', value);
                });
            }
        }));
    });

    // HTMX event listener for when the form content needs to be reloaded (e.g., after file removal)
    document.body.addEventListener('fileRemoved', function(event) {
        // When a file is removed, HTMX will swap the `staffEditFormContainer` div with updated content
        // This event ensures Alpine.js re-initializes on the new content.
        // The hx-target/hx-swap on the file removal buttons ensure this div is updated.
        console.log('File removed, re-initializing Alpine.js on form container.');
        // Re-init Alpine on the new content if it's not handled automatically by hx-swap
        const formContainer = document.getElementById('staffEditFormContainer');
        if (formContainer) {
            Alpine.init(formContainer);
        }
    });

    // HTMX event listener for success messages after form submission
    document.body.addEventListener('staffUpdated', function(event) {
        // If the form is submitted via HTMX, this event is triggered.
        // It provides a client-side indication of success.
        console.log('Staff details updated successfully.');
        // Redirect to the staff list page after update
        window.location.href = '{% url "hr:officestaff_list" %}';
    });
</script>
{% endblock %}

```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
We'll create URL paths for the staff list view, the main edit page (using `emp_id` as the identifier), and specific endpoints for HTMX-driven file deletion and download operations.

```python
# hr/urls.py
from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffTablePartialView, OfficeStaffUpdateView,
    FileDeleteView, DownloadFileView
)

app_name = 'hr' # Define the app namespace

urlpatterns = [
    # URL for displaying the list of office staff members (for DataTables)
    path('officestaff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    
    # URL for fetching the DataTables content dynamically via HTMX
    path('officestaff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),

    # URL for editing an existing office staff member.
    # Uses 'emp_id' from the URL path as a unique identifier.
    path('officestaff/edit/<str:emp_id>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    
    # HTMX-specific endpoint for deleting associated files (photo/CV)
    # Uses 'pk' (the userid from the model) and 'file_type' (e.g., 'photo', 'cv')
    path('officestaff/<int:pk>/delete_file/<str:file_type>/', FileDeleteView.as_view(), name='file_delete'),
    
    # HTMX-specific endpoint for downloading associated files (e.g., CV)
    path('officestaff/<int:pk>/download_file/<str:file_type>/', DownloadFileView.as_view(), name='file_download'),

    # Conceptual URL for a staff creation page, if one were to be implemented
    # path('officestaff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
]

```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
Tests will cover model method functionality (like email validation, file clearing) and view interactions (GET/POST requests for the update form, file operations). We aim for high test coverage to ensure reliability and facilitate future changes. We'll set up test data to mimic the scenarios encountered in the original ASP.NET application, including complex dropdown population logic.

```python       
# hr/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
import io # Used for simulating file uploads

from django.core.exceptions import ValidationError
from .models import (
    OfficeStaff, Designation, Department, BusinessGroup, Grade, IntercomExtension,
    SwapCard, CorporateMobileNo
)

# --- Unit Tests for Lookup Models ---
class LookupModelTest(TestCase):
    """
    Tests for the integrity and string representation of lookup models.
    """
    @classmethod
    def setUpTestData(cls):
        # Create minimal data for lookup tables
        Designation.objects.create(id=1, designation_type='Mgr', symbol='Manager')
        Department.objects.create(id=1, dept_name='IT')
        BusinessGroup.objects.create(id=1, name='Head Office', symbol='HO')
        Grade.objects.create(id=1, description='Junior', symbol='J')
        IntercomExtension.objects.create(id=1, ext_no='101')
        # Special case for SwapCard and CorporateMobileNo: ID=1 is 'Not Applicable'
        SwapCard.objects.create(id=1, swap_card_no='NA') 
        SwapCard.objects.create(id=2, swap_card_no='SC001')
        CorporateMobileNo.objects.create(id=1, mobile_no='NA') 
        CorporateMobileNo.objects.create(id=2, mobile_no='9999900001')

    def test_designation_creation_and_str(self):
        obj = Designation.objects.get(id=1)
        self.assertEqual(obj.designation_type, 'Mgr')
        self.assertEqual(str(obj), 'Mgr - Manager')

    def test_department_creation_and_str(self):
        obj = Department.objects.get(id=1)
        self.assertEqual(obj.dept_name, 'IT')
        self.assertEqual(str(obj), 'IT')

    def test_swap_card_str(self):
        obj = SwapCard.objects.get(id=1)
        self.assertEqual(str(obj), 'NA')


# --- Unit Tests for OfficeStaff Model ---
class OfficeStaffModelTest(TestCase):
    """
    Tests for OfficeStaff model's methods and field integrity.
    """
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup objects for foreign keys
        cls.designation = Designation.objects.create(id=10, designation_type='Sr.', symbol='Senior')
        cls.department = Department.objects.create(id=20, dept_name='Sales')
        cls.bg_group = BusinessGroup.objects.create(id=30, name='Branch', symbol='BR')
        cls.grade = Grade.objects.create(id=40, description='Mid', symbol='M')
        cls.extension = IntercomExtension.objects.create(id=50, ext_no='202')
        cls.swap_card_na = SwapCard.objects.create(id=1, swap_card_no='NA')
        cls.swap_card_assigned = SwapCard.objects.create(id=100, swap_card_no='SC_ASSIGNED')
        cls.mobile_na = CorporateMobileNo.objects.create(id=1, mobile_no='NA')
        cls.mobile_assigned = CorporateMobileNo.objects.create(id=200, mobile_no='9999900001')

        # Create a sample OfficeStaff instance
        cls.staff_member_1 = OfficeStaff.objects.create(
            userid=1,
            emp_id='EMP001',
            offer_id='OFF001',
            fin_year_id=2023,
            comp_id=1,
            title='Mr',
            employee_name='John Doe',
            department=cls.department,
            designation=cls.designation,
            bg_group=cls.bg_group,
            grade=cls.grade,
            contact_no='1234567890',
            company_email='<EMAIL>',
            joining_date=date(2020, 1, 15),
            permanent_address='123 Main St',
            correspondence_address='123 Main St',
            date_of_birth=date(1990, 5, 20),
            gender='M',
            marital_status=False, # Unmarried
            blood_group='O+',
            height='175cm',
            weight='70kg',
            physically_handicapped=False,
            religion='Christian',
            caste='General',
            educational_qualification='B.E.',
            current_ctc='500000',
            bank_account_no='12345',
            pf_no='PF123',
            pan_no='PAN123',
            passport_no='PS123',
            extension_no=cls.extension,
            swap_card_no=cls.swap_card_assigned,
            mobile_no=cls.mobile_assigned,
            photo_file_name='test_photo.jpg',
            photo_data=b'dummy_photo_data',
            cv_file_name='test_cv.pdf',
            cv_data=b'dummy_cv_data',
        )
        
        # Create another staff member using another swap card/mobile to test filtering logic
        cls.staff_member_2 = OfficeStaff.objects.create(
            userid=2,
            emp_id='EMP002',
            offer_id='OFF002',
            fin_year_id=2023,
            comp_id=1,
            title='Ms',
            employee_name='Jane Smith',
            department=cls.department,
            designation=cls.designation,
            bg_group=cls.bg_group,
            grade=cls.grade,
            contact_no='**********',
            company_email='<EMAIL>',
            joining_date=date(2021, 3, 1),
            permanent_address='456 Oak Ave',
            correspondence_address='456 Oak Ave',
            date_of_birth=date(1992, 7, 10),
            gender='F',
            marital_status=True, # Married
            blood_group='A-',
            height='160cm',
            weight='55kg',
            physically_handicapped=False,
            religion='Hindu',
            caste='OBC',
            educational_qualification='M.Tech',
            current_ctc='600000',
            bank_account_no='67890',
            pf_no='PF456',
            pan_no='PAN456',
            passport_no='PS456',
            extension_no=cls.extension,
            swap_card_no=SwapCard.objects.create(id=101, swap_card_no='SC_USED_BY_EMP2'),
            mobile_no=CorporateMobileNo.objects.create(id=201, mobile_no='**********'),
        )

    def test_officestaff_creation(self):
        obj = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(obj.employee_name, 'John Doe')
        self.assertEqual(obj.designation.designation_type, 'Sr.')
        self.assertFalse(obj.marital_status) # False because marital_status is 0

    def test_get_full_name_method(self):
        obj = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(obj.get_full_name(), 'Mr John Doe')

    def test_clean_photo_method(self):
        obj = OfficeStaff.objects.get(emp_id='EMP001')
        # Ensure photo data exists before cleaning
        self.assertIsNotNone(obj.photo_data)
        
        obj.clean_photo() # Call the fat model method
        obj.refresh_from_db() # Refresh to get latest state from DB
        self.assertIsNone(obj.photo_data)
        self.assertIsNone(obj.photo_file_name)

    def test_clean_cv_method(self):
        obj = OfficeStaff.objects.get(emp_id='EMP001')
        # Ensure CV data exists before cleaning
        self.assertIsNotNone(obj.cv_data)
        
        obj.clean_cv() # Call the fat model method
        obj.refresh_from_db() # Refresh to get latest state from DB
        self.assertIsNone(obj.cv_data)
        self.assertIsNone(obj.cv_file_name)

    def test_email_validation(self):
        obj = OfficeStaff.objects.get(emp_id='EMP001')
        # Test valid emails - should not raise error
        obj.company_email = '<EMAIL>'
        obj.erp_mail = '<EMAIL>'
        obj.personal_email = '<EMAIL>'
        obj.validate_email_fields() 

        # Test invalid company email
        obj.company_email = 'invalid-email'
        with self.assertRaises(ValidationError):
            obj.validate_email_fields()
        obj.company_email = '<EMAIL>' # Reset

        # Test invalid ERP email
        obj.erp_mail = 'invalid@'
        with self.assertRaises(ValidationError):
            obj.validate_email_fields()
        obj.erp_mail = None # Reset

        # Test invalid personal email
        obj.personal_email = 'invalid.com'
        with self.assertRaises(ValidationError):
            obj.validate_email_fields()
        obj.personal_email = None # Reset

    def test_save_triggers_validation(self):
        obj = OfficeStaff.objects.get(emp_id='EMP001')
        obj.company_email = 'invalid-email-on-save'
        with self.assertRaises(ValidationError):
            obj.save()


# --- Integration Tests for Views ---
class OfficeStaffViewsTest(TestCase):
    client = Client() # Initialize a test client

    @classmethod
    def setUpTestData(cls):
        # Create all necessary lookup objects for foreign keys
        cls.designation = Designation.objects.create(id=100, designation_type='Lead', symbol='L')
        cls.department = Department.objects.create(id=200, dept_name='Development')
        cls.bg_group = BusinessGroup.objects.create(id=300, name='Corp', symbol='C')
        cls.grade = Grade.objects.create(id=400, description='Senior', symbol='S')
        cls.extension = IntercomExtension.objects.create(id=500, ext_no='303')
        
        # Swap cards and Mobile Nos for testing dropdown logic
        cls.swap_card_na = SwapCard.objects.create(id=1, swap_card_no='NA') # "Not Applicable"
        cls.swap_card_free = SwapCard.objects.create(id=1000, swap_card_no='SC_FREE') # An available one
        cls.mobile_na = CorporateMobileNo.objects.create(id=1, mobile_no='NA') # "Not Applicable"
        cls.mobile_free = CorporateMobileNo.objects.create(id=2000, mobile_no='9999911111') # An available one

        # Create the main staff member to be edited
        cls.staff_member_to_edit = OfficeStaff.objects.create(
            userid=1001,
            emp_id='EMP_EDIT',
            offer_id='OFF_EDIT',
            fin_year_id=2024,
            comp_id=1, # Assuming comp_id=1 for this test environment
            title='Mr',
            employee_name='Edit Me',
            department=cls.department,
            designation=cls.designation,
            bg_group=cls.bg_group,
            grade=cls.grade,
            contact_no='1112223334',
            company_email='<EMAIL>',
            joining_date=date(2022, 1, 1),
            permanent_address='Edit Address',
            correspondence_address='Edit Address',
            date_of_birth=date(1985, 1, 1),
            gender='M',
            marital_status=False,
            blood_group='O+',
            height='180cm',
            weight='80kg',
            physically_handicapped=False,
            religion='None',
            caste='None',
            educational_qualification='B.Sc.',
            current_ctc='700000',
            bank_account_no='54321',
            pf_no='PF543',
            pan_no='PAN543',
            passport_no='PS543',
            extension_no=cls.extension,
            swap_card_no=cls.swap_card_na, # Initially assigned to NA
            mobile_no=cls.mobile_na,       # Initially assigned to NA
            photo_file_name='old_photo.jpg',
            photo_data=b'old_photo_data',
            cv_file_name='old_cv.pdf',
            cv_data=b'old_cv_data',
        )

        # Create another staff member using a unique swap card/mobile to test dropdown filtering
        cls.staff_member_using_exclusive = OfficeStaff.objects.create(
            userid=1002,
            emp_id='EMP_USED',
            offer_id='OFF_USED',
            fin_year_id=2024,
            comp_id=1,
            title='Mrs',
            employee_name='Used Staff',
            department=cls.department,
            designation=cls.designation,
            bg_group=cls.bg_group,
            grade=cls.grade,
            contact_no='5555555555',
            company_email='<EMAIL>',
            joining_date=date(2023, 1, 1),
            permanent_address='Used Address',
            correspondence_address='Used Address',
            date_of_birth=date(1990, 1, 1),
            gender='F',
            marital_status=True,
            blood_group='A+',
            height='165cm',
            weight='60kg',
            physically_handicapped=False,
            religion='None',
            caste='None',
            educational_qualification='MBA',
            current_ctc='800000',
            bank_account_no='11111',
            pf_no='PF111',
            pan_no='PAN111',
            passport_no='PS111',
            extension_no=cls.extension,
            swap_card_no=SwapCard.objects.create(id=1002, swap_card_no='SC_USED_BY_EMP2'),
            mobile_no=CorporateMobileNo.objects.create(id=2002, mobile_no='**********'),
        )
        
    def setUp(self):
        # Set session data before each test, as `compid` is read from session
        session = self.client.session
        session['compid'] = 1
        session.save()

    def test_officestaff_list_view(self):
        """Tests the staff list page view."""
        response = self.client.get(reverse('hr:officestaff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/list.html')
        self.assertTrue('officestaffs' in response.context)
        # Should show both staff members created in setUpTestData
        self.assertEqual(response.context['officestaffs'].count(), 2) 
        self.assertContains(response, 'EMP_EDIT')
        self.assertContains(response, 'EMP_USED')

    def test_officestaff_table_partial_view_htmx(self):
        """Tests the HTMX partial for the staff table."""
        # Simulate an HTMX request
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr:officestaff_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/_officestaff_table.html')
        self.assertContains(response, 'EMP_EDIT')
        self.assertContains(response, 'EMP_USED')

    def test_update_view_get(self):
        """Tests accessing the staff edit form via GET request."""
        response = self.client.get(reverse('hr:officestaff_edit', kwargs={'emp_id': self.staff_member_to_edit.emp_id}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/edit.html')
        self.assertContains(response, 'Edit Me')
        self.assertIsInstance(response.context['form'].instance, OfficeStaff)
        
        # Test if dropdowns are populated correctly based on ASP.NET logic
        form = response.context['form']
        
        # 'NA' (id=1) should be in queryset for swap_card_no and mobile_no
        self.assertIn(self.swap_card_na, form.fields['swap_card_no'].queryset)
        self.assertIn(self.mobile_na, form.fields['mobile_no'].queryset)
        
        # Free items should be available
        self.assertIn(self.swap_card_free, form.fields['swap_card_no'].queryset)
        self.assertIn(self.mobile_free, form.fields['mobile_no'].queryset)
        
        # Swap card/mobile used by another employee should NOT be available for selection by 'EMP_EDIT'
        self.assertNotIn(SwapCard.objects.get(id=1002), form.fields['swap_card_no'].queryset) 
        self.assertNotIn(CorporateMobileNo.objects.get(id=2002), form.fields['mobile_no'].queryset)

    def test_update_view_post_success(self):
        """Tests successful form submission and data update."""
        # Prepare updated data for POST request
        updated_data = {
            'emp_id': self.staff_member_to_edit.emp_id, 
            'offer_id': 'OFF_UPDATED',
            'fin_year_id': 2024,
            'comp_id': 1,
            'title': 'Ms', # Changed title
            'employee_name': 'Updated Name', # Changed name
            'department': self.department.id,
            'designation': self.designation.id,
            'bg_group': self.bg_group.id,
            'grade': self.grade.id,
            'contact_no': '9876543210',
            'company_email': '<EMAIL>',
            'erp_mail': '<EMAIL>',
            'extension_no': self.extension.id,
            'joining_date': '2022-01-01',
            'resignation_date': '',
            'permanent_address': 'Updated Permanent Address',
            'correspondence_address': 'Updated Correspondence Address',
            'personal_email': '<EMAIL>',
            'date_of_birth': '1985-01-01',
            'gender': 'F',
            'marital_status': 'True', # Changed marital status to Married
            'blood_group': 'B+',
            'height': '170cm',
            'weight': '75kg',
            'physically_handicapped': 'False',
            'religion': 'Buddhist',
            'caste': 'SC',
            'educational_qualification': 'M.Sc.',
            'additional_qualification': 'Project Management',
            'last_company_name': 'Old Corp',
            'working_duration': '5 years',
            'total_experience': '7 years',
            'current_ctc': '900000',
            'bank_account_no': '98765',
            'pf_no': 'PF987',
            'pan_no': 'PAN987',
            'passport_no': 'PS987',
            'expiry_date': '2025-12-31',
            'additional_information': 'Updated notes.',
            'swap_card_no': self.swap_card_free.id, # Assign a newly free one
            'mobile_no': self.mobile_free.id,
            '_photo_data_unchanged': 'True', # Keep existing photo
            '_cv_data_unchanged': 'True',     # Keep existing CV
        }

        response = self.client.post(reverse('hr:officestaff_edit', kwargs={'emp_id': self.staff_member_to_edit.emp_id}), updated_data)
        
        # Verify successful redirect after update
        self.assertEqual(response.status_code, 302) 
        self.assertRedirects(response, reverse('hr:officestaff_list'))

        # Retrieve the updated object and verify changes
        self.staff_member_to_edit.refresh_from_db()
        self.assertEqual(self.staff_member_to_edit.employee_name, 'Updated Name')
        self.assertEqual(self.staff_member_to_edit.company_email, '<EMAIL>')
        self.assertTrue(self.staff_member_to_edit.marital_status) # Should be True (Married)
        self.assertEqual(self.staff_member_to_edit.swap_card_no, self.swap_card_free)
        self.assertEqual(self.staff_member_to_edit.photo_data, b'old_photo_data') # Should retain old data
        self.assertEqual(self.staff_member_to_edit.cv_data, b'old_cv_data')     # Should retain old data
        
    def test_update_view_post_with_new_file_uploads(self):
        """Tests form submission with new photo and CV files."""
        # Prepare new file contents
        new_photo_content = b"new_photo_data_content"
        new_cv_content = b"new_cv_data_content"
        
        # Create in-memory file objects
        photo_file = io.BytesIO(new_photo_content)
        photo_file.name = 'new_photo.png'
        photo_file.content_type = 'image/png'

        cv_file = io.BytesIO(new_cv_content)
        cv_file.name = 'new_cv.docx'
        cv_file.content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

        # Prepare updated data, including the new file uploads
        updated_data = {
            'emp_id': self.staff_member_to_edit.emp_id, 
            'offer_id': 'OFF_UPDATED_FILES',
            'fin_year_id': 2024,
            'comp_id': 1,
            'title': 'Mr',
            'employee_name': 'File Uploader',
            'department': self.department.id,
            'designation': self.designation.id,
            'bg_group': self.bg_group.id,
            'grade': self.grade.id,
            'contact_no': '1112223334',
            'company_email': '<EMAIL>',
            'erp_mail': '<EMAIL>',
            'extension_no': self.extension.id,
            'joining_date': '2022-01-01',
            'permanent_address': 'File Address',
            'correspondence_address': 'File Address',
            'date_of_birth': '1985-01-01',
            'gender': 'M',
            'marital_status': 'False',
            'blood_group': 'O+',
            'height': '180cm',
            'weight': '80kg',
            'physically_handicapped': 'False',
            'religion': 'None',
            'caste': 'None',
            'educational_qualification': 'B.Sc.',
            'current_ctc': '700000',
            'bank_account_no': '54321',
            'pf_no': 'PF543',
            'pan_no': 'PAN543',
            'passport_no': 'PS543',
            'expiry_date': '2025-12-31',
            'additional_information': 'Notes for file upload.',
            'swap_card_no': self.swap_card_na.id, 
            'mobile_no': self.mobile_na.id,
            'photo_file': photo_file, # New photo file
            'cv_file': cv_file,       # New CV file
        }

        response = self.client.post(reverse('hr:officestaff_edit', kwargs={'emp_id': self.staff_member_to_edit.emp_id}), updated_data)
        
        self.assertEqual(response.status_code, 302) 
        self.staff_member_to_edit.refresh_from_db()
        self.assertEqual(self.staff_member_to_edit.employee_name, 'File Uploader')
        self.assertEqual(self.staff_member_to_edit.photo_file_name, 'new_photo.png')
        self.assertEqual(self.staff_member_to_edit.photo_data, new_photo_content)
        self.assertEqual(self.staff_member_to_edit.cv_file_name, 'new_cv.docx')
        self.assertEqual(self.staff_member_to_edit.cv_data, new_cv_content)

    def test_file_delete_view_photo_htmx(self):
        """Tests deleting photo data via HTMX."""
        # Ensure photo data exists before deletion
        self.staff_member_to_edit.photo_data = b'original_photo'
        self.staff_member_to_edit.photo_file_name = 'original.jpg'
        self.staff_member_to_edit.save()

        # Simulate HTMX POST request to delete photo
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(
            reverse('hr:file_delete', kwargs={'pk': self.staff_member_to_edit.userid, 'file_type': 'photo'}),
            **headers
        )
        self.assertEqual(response.status_code, 204) # HTMX success (No Content)
        
        # Verify data was cleared in DB
        self.staff_member_to_edit.refresh_from_db()
        self.assertIsNone(self.staff_member_to_edit.photo_data)
        self.assertIsNone(self.staff_member_to_edit.photo_file_name)

    def test_file_delete_view_cv_htmx(self):
        """Tests deleting CV data via HTMX."""
        # Ensure CV data exists before deletion
        self.staff_member_to_edit.cv_data = b'original_cv'
        self.staff_member_to_edit.cv_file_name = 'original.pdf'
        self.staff_member_to_edit.save()

        # Simulate HTMX POST request to delete CV
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(
            reverse('hr:file_delete', kwargs={'pk': self.staff_member_to_edit.userid, 'file_type': 'cv'}),
            **headers
        )
        self.assertEqual(response.status_code, 204) # HTMX success (No Content)
        
        # Verify data was cleared in DB
        self.staff_member_to_edit.refresh_from_db()
        self.assertIsNone(self.staff_member_to_edit.cv_data)
        self.assertIsNone(self.staff_member_to_edit.cv_file_name)

    def test_file_download_view_cv(self):
        """Tests downloading the CV file."""
        # Ensure CV data exists for download
        self.staff_member_to_edit.cv_data = b'downloadable_cv_content'
        self.staff_member_to_edit.cv_file_name = 'download.pdf'
        self.staff_member_to_edit.cv_content_type = 'application/pdf'
        self.staff_member_to_edit.save()

        response = self.client.get(reverse('hr:file_download', kwargs={'pk': self.staff_member_to_edit.userid, 'file_type': 'cv'}))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="download.pdf"')
        self.assertEqual(response.content, b'downloadable_cv_content')

    def test_file_download_view_invalid_type(self):
        """Tests downloading with an invalid file type."""
        response = self.client.get(reverse('hr:file_download', kwargs={'pk': self.staff_member_to_edit.userid, 'file_type': 'photo'}))
        self.assertEqual(response.status_code, 404)

    def test_file_download_view_no_file_data(self):
        """Tests downloading when no file data exists."""
        # Clear CV data
        self.staff_member_to_edit.cv_data = None
        self.staff_member_to_edit.cv_file_name = None
        self.staff_member_to_edit.save()
        
        response = self.client.get(reverse('hr:file_download', kwargs={'pk': self.staff_member_to_edit.userid, 'file_type': 'cv'}))
        self.assertEqual(response.status_code, 404)
```