## ASP.NET to Django Conversion Script:

This modernization plan outlines the transition of your `BankLoan_Print_Details.aspx` page from ASP.NET to a modern Django application. The core idea is to transform the static report viewer into a dynamic, interactive data display using Django's powerful ORM, Class-Based Views, HTMX for seamless interactions, and DataTables for advanced data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The ASP.NET page primarily functions as a report viewer for bank loan details, with optional filtering by employee ID. In Django, we will replace the Crystal Report Viewer with an interactive DataTables display. Although the original page was for "printing/viewing," a comprehensive Django modernization implies providing full CRUD capabilities for the `BankLoan` entity, which will be implemented following the provided templates.

---

## Step 1: Extract Database Schema

Based on the C# code-behind, we identify the following primary tables and their inferred relationships:

*   **`tblHR_BankLoan`**: This is the main table for bank loan information.
    *   `Id` (int, Primary Key)
    *   `EmpId` (string) - Foreign Key to `tblHR_OfficeStaff`
    *   `BankName` (string)
    *   `Branch` (string)
    *   `Amount` (double)
    *   `Installment` (double)
    *   `fromDate` (string) - This will be mapped to a `DateField` in Django.
    *   `ToDate` (string) - This will be mapped to a `DateField` in Django.
    *   `CompId` (int) - Foreign Key to a `Company` table.
    *   `FinYearId` (string) - Foreign Key to a `FinancialYear` table.

*   **`tblHR_OfficeStaff`**: Used to retrieve employee names based on `EmpId`.
    *   `EmpId` (string, Primary Key)
    *   `Title` (string)
    *   `EmployeeName` (string)
    *   `CompId` (int) - Foreign Key to a `Company` table.

*   **Inferred `Company` table (e.g., `tblCompany`)**: Used for `CompId` and company address.
    *   `CompId` (int, Primary Key)
    *   `CompanyName` (string)
    *   `CompanyAddress` (text)

*   **Inferred `FinancialYear` table (e.g., `tblHR_FinYear`)**: Used for `FinYearId`.
    *   `FinYearId` (string, Primary Key)
    *   `StartDate` (date)
    *   `EndDate` (date)

---

## Step 2: Identify Backend Functionality

The ASP.NET page primarily performs a **Read** operation, fetching bank loan data with joins and filtering, then displaying it via Crystal Reports.

*   **Read:**
    *   Data is fetched from `tblHR_BankLoan`.
    *   Employee names are fetched from `tblHR_OfficeStaff` based on `EmpId`.
    *   Data is filtered by `CompId` (Company ID) and `FinYearId` (Financial Year ID).
    *   An optional filter by `EmpId` (Employee ID) is applied if provided in the query string.
    *   Company address is retrieved using `CompId`.
*   **Create, Update, Delete:** Not present in the original ASP.NET code for `BankLoan_Print_Details.aspx`. However, as per the comprehensive modernization guideline, these will be implemented conceptually for the `BankLoan` entity in Django to provide a full CRUD module.
*   **Redirection:** A "Cancel" button redirects the user, which will be handled by Django's URL routing and `success_url` patterns.

---

## Step 3: Infer UI Components

The ASP.NET UI consists of:

*   A `CrystalReportViewer`: This will be replaced by a Django template rendering data in a `<table>` tag, which will be initialized as a DataTables instance.
*   An `asp:Button` for "Cancel": This will be a standard HTML button that triggers a redirect or closes a modal in the Django setup.
*   Styling: The `StyleSheet.css` and inline styles will be replaced by Tailwind CSS classes.

---

## Step 4: Generate Django Code

We will create a new Django application, let's call it `hr_module`, to house the bank loan functionality.

### 4.1 Models (`hr_module/models.py`)

```python
from django.db import models
from django.utils import timezone
from django.db.models import F

# Inferred Company Model
class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255, null=True, blank=True)
    address = models.TextField(db_column='CompanyAddress', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblCompany' # Assumed table name for company details
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"
        
    @classmethod
    def get_company_address(cls, comp_id):
        """Replicates fun.CompAdd(CompId) to fetch company address."""
        try:
            company = cls.objects.get(id=comp_id)
            return company.address
        except cls.DoesNotExist:
            return "Company Address Not Found"

# Inferred Financial Year Model
class FinancialYear(models.Model):
    id = models.CharField(db_column='FinYearId', primary_key=True, max_length=10)
    start_date = models.DateField(db_column='StartDate', null=True, blank=True)
    end_date = models.DateField(db_column='EndDate', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_FinYear' # Assumed table name for financial years
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.id

# Office Staff Model (for EmployeeName lookup)
class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=10, null=True, blank=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='staff_members', null=True, blank=True)
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title + '. ' if self.title else ''}{self.employee_name} ({self.emp_id})"

# BankLoan Model
class BankLoan(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    employee = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='bank_loans')
    bank_name = models.CharField(db_column='BankName', max_length=255)
    branch = models.CharField(db_column='Branch', max_length=255)
    amount = models.FloatField(db_column='Amount')
    installment = models.FloatField(db_column='Installment')
    from_date = models.DateField(db_column='fromDate')
    to_date = models.DateField(db_column='ToDate')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='bank_loans')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='bank_loans')

    class Meta:
        managed = False
        db_table = 'tblHR_BankLoan'
        verbose_name = 'Bank Loan'
        verbose_name_plural = 'Bank Loans'
        # Original was 'Order by EmpId Desc', using F() expression for ordering on related field
        ordering = [F('employee__emp_id').desc()]

    def __str__(self):
        return f"Loan for {self.employee.employee_name} from {self.bank_name}"

    # Business logic methods (Fat Model Approach)
    def get_employee_full_name(self):
        """Combines title and employee name, replicating ASP.NET logic."""
        return str(self.employee) # OfficeStaff __str__ handles this

    def get_formatted_from_date(self):
        """Formats the from_date as DD/MM/YYYY, replicating fun.FromDateDMY."""
        return self.from_date.strftime('%d/%m/%Y') if self.from_date else ''

    def get_formatted_to_date(self):
        """Formats the to_date as DD/MM/YYYY, replicating fun.FromDateDMY."""
        return self.to_date.strftime('%d/%m/%Y') if self.to_date else ''

    @classmethod
    def get_filtered_bank_loans(cls, comp_id, fin_year_id, emp_id=None):
        """
        Replicates the data retrieval logic from `binddata` function in ASP.NET.
        Filters bank loans by company, financial year, and optionally by employee.
        """
        loans = cls.objects.select_related('employee', 'company', 'financial_year').filter(
            company__id=comp_id,
            financial_year__id__lte=fin_year_id
        )
        if emp_id:
            loans = loans.filter(employee__emp_id=emp_id)
        
        # Ordering is handled by the Meta class 'ordering' attribute
        return loans
```

### 4.2 Forms (`hr_module/forms.py`)

```python
from django import forms
from .models import BankLoan, OfficeStaff, Company, FinancialYear

class BankLoanForm(forms.ModelForm):
    # This field will be used for inputting or displaying the EmpId string,
    # which we then resolve to an OfficeStaff object in clean_employee_emp_id.
    employee_emp_id = forms.CharField(
        label="Employee ID",
        max_length=50,
        help_text="Enter the Employee ID (e.g., E001)",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = BankLoan
        # Exclude ForeignKey fields (employee, company, financial_year) from 'fields' to handle them manually.
        # This gives more control over how related objects are presented in the form (e.g., via CharField for ID).
        fields = ['employee_emp_id', 'bank_name', 'branch', 'amount', 'installment', 'from_date', 'to_date']
        widgets = {
            'bank_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'branch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'installment': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'from_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'to_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            self.fields['employee_emp_id'].initial = self.instance.employee.emp_id
            # Assuming company and financial_year are determined by the application context
            # rather than direct form input for this specific loan.
            # If they were user selectable, they would be ModelChoiceFields.

    def clean_employee_emp_id(self):
        emp_id = self.cleaned_data['employee_emp_id']
        try:
            employee = OfficeStaff.objects.get(emp_id=emp_id)
        except OfficeStaff.DoesNotExist:
            raise forms.ValidationError("Employee ID does not exist. Please enter a valid Employee ID.")
        self.cleaned_data['employee'] = employee # Attach the actual employee object to cleaned_data
        return emp_id

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.employee = self.cleaned_data['employee']
        
        # For simplicity, assign to the first available company and financial year.
        # In a real application, these would come from the user's session, profile,
        # or be selected via a separate form field if the context is dynamic.
        try:
            instance.company = Company.objects.first()
            if not instance.company:
                raise forms.ValidationError("No companies configured. Please add a company record.")
        except Company.DoesNotExist:
            raise forms.ValidationError("No company found. Please ensure Company model has data.")

        try:
            instance.financial_year = FinancialYear.objects.first()
            if not instance.financial_year:
                raise forms.ValidationError("No financial years configured. Please add a financial year record.")
        except FinancialYear.DoesNotExist:
            raise forms.ValidationError("No financial year found. Please ensure FinancialYear model has data.")

        if commit:
            instance.save()
        return instance
```

### 4.3 Views (`hr_module/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import BankLoan, Company
from .forms import BankLoanForm

# Dummy session data for testing purposes (replace with actual session/user context in production)
# In a real application, company_id and fin_year_id would typically come from the logged-in user's profile
# or selected context.
DUMMY_COMPANY_ID = 1
DUMMY_FIN_YEAR_ID = "2023-24" # Example based on "FinYearId<='FyId'"

class BankLoanListView(ListView):
    model = BankLoan
    template_name = 'hr_module/bankloan/list.html'
    context_object_name = 'bank_loans'

    def get_queryset(self):
        # Retrieve filters from query parameters
        emp_id = self.request.GET.get('EmpId')
        
        # In a real application, these would come from user's session/profile
        comp_id = self.request.session.get('compid', DUMMY_COMPANY_ID)
        fin_year_id = self.request.session.get('finyear', DUMMY_FIN_YEAR_ID)

        queryset = BankLoan.get_filtered_bank_loans(comp_id, fin_year_id, emp_id)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass company address to the template, replicating the ASP.NET report parameter
        comp_id = self.request.session.get('compid', DUMMY_COMPANY_ID)
        context['company_address'] = Company.get_company_address(comp_id)
        context['current_emp_id_filter'] = self.request.GET.get('EmpId', '')
        return context

class BankLoanTablePartialView(ListView):
    """
    Renders only the DataTables table for HTMX requests.
    """
    model = BankLoan
    template_name = 'hr_module/bankloan/_bankloan_table.html'
    context_object_name = 'bank_loans'

    def get_queryset(self):
        # Retrieve filters from query parameters
        emp_id = self.request.GET.get('EmpId')
        
        # In a real application, these would come from user's session/profile
        comp_id = self.request.session.get('compid', DUMMY_COMPANY_ID)
        fin_year_id = self.request.session.get('finyear', DUMMY_FIN_YEAR_ID)

        queryset = BankLoan.get_filtered_bank_loans(comp_id, fin_year_id, emp_id)
        return queryset

    def render_to_response(self, context, **response_kwargs):
        return super().render_to_response(context, **response_kwargs)

class BankLoanCreateView(CreateView):
    model = BankLoan
    form_class = BankLoanForm
    template_name = 'hr_module/bankloan/_bankloan_form.html'
    # success_url is handled by HX-Trigger in form_valid for HTMX
    # If not HTMX, it would redirect to the list view.

    def form_valid(self, form):
        # ASP.NET code did not specify default CompId/FinYearId for creation.
        # Here we assume it comes from session or a default for new entries.
        # The form's save method already handles assigning these based on first()
        # You may need to adjust form.save() or this view to pull from session/context.
        response = super().form_valid(form)
        messages.success(self.request, 'Bank Loan added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshBankLoanList', # Trigger HTMX event to refresh the list
                    'HX-Redirect': reverse_lazy('bankloan_list') # Optional: redirect browser on successful form submission in modal context
                }
            )
        return response # Non-HTMX submission (e.g., if form is rendered directly on page)

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # Return the form with errors for HTMX to swap
        return response


class BankLoanUpdateView(UpdateView):
    model = BankLoan
    form_class = BankLoanForm
    template_name = 'hr_module/bankloan/_bankloan_form.html'
    # success_url is handled by HX-Trigger in form_valid for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bank Loan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshBankLoanList',
                    'HX-Redirect': reverse_lazy('bankloan_list')
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class BankLoanDeleteView(DeleteView):
    model = BankLoan
    template_name = 'hr_module/bankloan/confirm_delete.html'
    # success_url is handled by HX-Trigger in delete for HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Bank Loan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshBankLoanList',
                    'HX-Redirect': reverse_lazy('bankloan_list')
                }
            )
        return response

    def get_object(self, queryset=None):
        # Override to ensure object is retrieved, useful for confirm_delete template
        return get_object_or_404(BankLoan, pk=self.kwargs['pk'])
```

### 4.4 Templates (`hr_module/templates/hr_module/bankloan/`)

**`list.html`** (Main page for displaying bank loans)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Bank Loan Details</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'bankloan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Bank Loan
        </button>
    </div>

    <div class="mb-4">
        <form hx-get="{% url 'bankloan_list' %}" hx-target="#bankLoanTable-container" hx-swap="innerHTML">
            <label for="id_emp_id_filter" class="block text-sm font-medium text-gray-700">Filter by Employee ID:</label>
            <div class="flex space-x-2 mt-1">
                <input type="text" id="id_emp_id_filter" name="EmpId" value="{{ current_emp_id_filter }}"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       placeholder="e.g., E001">
                <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Filter
                </button>
                <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                        onclick="document.getElementById('id_emp_id_filter').value=''; this.form.submit();">
                    Clear Filter
                </button>
            </div>
        </form>
    </div>

    <div id="bankLoanTable-container"
         hx-trigger="load, refreshBankLoanList from:body"
         hx-get="{% url 'bankloan_table' %}?EmpId={{ current_emp_id_filter }}" {# Pass filter to partial #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading bank loan data...</p>
        </div>
    </div>
    
    <!-- Company Address Display (replicates Crystal Report Parameter) -->
    {% if company_address %}
    <div class="mt-6 p-4 bg-gray-100 rounded-md text-sm text-gray-700">
        <p><strong>Company Address:</strong> {{ company_address }}</p>
    </div>
    {% endif %}

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full h-auto overflow-y-auto max-h-[90vh]"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed (e.g., for complex UI state)
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            isOpen: false,
            openModal() { this.isOpen = true },
            closeModal() { this.isOpen = false }
        }));
    });
</script>
{% endblock %}
```

**`_bankloan_table.html`** (Partial for DataTables, loaded via HTMX)

```html
<table id="bankLoanTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Installment</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-100">
        {% for loan in bank_loans %}
        <tr>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.employee.emp_id }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.get_employee_full_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.bank_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.branch }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.amount|floatformat:2 }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.installment|floatformat:2 }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.get_formatted_from_date }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.get_formatted_to_date }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2 text-xs"
                    hx-get="{% url 'bankloan_edit' loan.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-get="{% url 'bankloan_delete' loan.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-gray-500">No bank loan records found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        $('#bankLoanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true // Make DataTables responsive
        });
    });
</script>
```

**`_bankloan_form.html`** (Partial for Create/Update forms)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Bank Loan</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML"> {# hx-swap outerHTML for form validation feedback #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial for delete confirmation)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Bank Loan for <strong>{{ object.employee.get_employee_full_name }}</strong> from <strong>{{ object.bank_name }}</strong>?</p>
    
    <form hx-post="{% url 'bankloan_delete' object.pk %}" hx-swap="none"> {# No swap, just trigger refresh #}
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`hr_module/urls.py`)

```python
from django.urls import path
from .views import (
    BankLoanListView, 
    BankLoanTablePartialView, 
    BankLoanCreateView, 
    BankLoanUpdateView, 
    BankLoanDeleteView
)

urlpatterns = [
    # Main list view (for initial page load and filtering)
    path('bankloan/', BankLoanListView.as_view(), name='bankloan_list'),
    
    # HTMX endpoint for refreshing the table content
    path('bankloan/table/', BankLoanTablePartialView.as_view(), name='bankloan_table'),
    
    # CRUD operations via HTMX modals
    path('bankloan/add/', BankLoanCreateView.as_view(), name='bankloan_add'),
    path('bankloan/edit/<int:pk>/', BankLoanUpdateView.as_view(), name='bankloan_edit'),
    path('bankloan/delete/<int:pk>/', BankLoanDeleteView.as_view(), name='bankloan_delete'),
]
```

### 4.6 Tests (`hr_module/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection # For checking db_table mapping
from .models import BankLoan, Company, FinancialYear, OfficeStaff
from django.db.utils import ProgrammingError

class ModelTestSetup(TestCase):
    """
    Base class to set up common test data and ensure models are properly mapped.
    """
    @classmethod
    def setUpTestData(cls):
        # Create dummy Company and FinancialYear objects for FK dependencies
        # These assume the primary keys match what's expected by the models (e.g., 1 for Company, '2023-24' for FinYear)
        # If your actual database uses different IDs, adjust these.
        Company.objects.create(id=1, name='Test Company Inc.', address='123 Test St, Test City')
        FinancialYear.objects.create(id='2023-24', start_date='2023-04-01', end_date='2024-03-31')
        FinancialYear.objects.create(id='2022-23', start_date='2022-04-01', end_date='2023-03-31')
        
        # Create dummy OfficeStaff
        OfficeStaff.objects.create(emp_id='E001', title='Mr.', employee_name='John Doe', company_id=1)
        OfficeStaff.objects.create(emp_id='E002', title='Ms.', employee_name='Jane Smith', company_id=1)

        # Create test BankLoan data
        BankLoan.objects.create(
            id=101, employee_id='E001', bank_name='First Bank', branch='Main', 
            amount=10000.00, installment=500.00, from_date='2023-01-15', 
            to_date='2025-01-15', company_id=1, financial_year_id='2023-24'
        )
        BankLoan.objects.create(
            id=102, employee_id='E002', bank_name='Second Bank', branch='North', 
            amount=20000.00, installment=1000.00, from_date='2022-03-01', 
            to_date='2024-03-01', company_id=1, financial_year_id='2022-23'
        )
        BankLoan.objects.create(
            id=103, employee_id='E001', bank_name='Third Bank', branch='South', 
            amount=5000.00, installment=250.00, from_date='2023-06-01', 
            to_date='2024-06-01', company_id=1, financial_year_id='2023-24'
        )

class BankLoanModelTest(ModelTestSetup):
    def test_bankloan_creation(self):
        loan = BankLoan.objects.get(id=101)
        self.assertEqual(loan.employee.emp_id, 'E001')
        self.assertEqual(loan.bank_name, 'First Bank')
        self.assertAlmostEqual(loan.amount, 10000.00)
        self.assertEqual(str(loan), 'Loan for Mr. John Doe (E001) from First Bank')
        self.assertEqual(loan.company.id, 1)
        self.assertEqual(loan.financial_year.id, '2023-24')

    def test_get_employee_full_name(self):
        loan = BankLoan.objects.get(id=101)
        self.assertEqual(loan.get_employee_full_name(), 'Mr. John Doe (E001)')

    def test_get_formatted_dates(self):
        loan = BankLoan.objects.get(id=101)
        self.assertEqual(loan.get_formatted_from_date(), '15/01/2023')
        self.assertEqual(loan.get_formatted_to_date(), '15/01/2025')

    def test_get_filtered_bank_loans(self):
        # Test filtering by CompId and FinYearId (LTE)
        loans = BankLoan.get_filtered_bank_loans(comp_id=1, fin_year_id='2023-24')
        self.assertEqual(loans.count(), 3) # E001 (2023-24), E002 (2022-23), E001 (2023-24)

        # Test filtering by EmpId
        loans_e001 = BankLoan.get_filtered_bank_loans(comp_id=1, fin_year_id='2023-24', emp_id='E001')
        self.assertEqual(loans_e001.count(), 2)
        self.assertTrue(all(loan.employee.emp_id == 'E001' for loan in loans_e001))

        # Test filtering with no matching EmpId
        loans_e003 = BankLoan.get_filtered_bank_loans(comp_id=1, fin_year_id='2023-24', emp_id='E003')
        self.assertEqual(loans_e003.count(), 0)

    def test_db_table_mapping(self):
        self.assertEqual(BankLoan._meta.db_table, 'tblHR_BankLoan')
        self.assertEqual(OfficeStaff._meta.db_table, 'tblHR_OfficeStaff')
        self.assertEqual(Company._meta.db_table, 'tblCompany')
        self.assertEqual(FinancialYear._meta.db_table, 'tblHR_FinYear')

    def test_managed_false(self):
        self.assertFalse(BankLoan._meta.managed)
        self.assertFalse(OfficeStaff._meta.managed)
        self.assertFalse(Company._meta.managed)
        self.assertFalse(FinancialYear._meta.managed)

    def test_company_get_company_address(self):
        company = Company.objects.get(id=1)
        self.assertEqual(Company.get_company_address(company.id), '123 Test St, Test City')
        self.assertEqual(Company.get_company_address(999), 'Company Address Not Found')

class BankLoanViewsTest(ModelTestSetup):
    def setUp(self):
        self.client = Client()
        # Set session data to simulate ASP.NET session for tests
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = '2023-24'
        session.save()
    
    def test_list_view(self):
        response = self.client.get(reverse('bankloan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_module/bankloan/list.html')
        self.assertIn('bank_loans', response.context)
        self.assertGreater(len(response.context['bank_loans']), 0)
        self.assertIn('company_address', response.context)
        self.assertContains(response, 'Bank Loan Details') # Check for title

    def test_list_view_filter_by_emp_id(self):
        response = self.client.get(reverse('bankloan_list'), {'EmpId': 'E001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_module/bankloan/list.html')
        self.assertEqual(response.context['bank_loans'].count(), 2) # E001 has 2 loans

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bankloan_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_module/bankloan/_bankloan_table.html')
        self.assertContains(response, 'id="bankLoanTable"') # Check if table is rendered
        self.assertContains(response, 'John Doe') # Check for specific data
        self.assertContains(response, 'Jane Smith')

        # Test filter via HTMX
        response_filtered = self.client.get(reverse('bankloan_table'), {'EmpId': 'E001'}, headers=headers)
        self.assertEqual(response_filtered.status_code, 200)
        self.assertTemplateUsed(response_filtered, 'hr_module/bankloan/_bankloan_table.html')
        self.assertContains(response_filtered, 'John Doe')
        self.assertNotContains(response_filtered, 'Jane Smith') # Ensure filtered content

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bankloan_add'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_module/bankloan/_bankloan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Bank Loan')

    def test_create_view_post_success(self):
        initial_count = BankLoan.objects.count()
        data = {
            'employee_emp_id': 'E002',
            'bank_name': 'New Bank',
            'branch': 'Central',
            'amount': 7500.00,
            'installment': 300.00,
            'from_date': '2024-01-01',
            'to_date': '2026-01-01'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloan_add'), data, headers=headers)
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.assertEqual(BankLoan.objects.count(), initial_count + 1)
        self.assertTrue(BankLoan.objects.filter(bank_name='New Bank').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBankLoanList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        initial_count = BankLoan.objects.count()
        data = {
            'employee_emp_id': 'NONEXISTENT', # Invalid EmpId
            'bank_name': '', # Missing required field
            'amount': 'invalid_amount',
            'from_date': '2024-01-01',
            'to_date': '2026-01-01'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloan_add'), data, headers=headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr_module/bankloan/_bankloan_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertContains(response, "Employee ID does not exist.")
        self.assertContains(response, "This field is required.")
        self.assertEqual(BankLoan.objects.count(), initial_count) # No object created

    def test_update_view_get(self):
        loan = BankLoan.objects.get(id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bankloan_edit', args=[loan.pk]), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_module/bankloan/_bankloan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Bank Loan')
        self.assertContains(response, 'value="First Bank"')

    def test_update_view_post_success(self):
        loan = BankLoan.objects.get(id=101)
        data = {
            'employee_emp_id': loan.employee.emp_id, # Must include EmpId
            'bank_name': 'Updated Bank',
            'branch': 'Updated Branch',
            'amount': 11000.00,
            'installment': 550.00,
            'from_date': '2023-01-15',
            'to_date': '2025-01-15'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloan_edit', args=[loan.pk]), data, headers=headers)
        self.assertEqual(response.status_code, 204)
        loan.refresh_from_db()
        self.assertEqual(loan.bank_name, 'Updated Bank')
        self.assertEqual(loan.amount, 11000.00)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBankLoanList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        loan = BankLoan.objects.get(id=101)
        data = {
            'employee_emp_id': 'E001',
            'bank_name': '', # Invalid data
            'amount': 10000.00,
            'installment': 500.00,
            'from_date': '2023-01-15',
            'to_date': '2025-01-15'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloan_edit', args=[loan.pk]), data, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_module/bankloan/_bankloan_form.html')
        self.assertContains(response, "This field is required.")

    def test_delete_view_get(self):
        loan = BankLoan.objects.get(id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bankloan_delete', args=[loan.pk]), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_module/bankloan/confirm_delete.html')
        self.assertContains(response, f"delete the Bank Loan for {loan.employee.get_employee_full_name()} from {loan.bank_name}")

    def test_delete_view_post_success(self):
        loan_to_delete = BankLoan.objects.get(id=101)
        initial_count = BankLoan.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloan_delete', args=[loan_to_delete.pk]), headers=headers)
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.assertEqual(BankLoan.objects.count(), initial_count - 1)
        self.assertFalse(BankLoan.objects.filter(id=loan_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBankLoanList', response.headers['HX-Trigger'])
```

---

## Step 5: HTMX and Alpine.js Integration

*   **HTMX:** All dynamic interactions (loading the DataTables content, opening/submitting CRUD forms in modals, and handling deletions) are driven by HTMX.
    *   `hx-get`: Used to fetch the table partial (`_bankloan_table.html`) on page load and when the filter changes. Also used to fetch CRUD forms into the modal.
    *   `hx-target`: Specifies where the HTMX response should be placed (e.g., `#bankLoanTable-container`, `#modalContent`).
    *   `hx-swap`: Determines how the new content replaces the target (e.g., `innerHTML`, `outerHTML`).
    *   `hx-trigger`: Defines when an HTMX request should be made (e.g., `load`, `click`, `refreshBankLoanList from:body`).
    *   `HX-Trigger` header: Used in Django views (`HttpResponse(status=204, headers={'HX-Trigger': 'refreshBankLoanList'})`) to signal the client-side to refresh the bank loan list after a successful CRUD operation.
    *   `HX-Redirect`: Used in success cases to ensure the browser URL updates correctly after a modal submission if needed.

*   **Alpine.js:** Used for simple UI state management, primarily for controlling the visibility of the modal.
    *   `_ = "on click add .is-active to #modal"`: A simple Alpine.js directive to add the `is-active` class to show the modal when a button is clicked.
    *   `_ = "on click if event.target.id == 'modal' remove .is-active from me"`: Allows clicking outside the modal content to close it.

*   **DataTables:**
    *   The `_bankloan_table.html` partial includes the necessary JavaScript to initialize DataTables on the rendered table (`$('#bankLoanTable').DataTable()`).
    *   This ensures client-side searching, sorting, and pagination are available for the bank loan data.

*   **DRY Template Inheritance:** `list.html` extends `core/base.html`, ensuring all common elements (like CDN links for HTMX, Alpine.js, DataTables, and Tailwind CSS) are inherited from a central base template.

---

## Final Notes

*   **Placeholders:** Replace placeholder values (e.g., `DUMMY_COMPANY_ID`, `DUMMY_FIN_YEAR_ID`) with actual logic to fetch `CompId` and `FinYearId` from your application's session management or user profile.
*   **Database Integration:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database, and Django can inspect and manage (read-only, given `managed=False`) the `tblCompany`, `tblHR_FinYear`, `tblHR_OfficeStaff`, and `tblHR_BankLoan` tables.
*   **Error Handling:** The provided code includes basic form error handling for HTMX. In a production environment, you might want more robust error reporting and user feedback mechanisms.
*   **Security:** Always ensure proper authentication and authorization are implemented for your views and data access. The provided code assumes you have a user logged in with appropriate session data for `compid` and `finyear`.
*   **Scalability:** The "fat model" approach centralizes business logic, making models reusable and testable. Thin views ensure controllers are lightweight. HTMX minimizes server load by fetching only necessary partials.
*   **Automation Focus:** This plan provides structured, component-level code generation that can be automated. The distinct files and clear instructions allow for systematic conversion, which can be guided by conversational AI tools for non-technical stakeholders to oversee.