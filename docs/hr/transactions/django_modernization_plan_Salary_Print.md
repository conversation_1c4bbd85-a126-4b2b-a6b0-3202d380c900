## ASP.NET to Django Conversion Script: Salary Print Module

This document outlines a comprehensive plan to modernize the `Salary_Print` module from ASP.NET to a modern Django application. Our approach leverages AI-assisted automation, focusing on Django 5.0+ best practices, 'Fat Model, Thin View' architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for rich tabular data presentation. The goal is to deliver a robust, maintainable, and user-friendly solution, abstracting complex technical details into clear, actionable steps for business stakeholders.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`Salary_Print`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define Django models.

**Analysis:**
The ASP.NET code interacts with several tables:

*   **`tblHR_Salary_Master`**: This is the primary table for salary records and cheque transactions.
    *   Columns inferred from usage: `Id` (PK), `ChequeNo`, `ChequeNoDate`, `BankId`, `EmpDirect`, `TransNo`, `FMonth`, `CompId`, `FinYearId`, `ReleaseFlag`, `EmpId`, `BGGroup`.
*   **`tblACC_Bank`**: Used for bank names.
    *   Columns inferred: `Id` (PK), `Name`.
*   **`BusinessGroup`**: Used for business group selection.
    *   Columns inferred: `Id` (PK), `Symbol` (mapped to `Dept`).
*   **`tblHR_OfficeStaff`**: Used for employee search and validation.
    *   Columns inferred: `EmpId` (PK), `EmployeeName`, `BGGroup`, `ResignationDate`.
*   **`tblHR_Salary_Details`**: Referenced for salary slip count, implying `MId` (FK to `tblHR_Salary_Master.Id`).

**Conclusion:** We will define Django models for each of these tables, using `managed = False` as they represent existing database structures. The primary focus for this module's display (`GridView2`) will be on data derived from `tblHR_Salary_Master` and `tblACC_Bank`.

#### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core business logic in the ASP.NET code.

**Analysis:**

*   **Data Loading/Read (for `GridView2`):**
    *   The `loaddata()` method populates `GridView2` by selecting `ChequeNo`, `ChequeNoDate`, `BankId`, `EmpDirect`, `TransNo` from `tblHR_Salary_Master`, filtered by `FMonth`, `CompId`, `FinYearId`, and `ReleaseFlag='1'`. It then fetches `BankName` from `tblACC_Bank`. This is a read operation for a summarized list of bank transactions.
    *   `ddlMonth_SelectedIndexChanged` triggers `loaddata()`, indicating a dynamic refresh based on month selection.

*   **Report Generation (Complex Read/Action Triggering):**
    *   The `Button1_Click` method is the central point for generating various reports based on `RadioButtonList1` selection. Instead of traditional CRUD, this performs "action-oriented" reads/redirects.
    *   **Salary Slip (Case "0"):** Requires `EmpId` and `MonthId`. Validates employee's BG Group and checks `tblHR_Salary_Details`. Redirects to `Salary_Print_Details.aspx`.
    *   **Salary Slip of All Employees (Case "1"):** Redirects to `Salary_Print_ALL.aspx`.
    *   **On Cash Report (Case "2"):** Redirects to `Salary_Neha.aspx`.
    *   **Over Time Report (Case "3"):** Redirects to `Salary_Neha_OverTimes.aspx`.
    *   **SAPL Summary Report (Case "4"):** Redirects to `Salary_SAPL_Neha_Summary.aspx`.
    *   **Neha Summary Report (Case "5"):** Redirects to `Salary_SAPL_Neha_Summary.aspx`.
    *   **Bank Statement (Case "6"):** Requires `ChequeNo`, `txtDate`. Validates date. Redirects to `Salary_BankStatement_Check.aspx`.
    *   **All Month Summary Report (Case "7"):** Redirects to `All_Month_Summary_Report.aspx`.
    *   **Consolidated Summary Report (Case "8"):** Redirects to `Consolidated_Summary_Report.aspx`.
    *   **Validation:** Basic checks for empty fields and date format.

*   **Employee Autocomplete:**
    *   `GetCompletionList` (WebMethod) provides `EmployeeName` lookup from `tblHR_OfficeStaff` based on `prefixText` and `CompId`.

*   **GridView Actions:**
    *   `GridView2_RowCommand` handles "Sel" (Select/Edit) and "Print" actions on individual rows.
        *   "Sel": Redirects to `Salary_BankStatement_CheckEdit.aspx`.
        *   "Print": Redirects to `Salary_BankStatement.aspx`.

**Conclusion:** The module is primarily a complex filtering and report dispatching interface, combined with a display of specific (bank statement) transactions. In Django, the various report generations will be handled by HTMX-driven dynamic content swaps or modal pop-ups, avoiding full page reloads.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to Django form fields and HTMX/Alpine.js interactions.

**Analysis:**

*   **Dropdowns:**
    *   `ddlMonth`: For month selection. Django `forms.ChoiceField`.
    *   `ddlBGGroup`: For Business Group selection. Django `forms.ModelChoiceField` for `BusinessGroup`.
    *   `ddlBankName`: For Bank Name selection. Django `forms.ModelChoiceField` for `Bank`.
    *   `ddlEmpOrDirect`: For Employee/Directors selection. Django `forms.ChoiceField`.
*   **Radio Button List:**
    *   `RadioButtonList1`: For selecting report type. Django `forms.ChoiceField` with `RadioSelect` widget.
*   **Text Inputs:**
    *   `TxtEmpSearch`: Employee search. Django `forms.CharField` with HTMX for autocomplete.
    *   `txtChequeNo`: Cheque Number. Django `forms.CharField`.
    *   `txtDate`: Date input with CalendarExtender. Django `forms.DateField` with a custom widget for date picker.
*   **Button:**
    *   `btnProceed`: Triggers report generation logic. HTMX `hx-post` to a view method.
*   **Data Grid:**
    *   `GridView2`: Displays a list of bank statement transactions. Will be converted to a Django template using DataTables, loaded dynamically via HTMX. Each row will have HTMX-enabled "Edit" and "Print" buttons.
*   **Client-Side Scripting:**
    *   `AjaxControlToolkit`: Will be replaced by HTMX and Alpine.js.
    *   `loadingNotifier.js`, `PopUpMsg.js`: Replaced by HTMX indicators, Alpine.js for modals/alerts, and Django messages framework.
    *   `yui-datatable.css`: Replaced by DataTables CSS and Tailwind CSS.
    *   `AutoCompleteExtender`, `CalendarExtender`: Replaced by HTMX/Alpine.js patterns.

**Conclusion:** The frontend will be entirely rebuilt using HTMX, Alpine.js, and DataTables, maintaining the interactive feel without requiring traditional JavaScript.

#### Step 4: Generate Django Code

We'll place this code within a new Django application, for example, `hr_reports`.

##### 4.1 Models (`hr_reports/models.py`)

We need models for `tblHR_Salary_Master`, `tblACC_Bank`, `BusinessGroup`, `tblHR_OfficeStaff`, and `tblHR_Salary_Details`. For clarity, `tblHR_Salary_Master` will be aliased as `SalaryMaster` and `tblACC_Bank` as `Bank`, `BusinessGroup` as `BusinessGroup`, `tblHR_OfficeStaff` as `Employee`, and `tblHR_Salary_Details` as `SalaryDetail`.

```python
from django.db import models
from django.utils import timezone
from datetime import date

# Helper for date conversion, assuming ASP.NET's fun.FromDate converts 'dd-MM-yyyy' to standard Python date.
# In a real scenario, this would handle various formats or ensure database stores as standard date.
def aspnet_date_to_python_date(date_str):
    if not date_str:
        return None
    try:
        return timezone.datetime.strptime(date_str, '%d-%m-%Y').date()
    except ValueError:
        # Handle other potential formats or return None/raise error
        return None

class Bank(models.Model):
    # Corresponds to tblACC_Bank
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Name = models.CharField(db_column='Name', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank'
        verbose_name_plural = 'Banks'

    def __str__(self):
        return self.Name

class BusinessGroup(models.Model):
    # Corresponds to BusinessGroup
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=255, verbose_name='Department Symbol')

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.Symbol

class Employee(models.Model):
    # Corresponds to tblHR_OfficeStaff
    EmpId = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is unique and primary
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255)
    BGGroup = models.CharField(db_column='BGGroup', max_length=50, blank=True, null=True) # Assuming string, adjust if FK
    ResignationDate = models.DateField(db_column='ResignationDate', blank=True, null=True) # Assuming blank means not resigned

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.EmployeeName} [{self.EmpId}]"

    @classmethod
    def get_completion_list(cls, prefix_text, company_id):
        # This method mimics the GetCompletionList web method logic.
        # In a real scenario, filtering by CompId would require 'tblHR_OfficeStaff' to have a CompId column.
        # Assuming CompId is part of the WHERE clause 'CompId=' + CompId + ''
        # We also need to consider if ResignationDate='' means NULL or empty string. Assuming empty string for now.
        employees = cls.objects.filter(
            EmployeeName__icontains=prefix_text,
            # Assuming 'CompId' field exists in Employee model as per original code context
            # CompId=company_id, # Uncomment if CompId exists on this model
            ResignationDate__isnull=True # Better to check for null if not resigned
        ).order_by('EmployeeName')

        # Mimic exact ASP.NET logic of startsWith and then appending ID
        matching_employees = []
        for emp in employees:
            if emp.EmployeeName.lower().startswith(prefix_text.lower()):
                matching_employees.append(f"{emp.EmployeeName} [{emp.EmpId}]")
        return matching_employees[:10] # ASP.NET had a commented out length limit.

class SalaryMaster(models.Model):
    # Corresponds to tblHR_Salary_Master
    Id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is PK
    ChequeNo = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    ChequeNoDate = models.DateField(db_column='ChequeNoDate', blank=True, null=True)
    BankId = models.IntegerField(db_column='BankId', blank=True, null=True) # Raw ID, will be linked via property
    EmpDirect = models.CharField(db_column='EmpDirect', max_length=10, blank=True, null=True) # 0 for Employee, 1 for Directors
    TransNo = models.CharField(db_column='TransNo', max_length=50, blank=True, null=True) # Assuming string
    FMonth = models.IntegerField(db_column='FMonth', blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)
    FinYearId = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    ReleaseFlag = models.BooleanField(db_column='ReleaseFlag', default=False) # Assuming 1 is true, 0 is false
    EmpId = models.CharField(db_column='EmpId', max_length=50, blank=True, null=True) # Link to Employee
    # Assuming BGGroup from Employee model is sufficient, or if it's stored here for override.
    # BGGroup = models.CharField(db_column='BGGroup', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Master'
        verbose_name = 'Salary Master'
        verbose_name_plural = 'Salary Masters'

    def __str__(self):
        return f"Salary Master ID: {self.Id} (Cheque: {self.ChequeNo})"

    @property
    def bank_name(self):
        try:
            return Bank.objects.get(Id=self.BankId).Name if self.BankId else None
        except Bank.DoesNotExist:
            return None

    @classmethod
    def get_bank_statement_summary(cls, month_id, company_id, fin_year_id):
        # Mimics loaddata() logic.
        # This query needs to perform a GROUP BY, which Django ORM handles for distinct values.
        # However, the ASP.NET query was `Group By ChequeNo,ChequeNoDate,BankId,EmpDirect,TransNo`
        # and then iterated to build a custom DataTable.
        # A direct ORM approach for summary might look like this:
        qs = cls.objects.filter(
            FMonth=month_id,
            CompId=company_id,
            FinYearId=fin_year_id,
            ReleaseFlag=True
        ).values('ChequeNo', 'ChequeNoDate', 'BankId', 'EmpDirect', 'TransNo').distinct()

        results = []
        for item in qs:
            bank_name_obj = Bank.objects.filter(Id=item['BankId']).first()
            bank_name = bank_name_obj.Name if bank_name_obj else 'N/A'
            results.append({
                'ChequeNo': item['ChequeNo'],
                'ChequeNoDate': item['ChequeNoDate'].strftime('%d-%m-%Y') if item['ChequeNoDate'] else '', # Format as per ASP.NET output
                'BankName': bank_name,
                'TransNo': item['TransNo'],
                'BankId': item['BankId'],
                'Type': item['EmpDirect'] # Mapped to Type in ASP.NET GridView
            })
        return results

    def check_salary_details_exist(self):
        # For 'Salary Slip' report, checks if salary details exist
        return SalaryDetail.objects.filter(
            MId=self.Id, # Assuming SalaryDetail has MId linking to SalaryMaster.Id
            # Add other relevant filters like CompId, FinYearId, FMonth if they exist on SalaryDetail
        ).exists()


class SalaryDetail(models.Model):
    # Corresponds to tblHR_Salary_Details
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MId = models.IntegerField(db_column='MId', blank=True, null=True) # Foreign key to SalaryMaster.Id

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Details'
        verbose_name = 'Salary Detail'
        verbose_name_plural = 'Salary Details'

    def __str__(self):
        return f"Salary Detail for Master ID: {self.MId}"

# Utility to get months, mimicking fun.GetMonth
# This might fetch from a lookup table or distinct months from SalaryMaster.
def get_available_months(company_id, fin_year_id):
    # Placeholder: In a real ERP, this might come from a predefined list or a calendar setup.
    # Here, we assume it's a fixed list or derived from available data in SalaryMaster.
    # For now, let's provide a static list for demonstration.
    MONTH_CHOICES = [
        (1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'),
        (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'),
        (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')
    ]
    # If fetching from DB, example:
    # distinct_months = SalaryMaster.objects.filter(CompId=company_id, FinYearId=fin_year_id).values_list('FMonth', flat=True).distinct().order_by('FMonth')
    # return [(m, dict(MONTH_CHOICES)[m]) for m in distinct_months]
    return MONTH_CHOICES

```

##### 4.2 Forms (`hr_reports/forms.py`)

This form will combine all the filter elements from the ASP.NET page.

```python
from django import forms
from .models import Bank, BusinessGroup, get_available_months, Employee # Import Employee for autocomplete context

class SalaryPrintForm(forms.Form):
    # Assuming CompId and FinYearId will come from session/user context in the view.
    # Hardcoding for demonstration, but these would be dynamic.
    COMPANY_ID = 1 # Example
    FIN_YEAR_ID = 1 # Example

    month_choices = get_available_months(COMPANY_ID, FIN_YEAR_ID)
    ddl_month = forms.ChoiceField(
        choices=month_choices,
        label="Select Month",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': 'hx-post="{% url "hr_reports:get_cheque_transactions" %}"', # HTMX to refresh table
            'hx-target': '#chequeTable-container',
            'hx-indicator': '#loading-indicator',
            'hx-swap': 'innerHTML'
        })
    )

    ddl_bggroup = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        empty_label="-- Select BG Group --",
        label="BG Group",
        to_field_name='Id', # Use 'Id' for data value
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    REPORT_TYPE_CHOICES = [
        ('0', 'Salary Slip'),
        ('1', 'Salary Slip of All Employees'),
        ('2', 'On Cash Report'),
        ('3', 'Over Time Report'),
        ('4', 'SAPL Summary Report'),
        ('5', 'Neha Summary Report'),
        ('6', 'Bank Statement'),
        ('7', 'All Month Summary Report'),
        ('8', 'Consolidated Summary Report'),
    ]
    radio_report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'space-y-2'}),
        label="Report Options",
        initial='0', # Default to Salary Slip as per ASP.NET
        # HTMX to dynamically show/hide month/cheque fields
        hx_trigger="change",
        hx_post="{% url 'hr_reports:update_form_visibility' %}",
        hx_target="#form-dynamic-fields",
        hx_swap="innerHTML"
    )

    txt_emp_search = forms.CharField(
        label="Employee Search",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-get': "{% url 'hr_reports:employee_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        })
    )
    # Hidden field to hold selected employee ID from autocomplete, if needed for backend validation
    selected_emp_id = forms.CharField(widget=forms.HiddenInput(), required=False)

    txt_cheque_no = forms.CharField(
        label="Cheque No.",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    txt_date = forms.DateField(
        label="Date",
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # Use HTML5 date input type for native picker
        }, format='%Y-%m-%d') # HTML5 date input expects YYYY-MM-DD
    )

    ddl_bank_name = forms.ModelChoiceField(
        queryset=Bank.objects.exclude(Id=4), # Based on WHERE [Id]!='4' in SqlDataSource1
        empty_label="-- Select Bank --",
        label="Bank Name",
        to_field_name='Id',
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    EMP_DIRECT_CHOICES = [
        ('0', 'Employee'),
        ('1', 'Directors'),
    ]
    ddl_emp_or_direct = forms.ChoiceField(
        choices=EMP_DIRECT_CHOICES,
        label="", # Label is part of the bold text in ASP.NET, not a separate label element
        initial='0',
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Default month selection as per ASP.NET (MonthId = 4 initially)
        if not self.is_bound:
            self.fields['ddl_month'].initial = 4 # Or from request.session.get('MonthId', 4)

    def clean(self):
        cleaned_data = super().clean()
        report_type = cleaned_data.get('radio_report_type')

        # Implement ASP.NET specific validation
        if report_type == '0': # Salary Slip
            emp_search = cleaned_data.get('txt_emp_search')
            month = cleaned_data.get('ddl_month')
            if not emp_search or not month:
                self.add_error(None, "Invalid Employee Name or Month.")
                # The ASP.NET code for this case also checks BG Group:
                # if DSEmp.Tables[0].Rows[0]["BGGroup"].ToString() == BGGroupId || BGGroupId == "1"
                # This complex validation needs to be moved to a fat model or a service layer.
                # For brevity in form validation, only basic presence is checked here.

        elif report_type == '6': # Bank Statement
            cheque_no = cleaned_data.get('txt_cheque_no')
            cheque_date = cleaned_data.get('txt_date')
            if not cheque_no or not cheque_date:
                self.add_error(None, "Invalid Cheque No. or Date.")

        return cleaned_data

```

##### 4.3 Views (`hr_reports/views.py`)

We need a main view to render the form and the data table. We'll also need separate HTMX endpoints for auto-completion, dynamic form field visibility, data table refresh, and report generation.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.shortcuts import render
from django.db import connection

from .forms import SalaryPrintForm
from .models import SalaryMaster, Employee, BusinessGroup, Bank # Ensure all models are imported

# Assume these are loaded from session/user profile in a real app
# For demonstration:
DEFAULT_COMPANY_ID = 1
DEFAULT_FIN_YEAR_ID = 1

class SalaryPrintView(TemplateView):
    template_name = 'hr_reports/salary_print/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with data, potentially from session or request
        # For initial load, ddl_month is set to 4 in form's __init__
        initial_month = self.request.GET.get('month', '4') # From QueryString or default
        form = SalaryPrintForm(initial={'ddl_month': initial_month})
        context['form'] = form

        # Determine if month field should be visible based on radio button selection
        selected_report_type = form['radio_report_type'].value()
        context['show_month_field'] = selected_report_type not in ['7', '8']
        context['show_cheque_details'] = selected_report_type == '6'

        # Initial load of cheque transactions based on default month
        # This will be replaced by an HTMX load for dynamic refresh.
        context['cheque_transactions'] = [] # DataTables will load this via separate HTMX endpoint
        return context

    def get(self, request, *args, **kwargs):
        context = self.get_context_data()
        return self.render_to_response(context)


class ChequeTransactionsPartialView(View):
    """
    HTMX endpoint to load/refresh the cheque transactions table.
    Mimics loaddata() logic.
    """
    def post(self, request, *args, **kwargs):
        form = SalaryPrintForm(request.POST)
        month_id = None
        if form.is_valid():
            month_id = form.cleaned_data['ddl_month']
        else:
            # Fallback to initial month if form is not valid (e.g., initial load)
            month_id = request.session.get('current_month_id', '4')

        # Company and financial year IDs should come from session/user context
        company_id = request.session.get('compid', DEFAULT_COMPANY_ID)
        fin_year_id = request.session.get('finyear', DEFAULT_FIN_YEAR_ID)

        cheque_transactions = SalaryMaster.get_bank_statement_summary(
            month_id, company_id, fin_year_id
        )
        return render(request, 'hr_reports/salary_print/_cheque_transactions_table.html', {
            'cheque_transactions': cheque_transactions
        })

    def get(self, request, *args, **kwargs):
        # Allow GET for initial load or simple refresh.
        month_id = request.GET.get('ddl_month', request.session.get('current_month_id', '4'))
        company_id = request.session.get('compid', DEFAULT_COMPANY_ID)
        fin_year_id = request.session.get('finyear', DEFAULT_FIN_YEAR_ID)

        cheque_transactions = SalaryMaster.get_bank_statement_summary(
            month_id, company_id, fin_year_id
        )
        return render(request, 'hr_reports/salary_print/_cheque_transactions_table.html', {
            'cheque_transactions': cheque_transactions
        })


class EmployeeAutocompleteView(View):
    """
    HTMX endpoint for employee search autocomplete.
    Mimics GetCompletionList web method.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        # Company ID from session/user context
        company_id = request.session.get('compid', DEFAULT_COMPANY_ID)
        suggestions = Employee.get_completion_list(prefix_text, company_id)
        # Return suggestions as HTML for HTMX to swap directly into a div
        # or as JSON to be handled by Alpine.js/some JS if needed.
        # For HTMX, simple list of items:
        html_response = ""
        if suggestions:
            html_response = "<ul class='border border-gray-300 bg-white rounded-md shadow-lg max-h-48 overflow-y-auto z-10'>"
            for s in suggestions:
                # Store EmpId in data-emp-id for easier selection
                emp_name, emp_id = s.rsplit(' [', 1)
                emp_id = emp_id[:-1] # Remove closing bracket
                html_response += f"<li class='p-2 hover:bg-blue-100 cursor-pointer' data-emp-id='{emp_id}' hx-on:click='this.closest(\"div\").querySelector(\"[name=\"txt_emp_search\"]\").value=\"{emp_name}\"; this.closest(\"div\").querySelector(\"[name=\"selected_emp_id\"]\").value=\"{emp_id}\"; this.outerHTML=\"\";'><a>{s}</a></li>"
            html_response += "</ul>"
        return HttpResponse(html_response)


class ReportGenerationView(View):
    """
    HTMX endpoint to handle 'Proceed' button click for report generation.
    Mimics Button1_Click logic.
    """
    def post(self, request, *args, **kwargs):
        form = SalaryPrintForm(request.POST)
        if form.is_valid():
            report_type = form.cleaned_data['radio_report_type']
            month_id = form.cleaned_data.get('ddl_month')
            bg_group_id = form.cleaned_data.get('ddl_bggroup')
            emp_search_full = form.cleaned_data.get('txt_emp_search')
            selected_emp_id = form.cleaned_data.get('selected_emp_id') # Get EmpId from hidden field
            cheque_no = form.cleaned_data.get('txt_cheque_no')
            cheque_date = form.cleaned_data.get('txt_date')
            bank_id = form.cleaned_data.get('ddl_bank_name')
            emp_direct = form.cleaned_data.get('ddl_emp_or_direct')

            # Company and financial year IDs from session/user context
            company_id = request.session.get('compid', DEFAULT_COMPANY_ID)
            fin_year_id = request.session.get('finyear', DEFAULT_FIN_YEAR_ID)

            # --- Logic based on report_type (mimicking ASP.NET switch cases) ---
            response_content = ""
            status_code = 200

            if report_type == '0': # Salary Slip
                emp_id = selected_emp_id # Prefer EmpId if available from autocomplete
                if not emp_id and emp_search_full: # Try to extract if only name typed
                    try:
                        emp_id = emp_search_full.split(' [')[-1][:-1]
                    except IndexError:
                        pass # Could not parse EmpId

                if not emp_id or not month_id:
                    messages.error(request, "Invalid Employee Name or Month.")
                    response_content = "<div class='text-red-600 p-4'>Invalid Employee Name or Month.</div>"
                else:
                    employee = Employee.objects.filter(EmpId=emp_id).first()
                    # Check BG Group logic as per ASP.NET
                    is_valid_bg_group = False
                    if employee:
                        if employee.BGGroup == str(bg_group_id) or str(bg_group_id) == '1': # Assuming BGGroup is string or can be compared
                            is_valid_bg_group = True
                    elif str(bg_group_id) == '1': # If no employee found, and BGGroup is default '1'
                        is_valid_bg_group = True

                    if not is_valid_bg_group:
                        messages.error(request, "Invalid BG Group for this employee.")
                        response_content = "<div class='text-red-600 p-4'>Invalid BG Group for this employee.</div>"
                    else:
                        # Check Salary Details exist (mimics fun.select("Count(*) As Cnt", "tblHR_Salary_Details,tblHR_Salary_Master", ...))
                        # This requires a more complex query if not directly tied to SalaryMaster.Id
                        # For simplicity, assuming a method on SalaryMaster can check this
                        salary_records_exist = SalaryMaster.objects.filter(
                            EmpId=emp_id, FMonth=month_id, CompId=company_id, FinYearId=fin_year_id
                        ).exists() # Simplification, original query was more complex

                        if salary_records_exist:
                            # In ASP.NET, this redirects to Salary_Print_Details.aspx
                            # In HTMX, we would swap in the report content or trigger a modal
                            # For now, a placeholder or direct content.
                            # You might trigger a new window/tab for a PDF report here.
                            messages.success(request, f"Generating Salary Slip for Employee {emp_id}, Month {month_id}...")
                            report_url = reverse_lazy('hr_reports:generate_salary_slip', kwargs={'emp_id': emp_id, 'month_id': month_id})
                            # Option 1: Trigger a full page reload or new window (less HTMX-y but mimics redirect)
                            # response = HttpResponse(status=204)
                            # response['HX-Redirect'] = report_url # This header makes HTMX redirect
                            # return response
                            # Option 2: Render content into a designated div
                            response_content = f"<div class='p-4 border border-blue-300 bg-blue-50'>Report ready. <a href='{report_url}' target='_blank' class='text-blue-700 underline'>Click here to view Salary Slip</a></div>"
                            # Or render an actual partial template:
                            # return render(request, 'hr_reports/reports/salary_slip_details_partial.html', {'emp_id': emp_id, 'month_id': month_id})
                        else:
                            messages.warning(request, "No Record Found for Salary Slip.")
                            response_content = "<div class='text-orange-600 p-4'>No Record Found for Salary Slip!</div>"
            elif report_type == '6': # Bank Statement
                if not cheque_no or not cheque_date:
                    messages.error(request, "Invalid Cheque No. or Date.")
                    response_content = "<div class='text-red-600 p-4'>Invalid Cheque No. or Date.</div>"
                else:
                    # In ASP.NET, this redirects to Salary_BankStatement_Check.aspx
                    messages.success(request, f"Generating Bank Statement for Cheque No. {cheque_no} on {cheque_date}...")
                    report_url = reverse_lazy('hr_reports:generate_bank_statement', kwargs={
                        'cheque_no': cheque_no, 'cheque_date': cheque_date.strftime('%Y-%m-%d'),
                        'bank_id': bank_id, 'emp_direct': emp_direct, 'bg_group_id': bg_group_id,
                        'month_id': month_id
                    })
                    response_content = f"<div class='p-4 border border-blue-300 bg-blue-50'>Report ready. <a href='{report_url}' target='_blank' class='text-blue-700 underline'>Click here to view Bank Statement</a></div>"

            else: # Other reports (1, 2, 3, 4, 5, 7, 8) - all are redirects in ASP.NET
                # In HTMX, this means swapping in a partial report view or a link.
                report_name_map = {
                    '1': 'Salary Slip of All Employees', '2': 'On Cash Report',
                    '3': 'Over Time Report', '4': 'SAPL Summary Report',
                    '5': 'Neha Summary Report', '7': 'All Month Summary Report',
                    '8': 'Consolidated Summary Report'
                }
                report_display_name = report_name_map.get(report_type, f'Report Type {report_type}')
                # Placeholder for the actual report URL or content generation
                report_url = reverse_lazy('hr_reports:generate_generic_report', kwargs={
                    'report_type': report_type, 'month_id': month_id, 'bg_group_id': bg_group_id
                })
                messages.info(request, f"Generating {report_display_name}...")
                response_content = f"<div class='p-4 border border-blue-300 bg-blue-50'>Report ready. <a href='{report_url}' target='_blank' class='text-blue-700 underline'>Click here to view {report_display_name}</a></div>"

            return HttpResponse(response_content, status=status_code)
        else:
            # Form is invalid, re-render the form with errors
            response = render(request, 'hr_reports/salary_print/_report_form_fields.html', {'form': form})
            # Add a header to indicate an error for HTMX, if needed
            response['HX-Retarget'] = '#report-generation-messages' # Target error messages to a specific div
            response['HX-Reswap'] = 'innerHTML'
            return response


class UpdateFormVisibilityPartialView(View):
    """
    HTMX endpoint to update form field visibility based on radio button selection.
    Mimics the ASP.NET `if (RadioButtonList1.SelectedValue == "7" || RadioButtonList1.SelectedValue == "8")`
    """
    def post(self, request, *args, **kwargs):
        form = SalaryPrintForm(request.POST)
        # We need to re-render the dynamic parts of the form template.
        # This view doesn't fully validate the form, just reads the selected radio button.
        selected_report_type = request.POST.get('radio_report_type', '0')

        context = {
            'form': form,
            'show_month_field': selected_report_type not in ['7', '8'],
            'show_cheque_details': selected_report_type == '6',
        }
        return render(request, 'hr_reports/salary_print/_report_form_fields.html', context)


class ChequeTransactionEditView(View):
    """
    HTMX endpoint for 'Select' command on GridView row.
    Mimics Salary_BankStatement_CheckEdit.aspx
    """
    def get(self, request, trans_no, month_id, *args, **kwargs):
        # In a real app, fetch the transaction details
        # For now, return a placeholder form/message
        messages.info(request, f"Loading edit form for transaction {trans_no} (Month: {month_id})...")
        return HttpResponse(f"<div class='p-4'>Edit form for transaction <strong>{trans_no}</strong> (Month: {month_id}) will load here.</div>")

class ChequeTransactionPrintView(View):
    """
    HTMX endpoint for 'Print' command on GridView row.
    Mimics Salary_BankStatement.aspx
    """
    def get(self, request, trans_no, month_id, *args, **kwargs):
        # In a real app, generate/return the printable report (PDF/HTML)
        messages.info(request, f"Generating print view for transaction {trans_no} (Month: {month_id})...")
        return HttpResponse(f"<div class='p-4'>Printable report for transaction <strong>{trans_no}</strong> (Month: {month_id}) will appear here.</div>")

# Placeholder views for the different report types
# In a real application, these would be proper views that generate the respective reports (HTML, PDF, etc.)
class GenericReportView(View):
    def get(self, request, report_type, month_id=None, bg_group_id=None, emp_id=None, cheque_no=None, cheque_date=None, bank_id=None, emp_direct=None, *args, **kwargs):
        report_names = {
            '0': 'Salary Slip Details', '1': 'Salary Slip of All Employees',
            '2': 'On Cash Report', '3': 'Over Time Report',
            '4': 'SAPL Summary Report', '5': 'Neha Summary Report',
            '6': 'Bank Statement Check', '7': 'All Month Summary Report',
            '8': 'Consolidated Summary Report'
        }
        report_name = report_names.get(report_type, f"Unknown Report Type {report_type}")
        return HttpResponse(f"<div class='p-6 text-xl font-bold'>This is the {report_name} Page. Parameters: Month={month_id}, BGGroup={bg_group_id}, Emp={emp_id}, Cheque={cheque_no}, Date={cheque_date}</div>")

```

##### 4.4 Templates (`hr_reports/templates/hr_reports/salary_print/`)

```html
<!-- hr_reports/templates/hr_reports/salary_print/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Payroll - Print</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="report-form" hx-post="{% url 'hr_reports:generate_report' %}" hx-swap="outerHTML" hx-target="#report-output" hx-indicator="#report-loading-indicator">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <!-- Month & BG Group -->
                <div id="month-bg-group-fields">
                    <div class="mb-4" {% if not show_month_field %}style="display:none;"{% endif %}>
                        <label for="{{ form.ddl_month.id_for_label }}" class="block text-sm font-medium text-gray-700">Select Month</label>
                        {{ form.ddl_month }}
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.ddl_bggroup.id_for_label }}" class="block text-sm font-medium text-gray-700">BG Group</label>
                        {{ form.ddl_bggroup }}
                    </div>
                </div>

                <!-- Report Type Radio Buttons -->
                <div class="mb-4">
                    <p class="block text-sm font-medium text-gray-700 mb-2">Report Options:</p>
                    <div id="radio-list-container">
                        {% for radio in form.radio_report_type %}
                        <div class="flex items-center mb-2">
                            {{ radio.tag }}
                            <label for="{{ radio.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">{{ radio.choice_label }}</label>
                        </div>
                        {% endfor %}
                    </div>
                    {% if form.radio_report_type.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.radio_report_type.errors }}</p>
                    {% endif %}
                </div>

                <!-- Employee Search & Cheque Details (dynamic) -->
                <div id="form-dynamic-fields" class="space-y-4">
                    {% include 'hr_reports/salary_print/_report_form_fields.html' %}
                </div>
            </div>

            <div class="mt-6 flex justify-center">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Proceed
                </button>
            </div>
        </form>

        <div id="report-loading-indicator" class="htmx-indicator text-center mt-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Generating Report...</p>
        </div>
        <div id="report-generation-messages" class="mt-4">
             {% for message in messages %}
                <div class="p-3 mb-3 text-sm rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'info' %}bg-blue-100 text-blue-700{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
        <div id="report-output" class="mt-6">
            <!-- Report content will be swapped here by HTMX -->
        </div>
    </div>

    <!-- Cheque Transactions GridView Replacement -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-4">Bank Statement Cheque Transactions</h3>
        <div id="chequeTable-container"
             hx-trigger="load, refreshChequeTransactions from:body"
             hx-post="{% url 'hr_reports:get_cheque_transactions' %}"
             hx-indicator="#cheque-loading-indicator"
             hx-swap="innerHTML">
            <!-- Initial content will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Cheque Transactions...</p>
            </div>
        </div>
        <div id="cheque-loading-indicator" class="htmx-indicator text-center mt-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Updating table...</p>
        </div>
    </div>

    <!-- Modal for Edit/Print actions or general purpose -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full mx-4"
             _="on htmx:afterSwap if my.contains(event.target) add .is-active to #modal">
            <!-- Content loaded via HTMX -->
            <button class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold"
                    _="on click remove .is-active from #modal">
                &times;
            </button>
        </div>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS & CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('formLogic', () => ({
            init() {
                // Initial form state handling if needed beyond HTMX
                // HTMX handles most dynamic show/hide directly via hx-swap
            }
        }));

        // For employee autocomplete selection
        htmx.on('body', 'click', function(evt) {
            const target = evt.target.closest('li');
            if (target && target.hasAttribute('data-emp-id')) {
                const empSearchInput = document.querySelector('[name="txt_emp_search"]');
                const selectedEmpIdInput = document.querySelector('[name="selected_emp_id"]');
                if (empSearchInput && selectedEmpIdInput) {
                    // Update the visible input with the name part
                    const empName = target.textContent.split(' [')[0];
                    empSearchInput.value = empName;
                    // Update the hidden input with the EmpId
                    selectedEmpIdInput.value = target.getAttribute('data-emp-id');
                }
                // Clear suggestions
                const suggestionsDiv = document.getElementById('employee-suggestions');
                if (suggestionsDiv) {
                    suggestionsDiv.innerHTML = '';
                }
            }
        });

        // Close modal on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modal = document.getElementById('modal');
                if (modal.classList.contains('is-active')) {
                    modal.classList.remove('is-active');
                }
            }
        });
    });
</script>
{% endblock %}

```

```html
<!-- hr_reports/templates/hr_reports/salary_print/_cheque_transactions_table.html -->
<!-- This is a partial template loaded via HTMX -->
<div class="overflow-x-auto">
    <table id="chequeTransactionsTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <!-- Hidden columns for DataTables, but useful for row commands -->
                <th class="hidden">TransNo</th>
                <th class="hidden">BankId</th>
                <th class="hidden">Type</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in cheque_transactions %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-700">{{ obj.ChequeNo }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-700">{{ obj.ChequeNoDate }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-700">{{ obj.BankName }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-700 whitespace-nowrap">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs"
                        hx-get="{% url 'hr_reports:cheque_transaction_edit' trans_no=obj.TransNo month_id=form.ddl_month.value %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Select
                    </button>
                    <button
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded-md text-xs"
                        hx-get="{% url 'hr_reports:cheque_transaction_print' trans_no=obj.TransNo month_id=form.ddl_month.value %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Print
                    </button>
                </td>
                <td class="hidden">{{ obj.TransNo }}</td>
                <td class="hidden">{{ obj.BankId }}</td>
                <td class="hidden">{{ obj.Type }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#chequeTransactionsTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "visible": false, "targets": [5, 6, 7] } // Hide TransNo, BankId, Type columns
        ]
    });
});
</script>
```

```html
<!-- hr_reports/templates/hr_reports/salary_print/_report_form_fields.html -->
<!-- Partial for dynamic form fields -->
<div class="space-y-4">
    {% if not form.ddl_month.value in ['7', '8'] %}
        <div class="mb-4">
            <label for="{{ form.txt_emp_search.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Search</label>
            {{ form.txt_emp_search }}
            <div id="employee-suggestions" class="relative mt-1">
                <!-- Autocomplete suggestions will be loaded here -->
            </div>
            {{ form.selected_emp_id }} {# Hidden field for selected employee ID #}
            {% if form.txt_emp_search.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.txt_emp_search.errors }}</p>
            {% endif %}
        </div>
    {% endif %}

    {% if form.radio_report_type.value == '6' %}
        <div class="mb-4">
            <label for="{{ form.txt_cheque_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Cheque No.</label>
            {{ form.txt_cheque_no }}
            {% if form.txt_cheque_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.txt_cheque_no.errors }}</p>
            {% endif %}
        </div>
        <div class="mb-4">
            <label for="{{ form.txt_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Date</label>
            {{ form.txt_date }}
            {% if form.txt_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.txt_date.errors }}</p>
            {% endif %}
        </div>
        <div class="mb-4">
            <label for="{{ form.ddl_bank_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Name</label>
            {{ form.ddl_bank_name }}
            {% if form.ddl_bank_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.ddl_bank_name.errors }}</p>
            {% endif %}
        </div>
        <div class="mb-4">
            <p class="block text-sm font-medium text-gray-700">Employee/Directors</p>
            {{ form.ddl_emp_or_direct }}
            {% if form.ddl_emp_or_direct.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.ddl_emp_or_direct.errors }}</p>
            {% endif %}
        </div>
    {% endif %}
</div>
```

##### 4.5 URLs (`hr_reports/urls.py`)

```python
from django.urls import path
from .views import (
    SalaryPrintView, ChequeTransactionsPartialView, EmployeeAutocompleteView,
    ReportGenerationView, UpdateFormVisibilityPartialView,
    ChequeTransactionEditView, ChequeTransactionPrintView,
    GenericReportView # Placeholder for other reports
)

app_name = 'hr_reports'

urlpatterns = [
    path('salary-print/', SalaryPrintView.as_view(), name='salary_print_list'),

    # HTMX endpoints for dynamic content
    path('salary-print/cheque-transactions/', ChequeTransactionsPartialView.as_view(), name='get_cheque_transactions'),
    path('salary-print/employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    path('salary-print/generate-report/', ReportGenerationView.as_view(), name='generate_report'),
    path('salary-print/update-form-visibility/', UpdateFormVisibilityPartialView.as_view(), name='update_form_visibility'),

    # HTMX/Redirect targets for GridView actions
    path('salary-print/cheque-transaction-edit/<str:trans_no>/<int:month_id>/', ChequeTransactionEditView.as_view(), name='cheque_transaction_edit'),
    path('salary-print/cheque-transaction-print/<str:trans_no>/<int:month_id>/', ChequeTransactionPrintView.as_view(), name='cheque_transaction_print'),

    # Placeholder URLs for actual report generation
    path('reports/salary-slip/<str:emp_id>/<int:month_id>/', GenericReportView.as_view(), name='generate_salary_slip'),
    path('reports/bank-statement/<str:cheque_no>/<str:cheque_date>/<int:bank_id>/<str:emp_direct>/<int:bg_group_id>/<int:month_id>/', GenericReportView.as_view(), name='generate_bank_statement'),
    path('reports/generic/<str:report_type>/', GenericReportView.as_view(), name='generate_generic_report'),
    path('reports/generic/<str:report_type>/<int:month_id>/<int:bg_group_id>/', GenericReportView.as_view(), name='generate_generic_report'),

]
```

##### 4.6 Tests (`hr_reports/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from .models import Bank, BusinessGroup, Employee, SalaryMaster, SalaryDetail, get_available_months
from .forms import SalaryPrintForm

# Mocking session data for tests, as ASP.NET uses Session heavily
class MockSession:
    def __init__(self, data=None):
        self._data = data if data is not None else {}

    def get(self, key, default=None):
        return self._data.get(key, default)

    def __setitem__(self, key, value):
        self._data[key] = value

    def __getitem__(self, key):
        return self._data[key]

    def __contains__(self, key):
        return key in self._data

@patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
class SalaryPrintModelTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_get_months):
        # Create test data for all models
        cls.bank1 = Bank.objects.create(Id=1, Name='Test Bank A')
        cls.bank2 = Bank.objects.create(Id=2, Name='Test Bank B')
        cls.bank_excluded = Bank.objects.create(Id=4, Name='Excluded Bank') # Based on exclude(Id=4)

        cls.bg1 = BusinessGroup.objects.create(Id=1, Symbol='BG1')
        cls.bg2 = BusinessGroup.objects.create(Id=2, Symbol='BG2')

        cls.employee1 = Employee.objects.create(EmpId='EMP001', EmployeeName='John Doe', BGGroup='BG1', ResignationDate=None)
        cls.employee2 = Employee.objects.create(EmpId='EMP002', EmployeeName='Jane Smith', BGGroup='BG2', ResignationDate=None)
        cls.employee_resigned = Employee.objects.create(EmpId='EMP003', EmployeeName='Retired Guy', ResignationDate=date(2023,1,1))

        cls.salary_master1 = SalaryMaster.objects.create(
            Id=101, ChequeNo='CHQ001', ChequeNoDate=date(2024,4,15), BankId=cls.bank1.Id,
            EmpDirect='0', TransNo='TRN101', FMonth=4, CompId=1, FinYearId=1, ReleaseFlag=True, EmpId='EMP001'
        )
        cls.salary_master2 = SalaryMaster.objects.create(
            Id=102, ChequeNo='CHQ002', ChequeNoDate=date(2024,4,20), BankId=cls.bank2.Id,
            EmpDirect='1', TransNo='TRN102', FMonth=4, CompId=1, FinYearId=1, ReleaseFlag=True, EmpId='EMP002'
        )
        cls.salary_master_unreleased = SalaryMaster.objects.create(
            Id=103, ChequeNo='CHQ003', ChequeNoDate=date(2024,4,25), BankId=cls.bank1.Id,
            EmpDirect='0', TransNo='TRN103', FMonth=4, CompId=1, FinYearId=1, ReleaseFlag=False, EmpId='EMP001'
        )

        cls.salary_detail1 = SalaryDetail.objects.create(Id=201, MId=cls.salary_master1.Id)

    def test_bank_model_creation(self):
        self.assertEqual(self.bank1.Name, 'Test Bank A')
        self.assertEqual(str(self.bank1), 'Test Bank A')

    def test_business_group_model_creation(self):
        self.assertEqual(self.bg1.Symbol, 'BG1')
        self.assertEqual(str(self.bg1), 'BG1')

    def test_employee_model_creation(self):
        self.assertEqual(self.employee1.EmployeeName, 'John Doe')
        self.assertEqual(str(self.employee1), 'John Doe [EMP001]')
        self.assertIsNone(self.employee1.ResignationDate)

    def test_employee_get_completion_list(self):
        # Patch the company_id in this context if Employee model has it, or pass explicitly
        with patch('hr_reports.views.DEFAULT_COMPANY_ID', 1):
            suggestions = Employee.get_completion_list('john', 1)
            self.assertIn('John Doe [EMP001]', suggestions)
            self.assertNotIn('Jane Smith [EMP002]', suggestions)
            self.assertNotIn('Retired Guy [EMP003]', suggestions) # Resigned employee should not appear

            suggestions = Employee.get_completion_list('Jane', 1)
            self.assertIn('Jane Smith [EMP002]', suggestions)

            suggestions = Employee.get_completion_list('Reti', 1)
            self.assertNotIn('Retired Guy [EMP003]', suggestions) # Resigned should be excluded

    def test_salary_master_model_creation(self):
        self.assertEqual(self.salary_master1.ChequeNo, 'CHQ001')
        self.assertEqual(self.salary_master1.bank_name, 'Test Bank A')
        self.assertTrue(self.salary_master1.ReleaseFlag)

    def test_salary_master_bank_name_property_no_bank(self):
        temp_sm = SalaryMaster.objects.create(
            Id=104, ChequeNo='CHQ004', ChequeNoDate=date(2024,5,1), BankId=999, # Non-existent bank
            EmpDirect='0', TransNo='TRN104', FMonth=5, CompId=1, FinYearId=1, ReleaseFlag=True
        )
        self.assertIsNone(temp_sm.bank_name)
        temp_sm.delete()

    def test_salary_master_get_bank_statement_summary(self):
        summary = SalaryMaster.get_bank_statement_summary(4, 1, 1)
        self.assertEqual(len(summary), 2) # Should include salary_master1 and salary_master2, not unreleased
        self.assertIn({'ChequeNo': 'CHQ001', 'ChequeNoDate': '15-04-2024', 'BankName': 'Test Bank A', 'TransNo': 'TRN101', 'BankId': 1, 'Type': '0'}, summary)
        self.assertIn({'ChequeNo': 'CHQ002', 'ChequeNoDate': '20-04-2024', 'BankName': 'Test Bank B', 'TransNo': 'TRN102', 'BankId': 2, 'Type': '1'}, summary)
        self.assertNotIn({'ChequeNo': 'CHQ003', 'ChequeNoDate': '25-04-2024', 'BankName': 'Test Bank A', 'TransNo': 'TRN103', 'BankId': 1, 'Type': '0'}, summary)

    def test_salary_master_check_salary_details_exist(self):
        self.assertTrue(self.salary_master1.check_salary_details_exist())
        self.assertFalse(self.salary_master2.check_salary_details_exist()) # No salary_detail2 created

    def test_salary_detail_model_creation(self):
        self.assertEqual(self.salary_detail1.MId, self.salary_master1.Id)


class SalaryPrintViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal data for dropdowns
        cls.bank1 = Bank.objects.create(Id=1, Name='Test Bank A')
        cls.bg1 = BusinessGroup.objects.create(Id=1, Symbol='BG1')
        cls.employee1 = Employee.objects.create(EmpId='EMP001', EmployeeName='John Doe', BGGroup='BG1', ResignationDate=None)
        cls.salary_master1 = SalaryMaster.objects.create(
            Id=101, ChequeNo='CHQ001', ChequeNoDate=date(2024,4,15), BankId=cls.bank1.Id,
            EmpDirect='0', TransNo='TRN101', FMonth=4, CompId=1, FinYearId=1, ReleaseFlag=True, EmpId='EMP001'
        )
        cls.salary_detail1 = SalaryDetail.objects.create(Id=201, MId=cls.salary_master1.Id)

    def setUp(self):
        self.client = Client()
        # Mock session for the client
        self.client.session = MockSession({
            'compid': 1,
            'finyear': 1,
            'current_month_id': '4',
        })

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_salary_print_list_view_get(self, mock_get_months):
        response = self.client.get(reverse('hr_reports:salary_print_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salary_print/list.html')
        self.assertIsInstance(response.context['form'], SalaryPrintForm)
        self.assertTrue(response.context['show_month_field']) # Default radio '0' should show month
        self.assertFalse(response.context['show_cheque_details'])

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_cheque_transactions_partial_view_post(self, mock_get_months):
        response = self.client.post(
            reverse('hr_reports:get_cheque_transactions'),
            {'ddl_month': '4', 'ddl_bggroup': '1', 'radio_report_type': '0', 'txt_emp_search': '', 'txt_cheque_no': '', 'txt_date': '', 'ddl_bank_name': '', 'ddl_emp_or_direct': ''},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salary_print/_cheque_transactions_table.html')
        self.assertIn(b'CHQ001', response.content)
        self.assertIn(b'CHQ002', response.content)
        self.assertNotIn(b'CHQ003', response.content) # Unreleased

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_employee_autocomplete_view(self, mock_get_months):
        response = self.client.get(
            reverse('hr_reports:employee_autocomplete'),
            {'q': 'john'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'John Doe [EMP001]', response.content)
        self.assertNotIn(b'Jane Smith', response.content)

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_report_generation_salary_slip_success(self, mock_get_months):
        data = {
            'ddl_month': '4', 'ddl_bggroup': '1', 'radio_report_type': '0',
            'txt_emp_search': 'John Doe', 'selected_emp_id': 'EMP001',
            'txt_cheque_no': '', 'txt_date': '', 'ddl_bank_name': '', 'ddl_emp_or_direct': ''
        }
        response = self.client.post(
            reverse('hr_reports:generate_report'), data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Generating Salary Slip for Employee EMP001, Month 4...', response.content)
        self.assertIn(b'Click here to view Salary Slip', response.content)
        self.assertContains(response, 'success') # Check for messages

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_report_generation_salary_slip_no_record(self, mock_get_months):
        # Create a salary master record that doesn't have details
        SalaryMaster.objects.create(
            Id=105, ChequeNo='CHQ_NO_DETAIL', ChequeNoDate=date(2024,4,1), BankId=self.bank1.Id,
            EmpDirect='0', TransNo='TRN105', FMonth=4, CompId=1, FinYearId=1, ReleaseFlag=True, EmpId='EMP002' # Jane Smith
        )
        data = {
            'ddl_month': '4', 'ddl_bggroup': '2', 'radio_report_type': '0', # Jane Smith is BG2
            'txt_emp_search': 'Jane Smith', 'selected_emp_id': 'EMP002',
            'txt_cheque_no': '', 'txt_date': '', 'ddl_bank_name': '', 'ddl_emp_or_direct': ''
        }
        response = self.client.post(
            reverse('hr_reports:generate_report'), data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'No Record Found for Salary Slip!', response.content)
        self.assertContains(response, 'warning') # Check for messages

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_report_generation_bank_statement_success(self, mock_get_months):
        data = {
            'ddl_month': '4', 'ddl_bggroup': '1', 'radio_report_type': '6',
            'txt_emp_search': '', 'selected_emp_id': '',
            'txt_cheque_no': 'CHQ123', 'txt_date': '2024-04-01', # YYYY-MM-DD for Django DateField
            'ddl_bank_name': str(self.bank1.Id), 'ddl_emp_or_direct': '0'
        }
        response = self.client.post(
            reverse('hr_reports:generate_report'), data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Generating Bank Statement for Cheque No. CHQ123 on 2024-04-01...', response.content)
        self.assertIn(b'Click here to view Bank Statement', response.content)
        self.assertContains(response, 'success') # Check for messages

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_report_generation_bank_statement_invalid_data(self, mock_get_months):
        data = {
            'ddl_month': '4', 'ddl_bggroup': '1', 'radio_report_type': '6',
            'txt_emp_search': '', 'selected_emp_id': '',
            'txt_cheque_no': '', 'txt_date': '', # Missing required fields
            'ddl_bank_name': str(self.bank1.Id), 'ddl_emp_or_direct': '0'
        }
        response = self.client.post(
            reverse('hr_reports:generate_report'), data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200) # HTMX swaps partial, not redirect
        self.assertIn(b'Invalid Cheque No. or Date.', response.content)
        self.assertContains(response, 'error') # Check for messages

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_update_form_visibility_partial_view_month_hidden(self, mock_get_months):
        data = {'radio_report_type': '7'} # All Month Summary Report should hide month
        response = self.client.post(
            reverse('hr_reports:update_form_visibility'), data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salary_print/_report_form_fields.html')
        self.assertNotIn(b'Employee Search', response.content) # Emp search also hidden for type 7
        self.assertNotIn(b'Cheque No', response.content)

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_cheque_transaction_edit_view(self, mock_get_months):
        response = self.client.get(
            reverse('hr_reports:cheque_transaction_edit', kwargs={'trans_no': 'TRN101', 'month_id': 4}),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Edit form for transaction', response.content)

    @patch('hr_reports.models.get_available_months', return_value=[(1, 'Jan'), (2, 'Feb'), (3, 'Mar'), (4, 'Apr')])
    def test_cheque_transaction_print_view(self, mock_get_months):
        response = self.client.get(
            reverse('hr_reports:cheque_transaction_print', kwargs={'trans_no': 'TRN101', 'month_id': 4}),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Printable report for transaction', response.content)

```

#### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content:**
    *   The `ddl_month` dropdown has `hx-post` to `get_cheque_transactions` to refresh the `chequeTable-container`.
    *   The `radio_report_type` radio buttons have `hx-post` to `update_form_visibility` to dynamically show/hide `txt_emp_search` and `txt_cheque_no`/`txt_date`/`ddl_bank_name`/`ddl_emp_or_direct` sections.
    *   The `txt_emp_search` input has `hx-get` to `employee_autocomplete` for live search suggestions, targeting `#employee-suggestions`.
    *   The main "Proceed" button `hx-post` to `generate_report`, swapping content into `#report-output` and showing `#report-loading-indicator`.
    *   The "Select" and "Print" buttons in the DataTables rows use `hx-get` to `cheque_transaction_edit` and `cheque_transaction_print` respectively, targeting `#modalContent` and activating `#modal`.
    *   `HX-Trigger` headers (`refreshChequeTransactions`) are used by views upon successful form submission (not strictly needed for this module, but good practice for CRUD).
*   **Alpine.js for UI State:**
    *   Alpine.js is included, primarily for simple DOM manipulations like showing/hiding the modal. The `on click` handlers are used for this.
    *   A generic `alpine:init` is added for potential future complex UI state management.
*   **DataTables for List Views:**
    *   The `_cheque_transactions_table.html` partial initializes DataTables on the loaded table element using `$(document).ready(function() { $('#chequeTransactionsTable').DataTable(); });`.
    *   Column definitions are used to hide `TransNo`, `BankId`, `Type` as per ASP.NET `Visible="false"`.
*   **No Additional JavaScript:** All dynamic behavior is managed by HTMX and basic Alpine.js attributes, adhering to the requirement.
*   **DRY Templates:** Use of `{% include 'hr_reports/salary_print/_report_form_fields.html' %}` demonstrates DRY.

**Final Notes:**

*   **Session/User Context:** The `CompId` and `FinYearId` (and `username`) from ASP.NET Session are assumed to be available in Django views via `request.session` or `request.user` attributes. In a real application, you'd likely map these to user profiles or a dedicated session management middleware.
*   **Report Generation Details:** The `GenericReportView` and the content generated by `ReportGenerationView` are placeholders. In a full migration, each specific report type (Salary Slip, Bank Statement, etc.) would have its own detailed Django view and template to render the actual report content (potentially as HTML, PDF, or Excel).
*   **Date Format:** The `txt_date` in ASP.NET used `dd-MM-yyyy`. The Django form `DateInput` is configured to expect `YYYY-MM-DD` for HTML5 `type='date'`, which is standard. If the backend database expects a different format, `DATE_INPUT_FORMATS` in `settings.py` or a custom `DateField` with custom widget/converter would be needed. `SalaryMaster.get_bank_statement_summary` formats `ChequeNoDate` back to `dd-MM-yyyy` for consistency with original output.
*   **Error Handling:** Basic error messages are displayed using Django's messages framework and HTMX retargeting, similar to ASP.NET's `ClientScript.RegisterStartupScript(..., "alert('...')")`.
*   **CSS:** Tailwind CSS is used for all styling, replacing `Css/yui-datatable.css`, `Css/styles.css`, `Css/StyleSheet.css`, and inline styles.