This document outlines a comprehensive plan for modernizing the provided ASP.NET `BankLoan` application to a modern Django-based solution. The focus is on leveraging AI-assisted automation, adhering to Django best practices, and utilizing HTMX, Alpine.js, and DataTables for a rich, interactive user experience.

The original ASP.NET application provides a list of employees, allowing users to select multiple employees and then input bank loan details for each selected employee in a batch submission. This pattern will be transformed into a Django application using a model formset for efficient batch creation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the ASP.NET code, the following database tables are identified:

*   **`tblHR_OfficeStaff`**: This table holds employee information.
    *   Columns inferred: `UserID`, `BGGroup` (Business Group ID), `EmpId` (Employee ID, often numeric), `EmployeeName` (the core name part), `Title` (e.g., Mr., Ms. for full name), `FinYearId`, `CompId`, `ResignationDate`.
*   **`BusinessGroup`**: This table stores business group details.
    *   Columns inferred: `Id`, `Symbol` (group symbol/name).
*   **`tblHR_BankLoan`**: This table stores the bank loan details.
    *   Columns inferred: `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId` (user/session ID), `EmpId` (Employee ID), `BankName`, `Branch`, `Amount`, `Installment`, `fromDate`, `ToDate`.

**Inferred Relationships:**
*   `tblHR_OfficeStaff.BGGroup` likely references `BusinessGroup.Id`.
*   `tblHR_BankLoan.EmpId` likely references `tblHR_OfficeStaff.EmpId`.

## Step 2: Identify Backend Functionality

The ASP.NET application primarily provides the following functionalities:

*   **Read (R):**
    *   Displays a list of employees from `tblHR_OfficeStaff` based on search criteria (Employee Name or BG Group).
    *   Retrieves Business Group `Symbol` from `BusinessGroup` table.
*   **Create (C) / Batch Entry:**
    *   Allows users to select multiple employees from the displayed list.
    *   For each selected employee, users can input `BankName`, `Branch`, `Amount`, `Installment`, `FromDate`, and `ToDate`.
    *   A "Submit" button processes all selected entries and inserts new records into the `tblHR_BankLoan` table.
    *   Includes validation for required fields, numeric formats (`Amount`, `Installment`), and date formats.
*   **Search/Filter:**
    *   Filters employee list by "Employee Name" (with an auto-complete feature) or "BG Group".
    *   Toggles input fields (`TxtMrs` vs `TxtEmpName`) based on search field selection.
*   **UI-driven Validation:**
    *   Client-side validation using `RequiredFieldValidator` and `RegularExpressionValidator` which become visible/active upon checkbox selection in the `GridView`.

## Step 3: Infer UI Components

The ASP.NET controls translate to the following Django/HTMX/Alpine.js components:

*   **`DropDownList` (DrpField), `TextBox` (TxtMrs, TxtEmpName), `Button` (Button1)**: These form the search/filter interface. In Django, this will be handled by a standard HTML form. `TxtEmpName`'s auto-complete will be replaced by an HTMX-powered endpoint.
*   **`GridView` (GridView2)**: This is the main data display and input mechanism. This will be replaced by a DataTables-enhanced HTML `<table>` populated by a Django ModelFormset. Each row will contain a checkbox and associated input fields that are conditionally enabled/visible using Alpine.js based on the checkbox state.
*   **`AjaxControlToolkit.AutoCompleteExtender`**: Replaced by an HTMX `hx-get` attribute on the employee search text input, targeting a Django view that returns employee suggestions.
*   **`CalendarExtender`**: Replaced by HTML5 `type="date"` input fields with appropriate date formatting handled by Django forms and potentially a lightweight Alpine.js logic for custom display if native pickers are insufficient.
*   **`Panel` (Panel1)**: A simple scrollable container, replaced by standard HTML `div` with CSS styling.
*   **`Button` (BtnSubmit)**: The form submission button for the batch entry. This will trigger an HTMX `hx-post` to the Django view handling the formset submission.
*   **Validation Controls (`RequiredFieldValidator`, `RegularExpressionValidator`)**: Django forms handle server-side validation. Client-side, Alpine.js can manage input states and display validation messages based on form errors.

---

## Step 4: Generate Django Code

The Django application will be named `hr_bankloan`.

### 4.1 Models (`hr_bankloan/models.py`)

We'll define models for `Employee`, `BusinessGroup`, and `BankLoan`. We'll assume `EmpId` is unique and can be used as a primary key or a unique identifier, and `BGGroup` references `BusinessGroup`.

```python
from django.db import models
from django.db.models import F, Value
from django.db.models.functions import Concat
from django.utils import timezone

# Assume these are utility functions from the ASP.NET `clsFunctions`
# In a real scenario, these would be part of a proper utility module or integrated into models/forms.
def get_current_date_str():
    return timezone.now().strftime("%d-%m-%Y")

def get_current_time_str():
    return timezone.now().strftime("%H:%M:%S")

# Helper to extract EmpId from "EmployeeName [EmpId]" format
def extract_empid_from_name(full_name_with_id):
    if '[' in full_name_with_id and ']' in full_name_with_id:
        return full_name_with_id.split('[')[-1].strip(']')
    return full_name_with_id # Return as is if format doesn't match

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class EmployeeManager(models.Manager):
    def get_queryset(self):
        # Apply filters like ResignationDate and UserID based on ASP.NET logic
        # Assuming tblHR_OfficeStaff.UserID is the actual ID for the employee
        return super().get_queryset().filter(
            resignation_date__exact='', # Assuming empty string for non-resigned
            user_id__ne=1 # Assuming UserID '1' is a system account
        ).annotate(
            full_employee_name=Concat(F('title'), Value('. '), F('employee_name'))
        )

    def get_employees_for_loan(self, comp_id, fin_year_id, search_type, search_term):
        queryset = self.get_queryset().filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id
        ).order_by('-user_id') # Order by UserID Desc as per ASP.NET

        if search_type == '0' and search_term: # Employee Name
            emp_id = extract_empid_from_name(search_term)
            if emp_id:
                queryset = queryset.filter(empid=emp_id)
            else: # Fallback if ID not extracted (e.g., partial name search)
                queryset = queryset.filter(employee_name__icontains=search_term) # Using icontains for flexibility
        elif search_type == '2' and search_term: # BG Group
            try:
                business_group = BusinessGroup.objects.get(symbol=search_term)
                queryset = queryset.filter(bg_group=business_group.id) # Filter by BusinessGroup.Id
            except BusinessGroup.DoesNotExist:
                queryset = queryset.none() # No matching group, return empty

        return queryset


class Employee(models.Model):
    user_id = models.IntegerField(db_column='UserID', primary_key=True) # Using UserID as primary key as it's ordered by it
    bg_group = models.IntegerField(db_column='BGGroup', null=True, blank=True) # Assuming this is an ID, not FK directly
    designation = models.CharField(db_column='Designation', max_length=100, blank=True, null=True)
    empid = models.CharField(db_column='EmpId', max_length=50, unique=True) # EmpId might be a business ID
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=200)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    joining_date = models.CharField(db_column='JoiningDate', max_length=50, blank=True, null=True) # Date as string in source
    department = models.CharField(db_column='Department', max_length=100, blank=True, null=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=20, blank=True, null=True)
    resignation_date = models.CharField(db_column='ResignationDate', max_length=50, blank=True, null=True) # Date as string in source
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    objects = EmployeeManager()

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        # This will be the full employee name including title
        return f"{self.title}. {self.employee_name}" if self.title else self.employee_name

    @property
    def bg_group_symbol(self):
        """Returns the symbol of the associated business group."""
        try:
            return BusinessGroup.objects.get(id=self.bg_group).symbol
        except BusinessGroup.DoesNotExist:
            return "N/A"

class BankLoanManager(models.Manager):
    def create_bank_loan_batch(self, loan_data_list, comp_id, fin_year_id, session_id):
        """
        Processes a list of dictionaries to create multiple BankLoan entries.
        Each dictionary represents a single bank loan record.
        """
        created_count = 0
        for data in loan_data_list:
            try:
                # Assuming 'empid' in data corresponds to Employee.empid
                employee = Employee.objects.get(empid=data['empid']) # Link to Employee using EmpId
                # Convert string dates to datetime objects if needed for database storage,
                # but based on ASP.NET, they might be stored as strings.
                # Assuming fun.FromDate converts dd-MM-yyyy to a consistent format.
                # For simplicity, store as strings if DB type is CHAR/VARCHAR
                from_date_str = data['from_date']
                to_date_str = data['to_date']

                BankLoan.objects.create(
                    sys_date=get_current_date_str(), # Using helper
                    sys_time=get_current_time_str(), # Using helper
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    session_id=session_id,
                    empid=employee.empid, # Store employee's EmpId
                    bank_name=data['bank_name'],
                    branch=data['branch'],
                    amount=data['amount'],
                    installment=data['installment'],
                    from_date=from_date_str, # Store as string
                    to_date=to_date_str,     # Store as string
                )
                created_count += 1
            except Employee.DoesNotExist:
                # Log this error or handle it as per business requirements
                print(f"Error: Employee with EmpId {data['empid']} not found.")
            except Exception as e:
                print(f"Error creating bank loan for EmpId {data['empid']}: {e}")
        return created_count

class BankLoan(models.Model):
    # Primary key should be auto-incrementing in Django by default,
    # if original table has identity column, Django will handle it.
    # Assuming no explicit PK defined in source, Django will add 'id' automatically.
    sys_date = models.CharField(db_column='SysDate', max_length=20, null=True, blank=True) # Stored as string in source
    sys_time = models.CharField(db_column='SysTime', max_length=20, null=True, blank=True) # Stored as string in source
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=100, null=True, blank=True)
    empid = models.CharField(db_column='EmpId', max_length=50) # Storing EmpId directly, not FK to Employee
    bank_name = models.CharField(db_column='BankName', max_length=200)
    branch = models.CharField(db_column='Branch', max_length=200)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3) # Assuming decimal for currency
    installment = models.DecimalField(db_column='Installment', max_digits=18, decimal_places=3)
    from_date = models.CharField(db_column='fromDate', max_length=20) # Stored as string in source
    to_date = models.CharField(db_column='ToDate', max_length=20)   # Stored as string in source

    objects = BankLoanManager()

    class Meta:
        managed = False
        db_table = 'tblHR_BankLoan'
        verbose_name = 'Bank Loan'
        verbose_name_plural = 'Bank Loans'

    def __str__(self):
        return f"Loan for EmpId: {self.empid} - {self.amount}"

```

### 4.2 Forms (`hr_bankloan/forms.py`)

A single form `BankLoanForm` will represent the editable fields for one loan entry. A `formset` will be created from this. We'll also need a search form.

```python
from django import forms
from django.forms import formset_factory, BaseFormSet
from .models import BankLoan, Employee, BusinessGroup # Import necessary models

class EmployeeSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Employee Name'),
        ('2', 'BG Group'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Search By"
    )
    # Combined search input, visibility handled by Alpine.js
    search_term = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Enter search term...',
            'hx-get': "{% url 'hr_bankloan:employee_autocomplete' %}", # HTMX autocomplete
            'hx-trigger': "keyup changed delay:500ms",
            'hx-target': "#autocomplete-results",
            'hx-swap': "innerHTML",
            'autocomplete': "off"
        }),
        label="Search"
    )

    # Hidden field to store selected EmpId from autocomplete for employee name search
    # This will be populated by Alpine.js from the autocomplete selection
    selected_empid = forms.CharField(
        widget=forms.HiddenInput(),
        required=False
    )
    
    # Placeholder for BG Group input, actual use based on search_field
    bg_group_input = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Enter BG Group symbol...'
        })
    )


class BankLoanEntryForm(forms.Form):
    # Fields representing an Employee row (read-only)
    employee_user_id = forms.IntegerField(widget=forms.HiddenInput(), required=False) # Primary Key
    employee_empid = forms.CharField(widget=forms.HiddenInput(), required=False) # Employee's Business ID
    employee_name_display = forms.CharField(widget=forms.HiddenInput(), required=False) # Full Name for display
    employee_bggroup_display = forms.CharField(widget=forms.HiddenInput(), required=False) # BG Group Symbol for display

    # Checkbox for selection (managed by Alpine.js for showing/hiding fields)
    select_employee = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'x-model': 'selected', # Alpine.js model for checkbox
            '@change': 'toggleFields()' # Alpine.js function to toggle fields
        })
    )

    # Bank Loan fields (initially hidden/disabled, enabled by Alpine.js)
    bank_name = forms.CharField(
        max_length=200,
        required=False, # Required only if select_employee is checked
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'x-bind:class': "{ 'border-red-500': showErrors && !bank_name_valid }",
            'x-bind:required': 'selected',
            'x-bind:disabled': '!selected' # Disabled when not selected
        })
    )
    branch = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'x-bind:class': "{ 'border-red-500': showErrors && !branch_valid }",
            'x-bind:required': 'selected',
            'x-bind:disabled': '!selected'
        })
    )
    amount = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'box3 w-full',
            'step': '0.01',
            'x-bind:class': "{ 'border-red-500': showErrors && !amount_valid }",
            'x-bind:required': 'selected',
            'x-bind:disabled': '!selected'
        })
    )
    installment = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'box3 w-full',
            'step': '0.01',
            'x-bind:class': "{ 'border-red-500': showErrors && !installment_valid }",
            'x-bind:required': 'selected',
            'x-bind:disabled': '!selected'
        })
    )
    from_date = forms.CharField( # Using CharField as per ASP.NET input format
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full datepicker', # Add datepicker class for Alpine.js/Flatpickr
            'placeholder': 'dd-MM-yyyy',
            'x-bind:class': "{ 'border-red-500': showErrors && !from_date_valid }",
            'x-bind:required': 'selected',
            'x-bind:disabled': '!selected',
            'x-init': 'initDatepicker($el)', # Alpine.js init for datepicker
            'readonly': 'readonly' # Match ASP.NET readonly attribute
        })
    )
    to_date = forms.CharField( # Using CharField as per ASP.NET input format
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full datepicker', # Add datepicker class for Alpine.js/Flatpickr
            'placeholder': 'dd-MM-yyyy',
            'x-bind:class': "{ 'border-red-500': showErrors && !to_date_valid }",
            'x-bind:required': 'selected',
            'x-bind:disabled': '!selected',
            'x-init': 'initDatepicker($el)', # Alpine.js init for datepicker
            'readonly': 'readonly' # Match ASP.NET readonly attribute
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        select_employee = cleaned_data.get('select_employee')
        
        # Only validate if the checkbox is selected
        if select_employee:
            bank_name = cleaned_data.get('bank_name')
            branch = cleaned_data.get('branch')
            amount = cleaned_data.get('amount')
            installment = cleaned_data.get('installment')
            from_date = cleaned_data.get('from_date')
            to_date = cleaned_data.get('to_date')

            if not bank_name:
                self.add_error('bank_name', 'This field is required.')
            if not branch:
                self.add_error('branch', 'This field is required.')
            if amount is None:
                self.add_error('amount', 'This field is required.')
            if installment is None:
                self.add_error('installment', 'This field is required.')
            if not from_date:
                self.add_error('from_date', 'This field is required.')
            if not to_date:
                self.add_error('to_date', 'This field is required.')
            
            # Additional regex validation from ASP.NET
            # For date format dd-MM-yyyy
            import re
            date_regex = r"^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$" # Matches dd-MM-yyyy
            if from_date and not re.match(date_regex, from_date):
                self.add_error('from_date', 'Enter date in dd-MM-yyyy format.')
            if to_date and not re.match(date_regex, to_date):
                self.add_error('to_date', 'Enter date in dd-MM-yyyy format.')

            # Amount/Installment format (^\d{1,15}(\.\d{0,3})?$)
            # DecimalField handles this generally, but if we need to be strict with input string
            # Python's decimal conversion is robust. Numeric validation is mostly covered by DecimalField type.
            # ASP.NET `fun.NumberValidationQty` implies checking if it's a valid number.
            # Django's DecimalField handles this.

        return cleaned_data

class BaseBankLoanFormSet(BaseFormSet):
    def clean(self):
        """Custom validation for the formset."""
        if any(self.errors):
            # Don't bother validating the formset unless individual forms are valid.
            return
        
        # Check if at least one employee is selected
        selected_forms = [form for form in self.forms if form.cleaned_data.get('select_employee')]
        if not selected_forms:
            raise forms.ValidationError("Please select at least one employee to add bank loan details.")

        # Optional: check for duplicate employee IDs if multiple forms are submitted for the same employee
        # This might not be needed if the UI prevents it or business logic allows multiple loans.
        # For simplicity, we'll assume multiple loans per employee are allowed or managed by separate entries.
        
BankLoanFormSet = formset_factory(BankLoanEntryForm, formset=BaseBankLoanFormSet, extra=0) # No extra forms by default
```

### 4.3 Views (`hr_bankloan/views.py`)

The views will manage the search, display, and batch submission of bank loans.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from .models import Employee, BusinessGroup, BankLoan
from .forms import EmployeeSearchForm, BankLoanEntryForm, BankLoanFormSet

class BankLoanListView(ListView):
    template_name = 'hr_bankloan/list.html'
    context_object_name = 'employees_forms' # Will be a list of dicts: {'employee': Employee, 'form': BankLoanEntryForm}
    paginate_by = 15 # ASP.NET GridView PageSize

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = EmployeeSearchForm(self.request.GET or None)
        
        # Pass session context. In a real app, this would come from request.user or middleware.
        # For this example, use dummy data or derive from request.
        context['comp_id'] = self.request.session.get('compid', 1) # Example dummy compid
        context['fin_year_id'] = self.request.session.get('finyear', 2024) # Example dummy finyear
        context['session_id'] = self.request.session.get('username', 'system') # Example dummy session_id

        return context
    
    def get_queryset(self):
        # Initial queryset, will be overridden by formset data if POST
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2024)
        
        search_form = EmployeeSearchForm(self.request.GET or None)
        search_type = '0' # Default to Employee Name
        search_term = ''

        if search_form.is_valid():
            search_type = search_form.cleaned_data.get('search_field', '0')
            if search_type == '0': # Employee Name search
                search_term = search_form.cleaned_data.get('selected_empid', '') # Use hidden EmpId
                if not search_term: # Fallback to search_term if hidden EmpId not set
                    search_term = search_form.cleaned_data.get('search_term', '')
            elif search_type == '2': # BG Group search
                search_term = search_form.cleaned_data.get('bg_group_input', '')

        # Use the custom manager method to get filtered employees
        return Employee.objects.get_employees_for_loan(comp_id, fin_year_id, search_type, search_term)

    def post(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2024)
        session_id = request.session.get('username', 'system')

        # When the form is submitted, it contains data for the entire formset.
        # We need to manually construct the data dict for the formset from the POST data.
        # The formset expects data in the format: prefix-idx-field_name
        formset_data = {
            'form-TOTAL_FORMS': request.POST.get('form-TOTAL_FORMS'),
            'form-INITIAL_FORMS': request.POST.get('form-INITIAL_FORMS'),
            'form-MIN_NUM_FORMS': request.POST.get('form-MIN_NUM_FORMS'),
            'form-MAX_NUM_FORMS': request.POST.get('form-MAX_NUM_FORMS'),
        }
        
        # Collect all individual form data
        total_forms = int(request.POST.get('form-TOTAL_FORMS', 0))
        loan_entries_to_save = []
        for i in range(total_forms):
            prefix = f'form-{i}-'
            is_selected = request.POST.get(prefix + 'select_employee') == 'on'
            
            # Only process forms that were "selected" by the checkbox
            if is_selected:
                try:
                    # Manually construct data for one form instance for validation
                    # Note: formset_factory can take data from request.POST directly, but here we
                    # explicitly want to filter by selected checkboxes.
                    
                    form_instance_data = {
                        'select_employee': True, # Mark as selected for validation
                        'employee_empid': request.POST.get(prefix + 'employee_empid'),
                        'bank_name': request.POST.get(prefix + 'bank_name'),
                        'branch': request.POST.get(prefix + 'branch'),
                        'amount': request.POST.get(prefix + 'amount'),
                        'installment': request.POST.get(prefix + 'installment'),
                        'from_date': request.POST.get(prefix + 'from_date'),
                        'to_date': request.POST.get(prefix + 'to_date'),
                    }
                    
                    form = BankLoanEntryForm(form_instance_data)
                    if form.is_valid():
                        loan_entries_to_save.append(form.cleaned_data)
                    else:
                        # If any selected form is invalid, return error.
                        # This simplifies error handling for batch submission.
                        # In a more complex scenario, you might want to highlight individual errors.
                        # For now, just re-render with errors and show a general message.
                        messages.error(self.request, f"Some selected loan entries have invalid data: {form.errors.as_text()}")
                        # Re-instantiate the view with current GET and errors
                        return self.render_to_response(self.get_context_data(object_list=self.get_queryset()))

                except Exception as e:
                    messages.error(self.request, f"An unexpected error occurred processing a loan entry: {e}")
                    return self.render_to_response(self.get_context_data(object_list=self.get_queryset()))

        if not loan_entries_to_save:
            messages.warning(self.request, "No bank loan entries were selected or submitted.")
            return self.render_to_response(self.get_context_data(object_list=self.get_queryset()))

        created_count = BankLoan.objects.create_bank_loan_batch(
            loan_entries_to_save, comp_id, fin_year_id, session_id
        )

        messages.success(self.request, f"{created_count} bank loan(s) added successfully.")
        
        # If HTMX request, trigger a refresh of the table and clear form
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshBankLoanList',
                    'HX-Redirect': reverse_lazy('hr_bankloan:bankloan_list') # Redirect to clear form
                }
            )
        return super().post(request, *args, **kwargs)


class BankLoanTablePartialView(ListView):
    """
    Renders just the table body and DataTables script for HTMX requests.
    """
    template_name = 'hr_bankloan/_bankloan_table.html'
    context_object_name = 'employees_forms'
    paginate_by = 15

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Re-pass search_form context if needed for persistent filter display
        context['search_form'] = EmployeeSearchForm(self.request.GET or None)
        return context

    def get_queryset(self):
        # Re-use the filtering logic from BankLoanListView
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2024)
        
        search_form = EmployeeSearchForm(self.request.GET or None)
        search_type = '0'
        search_term = ''

        if search_form.is_valid():
            search_type = search_form.cleaned_data.get('search_field', '0')
            if search_type == '0':
                search_term = search_form.cleaned_data.get('selected_empid', '')
                if not search_term:
                    search_term = search_form.cleaned_data.get('search_term', '')
            elif search_type == '2':
                search_term = search_form.cleaned_data.get('bg_group_input', '')

        return Employee.objects.get_employees_for_loan(comp_id, fin_year_id, search_type, search_term)

    def render_to_response(self, context, **response_kwargs):
        # Create a formset for the employees in the queryset
        # This will be used to render the inputs for each employee row
        employees = context['object_list']
        # Prepare initial data for the formset forms. Each form will represent an employee row.
        # We set initial values for the hidden employee details.
        initial_forms_data = []
        for employee in employees:
            initial_forms_data.append({
                'employee_user_id': employee.user_id,
                'employee_empid': employee.empid,
                'employee_name_display': str(employee), # Use __str__ method for full name
                'employee_bggroup_display': employee.bg_group_symbol,
            })
        
        # Instantiate the formset with initial data, but without POST data initially
        # When rendering GET, forms are empty. When POST, forms would be populated from request.
        formset = BankLoanFormSet(initial=initial_forms_data, prefix='form')
        context['formset'] = formset
        
        # Ensure pagination works with DataTables for HTMX
        return super().render_to_response(context, **response_kwargs)


class EmployeeAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        comp_id = request.session.get('compid', 1) # Get compid from session
        
        if query:
            # Filter employees by EmployeeName (case-insensitive contains)
            employees = Employee.objects.filter(
                comp_id=comp_id,
                employee_name__icontains=query
            )[:10] # Limit to 10 results, similar to ASP.NET CompletionSetCount

            suggestions = []
            for emp in employees:
                # Format: "EmployeeName [EmpId]"
                suggestions.append(f"{emp.employee_name} [{emp.empid}]")
            
            # Return as simple HTML list for HTMX to swap into a div
            html_response = '<div class="autocomplete-list bg-white border border-gray-200 rounded-md shadow-lg absolute z-10 w-full mt-1">'
            if suggestions:
                for s in suggestions:
                    # Use x-on:click to set value and hidden input via Alpine.js
                    # and x-text to display the suggestion
                    html_response += f'<div class="p-2 cursor-pointer hover:bg-gray-100" x-on:click="$dispatch(\'select-employee\', {{ value: \'{s}\' }})" x-text="\'{s}\'"></div>'
            else:
                html_response += '<div class="p-2 text-gray-500">No results found.</div>'
            html_response += '</div>'
            return HttpResponse(html_response)
        return HttpResponse('') # Return empty if no query
```

### 4.4 Templates (`hr_bankloan/templates/hr_bankloan/`)

We'll need `list.html` and `_bankloan_table.html` (partial for HTMX).

**`hr_bankloan/templates/hr_bankloan/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    searchType: '{{ search_form.search_field.value|default:'0' }}',
    searchTerm: '{{ search_form.search_term.value|default:'' }}',
    bgGroupInput: '{{ search_form.bg_group_input.value|default:'' }}',
    selectedEmpId: '{{ search_form.selected_empid.value|default:'' }}',
    showAutocomplete: false,
    autocompleteResults: '',
    
    handleSearchChange() {
        this.selectedEmpId = ''; // Clear selected EmpId when search type changes
        // Potentially trigger a search directly or update UI based on type
    },
    
    // For autocomplete selection
    handleSelectEmployee(event) {
        this.searchTerm = event.detail.value;
        this.selectedEmpId = this.searchTerm.split('[').pop().replace(']', ''); // Extract EmpId
        this.showAutocomplete = false; // Hide results after selection
    },
    
    // For general focus/blur on search input to hide autocomplete
    handleSearchFocus() {
        if (this.searchType === '0' && this.searchTerm.length > 0) {
            this.showAutocomplete = true;
        }
    },
    handleSearchBlur() {
        // Delay hiding to allow click event on results
        setTimeout(() => { this.showAutocomplete = false; }, 200);
    },
}">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Bank Loan - New</h2>

    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'hr_bankloan:bankloan_table_partial' %}"
              hx-target="#bankLoanTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_search_field">
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_field }}
                </div>
                <div class="relative">
                    <label for="id_search_term" class="block text-sm font-medium text-gray-700">Search Term</label>
                    <div x-show="searchType === '0'" x-cloak>
                        <input type="text" id="id_search_term" name="search_term" 
                                class="box3 w-full" placeholder="Enter Employee Name..."
                                hx-get="{% url 'hr_bankloan:employee_autocomplete' %}"
                                hx-trigger="keyup changed delay:500ms from:#id_search_term"
                                hx-target="#autocomplete-results"
                                hx-swap="innerHTML"
                                autocomplete="off"
                                x-model="searchTerm"
                                x-on:focus="handleSearchFocus()"
                                x-on:blur="handleSearchBlur()"
                                x-on:select-employee.window="handleSelectEmployee($event)">
                        <input type="hidden" name="selected_empid" x-model="selectedEmpId">
                        <div id="autocomplete-results" class="absolute w-full z-10" x-show="showAutocomplete" x-cloak></div>
                    </div>
                    <div x-show="searchType === '2'" x-cloak>
                        <input type="text" id="id_bg_group_input" name="bg_group_input" 
                                class="box3 w-full" placeholder="Enter BG Group Symbol..."
                                x-model="bgGroupInput">
                    </div>
                </div>
                <div>
                    <button type="submit" class="redbox w-full py-2">Search</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Bank Loan Table Container -->
    <form hx-post="{% url 'hr_bankloan:bankloan_list' %}"
          hx-target="body"
          hx-swap="none"
          x-data="{ showErrors: false }">
        {% csrf_token %}
        <div id="bankLoanTable-container"
             hx-trigger="load, refreshBankLoanList from:body"
             hx-get="{% url 'hr_bankloan:bankloan_table_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
             hx-target="#bankLoanTable-container"
             hx-swap="innerHTML">
            <!-- Table content loaded here by HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Employees...</p>
            </div>
        </div>

        <div class="mt-6 text-center">
            <button type="submit" class="redbox py-2 px-6" x-on:click="showErrors = true; $nextTick(() => { if (!document.querySelector('.has-error')) { return true; } else { event.preventDefault(); alert('Please correct the highlighted errors.'); } })">Submit</button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('loanRow', (initialSelected = false) => ({
            selected: initialSelected,
            toggleFields() {
                // Logic already handled by x-bind:disabled and x-bind:required
                // just toggle the selected state
            },
            initDatepicker(element) {
                flatpickr(element, {
                    dateFormat: "d-m-Y", // dd-MM-yyyy format
                    allowInput: false,   // Disable manual input
                    onClose: function(selectedDates, dateStr, instance) {
                        // Manually dispatch change event for HTMX/Alpine to pick up
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });
            }
        }));

        // General event listener for messages (Django messages framework)
        document.body.addEventListener('htmx:afterSwap', function(event) {
            const messagesContainer = document.getElementById('messages');
            if (messagesContainer && event.detail.xhr.getResponseHeader('HX-Trigger')) {
                // If the HTMX response triggered a message, ensure it's displayed
                // This assumes your base.html has a messages section that reloads or shows
            }
        });
    });
</script>
{% endblock %}
```

**`hr_bankloan/templates/hr_bankloan/_bankloan_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="bankLoanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EmpId</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Installment</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if formset.forms %}
                {{ formset.management_form }} {# Essential for formsets #}
                {% for form in formset %}
                <tr x-data="loanRow()" {% if form.errors %}class="has-error"{% endif %}>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ form.select_employee }}
                        {# Hidden employee fields #}
                        {{ form.employee_user_id }}
                        {{ form.employee_empid }}
                        {{ form.employee_name_display }}
                        {{ form.employee_bggroup_display }}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ form.initial.employee_empid }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-900">{{ form.initial.employee_name_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ form.initial.employee_bggroup_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.bank_name }}
                        {% if form.bank_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_name.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.branch }}
                        {% if form.branch.errors %}<p class="text-red-500 text-xs mt-1">{{ form.branch.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.amount }}
                        {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.installment }}
                        {% if form.installment.errors %}<p class="text-red-500 text-xs mt-1">{{ form.installment.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.from_date }}
                        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.to_date }}
                        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="11" class="py-8 text-center text-gray-500">No employees found matching your criteria.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization (ensure jQuery is loaded in base.html)
    $(document).ready(function() {
        $('#bankLoanTable').DataTable({
            "paging": true,
            "searching": false, // Handled by Django/HTMX search form
            "ordering": false, // Ordering logic handled by backend query (UserID Desc)
            "info": true,
            "pageLength": {{ view.paginate_by }},
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 5, 6, 7, 8, 9, 10] } // Disable sorting for SN, Checkbox, and input fields
            ]
        });
    });
</script>
```

### 4.5 URLs (`hr_bankloan/urls.py`)

Define the URL patterns for the views.

```python
from django.urls import path
from .views import BankLoanListView, BankLoanTablePartialView, EmployeeAutocompleteView

app_name = 'hr_bankloan'

urlpatterns = [
    path('bankloan/', BankLoanListView.as_view(), name='bankloan_list'),
    path('bankloan/table/', BankLoanTablePartialView.as_view(), name='bankloan_table_partial'),
    path('bankloan/autocomplete/employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]
```

### 4.6 Tests (`hr_bankloan/tests.py`)

Comprehensive unit tests for models and integration tests for views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.forms import formset_factory
from .models import Employee, BusinessGroup, BankLoan
from .forms import BankLoanEntryForm, EmployeeSearchForm, BankLoanFormSet # Import BaseBankLoanFormSet
from unittest.mock import patch, MagicMock

# Mock session for tests as ASP.NET code relies on it
class MockSession:
    def get(self, key, default=None):
        if key == 'compid':
            return 1
        elif key == 'finyear':
            return 2024
        elif key == 'username':
            return 'testuser'
        return default

class BankLoanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.bg1 = BusinessGroup.objects.create(id=101, symbol='IT')
        cls.bg2 = BusinessGroup.objects.create(id=102, symbol='HR')

        cls.emp1 = Employee.objects.create(
            user_id=10, empid='EMP001', employee_name='Alice Smith', title='Ms', bg_group=cls.bg1.id,
            comp_id=1, fin_year_id=2024, resignation_date='', user_id=10
        )
        cls.emp2 = Employee.objects.create(
            user_id=11, empid='EMP002', employee_name='Bob Johnson', title='Mr', bg_group=cls.bg2.id,
            comp_id=1, fin_year_id=2024, resignation_date='', user_id=11
        )
        cls.emp3 = Employee.objects.create(
            user_id=12, empid='EMP003', employee_name='Charlie Brown', title='Mr', bg_group=cls.bg1.id,
            comp_id=1, fin_year_id=2023, resignation_date='', user_id=12
        )
        # Resigned employee
        cls.emp_resigned = Employee.objects.create(
            user_id=13, empid='EMP004', employee_name='David Lee', title='Mr', bg_group=cls.bg1.id,
            comp_id=1, fin_year_id=2024, resignation_date='01-01-2024', user_id=13
        )
        # System user
        cls.emp_system = Employee.objects.create(
            user_id=1, empid='SYS001', employee_name='System User', title='Mr', bg_group=cls.bg1.id,
            comp_id=1, fin_year_id=2024, resignation_date='', user_id=1
        )

    def test_employee_creation(self):
        self.assertEqual(Employee.objects.count(), 5)
        self.assertEqual(self.emp1.employee_name, 'Alice Smith')
        self.assertEqual(self.emp1.bg_group_symbol, 'IT')

    def test_bankloan_creation(self):
        # Using the manager method
        loan_data = [{
            'empid': self.emp1.empid,
            'bank_name': 'Test Bank',
            'branch': 'Test Branch',
            'amount': 1000.00,
            'installment': 100.00,
            'from_date': '01-01-2025',
            'to_date': '01-10-2025'
        }]
        created_count = BankLoan.objects.create_bank_loan_batch(
            loan_data, comp_id=1, fin_year_id=2024, session_id='testuser'
        )
        self.assertEqual(created_count, 1)
        loan = BankLoan.objects.first()
        self.assertEqual(loan.empid, self.emp1.empid)
        self.assertEqual(loan.bank_name, 'Test Bank')

    def test_employee_manager_get_employees_for_loan(self):
        # Test basic filtering for valid employees
        employees = Employee.objects.get_employees_for_loan(
            comp_id=1, fin_year_id=2024, search_type='0', search_term=''
        )
        self.assertEqual(employees.count(), 3) # emp1, emp2, emp3 (fin_year_id <= 2024)

        # Test Employee Name search
        employees_by_name = Employee.objects.get_employees_for_loan(
            comp_id=1, fin_year_id=2024, search_type='0', search_term='Alice Smith [EMP001]'
        )
        self.assertEqual(employees_by_name.count(), 1)
        self.assertEqual(employees_by_name.first().empid, 'EMP001')

        # Test BG Group search
        employees_by_bg = Employee.objects.get_employees_for_loan(
            comp_id=1, fin_year_id=2024, search_type='2', search_term='IT'
        )
        self.assertEqual(employees_by_bg.count(), 2) # emp1, emp3

        # Test non-existent BG Group search
        employees_by_non_existent_bg = Employee.objects.get_employees_for_loan(
            comp_id=1, fin_year_id=2024, search_type='2', search_term='NonExistent'
        )
        self.assertEqual(employees_by_non_existent_bg.count(), 0)

    def test_employee_manager_filters_resigned_and_system_user(self):
        all_active_employees = Employee.objects.get_queryset()
        self.assertNotIn(self.emp_resigned, all_active_employees)
        self.assertNotIn(self.emp_system, all_active_employees)
        self.assertIn(self.emp1, all_active_employees)

class BankLoanFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.bg1 = BusinessGroup.objects.create(id=101, symbol='IT')
        cls.emp1 = Employee.objects.create(
            user_id=10, empid='EMP001', employee_name='Alice Smith', title='Ms', bg_group=cls.bg1.id,
            comp_id=1, fin_year_id=2024, resignation_date='', user_id=10
        )

    def test_employee_search_form_valid(self):
        form = EmployeeSearchForm({'search_field': '0', 'search_term': 'Alice Smith', 'selected_empid': 'EMP001'})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['search_field'], '0')
        self.assertEqual(form.cleaned_data['selected_empid'], 'EMP001')

    def test_bank_loan_entry_form_valid_selected(self):
        data = {
            'select_employee': True,
            'employee_empid': 'EMP001',
            'bank_name': 'ABC Bank',
            'branch': 'Main Branch',
            'amount': '1500.50',
            'installment': '150.00',
            'from_date': '15-03-2024',
            'to_date': '15-03-2025',
        }
        form = BankLoanEntryForm(data)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['bank_name'], 'ABC Bank')

    def test_bank_loan_entry_form_invalid_missing_fields_when_selected(self):
        data = {
            'select_employee': True,
            'employee_empid': 'EMP001',
            'bank_name': '', # Missing
            'branch': 'Main Branch',
            'amount': '1500.50',
            'installment': '150.00',
            'from_date': '15-03-2024',
            'to_date': '15-03-2025',
        }
        form = BankLoanEntryForm(data)
        self.assertFalse(form.is_valid())
        self.assertIn('bank_name', form.errors)

    def test_bank_loan_entry_form_invalid_date_format_when_selected(self):
        data = {
            'select_employee': True,
            'employee_empid': 'EMP001',
            'bank_name': 'ABC Bank',
            'branch': 'Main Branch',
            'amount': '1500.50',
            'installment': '150.00',
            'from_date': '2024-03-15', # Wrong format
            'to_date': '15-03-2025',
        }
        form = BankLoanEntryForm(data)
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)

    def test_bank_loan_entry_form_not_validated_when_not_selected(self):
        data = {
            'select_employee': False, # Not selected
            'employee_empid': 'EMP001',
            'bank_name': '',
            'branch': '',
            'amount': '',
            'installment': '',
            'from_date': '',
            'to_date': '',
        }
        form = BankLoanEntryForm(data)
        self.assertTrue(form.is_valid()) # Should be valid as select_employee is False
        self.assertIsNone(form.cleaned_data['bank_name']) # Should be None or empty string

    def test_bank_loan_formset_no_selection(self):
        BankLoanFormSet = formset_factory(BankLoanEntryForm, formset=BaseBankLoanFormSet, extra=0)
        formset = BankLoanFormSet({
            'form-TOTAL_FORMS': 1,
            'form-INITIAL_FORMS': 1,
            'form-MIN_NUM_FORMS': 0,
            'form-MAX_NUM_FORMS': 1000,
            'form-0-select_employee': False,
            'form-0-employee_empid': 'EMP001',
            # Other fields left empty
        })
        self.assertFalse(formset.is_valid())
        self.assertIn('Please select at least one employee', formset.non_form_errors())

    def test_bank_loan_formset_with_selection(self):
        BankLoanFormSet = formset_factory(BankLoanEntryForm, formset=BaseBankLoanFormSet, extra=0)
        formset = BankLoanFormSet({
            'form-TOTAL_FORMS': 1,
            'form-INITIAL_FORMS': 1,
            'form-MIN_NUM_FORMS': 0,
            'form-MAX_NUM_FORMS': 1000,
            'form-0-select_employee': True,
            'form-0-employee_empid': 'EMP001',
            'form-0-bank_name': 'BankX',
            'form-0-branch': 'BranchY',
            'form-0-amount': '100',
            'form-0-installment': '10',
            'form-0-from_date': '01-01-2024',
            'form-0-to_date': '01-01-2025',
        })
        self.assertTrue(formset.is_valid(), formset.errors)


class BankLoanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.bg1 = BusinessGroup.objects.create(id=101, symbol='IT')
        cls.emp1 = Employee.objects.create(
            user_id=10, empid='EMP001', employee_name='Alice Smith', title='Ms', bg_group=cls.bg1.id,
            comp_id=1, fin_year_id=2024, resignation_date='', user_id=10
        )
        cls.emp2 = Employee.objects.create(
            user_id=11, empid='EMP002', employee_name='Bob Johnson', title='Mr', bg_group=cls.bg1.id,
            comp_id=1, fin_year_id=2024, resignation_date='', user_id=11
        )
        
    def setUp(self):
        self.client = Client()
        # Mock session for the client
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2024
        self.client.session['username'] = 'testuser'
    
    def test_list_view_get(self):
        response = self.client.get(reverse('hr_bankloan:bankloan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_bankloan/list.html')
        self.assertIn('employees_forms', response.context) # Should contain the formset/employees list

    def test_bankloan_table_partial_view_get(self):
        response = self.client.get(reverse('hr_bankloan:bankloan_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_bankloan/_bankloan_table.html')
        self.assertIn('formset', response.context)
        self.assertContains(response, 'EMP001') # Check if employee data is present

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('hr_bankloan:employee_autocomplete'), {'q': 'Ali'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alice Smith [EMP001]')
        self.assertNotContains(response, 'Bob Johnson')

    @patch('hr_bankloan.models.BankLoan.objects.create_bank_loan_batch')
    def test_list_view_post_valid_data(self, mock_create_batch):
        # Mock the create_bank_loan_batch to avoid actual DB interaction for this test
        mock_create_batch.return_value = 1 # Simulate one loan created successfully

        post_data = {
            'form-TOTAL_FORMS': 2,
            'form-INITIAL_FORMS': 2,
            'form-MIN_NUM_FORMS': 0,
            'form-MAX_NUM_FORMS': 1000,
            
            'form-0-select_employee': 'on', # Selected
            'form-0-employee_user_id': self.emp1.user_id,
            'form-0-employee_empid': self.emp1.empid,
            'form-0-bank_name': 'Bank A',
            'form-0-branch': 'Branch A',
            'form-0-amount': '1000.00',
            'form-0-installment': '100.00',
            'form-0-from_date': '01-01-2024',
            'form-0-to_date': '01-01-2025',

            'form-1-select_employee': '', # Not selected
            'form-1-employee_user_id': self.emp2.user_id,
            'form-1-employee_empid': self.emp2.empid,
            'form-1-bank_name': '', # Should be ignored
            'form-1-branch': '',
            'form-1-amount': '',
            'form-1-installment': '',
            'form-1-from_date': '',
            'form-1-to_date': '',
        }
        
        response = self.client.post(reverse('hr_bankloan:bankloan_list'), post_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content expected for HX-Request
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankLoanList')
        self.assertIn('HX-Redirect', response.headers)
        
        # Verify that create_bank_loan_batch was called with correct data
        mock_create_batch.assert_called_once()
        args, kwargs = mock_create_batch.call_args
        loan_data_list = args[0]
        self.assertEqual(len(loan_data_list), 1) # Only one selected loan should be processed
        self.assertEqual(loan_data_list[0]['empid'], self.emp1.empid)
        self.assertEqual(loan_data_list[0]['bank_name'], 'Bank A')
        self.assertEqual(BankLoan.objects.count(), 0) # No actual DB write due to mock

    @patch('hr_bankloan.models.BankLoan.objects.create_bank_loan_batch')
    def test_list_view_post_invalid_data(self, mock_create_batch):
        post_data = {
            'form-TOTAL_FORMS': 1,
            'form-INITIAL_FORMS': 1,
            'form-MIN_NUM_FORMS': 0,
            'form-MAX_NUM_FORMS': 1000,
            
            'form-0-select_employee': 'on',
            'form-0-employee_user_id': self.emp1.user_id,
            'form-0-employee_empid': self.emp1.empid,
            'form-0-bank_name': '', # Invalid: required when selected
            'form-0-branch': 'Branch A',
            'form-0-amount': 'abc', # Invalid: not numeric
            'form-0-installment': '100.00',
            'form-0-from_date': '01/01/2024', # Invalid format
            'form-0-to_date': '01-01-2025',
        }

        response = self.client.post(reverse('hr_bankloan:bankloan_list'), post_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Should render with errors
        self.assertContains(response, "Some selected loan entries have invalid data")
        mock_create_batch.assert_not_called() # Should not attempt to save
```

## Step 5: HTMX and Alpine.js Integration

*   **HTMX for Search & Table Refresh**:
    *   The search form uses `hx-get` to `{% url 'hr_bankloan:bankloan_table_partial' %}` to refresh just the table, not the whole page.
    *   The main table container (`#bankLoanTable-container`) listens for `refreshBankLoanList` custom event (triggered after successful `hx-post` submission) and re-fetches its content.
    *   The `BtnSubmit` button uses `hx-post` to the `bankloan_list` URL. The view responds with `status=204` and `HX-Trigger` to refresh the list, achieving a seamless update.
*   **HTMX for Autocomplete**:
    *   The `search_term` input has `hx-get` pointing to `{% url 'hr_bankloan:employee_autocomplete' %}`.
    *   `hx-trigger="keyup changed delay:500ms"` ensures efficient requests.
    *   `hx-target="#autocomplete-results"` and `hx-swap="innerHTML"` display results dynamically.
*   **Alpine.js for UI State**:
    *   **Search Form Toggle**: `x-data` and `x-show` attributes on the search input containers (`id_search_term` and `id_bg_group_input`) control which input is visible based on `searchType` (bound to `search_field` dropdown).
    *   **Autocomplete Selection**: `x-on:select-employee.window` on the main `x-data` block handles a custom event dispatched from the autocomplete results, populating the `searchTerm` and `selectedEmpId`.
    *   **Inline Form Fields**: Each table row (`<tr>`) uses `x-data="loanRow()"` to manage its own `selected` state (bound to the checkbox).
    *   `x-bind:required` and `x-bind:disabled` on the input fields dynamically set these attributes based on `selected` status, replicating the ASP.NET validator visibility.
    *   `x-init="initDatepicker($el)"` on date inputs initializes Flatpickr, ensuring a user-friendly date selection and `readonly` behavior.
    *   **Form Validation Feedback**: The `showErrors` Alpine.js variable combined with `x-bind:class="{ 'border-red-500': showErrors && !bank_name_valid }"` (and similar for other fields) provides client-side visual feedback on validation errors when the submit button is clicked and there are errors. `showErrors` is toggled on submit.

## Final Notes

*   **Session Management**: The ASP.NET application heavily relies on `Session["compid"]`, `Session["finyear"]`, and `Session["username"]`. In Django, these would typically be derived from the authenticated `request.user` object (e.g., `request.user.company.id`, `request.user.financial_year`, `request.user.username`) if user profiles are extended, or from a custom middleware injecting `comp_id`/`fin_year_id` into the request for non-user-specific global context. For this migration, dummy values or `request.session.get()` are used for demonstration.
*   **Error Handling**: The ASP.NET `try-catch` blocks are replaced by Django's robust error handling. Form validation and `messages` framework handle user feedback.
*   **Date Formats**: The ASP.NET uses `dd-MM-yyyy`. Django forms will handle parsing and validation, and Flatpickr on the frontend ensures consistent input. `CharField` was used for date fields in models to match source behavior; typically `DateField` would be preferred with proper format conversion.
*   **`clsFunctions` Replacement**: The utility functions are either replaced by Django ORM, built-in Python `datetime`, or integrated into model managers/form validation logic. `fun.getCode` for employee ID extraction is handled by `extract_empid_from_name` in the model's helper.
*   **Scalability**: The batch processing approach with a formset is scalable. DataTables provides client-side pagination, which handles large datasets efficiently on the browser side after initial data load. For extremely large datasets, server-side pagination for DataTables could be implemented using a separate HTMX endpoint.