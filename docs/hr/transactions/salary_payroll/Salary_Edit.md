## ASP.NET to Django Conversion Script:

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET Salary Edit module to a modern Django-based solution. The focus is on leveraging automated approaches, adhering to modern Django 5.0+ best practices, and ensuring a user experience driven by HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`hr_payroll`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the `binddata` method and `GridView2` column bindings, we identify the main table and several lookup tables.

- **Main Table:** `tblHR_OfficeStaff`
    - **Columns:** `EmpId` (used as a unique identifier), `UserID`, `Title`, `EmployeeName`, `FinYearId`, `Department`, `BGGroup`, `Designation`, `JoiningDate`, `ResignationDate`, `CompId`.
- **Lookup Tables (Inferred):**
    - `tblFinancial_master`: Used for `FinYearId` lookup (returns `FinYear`). Columns: `FinYearId`, `FinYear`.
    - `tblHR_Departments`: Used for `Department` lookup (returns `Description`). Columns: `Id`, `Description`.
    - `BusinessGroup`: Used for `BGGroup` lookup (returns `Symbol`). Columns: `Id`, `Symbol`.
    - `tblHR_Designation`: Used for `Designation` lookup (returns `Symbol + '-' + Type`). Columns: `Id`, `Symbol`, `Type`.

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**

- **Read/List (R):** The primary functionality is to display a list of employees from `tblHR_OfficeStaff` in a `GridView`. Data is filtered based on search criteria.
- **Search:** The page allows searching employees by:
    - Employee Name (`TxtEmpName`) with an auto-complete feature.
    - Department Name (`TxtMrs`).
    - Business Group (`TxtMrs`).
    - The search filters are dynamically applied using parameters passed to the `binddata` method.
- **Pagination:** `GridView2_PageIndexChanging` indicates client-side or server-side pagination for the employee list. We will implement this using DataTables for client-side.
- **Auto-complete:** The `GetCompletionList` WebMethod provides suggestions for employee names, returning "Employee Name [EmpId]".
- **No Create, Update, or Delete (CUD) operations are performed on this page.** The "Select" link (`HyperLinkField`) navigates to `Salary_Edit_Details.aspx`, implying that editing is handled on a separate page.
- **Session/Context:** `CompId` and `FyId` are retrieved from `Session`, indicating a system-wide company and financial year context.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

- `DrpField` (DropDownList): Allows selecting the search category ('Employee Name', 'Dept Name', 'BG Group'). This will be a Django `forms.ChoiceField` with HTMX to dynamically change the input field.
- `TxtMrs` (TextBox): Used for entering search terms for Department Name or Business Group. This will be a `forms.TextInput`.
- `TxtEmpName` (TextBox) with `AutoCompleteExtender`: Used for entering employee names with auto-completion. This will be a `forms.TextInput` with a jQuery UI Autocomplete script integrated via HTMX.
- `Button1` (Button): Triggers the search functionality. This will be a submit button with HTMX attributes.
- `Label2` (Label): Displays messages (e.g., from query string). This will be rendered as a Django message or simple context variable.
- `GridView2` (GridView): Displays the paginated, searchable, and sortable list of employee data. This will be converted to an HTML `<table>` managed by DataTables.js, with HTMX for dynamic content loading.
- `HyperLinkField`: Provides a link to the `Salary_Edit_Details.aspx` page for each employee, passing `EmpId`.

---

### Step 4: Generate Django Code

We will create a Django application named `hr_payroll`.

#### 4.1 Models (`hr_payroll/models.py`)

We'll define Django models that map to the existing database tables using `managed = False` and `db_table`. Foreign key relationships will be set up to simplify data retrieval.

```python
# hr_payroll/models.py
from django.db import models

# Lookup Tables
class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)
    type = models.CharField(db_column='Type', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        # Combines symbol and type as seen in ASP.NET code-behind
        return f"{self.symbol} - {self.type}"

# Main Office Staff Model
class OfficeStaff(models.Model):
    # EmpId is the primary key and used for navigation
    emp_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    user_id = models.CharField(db_column='UserID', max_length=50)
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)
    employee_name_raw = models.CharField(db_column='EmployeeName', max_length=255)
    
    # Foreign Keys to lookup tables
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='staff_members', blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.DO_NOTHING, db_column='Department', related_name='staff_members', blank=True, null=True)
    bg_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', related_name='staff_members', blank=True, null=True)
    designation_rel = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation', related_name='staff_members', blank=True, null=True)
    
    # Assuming DateField is appropriate; adjust to CharField if actual DB stores non-parseable date strings
    joining_date = models.DateField(db_column='JoiningDate', blank=True, null=True)
    # ASP.NET checks `ResignationDate=''`. For Django DateField, `NULL` is equivalent to "not set".
    resignation_date = models.DateField(db_column='ResignationDate', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Tells Django not to create/manage this table
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.full_employee_name

    @property
    def full_employee_name(self):
        """Combines Title and EmployeeName_Raw as per ASP.NET logic."""
        return f"{self.title or ''}. {self.employee_name_raw}".strip()

    @classmethod
    def get_filtered_employees(cls, company_id, fin_year_id, search_criteria=None):
        """
        Retrieves a queryset of OfficeStaff members based on specified filters.
        Mimics the `binddata` logic from the ASP.NET code-behind.
        """
        # Base queryset filters matching ASP.NET `binddata`
        queryset = cls.objects.filter(
            company_id=company_id,
            # ASP.NET uses `FinYearId<=`, this is mapped to FK `fin_year`
            fin_year__id__lte=fin_year_id, 
            # ASP.NET uses `ResignationDate=''`
            resignation_date__isnull=True, 
            user_id__ne='1' # Excludes user_id '1'
        ).order_by('-user_id') # Orders by UserID Desc

        # Apply dynamic search filters
        if search_criteria and search_criteria.get('value'):
            search_type = search_criteria.get('type')
            search_value = search_criteria.get('value')
            
            if search_type == 'employee_name':
                # Search by raw employee name, case-insensitive partial match
                queryset = queryset.filter(employee_name_raw__icontains=search_value)
            elif search_type == 'department':
                # Search by department description, case-insensitive partial match
                queryset = queryset.filter(department__description__icontains=search_value)
            elif search_type == 'bg_group':
                # Search by business group symbol, case-insensitive partial match
                queryset = queryset.filter(bg_group__symbol__icontains=search_value)

        return queryset

```

#### 4.2 Forms (`hr_payroll/forms.py`)

A simple Django Form is used for the search controls, as it's not directly mapping to a database model for creation/update.

```python
# hr_payroll/forms.py
from django import forms

class EmployeeSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('employee_name', 'Employee Name'),
        ('department', 'Dept Name'),
        ('('bg_group', 'BG Group'),
    ]
    
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 min-w-[150px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/hr_payroll/salary-edit/get_search_input_type/', # HTMX endpoint to dynamically load the correct input field
            'hx-target': '#search_input_container',
            'hx-swap': 'innerHTML'
        }),
        label="Search By"
    )
    # The 'search_text' field will be dynamically rendered in a partial template
    # Its value will be captured directly from the request in the view.
    # No explicit field here as it's part of the dynamic form portion.
```

#### 4.3 Views (`hr_payroll/views.py`)

Views are kept thin, delegating business logic to models. HTMX is heavily utilized for dynamic content.

```python
# hr_payroll/views.py
from django.views.generic import ListView, View
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from .models import OfficeStaff, Department, BusinessGroup # Import necessary lookup models
from .forms import EmployeeSearchForm

# Helper function to mock ASP.NET Session context (e.g., Company ID, Financial Year ID)
def get_session_context(request):
    """
    Mocks retrieval of session-based company and financial year IDs.
    In a real application, these would come from the authenticated user's session
    or a multi-tenancy context.
    """
    # Default values for testing/initial setup
    company_id = request.session.get('compid', 1) 
    fin_year_id = request.session.get('finyear', 1) 
    return {'company_id': company_id, 'fin_year_id': fin_year_id}

class SalaryEditListView(ListView):
    """
    Handles the main Salary Edit list page, including the search form.
    """
    model = OfficeStaff
    template_name = 'hr_payroll/salary_edit/list.html'
    context_object_name = 'employees'
    paginate_by = 20 # Although DataTables handles pagination client-side, Django's paginate_by can provide initial paginated data if needed for server-side processing or alternative rendering.

    def get_queryset(self):
        """
        Retrieves the filtered queryset based on session context and search parameters.
        Logic is delegated to OfficeStaff.get_filtered_employees.
        """
        context = get_session_context(self.request)
        company_id = context['company_id']
        fin_year_id = context['fin_year_id']

        # Get search parameters from GET request (for initial load or HTMX search)
        search_field = self.request.GET.get('search_field')
        search_value = self.request.GET.get('search_text')
        
        search_criteria = None
        if search_field and search_value:
            search_criteria = {'type': search_field, 'value': search_value}

        return OfficeStaff.get_filtered_employees(company_id, fin_year_id, search_criteria)

    def get_context_data(self, **kwargs):
        """
        Adds the search form and any query string messages to the context.
        """
        context = super().get_context_data(**kwargs)
        # Populate the form with current GET parameters for sticky form
        context['search_form'] = EmployeeSearchForm(self.request.GET or None)
        # Mimic ASP.NET Request.QueryString["msg"] by popping from session
        context['message'] = self.request.session.pop('msg', '') 
        return context

class EmployeeTablePartialView(SalaryEditListView):
    """
    HTMX partial view to render only the employee table content.
    This view re-renders the table after search operations without a full page reload.
    It inherits from SalaryEditListView to reuse its `get_queryset` and context logic.
    """
    template_name = 'hr_payroll/salary_edit/_employee_table.html'

    def get_context_data(self, **kwargs):
        # Override to ensure the correct page_obj is passed for SN calculation if paginated
        context = super().get_context_data(**kwargs)
        # DataTables handles client-side pagination, so we pass the entire filtered set.
        # If server-side DataTables processing was implemented, this would need adjustment.
        return context

class SearchInputPartialView(View):
    """
    HTMX endpoint to dynamically swap the search input field based on dropdown selection.
    """
    def get(self, request, *args, **kwargs):
        search_field = request.GET.get('search_field')
        context = {'search_field': search_field, 'request': request} # Pass request for current search_text value
        # Render the specific partial template for the search input
        return HttpResponse(render_to_string('hr_payroll/salary_edit/_search_input.html', context, request))

class EmployeeAutocomplete(View):
    """
    HTMX endpoint for jQuery UI Autocomplete for employee names.
    Mimics ASP.NET's `GetCompletionList` WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '')  # jQuery UI Autocomplete sends 'term'
        company_id = get_session_context(request)['company_id']
        
        if not prefix_text:
            return JsonResponse([], safe=False)

        # Filter employees whose raw name contains the prefix text
        # Order by name for consistent autocomplete results
        employees = OfficeStaff.objects.filter(
            company_id=company_id,
            resignation_date__isnull=True, # No resignation
            employee_name_raw__icontains=prefix_text
        ).order_by('employee_name_raw')[:10] # Limit to 10 results, similar to ASP.NET implicit limit

        results = []
        for emp in employees:
            # Format as "Employee Name [EmpId]" for the autocomplete display
            results.append(f"{emp.full_employee_name} [{emp.emp_id}]")
        
        return JsonResponse(results, safe=False) # `safe=False` allows non-dict JSON responses

```

#### 4.4 Templates

Templates adhere to DRY principles, extend `core/base.html`, and use HTMX for dynamic updates.

**`hr_payroll/templates/hr_payroll/salary_edit/list.html`** (Main list page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white font-bold py-2 px-4 rounded-t-md mb-4">
        &nbsp;PayRoll - Edit
    </div>

    <div class="bg-white p-6 rounded-b-md shadow-md">
        {# Search Form - Uses HTMX to submit and update table #}
        <form hx-get="{% url 'hr_payroll:salary_edit_table_partial' %}" hx-target="#employeeTableContainer" hx-swap="innerHTML">
            <div class="flex items-end space-x-4 mb-6">
                <div class="flex-shrink-0">
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">Search By</label>
                    {{ search_form.search_field }}
                </div>
                
                <div id="search_input_container" class="flex-grow">
                    {# This div will be updated by HTMX when search_field changes #}
                    {# Initial render of search input based on default choice or existing GET param #}
                    {% include 'hr_payroll/salary_edit/_search_input.html' with search_field=search_form.search_field.value search_text=request.GET.search_text %}
                </div>
                
                <div class="flex-shrink-0">
                    <button type="submit" class="redbox bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
                
                {% if message %}
                <div class="ml-4 text-red-500 font-bold">
                    {{ message }}
                </div>
                {% endif %}
            </div>
        </form>

        {# Employee Table Container - Content loaded via HTMX #}
        <div id="employeeTableContainer"
             hx-trigger="load, reloadEmployeeList from:body" {# Loads on page load, and on custom event #}
             hx-get="{% url 'hr_payroll:salary_edit_table_partial' %}"
             hx-swap="innerHTML">
            <!-- Initial loading state while HTMX fetches the table -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading employee data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Required for DataTables and jQuery UI Autocomplete #}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet">
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/SRWaU3fSEKrGVtP6xeOzQkKVLgBqTJblzcNQ=" crossorigin="anonymous"></script>
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">

<script>
    // Alpine.js component initialization if needed for modals or other UI state
    document.addEventListener('alpine:init', () => {
        // e.g., Alpine.data('modal', () => ({ isOpen: false, open: () => this.isOpen = true, close: () => this.isOpen = false }));
    });

    // DataTables initialization is handled within the _employee_table.html partial,
    // ensuring it runs after HTMX loads the table content.
    // jQuery UI Autocomplete init is handled within _search_input.html partial
</script>
{% endblock %}
```

**`hr_payroll/templates/hr_payroll/salary_edit/_employee_table.html`** (Partial for HTMX swap)

```html
{% comment %}
    This template is loaded via HTMX into #employeeTableContainer.
    It receives 'employees' (a queryset) and 'page_obj' (if ListView's pagination is active) as context.
{% endcomment %}
<div class="overflow-x-auto">
    <table id="employeeDataTable" class="min-w-full bg-white yui-datatable-theme border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            </tr>
        </thead>
        <tbody>
            {% if employees %}
                {% for employee in employees %}
                <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter + page_obj.start_index - 1 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {# Link to details page, assuming URL 'salary_edit_details' exists #}
                        <a href="{% url 'hr_payroll:salary_edit_details' employee.emp_id %}" class="text-blue-600 hover:underline">Select</a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ employee.fin_year.fin_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ employee.emp_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ employee.full_employee_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ employee.department.description }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ employee.bg_group.symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ employee.designation_rel }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="8" class="py-4 px-4 border-b border-gray-200 text-center text-lg font-bold text-maroon-600">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table content is loaded by HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#employeeDataTable')) {
            $('#employeeDataTable').DataTable().destroy(); // Destroy previous instance if it exists
        }
        $('#employeeDataTable').DataTable({
            "paging": true,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 20, // Matches ASP.NET PageSize
            "searching": true, // Enable client-side search box provided by DataTables
            "ordering": true,  // Enable client-side sorting
            "info": true,      // Show info about number of entries
            "autoWidth": false, // Disable auto-width to allow custom widths
            "columnDefs": [
                { "orderable": false, "targets": [0, 1] }, // Disable sorting on SN and Select columns
            ],
        });
    });
</script>
```

**`hr_payroll/templates/hr_payroll/salary_edit/_search_input.html`** (Partial for HTMX swapping of input fields)

```html
{% comment %}
    This template is loaded via HTMX into #search_input_container.
    It receives 'search_field' (the selected dropdown value) and 'search_text' (current input value).
{% endcomment %}
{% if search_field == 'employee_name' %}
    <input type="text" id="id_search_text_employee_name" name="search_text" 
           class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
           placeholder="Enter Employee Name" 
           value="{{ search_text }}"
           autocomplete="off">
    <script>
        // Initialize jQuery UI Autocomplete for employee name
        // This script runs every time this partial is loaded by HTMX.
        $(function() {
            $("#id_search_text_employee_name").autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: "{% url 'hr_payroll:employee_autocomplete' %}",
                        data: {
                            term: request.term
                        },
                        dataType: "json",
                        success: function(data) {
                            response(data);
                        }
                    });
                },
                minLength: 1, // Matches ASP.NET MinimumPrefixLength
                // AutoCompleteExtender has FirstRowSelected=True, CompletionSetCount=2.
                // jQuery UI's behavior is similar for FirstRowSelected. CompletionSetCount not directly applicable.
            });
        });
    </script>
{% else %}
    <input type="text" id="id_search_text_mrs" name="search_text" 
           class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
           placeholder="Enter Department or BG Group Name" 
           value="{{ search_text }}">
{% endif %}
```

#### 4.5 URLs (`hr_payroll/urls.py`)

URL patterns for the various views and HTMX endpoints.

```python
# hr_payroll/urls.py
from django.urls import path
from .views import (
    SalaryEditListView,
    EmployeeTablePartialView,
    SearchInputPartialView,
    EmployeeAutocomplete,
)

app_name = 'hr_payroll' # Namespace for URLs

urlpatterns = [
    # Main list view for Salary Edit
    path('salary-edit/', SalaryEditListView.as_view(), name='salary_edit_list'),
    
    # HTMX endpoint to refresh the employee table
    path('salary-edit/table/', EmployeeTablePartialView.as_view(), name='salary_edit_table_partial'),
    
    # HTMX endpoint to dynamically load search input field based on dropdown selection
    path('salary-edit/get_search_input_type/', SearchInputPartialView.as_view(), name='get_search_input_type'),
    
    # Endpoint for employee name autocomplete functionality
    path('autocomplete/employee/', EmployeeAutocomplete.as_view(), name='employee_autocomplete'),

    # Placeholder for the "Salary_Edit_Details" page, assuming it's a separate view
    path('salary-edit-details/<str:emp_id>/', View.as_view(), name='salary_edit_details'), # Replace View.as_view() with actual detail view
]

```

#### 4.6 Tests (`hr_payroll/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure functionality and maintainability.

```python
# hr_payroll/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import OfficeStaff, FinancialYear, Department, BusinessGroup, Designation
from .forms import EmployeeSearchForm

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for related lookup tables first
        cls.fin_year_2023 = FinancialYear.objects.create(id=1, fin_year='2023-2024')
        cls.fin_year_2024 = FinancialYear.objects.create(id=2, fin_year='2024-2025')
        cls.dept_it = Department.objects.create(id=10, description='IT Department')
        cls.dept_hr = Department.objects.create(id=20, description='HR Department')
        cls.bg_tech = BusinessGroup.objects.create(id=100, symbol='TECH')
        cls.bg_admin = BusinessGroup.objects.create(id=200, symbol='ADMIN')
        cls.desig_dev = Designation.objects.create(id=1000, symbol='DEV', type='Software Engineer')
        cls.desig_manager = Designation.objects.create(id=2000, symbol='MGR', type='Manager')

        # Create test OfficeStaff instances
        cls.employee_john = OfficeStaff.objects.create(
            emp_id='EMP001',
            user_id='U001',
            title='Mr',
            employee_name_raw='John Doe',
            fin_year=cls.fin_year_2023,
            department=cls.dept_it,
            bg_group=cls.bg_tech,
            designation_rel=cls.desig_dev,
            joining_date=timezone.now().date(),
            resignation_date=None, # No resignation
            company_id=100
        )
        cls.employee_jane = OfficeStaff.objects.create(
            emp_id='EMP002',
            user_id='U002',
            title='Ms',
            employee_name_raw='Jane Smith',
            fin_year=cls.fin_year_2023,
            department=cls.dept_hr,
            bg_group=cls.bg_admin,
            designation_rel=cls.desig_manager,
            joining_date=timezone.now().date(),
            resignation_date=None,
            company_id=100
        )
        cls.employee_resigned = OfficeStaff.objects.create(
            emp_id='EMP003',
            user_id='U003',
            title='Dr',
            employee_name_raw='Albert Einstein',
            fin_year=cls.fin_year_2023,
            department=cls.dept_it,
            bg_group=cls.bg_tech,
            designation_rel=cls.desig_dev,
            joining_date=timezone.now().date(),
            resignation_date=timezone.now().date() - timezone.timedelta(days=30), # Resigned
            company_id=100
        )
        cls.employee_system = OfficeStaff.objects.create(
            emp_id='EMP004',
            user_id='1', # Should be filtered out by `user_id__ne='1'`
            title='Mr',
            employee_name_raw='System Account',
            fin_year=cls.fin_year_2023,
            department=cls.dept_it,
            bg_group=cls.bg_tech,
            designation_rel=cls.desig_dev,
            joining_date=timezone.now().date(),
            resignation_date=None,
            company_id=100
        )
        cls.employee_other_company = OfficeStaff.objects.create(
            emp_id='EMP005',
            user_id='U005',
            title='Mr',
            employee_name_raw='Company B Employee',
            fin_year=cls.fin_year_2023,
            department=cls.dept_it,
            bg_group=cls.bg_tech,
            designation_rel=cls.desig_dev,
            joining_date=timezone.now().date(),
            resignation_date=None,
            company_id=200 # Different company
        )
        cls.employee_future_fin_year = OfficeStaff.objects.create(
            emp_id='EMP006',
            user_id='U006',
            title='Mr',
            employee_name_raw='Future Employee',
            fin_year=cls.fin_year_2024, # FinYearId is 2
            department=cls.dept_it,
            bg_group=cls.bg_tech,
            designation_rel=cls.desig_dev,
            joining_date=timezone.now().date(),
            resignation_date=None,
            company_id=100
        )

    def test_office_staff_attributes(self):
        emp = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(emp.employee_name_raw, 'John Doe')
        self.assertEqual(emp.title, 'Mr')
        self.assertEqual(emp.company_id, 100)
        self.assertIsNotNone(emp.joining_date)
        self.assertIsNone(emp.resignation_date)

    def test_full_employee_name_property(self):
        emp_john = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(emp_john.full_employee_name, 'Mr. John Doe')
        emp_jane = OfficeStaff.objects.get(emp_id='EMP002')
        self.assertEqual(emp_jane.full_employee_name, 'Ms. Jane Smith')
        
        # Test employee without title
        temp_emp = OfficeStaff.objects.create(emp_id='TEMP001', user_id='T001', title=None, employee_name_raw='No Title', fin_year=self.fin_year_2023, company_id=100)
        self.assertEqual(temp_emp.full_employee_name, 'No Title')


    def test_get_filtered_employees_base_filters(self):
        # Should return active employees for company 100, fin year <= 1, excluding user_id '1'
        # Expected: EMP001 (John Doe), EMP002 (Jane Smith)
        employees = OfficeStaff.get_filtered_employees(company_id=100, fin_year_id=1)
        self.assertEqual(employees.count(), 2) 
        self.assertIn(self.employee_john, employees)
        self.assertIn(self.employee_jane, employees)
        self.assertNotIn(self.employee_resigned, employees) # Resigned
        self.assertNotIn(self.employee_system, employees) # user_id '1'
        self.assertNotIn(self.employee_other_company, employees) # Different company
        self.assertNotIn(self.employee_future_fin_year, employees) # Future financial year

    def test_get_filtered_employees_employee_name_search(self):
        search_criteria = {'type': 'employee_name', 'value': 'John'}
        employees = OfficeStaff.get_filtered_employees(company_id=100, fin_year_id=1, search_criteria=search_criteria)
        self.assertEqual(employees.count(), 1)
        self.assertEqual(employees.first().emp_id, 'EMP001')

        search_criteria = {'type': 'employee_name', 'value': 'smith'} # Case-insensitive
        employees = OfficeStaff.get_filtered_employees(company_id=100, fin_year_id=1, search_criteria=search_criteria)
        self.assertEqual(employees.count(), 1)
        self.assertEqual(employees.first().emp_id, 'EMP002')

    def test_get_filtered_employees_department_search(self):
        search_criteria = {'type': 'department', 'value': 'IT'}
        employees = OfficeStaff.get_filtered_employees(company_id=100, fin_year_id=1, search_criteria=search_criteria)
        self.assertEqual(employees.count(), 1) # Only John Doe is IT dept and active
        self.assertEqual(employees.first().emp_id, 'EMP001')

    def test_get_filtered_employees_bg_group_search(self):
        search_criteria = {'type': 'bg_group', 'value': 'ADMIN'}
        employees = OfficeStaff.get_filtered_employees(company_id=100, fin_year_id=1, search_criteria=search_criteria)
        self.assertEqual(employees.count(), 1)
        self.assertEqual(employees.first().emp_id, 'EMP002')

class SalaryEditViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.fin_year = FinancialYear.objects.create(id=1, fin_year='2023-2024')
        cls.department = Department.objects.create(id=10, description='IT Department')
        cls.bg_group = BusinessGroup.objects.create(id=100, symbol='TECH')
        cls.designation = Designation.objects.create(id=1000, symbol='DEV', type='Software Engineer')
        OfficeStaff.objects.create(
            emp_id='EMP001', user_id='U001', title='Mr', employee_name_raw='Alice',
            fin_year=cls.fin_year, department=cls.department, bg_group=cls.bg_group,
            designation_rel=cls.designation, joining_date=timezone.now().date(),
            resignation_date=None, company_id=100
        )
        OfficeStaff.objects.create(
            emp_id='EMP002', user_id='U002', title='Ms', employee_name_raw='Bob',
            fin_year=cls.fin_year, department=cls.department, bg_group=cls.bg_group,
            designation_rel=cls.designation, joining_date=timezone.now().date(),
            resignation_date=None, company_id=100
        )
        OfficeStaff.objects.create(
            emp_id='EMP003', user_id='U003', title='Mr', employee_name_raw='Charlie',
            fin_year=cls.fin_year, department=cls.department, bg_group=cls.bg_group,
            designation_rel=cls.designation, joining_date=timezone.now().date(),
            resignation_date=timezone.now().date() - timezone.timedelta(days=1),
            company_id=100
        )
        OfficeStaff.objects.create(
            emp_id='EMP004', user_id='1', title='Mr', employee_name_raw='System',
            fin_year=cls.fin_year, department=cls.department, bg_group=cls.bg_group,
            designation_rel=cls.designation, joining_date=timezone.now().date(),
            resignation_date=None, company_id=100
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables for `get_session_context`
        session = self.client.session
        session['compid'] = 100
        session['finyear'] = 1
        session.save()

    def test_salary_edit_list_view_get(self):
        response = self.client.get(reverse('hr_payroll:salary_edit_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_payroll/salary_edit/list.html')
        self.assertContains(response, 'Alice')
        self.assertContains(response, 'Bob')
        self.assertNotContains(response, 'Charlie') # Resigned
        self.assertNotContains(response, 'System') # user_id 1
        self.assertIsInstance(response.context['search_form'], EmployeeSearchForm)

    def test_salary_edit_list_view_search_employee_name(self):
        response = self.client.get(reverse('hr_payroll:salary_edit_list'), {'search_field': 'employee_name', 'search_text': 'Ali'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alice')
        self.assertNotContains(response, 'Bob')

    def test_salary_edit_list_view_search_department(self):
        # All test employees are in 'IT Department' in this setup
        response = self.client.get(reverse('hr_payroll:salary_edit_list'), {'search_field': 'department', 'search_text': 'IT'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alice')
        self.assertContains(response, 'Bob')

    def test_salary_edit_list_view_search_bg_group(self):
        # All test employees are in 'TECH' BG Group in this setup
        response = self.client.get(reverse('hr_payroll:salary_edit_list'), {'search_field': 'bg_group', 'search_text': 'TECH'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alice')
        self.assertContains(response, 'Bob')

    def test_employee_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr_payroll:salary_edit_table_partial'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_payroll/salary_edit/_employee_table.html')
        self.assertContains(response, '<table id="employeeDataTable"')
        self.assertContains(response, 'Alice')

    def test_search_input_partial_view_employee_name(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr_payroll:get_search_input_type'), {'search_field': 'employee_name'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_payroll/salary_edit/_search_input.html')
        self.assertContains(response, 'id="id_search_text_employee_name"')

    def test_search_input_partial_view_other_field(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr_payroll:get_search_input_type'), {'search_field': 'department'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_payroll/salary_edit/_search_input.html')
        self.assertContains(response, 'id="id_search_text_mrs"')

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('hr_payroll:employee_autocomplete'), {'term': 'ali'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('Mr. Alice [EMP001]', response.json())
        self.assertNotIn('Ms. Bob [EMP002]', response.json())

    def test_employee_autocomplete_view_no_term(self):
        response = self.client.get(reverse('hr_payroll:employee_autocomplete'), {'term': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

```

### Step 5: HTMX and Alpine.js Integration

- **HTMX for dynamic updates:**
    - The `search_field` dropdown uses `hx-get` to `get_search_input_type` to dynamically swap the text input field (`_search_input.html`).
    - The search form uses `hx-get` to `salary_edit_table_partial` to update the employee list table without a full page reload.
    - The `employeeTableContainer` div uses `hx-trigger="load, reloadEmployeeList from:body"` and `hx-get="salary_edit_table_partial"` to load the table content on page load and allow other parts of the application to trigger a table refresh (e.g., after an edit on a separate details page).
- **Alpine.js for UI state management:**
    - An `alpine:init` block is included in `list.html` for future Alpine.js component integration, should complex client-side UI states or interactions be required (e.g., modals, toggles). For this specific page, HTMX handles most of the dynamic behavior.
- **DataTables for list views:**
    - The `_employee_table.html` partial includes a JavaScript block that initializes `$('#employeeDataTable').DataTable()` once the HTMX content is loaded. This provides client-side searching, sorting, and pagination.
    - The `yui-datatable-theme` CSS class from the original ASP.NET is kept for potential styling integration, though actual styling would be done with Tailwind CSS.
- **Autocomplete:** jQuery UI Autocomplete is used for the employee name search, integrated via JavaScript within the `_search_input.html` partial, calling the `employee_autocomplete` HTMX endpoint.
- **No additional JavaScript:** The entire frontend interaction strategy relies purely on HTMX, Alpine.js, jQuery, and DataTables, avoiding custom, sprawling JavaScript files.

---

### Final Notes

- **Placeholder Replacement:** All `[PLACEHOLDERS]` have been replaced with concrete names and values derived from the ASP.NET code analysis.
- **DRY Templates:** Template inheritance (`{% extends 'core/base.html' %}`) and partial templates (`_employee_table.html`, `_search_input.html`) are used for reusability.
- **Fat Models, Thin Views:** Business logic (data retrieval and filtering) has been pushed into the `OfficeStaff.get_filtered_employees` class method, keeping the Django views concise and focused on handling requests and responses.
- **Comprehensive Tests:** Unit tests for models and integration tests for views are provided to ensure code quality and prevent regressions during ongoing development or future migrations.
- **Scalability:** This approach supports scaling by separating concerns, leveraging efficient database queries in models, and offloading UI interactions to the client side with HTMX and DataTables.