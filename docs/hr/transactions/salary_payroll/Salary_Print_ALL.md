## ASP.NET to Django Conversion Script: Salary Report Modernization

This plan outlines the systematic conversion of your ASP.NET Salary Report module to a modern Django application. Our focus is on leveraging AI-assisted automation to streamline the migration process, reduce manual effort, and deliver a robust, maintainable, and user-friendly solution.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists as `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Aim for at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define corresponding Django models.

**Instructions:**
The ASP.NET code interacts with numerous database tables to generate the salary report. We've identified the following key tables and will infer common column names and types based on their usage in the C# code. This forms the foundation for your Django models.

-   **`tblHR_Salary_Master`**: Contains main salary records.
    *   `Id` (Primary Key, inferred `int`)
    *   `EmpId` (`string`) - Foreign key to `tblHR_OfficeStaff`
    *   `FMonth` (`int`) - Month of salary
    *   `FinYearId` (`int`) - Foreign key to `tblFinancial_master`
    *   `CompId` (`int`) - Company ID, foreign key to `tblCompany_Master` (inferred)
    *   `Increment` (`int`) - Increment version of offer
-   **`tblHR_OfficeStaff`**: Employee basic information.
    *   `EmpId` (Primary Key, inferred `string` or `int` for Django)
    *   `UserID` (`string`)
    *   `CompId` (`int`)
    *   `OfferId` (`int`) - Foreign key to `tblHR_Offer_Master`
    *   `FinYearId` (`int`)
    *   `Title` (`string`)
    *   `EmployeeName` (`string`)
    *   `SwapCardNo` (`string`)
    *   `Department` (`int`) - Foreign key to `tblHR_Departments`
    *   `BGGroup` (`int`) - Business Group
    *   `DirectorsName` (`string`)
    *   `DeptHead` (`string`)
    *   `Designation` (`int`) - Foreign key to `tblHR_Designation`
    *   `Grade` (`int`) - Foreign key to `tblHR_Grade`
    *   `MobileNo` (`string`)
    *   `BankAccountNo` (`string`)
    *   `PFNo` (`string`)
    *   `PANNo` (`string`)
-   **`tblHR_Offer_Master`**: Defines salary structure for offers.
    *   `OfferId` (Primary Key, inferred `int`)
    *   `StaffType` (`int`) - Foreign key to `tblHR_EmpType`
    *   `TypeOf` (`int`)
    *   `salary` (`double`) - Gross salary
    *   `DutyHrs` (`int`) - Foreign key to `tblHR_DutyHour`
    *   `OTHrs` (`int`) - Foreign key to `tblHR_OTHour`
    *   `OverTime` (`int`) - Overtime calculation type
    *   `Designation` (`int`)
    *   `ExGratia` (`double`)
    *   `VehicleAllowance` (`double`)
    *   `LTA` (`double`)
    *   `Loyalty` (`double`)
    *   `PaidLeaves` (`double`)
    *   `Bonus` (`double`)
    *   `AttBonusPer1` (`int`) - Attendance bonus percentage 1
    *   `AttBonusPer2` (`int`) - Attendance bonus percentage 2
    *   `PFEmployee` (`double`) - Employee PF percentage
    *   `PFCompany` (`double`) - Company PF percentage
    *   `Increment` (`int`) - Increment version
-   **`tblHR_Increment_Master`**: Stores increment-specific salary structures.
    *   `Id` (Primary Key, inferred `int`)
    *   `OfferId` (`int`) - Foreign key to `tblHR_Offer_Master`
    *   `Increment` (`int`)
    *   `salary` (`double`)
    *   ... (Other fields similar to `tblHR_Offer_Master`)
-   **`tblHR_Salary_Details`**: Monthly attendance and specific deductions/additions.
    *   `Id` (Primary Key, inferred `int`)
    *   `MId` (`int`) - Foreign key to `tblHR_Salary_Master`
    *   `Present` (`double`)
    *   `Absent` (`double`)
    *   `LateIn` (`double`)
    *   `HalfDay` (`double`)
    *   `Sunday` (`double`)
    *   `Coff` (`double`)
    *   `PL` (`double`)
    *   `OverTimeHrs` (`double`)
    *   `OverTimeRate` (`double`)
    *   `Installment` (`double`)
    *   `MobileExeAmt` (`double`)
    *   `Addition` (`double`)
    *   `Remarks1` (`string`)
    *   `Deduction` (`double`)
    *   `Remarks2` (`string`)
-   **`tblFinancial_master`**: Financial year definitions.
    *   `FinYearId` (Primary Key, inferred `int`)
    *   `FinYear` (`string`) - e.g., "2023-2024"
    *   `CompId` (`int`)
-   **`tblHR_Departments`**: Department details.
    *   `Id` (Primary Key, inferred `int`)
    *   `Description` (`string`)
    *   `Symbol` (`string`)
-   **`tblHR_Designation`**: Designation details.
    *   `Id` (Primary Key, inferred `int`)
    *   `Type` (`string`)
    *   `Symbol` (`string`)
-   **`tblHR_Grade`**: Grade details.
    *   `Id` (Primary Key, inferred `int`)
    *   `Symbol` (`string`)
-   **`tblHR_EmpType`**: Employee type details.
    *   `Id` (Primary Key, inferred `int`)
    *   `Description` (`string`)
-   **`tblHR_Offer_Accessories`**: Accessories/benefits linked to offers.
    *   `Id` (Primary Key, inferred `int`)
    *   `MId` (`int`) - Foreign key to `tblHR_Offer_Master`
    *   `Qty` (`double`)
    *   `Amount` (`double`)
    *   `IncludesIn` (`string`) - "1" for CTC, "2" for Take Home, "3" for Both
-   **`tblHR_Increment_Accessories`**: Accessories/benefits linked to increments.
    *   `Id` (Primary Key, inferred `int`)
    *   `MId` (`int`) - Foreign key to `tblHR_Increment_Master`
    *   `Qty` (`double`)
    *   `Amount` (`double`)
    *   `IncludesIn` (`string`)
-   **`tblHR_OTHour`**: Overtime hour definitions.
    *   `Id` (Primary Key, inferred `int`)
    *   `Hours` (`double`)
-   **`tblHR_DutyHour`**: Duty hour definitions.
    *   `Id` (Primary Key, inferred `int`)
    *   `Hours` (`double`)

### Step 2: Identify Backend Functionality

**Task:** Determine the core functionality of the ASP.NET code, which is primarily report generation.

**Instructions:**
The provided ASP.NET code focuses on a single, complex **Read** operation: generating a detailed salary report for multiple employees based on specific month, financial year, and business group.

-   **Read (Report Generation):**
    -   The `Page_Init` method fetches various parameters (Business Group, Month, Financial Year, Company ID) from query strings and session.
    -   It then queries multiple HR-related tables (`tblHR_Salary_Master`, `tblHR_OfficeStaff`, `tblHR_Offer_Master`, `tblHR_Salary_Details`, etc.) to gather comprehensive employee and salary data.
    -   Extensive calculations are performed to determine various salary components (Basic, DA, HRA, PF, PTax, Attendance Bonus, Overtime, Miscellaneous additions/deductions) based on complex business rules defined in helper functions (`fun.Offer_Cal`, `fun.Pf_Cal`, `fun.PTax_Cal`, etc.).
    -   The calculated data is compiled into a `DataTable` and then used as the data source for a Crystal Report, which is displayed.
    -   There are no explicit Create, Update, or Delete operations on the *salary data itself* within this specific module; it's purely for display. The 'Cancel' button simply redirects.

**Core Business Logic:** The `fun` helper functions and the detailed calculations within the loop are the critical business logic that needs to be migrated to Python methods within Django models or a dedicated service class. This includes logic for:
-   Calculating working days, Sundays, and holidays.
-   Determining salary components (Basic, DA, HRA, etc.) based on offer/increment.
-   Calculating PF (Employee/Company), Professional Tax.
-   Calculating Overtime amount.
-   Determining Attendance Bonus.
-   Aggregating miscellaneous additions and deductions.
-   Final Net Pay calculation.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in the reporting context.

**Instructions:**
The original ASP.NET page is simple, primarily acting as a container for a Crystal Report viewer.

-   **Input Controls (Inferred):** While not directly visible in the ASPX, the C# code retrieves `BGGroupId`, `MonthId`, `CompId`, `FinYearId` from `Request.QueryString` and `Session`. This implies there's likely a preceding page (`Salary_Print.aspx`) where these parameters are selected.
    *   **Django Equivalent:** We will create a simple Django form with `select` elements (DropdownLists) for `Business Group`, `Month`, and `Financial Year`.
-   **Report Display:** `CR:CrystalReportViewer` and `CR:CrystalReportSource`
    *   **Django Equivalent:** This will be replaced by a dynamically generated HTML table, powered by **DataTables**, to present the calculated salary data.
-   **Action Button:** `asp:Button ID="Cancel"`
    *   **Django Equivalent:** A simple HTML `<button>` or `<a>` tag that redirects or closes a modal, typically styled with Tailwind CSS.

### Step 4: Generate Django Code

We will create a new Django application named `hr_reports`.

#### 4.1 Models (`hr_reports/models.py`)

**Task:** Create Django models based on the identified database schema. Since these tables already exist, we will use `managed = False`.

```python
from django.db import models

# Assuming a `Company` model exists elsewhere for `CompId`
# from core.models import Company # Example for FK if Company model is available

class FinancialYear(models.Model):
    fin_year_id = models.AutoField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50) # e.g., "2023-2024"
    company_id = models.IntegerField(db_column='CompId') # Or models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Department(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return f"{self.description} [{self.symbol}]"

class Designation(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.type} [{self.symbol}]"

class Grade(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.symbol

class EmployeeType(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_EmpType'
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'

    def __str__(self):
        return self.description

class OTHour(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    hours = models.FloatField(db_column='Hours')

    class Meta:
        managed = False
        db_table = 'tblHR_OTHour'
        verbose_name = 'OT Hour'
        verbose_name_plural = 'OT Hours'

    def __str__(self):
        return f"{self.hours} hours"

class DutyHour(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    hours = models.FloatField(db_column='Hours')

    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'Duty Hour'
        verbose_name_plural = 'Duty Hours'

    def __str__(self):
        return f"{self.hours} hours"

class OfferMaster(models.Model):
    offer_id = models.AutoField(db_column='OfferId', primary_key=True)
    staff_type = models.ForeignKey(EmployeeType, on_delete=models.DO_NOTHING, db_column='StaffType')
    type_of = models.IntegerField(db_column='TypeOf') # e.g., 1 for SAPL, 2 for NEHA
    salary = models.FloatField(db_column='salary') # Gross Salary
    duty_hrs = models.ForeignKey(DutyHour, on_delete=models.DO_NOTHING, db_column='DutyHrs', null=True, blank=True)
    ot_hrs = models.ForeignKey(OTHour, on_delete=models.DO_NOTHING, db_column='OTHrs', null=True, blank=True)
    over_time = models.IntegerField(db_column='OverTime') # e.g., 2 for enabled
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation')
    ex_gratia = models.FloatField(db_column='ExGratia', default=0.0)
    vehicle_allowance = models.FloatField(db_column='VehicleAllowance', default=0.0)
    lta = models.FloatField(db_column='LTA', default=0.0)
    loyalty = models.FloatField(db_column='Loyalty', default=0.0)
    paid_leaves = models.FloatField(db_column='PaidLeaves', default=0.0)
    bonus = models.FloatField(db_column='Bonus', default=0.0)
    att_bonus_per1 = models.IntegerField(db_column='AttBonusPer1', default=0)
    att_bonus_per2 = models.IntegerField(db_column='AttBonusPer2', default=0)
    pf_employee = models.FloatField(db_column='PFEmployee', default=0.0)
    pf_company = models.FloatField(db_column='PFCompany', default=0.0)
    increment = models.IntegerField(db_column='Increment', default=1)

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Master'
        verbose_name_plural = 'Offer Masters'

    def __str__(self):
        return f"Offer {self.offer_id} - {self.salary}"

class IncrementMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    offer = models.ForeignKey(OfferMaster, on_delete=models.DO_NOTHING, db_column='OfferId')
    increment = models.IntegerField(db_column='Increment')
    salary = models.FloatField(db_column='salary')
    # ... other fields as in OfferMaster that can change with increment
    staff_type = models.ForeignKey(EmployeeType, on_delete=models.DO_NOTHING, db_column='StaffType', null=True) # Added for consistency
    type_of = models.IntegerField(db_column='TypeOf', null=True)
    duty_hrs = models.ForeignKey(DutyHour, on_delete=models.DO_NOTHING, db_column='DutyHrs', null=True, blank=True)
    ot_hrs = models.ForeignKey(OTHour, on_delete=models.DO_NOTHING, db_column='OTHrs', null=True, blank=True)
    over_time = models.IntegerField(db_column='OverTime', null=True)
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation', null=True)
    ex_gratia = models.FloatField(db_column='ExGratia', default=0.0, null=True)
    vehicle_allowance = models.FloatField(db_column='VehicleAllowance', default=0.0, null=True)
    att_bonus_per1 = models.IntegerField(db_column='AttBonusPer1', default=0, null=True)
    att_bonus_per2 = models.IntegerField(db_column='AttBonusPer2', default=0, null=True)
    pf_employee = models.FloatField(db_column='PFEmployee', default=0.0, null=True)
    pf_company = models.FloatField(db_column='PFCompany', default=0.0, null=True)


    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Master'
        verbose_name = 'Increment Master'
        verbose_name_plural = 'Increment Masters'

    def __str__(self):
        return f"Increment {self.increment} for Offer {self.offer.offer_id}"

class OfficeStaff(models.Model):
    # EmpId as primary key assuming it's unique and maps to a string/int in the DB
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    user_id = models.CharField(db_column='UserID', max_length=50)
    company_id = models.IntegerField(db_column='CompId') # Or models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    offer = models.ForeignKey(OfferMaster, on_delete=models.DO_NOTHING, db_column='OfferId')
    fin_year_id = models.IntegerField(db_column='FinYearId') # Redundant if per employee, consider relation
    title = models.CharField(db_column='Title', max_length=10)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    swap_card_no = models.CharField(db_column='SwapCardNo', max_length=50)
    department = models.ForeignKey(Department, on_delete=models.DO_NOTHING, db_column='Department')
    bg_group = models.IntegerField(db_column='BGGroup')
    directors_name = models.CharField(db_column='DirectorsName', max_length=255)
    dept_head = models.CharField(db_column='DeptHead', max_length=255)
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation')
    grade = models.ForeignKey(Grade, on_delete=models.DO_NOTHING, db_column='Grade')
    mobile_no = models.CharField(db_column='MobileNo', max_length=20)
    bank_account_no = models.CharField(db_column='BankAccountNo', max_length=50)
    pf_no = models.CharField(db_column='PFNo', max_length=50, null=True, blank=True)
    pan_no = models.CharField(db_column='PANNo', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"

class SalaryMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    emp = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='emp_id') # Link to OfficeStaff.emp_id
    fmonth = models.IntegerField(db_column='FMonth')
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    company_id = models.IntegerField(db_column='CompId') # Or models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    increment = models.IntegerField(db_column='Increment', default=1)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Master'
        verbose_name = 'Salary Master'
        verbose_name_plural = 'Salary Masters'
        unique_together = ('emp', 'fmonth', 'fin_year', 'company_id') # Assuming this combination is unique

    def __str__(self):
        return f"Salary for {self.emp.employee_name} ({self.fmonth}/{self.fin_year.fin_year})"

class SalaryDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    master = models.OneToOneField(SalaryMaster, on_delete=models.DO_NOTHING, db_column='MId', primary_key=True) # OneToOne with SalaryMaster
    present = models.FloatField(db_column='Present', default=0.0)
    absent = models.FloatField(db_column='Absent', default=0.0)
    late_in = models.FloatField(db_column='LateIn', default=0.0)
    half_day = models.FloatField(db_column='HalfDay', default=0.0)
    sunday = models.FloatField(db_column='Sunday', default=0.0)
    coff = models.FloatField(db_column='Coff', default=0.0)
    pl = models.FloatField(db_column='PL', default=0.0) # Paid Leave
    over_time_hrs = models.FloatField(db_column='OverTimeHrs', default=0.0)
    over_time_rate = models.FloatField(db_column='OverTimeRate', default=0.0) # Unused in C# code, likely calculated
    installment = models.FloatField(db_column='Installment', default=0.0)
    mobile_exe_amt = models.FloatField(db_column='MobileExeAmt', default=0.0)
    addition = models.FloatField(db_column='Addition', default=0.0)
    remarks1 = models.CharField(db_column='Remarks1', max_length=255, null=True, blank=True)
    deduction = models.FloatField(db_column='Deduction', default=0.0)
    remarks2 = models.CharField(db_column='Remarks2', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Details'
        verbose_name = 'Salary Detail'
        verbose_name_plural = 'Salary Details'

    def __str__(self):
        return f"Details for Salary Master {self.master.id}"

class OfferAccessory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    master = models.ForeignKey(OfferMaster, on_delete=models.DO_NOTHING, db_column='MId')
    qty = models.FloatField(db_column='Qty', default=0.0)
    amount = models.FloatField(db_column='Amount', default=0.0)
    includes_in = models.CharField(db_column='IncludesIn', max_length=10) # 1:CTC, 2:TH, 3:Both

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'Offer Accessory'
        verbose_name_plural = 'Offer Accessories'

    def __str__(self):
        return f"Accessory for Offer {self.master.offer_id}"

class IncrementAccessory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    master = models.ForeignKey(IncrementMaster, on_delete=models.DO_NOTHING, db_column='MId')
    qty = models.FloatField(db_column='Qty', default=0.0)
    amount = models.FloatField(db_column='Amount', default=0.0)
    includes_in = models.CharField(db_column='IncludesIn', max_length=10) # 1:CTC, 2:TH, 3:Both

    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Accessories'
        verbose_name = 'Increment Accessory'
        verbose_name_plural = 'Increment Accessories'

    def __str__(self):
        return f"Accessory for Increment {self.master.id}"

# --- Business Logic / Service Layer within models or separate file ---
# This complex logic will be encapsulated in a service or manager for clarity and testability.
# We'll put it here for now to demonstrate the "fat model" principle if it can be directly tied to salary calculation.
# However, for multi-model calculations, a separate service class is often better.

# Let's define a service class `SalaryReportGenerator` that lives in a `services.py` file.
# This keeps the model definition clean and puts complex cross-model logic into a dedicated layer.
```

#### 4.1.1 Services (`hr_reports/services.py`)

**Task:** Replicate the complex calculation logic from `Page_Init` and `fun` functions into a dedicated service layer. This is crucial for maintaining a "thin view" and "fat model/service" architecture.

```python
import calendar
from datetime import datetime
from django.db.models import QuerySet

# Import all necessary models
from .models import (
    SalaryMaster, OfficeStaff, OfferMaster, IncrementMaster, SalaryDetail,
    FinancialYear, Department, Designation, Grade, EmployeeType,
    OfferAccessory, IncrementAccessory, OTHour, DutyHour
)

class SalaryReportGenerator:
    """
    Service class to encapsulate the complex salary calculation logic
    originally found in the ASP.NET Page_Init method and helper functions.
    """

    def __init__(self, company_id: int, financial_year_id: int, month_id: int, bg_group_id: int):
        self.company_id = company_id
        self.financial_year_id = financial_year_id
        self.month_id = month_id
        self.bg_group_id = bg_group_id
        self.fin_year_obj = FinancialYear.objects.get(fin_year_id=financial_year_id)
        self.year = self._get_calendar_year()
        self.days_in_month = calendar.monthrange(self.year, self.month_id)[1]
        self.sundays_in_month = self._count_sundays()
        self.holidays_in_month = self._get_holiday_count() # Placeholder for actual holiday logic

    def _get_calendar_year(self) -> int:
        """
        Determines the calendar year based on financial year and month.
        Equivalent to fun.SalYrs logic.
        """
        fin_year_parts = self.fin_year_obj.fin_year.split('-')
        start_year = int(fin_year_parts[0])
        end_year = int(fin_year_parts[1])
        if self.month_id in [1, 2, 3]: # January, February, March typically fall in the end year of financial year
            return end_year
        else:
            return start_year

    def _count_sundays(self) -> int:
        """
        Counts Sundays in the given month and year.
        Equivalent to fun.CountSundays.
        """
        sunday_count = 0
        for day in range(1, self.days_in_month + 1):
            if datetime(self.year, self.month_id, day).weekday() == calendar.SUNDAY:
                sunday_count += 1
        return sunday_count

    def _get_holiday_count(self) -> int:
        """
        Placeholder for fetching actual holiday count.
        Equivalent to fun.GetHoliday.
        In a real system, this would query a Holiday model or similar.
        """
        # This needs actual database logic for tblHR_Holiday_Master or similar
        # For now, returning a static value for demonstration.
        return 2 # Example: Assume 2 holidays for simplicity

    def _calculate_working_days(self) -> float:
        """
        Calculates working days for the month.
        Equivalent to fun.WorkingDays.
        """
        # This typically means total days minus Sundays, holidays, and maybe other off days.
        # Assuming the original `fun.WorkingDays` returned DayOfMonth - (Sundays + Holidays).
        # The C# code uses DayOfMonth - (Absent - (PL + Coff)) which is complex.
        # Let's interpret it as days available for work, excluding fixed offs.
        return self.days_in_month - self.sundays_in_month - self.holidays_in_month
    
    def _calculate_offer_component(self, gross_salary: float, component_type: int, offer_type: int, staff_type: int) -> float:
        """
        Placeholder for fun.Offer_Cal.
        This would be a complex business rule based on component_type (Basic, DA, HRA, etc.),
        offer_type (TypeOf), and staff_type.
        """
        # Example simplified logic, actual implementation would match fun.Offer_Cal
        if component_type == 1: # Basic
            return gross_salary * 0.40 # Example: 40% of gross
        elif component_type == 2: # DA
            return gross_salary * 0.20
        elif component_type == 3: # HRA
            return gross_salary * 0.15
        elif component_type == 4: # Conveyance
            return 1000.0 # Fixed amount
        elif component_type == 5: # Education
            return 500.0
        elif component_type == 6: # Medical
            return 750.0
        return 0.0

    def _calculate_pf(self, gross_or_cal_gross: float, pf_type: int, pf_percentage: float) -> float:
        """
        Placeholder for fun.Pf_Cal.
        Calculates PF for employee (type 1) or company (type 2).
        """
        if pf_percentage > 0:
            return round((gross_or_cal_gross * pf_percentage) / 100, 2)
        return 0.0

    def _calculate_ot_rate(self, gross_salary: float, ot_hours: float, duty_hours: float) -> float:
        """
        Placeholder for fun.OTRate.
        Calculates per-hour OT rate.
        """
        if duty_hours > 0:
            # Example: (Gross / DaysInMonth / DutyHoursPerDay) * OT_Factor
            return round((gross_salary / self.days_in_month / duty_hours) * 2, 2) # Assume 2x OT
        return 0.0

    def _calculate_ot_amount(self, ot_rate: float, ot_actual_hours: float) -> float:
        """
        Placeholder for fun.OTAmt.
        Calculates total OT amount.
        """
        return round(ot_rate * ot_actual_hours, 2)

    def _calculate_p_tax(self, taxable_income: float) -> float:
        """
        Placeholder for fun.PTax_Cal.
        Calculates Professional Tax based on income slab.
        This is state-specific in India.
        """
        # Example slabs, replace with actual P-Tax rules
        if taxable_income > 15000:
            return 200.0 # Example P-Tax amount
        elif taxable_income > 10000:
            return 150.0
        return 0.0

    def generate_report_data(self) -> list:
        """
        Generates the comprehensive salary report data, replicating the C# Page_Init logic.
        Returns a list of dictionaries, each representing an employee's salary record.
        """
        employees_data = []

        # Step 1: Filter employees based on BGGroup and fetch their SalaryMaster records
        salary_masters = SalaryMaster.objects.select_related(
            'emp__department', 'emp__designation', 'emp__grade',
            'emp__offer__staff_type', 'emp__offer__duty_hrs', 'emp__offer__ot_hrs',
            'fin_year'
        ).filter(
            company_id=self.company_id,
            fmonth=self.month_id,
            fin_year_id=self.financial_year_id
        )

        if self.bg_group_id != 0: # Assuming 0 or 1 is "All" or a specific group
            if self.bg_group_id == 1: # "All" staff / OfficeStaff
                 # If BGGroupId == 1, means no filter by BGGroup
                 # The current filter already covers this if it's based on general salary masters.
                 # The C# code uses 'BGGroupId == 1' for 'OfficeStaff' type records without BGGroup filter
                 # and 'else' for specific BGGroup. We'll handle this by default filtering on staff for 0.
                 # If BGGroupId is 0, it means all groups or 'OfficeStaff' which has BGGroup=1.
                 # The C# code implies BGGroupId == 1 means office staff (not filtering by BGGroup)
                 # and else means filtering by BGGroup. Let's assume bg_group_id=0 means all and bg_group_id=1 means office staff.
                 # If the original DB schema for tblHR_OfficeStaff has BGGroup values for "office staff",
                 # then filter by that. Otherwise, skip filter for BGGroup=1.
                 pass # No additional filter for BGGroup == 1
            else:
                salary_masters = salary_masters.filter(emp__bg_group=self.bg_group_id)

        for sal_master in salary_masters:
            emp = sal_master.emp
            offer = emp.offer
            salary_detail = None
            try:
                salary_detail = SalaryDetail.objects.get(master=sal_master)
            except SalaryDetail.DoesNotExist:
                # If no salary details, skip or populate with zeros
                continue # Skip for now, or handle as per business rule

            current_offer_or_increment = None
            # Check for increment match as per C# logic
            if IncrementMaster.objects.filter(offer=offer, increment=sal_master.increment).exists():
                current_offer_or_increment = IncrementMaster.objects.get(offer=offer, increment=sal_master.increment)
            else:
                current_offer_or_increment = offer # Use base offer if no matching increment

            if not current_offer_or_increment:
                continue # Skip if no offer/increment found

            # --- Employee Basic Info ---
            employee_data = {
                'emp_id': emp.emp_id,
                'company_id': emp.company_id,
                'employee_name': f"{emp.title}.{emp.employee_name}",
                'month': datetime(self.year, self.month_id, 1).strftime('%B'),
                'year': str(self.year),
                'department': str(emp.department) if emp.department else '',
                'designation': str(emp.designation) if emp.designation else '',
                'grade': str(emp.grade) if emp.grade else '',
                'status': '', # Will be set based on TypeOf and StaffType
                'pf_no': emp.pf_no or '',
                'pan_no': emp.pan_no or '',
                'bank_account_no': emp.bank_account_no or '',
                'date': datetime.now().strftime('%d-%m-%Y'), # Equivalent to fun.getCurrDate() and fun.FromDateDMY

                # Salary components (initialized to 0)
                'basic': 0.0, 'da': 0.0, 'hra': 0.0, 'conveyance': 0.0, 'education': 0.0, 'medical': 0.0,
                'gross_total': 0.0, 'attendance_bonus': 0.0, 'special_allowance': 0.0, 'ex_gratia': 0.0,
                'travelling_allowance': 0.0, 'miscellaneous_add': 0.0, 'total_additions': 0.0, 'net_pay_pre_deductions': 0.0,
                'working_days_cal': 0.0, 'present_days': 0.0, 'absent_days': 0.0, 'sunday_taken': 0.0,
                'holiday_taken': 0.0, 'late_in': 0.0, 'coff': 0.0, 'half_days': 0.0, 'pl': 0.0, 'lwp': 0.0,
                'pf_of_employee': 0.0, 'p_tax': 0.0, 'personal_loan_install': 0.0, 'mobile_bill': 0.0,
                'miscellaneous_deduct': 0.0, 'total_deductions': 0.0, 'net_pay_final': 0.0,

                # Calculated components (for internal use or display)
                'cal_basic': 0.0, 'cal_da': 0.0, 'cal_hra': 0.0, 'cal_conveyance': 0.0, 'cal_education': 0.0, 'cal_medical': 0.0,
                'cal_gross_total': 0.0, 'att_bonus_type': 0, 'att_bonus_amount': 0.0
            }

            gross_salary_from_offer = current_offer_or_increment.salary

            # Calculate base components from offer/increment
            basic = self._calculate_offer_component(gross_salary_from_offer, 1, current_offer_or_increment.type_of, current_offer_or_increment.staff_type.id)
            da = self._calculate_offer_component(gross_salary_from_offer, 2, current_offer_or_increment.type_of, current_offer_or_increment.staff_type.id)
            hra = self._calculate_offer_component(gross_salary_from_offer, 3, current_offer_from_offer.type_of, current_offer_from_offer.staff_type.id)
            conveyance = self._calculate_offer_component(gross_salary_from_offer, 4, current_offer_or_increment.type_of, current_offer_or_increment.staff_type.id)
            education = self._calculate_offer_component(gross_salary_from_offer, 5, current_offer_or_increment.type_of, current_offer_or_increment.staff_type.id)
            medical = self._calculate_offer_component(gross_salary_from_offer, 6, current_offer_or_increment.type_of, current_offer_or_increment.staff_type.id)

            employee_data['basic'] = basic
            employee_data['da'] = da
            employee_data['hra'] = hra
            employee_data['conveyance'] = conveyance
            employee_data['education'] = education
            employee_data['medical'] = medical
            employee_data['gross_total'] = gross_salary_from_offer

            # Determine Status
            emp_type_desc = current_offer_or_increment.staff_type.description if current_offer_or_increment.staff_type else ''
            if current_offer_or_increment.type_of == 1:
                employee_data['status'] = f"SAPL - {emp_type_desc}"
            elif current_offer_or_increment.type_of == 2:
                employee_data['status'] = f"NEHA - {emp_type_desc}"

            # --- Salary Details Calculations (from SalaryDetail and other factors) ---
            working_days_of_month = self._calculate_working_days() # Total days available for work
            present_days = salary_detail.present
            absent_days = salary_detail.absent
            pl = salary_detail.pl
            coff = salary_detail.coff
            half_day = salary_detail.half_day
            sunday_present = salary_detail.sunday # Sundays employee marked present
            late_in = salary_detail.late_in
            installment = salary_detail.installment
            mob_bill = salary_detail.mobile_exe_amt
            addition_manual = salary_detail.addition
            deduction_manual = salary_detail.deduction
            overtime_hrs_recorded = salary_detail.over_time_hrs

            # Recalculate total days worked effectively
            # C# logic: TotalDays = DayOfMonth - (Absent - (PL + Coff));
            # This is complex, meaning LWP is calculated from this TotalDays
            # For simplicity, let's use the explicit LWP calculation as (DaysInMonth - effective_present_days)
            effective_present_days = present_days + pl + coff + half_day + sunday_present
            lwp = max(0, self.days_in_month - effective_present_days)

            employee_data['working_days_cal'] = working_days_of_month
            employee_data['present_days'] = present_days
            employee_data['absent_days'] = absent_days
            employee_data['sunday_taken'] = sunday_present
            employee_data['holiday_taken'] = self.holidays_in_month
            employee_data['late_in'] = late_in
            employee_data['coff'] = coff
            employee_data['half_days'] = half_day
            employee_data['pl'] = pl
            employee_data['lwp'] = lwp

            # --- Pro-rated salary components ---
            # C# uses Math.Round((Component * TotalDays) / DayOfMonth);
            # TotalDays here seems to be `DayOfMonth - (Absent - (PL + Coff))`
            total_days_for_proration = self.days_in_month - (absent_days - (pl + coff))

            cal_basic = round((basic * total_days_for_proration) / self.days_in_month, 2)
            cal_da = round((da * total_days_for_proration) / self.days_in_month, 2)
            cal_hra = round((hra * total_days_for_proration) / self.days_in_month, 2)
            cal_conveyance = round((conveyance * total_days_for_proration) / self.days_in_month, 2)
            cal_education = round((education * total_days_for_proration) / self.days_in_month, 2)
            cal_medical = round((medical * total_days_for_proration) / self.days_in_month, 2)
            cal_gross_total = round(cal_basic + cal_da + cal_hra + cal_conveyance + cal_education + cal_medical, 2)

            employee_data['cal_basic'] = cal_basic
            employee_data['cal_da'] = cal_da
            employee_data['cal_hra'] = cal_hra
            employee_data['cal_conveyance'] = cal_conveyance
            employee_data['cal_education'] = cal_education
            employee_data['cal_medical'] = cal_medical
            employee_data['cal_gross_total'] = cal_gross_total

            # PF for Employee
            pf_emp_percent = current_offer_or_increment.pf_employee
            cal_pf_employee = self._calculate_pf(cal_gross_total, 1, pf_emp_percent)
            employee_data['pf_of_employee'] = cal_pf_employee

            # Ex-Gratia (pro-rated)
            ex_gratia_amount = current_offer_or_increment.ex_gratia
            cal_ex_gratia = round((ex_gratia_amount * total_days_for_proration) / self.days_in_month, 2)
            employee_data['ex_gratia'] = cal_ex_gratia

            # Accessories Addition
            accessories_ctc = 0.0
            accessories_th = 0.0
            accessories_both = 0.0

            accessories_qs = QuerySet()
            if isinstance(current_offer_or_increment, OfferMaster):
                accessories_qs = OfferAccessory.objects.filter(master=current_offer_or_increment)
            elif isinstance(current_offer_or_increment, IncrementMaster):
                accessories_qs = IncrementAccessory.objects.filter(master=current_offer_or_increment)

            for acc in accessories_qs:
                total_acc_amount = acc.qty * acc.amount
                if acc.includes_in == '1': # CTC
                    accessories_ctc += total_acc_amount
                elif acc.includes_in == '2': # Take Home
                    accessories_th += total_acc_amount
                elif acc.includes_in == '3': # Both
                    accessories_both += total_acc_amount

            # Overtime Calculation
            ot_amount = 0.0
            if current_offer_or_increment.over_time == 2: # Overtime enabled
                ot_rate = 0.0
                duty_hrs_val = current_offer_or_increment.duty_hrs.hours if current_offer_or_increment.duty_hrs else 0
                ot_hrs_def = current_offer_or_increment.ot_hrs.hours if current_offer_or_increment.ot_hrs else 0

                if duty_hrs_val > 0 and ot_hrs_def > 0: # Ensure valid divisors
                    ot_rate = self._calculate_ot_rate(gross_salary_from_offer, ot_hrs_def, duty_hrs_val)
                ot_amount = self._calculate_ot_amount(ot_rate, overtime_hrs_recorded)

            # Attendance Bonus
            att_bonus_days = present_days + sunday_present + half_day
            att_bonus_type = 0
            att_bonus_amt = 0.0

            # C# logic: AttBonusDays >= (DayOfMonth - (Holiday + SundayInMonth + 2)) && AttBonusDays < ((DayOfMonth + 2) - (Holiday + SundayInMonth))
            # First Slab: AttBonusDays >= (TotalDaysOfMonth - (Holidays + SundaysInMonth + 2)) AND < ((TotalDaysOfMonth + 2) - (Holidays + SundaysInMonth))
            threshold1_lower = self.days_in_month - (self.holidays_in_month + self.sundays_in_month + 2)
            threshold1_upper = (self.days_in_month + 2) - (self.holidays_in_month + self.sundays_in_month)
            
            if att_bonus_days >= threshold1_lower and att_bonus_days < threshold1_upper:
                att_bonus_type = 1
                att_bonus_amt = round((gross_salary_from_offer * current_offer_or_increment.att_bonus_per1) / 100, 2)
            # C# logic: else if (AttBonusDays >= ((DayOfMonth + 2) - (Holiday + SundayInMonth)))
            # Second Slab: AttBonusDays >= ((TotalDaysOfMonth + 2) - (Holidays + SundaysInMonth))
            elif att_bonus_days >= threshold1_upper:
                att_bonus_type = 2
                att_bonus_amt = round((gross_salary_from_offer * current_offer_or_increment.att_bonus_per2) / 100, 2)

            employee_data['att_bonus_type'] = att_bonus_type
            employee_data['att_bonus_amount'] = att_bonus_amt

            # Miscellaneous Additions
            # VehicleAllow + AccessoriesTH + AccessoriesBoth + OTAmt + Addition
            vehicle_allowance_amt = current_offer_or_increment.vehicle_allowance
            misc_additions_total = round(vehicle_allowance_amt + accessories_th + accessories_both + ot_amount + addition_manual, 2)
            employee_data['miscellaneous_add'] = misc_additions_total

            # Professional Tax (PTax)
            # CalPTax = fun.PTax_Cal((CalGrossTotal + AttBonusAmt + AccessoriesTH + AccessoriesBoth + CalExGratia + VehicleAllow + Addition + OTAmt), MonthId.ToString("D2"));
            p_tax_input_income = cal_gross_total + att_bonus_amt + accessories_th + accessories_both + cal_ex_gratia + vehicle_allowance_amt + addition_manual + ot_amount
            cal_p_tax = self._calculate_p_tax(p_tax_input_income)
            employee_data['p_tax'] = cal_p_tax

            # Miscellaneous Deductions
            misc_deduct_total = deduction_manual
            employee_data['miscellaneous_deduct'] = misc_deduct_total
            
            # Total Deductions
            # CalPFEmp + CalPTax + Installment + MobBill + MiscDeduct
            total_deductions = round(cal_pf_employee + cal_p_tax + installment + mob_bill + misc_deduct_total, 2)
            employee_data['personal_loan_install'] = installment
            employee_data['mobile_bill'] = mob_bill
            employee_data['total_deductions'] = total_deductions

            # Net Pay (Before Deductions)
            # CalGrossTotal + AttBonusAmt + CalExGratia + MiscAdd
            net_pay_pre_deductions = round(cal_gross_total + att_bonus_amt + cal_ex_gratia + misc_additions_total, 2)
            employee_data['total_additions'] = net_pay_pre_deductions # Renamed to be total additions
            
            # Final Net Pay
            net_pay_final = round(net_pay_pre_deductions - total_deductions, 2)
            employee_data['net_pay_final'] = net_pay_final

            employees_data.append(employee_data)

        return employees_data
```

#### 4.2 Forms (`hr_reports/forms.py`)

**Task:** Define a Django form for selecting the report parameters.

```python
from django import forms
from .models import FinancialYear, Department

class SalaryReportForm(forms.Form):
    """
    Form for selecting parameters for the salary report.
    """
    company_id = forms.IntegerField(
        widget=forms.HiddenInput(),
        initial=1 # Default company ID, ideally from logged-in user or system config
    )

    financial_year = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all().order_by('-fin_year'),
        empty_label="Select Financial Year",
        label="Financial Year",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    month = forms.ChoiceField(
        choices=[(i, f"{_month_name}") for i, _month_name in enumerate(
            ["", "January", "February", "March", "April", "May", "June",
             "July", "August", "September", "October", "November", "December"]) if i > 0],
        empty_label="Select Month",
        label="Month",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # BGGroupId is a tricky one. Original code uses 1 for 'all staff' and then filters
    # based on actual BGGroup values. Let's provide a dropdown for BGGroup.
    # Assuming BGGroup values correspond to a specific business group, or '0'/'1' for all.
    # For now, let's map common values. This might need to be dynamic from DB.
    BG_GROUP_CHOICES = [
        ('', 'Select Business Group'),
        ('0', 'All Staff (BGGroup 0)'), # Assuming 0 represents "All Staff"
        ('1', 'Office Staff (BGGroup 1)'), # Or a specific BGGroup
        # Add more BGGroup options if they exist in tblHR_OfficeStaff.BGGroup
        # (models.OfficeStaff.objects.values_list('bg_group', flat=True).distinct())
    ]
    bg_group = forms.ChoiceField(
        choices=BG_GROUP_CHOICES,
        label="Business Group",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically load BGGroup choices if they are not static
        # Example: self.fields['bg_group'].choices = [(str(bg.bg_group), f"Group {bg.bg_group}") for bg in OfficeStaff.objects.values('bg_group').distinct()]
        # This requires careful handling if '0' or '1' have special meaning outside of actual BGGroup IDs.
        # For simplicity, we'll keep it as defined above for now, mimicking the ASP.NET interpretation.
```

#### 4.3 Views (`hr_reports/views.py`)

**Task:** Implement Django CBVs for the report input and the HTMX-driven report display.

```python
from django.views.generic import FormView, TemplateView
from django.urls import reverse_lazy
from django.http import HttpResponse
from django.contrib import messages
from .forms import SalaryReportForm
from .services import SalaryReportGenerator # Import the service class

class SalaryReportInputView(FormView):
    """
    View to display the form for selecting salary report parameters.
    Handles form submission and triggers report generation via HTMX.
    """
    template_name = 'hr_reports/salary_report/report_input.html'
    form_class = SalaryReportForm
    success_url = reverse_lazy('salary_report_input') # Redirects to itself on success

    def get_initial(self):
        initial = super().get_initial()
        # Pre-fill with current month/year or last selected values
        # In a real app, comp_id would come from request.user profile
        initial['company_id'] = self.request.session.get('compid', 1) # Get from session if available
        # You'd typically set default financial year/month based on current date or last user selection
        return initial

    def form_valid(self, form):
        # This method is called on POST request when form is valid.
        # It's kept thin; the heavy lifting is in the service.
        company_id = form.cleaned_data['company_id']
        financial_year_id = form.cleaned_data['financial_year'].fin_year_id
        month_id = int(form.cleaned_data['month'])
        bg_group_id = int(form.cleaned_data['bg_group']) # Convert to int

        # Store parameters in session for later use if needed, similar to ASP.NET
        self.request.session['report_params'] = {
            'company_id': company_id,
            'financial_year_id': financial_year_id,
            'month_id': month_id,
            'bg_group_id': bg_group_id
        }
        
        messages.success(self.request, "Report parameters submitted. Loading report...")
        
        # If HTMX request, we don't redirect. We trigger a swap to load the table.
        if self.request.headers.get('HX-Request'):
            # This triggers the _salary_table.html to be loaded via HTMX
            # We don't actually render the table here directly,
            # we just tell HTMX to update a target with data from another URL.
            return HttpResponse(
                status=204, # No content, successful POST
                headers={
                    'HX-Trigger': 'loadReportTable', # Custom event to trigger table load
                }
            )
        return super().form_valid(form) # For non-HTMX requests (unlikely for this flow)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Check if parameters are in session to determine if a report table should be loaded initially
        if self.request.session.get('report_params'):
            context['initial_report_params_exist'] = True
        return context

class SalaryReportTableView(TemplateView):
    """
    View to render the HTMX-powered DataTables partial with salary report data.
    This view will be called via HTMX and generate the report data.
    """
    template_name = 'hr_reports/salary_report/_salary_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = []

        # Retrieve parameters from session
        report_params = self.request.session.get('report_params')
        if report_params:
            try:
                generator = SalaryReportGenerator(
                    company_id=report_params['company_id'],
                    financial_year_id=report_params['financial_year_id'],
                    month_id=report_params['month_id'],
                    bg_group_id=report_params['bg_group_id']
                )
                report_data = generator.generate_report_data()
            except Exception as e:
                messages.error(self.request, f"Error generating report: {e}")
                report_data = [] # Ensure report_data is empty on error

        context['salary_records'] = report_data
        return context

```

#### 4.4 Templates (`hr_reports/templates/hr_reports/salary_report/`)

**Task:** Create templates for the input form and the report table.

##### `report_input.html` (Main page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Employee Salary Report</h2>

    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Select Report Parameters</h3>
        <form hx-post="{% url 'salary_report_input' %}" hx-swap="none" id="report-param-form">
            {% csrf_token %}
            {{ form.company_id }} {# Hidden input for company_id #}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div>
                    <label for="{{ form.financial_year.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.financial_year.label }}
                    </label>
                    {{ form.financial_year }}
                    {% if form.financial_year.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.financial_year.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.month.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.month.label }}
                    </label>
                    {{ form.month }}
                    {% if form.month.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.month.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.bg_group.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.bg_group.label }}
                    </label>
                    {{ form.bg_group }}
                    {% if form.bg_group.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.bg_group.errors }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button 
                    type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Generate Report
                </button>
                <button 
                    type="button" 
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300"
                    onclick="window.location.href='{% url 'dashboard' %}'"> {# Assuming a dashboard URL #}
                    Cancel
                </button>
            </div>
        </form>
    </div>

    <div id="salaryReportTableContainer"
         hx-trigger="loadReportTable from:body, load {% if initial_report_params_exist %}once{% else %}never{% endif %}"
         hx-get="{% url 'salary_report_table' %}"
         hx-swap="innerHTML">
        <!-- Report table will be loaded here via HTMX -->
        <div class="text-center py-10" id="initial-loading-spinner">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Please select parameters and generate the report...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'salaryReportTableContainer') {
            // Ensure DataTables is re-initialized after HTMX swap
            // Use jQuery's `destroy` method if table already exists, then re-initialize
            if ($.fn.DataTable.isDataTable('#salaryReportTable')) {
                $('#salaryReportTable').DataTable().destroy();
            }
            $('#salaryReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": 'lfrtip', // Show length changing, filtering, processing, table, info, pagination
            });
            // Hide initial loading spinner once table is loaded
            document.getElementById('initial-loading-spinner')?.remove();
        }
    });

    // Initial load check for the spinner removal if report params exist
    document.addEventListener('DOMContentLoaded', () => {
        const initialSpinner = document.getElementById('initial-loading-spinner');
        if (initialSpinner && htmx.find("#salaryReportTableContainer[hx-trigger*='load once']")) {
            // If the table container has 'load once', it means initial params exist
            // and the table will load automatically, so hide spinner until then.
        } else if (initialSpinner) {
            // If no initial report params, show the message "Please select parameters..."
            // which is part of the initial-loading-spinner div.
        }
    });
</script>
{% endblock %}
```

##### `_salary_table.html` (Partial for HTMX rendering)

```html
<div class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
    <h3 class="text-xl font-semibold text-gray-700 mb-4">Generated Salary Report</h3>
    
    {% if salary_records %}
    <table id="salaryReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Basic (Cal)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">DA (Cal)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">HRA (Cal)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Conveyance (Cal)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Education (Cal)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Medical (Cal)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Total (Cal)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PF (Emp)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">P. Tax</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Loan Install.</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile Bill</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Misc. Deduct.</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Deduct.</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Net Pay (Final)</th>
                <!-- Add more columns as per the C# DataTable definition -->
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ex-Gratia</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Misc. Add.</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Additions</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Working Days</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Present Days</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Absent Days</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Sunday</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Holiday</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Late In</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">COFF</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Half Days</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">PL</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LWP</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">PF No.</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">PAN No.</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Bank A/C No.</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for record in salary_records %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.emp_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.employee_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.month }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.year }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.department }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.designation }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.status }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ record.grade }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.cal_basic|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.cal_da|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.cal_hra|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.cal_conveyance|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.cal_education|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.cal_medical|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.cal_gross_total|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.pf_of_employee|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.p_tax|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.personal_loan_install|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.mobile_bill|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.miscellaneous_deduct|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap font-semibold">{{ record.total_deductions|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap font-bold text-green-700">{{ record.net_pay_final|floatformat:2 }}</td>
                <!-- Add more cells matching the column headers -->
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.ex_gratia|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap">{{ record.miscellaneous_add|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right whitespace-nowrap font-semibold">{{ record.total_additions|floatformat:2 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.working_days_cal|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.present_days|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.absent_days|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.sunday_taken|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.holiday_taken|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.late_in|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.coff|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.half_days|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.pl|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.lwp|floatformat:0 }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.pf_no }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.pan_no }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.bank_account_no }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap">{{ record.date }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
        <p class="text-center text-gray-600 py-10">No salary data found for the selected parameters. Please check your selections.</p>
    {% endif %}
</div>
```

#### 4.5 URLs (`hr_reports/urls.py`)

**Task:** Define URL patterns for the report input and the HTMX table.

```python
from django.urls import path
from .views import SalaryReportInputView, SalaryReportTableView

urlpatterns = [
    path('salary-report/', SalaryReportInputView.as_view(), name='salary_report_input'),
    path('salary-report/table/', SalaryReportTableView.as_view(), name='salary_report_table'),
]
```

#### 4.6 Tests (`hr_reports/tests.py`)

**Task:** Write comprehensive unit tests for the models and the `SalaryReportGenerator` service, and integration tests for the views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
import calendar
from unittest.mock import patch, MagicMock

# Import all models and the service
from .models import (
    FinancialYear, Department, Designation, Grade, EmployeeType, OTHour, DutyHour,
    OfferMaster, IncrementMaster, OfficeStaff, SalaryMaster, SalaryDetail,
    OfferAccessory, IncrementAccessory
)
from .services import SalaryReportGenerator

class ModelIntegrityTest(TestCase):
    """
    Tests for basic model creation and relationships, ensuring db_table and managed=False.
    """
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for testing relationships
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2024, fin_year='2023-2024', company_id=1)
        cls.dept = Department.objects.create(id=1, description='HR', symbol='HRD')
        cls.desig = Designation.objects.create(id=1, type='Manager', symbol='MGR')
        cls.grade = Grade.objects.create(id=1, symbol='A')
        cls.emp_type = EmployeeType.objects.create(id=1, description='Permanent')
        cls.ot_hour = OTHour.objects.create(id=1, hours=8.0)
        cls.duty_hour = DutyHour.objects.create(id=1, hours=40.0)
        
        cls.offer = OfferMaster.objects.create(
            offer_id=1, staff_type=cls.emp_type, type_of=1, salary=50000.0,
            duty_hrs=cls.duty_hour, ot_hrs=cls.ot_hour, over_time=2, designation=cls.desig,
            ex_gratia=500.0, vehicle_allowance=1000.0, att_bonus_per1=5, att_bonus_per2=10,
            pf_employee=12.0, pf_company=13.0, increment=1
        )
        cls.increment_offer = IncrementMaster.objects.create(
            id=1, offer=cls.offer, increment=2, salary=60000.0,
            staff_type=cls.emp_type, type_of=1, duty_hrs=cls.duty_hour, ot_hrs=cls.ot_hour,
            over_time=2, designation=cls.desig, ex_gratia=700.0, vehicle_allowance=1200.0,
            att_bonus_per1=6, att_bonus_per2=11, pf_employee=12.0, pf_company=13.0
        )
        
        cls.employee = OfficeStaff.objects.create(
            emp_id='EMP001', user_id='U001', company_id=1, offer=cls.offer, fin_year_id=2024,
            title='Mr', employee_name='John Doe', swap_card_no='12345', department=cls.dept,
            bg_group=1, directors_name='Director A', dept_head='Head B', designation=cls.desig,
            grade=cls.grade, mobile_no='**********', bank_account_no='*********',
            pf_no='PF123', pan_no='PAN123'
        )
        cls.salary_master = SalaryMaster.objects.create(
            id=1, emp=cls.employee, fmonth=4, fin_year=cls.fin_year, company_id=1, increment=1
        )
        cls.salary_detail = SalaryDetail.objects.create(
            master=cls.salary_master, present=20.0, absent=2.0, late_in=0.5, half_day=1.0,
            sunday=4.0, coff=0.0, pl=1.0, over_time_hrs=5.0, installment=1000.0, mobile_exe_amt=200.0,
            addition=50.0, deduction=100.0
        )
        
        OfferAccessory.objects.create(id=1, master=cls.offer, qty=1, amount=500, includes_in='2') # Take Home
        IncrementAccessory.objects.create(id=2, master=cls.increment_offer, qty=1, amount=600, includes_in='3') # Both


    def test_model_db_table_and_managed(self):
        self.assertEqual(FinancialYear._meta.db_table, 'tblFinancial_master')
        self.assertFalse(FinancialYear._meta.managed)
        self.assertEqual(OfficeStaff._meta.db_table, 'tblHR_OfficeStaff')
        self.assertFalse(OfficeStaff._meta.managed)
        # Add checks for all other models

    def test_model_creation(self):
        self.assertIsNotNone(self.salary_master.id)
        self.assertEqual(self.salary_master.emp.employee_name, 'John Doe')
        self.assertEqual(self.salary_detail.master.id, self.salary_master.id)
        self.assertEqual(self.offer.salary, 50000.0)
        self.assertEqual(self.increment_offer.salary, 60000.0)

    def test_related_objects_access(self):
        self.assertEqual(self.salary_master.emp.department.description, 'HR')
        self.assertEqual(self.salary_master.fin_year.fin_year, '2023-2024')
        self.assertEqual(self.employee.offer.staff_type.description, 'Permanent')

class SalaryReportGeneratorTest(TestCase):
    """
    Unit tests for the SalaryReportGenerator service, focusing on calculations.
    We'll mock database calls where complex data is involved.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup data similar to ModelIntegrityTest, but directly using objects
        cls.company_id = 1
        cls.financial_year_id = 2024
        cls.month_id = 4 # April
        cls.bg_group_id = 1 # Office Staff

        cls.fin_year = FinancialYear.objects.create(fin_year_id=cls.financial_year_id, fin_year='2023-2024', company_id=cls.company_id)
        cls.dept = Department.objects.create(id=1, description='HR', symbol='HRD')
        cls.desig = Designation.objects.create(id=1, type='Manager', symbol='MGR')
        cls.grade = Grade.objects.create(id=1, symbol='A')
        cls.emp_type = EmployeeType.objects.create(id=1, description='Permanent')
        cls.ot_hour = OTHour.objects.create(id=1, hours=8.0)
        cls.duty_hour = DutyHour.objects.create(id=1, hours=40.0)
        
        cls.offer = OfferMaster.objects.create(
            offer_id=1, staff_type=cls.emp_type, type_of=1, salary=50000.0,
            duty_hrs=cls.duty_hour, ot_hrs=cls.ot_hour, over_time=2, designation=cls.desig,
            ex_gratia=500.0, vehicle_allowance=1000.0, att_bonus_per1=5, att_bonus_per2=10,
            pf_employee=12.0, pf_company=13.0, increment=1
        )
        cls.employee = OfficeStaff.objects.create(
            emp_id='EMP001', user_id='U001', company_id=cls.company_id, offer=cls.offer, fin_year_id=cls.financial_year_id,
            title='Mr', employee_name='John Doe', swap_card_no='12345', department=cls.dept,
            bg_group=cls.bg_group_id, directors_name='Director A', dept_head='Head B', designation=cls.desig,
            grade=cls.grade, mobile_no='**********', bank_account_no='*********',
            pf_no='PF123', pan_no='PAN123'
        )
        cls.salary_master = SalaryMaster.objects.create(
            id=1, emp=cls.employee, fmonth=cls.month_id, fin_year=cls.fin_year, company_id=cls.company_id, increment=1
        )
        cls.salary_detail = SalaryDetail.objects.create(
            master=cls.salary_master, present=20.0, absent=2.0, late_in=0.5, half_day=1.0,
            sunday=4.0, coff=0.0, pl=1.0, over_time_hrs=5.0, installment=1000.0, mobile_exe_amt=200.0,
            addition=50.0, deduction=100.0
        )
        OfferAccessory.objects.create(id=1, master=cls.offer, qty=1, amount=500, includes_in='2') # Take Home

    def setUp(self):
        # Each test gets a fresh generator instance
        self.generator = SalaryReportGenerator(
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            month_id=self.month_id,
            bg_group_id=self.bg_group_id
        )
        # Patch external dependencies like holiday count if needed for specific tests
        patch('hr_reports.services.SalaryReportGenerator._get_holiday_count', return_value=2).start()
        self.addCleanup(patch.stopall)

    def test_get_calendar_year(self):
        self.assertEqual(self.generator._get_calendar_year(), 2023) # April 2023 for 2023-2024 fin year

    def test_count_sundays(self):
        # April 2023 has 4 Sundays
        self.assertEqual(self.generator._count_sundays(), 4)

    def test_calculate_offer_component(self):
        gross = 10000.0
        # These values depend on the placeholder logic in services.py
        self.assertEqual(self.generator._calculate_offer_component(gross, 1, 1, 1), 4000.0) # Basic
        self.assertEqual(self.generator._calculate_offer_component(gross, 3, 1, 1), 1500.0) # HRA

    def test_calculate_pf(self):
        self.assertEqual(self.generator._calculate_pf(10000.0, 1, 12.0), 1200.0)
        self.assertEqual(self.generator._calculate_pf(10000.0, 1, 0.0), 0.0)

    def test_generate_report_data_basic(self):
        # Test the full report generation for one employee
        report_data = self.generator.generate_report_data()
        self.assertEqual(len(report_data), 1)
        record = report_data[0]

        self.assertEqual(record['emp_id'], 'EMP001')
        self.assertAlmostEqual(record['cal_basic'], 4000.0 * (30 - (2 - (1 + 0))) / 30, 2) # April has 30 days
        self.assertAlmostEqual(record['total_deductions'], 1200.0 + 200.0 + 1000.0 + 200.0 + 100.0, 2) # PF + PTax + Loan + Mobile + MiscDeduct
        self.assertAlmostEqual(record['net_pay_final'], record['total_additions'] - record['total_deductions'], 2)

    def test_generate_report_data_no_salary_detail(self):
        # Create a salary master without detail
        SalaryMaster.objects.create(
            id=2, emp=OfficeStaff.objects.create(
                emp_id='EMP002', user_id='U002', company_id=self.company_id, offer=self.offer, fin_year_id=self.financial_year_id,
                title='Ms', employee_name='Jane Doe', swap_card_no='54321', department=self.dept,
                bg_group=self.bg_group_id, directors_name='Director B', dept_head='Head C', designation=self.desig,
                grade=self.grade, mobile_no='**********', bank_account_no='*********',
                pf_no='PF456', pan_no='PAN456'
            ), fmonth=self.month_id, fin_year=self.fin_year, company_id=self.company_id, increment=1
        )
        report_data = self.generator.generate_report_data()
        self.assertEqual(len(report_data), 1) # Still 1, as the new one is skipped due to missing detail

    def test_attendance_bonus_calculation(self):
        # Mocking attendance_bonus_days calculation for specific scenarios
        # Case 1: AttBonusType = 1 (threshold1_lower <= days < threshold1_upper)
        april_days = 30
        sunday_in_april = 4
        holiday_in_april = 2
        threshold1_lower = april_days - (holiday_in_april + sunday_in_april + 2) # 30 - (2+4+2) = 22
        threshold1_upper = (april_days + 2) - (holiday_in_april + sunday_in_april) # 32 - (2+4) = 26

        # Test case: days = 24 (between 22 and 26)
        mock_salary_detail = MagicMock(spec=SalaryDetail)
        mock_salary_detail.present = 20.0
        mock_salary_detail.sunday = 4.0
        mock_salary_detail.half_day = 0.0
        mock_salary_detail.over_time_hrs = 0.0 # Not relevant for bonus
        mock_salary_detail.installment = 0.0
        mock_salary_detail.mobile_exe_amt = 0.0
        mock_salary_detail.addition = 0.0
        mock_salary_detail.deduction = 0.0
        mock_salary_detail.absent = 0.0
        mock_salary_detail.pl = 0.0
        mock_salary_detail.coff = 0.0

        with patch('hr_reports.models.SalaryDetail.objects.get', return_value=mock_salary_detail):
            report_data = self.generator.generate_report_data()
            record = report_data[0]
            self.assertEqual(record['att_bonus_type'], 1)
            self.assertAlmostEqual(record['att_bonus_amount'], 50000.0 * 0.05, 2) # 5% of gross

        # Case 2: AttBonusType = 2 (days >= threshold1_upper)
        mock_salary_detail.present = 22.0 # 22 + 4 = 26
        with patch('hr_reports.models.SalaryDetail.objects.get', return_value=mock_salary_detail):
            report_data = self.generator.generate_report_data()
            record = report_data[0]
            self.assertEqual(record['att_bonus_type'], 2)
            self.assertAlmostEqual(record['att_bonus_amount'], 50000.0 * 0.10, 2) # 10% of gross

class SalaryReportViewsTest(TestCase):
    """
    Integration tests for the Django views.
    """
    @classmethod
    def setUpTestData(cls):
        # Minimal data for views to operate without errors
        cls.company_id = 1
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2024, fin_year='2023-2024', company_id=cls.company_id)
        cls.dept = Department.objects.create(id=1, description='HR', symbol='HRD')
        cls.desig = Designation.objects.create(id=1, type='Manager', symbol='MGR')
        cls.grade = Grade.objects.create(id=1, symbol='A')
        cls.emp_type = EmployeeType.objects.create(id=1, description='Permanent')
        cls.ot_hour = OTHour.objects.create(id=1, hours=8.0)
        cls.duty_hour = DutyHour.objects.create(id=1, hours=40.0)
        cls.offer = OfferMaster.objects.create(
            offer_id=1, staff_type=cls.emp_type, type_of=1, salary=50000.0,
            duty_hrs=cls.duty_hour, ot_hrs=cls.ot_hour, over_time=2, designation=cls.desig,
            ex_gratia=500.0, vehicle_allowance=1000.0, att_bonus_per1=5, att_bonus_per2=10,
            pf_employee=12.0, pf_company=13.0, increment=1
        )
        cls.employee = OfficeStaff.objects.create(
            emp_id='EMP001', user_id='U001', company_id=cls.company_id, offer=cls.offer, fin_year_id=2024,
            title='Mr', employee_name='John Doe', swap_card_no='12345', department=cls.dept,
            bg_group=1, directors_name='Director A', dept_head='Head B', designation=cls.desig,
            grade=cls.grade, mobile_no='**********', bank_account_no='*********',
            pf_no='PF123', pan_no='PAN123'
        )
        cls.salary_master = SalaryMaster.objects.create(
            id=1, emp=cls.employee, fmonth=4, fin_year=cls.fin_year, company_id=cls.company_id, increment=1
        )
        cls.salary_detail = SalaryDetail.objects.create(
            master=cls.salary_master, present=20.0, absent=2.0, late_in=0.5, half_day=1.0,
            sunday=4.0, coff=0.0, pl=1.0, over_time_hrs=5.0, installment=1000.0, mobile_exe_amt=200.0,
            addition=50.0, deduction=100.0
        )
        OfferAccessory.objects.create(id=1, master=cls.offer, qty=1, amount=500, includes_in='2')


    def setUp(self):
        self.client = Client()
        # Patch the SalaryReportGenerator to avoid complex calculations during view tests,
        # unless specifically testing the generator through the view.
        # For simplicity, we'll return a minimal valid structure.
        mock_report_data = [{
            'emp_id': 'EMP001', 'employee_name': 'Mr.John Doe', 'month': 'April', 'year': '2023',
            'department': 'HR [HRD]', 'designation': 'Manager [MGR]', 'status': 'SAPL - Permanent', 'grade': 'A',
            'cal_basic': 25000.00, 'cal_da': 10000.00, 'cal_hra': 7500.00, 'cal_conveyance': 1000.00,
            'cal_education': 500.00, 'cal_medical': 750.00, 'cal_gross_total': 44750.00,
            'pf_of_employee': 5370.00, 'p_tax': 200.00, 'personal_loan_install': 1000.00,
            'mobile_bill': 200.00, 'miscellaneous_deduct': 100.00, 'total_deductions': 6870.00,
            'net_pay_final': 38000.00, 'ex_gratia': 500.00, 'miscellaneous_add': 1050.00,
            'total_additions': 46300.00,
            'working_days_cal': 24.0, 'present_days': 20.0, 'absent_days': 2.0, 'sunday_taken': 4.0,
            'holiday_taken': 2.0, 'late_in': 0.5, 'coff': 0.0, 'half_days': 1.0, 'pl': 1.0, 'lwp': 0.0,
            'pf_no': 'PF123', 'pan_no': 'PAN123', 'bank_account_no': '*********', 'date': '01-01-2024'
        }]
        # Ensure the patch is for the instance in the view's context
        patch('hr_reports.services.SalaryReportGenerator.generate_report_data', return_value=mock_report_data).start()
        patch('hr_reports.services.SalaryReportGenerator.__init__', return_value=None).start() # Mock __init__ to avoid DB calls

        self.addCleanup(patch.stopall) # Stop all patches after each test

    def test_report_input_view_get(self):
        response = self.client.get(reverse('salary_report_input'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salary_report/report_input.html')
        self.assertIsInstance(response.context['form'], SalaryReportForm)
        self.assertFalse(response.context.get('initial_report_params_exist'))

    def test_report_input_view_post_htmx(self):
        data = {
            'company_id': self.company_id,
            'financial_year': self.fin_year.fin_year_id,
            'month': 4,
            'bg_group': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('salary_report_input'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX POST success
        self.assertEqual(response.headers['HX-Trigger'], 'loadReportTable')
        self.assertIn('report_params', self.client.session)
        self.assertEqual(self.client.session['report_params']['month_id'], 4)

    def test_report_table_view_htmx(self):
        # First, simulate a successful form submission to populate session
        self.client.session['report_params'] = {
            'company_id': self.company_id,
            'financial_year_id': self.fin_year.fin_year_id,
            'month_id': 4,
            'bg_group_id': 1
        }
        self.client.session.save()

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('salary_report_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salary_report/_salary_table.html')
        self.assertIn('salary_records', response.context)
        self.assertEqual(len(response.context['salary_records']), 1)
        self.assertContains(response, 'Mr.John Doe')
        self.assertContains(response, '44750.00') # Calculated Gross Total from mock data

    def test_report_table_view_no_session_params(self):
        # Simulate accessing table view directly without prior form submission
        response = self.client.get(reverse('salary_report_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salary_report/_salary_table.html')
        self.assertIn('salary_records', response.context)
        self.assertEqual(len(response.context['salary_records']), 0)
        self.assertContains(response, 'No salary data found')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates and views are designed for a seamless HTMX and Alpine.js experience:

-   **HTMX for Dynamic Updates:**
    -   The `report_input.html` uses `hx-post` on the form to submit parameters without a full page reload.
    -   Upon successful form submission (204 No Content response from `SalaryReportInputView`), an `HX-Trigger: loadReportTable` event is sent.
    -   The `salaryReportTableContainer` `div` in `report_input.html` is configured with `hx-trigger="loadReportTable from:body"` to listen for this event and then perform an `hx-get` request to `{% url 'salary_report_table' %}`. This fetches the `_salary_table.html` partial and swaps its content into the container, dynamically loading the report.
    -   The `hx-trigger="load once"` on `salaryReportTableContainer` allows the table to be loaded automatically if initial report parameters are detected in the session (e.g., if a user navigates back to the page).
-   **DataTables for List Views:**
    -   The `_salary_table.html` partial contains a `<table id="salaryReportTable">`.
    -   A `<script>` block within this partial (which will be part of the `extra_js` block in `base.html` after HTMX swap) initializes DataTables on this table, providing client-side searching, sorting, and pagination. The `htmx:afterSwap` event listener in `report_input.html` ensures DataTables is properly re-initialized each time the table content is updated via HTMX.
-   **Alpine.js for UI State:**
    -   While not explicitly used for complex UI state in this module, Alpine.js remains available via `base.html`. For instance, if you wanted to toggle a filter panel or show/hide detailed rows, Alpine.js would be the perfect fit. In this simple report, HTMX handles the primary dynamic behavior.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Salary Report module to Django.

-   **AI-Assisted Automation:** The structured approach with clearly defined inputs, outputs, and transformations for each step is designed for automation. AI tools can analyze the original ASP.NET code, extract schema, identify business logic patterns, and then generate these Django components with high accuracy, significantly reducing manual coding.
-   **Business Value:** This modernization will lead to:
    -   **Improved Performance:** Django's optimized ORM and Python's efficiency, combined with HTMX, deliver a faster and more responsive user experience compared to legacy ASP.NET Web Forms.
    -   **Reduced Maintenance Costs:** A clean, modern Django codebase with clear separation of concerns (fat models, thin views) and comprehensive tests is easier to understand, debug, and maintain.
    -   **Enhanced Scalability:** Django's architecture is inherently more scalable, ready to handle growing data volumes and user loads.
    -   **Future-Proofing:** Moving away from proprietary Crystal Reports and legacy ASP.NET ensures your application is built on widely adopted, open-source technologies, mitigating vendor lock-in risks.
    -   **Better User Experience:** Interactive DataTables and HTMX-driven partial updates provide a smoother, more desktop-like feel without complex JavaScript frameworks.

By following these steps, you can systematically convert your existing ASP.NET application into a modern, robust Django solution, realizing significant operational and strategic benefits. Remember to adjust model field types and lengths precisely to match your actual database schema during the automated migration. The `SalaryReportGenerator` requires careful, step-by-step replication of all original `fun` methods and calculation logic to ensure exact output parity.