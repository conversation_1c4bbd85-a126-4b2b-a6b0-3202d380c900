## ASP.NET to Django Conversion Script: Tour Intimation Print

This document outlines a modernization plan to transition your legacy ASP.NET 'Tour Intimation Print' module to a robust, modern Django application. Our approach leverages AI-assisted automation, focusing on creating efficient, maintainable, and highly interactive user interfaces without extensive manual coding.

The core principle behind this migration is to empower your business by moving away from monolithic, difficult-to-change systems towards a flexible, component-based architecture. This transition will significantly reduce future development costs, improve system performance, and enhance user experience through modern web technologies.

### Business Value Proposition:

-   **Reduced Maintenance Costs:** Django's clean structure and Python's readability make code easier to understand and maintain.
-   **Improved Performance & Scalability:** Modern Django, coupled with HTMX, provides a snappy user experience akin to Single Page Applications (SPAs) but with simpler development. DataTables ensures efficient handling of large datasets on the client side.
-   **Enhanced User Experience:** Interactive search, filtering, and dynamic content updates without full page reloads lead to a more intuitive and responsive interface for your users.
-   **Future-Proofing:** Adopting a popular, actively developed framework like Django ensures your application remains relevant and extensible for years to come.
-   **Developer Efficiency:** The "fat model, thin view" pattern centralizes business logic, making development faster and less prone to errors.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   **NEVER** include `base.html` template code in your output - assume it already exists and is extended.
-   Focus **ONLY** on component-specific code for the current module.
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

## AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `binddata` function and SQL statements, we identify the primary table and its related lookups.

**Primary Table:** `tblACC_TourIntimation_Master`

**Inferred Columns:**
-   `Id` (Primary Key, integer)
-   `CompId` (Integer, likely Foreign Key to Company)
-   `FinYearId` (Integer, likely Foreign Key to Financial Year)
-   `EmpId` (String, likely Foreign Key to Employee/Office Staff, but stored as string)
-   `TINo` (String)
-   `WONo` (String)
-   `BGGroupId` (Integer, likely Foreign Key to Business Group)
-   `ProjectName` (String)
-   `PlaceOfTourCity` (Integer, likely Foreign Key to City)
-   `PlaceOfTourState` (Integer, likely Foreign Key to State)
-   `PlaceOfTourCountry` (Integer, likely Foreign Key to Country)
-   `TourStartDate` (Date/DateTime)
-   `TourEndDate` (Date/DateTime)

**Lookup Tables (Implied Models):**
-   `tblFinancial_master` (for `FinYear`)
-   `tblHR_OfficeStaff` (for `EmpName`)
-   `BusinessGroup` (for `BGGroup`)
-   `tblCity` (for `CityName`)
-   `tblState` (for `StateName`)
-   `tblCountry` (for `CountryName`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core logic in the ASP.NET code.

**Instructions:**
The primary functionality is **Reading** (displaying) and **Filtering/Searching** "Tour Intimation" records.

-   **Read/Display:** The `GridView2` populates a list of tour intimations.
-   **Search/Filter:**
    -   Users can select a search criterion (TI No, Employee Name, WO No, BG Group, Project Name) using a `DropDownList` (`DrpField`).
    -   Input fields (`TxtMrs`, `TxtEmpName`, `drpGroup`) dynamically appear/hide based on the selected criterion.
    -   The `Button1_Click` event triggers the `binddata` function to re-fetch and display filtered results.
    -   Employee Name field (`TxtEmpName`) uses an `AutoCompleteExtender` for predictive search.
-   **Pagination:** The `GridView2_PageIndexChanging` event handles pagination for the displayed results.
-   **Navigation to Details:** Clicking on a "TI No" in the `GridView` (`LinkButton1`) redirects to `TourIntimation_Print_Details.aspx?Id=...`, indicating a separate detail view for each record.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Mapping ASP.NET controls to modern Django UI components:

-   `DrpField` (DropDownList): Will be a Django `forms.ChoiceField` in a dedicated search form.
-   `TxtMrs` (TextBox): Will be a Django `forms.CharField` within the search form.
-   `TxtEmpName` (TextBox) with `AutoCompleteExtender`: Will be a Django `forms.CharField` enhanced with HTMX for real-time autocomplete suggestions, hitting a dedicated Django API endpoint.
-   `drpGroup` (DropDownList): Will be a Django `forms.ModelChoiceField` for the `BusinessGroup` model.
-   `Button1` (Search Button): Will trigger an HTMX form submission to refresh the DataTables content.
-   `GridView2`: Will be replaced by a DataTables-powered HTML table, dynamically loaded and updated via HTMX.
-   `LinkButton1` (inside GridView for TI No): Will be a standard Django `<a>` tag or a button that navigates to the detail page (or potentially opens a detail modal via HTMX if desired for a more SPA-like feel). Given the original `Response.Redirect`, a direct link is a faithful translation.

### Step 4: Generate Django Code

We will structure the Django application under a hypothetical `hr` app (Human Resources), as inferred from `Module_HR_TourIntimation_Print`.

#### 4.1 Models (`hr/models.py`)

**Task:** Create Django models based on the database schema, including relationships and business logic as properties.

**Instructions:**
We'll define `TourIntimation` mapping directly to `tblACC_TourIntimation_Master`. For related tables, we'll create proxy models with `managed=False` as well, assuming they are managed by the legacy system but need to be accessible for joins and lookups in Django. This adheres to the "fat model" approach by embedding data retrieval and formatting logic within the model.

```python
from django.db import models
from django.db.models.functions import Cast
from django.db.models import CharField

# Assume these models exist and are managed by the legacy system,
# or are placeholders for your actual database schema.
# They are included here to demonstrate relationships.

class Company(models.Model):
    # Example fields, adjust based on your actual tblCompany schema
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCompany' # Replace with actual table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year = models.CharField(db_column='FinYear', max_length=10) # Assuming 'FinYear' is like '2023-24'

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year

class Employee(models.Model):
    # Mapping to tblHR_OfficeStaff
    id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # EmpId is string in ASP.NET
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    name = models.CharField(db_column='EmployeeName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='employees', null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='employees', null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.name} [{self.id}]".strip()

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

# Main Model
class TourIntimation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId') # Store ID directly if FinYear is not a real FK
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='id') # EmpId is string
    ti_no = models.CharField(db_column='TINo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroupId', blank=True, null=True)
    project_name = models.CharField(db_column='ProjectName', max_length=255, blank=True, null=True)
    place_of_tour_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCity', blank=True, null=True)
    place_of_tour_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='PlaceOfTourState', blank=True, null=True)
    place_of_tour_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCountry', blank=True, null=True)
    tour_start_date = models.DateField(db_column='TourStartDate')
    tour_end_date = models.DateField(db_column='TourEndDate')

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation'
        verbose_name_plural = 'Tour Intimations'

    def __str__(self):
        return self.ti_no

    @property
    def financial_year_display(self):
        """Fetches the financial year symbol based on FinYearId."""
        try:
            # Assuming FinancialYear model also maps to tblFinancial_master
            # and has a 'year' field for 'FinYear' column
            return FinancialYear.objects.get(id=self.financial_year_id).year
        except FinancialYear.DoesNotExist:
            return "N/A"

    @property
    def business_group_display(self):
        """Fetches the business group symbol."""
        if self.business_group:
            return self.business_group.symbol
        return "NA" # As per ASP.NET logic

    @property
    def work_order_or_na(self):
        """Returns WO No or 'NA' based on BGGroupId logic in ASP.NET."""
        # ASP.NET logic: if BGGroupId is '1', WO No is used, else 'NA'
        # Assuming BGGroupId '1' corresponds to a specific BusinessGroup
        # Adjust logic if '1' is a magic number for 'no group' or similar
        if self.business_group and self.business_group.id == 1: # Assuming '1' means 'No Group' or specific WO scenario
            return self.wo_no
        return "NA"

    @property
    def full_place_of_tour(self):
        """Combines City, State, Country into a single string."""
        parts = []
        if self.place_of_tour_country:
            parts.append(self.place_of_tour_country.name)
        if self.place_of_tour_state:
            parts.append(self.place_of_tour_state.name)
        if self.place_of_tour_city:
            parts.append(self.place_of_tour_city.name)
        return ", ".join(parts) if parts else "N/A"

    def get_absolute_url(self):
        """Returns the URL to access a particular instance of TourIntimation."""
        from django.urls import reverse
        return reverse('tourintimation_detail', args=[str(self.id)])

```

#### 4.2 Forms (`hr/forms.py`)

**Task:** Define a Django form for user input and search criteria.

**Instructions:**
We need a dedicated form for the search functionality, mirroring the `DrpField`, `TxtMrs`, `TxtEmpName`, and `drpGroup` controls. This form will not be a `ModelForm` but a regular `forms.Form` since it's for filtering, not directly creating/updating a model instance.

```python
from django import forms
from .models import TourIntimation, BusinessGroup, Employee

class TourIntimationSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('0', 'TI No'),
        ('1', 'Employee Name'),
        ('2', 'WO No'),
        ('3', 'BG Group'),
        ('4', 'Project Name'),
    ]

    drp_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/hr/tourintimation/search_form_fields/', # HTMX endpoint to dynamically update fields
            'hx-target': '#search-fields-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )

    # These fields will be toggled visibility via Alpine.js based on drp_field selection
    txt_mrs = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter value...',
        })
    )

    txt_emp_name = forms.CharField(
        max_length=255,
        required=False,
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-get': '/hr/employees/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
        })
    )

    drp_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False,
        label="Business Group",
        empty_label="Select Business Group",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial visibility based on default or loaded data
        # For simplicity in this initial rendering, all might be visible,
        # Alpine.js/HTMX will handle dynamic display.
        # This part will be mostly managed by the HTMX partial response for form fields.
        if 'drp_field' in self.data and self.data['drp_field'] != 'Select':
            selected_field = self.data['drp_field']
            if selected_field == '1': # Employee Name
                self.fields['txt_mrs'].widget.attrs['style'] = 'display:none;'
                self.fields['drp_group'].widget.attrs['style'] = 'display:none;'
            elif selected_field == '3': # BG Group
                self.fields['txt_mrs'].widget.attrs['style'] = 'display:none;'
                self.fields['txt_emp_name'].widget.attrs['style'] = 'display:none;'
            elif selected_field in ['0', '2', '4']: # TI No, WO No, Project Name
                self.fields['txt_emp_name'].widget.attrs['style'] = 'display:none;'
                self.fields['drp_group'].widget.attrs['style'] = 'display:none;'
        else: # Default 'Select' or no selection
            self.fields['txt_emp_name'].widget.attrs['style'] = 'display:none;'
            self.fields['drp_group'].widget.attrs['style'] = 'display:none;'
            # For 'Select' default, ASP.NET showed TxtMrs.
            # We'll make it visible, and Alpine.js/HTMX will manage on dropdown change.

# Generic ModelForm (placeholder, not strictly from ASP.NET code for *this* page)
class TourIntimationForm(forms.ModelForm):
    class Meta:
        model = TourIntimation
        fields = [
            'company', 'financial_year_id', 'employee', 'ti_no', 'wo_no',
            'business_group', 'project_name', 'place_of_tour_city',
            'place_of_tour_state', 'place_of_tour_country',
            'tour_start_date', 'tour_end_date'
        ]
        widgets = {
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ti_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'business_group': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tour_start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'tour_end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
```

#### 4.3 Views (`hr/views.py`)

**Task:** Implement Read and Search operations using Class-Based Views (CBVs), ensuring views are thin and business logic resides in models. Also, add the autocomplete and form field update views.

**Instructions:**
We'll create a main `ListView` to render the page, a `TemplateView` to handle the dynamic search form fields, a `ListView` for the DataTables content, and a `DetailView` for the individual record.

```python
from django.views.generic import ListView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q # For complex queries
from .models import TourIntimation, Employee, BusinessGroup
from .forms import TourIntimationSearchForm, TourIntimationForm # Placeholder for CRUD forms

class TourIntimationListView(TemplateView):
    """
    Renders the main Tour Intimation Print page with the search form and a container for the DataTable.
    """
    template_name = 'hr/tourintimation/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TourIntimationSearchForm()
        # Initialize form fields based on default selection ("Select" -> TxtMrs visible)
        initial_form_data = {'drp_field': 'Select'}
        context['search_form_fields'] = TourIntimationSearchForm(initial=initial_form_data)
        return context

class TourIntimationTablePartialView(ListView):
    """
    Returns the HTML for the DataTable, filtered based on search parameters.
    This view is specifically designed to be loaded via HTMX.
    """
    model = TourIntimation
    template_name = 'hr/tourintimation/_tourintimation_table.html'
    context_object_name = 'tour_intimations'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-id') # Order by Id Desc as in ASP.NET

        # Apply filtering based on GET parameters (from search form submission)
        search_field = self.request.GET.get('drp_field', 'Select')
        txt_mrs = self.request.GET.get('txt_mrs', '').strip()
        txt_emp_name = self.request.GET.get('txt_emp_name', '').strip()
        drp_group_id = self.request.GET.get('drp_group')

        # Implement ASP.NET search logic
        if search_field == '0' and txt_mrs: # TI No
            queryset = queryset.filter(ti_no__iexact=txt_mrs)
        elif search_field == '1' and txt_emp_name: # Employee Name
            try:
                # Extract EmpId from "EmployeeName [EmpId]" format
                # The ASP.NET fun.getCode(TxtEmpName.Text) suggests a lookup by formatted string
                emp_id_start = txt_emp_name.rfind('[')
                emp_id_end = txt_emp_name.rfind(']')
                if emp_id_start != -1 and emp_id_end != -1:
                    emp_id = txt_emp_name[emp_id_start + 1:emp_id_end].strip()
                    queryset = queryset.filter(employee__id=emp_id)
                else: # Fallback if format is not strict or only name is entered
                    queryset = queryset.filter(employee__name__icontains=txt_emp_name)
            except Employee.DoesNotExist:
                queryset = queryset.none() # No results if employee not found
        elif search_field == '2' and txt_mrs: # WO No
            queryset = queryset.filter(wo_no__iexact=txt_mrs)
        elif search_field == '3' and drp_group_id: # BG Group
            queryset = queryset.filter(business_group__id=drp_group_id)
        elif search_field == '4' and txt_mrs: # Project Name
            queryset = queryset.filter(project_name__icontains=txt_mrs)
        
        # ASP.NET also filtered by CompId and FinYearId from session.
        # Assuming these are available from request context or user session in Django.
        # For demonstration, hardcoding or getting from assumed request.user.profile
        # comp_id = self.request.session.get('compid')
        # fy_id = self.request.session.get('finyear')
        # if comp_id:
        #     queryset = queryset.filter(company_id=comp_id)
        # if fy_id:
        #     queryset = queryset.filter(financial_year_id__lte=fy_id) # 'FinYearId<=' as in ASP.NET

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # DataTables uses its own pagination, so Django's default pagination context
        # might not be directly used for rendering the table but is useful for `get_queryset`
        return context

class TourIntimationDetailView(DetailView):
    """
    Displays the details of a specific Tour Intimation record.
    Corresponds to TourIntimation_Print_Details.aspx.
    """
    model = TourIntimation
    template_name = 'hr/tourintimation/detail.html'
    context_object_name = 'tour_intimation'

    # Example of moving complex logic to model
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # Ensure related objects are loaded or properties computed
        # e.g., obj.financial_year_display will trigger a lookup
        return obj

class EmployeeAutocompleteView(TemplateView):
    """
    Provides employee names for autocomplete functionality via HTMX.
    Corresponds to GetCompletionList web method.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefix_text', '')
        # Assuming CompId from session as in ASP.NET
        # comp_id = request.session.get('compid')
        # For now, fetching all relevant employees
        
        employees = Employee.objects.filter(
            name__icontains=prefix_text
            # Add company filtering if needed: , company_id=comp_id
        ).values_list('name', 'id')[:10] # Limit results for performance

        results = [f"{name} [{emp_id}]" for name, emp_id in employees]
        
        # HTMX expects a list of options for a <datalist> or similar HTML structure.
        # For generic autocomplete, return as plain text lines or simple JSON.
        # If targeting a <datalist>, render a partial template.
        
        # For simplicity, returning a simple list of lines as in ASP.NET example
        response_content = "\n".join(results)
        return HttpResponse(response_content, content_type='text/plain')

class TourIntimationSearchFormFieldsView(TemplateView):
    """
    Renders only the dynamic search form fields based on the selected `drp_field` choice.
    Used by HTMX to swap content in `#search-fields-container`.
    """
    template_name = 'hr/tourintimation/_search_form_fields.html'

    def post(self, request, *args, **kwargs):
        form = TourIntimationSearchForm(request.POST)
        selected_field = request.POST.get('drp_field', 'Select')
        context = {'form': form, 'selected_field': selected_field}
        return render(request, self.template_name, context)

# --- Generic CRUD Views (Included as per template, not strictly from original ASP.NET code-behind for this page) ---
# These would typically be for a Tour Intimation "management" page, not just "print".

from django.views.generic import CreateView, UpdateView, DeleteView

class TourIntimationCreateView(CreateView):
    model = TourIntimation
    form_class = TourIntimationForm
    template_name = 'hr/tourintimation/_form.html'
    success_url = reverse_lazy('tourintimation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Tour Intimation added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return response

class TourIntimationUpdateView(UpdateView):
    model = TourIntimation
    form_class = TourIntimationForm
    template_name = 'hr/tourintimation/_form.html'
    success_url = reverse_lazy('tourintimation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Tour Intimation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return response

class TourIntimationDeleteView(DeleteView):
    model = TourIntimation
    template_name = 'hr/tourintimation/_confirm_delete.html'
    success_url = reverse_lazy('tourintimation_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Tour Intimation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return response

```

#### 4.4 Templates (`hr/templates/hr/tourintimation/`)

**Task:** Create templates for each view, ensuring DRY principles, HTMX integration, and DataTables usage.

**Instructions:**
-   `list.html`: Main page for the Tour Intimation list and search.
-   `_search_form.html`: Partial template for the search form, including HTMX attributes.
-   `_search_form_fields.html`: Partial template to dynamically render the relevant search input fields.
-   `_tourintimation_table.html`: Partial template for the DataTables content.
-   `detail.html`: For displaying single record details.
-   `_form.html` and `_confirm_delete.html`: For the generic CRUD operations (if implemented).

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Tour Intimation Print</h2>
        {# Add New button if CRUD is active, otherwise omit #}
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'tourintimation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Tour Intimation
        </button>
    </div>

    {# Search Form Section #}
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <form id="tourIntimationSearchForm"
              hx-get="{% url 'tourintimation_table' %}"
              hx-target="#tourintimation-table-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_drp_group" {# Trigger on submit of form or change of drp_group #}
              class="space-y-4">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ search_form.drp_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.drp_field.label }}
                    </label>
                    {{ search_form.drp_field }}
                </div>
                <div id="search-fields-container" class="md:col-span-2">
                    {# Dynamic fields loaded here via HTMX #}
                    {% include "hr/tourintimation/_search_form_fields.html" with form=search_form_fields selected_field=search_form.drp_field.value %}
                </div>
                <div>
                    <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
            <div id="autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg"></div>
        </form>
    </div>

    {# DataTable Container #}
    <div id="tourintimation-table-container"
         hx-trigger="load, refreshTourIntimationList from:body"
         hx-get="{% url 'tourintimation_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Tour Intimations...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterOnLoad from #modalContent if event.detail.xhr.status != 204 add .is-active to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad from #modalContent if event.detail.xhr.status == 204 remove .is-active from #modal">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('tourIntimation', {
            drpField: 'Select', // Initial value for the dropdown
            init() {
                // Initial state management for search fields visibility if needed by Alpine
                // However, HTMX will largely handle this by swapping _search_form_fields.html
            },
            updateSearchFields: function(event) {
                this.drpField = event.target.value;
                // HTMX is handling the dynamic loading of _search_form_fields.html
                // No need for direct Alpine manipulation of display styles here
            }
        });
    });

    // Helper to select an autocomplete result and update the input field
    function selectEmployee(employeeText) {
        document.getElementById('id_txt_emp_name').value = employeeText;
        document.getElementById('autocomplete-results').innerHTML = ''; // Clear results
    }

    // Initialize DataTables after content is loaded via HTMX
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'tourintimation-table-container') {
            $('#tourintimationTable').DataTable({
                "pageLength": 20, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true, // Allows re-initialization if content is swapped
                "searching": true, // Enable default search box
                "ordering": true,  // Enable column ordering
                "paging": true     // Enable pagination
            });
        }
    });
</script>
{% endblock %}
```

**`_search_form.html` (Not used directly, but its structure is in `list.html`)**
The `list.html` already contains the full form, and `_search_form_fields.html` handles the dynamic parts. This partial is largely superseded by the inline form in `list.html` and the dynamic fields partial.

**`_search_form_fields.html`**

```html
{% comment %} This partial is swapped into #search-fields-container {% endcomment %}
<div class="flex items-end space-x-4">
    {% if selected_field == '0' or selected_field == '2' or selected_field == '4' or selected_field == 'Select' %}
    <div class="flex-grow">
        <label for="{{ form.txt_mrs.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Search Value
        </label>
        {{ form.txt_mrs }}
    </div>
    {% elif selected_field == '1' %}
    <div class="flex-grow relative">
        <label for="{{ form.txt_emp_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Employee Name
        </label>
        {{ form.txt_emp_name }}
        <div id="autocomplete-results" class="absolute bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full z-10 max-h-60 overflow-y-auto">
            {# Autocomplete results will be loaded here via HTMX #}
        </div>
    </div>
    {% elif selected_field == '3' %}
    <div class="flex-grow">
        <label for="{{ form.drp_group.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Business Group
        </label>
        {{ form.drp_group }}
    </div>
    {% endif %}
</div>
```

**`_tourintimation_table.html`**

```html
<table id="tourintimationTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TI No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place of Tour</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour Start Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour End Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in tour_intimations %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.financial_year_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a href="{{ obj.get_absolute_url }}" class="text-blue-600 hover:underline">
                    {{ obj.ti_no }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee }}</td> {# Assumes __str__ on Employee outputs formatted name #}
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.work_order_or_na }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.business_group_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.project_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.full_place_of_tour }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.tour_start_date|date:"d M Y" }}</td> {# Format date as in ASP.NET's FromDateDMY #}
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.tour_end_date|date:"d M Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md text-sm transition duration-300 ease-in-out mr-2"
                    hx-get="{% url 'tourintimation_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'tourintimation_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="11" class="py-4 text-center text-red-700 text-lg font-semibold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`detail.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Tour Intimation Details: {{ tour_intimation.ti_no }}</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm font-medium text-gray-600">TI No:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.ti_no }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Financial Year:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.financial_year_display }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Employee Name:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.employee }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">WO No:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.work_order_or_na }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Business Group:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.business_group_display }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Project Name:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.project_name }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Place of Tour:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.full_place_of_tour }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Tour Start Date:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.tour_start_date|date:"d M Y" }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Tour End Date:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.tour_end_date|date:"d M Y" }}</p>
            </div>
        </div>

        <div class="mt-8 flex justify-end">
            <a href="{% url 'tourintimation_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Back to List
            </a>
            <button
                class="ml-4 bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                hx-get="{% url 'tourintimation_edit' tour_intimation.pk %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Edit
            </button>
            <button
                class="ml-4 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                hx-get="{% url 'tourintimation_delete' tour_intimation.pk %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Delete
            </button>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterOnLoad from #modalContent if event.detail.xhr.status != 204 add .is-active to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad from #modalContent if event.detail.xhr.status == 204 remove .is-active from #modal">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}
```

**`_form.html` (For generic CRUD, loaded via HTMX modal)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Tour Intimation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-4">
        {% csrf_token %}

        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.errors %}
            <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_confirm_delete.html` (For generic CRUD, loaded via HTMX modal)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Tour Intimation **{{ tourintimation.ti_no }}**?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`hr/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
Setup paths for the main list page, the HTMX-loaded table, search form field updates, employee autocomplete, and the detail page. Also include the generic CRUD URLs.

```python
from django.urls import path
from .views import (
    TourIntimationListView,
    TourIntimationTablePartialView,
    TourIntimationDetailView,
    EmployeeAutocompleteView,
    TourIntimationSearchFormFieldsView,
    # CRUD Views
    TourIntimationCreateView,
    TourIntimationUpdateView,
    TourIntimationDeleteView,
)

urlpatterns = [
    # Main List View (initial page load)
    path('tourintimation/', TourIntimationListView.as_view(), name='tourintimation_list'),

    # HTMX endpoint for the table content (search results)
    path('tourintimation/table/', TourIntimationTablePartialView.as_view(), name='tourintimation_table'),

    # HTMX endpoint for dynamically updating search form fields
    path('tourintimation/search_form_fields/', TourIntimationSearchFormFieldsView.as_view(), name='tourintimation_search_form_fields'),

    # HTMX/API endpoint for employee autocomplete
    path('employees/autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Detail View (for LinkButton1 functionality)
    path('tourintimation/detail/<int:pk>/', TourIntimationDetailView.as_view(), name='tourintimation_detail'),

    # --- Generic CRUD URLs (Optional, as per template) ---
    path('tourintimation/add/', TourIntimationCreateView.as_view(), name='tourintimation_add'),
    path('tourintimation/edit/<int:pk>/', TourIntimationUpdateView.as_view(), name='tourintimation_edit'),
    path('tourintimation/delete/<int:pk>/', TourIntimationDeleteView.as_view(), name='tourintimation_delete'),
]

```

#### 4.6 Tests (`hr/tests.py`)

**Task:** Write comprehensive tests for the models and views to ensure functionality and data integrity.

**Instructions:**
Include unit tests for model properties/methods and integration tests for all view interactions, including HTMX requests.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    TourIntimation, Company, FinancialYear, Employee,
    BusinessGroup, City, State, Country
)
from datetime import date

class TourIntimationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for related models first
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=10, year='2023-24')
        cls.employee1 = Employee.objects.create(id='EMP001', title='Mr.', name='John Doe', company=cls.company, financial_year=cls.fin_year)
        cls.employee2 = Employee.objects.create(id='EMP002', title='Ms.', name='Jane Smith', company=cls.company, financial_year=cls.fin_year)
        cls.bg_group1 = BusinessGroup.objects.create(id=1, symbol='General') # Corresponds to WO No logic
        cls.bg_group2 = BusinessGroup.objects.create(id=2, symbol='Sales')
        cls.city = City.objects.create(id=101, name='New York')
        cls.state = State.objects.create(id=201, name='NY')
        cls.country = Country.objects.create(id=301, name='USA')

        # Create test data for TourIntimation
        cls.ti1 = TourIntimation.objects.create(
            id=1,
            company=cls.company,
            financial_year_id=cls.fin_year.id,
            employee=cls.employee1,
            ti_no='TI-001',
            wo_no='WO-XYZ',
            business_group=cls.bg_group1, # BG Group 1 for WO No
            project_name='Project Alpha',
            place_of_tour_city=cls.city,
            place_of_tour_state=cls.state,
            place_of_tour_country=cls.country,
            tour_start_date=date(2023, 10, 1),
            tour_end_date=date(2023, 10, 5),
        )
        cls.ti2 = TourIntimation.objects.create(
            id=2,
            company=cls.company,
            financial_year_id=cls.fin_year.id,
            employee=cls.employee2,
            ti_no='TI-002',
            wo_no='WO-123',
            business_group=cls.bg_group2, # BG Group 2, so WO No should be 'NA'
            project_name='Project Beta',
            place_of_tour_city=cls.city,
            place_of_tour_state=cls.state,
            place_of_tour_country=cls.country,
            tour_start_date=date(2023, 11, 10),
            tour_end_date=date(2023, 11, 15),
        )

    def test_tour_intimation_creation(self):
        ti = TourIntimation.objects.get(id=1)
        self.assertEqual(ti.ti_no, 'TI-001')
        self.assertEqual(ti.employee.name, 'John Doe')
        self.assertEqual(ti.business_group.symbol, 'General')

    def test_financial_year_display_property(self):
        ti = TourIntimation.objects.get(id=1)
        self.assertEqual(ti.financial_year_display, '2023-24')

    def test_business_group_display_property(self):
        ti1 = TourIntimation.objects.get(id=1)
        ti2 = TourIntimation.objects.get(id=2)
        self.assertEqual(ti1.business_group_display, 'General')
        self.assertEqual(ti2.business_group_display, 'Sales')

    def test_work_order_or_na_property(self):
        ti1 = TourIntimation.objects.get(id=1)
        ti2 = TourIntimation.objects.get(id=2)
        self.assertEqual(ti1.work_order_or_na, 'WO-XYZ') # BG Group 1, so WO No
        self.assertEqual(ti2.work_order_or_na, 'NA')      # BG Group 2, so NA

    def test_full_place_of_tour_property(self):
        ti = TourIntimation.objects.get(id=1)
        self.assertEqual(ti.full_place_of_tour, 'USA, NY, New York')

    def test_employee_str_representation(self):
        self.assertEqual(str(self.employee1), 'Mr. John Doe [EMP001]')


class TourIntimationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=10, year='2023-24')
        cls.employee1 = Employee.objects.create(id='EMP001', title='Mr.', name='John Doe', company=cls.company, financial_year=cls.fin_year)
        cls.employee2 = Employee.objects.create(id='EMP002', title='Jane Smith', name='Jane Smith', company=cls.company, financial_year=cls.fin_year) # Another employee
        cls.bg_group1 = BusinessGroup.objects.create(id=1, symbol='General')
        cls.bg_group2 = BusinessGroup.objects.create(id=2, symbol='Sales')
        cls.city = City.objects.create(id=101, name='New York')
        cls.state = State.objects.create(id=201, name='NY')
        cls.country = Country.objects.create(id=301, name='USA')

        TourIntimation.objects.create(
            id=1, company=cls.company, financial_year_id=cls.fin_year.id,
            employee=cls.employee1, ti_no='TI-001', wo_no='WO-XYZ', business_group=cls.bg_group1,
            project_name='Project Alpha', place_of_tour_city=cls.city, place_of_tour_state=cls.state,
            place_of_tour_country=cls.country, tour_start_date=date(2023, 10, 1), tour_end_date=date(2023, 10, 5),
        )
        TourIntimation.objects.create(
            id=2, company=cls.company, financial_year_id=cls.fin_year.id,
            employee=cls.employee2, ti_no='TI-002', wo_no='WO-123', business_group=cls.bg_group2,
            project_name='Project Beta', place_of_tour_city=cls.city, place_of_tour_state=cls.state,
            place_of_tour_country=cls.country, tour_start_date=date(2023, 11, 1), tour_end_date=date(2023, 11, 5),
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('tourintimation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/list.html')
        self.assertContains(response, 'Tour Intimation Print')
        self.assertIsInstance(response.context['search_form'], TourIntimationSearchForm)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('tourintimation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/_tourintimation_table.html')
        self.assertContains(response, 'TI-001') # Check if data is present
        self.assertContains(response, 'TI-002')

    def test_table_partial_view_search_ti_no(self):
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '0', 'txt_mrs': 'TI-001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TI-001')
        self.assertNotContains(response, 'TI-002')

    def test_table_partial_view_search_employee_name(self):
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '1', 'txt_emp_name': 'John Doe [EMP001]'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TI-001')
        self.assertNotContains(response, 'TI-002')

    def test_table_partial_view_search_wo_no(self):
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '2', 'txt_mrs': 'WO-XYZ'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TI-001')
        self.assertNotContains(response, 'TI-002')

    def test_table_partial_view_search_bg_group(self):
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '3', 'drp_group': self.bg_group2.id})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TI-002')
        self.assertNotContains(response, 'TI-001')

    def test_table_partial_view_search_project_name(self):
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '4', 'txt_mrs': 'Alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TI-001')
        self.assertNotContains(response, 'TI-002')

    def test_detail_view(self):
        ti = TourIntimation.objects.get(id=1)
        response = self.client.get(reverse('tourintimation_detail', args=[ti.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/detail.html')
        self.assertContains(response, 'TI-001')
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'USA, NY, New York')

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'prefix_text': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertIn('John Doe [EMP001]', response.content.decode())
        self.assertNotIn('Jane Smith', response.content.decode())

    def test_tourintimation_search_form_fields_view(self):
        # Test for TI No / WO No / Project Name fields
        response = self.client.post(reverse('tourintimation_search_form_fields'), {'drp_field': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/_search_form_fields.html')
        self.assertContains(response, 'id="id_txt_mrs"')
        self.assertNotContains(response, 'id="id_txt_emp_name"')
        self.assertNotContains(response, 'id="id_drp_group"')

        # Test for Employee Name field
        response = self.client.post(reverse('tourintimation_search_form_fields'), {'drp_field': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'id="id_txt_emp_name"')
        self.assertNotContains(response, 'id="id_txt_mrs"')
        self.assertNotContains(response, 'id="id_drp_group"')

        # Test for BG Group field
        response = self.client.post(reverse('tourintimation_search_form_fields'), {'drp_field': '3'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'id="id_drp_group"')
        self.assertNotContains(response, 'id="id_txt_mrs"')
        self.assertNotContains(response, 'id="id_txt_emp_name"')

    # --- Generic CRUD View Tests (as per template) ---
    def test_create_view_get(self):
        response = self.client.get(reverse('tourintimation_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_htmx(self):
        initial_count = TourIntimation.objects.count()
        data = {
            'company': self.company.id,
            'financial_year_id': self.fin_year.id,
            'employee': self.employee1.id,
            'ti_no': 'TI-NEW',
            'wo_no': 'WO-NEW',
            'business_group': self.bg_group1.id,
            'project_name': 'New Project',
            'place_of_tour_city': self.city.id,
            'place_of_tour_state': self.state.id,
            'place_of_tour_country': self.country.id,
            'tour_start_date': '2024-01-01',
            'tour_end_date': '2024-01-05',
        }
        response = self.client.post(reverse('tourintimation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(TourIntimation.objects.count(), initial_count + 1)
        self.assertTrue(TourIntimation.objects.filter(ti_no='TI-NEW').exists())

    def test_update_view_get(self):
        ti = TourIntimation.objects.get(id=1)
        response = self.client.get(reverse('tourintimation_edit', args=[ti.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.ti_no, 'TI-001')

    def test_update_view_post_htmx(self):
        ti = TourIntimation.objects.get(id=1)
        data = {
            'company': ti.company.id,
            'financial_year_id': ti.financial_year_id,
            'employee': ti.employee.id,
            'ti_no': 'TI-001-UPDATED', # Changed
            'wo_no': ti.wo_no,
            'business_group': ti.business_group.id,
            'project_name': ti.project_name,
            'place_of_tour_city': ti.place_of_tour_city.id,
            'place_of_tour_state': ti.place_of_tour_state.id,
            'place_of_tour_country': ti.place_of_tour_country.id,
            'tour_start_date': ti.tour_start_date.isoformat(),
            'tour_end_date': ti.tour_end_date.isoformat(),
        }
        response = self.client.post(reverse('tourintimation_edit', args=[ti.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        ti.refresh_from_db()
        self.assertEqual(ti.ti_no, 'TI-001-UPDATED')

    def test_delete_view_get(self):
        ti = TourIntimation.objects.get(id=1)
        response = self.client.get(reverse('tourintimation_delete', args=[ti.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/tourintimation/_confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete Tour Intimation')

    def test_delete_view_post_htmx(self):
        initial_count = TourIntimation.objects.count()
        ti = TourIntimation.objects.get(id=1)
        response = self.client.post(reverse('tourintimation_delete', args=[ti.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(TourIntimation.objects.count(), initial_count - 1)
        self.assertFalse(TourIntimation.objects.filter(id=ti.id).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django code provided extensively uses HTMX and Alpine.js:

-   **HTMX for DataTables Loading:**
    -   The `tourintimation-table-container` div in `list.html` uses `hx-get="{% url 'tourintimation_table' %}"` and `hx-trigger="load, refreshTourIntimationList from:body"` to load the table content dynamically.
    -   After CRUD operations (add, edit, delete), `HX-Trigger: refreshTourIntimationList` is sent back from the Django view, causing the table to refresh without a full page reload.
-   **HTMX for Search Form:**
    -   The `TourIntimationSearchForm` submits its data using `hx-get` to the `tourintimation_table` endpoint, updating only the table area.
    -   The `drp_field` dropdown uses `hx-post` to `tourintimation_search_form_fields` to dynamically swap the input fields (`_search_form_fields.html`), mimicking the ASP.NET `SelectedIndexChanged` and `Visible` property changes.
-   **HTMX for Autocomplete:**
    -   `txt_emp_name` uses `hx-get="{% url 'employee_autocomplete' %}"` with `hx-trigger="keyup changed delay:500ms"` to fetch suggestions from `EmployeeAutocompleteView`, targeting `#autocomplete-results`.
    -   Alpine.js can be used to manage the visibility of the autocomplete results div, but the HTMX swap directly replaces its content.
-   **HTMX for Modals (CRUD forms and Delete Confirmation):**
    -   Buttons for 'Add New', 'Edit', and 'Delete' in `list.html` and `detail.html` use `hx-get` to load the respective form/confirmation partials (`_form.html`, `_confirm_delete.html`) into a modal container (`#modalContent`).
    -   Alpine.js (using `_`) manages the modal's `hidden` class based on button clicks and HTMX events (`htmx:afterOnLoad`).
-   **DataTables:**
    -   The `_tourintimation_table.html` partial contains the `<table id="tourintimationTable">` element.
    -   The `$(document).ready` and `htmx:afterSwap` event listener in `list.html` ensures DataTables is initialized correctly whenever the table content is loaded or refreshed by HTMX.
-   **Alpine.js for UI State:**
    -   Primarily used for simple UI interactions like managing the modal's visibility (`on click add/remove .is-active to #modal`).
    -   While `Alpine.store('tourIntimation')` is shown, the dynamic field logic is offloaded to HTMX-swapped partials, reducing the need for complex Alpine `x-show` logic across multiple fields.

## Final Notes

-   **Placeholders:** Replace `[TABLE_NAME]`, `[MODEL_NAME]`, `[APP_NAME]`, `[FIELD1]` etc., with actual values derived from your full ASP.NET schema and business requirements. This plan has already made many of these substitutions based on the provided code.
-   **Legacy Data Migration:** This plan assumes direct mapping to existing database tables (`managed = False`). Ensure your Django database connection is configured correctly to connect to your SQL Server (or equivalent) legacy database.
-   **Error Handling:** The original ASP.NET code had empty `try-catch` blocks. In Django, proper error logging and user-friendly error messages are essential. This plan implicitly assumes Django's default error handling and `messages` framework for user feedback.
-   **Authentication/Authorization:** The ASP.NET code uses `Session["compid"]` and `Session["finyear"]`. In Django, this would typically be handled by `request.user` (after authentication) and a user profile model or specific session management, ensuring correct company and financial year context for data filtering. This plan leaves those specific session lookups as comments for you to integrate.
-   **Security:** Always sanitize user input, use Django's built-in CSRF protection (`{% csrf_token %}`), and implement robust authentication/authorization.
-   **Scalability:** The HTMX and DataTables approach significantly offloads rendering and some data manipulation to the client, improving server performance and scalability compared to traditional server-side rendering of large tables.