## ASP.NET to Django Conversion Script: Bank Loan Print Module

This document outlines a modernization plan to transition the `BankLoan_Print.aspx` module from ASP.NET to a modern Django-based solution. The focus is on leveraging AI-assisted automation, adhering to Django best practices, and ensuring a robust, maintainable, and user-friendly application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `binddata` method and `GridView2` column bindings, we identify the primary data source:

*   **Primary Table:** `tblHR_BankLoan`
    *   **Columns:**
        *   `Id` (int) - Maps to Django `id` (primary key)
        *   `EmpId` (string) - Maps to Django `CharField`
        *   `BankName` (string) - Maps to Django `CharField`
        *   `Branch` (string) - Maps to Django `CharField`
        *   `Amount` (double) - Maps to Django `FloatField`
        *   `Installment` (double) - Maps to Django `FloatField`
        *   `fromDate` (string, DMY format) - Maps to Django `DateField`
        *   `ToDate` (string, DMY format) - Maps to Django `DateField`
        *   `CompId` (int) - Maps to Django `IntegerField` (implicitly used in queries)
        *   `FinYearId` (string) - Maps to Django `CharField` (implicitly used in queries)

*   **Lookup Table:** `tblHR_OfficeStaff` (for `EmployeeName` lookup and autocomplete)
    *   **Columns:**
        *   `EmpId` (string) - Maps to Django `CharField`
        *   `EmployeeName` (string) - Maps to Django `CharField`
        *   `Title` (string) - Maps to Django `CharField`
        *   `CompId` (int) - Maps to Django `IntegerField` (implicitly used in queries)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations in the ASP.NET code.

*   **Read (List):**
    *   Displays a list of bank loans.
    *   Filters by `CompId` and `FinYearId`.
    *   Allows searching by `EmpId` (Employee Name).
    *   Employee Name lookup involves joining `tblHR_OfficeStaff` (N+1 query detected, will optimize).
    *   Includes client-side pagination.
*   **Search/Filter:**
    *   Radio buttons to select "Emp Wise" or "All".
    *   "Emp Wise" displays an employee name search field with an autocomplete extender.
    *   "Search" button triggers data re-binding.
*   **Redirection/Navigation:**
    *   "All" radio button option redirects to `BankLoan_Print_Details.aspx` (with empty `EmpId`).
    *   "Select" link button in GridView rows redirects to `BankLoan_Print_Details.aspx` with the specific `EmpId`.
*   **Autocomplete:** Provides suggestions for employee names based on `tblHR_OfficeStaff`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

*   **Radio Buttons:** `RadioButtonList1` (Emp Wise / All) -> Django forms or direct HTMX toggles.
*   **Panels:** `Panel1` (conditional visibility) -> Django template logic or HTMX conditional rendering.
*   **Dropdown:** `DrpField` (Employee Name) -> Django forms or HTMX.
*   **Textboxes:** `TxtEmpName` (Employee Name search), `TxtMrs` (alternative search, currently unused) -> Django forms.
*   **Autocomplete:** `AutoCompleteExtender` for `TxtEmpName` -> HTMX `hx-get` and Alpine.js for suggestions.
*   **Button:** `Button1` (Search) -> HTMX `hx-post` or `hx-get`.
*   **GridView:** `GridView2` (Bank Loan data display, pagination, "Select" link) -> DataTables with HTMX for initial load and reloads.
*   **Labels:** `Label2` (error/message display) -> Django messages framework and HTMX `hx-swap`.

### Step 4: Generate Django Code

We will create a new Django application, e.g., `bankloans_app`, within the project structure.

#### 4.1 Models (`bankloans_app/models.py`)

```python
from django.db import models
from django.utils import timezone
import re
from datetime import datetime

class BankLoanManager(models.Manager):
    """
    Custom manager for BankLoan to handle complex queries and business logic.
    """
    def get_queryset(self):
        return super().get_queryset()

    def get_bank_loans_with_employee_info(self, company_id, financial_year_id, emp_id_filter=None):
        """
        Fetches bank loan data and efficiently includes employee names.
        Optimized to avoid N+1 queries for employee names.
        """
        # Step 1: Fetch BankLoan records
        loans_queryset = self.get_queryset().filter(
            db_column_compid=company_id, # Assuming 'db_column_compid' is the actual column name for CompId
            db_column_finyearid__lte=financial_year_id # Assuming 'db_column_finyearid' for FinYearId
        ).order_by('-db_column_empid')

        if emp_id_filter:
            loans_queryset = loans_queryset.filter(db_column_empid=emp_id_filter)

        # Step 2: Collect all unique EmpIds from the fetched loans
        employee_ids = list(loans_queryset.values_list('db_column_empid', flat=True).distinct())

        # Step 3: Fetch all relevant OfficeStaff in a single query
        # Assuming 'db_column_office_staff_empid' and 'db_column_employeename' are actual column names
        employee_info = OfficeStaff.objects.filter(
            db_column_compid=company_id,
            db_column_office_staff_empid__in=employee_ids
        ).values('db_column_office_staff_empid', 'db_column_employeename', 'db_column_title')

        # Create a dictionary for quick lookup of employee names
        employee_name_map = {}
        for emp in employee_info:
            full_name = f"{emp['db_column_title']}. {emp['db_column_employeename']}" if emp['db_column_title'] else emp['db_column_employeename']
            employee_name_map[emp['db_column_office_staff_empid']] = full_name

        # Step 4: Attach employee names to each loan object
        # We need to iterate and add dynamically because EmpId is not a ForeignKey
        # Or, we can construct a list of dictionaries as output
        result_data = []
        for loan in loans_queryset:
            loan_dict = {
                'id': loan.pk,
                'EmpId': loan.db_column_empid,
                'EmployeeName': employee_name_map.get(loan.db_column_empid, 'N/A'), # Default if not found
                'BankName': loan.db_column_bankname,
                'Branch': loan.db_column_branch,
                'Amount': loan.db_column_amount,
                'Installment': loan.db_column_installment,
                'fromDate': loan.formatted_from_date(),
                'ToDate': loan.formatted_to_date(),
            }
            result_data.append(loan_dict)
        return result_data

class BankLoan(models.Model):
    # Using 'db_column' to explicitly map to existing database column names
    # Assuming standard primary key 'Id' auto-managed by Django if not specified.
    # If 'Id' is truly a column and not the PK, rename it in db_column and use a different PK.
    # For this example, assuming 'Id' from ASP.NET maps to Django's default 'id' PK.
    db_column_empid = models.CharField(db_column='EmpId', max_length=50)
    db_column_bankname = models.CharField(db_column='BankName', max_length=100)
    db_column_branch = models.CharField(db_column='Branch', max_length=100)
    db_column_amount = models.FloatField(db_column='Amount')
    db_column_installment = models.FloatField(db_column='Installment')
    db_column_fromdate = models.CharField(db_column='fromDate', max_length=10) # Stored as string like DD/MM/YYYY
    db_column_todate = models.CharField(db_column='ToDate', max_length=10) # Stored as string like DD/MM/YYYY
    db_column_compid = models.IntegerField(db_column='CompId')
    db_column_finyearid = models.CharField(db_column='FinYearId', max_length=10) # Stored as string

    objects = BankLoanManager() # Attach the custom manager

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblHR_BankLoan'
        verbose_name = 'Bank Loan'
        verbose_name_plural = 'Bank Loans'

    def __str__(self):
        return f"Loan {self.pk} for Emp ID: {self.db_column_empid}"

    # Business logic for date formatting
    def _parse_date_string(self, date_str):
        """Helper to parse DD/MM/YYYY string to date object."""
        try:
            return datetime.strptime(date_str, '%d/%m/%Y').date()
        except (ValueError, TypeError):
            return None # Or raise an error, depending on desired behavior

    def formatted_from_date(self):
        """Returns fromDate as a formatted string."""
        date_obj = self._parse_date_string(self.db_column_fromdate)
        return date_obj.strftime('%d/%m/%Y') if date_obj else self.db_column_fromdate

    def formatted_to_date(self):
        """Returns ToDate as a formatted string."""
        date_obj = self._parse_date_string(self.db_column_todate)
        return date_obj.strftime('%d/%m/%Y') if date_obj else self.db_column_todate

    def get_employee_code_from_name(self, emp_name_with_code):
        """
        Mimics fun.getCode() to extract employee ID from string like "Name [ID]".
        """
        match = re.search(r'\[(.*?)\]', emp_name_with_code)
        if match:
            return match.group(1)
        return None

class OfficeStaffManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset()

    def get_autocomplete_suggestions(self, prefix_text, company_id):
        """
        Provides autocomplete suggestions for employee names.
        """
        # Assuming 'db_column_employeename' and 'db_column_office_staff_empid' are actual column names
        results = self.get_queryset().filter(
            db_column_compid=company_id,
            db_column_employeename__icontains=prefix_text
        ).order_by('db_column_employeename')[:10] # Limit suggestions

        suggestions = []
        for staff in results:
            full_name = f"{staff.db_column_title}. {staff.db_column_employeename}" if staff.db_column_title else staff.db_column_employeename
            suggestions.append(f"{full_name} [{staff.db_column_office_staff_empid}]")
        return suggestions

class OfficeStaff(models.Model):
    # This model is for lookup purposes, primarily for autocomplete
    db_column_office_staff_empid = models.CharField(db_column='EmpId', max_length=50, primary_key=True) # Assuming EmpId is PK here
    db_column_employeename = models.CharField(db_column='EmployeeName', max_length=200)
    db_column_title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)
    db_column_compid = models.IntegerField(db_column='CompId')

    objects = OfficeStaffManager()

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.db_column_employeename} [{self.db_column_office_staff_empid}]"

```

#### 4.2 Forms (`bankloans_app/forms.py`)

```python
from django import forms
from .models import BankLoan # Although no direct form is needed for 'print' list, keeping for future CRUD

class BankLoanSearchForm(forms.Form):
    SEARCH_OPTIONS = [
        ('0', 'Emp Wise'),
        ('1', 'All'),
    ]
    search_option = forms.ChoiceField(
        choices=SEARCH_OPTIONS,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'}),
        initial='0'
    )
    employee_name_search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-get': '/bankloans/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms', # Trigger on keyup, with delay
            'hx-target': '#employee-suggestions', # Target div for suggestions
            'hx-swap': 'innerHTML',
            'name': 'employee_name_search', # Ensure name is present for form data
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-model': 'selectedEmployee', # Alpine.js binding
            '@input': 'clearSelectedId()', # Alpine.js method to clear hidden ID
            '@focus': 'showSuggestions = true', # Show suggestions on focus
            '@blur.away': 'setTimeout(() => showSuggestions = false, 100)', # Hide on blur away
        })
    )
    # Hidden field to store the actual EmpId from autocomplete selection
    selected_employee_id = forms.CharField(
        widget=forms.HiddenInput(attrs={'x-model': 'selectedEmployeeId'}),
        required=False
    )

    # Note: DrpField "Employee Name" is hardcoded to "0", so no need for a dropdown list in the form.
    # TxtMrs is not used in the ASP.NET code for this specific function.

    def clean(self):
        cleaned_data = super().clean()
        search_option = cleaned_data.get('search_option')
        employee_name_search = cleaned_data.get('employee_name_search')
        selected_employee_id = cleaned_data.get('selected_employee_id')

        if search_option == '0': # Emp Wise
            # If search is emp wise, ensure an employee is selected or text is provided
            if not employee_name_search and not selected_employee_id:
                self.add_error('employee_name_search', 'Employee name or selection is required for "Emp Wise" search.')
            elif employee_name_search and not selected_employee_id:
                 # Attempt to parse code if direct input without selection
                 parsed_code = BankLoan().get_employee_code_from_name(employee_name_search)
                 if parsed_code:
                     cleaned_data['selected_employee_id'] = parsed_code
                 else:
                     # If the input doesn't match the [ID] format, it might be an invalid direct entry
                     # A more robust solution might validate against existing employees.
                     pass # Allow partial match, but warn if no ID found if strict.
        return cleaned_data

# Skeleton for actual BankLoan CRUD form (if needed in other modules)
class BankLoanForm(forms.ModelForm):
    class Meta:
        model = BankLoan
        fields = ['db_column_empid', 'db_column_bankname', 'db_column_branch', 'db_column_amount', 'db_column_installment', 'db_column_fromdate', 'db_column_todate']
        widgets = {
            'db_column_empid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_bankname': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_branch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_installment': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_fromdate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD/MM/YYYY'}),
            'db_column_todate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD/MM/YYYY'}),
        }

    def clean_db_column_fromdate(self):
        date_str = self.cleaned_data['db_column_fromdate']
        try:
            datetime.strptime(date_str, '%d/%m/%Y')
        except ValueError:
            raise forms.ValidationError("Invalid date format. Use DD/MM/YYYY.")
        return date_str

    def clean_db_column_todate(self):
        date_str = self.cleaned_data['db_column_todate']
        try:
            datetime.strptime(date_str, '%d/%m/%Y')
        except ValueError:
            raise forms.ValidationError("Invalid date format. Use DD/MM/YYYY.")
        return date_str

```

#### 4.3 Views (`bankloans_app/views.py`)

```python
from django.views.generic import ListView, View, RedirectView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from .models import BankLoan, OfficeStaff
from .forms import BankLoanSearchForm
from django.conf import settings # For accessing settings like COMPANY_ID, FINANCIAL_YEAR_ID

# Dummy values for Session parameters, these should ideally come from user context or settings
DEFAULT_COMPANY_ID = 1
DEFAULT_FINANCIAL_YEAR_ID = '2024' # Example: or 'FY24' etc based on actual data

class BankLoanPrintListView(ListView):
    """
    Main view to display the Bank Loan print list, handling search and filter.
    """
    model = BankLoan
    template_name = 'bankloans_app/bankloan_print/list.html'
    context_object_name = 'bankloans' # Not directly used for table content, but good practice

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        if 'form' not in context: # Ensure form is not re-initialized if passed from POST
            context['form'] = BankLoanSearchForm(self.request.GET or None)
        return context

    def get(self, request, *args, **kwargs):
        # Determine company_id and financial_year_id (e.g., from session, user profile, or config)
        # For demonstration, using defaults. In a real app, integrate with authentication system.
        company_id = getattr(request.user, 'company_id', DEFAULT_COMPANY_ID) # Example: request.user.profile.company_id
        financial_year_id = getattr(request.user, 'financial_year_id', DEFAULT_FINANCIAL_YEAR_ID) # Example: request.user.profile.fin_year_id

        form = BankLoanSearchForm(request.GET)
        emp_id_filter = None
        search_option = '0' # Default to Emp Wise

        if form.is_valid():
            search_option = form.cleaned_data.get('search_option')
            if search_option == '0': # Emp Wise
                employee_name_search = form.cleaned_data.get('employee_name_search')
                selected_employee_id = form.cleaned_data.get('selected_employee_id')

                if selected_employee_id:
                    emp_id_filter = selected_employee_id
                elif employee_name_search:
                    # Attempt to parse code from direct input if not selected via autocomplete
                    parsed_code = BankLoan().get_employee_code_from_name(employee_name_search)
                    if parsed_code:
                        emp_id_filter = parsed_code
                    else:
                        # If no specific ID found, perhaps search by name (though original code only by ID)
                        # For now, stick to exact ID match as per original 'fun.getCode' behavior.
                        pass # emp_id_filter remains None, leading to no results for invalid direct input.
            elif search_option == '1': # All
                # Redirect to details page or handle in HTMX
                # As per ASP.NET, this redirects immediately.
                # In Django, with HTMX, we can send an HX-Redirect header.
                detail_url = reverse('bankloans_app:bankloan_print_details', kwargs={'emp_id': 'all_employees'})
                messages.info(request, "Redirecting to Bank Loan Details for All Employees.")
                return HttpResponse(status=204, headers={'HX-Redirect': detail_url})
        else:
            # If form is not valid on GET, e.g., initial load without parameters, proceed normally
            pass # Form validation errors can be displayed in template

        # Fetch data using the optimized manager method
        bankloans_data = self.model.objects.get_bank_loans_with_employee_info(
            company_id=company_id,
            financial_year_id=financial_year_id,
            emp_id_filter=emp_id_filter
        )

        context = self.get_context_data(form=form) # Pass the form to context
        context['bankloans'] = bankloans_data
        context['search_option'] = search_option

        # If it's an HTMX request for the table, return only the partial
        if request.headers.get('HX-Request'):
            return render(request, 'bankloans_app/bankloan_print/_bankloan_table.html', context)
        
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        # This handles the search form submission via HTMX POST
        return self.get(request, *args, **kwargs) # Re-use GET logic for data fetching


class BankLoanAutocompleteView(View):
    """
    Provides autocomplete suggestions for employee names.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('query', '')
        # Determine company_id (e.g., from session, user profile, or config)
        company_id = getattr(request.user, 'company_id', DEFAULT_COMPANY_ID)

        suggestions = OfficeStaff.objects.get_autocomplete_suggestions(query, company_id)

        # Return a simple unordered list of suggestions for HTMX to render
        html_suggestions = "<ul>"
        for suggestion in suggestions:
            # Use data-value to pass the full string (including code) for Alpine.js/HTMX
            html_suggestions += f"<li class='p-2 hover:bg-gray-200 cursor-pointer' x-on:click='selectEmployee(\"{suggestion}\")' data-value='{suggestion}'>{suggestion}</li>"
        html_suggestions += "</ul>"
        return HttpResponse(html_suggestions)

class BankLoanPrintDetailRedirectView(RedirectView):
    """
    Handles redirection to the bank loan details page.
    Mimics the ASP.NET Response.Redirect functionality.
    """
    permanent = False
    query_string = True # Keep query parameters from original request

    def get_redirect_url(self, *args, **kwargs):
        emp_id = kwargs.get('emp_id') # Can be a specific ID or 'all_employees'
        # In a real application, 'Key', 'ModId', 'SubModId' would be handled via Django URL parameters or a more secure method.
        # For this example, we generate a random key.
        random_key = BankLoan().get_random_alphanumeric() # Assuming this is a static method or similar util
        # Replace 'bankloan_details_page' with the actual URL name for the details page
        return reverse('bankloans_app:bankloan_print_details', kwargs={'emp_id': emp_id, 'key': random_key}) + "?ModId=12&SubModId=129"

# Helper function to generate random alphanumeric string (mimics fun.GetRandomAlphaNumeric)
# This could be a utility function in a separate file or a static method in a relevant model/manager
def get_random_alphanumeric(length=10):
    import string
    import random
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for i in range(length))

# Monkey patch the method onto BankLoan model instance for demonstration if not already static/class method
setattr(BankLoan, 'get_random_alphanumeric', staticmethod(get_random_alphanumeric))


# Note: The original ASP.NET code also had dummy Create/Update/Delete views mentioned in the template.
# These are not directly applicable to this "Print" module but are provided as skeletons for completeness.
# They are not explicitly generated from the analysis of BankLoan_Print.aspx.
# For demonstration purposes, I will not include them as per the instructions to focus "ONLY on component-specific code for the current module".
# However, if needed, they would follow the exact structure provided in the prompt's template.
```

#### 4.4 Templates (`bankloans_app/templates/bankloans_app/bankloan_print/`)

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h2 class="text-2xl font-bold text-gray-800">Bank Loan - Print</h2>
        </div>

        <form hx-post="{% url 'bankloans_app:bankloan_list' %}" hx-target="#bankloanTable-container" hx-swap="innerHTML" hx-indicator="#loading-indicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center mb-6">
                <div class="flex items-center">
                    <label class="font-bold text-gray-700 mr-4">Search Option :</label>
                    <div class="flex space-x-4">
                        {% for radio in form.search_option %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 font-bold text-gray-800">{{ radio.choice_label }}</span>
                        </label>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div id="search-panel" x-data="{ searchOption: '{{ form.search_option.value }}', selectedEmployee: '{{ form.employee_name_search.value|default:"" }}', selectedEmployeeId: '{{ form.selected_employee_id.value|default:"" }}', showSuggestions: false }"
                 x-init="$watch('searchOption', value => {
                     if (value === '0') {
                        $el.style.display = 'block';
                        // Trigger a load for Emp Wise search
                        htmx.trigger('#bankloanTable-container', 'refreshTable');
                     } else {
                        // For 'All' option, redirect using HTMX (hx-redirect on form submit)
                        document.querySelector('form').submit(); // Submitting the form will trigger HX-Redirect
                     }
                 })">

                <div x-show="searchOption === '0'" class="mb-4">
                    <div class="flex items-center space-x-2">
                        <!-- Dropdown field is hardcoded to "Employee Name" (value 0) in ASP.NET, so no need for actual select box -->
                        <span class="text-gray-700">Employee Name</span>
                        {{ form.employee_name_search }}
                        {{ form.selected_employee_id }} <!-- Hidden input for selected EmpId -->
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">Search</button>
                    </div>
                    <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-64" x-show="showSuggestions && selectedEmployee.length > 0">
                        <!-- Suggestions will be loaded here by HTMX -->
                    </div>
                    {% if form.employee_name_search.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.employee_name_search.errors }}</p>
                    {% endif %}
                </div>
            </div>
        </form>

        <div class="mb-4">
            <span id="loading-indicator" class="htmx-indicator text-blue-500 text-sm">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                Loading data...
            </span>
            <p class="text-red-500 font-bold" id="label2"></p> {# Placeholder for Label2 #}
            {% if messages %}
            <div class="mt-4">
                {% for message in messages %}
                <div class="p-3 mb-2 rounded-md {% if message.tags %}bg-{{ message.tags }}-100 text-{{ message.tags }}-800{% endif %}" role="alert">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <div id="bankloanTable-container"
             hx-trigger="load, refreshTable from:body, submit from:form"
             hx-get="{% url 'bankloans_app:bankloan_list' %}"
             hx-target="#bankloanTable-container"
             hx-swap="innerHTML"
             class="min-h-[410px] overflow-auto">
            <!-- Initial content or loading indicator -->
            <div class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Bank Loan Data...</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('bankLoanSearch', () => ({
            searchOption: '{{ form.search_option.value }}',
            selectedEmployee: '{{ form.employee_name_search.value|default:"" }}',
            selectedEmployeeId: '{{ form.selected_employee_id.value|default:"" }}',
            showSuggestions: false,

            // Function to handle selection from autocomplete
            selectEmployee(suggestionText) {
                this.selectedEmployee = suggestionText;
                // Extract the ID from the suggestion string (e.g., "Name [ID]")
                const match = suggestionText.match(/\[(.*?)\]$/);
                if (match && match[1]) {
                    this.selectedEmployeeId = match[1];
                } else {
                    this.selectedEmployeeId = '';
                }
                this.showSuggestions = false;
                // Optional: Submit the form immediately after selection
                // htmx.trigger(document.querySelector('form'), 'submit');
            },
            clearSelectedId() {
                // Clear the hidden ID if the user is typing again
                const currentText = this.selectedEmployee;
                const hiddenIdField = document.querySelector('input[name="selected_employee_id"]');
                if (hiddenIdField && hiddenIdField.value && !currentText.includes(hiddenIdField.value)) {
                    this.selectedEmployeeId = '';
                    hiddenIdField.value = ''; // Ensure DOM is updated
                }
            }
        }));
    });

    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Reinitialize DataTables after HTMX swaps the table content
        if (evt.target.id === 'bankloanTable-container' || evt.detail.elt.id === 'bankloanTable') {
            $('#bankloanTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "language": {
                    "emptyTable": "No data to display !"
                }
            });
        }
    });

    // Initial DataTables setup on page load (if not loaded via HTMX first)
    $(document).ready(function() {
        if ($('#bankloanTable').length) {
            $('#bankloanTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "language": {
                    "emptyTable": "No data to display !"
                }
            });
        }
    });
</script>
{% endblock %}
```

**`_bankloan_table.html` (Partial Template for HTMX Swap)**

```html
<table id="bankloanTable" class="min-w-full bg-white yui-datatable-theme border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-4%">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-4%">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-6%">EmpId</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20%">Emp Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-18%">Bank Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Branch</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">Installment</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">From Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">To Date</th>
        </tr>
    </thead>
    <tbody>
        {% for loan in bankloans %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'bankloans_app:bankloan_print_details' emp_id=loan.EmpId %}"
                    hx-redirect="{% url 'bankloans_app:bankloan_print_details' emp_id=loan.EmpId %}"
                    hx-target="body" hx-swap="outerHTML"
                    _="on click add .htmx-request to body"
                    title="View Details">
                    Select
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ loan.EmpId }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.EmployeeName }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.BankName }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.Branch }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ loan.Amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ loan.Installment|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.fromDate }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.ToDate }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-8 px-4 text-center font-bold text-lg text-maroon-600">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization (will be re-triggered by HTMX after swap)
    // This script block should only be included within the partial for re-initialization on HTMX swap
    // The main list.html block also has a document.ready() for initial page load.
    // Ensure `destroy: true` is used in the DataTables config to handle re-initialization.
</script>
```

**Skeleton `_bankloan_form.html` (For future CRUD operations, not directly from this `Print` module)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Bank Loan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**Skeleton `_confirm_delete.html` (For future CRUD operations, not directly from this `Print` module)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this Bank Loan record?</p>

    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`bankloans_app/urls.py`)

```python
from django.urls import path
from .views import BankLoanPrintListView, BankLoanAutocompleteView, BankLoanPrintDetailRedirectView

app_name = 'bankloans_app'

urlpatterns = [
    path('bankloans/', BankLoanPrintListView.as_view(), name='bankloan_list'),
    path('bankloans/autocomplete/', BankLoanAutocompleteView.as_view(), name='bankloan_autocomplete'),
    # This URL is a placeholder for the actual "Bank Loan Print Details" page.
    # In a full migration, this would be a separate Django view/module.
    path('bankloans/details/<str:emp_id>/', BankLoanPrintDetailRedirectView.as_view(), name='bankloan_print_details'),
]
```

#### 4.6 Tests (`bankloans_app/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import BankLoan, OfficeStaff, get_random_alphanumeric # Import the utility function
from datetime import datetime

class BankLoanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        OfficeStaff.objects.create(
            db_column_office_staff_empid='EMP001',
            db_column_employeename='John Doe',
            db_column_title='Mr.',
            db_column_compid=1
        )
        OfficeStaff.objects.create(
            db_column_office_staff_empid='EMP002',
            db_column_employeename='Jane Smith',
            db_column_title='Ms.',
            db_column_compid=1
        )
        BankLoan.objects.create(
            db_column_empid='EMP001',
            db_column_bankname='Test Bank A',
            db_column_branch='Main',
            db_column_amount=1000.00,
            db_column_installment=100.00,
            db_column_fromdate='01/01/2023',
            db_column_todate='01/10/2023',
            db_column_compid=1,
            db_column_finyearid='2023'
        )
        BankLoan.objects.create(
            db_column_empid='EMP002',
            db_column_bankname='Test Bank B',
            db_column_branch='Branch X',
            db_column_amount=2000.00,
            db_column_installment=200.00,
            db_column_fromdate='01/03/2023',
            db_column_todate='01/12/2023',
            db_column_compid=1,
            db_column_finyearid='2024'
        )
        BankLoan.objects.create(
            db_column_empid='EMP003', # This employee doesn't exist in OfficeStaff
            db_column_bankname='Unknown Bank',
            db_column_branch='Unknown Branch',
            db_column_amount=500.00,
            db_column_installment=50.00,
            db_column_fromdate='15/06/2023',
            db_column_todate='15/03/2024',
            db_column_compid=1,
            db_column_finyearid='2024'
        )

    def test_bankloan_creation(self):
        loan = BankLoan.objects.get(db_column_empid='EMP001')
        self.assertEqual(loan.db_column_bankname, 'Test Bank A')
        self.assertEqual(loan.db_column_amount, 1000.00)
        self.assertEqual(loan.db_column_fromdate, '01/01/2023')

    def test_bankloan_formatted_dates(self):
        loan = BankLoan.objects.get(db_column_empid='EMP001')
        self.assertEqual(loan.formatted_from_date(), '01/01/2023')
        self.assertEqual(loan.formatted_to_date(), '01/10/2023')

        # Test with invalid date format
        loan_invalid_date = BankLoan.objects.create(
            db_column_empid='EMP_INVALID',
            db_column_bankname='Invalid Date Bank',
            db_column_branch='Invalid',
            db_column_amount=100,
            db_column_installment=10,
            db_column_fromdate='2023-01-01', # Incorrect format
            db_column_todate='2023-12-31',
            db_column_compid=1,
            db_column_finyearid='2024'
        )
        self.assertEqual(loan_invalid_date.formatted_from_date(), '2023-01-01') # Should return original string if parse fails

    def test_get_employee_code_from_name(self):
        loan = BankLoan() # Use an instance to call the method
        self.assertEqual(loan.get_employee_code_from_name("John Doe [EMP001]"), "EMP001")
        self.assertIsNone(loan.get_employee_code_from_name("John Doe"))
        self.assertIsNone(loan.get_employee_code_from_name("John Doe [EMP001"))
        self.assertIsNone(loan.get_employee_code_from_name(""))

    def test_bankloan_manager_get_bank_loans_with_employee_info(self):
        # Test fetching all loans for a company and financial year
        loans = BankLoan.objects.get_bank_loans_with_employee_info(company_id=1, financial_year_id='2024')
        self.assertEqual(len(loans), 2) # EMP002 and EMP003 (as per setup finyear <= 2024)
        
        # Verify employee names are correctly attached
        emp002_loan = next(item for item in loans if item['EmpId'] == 'EMP002')
        self.assertEqual(emp002_loan['EmployeeName'], 'Ms. Jane Smith')

        emp003_loan = next(item for item in loans if item['EmpId'] == 'EMP003')
        self.assertEqual(emp003_loan['EmployeeName'], 'N/A') # Should be 'N/A' as not in OfficeStaff

        # Test with emp_id_filter
        filtered_loans = BankLoan.objects.get_bank_loans_with_employee_info(company_id=1, financial_year_id='2023', emp_id_filter='EMP001')
        self.assertEqual(len(filtered_loans), 1)
        self.assertEqual(filtered_loans[0]['EmpId'], 'EMP001')
        self.assertEqual(filtered_loans[0]['EmployeeName'], 'Mr. John Doe')

    def test_officestaff_manager_get_autocomplete_suggestions(self):
        suggestions = OfficeStaff.objects.get_autocomplete_suggestions('john', 1)
        self.assertIn('Mr. John Doe [EMP001]', suggestions)
        self.assertNotIn('Ms. Jane Smith [EMP002]', suggestions)
        self.assertEqual(len(suggestions), 1)

        suggestions = OfficeStaff.objects.get_autocomplete_suggestions('ja', 1)
        self.assertIn('Ms. Jane Smith [EMP002]', suggestions)

    def test_random_alphanumeric_generation(self):
        key = get_random_alphanumeric()
        self.assertEqual(len(key), 10)
        self.assertTrue(all(c.isalnum() for c in key))

class BankLoanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        OfficeStaff.objects.create(
            db_column_office_staff_empid='EMP001',
            db_column_employeename='John Doe',
            db_column_title='Mr.',
            db_column_compid=1
        )
        BankLoan.objects.create(
            db_column_empid='EMP001',
            db_column_bankname='Test Bank A',
            db_column_branch='Main',
            db_column_amount=1000.00,
            db_column_installment=100.00,
            db_column_fromdate='01/01/2023',
            db_column_todate='01/10/2023',
            db_column_compid=1,
            db_column_finyearid='2023'
        )
        BankLoan.objects.create(
            db_column_empid='EMP002',
            db_column_bankname='Test Bank B',
            db_column_branch='Branch X',
            db_column_amount=2000.00,
            db_column_installment=200.00,
            db_column_fromdate='01/03/2023',
            db_column_todate='01/12/2023',
            db_column_compid=1,
            db_column_finyearid='2024'
        )
    
    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('bankloans_app:bankloan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bankloans_app/bankloan_print/list.html')
        self.assertIn('form', response.context)
        # Check initial data load (should show all as no filter applied initially)
        # The list view now directly uses get_bank_loans_with_employee_info
        # The initial load would get items matching DEFAULT_COMPANY_ID & DEFAULT_FINANCIAL_YEAR_ID (2024 for EMP002)
        # Default finyear 2024 should show EMP002
        response_content = response.content.decode('utf-8')
        self.assertIn('Jane Smith', response_content)
        self.assertIn('Test Bank B', response_content)

    def test_list_view_post_emp_wise_search_valid(self):
        # Simulate HTMX search for a specific employee
        data = {
            'search_option': '0',
            'employee_name_search': 'Mr. John Doe [EMP001]',
            'selected_employee_id': 'EMP001',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloans_app:bankloan_list'), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bankloans_app/bankloan_print/_bankloan_table.html')
        response_content = response.content.decode('utf-8')
        self.assertIn('John Doe', response_content)
        self.assertIn('Test Bank A', response_content)
        self.assertNotIn('Jane Smith', response_content)

    def test_list_view_post_emp_wise_search_invalid_no_id(self):
        # Simulate HTMX search for a non-existent employee or malformed input
        data = {
            'search_option': '0',
            'employee_name_search': 'Non Existent Employee',
            'selected_employee_id': '',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloans_app:bankloan_list'), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bankloans_app/bankloan_print/_bankloan_table.html')
        response_content = response.content.decode('utf-8')
        self.assertIn('No data to display', response_content)

    def test_list_view_post_all_option_redirect(self):
        # Simulate HTMX POST for "All" option which triggers redirection
        data = {
            'search_option': '1', # 'All' option
            'employee_name_search': '',
            'selected_employee_id': '',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankloans_app:bankloan_list'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTTP 204 No Content for HX-Redirect
        self.assertIn('HX-Redirect', response.headers)
        expected_redirect_url_prefix = reverse('bankloans_app:bankloan_print_details', kwargs={'emp_id': 'all_employees'}).split('?')[0] # Remove query params for comparison
        self.assertTrue(response.headers['HX-Redirect'].startswith(expected_redirect_url_prefix))
        self.assertIn('ModId=12', response.headers['HX-Redirect'])
        self.assertIn('SubModId=129', response.headers['HX-Redirect'])

    def test_autocomplete_view(self):
        response = self.client.get(reverse('bankloans_app:bankloan_autocomplete') + '?query=john', **{'HTTP_X_REQUESTED_WITH': 'XMLHttpRequest'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/html; charset=utf-8')
        self.assertIn('Mr. John Doe [EMP001]', response.content.decode('utf-8'))
        self.assertNotIn('Jane Smith', response.content.decode('utf-8'))

    def test_autocomplete_view_no_query(self):
        response = self.client.get(reverse('bankloans_app:bankloan_autocomplete'), **{'HTTP_X_REQUESTED_WITH': 'XMLHttpRequest'})
        self.assertEqual(response.status_code, 200)
        self.assertIn('<ul></ul>', response.content.decode('utf-8')) # Empty list for no query

    def test_redirect_view_specific_emp(self):
        random_key = get_random_alphanumeric() # Match the function used in view
        with self.settings(DEFAULT_RANDOM_KEY=random_key): # Mock random key for deterministic test
            response = self.client.get(reverse('bankloans_app:bankloan_print_details', kwargs={'emp_id': 'EMP001'}))
            self.assertEqual(response.status_code, 302) # Standard redirect
            expected_url_prefix = reverse('bankloans_app:bankloan_print_details', kwargs={'emp_id': 'EMP001'}).split('?')[0]
            self.assertTrue(response.url.startswith(expected_url_prefix))
            self.assertIn('ModId=12', response.url)
            self.assertIn('SubModId=129', response.url)
            self.assertIn('Key=', response.url) # Key should be present

    def test_redirect_view_all_emp(self):
        random_key = get_random_alphanumeric()
        with self.settings(DEFAULT_RANDOM_KEY=random_key):
            response = self.client.get(reverse('bankloans_app:bankloan_print_details', kwargs={'emp_id': 'all_employees'}))
            self.assertEqual(response.status_code, 302)
            expected_url_prefix = reverse('bankloans_app:bankloan_print_details', kwargs={'emp_id': 'all_employees'}).split('?')[0]
            self.assertTrue(response.url.startswith(expected_url_prefix))
            self.assertIn('ModId=12', response.url)
            self.assertIn('SubModId=129', response.url)
            self.assertIn('Key=', response.url)
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic table updates:** The main `list.html` uses `hx-get` on `bankloanTable-container` to fetch the table content from `_bankloan_table.html` when the page loads, or when `refreshTable` custom event is triggered, or when the form is submitted.
*   **HTMX for search form:** The search form uses `hx-post` to send data to the `bankloan_list` view. The view processes the request and returns the updated table partial via `hx-target` and `hx-swap`.
*   **HTMX for autocomplete:** The `employee_name_search` input uses `hx-get` to `bankloan_autocomplete` endpoint, updating `employee-suggestions` div on `keyup changed delay:500ms`.
*   **HTMX for redirection:** The "All" radio button option and "Select" link buttons use `hx-redirect` to navigate to the detail page without a full page refresh *on the client side*. The server responds with a 204 status and the `HX-Redirect` header.
*   **Alpine.js for UI state:**
    *   `x-data` is used on the search panel to manage `searchOption`, `selectedEmployee`, `selectedEmployeeId`, and `showSuggestions`.
    *   `x-show` controls the visibility of the employee search input and autocomplete suggestions based on `searchOption` and `showSuggestions`.
    *   `x-model` binds input values to Alpine.js data properties for reactive updates.
    *   `x-on:click` on autocomplete suggestions calls `selectEmployee` function to populate the input and hidden ID field.
    *   `@input` and `@blur.away` manage `showSuggestions` state.
*   **DataTables:** The `_bankloan_table.html` includes a `<script>` block that re-initializes `DataTables` on the `#bankloanTable` after HTMX swaps the content. The `list.html` also includes a `$(document).ready` to initialize it on initial page load. `destroy: true` is crucial for correct re-initialization.

### Final Notes

*   **Placeholders:** Replace `DEFAULT_COMPANY_ID` and `DEFAULT_FINANCIAL_YEAR_ID` in `views.py` with actual values derived from the user's session or profile in a production environment.
*   **CSS:** The provided templates assume Tailwind CSS is configured. Classes like `bg-blue-500`, `py-2`, `px-4`, `rounded`, `shadow-md`, `w-full` are Tailwind classes.
*   **Error Handling:** The current setup includes basic form error display. More sophisticated error handling (e.g., using a dedicated message area or toast notifications) could be integrated.
*   **Security:** Ensure proper authentication and authorization are implemented in Django. The ASP.NET code relies on `Session["compid"]` and `Session["finyear"]`, which should be mapped to Django's authentication system (e.g., `request.user.profile.company_id`).
*   **`fun` class utilities:** The functions from `clsFunctions` (`fun.Connection()`, `fun.getCode()`, `fun.FromDateDMY()`, `fun.select()`, `fun.GetRandomAlphaNumeric()`) have been re-implemented or replaced by Django ORM queries, Python's `datetime` module, and custom utility functions/methods.
*   **`BankLoan_Print_Details.aspx`:** The redirection to this page is handled by Django's `RedirectView` and `HX-Redirect` header. The target Django view for this page (e.g., `bankloan_details`) would need to be created as a separate migration task.