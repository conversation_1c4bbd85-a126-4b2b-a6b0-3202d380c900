This modernization plan outlines the transition of your ASP.NET Gate Pass Print module to a robust, modern Django application. Our approach prioritizes automation and leverages cutting-edge web technologies like Django 5.0+, HTMX, Alpine.js, and DataTables to deliver a highly interactive and efficient solution without relying on traditional JavaScript frameworks.

## ASP.NET to Django Conversion Script: Gate Pass Print Module

This document provides a comprehensive plan to migrate your `GatePass_Print.aspx` functionality to a Django-based system. We will focus on creating a dynamic, single-page-like experience using HTMX for interactions and DataTables for efficient data display, replacing the legacy iframe-based reporting.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`hr` app, `gatepass` submodule).
- Always include complete unit tests for models and integration tests for views, aiming for 80% coverage.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy (DRY principle).
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with at least three primary tables:
1.  `tblGate_Pass`: The main gate pass record, containing master information.
2.  `tblGatePass_Details`: Contains detail records for each gate pass, linked by `MId` to `tblGate_Pass.Id`. This table holds the `FromDate` used in the search and employee IDs (`OtherEId`) if the pass is for someone other than the primary applicant.
3.  `tblHR_OfficeStaff`: Used for the employee autocomplete functionality, providing `EmpId` and `EmployeeName`.

**Identified Tables and Columns:**

*   **`tblGate_Pass` (Mapping to Django Model `GatePass`)**
    *   `Id` (Primary Key, Integer)
    *   `SessionId` (String)
    *   `CompId` (Integer) - Company ID (from session)
    *   `SysDate` (DateTime)
    *   `Authorize` (Boolean)
    *   `EmpId` (Integer, Foreign Key to `tblHR_OfficeStaff`) - Self Employee ID
    *   `FinYearId` (Integer) - Financial Year ID (from session)
    *   `GPNo` (String)
    *   `AuthorizedBy` (String)
    *   `AuthorizeDate` (DateTime)
    *   `AuthorizeTime` (String)

*   **`tblGatePass_Details` (Mapping to Django Model `GatePassDetail`)**
    *   `Id` (Primary Key, Integer)
    *   `MId` (Integer, Foreign Key to `tblGate_Pass.Id`)
    *   `FromDate` (Date)
    *   `FromTime` (String)
    *   `ToTime` (String)
    *   `Type` (String)
    *   `TypeFor` (String)
    *   `Reason` (Text)
    *   `Feedback` (Text)
    *   `EmpId` (Integer, Foreign Key to `tblHR_OfficeStaff`) - Other Employee ID
    *   `Place` (String)
    *   `ContactPerson` (String)
    *   `ContactNo` (String)

*   **`tblHR_OfficeStaff` (Mapping to Django Model `Employee`)**
    *   `EmpId` (Primary Key, Integer)
    *   `EmployeeName` (String)
    *   `CompId` (Integer)

### Step 2: Identify Backend Functionality

**Core Functionality:**
The primary purpose of the ASP.NET page is to enable users to search for Gate Pass records based on date ranges and employee names, and then display these results.

*   **Read/Search:** The `BtnSearch_Click` event orchestrates a complex `SELECT` query joining `tblGate_Pass` and `tblGatePass_Details`. It dynamically builds `WHERE` clauses based on `FromDate`, `ToDate`, and `EmpCode`. A unique aspect is how `EmpCode` is used: it checks if the employee ID matches either the `EmpId` in `tblGate_Pass` (self-employee) or the `EmpId` in `tblGatePass_Details` (other employee).
*   **Autocomplete:** The `sql3` web method provides `EmployeeName` suggestions from `tblHR_OfficeStaff` for the `txtEmpCode` field.
*   **Session Management:** `CompId` and `FinYearId` are retrieved from `Session` for filtering.
*   **Reporting (Iframe):** The search results are not displayed directly but passed as query parameters to `GatePass_Print_Details.aspx` which is loaded into an `<iframe>`.

**Missing/Inferred Functionality:**
This specific page does not implement Create, Update, or Delete operations directly. However, for a complete Django module, we will provide placeholder CRUD views for the `GatePass` model as per the guidelines, assuming these operations exist elsewhere in the original application. The "Print" aspect, historically handled by Crystal Reports via `GatePass_Print_Details.aspx`, will be replaced by displaying data directly in a DataTables-powered HTML table.

### Step 3: Infer UI Components

**ASP.NET Control Mapping to Django/HTMX/Tailwind CSS:**

*   `asp:TextBox ID="txtFromDate"` and `txtToDate"` with `CalendarExtender`:
    *   **Django:** `forms.DateField` with `forms.DateInput(attrs={'type': 'date', ...})`.
    *   **Frontend:** Standard HTML5 date input, enhanced with Tailwind CSS for styling.
*   `asp:TextBox ID="txtEmpCode"` with `AutoCompleteExtender`:
    *   **Django:** `forms.CharField` with `forms.TextInput` and HTMX attributes.
    *   **Frontend:** HTML `input type="text"` with HTMX `hx-get` to a Django autocomplete endpoint (`EmployeeAutocompleteView`), `hx-trigger`, and `hx-target` to display suggestions. Alpine.js can manage the visibility and selection from suggestions.
*   `asp:Button ID="BtnSearch"`:
    *   **Frontend:** HTML `button type="submit"` with HTMX `hx-get` to trigger the table refresh (`GatePassTablePartialView`).
*   `iframe id="Iframe1"`:
    *   **Frontend:** Replaced entirely by a `div` container (`#gatepass-results-container`) that will be swapped via HTMX with the DataTables-powered HTML table from `_gatepass_table.html`.

### Step 4: Generate Django Code

We will create a new Django app, e.g., `hr`, and within it, structure the `gatepass` module.

#### 4.1 Models (`hr/models.py`)

We'll define three models: `Employee`, `GatePass`, and `GatePassDetail`, corresponding to the identified tables. We'll also implement a custom manager for `GatePass` to encapsulate the complex search logic from the ASP.NET code-behind.

```python
from django.db import models
from django.db.models import Q # For complex queries
import datetime # For date parsing if needed, though forms handle this better

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee data, used in autocomplete.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId') # Company ID for filtering
    # Add other fields from tblHR_OfficeStaff if they are relevant to business logic
    # e.g., department, designation etc.

    class Meta:
        managed = False # Django won't manage this table's schema (it exists externally)
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.employee_name} [{self.emp_id}]"
        
    @classmethod
    def get_code_from_name_id(cls, name_id_str):
        """
        Extracts the numeric Employee ID from a string in the format 'Employee Name [ID]'.
        This mirrors the fun.getCode() logic in ASP.NET.
        """
        if '[' in name_id_str and name_id_str.endswith(']'):
            try:
                return int(name_id_str.split('[')[-1][:-1])
            except ValueError:
                return None
        return None

class GatePassManager(models.Manager):
    """
    Custom manager for GatePass to encapsulate the complex search logic
    from the original ASP.NET BtnSearch_Click method.
    """
    def filter_gate_passes(self, from_date=None, to_date=None, employee_code=None, company_id=None, financial_year_id=None):
        queryset = self.get_queryset().select_related('emp').prefetch_related('gatepassdetail_set', 'gatepassdetail_set__other_emp')
        
        # Apply company and financial year filters from session context
        if company_id:
            queryset = queryset.filter(comp_id=company_id)
        if financial_year_id:
            queryset = queryset.filter(fin_year_id=financial_year_id)

        # Apply date range filter (assuming FromDate in GatePassDetail is the primary filter)
        if from_date and to_date:
            queryset = queryset.filter(gatepassdetail__from_date__range=(from_date, to_date))
        
        # Apply employee code filter, mimicking the complex OR logic from ASP.NET
        # Check if EmpId matches self_emp or other_emp in gatepass details
        if employee_code:
            queryset = queryset.filter(
                Q(emp__emp_id=employee_code) | Q(gatepassdetail__other_emp__emp_id=employee_code)
            ).distinct() # Use distinct to avoid duplicate GatePass records if employee matches multiple details

        return queryset.order_by('-sys_date', '-id') # Order by latest system date, then by ID for consistency

class GatePass(models.Model):
    """
    Maps to tblGate_Pass, representing the master record of a gate pass.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    sys_date = models.DateTimeField(db_column='SysDate')
    authorize = models.BooleanField(db_column='Authorize')
    # ForeignKey to Employee for the "Self Employee"
    emp = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='gate_passes_self', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    gp_no = models.CharField(db_column='GPNo', max_length=50)
    authorized_by = models.CharField(db_column='AuthorizedBy', max_length=255, blank=True, null=True)
    authorize_date = models.DateTimeField(db_column='AuthorizeDate', blank=True, null=True)
    authorize_time = models.CharField(db_column='AuthorizeTime', max_length=50, blank=True, null=True)

    objects = GatePassManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblGate_Pass'
        verbose_name = 'Gate Pass'
        verbose_name_plural = 'Gate Passes'

    def __str__(self):
        return f"GP No: {self.gp_no} (Self: {self.emp.employee_name if self.emp else 'N/A'})"

class GatePassDetail(models.Model):
    """
    Maps to tblGatePass_Details, representing the detail records for a gate pass.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # ForeignKey to GatePass for the master record
    master_pass = models.ForeignKey(GatePass, on_delete=models.DO_NOTHING, db_column='MId')
    from_date = models.DateField(db_column='FromDate')
    from_time = models.CharField(db_column='FromTime', max_length=50)
    to_time = models.CharField(db_column='ToTime', max_length=50)
    type = models.CharField(db_column='Type', max_length=50)
    type_for = models.CharField(db_column='TypeFor', max_length=50)
    reason = models.TextField(db_column='Reason')
    feedback = models.TextField(db_column='Feedback', blank=True, null=True)
    # ForeignKey to Employee for the "Other Employee"
    other_emp = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='gate_passes_other', blank=True, null=True)
    place = models.CharField(db_column='Place', max_length=255)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblGatePass_Details'
        verbose_name = 'Gate Pass Detail'
        verbose_name_plural = 'Gate Pass Details'

    def __str__(self):
        return f"Detail for GP {self.master_pass.gp_no} (From: {self.from_date})"
```

#### 4.2 Forms (`hr/forms.py`)

We'll define a `GatePassSearchForm` for the search criteria and a `GatePassForm` for standard CRUD operations on the `GatePass` model.

```python
from django import forms
from .models import GatePass, Employee

class GatePassSearchForm(forms.Form):
    """
    Form for capturing search criteria for Gate Pass reports.
    This is not a ModelForm as it's for filtering, not creating/updating a model instance.
    """
    from_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    to_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    employee_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            # HTMX attributes for autocomplete
            'hx-get': '/hr/gatepass/employees-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup, with a delay
            'hx-target': '#employee-suggestions', # Target div for suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
        })
    )

class GatePassForm(forms.ModelForm):
    """
    Standard form for creating and updating GatePass records.
    """
    class Meta:
        model = GatePass
        fields = ['gp_no', 'emp', 'sys_date', 'authorize', 'authorized_by', 'authorize_date', 'authorize_time']
        widgets = {
            'gp_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'emp': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'authorized_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Custom validation can be added here if needed, e.g., to ensure date logic is correct.
```

#### 4.3 Views (`hr/views.py`)

We'll define the main search view, a partial view to render the DataTables content via HTMX, an autocomplete view, and the standard CRUD views for `GatePass`.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
import datetime # Used for parsing dates from form, if needed
from .models import GatePass, Employee
from .forms import GatePassForm, GatePassSearchForm

class GatePassPrintView(ListView):
    """
    Main view for the Gate Pass Print/Search page.
    It primarily renders the search form and the container for the HTMX-loaded table.
    The actual table data is loaded by a separate HTMX request to GatePassTablePartialView.
    """
    model = GatePass # Although we return an empty queryset, ListView structure is suitable.
    template_name = 'hr/gatepass/print_search.html'
    context_object_name = 'gate_passes' # Initial context, will be empty

    def get_queryset(self):
        # This view doesn't render the initial table data directly.
        # The table content is loaded via HTMX after the page loads or a search is performed.
        return GatePass.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = GatePassSearchForm() # Pass an empty search form initially
        return context

class GatePassTablePartialView(View):
    """
    HTMX-targeted view to render the filtered Gate Pass table.
    It receives search parameters via GET and uses the GatePassManager to filter data.
    """
    def get(self, request, *args, **kwargs):
        form = GatePassSearchForm(request.GET)
        gate_passes = GatePass.objects.none() # Default empty queryset

        if form.is_valid():
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')
            employee_name_id_str = form.cleaned_data.get('employee_name')

            employee_code = None
            if employee_name_id_str:
                employee_code = Employee.get_code_from_name_id(employee_name_id_str)
            
            # Retrieve CompId and FinYearId from session, mimicking ASP.NET's behavior
            # Defaulting to 1 if not found, adjust as per your session management
            comp_id = request.session.get('compid', 1) 
            fin_year_id = request.session.get('finyear', 1) 

            gate_passes = GatePass.objects.filter_gate_passes(
                from_date=from_date,
                to_date=to_date,
                employee_code=employee_code,
                company_id=comp_id,
                financial_year_id=fin_year_id
            )

        context = {
            'gate_passes': gate_passes,
        }
        # Render the partial table template
        return HttpResponse(render_to_string('hr/gatepass/_gatepass_table.html', context, request))

class EmployeeAutocompleteView(View):
    """
    HTMX-targeted view to provide employee name suggestions for the autocomplete field.
    Returns JSON data as expected by the frontend.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '').strip()
        suggestions = []
        if query:
            comp_id = request.session.get('compid', 1) # Filter by company ID from session
            employees = Employee.objects.filter(
                employee_name__icontains=query,
                comp_id=comp_id
            ).order_by('employee_name')[:15] # Limit results to 15, matching original logic

            suggestions = [f"{emp.employee_name} [{emp.emp_id}]" for emp in employees]
        
        return JsonResponse(suggestions, safe=False) # Return a JSON array

# --- Standard CRUD Views for GatePass (as per modernization guidelines) ---

class GatePassListView(ListView):
    model = GatePass
    template_name = 'hr/gatepass/list.html'
    context_object_name = 'gate_passes'

class GatePassCreateView(CreateView):
    model = GatePass
    form_class = GatePassForm
    template_name = 'hr/gatepass/form.html'
    success_url = reverse_lazy('gatepass_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Gate Pass added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGatePassList' # Custom HTMX event to refresh the list
                }
            )
        return response # Fallback for non-HTMX requests

class GatePassUpdateView(UpdateView):
    model = GatePass
    form_class = GatePassForm
    template_name = 'hr/gatepass/form.html'
    success_url = reverse_lazy('gatepass_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Gate Pass updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGatePassList'
                }
            )
        return response

class GatePassDeleteView(DeleteView):
    model = GatePass
    template_name = 'hr/gatepass/confirm_delete.html'
    success_url = reverse_lazy('gatepass_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Gate Pass deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGatePassList'
                }
            )
        return response
```

#### 4.4 Templates (`hr/templates/hr/gatepass/`)

We'll create the main search template, a partial for the DataTables content, and the generic CRUD templates.

##### `hr/templates/hr/gatepass/print_search.html` (Main Search Page)

This template provides the search form and the container for the results.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Gate Pass - Print / Search</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'gatepass_table' %}" 
              hx-target="#gatepass-results-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date:</label>
                    {{ form.from_date }}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date:</label>
                    {{ form.to_date }}
                </div>
                <div class="relative">
                    <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name:</label>
                    {{ form.employee_name }}
                    <!-- Suggestions will be swapped here by HTMX -->
                    <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto w-full"></div>
                </div>
                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm w-full md:w-auto">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading indicator for HTMX requests -->
    <div id="loading-indicator" class="text-center py-4 htmx-indicator" style="display:none;">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading search results...</p>
    </div>

    <!-- Container for the HTMX-loaded DataTables content -->
    <div id="gatepass-results-container" 
         hx-trigger="load, searchGatePasses from:body" 
         hx-get="{% url 'gatepass_table' %}" 
         hx-swap="innerHTML">
        <!-- Initial content before load/search -->
        <div class="text-center py-8">
            <p class="text-gray-500">Enter search criteria and click 'Search' to view gate passes.</p>
        </div>
    </div>

    <!-- Universal Modal for Create/Update/Delete operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
            <!-- Modal content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be initialized here if more complex UI state is needed
    });

    // Re-initialize DataTables after HTMX swaps the table content
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'gatepass-results-container') {
            // Destroy existing DataTable instance if it exists
            if ($.fn.DataTable.isDataTable('#gatePassTable')) {
                $('#gatePassTable').DataTable().destroy();
            }
            // Re-initialize DataTable
            $('#gatePassTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Handle employee autocomplete suggestions
    document.getElementById('id_employee_name').addEventListener('htmx:afterOnLoad', function(evt) {
        const suggestionsContainer = document.getElementById('employee-suggestions');
        try {
            const data = JSON.parse(evt.detail.xhr.responseText);
            if (data && data.length > 0) {
                suggestionsContainer.innerHTML = data.map(item => `
                    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer" 
                         onclick="document.getElementById('id_employee_name').value='${item}'; document.getElementById('employee-suggestions').innerHTML='';">
                        ${item}
                    </div>
                `).join('');
                suggestionsContainer.classList.remove('hidden');
            } else {
                suggestionsContainer.innerHTML = '';
                suggestionsContainer.classList.add('hidden');
            }
        } catch (e) {
            console.error("Error parsing autocomplete response:", e);
            suggestionsContainer.innerHTML = '';
            suggestionsContainer.classList.add('hidden');
        }
    });

    // Hide suggestions when input loses focus (unless clicking a suggestion)
    document.getElementById('id_employee_name').addEventListener('focusout', function(evt) {
        // A small delay to allow click event on suggestion to fire first
        setTimeout(() => {
            const suggestionsContainer = document.getElementById('employee-suggestions');
            if (!suggestionsContainer.contains(document.activeElement)) {
                suggestionsContainer.classList.add('hidden');
                suggestionsContainer.innerHTML = '';
            }
        }, 100);
    });

    document.getElementById('id_employee_name').addEventListener('focus', function(evt) {
         const suggestionsContainer = document.getElementById('employee-suggestions');
         if (suggestionsContainer.innerHTML.trim() !== '') {
            suggestionsContainer.classList.remove('hidden');
         }
    });
</script>
{% endblock %}
```

##### `hr/templates/hr/gatepass/_gatepass_table.html` (Partial for DataTables Content)

This template renders the table structure that DataTables will enhance. It's designed to be loaded dynamically via HTMX.

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="gatePassTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GP No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Self Employee</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Other Employee (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for gp in gate_passes %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.gp_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.emp.employee_name|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% for detail in gp.gatepassdetail_set.all %}
                        {{ detail.from_date|date:"d-M-Y" }}<br>
                    {% empty %}
                        N/A
                    {% endfor %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% for detail in gp.gatepassdetail_set.all %}
                        {{ detail.type }}<br>
                    {% empty %}
                        N/A
                    {% endfor %}
                </td>
                <td class="py-3 px-4 text-sm text-gray-900 max-w-xs truncate">{{ gp.gatepassdetail_set.first.reason|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.gatepassdetail_set.first.other_emp.employee_name|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.gatepassdetail_set.first.place|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-1"
                        hx-get="{% url 'gatepass_edit' gp.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'gatepass_delete' gp.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 px-4 text-center text-sm text-gray-500">No gate passes found for the selected criteria.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization is handled by the parent template's htmx:afterSwap event listener -->
```

##### `hr/templates/hr/gatepass/list.html` (Placeholder List View for CRUD)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Gate Passes</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'gatepass_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Gate Pass
        </button>
    </div>
    
    <div id="gatepassTable-container"
         hx-trigger="load, refreshGatePassList from:body"
         hx-get="{% url 'gatepass_table' %}" {# Reuse the partial table for general list #}
         hx-swap="innerHTML">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });

    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'gatepassTable-container') {
            // Re-initialize DataTables after HTMX swap
            if ($.fn.DataTable.isDataTable('#gatePassTable')) {
                $('#gatePassTable').DataTable().destroy();
            }
            $('#gatePassTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });
</script>
{% endblock %}
```

##### `hr/templates/hr/gatepass/form.html` (Partial for Create/Update Form)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Gate Pass</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" handles success with HX-Trigger header #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

##### `hr/templates/hr/gatepass/confirm_delete.html` (Partial for Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-5">Are you sure you want to delete Gate Pass "<strong>{{ object.gp_no }}</strong>"?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`hr/urls.py`)

Define the URL patterns for all views.

```python
from django.urls import path
from .views import (
    GatePassPrintView, 
    GatePassTablePartialView, 
    EmployeeAutocompleteView,
    GatePassListView, 
    GatePassCreateView, 
    GatePassUpdateView, 
    GatePassDeleteView
)

urlpatterns = [
    # Main Gate Pass Print/Search functionality
    path('gatepass/print/', GatePassPrintView.as_view(), name='gatepass_print_search'),
    path('gatepass/table/', GatePassTablePartialView.as_view(), name='gatepass_table'),
    path('gatepass/employees-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Standard CRUD operations for GatePass (as per modernization guidelines)
    path('gatepass/', GatePassListView.as_view(), name='gatepass_list'),
    path('gatepass/add/', GatePassCreateView.as_view(), name='gatepass_add'),
    path('gatepass/edit/<int:pk>/', GatePassUpdateView.as_view(), name='gatepass_edit'),
    path('gatepass/delete/<int:pk>/', GatePassDeleteView.as_view(), name='gatepass_delete'),
]
```

#### 4.6 Tests (`hr/tests.py`)

Comprehensive unit tests for models and integration tests for views, covering search logic, autocomplete, and CRUD operations.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
import datetime
from .models import GatePass, Employee, GatePassDetail
from .forms import GatePassSearchForm, GatePassForm

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.employee1 = Employee.objects.create(emp_id=101, employee_name='John Doe', comp_id=1)
        cls.employee2 = Employee.objects.create(emp_id=102, employee_name='Jane Smith', comp_id=1)
        cls.employee3 = Employee.objects.create(emp_id=201, employee_name='Bob Johnson', comp_id=2)

    def test_employee_creation(self):
        obj = Employee.objects.get(emp_id=101)
        self.assertEqual(obj.employee_name, 'John Doe')
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(str(obj), 'John Doe [101]')

    def test_get_code_from_name_id(self):
        self.assertEqual(Employee.get_code_from_name_id('John Doe [101]'), 101)
        self.assertIsNone(Employee.get_code_from_name_id('John Doe'))
        self.assertIsNone(Employee.get_code_from_name_id('John Doe [ABC]'))
        self.assertIsNone(Employee.get_code_from_name_id(''))
        self.assertIsNone(Employee.get_code_from_name_id(None)) # Test None input

class GatePassModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.employee1 = Employee.objects.create(emp_id=101, employee_name='John Doe', comp_id=1)
        cls.employee2 = Employee.objects.create(emp_id=102, employee_name='Jane Smith', comp_id=1)

        # GatePass 1 (Self: John Doe, Detail: no other employee)
        cls.gp1 = GatePass.objects.create(
            id=1, session_id='S1', comp_id=1, fin_year_id=1, gp_no='GP001',
            sys_date=timezone.now() - datetime.timedelta(days=15), authorize=True, emp=cls.employee1
        )
        cls.gpd1 = GatePassDetail.objects.create(
            id=1, master_pass=cls.gp1, from_date=datetime.date(2023, 1, 15),
            from_time='09:00', to_time='17:00', type='Official', type_for='Meeting',
            reason='Client meeting', place='Client Office', other_emp=None
        )

        # GatePass 2 (Self: Jane Smith, Detail: Other: John Doe)
        cls.gp2 = GatePass.objects.create(
            id=2, session_id='S2', comp_id=1, fin_year_id=1, gp_no='GP002',
            sys_date=timezone.now() - datetime.timedelta(days=10), authorize=False, emp=cls.employee2
        )
        cls.gpd2 = GatePassDetail.objects.create(
            id=2, master_pass=cls.gp2, from_date=datetime.date(2023, 1, 5),
            from_time='10:00', to_time='12:00', type='Personal', type_for='Doctor',
            reason='Doctor visit', place='Clinic', other_emp=cls.employee1 # John Doe as other employee
        )

        # GatePass 3 (Self: None, Detail: Other: Jane Smith)
        cls.gp3 = GatePass.objects.create(
            id=3, session_id='S3', comp_id=1, fin_year_id=1, gp_no='GP003',
            sys_date=timezone.now() - datetime.timedelta(days=20), authorize=True, emp=None
        )
        cls.gpd3 = GatePassDetail.objects.create(
            id=3, master_pass=cls.gp3, from_date=datetime.date(2023, 1, 1),
            from_time='08:00', to_time='10:00', type='Official', type_for='Vendor',
            reason='Vendor meeting', place='Vendor Site', other_emp=cls.employee2 # Jane Smith as other employee
        )

        # GatePass 4 (Different Company)
        cls.gp4 = GatePass.objects.create(
            id=4, session_id='S4', comp_id=2, fin_year_id=1, gp_no='GP004',
            sys_date=timezone.now() - datetime.timedelta(days=5), authorize=True, emp=cls.employee3
        )
        cls.gpd4 = GatePassDetail.objects.create(
            id=4, master_pass=cls.gp4, from_date=datetime.date(2023, 1, 20),
            from_time='11:00', to_time='13:00', type='Official', type_for='Client',
            reason='New client meet', place='Remote', other_emp=None
        )

    def test_gatepass_creation(self):
        self.assertEqual(self.gp1.gp_no, 'GP001')
        self.assertEqual(self.gp1.emp, self.employee1)
        self.assertEqual(str(self.gp1), 'GP No: GP001 (Self: John Doe)')

    def test_gatepassdetail_creation(self):
        self.assertEqual(self.gpd1.master_pass, self.gp1)
        self.assertEqual(self.gpd1.from_date, datetime.date(2023, 1, 15))
        self.assertIsNone(self.gpd1.other_emp)
        self.assertEqual(str(self.gpd1), 'Detail for GP GP001 (From: 2023-01-15)')

    def test_filter_gate_passes_no_filter(self):
        gate_passes = GatePass.objects.filter_gate_passes(company_id=1, financial_year_id=1)
        self.assertEqual(gate_passes.count(), 3) # GP1, GP2, GP3
        self.assertIn(self.gp1, gate_passes)
        self.assertIn(self.gp2, gate_passes)
        self.assertIn(self.gp3, gate_passes)
        self.assertNotIn(self.gp4, gate_passes) # Excluded by company_id

    def test_filter_gate_passes_by_date(self):
        from_date = datetime.date(2023, 1, 10)
        to_date = datetime.date(2023, 1, 20)
        gate_passes = GatePass.objects.filter_gate_passes(
            from_date=from_date, to_date=to_date, company_id=1, financial_year_id=1
        )
        self.assertEqual(gate_passes.count(), 1) # Only GP001 (Jan 15)
        self.assertIn(self.gp1, gate_passes)
        self.assertNotIn(self.gp2, gate_passes)
        self.assertNotIn(self.gp3, gate_passes)

    def test_filter_gate_passes_by_self_employee_code(self):
        gate_passes = GatePass.objects.filter_gate_passes(
            employee_code=101, company_id=1, financial_year_id=1
        )
        # John Doe (101) is self_emp for gp1, and other_emp for gp2
        self.assertEqual(gate_passes.count(), 2)
        self.assertIn(self.gp1, gate_passes)
        self.assertIn(self.gp2, gate_passes)
        self.assertNotIn(self.gp3, gate_passes)

    def test_filter_gate_passes_by_other_employee_code(self):
        gate_passes = GatePass.objects.filter_gate_passes(
            employee_code=102, company_id=1, financial_year_id=1
        )
        # Jane Smith (102) is self_emp for gp2, and other_emp for gp3
        self.assertEqual(gate_passes.count(), 2)
        self.assertIn(self.gp2, gate_passes)
        self.assertIn(self.gp3, gate_passes)
        self.assertNotIn(self.gp1, gate_passes)

    def test_filter_gate_passes_by_non_existent_employee(self):
        gate_passes = GatePass.objects.filter_gate_passes(
            employee_code=999, company_id=1, financial_year_id=1
        )
        self.assertEqual(gate_passes.count(), 0)

    def test_filter_gate_passes_no_company_or_fin_year(self):
        # Should return all (from all companies/fin years) if filters are None
        gate_passes = GatePass.objects.filter_gate_passes()
        self.assertEqual(gate_passes.count(), 4) # All 4 gate passes

class GatePassViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.employee_comp1 = Employee.objects.create(emp_id=100, employee_name='Test Emp Comp1', comp_id=1)
        cls.employee_comp2 = Employee.objects.create(emp_id=200, employee_name='Test Emp Comp2', comp_id=2)

        cls.gp_comp1 = GatePass.objects.create(
            id=1, session_id='S_Test1', comp_id=1, fin_year_id=1, gp_no='GPT001',
            sys_date=timezone.now(), authorize=True, emp=cls.employee_comp1
        )
        GatePassDetail.objects.create(
            id=1, master_pass=cls.gp_comp1, from_date=datetime.date(2023, 2, 1),
            from_time='09:00', to_time='17:00', type='Official', type_for='Testing',
            reason='Testing reason', place='Test Place', other_emp=None
        )

        cls.gp_comp2 = GatePass.objects.create(
            id=2, session_id='S_Test2', comp_id=2, fin_year_id=1, gp_no='GPT002',
            sys_date=timezone.now() - datetime.timedelta(days=5), authorize=False, emp=cls.employee_comp2
        )
        GatePassDetail.objects.create(
            id=2, master_pass=cls.gp_comp2, from_date=datetime.date(2023, 2, 5),
            from_time='10:00', to_time='18:00', type='Personal', type_for='Family',
            reason='Family reason', place='Home', other_emp=cls.employee_comp1 # Comp1 employee for Comp2 GP
        )

    def setUp(self):
        self.client = Client()
        # Simulate session variables
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 1
        session.save()

    def test_print_search_view_get(self):
        response = self.client.get(reverse('gatepass_print_search'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/gatepass/print_search.html')
        self.assertIsInstance(response.context['form'], GatePassSearchForm)
        self.assertContains(response, 'Enter search criteria and click &#x27;Search&#x27; to view gate passes.')

    def test_gatepass_table_partial_view_no_filter(self):
        # Initial load will return no results because no search params are passed
        response = self.client.get(reverse('gatepass_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/gatepass/_gatepass_table.html')
        self.assertContains(response, 'No gate passes found for the selected criteria.')

    def test_gatepass_table_partial_view_with_self_employee_filter(self):
        response = self.client.get(reverse('gatepass_table'), {'employee_name': 'Test Emp Comp1 [100]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GPT001') # Matched by self_emp
        self.assertContains(response, 'GPT002') # Matched by other_emp for GPT002

    def test_gatepass_table_partial_view_with_date_filter(self):
        response = self.client.get(reverse('gatepass_table'), {
            'from_date': '2023-01-01',
            'to_date': '2023-02-03' # Captures GPT001 (Feb 1st)
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GPT001')
        self.assertNotContains(response, 'GPT002') # Outside date range

    def test_gatepass_table_partial_view_no_results(self):
        response = self.client.get(reverse('gatepass_table'), {'employee_name': 'NonExistent Emp [999]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No gate passes found for the selected criteria.')

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'Test Emp Comp1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['Test Emp Comp1 [100]'])

        # Test with no query
        response = self.client.get(reverse('employee_autocomplete'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

        # Test with a different company's employee (should not show if session compid is 1)
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'Test Emp Comp2'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), []) # Should be empty as compid=1 in session

    # --- Standard CRUD View Tests for GatePass ---

    def test_list_view_get(self):
        response = self.client.get(reverse('gatepass_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/gatepass/list.html')
        self.assertContains(response, 'Gate Passes') # Title check

    def test_create_view_get(self):
        response = self.client.get(reverse('gatepass_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/gatepass/form.html')
        self.assertIsInstance(response.context['form'], GatePassForm)
        self.assertContains(response, 'Add Gate Pass')

    def test_create_view_post_htmx_success(self):
        initial_count = GatePass.objects.count()
        new_employee = Employee.objects.create(emp_id=300, employee_name='New Test User', comp_id=1)
        data = {
            'gp_no': 'NEWGP001',
            'emp': new_employee.emp_id,
            'sys_date': timezone.now().isoformat(),
            'authorize': 'on', # Checkbox value for True
            'authorized_by': 'Test Admin',
            'authorize_date': timezone.now().isoformat(),
            'authorize_time': '10:00',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('gatepass_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(GatePass.objects.count(), initial_count + 1)
        self.assertTrue(GatePass.objects.filter(gp_no='NEWGP001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGatePassList', response.headers['HX-Trigger'])

    def test_create_view_post_validation_error(self):
        initial_count = GatePass.objects.count()
        data = { # Missing required fields, e.g., 'gp_no' and 'sys_date'
            'authorize': 'on',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('gatepass_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertEqual(GatePass.objects.count(), initial_count)
        self.assertContains(response, 'This field is required.') # Check for form errors
        self.assertTemplateUsed(response, 'hr/gatepass/form.html')

    def test_update_view_get(self):
        response = self.client.get(reverse('gatepass_edit', args=[self.gp_comp1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/gatepass/form.html')
        self.assertIsInstance(response.context['form'], GatePassForm)
        self.assertEqual(response.context['object'], self.gp_comp1)
        self.assertContains(response, 'Edit Gate Pass')

    def test_update_view_post_htmx_success(self):
        updated_gp_no = 'GPT001_UPDATED'
        data = {
            'gp_no': updated_gp_no,
            'emp': self.employee_comp1.emp_id,
            'sys_date': timezone.now().isoformat(),
            'authorize': 'on',
            'authorized_by': 'Updated Admin',
            'authorize_date': timezone.now().isoformat(),
            'authorize_time': '12:00',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('gatepass_edit', args=[self.gp_comp1.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.gp_comp1.refresh_from_db()
        self.assertEqual(self.gp_comp1.gp_no, updated_gp_no)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGatePassList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(reverse('gatepass_delete', args=[self.gp_comp1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/gatepass/confirm_delete.html')
        self.assertEqual(response.context['object'], self.gp_comp1)
        self.assertContains(response, f'Are you sure you want to delete Gate Pass "<strong>{self.gp_comp1.gp_no}</strong>"?')

    def test_delete_view_post_htmx_success(self):
        # Create a temporary object to delete
        temp_gp = GatePass.objects.create(
            id=999, session_id='S_Temp', comp_id=1, fin_year_id=1, gp_no='TEMP001',
            sys_date=timezone.now(), authorize=False, emp=self.employee_comp1
        )
        initial_count = GatePass.objects.count()
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('gatepass_delete', args=[temp_gp.id]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GatePass.objects.count(), initial_count - 1)
        self.assertFalse(GatePass.objects.filter(id=999).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGatePassList', response.headers['HX-Trigger'])
```

### Step 5: HTMX and Alpine.js Integration

The provided code already demonstrates significant HTMX integration:

*   **Dynamic Search and Table Updates:** The main `print_search.html` uses `hx-get` to `gatepass_table` which dynamically loads the `_gatepass_table.html` content. The `hx-trigger="load, searchGatePasses from:body"` ensures the table loads on page load and can be refreshed by a custom event.
*   **Autocomplete:** The `employee_name` input uses `hx-get` to `employee_autocomplete` with `hx-trigger` and `hx-target` to fetch and display suggestions. Simple `onclick` handlers on suggestions auto-fill the input.
*   **CRUD Modals:** All CRUD actions (`add`, `edit`, `delete`) are initiated by `hx-get` requests that load the respective forms/confirmations into a modal (`#modalContent`).
*   **Form Submission:** Forms within the modal use `hx-post` with `hx-swap="none"` and the view responds with `HX-Trigger: refreshGatePassList` to update the main table without a full page refresh.
*   **DataTables:** JavaScript in `print_search.html` (within `extra_js`) ensures DataTables is initialized on the `gatePassTable` element after HTMX swaps in the content, providing client-side features.

**Alpine.js (Minimal Use in this Example):**
While Alpine.js is recommended, for this specific module's current functionality, it's not strictly necessary for complex UI state management as HTMX handles most of the dynamic content. The provided `_ = "on click add .is-active to #modal"` syntax uses `_hyperscript`, which is often used in conjunction with Alpine.js or as a simpler alternative. For a more complex UI (e.g., custom date pickers, form interactivity that doesn't involve server roundtrips), Alpine.js would be integrated via `x-data` attributes on HTML elements.

**CDN links for HTMX, Alpine.js, and DataTables** should be present in your `core/base.html` template.

```html
<!-- Example in core/base.html (DO NOT include in the output) -->
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script src="https://unpkg.com/htmx.org/dist/ext/response-targets.js"></script>
<script src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js" defer></script>
<script src="https://unpkg.com/hyperscript.org@0.9.12"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
<script type="text/javascript" charset="utf8" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
```

This comprehensive plan transforms the legacy ASP.NET Gate Pass Print module into a modern, efficient, and maintainable Django application, leveraging automated patterns and best practices for future scalability and development.