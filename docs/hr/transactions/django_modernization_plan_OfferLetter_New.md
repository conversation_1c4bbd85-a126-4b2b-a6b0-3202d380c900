Here's a comprehensive Django modernization plan for your ASP.NET application, designed for AI-assisted automation and presented in plain English for all stakeholders.

## ASP.NET to Django Conversion Script: Offer Letter Module

This plan outlines the systematic migration of your existing ASP.NET Offer Letter functionality to a modern Django-based solution. Our focus is on leveraging automation, ensuring clean architecture, and providing clear, actionable steps.

### Business Value of this Modernization

Transitioning to Django will bring significant benefits:

*   **Cost Reduction:** Open-source nature eliminates licensing fees, and Python's efficiency can reduce development and maintenance costs.
*   **Scalability & Performance:** Django's robust framework and Python's ecosystem provide excellent scalability for future growth and improved application performance.
*   **Maintainability & Reliability:** Adherence to best practices (Fat Models, Thin Views, clear separation of concerns) makes the code easier to understand, debug, and maintain, leading to fewer errors.
*   **Developer Productivity:** Django's "batteries included" philosophy, combined with HTMX and Alpine.js, accelerates development cycles for new features.
*   **Modern User Experience:** HTMX and Alpine.js deliver dynamic, responsive interfaces without the complexity of traditional JavaScript frameworks, enhancing user satisfaction.
*   **Future-Proofing:** Moving away from legacy ASP.NET ensures your application remains compatible with modern technologies and security standards.
*   **Enhanced Data Integrity:** Stronger model-level validation and ORM (Object-Relational Mapper) reduce data inconsistencies.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code. This foundational step provides the blueprint for our Django models.

**Instructions:**

From the ASP.NET `SqlDataSource` definitions and `fun.select`/`fun.insert` commands, we identify the following key tables and their inferred columns. Note that the original code implicitly uses `Id` for primary keys and relationships.

*   **`tblHR_Designation`**:
    *   `Id` (PK, int)
    *   `Designation` (varchar) - derived from `Symbol + ' - ' + Type`
*   **`tblHR_DutyHour`**:
    *   `Id` (PK, int)
    *   `Hours` (varchar)
*   **`tblHR_OTHour`**:
    *   `Id` (PK, int)
    *   `Hours` (varchar)
*   **`tblHR_OverTime`**:
    *   `Id` (PK, int)
    *   `Description` (varchar)
*   **`tblHR_EmpType`**:
    *   `Id` (PK, int)
    *   `Description` (varchar)
*   **`tblHR_IncludesIn`**:
    *   `Id` (PK, int)
    *   `IncludesIn` (varchar, e.g., 'CTC', 'Take Home', 'Both')
*   **`tblHR_PF_Slab`**:
    *   `Id` (PK, int)
    *   `PFEmployee` (float)
    *   `PFCompany` (float)
    *   `Active` (bool/bit)
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (PK, int)
    *   `EmployeeName` (varchar)
*   **`tblHR_Offer_Master` (Main Offer Letter Data)**:
    *   `OfferId` (PK, int)
    *   `SysDate` (datetime)
    *   `SysTime` (datetime)
    *   `FinYearId` (int)
    *   `CompId` (int)
    *   `SessionId` (varchar)
    *   `Title` (varchar)
    *   `EmployeeName` (varchar)
    *   `TypeOf` (int, e.g., 1=SAPL, 2=NEHA)
    *   `StaffType` (int, FK to `tblHR_EmpType`)
    *   `salary` (float) - Gross Salary
    *   `DutyHrs` (int, FK to `tblHR_DutyHour`)
    *   `OTHrs` (int, FK to `tblHR_OTHour`)
    *   `OverTime` (int, FK to `tblHR_OverTime`)
    *   `Address` (varchar)
    *   `ContactNo` (varchar)
    *   `EmailId` (varchar)
    *   `InterviewedBy` (int, FK to `tblHR_OfficeStaff` EmpId)
    *   `AuthorizedBy` (int, FK to `tblHR_OfficeStaff` EmpId)
    *   `ReferenceBy` (varchar)
    *   `Designation` (int, FK to `tblHR_Designation`)
    *   `ExGratia` (float)
    *   `VehicleAllowance` (float)
    *   `LTA` (float)
    *   `Loyalty` (float) - Loyalty Benefits
    *   `PaidLeaves` (float)
    *   `HeaderText` (text)
    *   `FooterText` (text)
    *   `Remarks` (text)
    *   `Bonus` (float)
    *   `AttBonusPer1` (float) - Attendance Bonus %1
    *   `AttBonusPer2` (float) - Attendance Bonus %2
    *   `PFEmployee` (float) - PF Employee %
    *   `PFCompany` (float) - PF Company %
*   **`tblHR_Offer_Accessories` (Offer Letter Line Items)**:
    *   `Id` (PK, int)
    *   `MId` (int, FK to `tblHR_Offer_Master` OfferId)
    *   `Perticulars` (varchar)
    *   `Qty` (float)
    *   `Amount` (float)
    *   `IncludesIn` (int, FK to `tblHR_IncludesIn`)

### Step 2: Identify Backend Functionality

**Task:** Determine the core Create, Read, Update, and Delete (CRUD) operations, along with business logic and validations.

**Instructions:**

*   **Create (Add New Offer Letter):**
    *   Triggered by the `BtnSubmit` button.
    *   Inserts into `tblHR_Offer_Master`.
    *   Iterates through and inserts temporary accessory data from `tblHR_Offer_Accessories_Temp` into `tblHR_Offer_Accessories`.
    *   Deletes temporary accessory data.
    *   Includes validation for required fields (`txtFooter`, `txtHeader`, `TxtGrossSalry`, `TxtName`, `TxtAddress`, `DrpEmpTypeOf`).
*   **Read (Display Data):**
    *   Initial form load retrieves PF slab values from `tblHR_PF_Slab`.
    *   Dropdowns are populated from various `tblHR_` lookup tables (Designation, DutyHour, etc.).
    *   `GridView1` displays "Offer Accessories" from `tblHR_Offer_Accessories_Temp`.
*   **Update (Modify Existing Offer Letter):**
    *   The provided code is primarily for "New" offers, but the "Calculate" button updates displayed salary components dynamically without a full save. The submit button saves the full record.
    *   For the accessories `GridView`, the add (`Add`/`Add1` commands) and delete (`RowDeleting`) operations dynamically modify the temporary accessories and trigger a recalculation.
*   **Delete (Remove Offer Accessories):**
    *   The `LinkButton` in `GridView1` (with `CommandName="Delete"`) triggers `GridView1_RowDeleting` to remove items from `tblHR_Offer_Accessories_Temp`.
*   **Business Logic & Validation:**
    *   **Salary Calculation (`CalSalary()`):** This is a complex function that calculates Basic, DA, HRA, Convenience, Education, Medical, LTA, Ex Gratia, Loyalty Benefits, Vehicle Allowance, Paid Leaves, PF (Employee/Company), P. Tax, Attendance Bonus, Bonus, Gratuity, Take Home Salary, and CTC. It considers `Employee Type` (`DrpEmpTypeOf`) and `Staff Type` (`DrpEmpType`) for specific rules. Accessory totals are also included.
    *   **Dynamic Field Behavior:** `DrpEmpTypeOf_SelectedIndexChanged` and `DrpEmpType_SelectedIndexChanged` adjust values (`AttBonus1`, `AttBonus2`) and enable/disable PF/Bonus fields, triggering `CalSalary()`.
    *   **Auto-Complete:** `GetCompletionList` (WebMethod) for Interviewed By and Authorized By fields.
    *   **Validation:** Extensive use of `RequiredFieldValidator` and `RegularExpressionValidator` for numeric and email formats.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles to map them to modern Django templates with HTMX and Alpine.js.

**Instructions:**

*   **Master Page / Layout:** `MasterPage.master` maps to `core/base.html` in Django, providing the overall layout.
*   **Form Input:**
    *   `asp:DropDownList` (`DrpDesignation`, `DrpTitle`, `DrpDutyHrs`, `DrpOTHrs`, `DrpOvertime`, `DrpEmpTypeOf`, `DrpEmpType`, `IncludeIn`): Will be replaced by Django `forms.ChoiceField` or `forms.ModelChoiceField` rendered as `<select>` elements.
    *   `asp:TextBox` (`TxtName`, `TxtContactNo`, `TxtAddress`, `TxtEmail`, `Txtinterviewedby`, `TxtAuthorizedby`, `TxtReferencedby`, `TxtGrossSalry`, `txtHeader`, `txtFooter`, `TxtRemarks`, `TxtGLTA`, `TxtGGratia`, `TxtAnnLOYAlty`, `TxtGVehAll`, `TxtAnnpaidleaves`, `txtPFEmployee`, `txtPFCompany`, `txtAttB1`, `txtAttB2`, `txtBonus`, `txtPerticulars`, `txtAccQty`, `txtAccAmount`): Will be Django `forms.CharField`, `forms.DecimalField`, `forms.EmailField`, `forms.Textarea` rendered as `<input type="text">` or `<textarea>` elements.
    *   `asp:Label` (all `TxtG...`, `TxtAn...`, `lblTH`, `lblCTC` etc.): Used for displaying calculated salary components. These will be rendered directly from context variables or updated via HTMX.
*   **Data Display (List View):**
    *   `asp:GridView` (`GridView1`): This is a primary candidate for replacement with an HTMX-driven DataTables component, providing client-side search, sort, and pagination.
*   **Actions:**
    *   `asp:Button` (`ButtonSubmit`, `BtnSubmit`, `btnInsert`): Will be standard `<button>` elements with `hx-post` or `hx-get` attributes for HTMX interactions.
    *   `asp:LinkButton` (Delete in `GridView1`): Will be a standard `<button>` or `<a>` with `hx-delete` or `hx-post` for HTMX.
*   **Dynamic Features:**
    *   `AjaxControlToolkit:AutoCompleteExtender`: Replaced by HTMX for auto-complete suggestions.
    *   `AutoPostBack="True"` on `DrpEmpTypeOf` and `DrpEmpType`: Replaced by HTMX `hx-post` to trigger partial updates and re-calculations.
    *   `ClientScript.RegisterStartupScript`: Replaced by Django messages framework (for success/error banners) or HTMX-triggered UI elements.
*   **Styling:** The ASP.NET `CssClass="box3"` and inline styles will be replaced by Tailwind CSS classes.

---

### Step 4: Generate Django Code

We will create a Django application named `hrmodules` to host the Offer Letter functionality.

#### 4.1 Models (`hrmodules/models.py`)

**Task:** Create Django models that map to the identified database tables. Business logic related to salary calculation will be encapsulated here.

**Instructions:**

*   Define all identified models.
*   Use `managed = False` and `db_table` to map to existing tables.
*   For FKs, ensure `db_column` is used if the column name is not Django-standard `_id`.
*   Implement the complex `CalSalary()` logic as a method within the `OfferLetter` model. This method will take current field values and return a dictionary of all calculated components.

```python
# hrmodules/models.py
from django.db import models
from django.db.models import F, Sum, Q # Import for potential future aggregates/complex lookups

# --- Lookup Models ---
class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    designation = models.CharField(db_column='Designation', max_length=255) # Assuming Symbol + Type
    
    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.designation

class DutyHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.CharField(db_column='Hours', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'Duty Hour'
        verbose_name_plural = 'Duty Hours'

    def __str__(self):
        return self.hours

class OTHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.CharField(db_column='Hours', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_OTHour'
        verbose_name = 'OT Hour'
        verbose_name_plural = 'OT Hours'

    def __str__(self):
        return self.hours

class OverTime(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OverTime'
        verbose_name = 'Overtime Option'
        verbose_name_plural = 'Overtime Options'

    def __str__(self):
        return self.description

class EmployeeType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_EmpType'
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'

    def __str__(self):
        return self.description

class IncludesIn(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    includes_in = models.CharField(db_column='IncludesIn', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_IncludesIn'
        verbose_name = 'Includes In Category'
        verbose_name_plural = 'Includes In Categories'

    def __str__(self):
        return self.includes_in

class PF_Slab(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pf_employee = models.FloatField(db_column='PFEmployee')
    pf_company = models.FloatField(db_column='PFCompany')
    active = models.BooleanField(db_column='Active')

    class Meta:
        managed = False
        db_table = 'tblHR_PF_Slab'
        verbose_name = 'PF Slab'
        verbose_name_plural = 'PF Slabs'

    def __str__(self):
        return f"PF Employee: {self.pf_employee}%, PF Company: {self.pf_company}%"

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

# --- Main Transactional Models ---
class OfferLetter(models.Model):
    offer_id = models.AutoField(db_column='OfferId', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    title = models.CharField(db_column='Title', max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    type_of = models.IntegerField(db_column='TypeOf') # 1 for SAPL, 2 for NEHA
    staff_type = models.ForeignKey(EmployeeType, models.DO_NOTHING, db_column='StaffType', blank=True, null=True)
    salary = models.FloatField(db_column='salary') # Gross Salary
    duty_hrs = models.ForeignKey(DutyHour, models.DO_NOTHING, db_column='DutyHrs', blank=True, null=True)
    ot_hrs = models.ForeignKey(OTHour, models.DO_NOTHING, db_column='OTHrs', blank=True, null=True)
    overtime_applicable = models.ForeignKey(OverTime, models.DO_NOTHING, db_column='OverTime', blank=True, null=True)
    address = models.TextField(db_column='Address')
    contact_no = models.CharField(db_column='ContactNo', max_length=255)
    email_id = models.CharField(db_column='EmailId', max_length=255)
    interviewed_by = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='InterviewedBy', blank=True, null=True, related_name='interviewed_offers')
    authorized_by = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='AuthorizedBy', blank=True, null=True, related_name='authorized_offers')
    reference_by = models.CharField(db_column='ReferenceBy', max_length=255, blank=True, null=True)
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    ex_gratia = models.FloatField(db_column='ExGratia', default=0.0)
    vehicle_allowance = models.FloatField(db_column='VehicleAllowance', default=0.0)
    lta = models.FloatField(db_column='LTA', default=0.0)
    loyalty = models.FloatField(db_column='Loyalty', default=0.0)
    paid_leaves = models.FloatField(db_column='PaidLeaves', default=0.0)
    header_text = models.TextField(db_column='HeaderText')
    footer_text = models.TextField(db_column='FooterText')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    bonus = models.FloatField(db_column='Bonus', default=0.0)
    att_bonus_per1 = models.FloatField(db_column='AttBonusPer1', default=0.0)
    att_bonus_per2 = models.FloatField(db_column='AttBonusPer2', default=0.0)
    pf_employee_perc = models.FloatField(db_column='PFEmployee', default=0.0) # Renamed to avoid clash with method
    pf_company_perc = models.FloatField(db_column='PFCompany', default=0.0) # Renamed to avoid clash with method

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Letter'
        verbose_name_plural = 'Offer Letters'

    def __str__(self):
        return f"Offer for {self.employee_name} ({self.designation})"

    def _get_offer_component_value(self, gross_salary, component_id, type_of):
        """
        Mimics fun.Offer_Cal(GrossSalary, component_id, calculation_type, TypeOf)
        Calculation_type: 1 for monthly, 2 for annual.
        This is a placeholder, actual logic needs to be reverse-engineered from fun.Offer_Cal.
        For demonstration, assuming fixed percentages based on component_id and type_of.
        """
        # Placeholder for fun.Offer_Cal logic. In a real migration, this would involve
        # reverse-engineering the stored procedures or C# functions from the ASP.NET 'fun' class.
        # Example rough logic:
        if type_of == 1: # SAPL
            if component_id == 1: return gross_salary * 0.30 # Basic
            if component_id == 2: return gross_salary * 0.20 # DA
            if component_id == 3: return gross_salary * 0.20 # HRA
            if component_id == 4: return gross_salary * 0.20 # Convenience
            if component_id == 5: return gross_salary * 0.05 # Education
            if component_id == 6: return gross_salary * 0.05 # Medical
        elif type_of == 2: # NEHA (example, could be different percentages)
            if component_id == 1: return gross_salary * 0.25
            if component_id == 2: return gross_salary * 0.15
            if component_id == 3: return gross_salary * 0.15
            if component_id == 4: return gross_salary * 0.15
            if component_id == 5: return gross_salary * 0.03
            if component_id == 6: return gross_salary * 0.03
        return 0.0

    def _get_pf_value(self, gross_salary, pf_type_id, pf_percentage):
        """
        Mimics fun.Pf_Cal(GrossSalary, pf_type_id, pf_percentage)
        pf_type_id: 1 for Employee PF, 2 for Company PF
        """
        # Placeholder for fun.Pf_Cal logic.
        return min(15000 * (pf_percentage / 100), gross_salary * (pf_percentage / 100)) # Example PF Cap/Calculation

    def _get_ptax_value(self, total_for_ptax, dummy_param):
        """
        Mimics fun.PTax_Cal(Gross + Att. Bonus 1 + Ex Gratia, "0")
        This is a placeholder. Actual PTax logic needs to be reverse-engineered.
        """
        # Placeholder for fun.PTax_Cal logic. Assumes slab based calculation.
        if total_for_ptax <= 10000: return 0
        if total_for_ptax <= 15000: return 150
        if total_for_ptax <= 25000: return 300
        return 500 # Example fixed value

    def _get_gratuity_value(self, gross_salary, calculation_type, employee_type_id):
        """
        Mimics fun.Gratuity_Cal(GrossSalary, calculation_type, DrpEmpTypeOf.SelectedValue)
        Calculation_type: 1 for monthly, 2 for annual.
        """
        # Placeholder for fun.Gratuity_Cal logic.
        if employee_type_id == 2: # NEHA, gratuity is 0
            return 0.0
        # Example: 4.81% of Basic for monthly, 4.81% * 12 for annual (approx. 15 days/year)
        monthly_basic = self._get_offer_component_value(gross_salary, 1, employee_type_id)
        if calculation_type == 1: # Monthly
            return monthly_basic * (4.81 / 100)
        elif calculation_type == 2: # Annual
            return monthly_basic * (4.81 / 100) * 12
        return 0.0

    def calculate_salary_components(self, gross_salary, type_of_id, staff_type_desc, 
                                    att_bonus_per1, att_bonus_per2, 
                                    pf_employee_perc, pf_company_perc,
                                    bonus_amount, ex_gratia, lta, loyalty, paid_leaves,
                                    accessories_data):
        """
        Centralized salary calculation logic, mimicking CalSalary().
        This method is designed to be called from views to get real-time calculation
        based on current form inputs or saved data.
        """
        if gross_salary <= 0:
            return {} # Return empty if gross salary is invalid

        annual_salary = gross_salary * 12

        # Core Components (Monthly & Annual)
        g_basic = self._get_offer_component_value(gross_salary, 1, type_of_id)
        an_basic = g_basic * 12
        g_da = self._get_offer_component_value(gross_salary, 2, type_of_id)
        an_da = g_da * 12
        g_hra = self._get_offer_component_value(gross_salary, 3, type_of_id)
        an_hra = g_hra * 12
        g_convenience = self._get_offer_component_value(gross_salary, 4, type_of_id)
        an_convenience = g_convenience * 12
        g_edu = self._get_offer_component_value(gross_salary, 5, type_of_id)
        an_edu = g_edu * 12
        g_wash = self._get_offer_component_value(gross_salary, 6, type_of_id) # Medical
        an_wash = g_wash * 12

        # Other specific components
        att_bonus1 = 0.0
        att_bonus2 = 0.0
        pfe = 0.0
        pfc = 0.0
        ptax = 0.0
        gratuity_monthly = 0.0
        gratuity_annual = 0.0

        if staff_type_desc != "Casuals":
            att_bonus1 = gross_salary * (att_bonus_per1 / 100)
            att_bonus2 = gross_salary * (att_bonus_per2 / 100)
            pfe = self._get_pf_value(gross_salary, 1, pf_employee_perc)
            pfc = self._get_pf_value(gross_salary, 2, pf_company_perc)
            
            # PTax calculation: Gross + Att. Bonus 1 + Ex Gratia
            total_for_ptax = gross_salary + att_bonus1 + ex_gratia
            ptax = self._get_ptax_value(total_for_ptax, "0")

            gratuity_monthly = self._get_gratuity_value(gross_salary, 1, type_of_id)
            gratuity_annual = self._get_gratuity_value(gross_salary, 2, type_of_id)
            
            if type_of_id == 2: # NEHA, gratuity is 0
                gratuity_monthly = 0.0
                gratuity_annual = 0.0
        else: # Casuals
            # All these components are 0 for Casuals
            att_bonus1 = 0.0
            att_bonus2 = 0.0
            pfe = 0.0
            pfc = 0.0
            ptax = 0.0
            bonus_amount = 0.0 # From original code, bonus text field also set to 0 and disabled
            gratuity_monthly = 0.0
            gratuity_annual = 0.0

        annual_bonus = bonus_amount * 12 # txtBonus is per month, so multiply by 12 for annual

        # Accessories Amount
        accessories_amt_ctc = 0.0
        accessories_amt_take_home = 0.0
        accessories_amt_both = 0.0

        for acc in accessories_data:
            qty = acc.get('qty', 0.0)
            amount = acc.get('amount', 0.0)
            includes_in_id = acc.get('includes_in_id') # This should be the ID of IncludesIn model

            # Check if IncludesIn object exists for safety
            includes_in_obj = IncludesIn.objects.filter(id=includes_in_id).first()
            if includes_in_obj:
                if includes_in_obj.includes_in == "CTC": # Assuming "CTC" for case "1"
                    accessories_amt_ctc += qty * amount
                elif includes_in_obj.includes_in == "Take Home": # Assuming "Take Home" for case "2"
                    accessories_amt_take_home += qty * amount
                elif includes_in_obj.includes_in == "Both": # Assuming "Both" for case "3"
                    accessories_amt_both += qty * amount

        # Take Home Salary
        th = round((gross_salary + ex_gratia + accessories_amt_take_home + accessories_amt_both) - (pfe + ptax), 2)
        th_att1 = round(th + att_bonus1, 2)
        th_att2 = round(th + att_bonus2, 2)
        th_annual = round(th * 12, 2)
        th_ann_att1 = round(th_att1 * 12, 2)
        th_ann_att2 = round(th_att2 * 12, 2)

        # CTC (Cost to Company)
        ctc = round(gross_salary + bonus_amount + loyalty + lta + gratuity_monthly + pfc + ex_gratia + accessories_amt_ctc + accessories_amt_both, 2)
        ctc_att1 = round(ctc + att_bonus1, 2)
        ctc_att2 = round(ctc + att_bonus2, 2)
        ctc_annual = round(ctc * 12, 2)
        ctc_ann_att1 = round(ctc_att1 * 12, 2)
        ctc_ann_att2 = round(ctc_att2 * 12, 2)

        return {
            'gross_salary': gross_salary,
            'annual_salary': annual_salary,
            'g_basic': g_basic, 'an_basic': an_basic,
            'g_da': g_da, 'an_da': an_da,
            'g_hra': g_hra, 'an_hra': an_hra,
            'g_convenience': g_convenience, 'an_convenience': an_convenience,
            'g_edu': g_edu, 'an_edu': an_edu,
            'g_wash': g_wash, 'an_wash': an_wash,
            'att_bonus1': att_bonus1, 'att_bonus2': att_bonus2,
            'pf_employee': pfe, 'pf_company': pfc,
            'ptax': ptax,
            'bonus_amount': bonus_amount, 'annual_bonus': annual_bonus, # Note: bonus_amount is monthly here
            'gratuity_monthly': gratuity_monthly, 'gratuity_annual': gratuity_annual,
            'take_home': th, 'take_home_att1': thatt1, 'take_home_att2': thatt2,
            'take_home_annual': th_annual, 'take_home_ann_att1': th_ann_att1, 'take_home_ann_att2': th_ann_att2,
            'ctc': ctc, 'ctc_att1': ctc_att1, 'ctc_att2': ctc_att2,
            'ctc_annual': ctc_annual, 'ctc_ann_att1': ctc_ann_att1, 'ctc_ann_att2': ctc_ann_att2,
            # Add calculated accessories_amt for debugging/verification if needed
            'accessories_amt_ctc': accessories_amt_ctc,
            'accessories_amt_take_home': accessories_amt_take_home,
            'accessories_amt_both': accessories_amt_both,
        }

class OfferAccessory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    offer_master = models.ForeignKey(OfferLetter, models.CASCADE, db_column='MId', related_name='accessories')
    perticulars = models.CharField(db_column='Perticulars', max_length=255)
    qty = models.FloatField(db_column='Qty')
    amount = models.FloatField(db_column='Amount')
    includes_in = models.ForeignKey(IncludesIn, models.DO_NOTHING, db_column='IncludesIn')

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'Offer Accessory'
        verbose_name_plural = 'Offer Accessories'

    def __str__(self):
        return self.perticulars

    @property
    def total(self):
        return round(self.qty * self.amount, 2)

```

#### 4.2 Forms (`hrmodules/forms.py`)

**Task:** Define Django forms for user input, including the main offer letter and a formset for accessories.

**Instructions:**

*   Create a `ModelForm` for `OfferLetter`.
*   Create a `ModelForm` for `OfferAccessory`.
*   Use `inlineformset_factory` to handle `OfferAccessory` instances directly linked to `OfferLetter`. This replaces the need for the temporary table.
*   Implement custom validation where required (e.g., email regex, numeric checks).
*   Add Tailwind CSS classes to widgets.

```python
# hrmodules/forms.py
from django import forms
from django.forms import inlineformset_factory
from django.core.validators import RegexValidator
from .models import (
    OfferLetter, Designation, DutyHour, OTHour, OverTime, 
    EmployeeType, IncludesIn, OfferAccessory, PF_Slab, OfficeStaff
)

# Common Tailwind CSS classes for form fields
COMMON_ATTRS = {
    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
    'x-model': '', # Placeholder for Alpine.js binding
}

class OfferLetterForm(forms.ModelForm):
    # Static choices for Title (Mr./Mrs./Miss.)
    TITLE_CHOICES = [
        ('Mr.', 'Mr.'),
        ('Mrs.', 'Mrs.'),
        ('Miss.', 'Miss.'),
    ]
    title = forms.ChoiceField(choices=TITLE_CHOICES, widget=forms.Select(attrs=COMMON_ATTRS))

    # Static choices for Type of Employee (SAPL/NEHA)
    TYPE_OF_CHOICES = [
        ('0', 'Select'), # InitialValue="0" in ASP.NET validator
        ('1', 'SAPL'),
        ('2', 'NEHA'),
    ]
    type_of = forms.ChoiceField(
        choices=TYPE_OF_CHOICES, 
        widget=forms.Select(attrs={**COMMON_ATTRS, 'hx-post': 'hx-post="{% url "hrmodules:calculate_salary_partial" %}" hx-target="#salaryBreakdown" hx-trigger="change delay:300ms, updateSalary"'})
    )

    # Auto-complete fields - we will render these as text inputs and handle autocomplete via HTMX
    interviewed_by_name = forms.CharField(
        label="Interviewed By", 
        required=True, 
        widget=forms.TextInput(attrs={
            **COMMON_ATTRS,
            'hx-get': "{% url 'hrmodules:staff_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms",
            'hx-target': "#interviewed-by-suggestions",
            'hx-swap': "innerHTML",
            'autocomplete': 'off',
            'id': 'Txtinterviewedby' # Match original ID for easier mapping
        })
    )
    authorized_by_name = forms.CharField(
        label="Authorized By", 
        required=True, 
        widget=forms.TextInput(attrs={
            **COMMON_ATTRS,
            'hx-get': "{% url 'hrmodules:staff_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms",
            'hx-target': "#authorized-by-suggestions",
            'hx-swap': "innerHTML",
            'autocomplete': 'off',
            'id': 'TxtAuthorizedby' # Match original ID for easier mapping
        })
    )

    # Email validation from ASP.NET RegularExpressionValidator
    email_regex_validator = RegexValidator(
        regex=r'^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$',
        message='Please enter a valid email address.'
    )
    email_id = forms.CharField(
        validators=[email_regex_validator],
        widget=forms.TextInput(attrs=COMMON_ATTRS)
    )

    # Numeric fields with regex validation from ASP.NET RegularExpressionValidator
    numeric_regex_validator = RegexValidator(
        regex=r'^\d{1,15}(\.\d{0,3})?$',
        message='Enter a valid number (up to 15 digits, 3 decimal places).'
    )
    
    salary = forms.FloatField(
        label="Gross Salary", 
        validators=[numeric_regex_validator],
        widget=forms.NumberInput(attrs={
            **COMMON_ATTRS, 
            'value': '0', # Default value
            'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 
            'hx-target': "#salaryBreakdown", 
            'hx-trigger': "change delay:300ms, updateSalary",
            'x-model': 'grossSalary', # Alpine.js binding
            'min': '0'
        })
    )
    # Other numeric fields from the form
    ex_gratia = forms.FloatField(label="Ex Gratia", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    vehicle_allowance = forms.FloatField(label="Vehicle Allowance", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    lta = forms.FloatField(label="LTA", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    loyalty = forms.FloatField(label="Loyalty Benefits", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    paid_leaves = forms.FloatField(label="Paid Leaves", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    pf_employee_perc = forms.FloatField(label="PF-Employee (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    pf_company_perc = forms.FloatField(label="PF-Company (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    att_bonus_per1 = forms.FloatField(label="Attend. Bonus - 1 (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    att_bonus_per2 = forms.FloatField(label="Attend. Bonus - 2 (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    bonus = forms.FloatField(label="Bonus (Monthly)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))


    class Meta:
        model = OfferLetter
        fields = [
            'title', 'employee_name', 'designation', 'duty_hrs', 'ot_hrs', 'overtime_applicable', 
            'type_of', 'staff_type', 'contact_no', 'address', 'email_id', 'reference_by', 
            'salary', 'ex_gratia', 'vehicle_allowance', 'lta', 'loyalty', 'paid_leaves',
            'pf_employee_perc', 'pf_company_perc', 'att_bonus_per1', 'att_bonus_per2', 'bonus',
            'header_text', 'footer_text', 'remarks'
        ]
        # Exclude interviewed_by and authorized_by from Meta.fields, will handle manually for string input to ID mapping
        # We manually define these two fields above.
        
        widgets = {
            'employee_name': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Enter Employee Name'}),
            'address': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 3, 'placeholder': 'Enter Address'}),
            'contact_no': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Enter Contact Number'}),
            'reference_by': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Reference By (Optional)'}),
            'header_text': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 4, 'placeholder': 'Offer letter header content'}),
            'footer_text': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 4, 'placeholder': 'Offer letter footer content'}),
            'remarks': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 2, 'placeholder': 'Any remarks?'}),
            'designation': forms.Select(attrs=COMMON_ATTRS),
            'duty_hrs': forms.Select(attrs=COMMON_ATTRS),
            'ot_hrs': forms.Select(attrs=COMMON_ATTRS),
            'overtime_applicable': forms.Select(attrs=COMMON_ATTRS),
            'staff_type': forms.Select(attrs={**COMMON_ATTRS, 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}" , 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary"})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dropdowns from database
        self.fields['designation'].queryset = Designation.objects.all()
        self.fields['duty_hrs'].queryset = DutyHour.objects.all()
        self.fields['ot_hrs'].queryset = OTHour.objects.all()
        self.fields['overtime_applicable'].queryset = OverTime.objects.all()
        self.fields['staff_type'].queryset = EmployeeType.objects.all()
        
        # Set initial values for auto-complete fields if instance exists
        if self.instance.pk:
            if self.instance.interviewed_by:
                self.fields['interviewed_by_name'].initial = f"{self.instance.interviewed_by.employee_name} [{self.instance.interviewed_by.emp_id}]"
            if self.instance.authorized_by:
                self.fields['authorized_by_name'].initial = f"{self.instance.authorized_by.employee_name} [{self.instance.authorized_by.emp_id}]"

        # Set initial PF Slab values for a new form
        if not self.instance.pk:
            pf_slab = PF_Slab.objects.filter(active=True).first()
            if pf_slab:
                self.fields['pf_employee_perc'].initial = pf_slab.pf_employee
                self.fields['pf_company_perc'].initial = pf_slab.pf_company
        
        # Initial Att Bonus based on TypeOf (SAPL/NEHA)
        # This will be handled by HTMX when TypeOf changes, but for initial load:
        if self.instance.type_of == 1: # SAPL
            self.fields['att_bonus_per1'].initial = 10
            self.fields['att_bonus_per2'].initial = 20
        elif self.instance.type_of == 2: # NEHA
            self.fields['att_bonus_per1'].initial = 5
            self.fields['att_bonus_per2'].initial = 15

        # Handle "Casuals" logic (disable/set to 0)
        if self.instance.staff_type and self.instance.staff_type.description == "Casuals":
            self.fields['bonus'].initial = 0
            self.fields['bonus'].widget.attrs['disabled'] = 'disabled'
            self.fields['att_bonus_per1'].initial = 0
            self.fields['att_bonus_per1'].widget.attrs['disabled'] = 'disabled'
            self.fields['att_bonus_per2'].initial = 0
            self.fields['att_bonus_per2'].widget.attrs['disabled'] = 'disabled'
            self.fields['pf_employee_perc'].initial = 0
            self.fields['pf_employee_perc'].widget.attrs['disabled'] = 'disabled'
            self.fields['pf_company_perc'].initial = 0
            self.fields['pf_company_perc'].widget.attrs['disabled'] = 'disabled'

    def clean(self):
        cleaned_data = super().clean()
        
        # Manually assign FKs for auto-complete fields
        interviewed_by_name = cleaned_data.get('interviewed_by_name')
        authorized_by_name = cleaned_data.get('authorized_by_name')

        if interviewed_by_name:
            try:
                emp_id = int(interviewed_by_name.split(' [')[-1][:-1]) # Extract ID from "Name [ID]"
                cleaned_data['interviewed_by'] = OfficeStaff.objects.get(emp_id=emp_id)
            except (ValueError, IndexError, OfficeStaff.DoesNotExist):
                self.add_error('interviewed_by_name', 'Invalid Interviewed By selection.')
        else:
            self.add_error('interviewed_by_name', 'This field is required.')

        if authorized_by_name:
            try:
                emp_id = int(authorized_by_name.split(' [')[-1][:-1])
                cleaned_data['authorized_by'] = OfficeStaff.objects.get(emp_id=emp_id)
            except (ValueError, IndexError, OfficeStaff.DoesNotExist):
                self.add_error('authorized_by_name', 'Invalid Authorized By selection.')
        else:
            self.add_error('authorized_by_name', 'This field is required.')

        # Required field validation for TypeOf (ASP.NET InitialValue="0")
        if cleaned_data.get('type_of') == '0':
            self.add_error('type_of', 'Please select a Type of Employee.')
            
        return cleaned_data

class OfferAccessoryForm(forms.ModelForm):
    numeric_regex_validator = RegexValidator(
        regex=r'^\d{1,15}(\.\d{0,3})?$',
        message='Enter a valid number (up to 15 digits, 3 decimal places).'
    )

    qty = forms.FloatField(label="Qty", validators=[numeric_regex_validator], 
                           widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'min': '0'}))
    amount = forms.FloatField(label="Amount", validators=[numeric_regex_validator], 
                              widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'min': '0'}))

    class Meta:
        model = OfferAccessory
        fields = ['perticulars', 'qty', 'amount', 'includes_in']
        widgets = {
            'perticulars': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Perticulars'}),
            'includes_in': forms.Select(attrs=COMMON_ATTRS),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['includes_in'].queryset = IncludesIn.objects.all()


# Formset for handling multiple OfferAccessory instances related to an OfferLetter
OfferAccessoryFormSet = inlineformset_factory(
    OfferLetter,
    OfferAccessory,
    form=OfferAccessoryForm,
    extra=1, # One empty form initially
    can_delete=True,
    min_num=0,
    validate_min=False, # Allows form to be valid even if no accessories
)
```

#### 4.3 Views (`hrmodules/views.py`)

**Task:** Implement CRUD operations and dynamic updates using Django Class-Based Views (CBVs) and HTMX.

**Instructions:**

*   Create `OfferLetterCreateUpdateView` to handle the main form and its accessory formset.
*   Create `SalaryBreakdownPartialView` to handle HTMX requests for salary recalculations.
*   Create `OfferAccessoryTablePartialView` to render the accessories table.
*   Create `StaffAutoCompleteView` for the auto-complete functionality.
*   Keep views thin (5-15 lines per method), pushing logic to models or forms.
*   Use `messages` for user feedback.
*   Ensure proper HTMX response headers (`HX-Trigger`, `HX-Swap`).

```python
# hrmodules/views.py
from django.views.generic import FormView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.db import transaction
from django.forms.models import model_to_dict

from .models import OfferLetter, OfferAccessory, PF_Slab, OfficeStaff, EmployeeType, IncludesIn
from .forms import OfferLetterForm, OfferAccessoryFormSet

class OfferLetterCreateUpdateView(FormView):
    template_name = 'hrmodules/offerletter/form.html'
    form_class = OfferLetterForm
    success_url = reverse_lazy('hrmodules:offerletter_add') # Redirect back to add or list on success

    def get_object(self):
        pk = self.kwargs.get('pk')
        if pk:
            return get_object_or_404(OfferLetter, pk=pk)
        return None

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        offer_letter = self.get_object()
        
        if self.request.POST and self.request.headers.get('HX-Request'):
            # If it's an HTMX POST, formsets are handled by the 'clean' method of main form
            # For this scenario, we re-instantiate with POST data
            context['form'] = OfferLetterForm(self.request.POST, instance=offer_letter)
            context['accessory_formset'] = OfferAccessoryFormSet(self.request.POST, instance=offer_letter, prefix='accessories')
        else:
            context['form'] = OfferLetterForm(instance=offer_letter)
            context['accessory_formset'] = OfferAccessoryFormSet(instance=offer_letter, prefix='accessories')
        
        # Pass initial calculated values or default to 0 for rendering _salary_breakdown.html
        calculated_data = {}
        if offer_letter and offer_letter.salary:
             # Fetch accessory data for calculation
            accessories_data = [
                {'qty': acc.qty, 'amount': acc.amount, 'includes_in_id': acc.includes_in.id}
                for acc in offer_letter.accessories.all()
            ]
            staff_type_desc = offer_letter.staff_type.description if offer_letter.staff_type else ""

            calculated_data = offer_letter.calculate_salary_components(
                gross_salary=offer_letter.salary,
                type_of_id=offer_letter.type_of,
                staff_type_desc=staff_type_desc,
                att_bonus_per1=offer_letter.att_bonus_per1,
                att_bonus_per2=offer_letter.att_bonus_per2,
                pf_employee_perc=offer_letter.pf_employee_perc,
                pf_company_perc=offer_letter.pf_company_perc,
                bonus_amount=offer_letter.bonus,
                ex_gratia=offer_letter.ex_gratia,
                lta=offer_letter.lta,
                loyalty=offer_letter.loyalty,
                paid_leaves=offer_letter.paid_leaves,
                accessories_data=accessories_data
            )
        
        context['calculated_salary'] = calculated_data
        context['offer_letter'] = offer_letter # Pass object for rendering if needed
        return context

    def form_valid(self, form):
        offer_letter = form.save(commit=False)
        accessory_formset = OfferAccessoryFormSet(self.request.POST, instance=offer_letter, prefix='accessories')

        if accessory_formset.is_valid():
            with transaction.atomic():
                offer_letter.fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not in session
                offer_letter.comp_id = self.request.session.get('compid', 1)     # Default to 1 if not in session
                offer_letter.session_id = self.request.session.get('username', 'system') # Default if not in session

                offer_letter.save()
                accessory_formset.save()

            messages.success(self.request, 'Offer Letter and accessories saved successfully!')
            
            if self.request.headers.get('HX-Request'):
                # For HTMX, trigger a reload of the form or redirect to list
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshOfferLetterForm'
                    }
                )
            return super().form_valid(form) # Standard redirect
        else:
            # If formset is invalid, render the form again with errors.
            messages.error(self.request, 'Please correct the errors in the accessory section.')
            return self.form_invalid(form) # Re-render with errors

    def form_invalid(self, form):
        # Render the form with errors, especially if HTMX request
        messages.error(self.request, 'Please correct the errors in the form.')
        if self.request.headers.get('HX-Request'):
            context = self.get_context_data(form=form)
            return render(self.request, self.template_name, context)
        return super().form_invalid(form)

class SalaryBreakdownPartialView(TemplateView):
    template_name = 'hrmodules/offerletter/_salary_breakdown.html'

    def post(self, request, *args, **kwargs):
        # Extract data from POST request (form fields)
        gross_salary = float(request.POST.get('salary', 0))
        type_of_id = int(request.POST.get('type_of', 0)) # DrpEmpTypeOf
        staff_type_id = request.POST.get('staff_type') # DrpEmpType
        
        # Get staff_type description from ID
        staff_type_obj = EmployeeType.objects.filter(id=staff_type_id).first()
        staff_type_desc = staff_type_obj.description if staff_type_obj else ""

        att_bonus_per1 = float(request.POST.get('att_bonus_per1', 0))
        att_bonus_per2 = float(request.POST.get('att_bonus_per2', 0))
        pf_employee_perc = float(request.POST.get('pf_employee_perc', 0))
        pf_company_perc = float(request.POST.get('pf_company_perc', 0))
        bonus_amount = float(request.POST.get('bonus', 0)) # This is monthly bonus
        ex_gratia = float(request.POST.get('ex_gratia', 0))
        lta = float(request.POST.get('lta', 0))
        loyalty = float(request.POST.get('loyalty', 0))
        paid_leaves = float(request.POST.get('paid_leaves', 0))

        # Dynamically set att bonus % based on type_of_id, mimicking DrpEmpTypeOf_SelectedIndexChanged
        if type_of_id == 1: # SAPL
            att_bonus_per1 = 10.0
            att_bonus_per2 = 20.0
        elif type_of_id == 2: # NEHA
            att_bonus_per1 = 5.0
            att_bonus_per2 = 15.0

        # Fetch current PF slab values for staff type logic, mimicking DrpEmpType_SelectedIndexChanged
        if staff_type_desc != "Casuals":
            pf_slab = PF_Slab.objects.filter(active=True).first()
            if pf_slab:
                pf_employee_perc = pf_slab.pf_employee
                pf_company_perc = pf_slab.pf_company
        else: # Casuals
            att_bonus_per1 = 0.0
            att_bonus_per2 = 0.0
            bonus_amount = 0.0
            pf_employee_perc = 0.0
            pf_company_perc = 0.0

        # Retrieve accessory data from formset if present
        # This will require parsing the formset data from the POST request.
        # For simplicity, we'll assume accessories are passed as an array of objects
        # or we re-read from the (potentially unsaved) formset data.
        # The correct way to handle formset data in HTMX partials requires some frontend JS (Alpine)
        # to gather all formset rows and send them.
        # For now, let's assume we can parse it from the POST or fetch from session if we went the temp model route.
        # Given the current formset approach, accessories are either saved or in the unsaved form.
        # For a partial update like this, the best approach might be to send all form data (including accessory formset)
        # to this endpoint, validate it, and then recalculate.
        
        # Simplified accessory parsing for this example:
        accessories_data = []
        for i in range(int(request.POST.get('accessories-TOTAL_FORMS', 0))):
            DELETE = request.POST.get(f'accessories-{i}-DELETE')
            if DELETE == 'on': continue # Skip deleted forms
            
            try:
                acc_qty = float(request.POST.get(f'accessories-{i}-qty', 0))
                acc_amount = float(request.POST.get(f'accessories-{i}-amount', 0))
                acc_includes_in_id = int(request.POST.get(f'accessories-{i}-includes_in', 0))
                accessories_data.append({
                    'qty': acc_qty,
                    'amount': acc_amount,
                    'includes_in_id': acc_includes_in_id
                })
            except ValueError:
                pass # Skip invalid accessory rows

        # Create a dummy OfferLetter instance to use its calculation method
        # This is a 'fat model' approach: logic lives in the model.
        temp_offer = OfferLetter() 
        calculated_salary = temp_offer.calculate_salary_components(
            gross_salary=gross_salary,
            type_of_id=type_of_id,
            staff_type_desc=staff_type_desc,
            att_bonus_per1=att_bonus_per1,
            att_bonus_per2=att_bonus_per2,
            pf_employee_perc=pf_employee_perc,
            pf_company_perc=pf_company_perc,
            bonus_amount=bonus_amount,
            ex_gratia=ex_gratia,
            lta=lta,
            loyalty=loyalty,
            paid_leaves=paid_leaves,
            accessories_data=accessories_data
        )

        context = {'calculated_salary': calculated_salary}
        context['form_data'] = {
            'att_bonus_per1': att_bonus_per1,
            'att_bonus_per2': att_bonus_per2,
            'pf_employee_perc': pf_employee_perc,
            'pf_company_perc': pf_company_perc,
            'bonus': bonus_amount,
            'type_of': type_of_id, # Re-send selected type_of to maintain state
            'staff_type': staff_type_id, # Re-send selected staff_type to maintain state
        }
        
        return render(request, self.template_name, context)

class OfferAccessoryTablePartialView(TemplateView):
    template_name = 'hrmodules/offerletter/_accessory_table.html'

    def post(self, request, *args, **kwargs):
        # This view is called when accessory formset is modified via HTMX.
        # We need to re-render the formset, potentially with a new empty form.
        # The main OfferLetterCreateUpdateView's context method already handles formset instantiation.
        # So we just render the formset part of the main form.
        offer_letter = None
        if self.kwargs.get('pk'): # For existing offer letters
            offer_letter = get_object_or_404(OfferLetter, pk=self.kwargs['pk'])
        
        formset = OfferAccessoryFormSet(request.POST, instance=offer_letter, prefix='accessories')

        if formset.is_valid():
            # If the request is for adding a new empty form
            if 'add_accessory' in request.POST:
                formset = OfferAccessoryFormSet(instance=offer_letter, prefix='accessories')
                # Dynamically add an empty form by increasing extra, or just let template handle it
                # For this setup, simply re-rendering the formset is enough.
                # Client-side JS (Alpine) will manage "add row" button.
            
            # Recalculate salary if accessories changed
            # This is done by triggering the salary partial update from the frontend after this swap.
            # Example: hx-trigger="endSettle from:#accessoryFormset, updateSalary"
            pass # Formset data is valid, now pass to template

        context = {
            'accessory_formset': formset,
            'includes_in_options': IncludesIn.objects.all(), # Pass dropdown options
        }
        return render(request, self.template_name, context)

class StaffAutoCompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # Mimic GetCompletionList logic from ASP.NET
        staff = OfficeStaff.objects.filter(employee_name__icontains=query) \
                                   .order_by('employee_name')[:10] # Limit results
        
        suggestions = []
        for s in staff:
            suggestions.append(f"{s.employee_name} [{s.emp_id}]")
        
        return HttpResponse('\n'.join(suggestions), content_type='text/plain')

# Example for showing a list of existing offers (not requested in detail, but good practice)
class OfferLetterListView(TemplateView):
    template_name = 'hrmodules/offerletter/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['offer_letters'] = OfferLetter.objects.all().order_by('-offer_id') # Latest first
        return context

# HTMX partial for the table of offer letters
class OfferLetterTablePartialView(TemplateView):
    template_name = 'hrmodules/offerletter/_offerletter_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['offer_letters'] = OfferLetter.objects.all().order_by('-offer_id')
        return context

class OfferLetterDeleteView(View):
    def post(self, request, pk):
        offer_letter = get_object_or_404(OfferLetter, pk=pk)
        offer_letter.delete()
        messages.success(request, 'Offer Letter deleted successfully.')
        
        # HTMX response for deletion
        return HttpResponse(
            status=204, # No content
            headers={
                'HX-Trigger': 'refreshOfferLetterList' # Custom event to refresh the list
            }
        )

```

#### 4.4 Templates (`hrmodules/templates/hrmodules/offerletter/`)

**Task:** Create templates for the main form, salary breakdown, accessory table, and the main list view.

**Instructions:**

*   All templates extend `core/base.html`.
*   Use DataTables for lists.
*   Implement HTMX for dynamic interactions (form submissions, partial updates, auto-complete, accessory adds/deletes).
*   Use Alpine.js for modal management and local UI state.
*   No raw JavaScript beyond DataTables initialization and Alpine.js setup.
*   All forms and partials should be designed to be loaded via HTMX, with `hx-swap="outerHTML"` or `hx-swap="innerHTML"`.

##### `hrmodules/templates/hrmodules/offerletter/form.html` (Main form)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ 
    grossSalary: parseFloat('{{ form.salary.value|default:0 }}'),
    typeOf: '{{ form.type_of.value|default:"0" }}',
    staffType: '{{ form.staff_type.value|default:"" }}',
    attBonusPer1: parseFloat('{{ form.att_bonus_per1.value|default:0 }}'),
    attBonusPer2: parseFloat('{{ form.att_bonus_per2.value|default:0 }}'),
    pfEmployeePerc: parseFloat('{{ form.pf_employee_perc.value|default:0 }}'),
    pfCompanyPerc: parseFloat('{{ form.pf_company_perc.value|default:0 }}'),
    bonus: parseFloat('{{ form.bonus.value|default:0 }}'),
    isCasuals: '{{ form.staff_type.value|default:"" }}' === '{{ staff_type_casuals_id }}' ? true : false,
    init() {
        this.$watch('staffType', value => {
            this.isCasuals = value === '{{ staff_type_casuals_id }}';
            if (this.isCasuals) {
                this.attBonusPer1 = 0;
                this.attBonusPer2 = 0;
                this.pfEmployeePerc = 0;
                this.pfCompanyPerc = 0;
                this.bonus = 0;
            } else {
                // Re-initialize based on typeOf, or fetch from PF_Slab
                this.updateAttBonusAndPF();
            }
            this.$dispatch('updateSalary'); // Trigger salary recalculation
        });
        this.$watch('typeOf', value => {
            this.updateAttBonusAndPF();
            this.$dispatch('updateSalary');
        });

        // Initialize PF values based on active slab on load if not set
        if (!this.pfEmployeePerc || !this.pfCompanyPerc) {
            fetch('/hr-modules/get-pf-slab/') // HTMX endpoint for PF slab
                .then(response => response.json())
                .then(data => {
                    if (data.pf_employee && data.pf_company) {
                        this.pfEmployeePerc = data.pf_employee;
                        this.pfCompanyPerc = data.pf_company;
                        this.$dispatch('updateSalary');
                    }
                });
        }
    },
    updateAttBonusAndPF() {
        if (this.typeOf === '1') { // SAPL
            this.attBonusPer1 = 10;
            this.attBonusPer2 = 20;
        } else if (this.typeOf === '2') { // NEHA
            this.attBonusPer1 = 5;
            this.attBonusPer2 = 15;
        }
    }
}" x-init="init()">
    <h2 class="text-2xl font-bold mb-6">Offer Letter - New</h2>

    <form hx-post="." hx-swap="none" hx-trigger="refreshOfferLetterForm from:body"
          _="on hx-trigger.refreshOfferLetterForm from body set window.location.href = '{{ request.path }}'">
        {% csrf_token %}
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Basic Employee Details -->
                <div class="col-span-full bg-gray-50 p-4 rounded-md mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Basic Details</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">Designation</label>
                            {{ form.designation }}
                            {% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}
                        </div>
                        <div class="flex items-center">
                            <div class="w-1/3 pr-2">
                                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">Title</label>
                                {{ form.title }}
                            </div>
                            <div class="w-2/3 pl-2">
                                <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                                {{ form.employee_name }}
                                {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div>
                            <label for="{{ form.duty_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">Duty Hours</label>
                            {{ form.duty_hrs }}
                            {% if form.duty_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.duty_hrs.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Nos.</label>
                            {{ form.contact_no }}
                            {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.ot_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Hours</label>
                            {{ form.ot_hrs }}
                            {% if form.ot_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ot_hrs.errors }}</p>{% endif %}
                        </div>
                        <div class="row-span-2">
                            <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                            {{ form.address }}
                            {% if form.address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.address.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.overtime_applicable.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Applicable</label>
                            {{ form.overtime_applicable }}
                            {% if form.overtime_applicable.errors %}<p class="text-red-500 text-xs mt-1">{{ form.overtime_applicable.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.type_of.id_for_label }}" class="block text-sm font-medium text-gray-700">Type of Employee</label>
                            {{ form.type_of }}
                            {% if form.type_of.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_of.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.staff_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Staff Type</label>
                            {{ form.staff_type }}
                            {% if form.staff_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.staff_type.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.email_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Email Id</label>
                            {{ form.email_id }}
                            {% if form.email_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email_id.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.interviewed_by_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Interviewed By</label>
                            {{ form.interviewed_by_name }}
                            <div id="interviewed-by-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-auto max-h-48 overflow-y-auto"></div>
                            {% if form.interviewed_by_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.interviewed_by_name.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.authorized_by_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Authorized By</label>
                            {{ form.authorized_by_name }}
                            <div id="authorized-by-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-auto max-h-48 overflow-y-auto"></div>
                            {% if form.authorized_by_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.authorized_by_name.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.reference_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Reference By</label>
                            {{ form.reference_by }}
                            {% if form.reference_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reference_by.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.salary.id_for_label }}" class="block text-sm font-medium text-gray-700">Gross Salary</label>
                            {{ form.salary }}
                            {% if form.salary.errors %}<p class="text-red-500 text-xs mt-1">{{ form.salary.errors }}</p>{% endif %}
                        </div>
                    </div>
                </div>

                <!-- Header & Footer Text (Right Column in ASP.NET) -->
                <div class="col-span-full lg:col-span-1 lg:row-span-3 bg-gray-50 p-4 rounded-md">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Offer Letter Content</h3>
                    <div class="mb-4">
                        <label for="{{ form.header_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Header Text</label>
                        {{ form.header_text }}
                        {% if form.header_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.header_text.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.footer_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Footer Text</label>
                        {{ form.footer_text }}
                        {% if form.footer_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.footer_text.errors }}</p>{% endif %}
                    </div>
                </div>
            </div>

            <!-- Salary Breakdown Section (HTMX Target) -->
            <div id="salaryBreakdown" 
                 hx-post="{% url 'hrmodules:calculate_salary_partial' %}" 
                 hx-trigger="updateSalary from:body, change from:#id_salary, change from:#id_ex_gratia, change from:#id_vehicle_allowance, change from:#id_lta, change from:#id_loyalty, change from:#id_paid_leaves, change from:#id_pf_employee_perc, change from:#id_pf_company_perc, change from:#id_att_bonus_per1, change from:#id_att_bonus_per2, change from:#id_bonus, change from:#accessoryFormset"
                 hx-swap="outerHTML" 
                 class="mt-6 bg-gray-50 p-4 rounded-md">
                {% include 'hrmodules/offerletter/_salary_breakdown.html' with calculated_salary=calculated_salary form_data=form.data %}
            </div>

            <!-- Accessories Section (HTMX Target) -->
            <div id="accessorySection" 
                 hx-post="{% url 'hrmodules:offerletter_accessories_table_partial' %}" 
                 hx-trigger="refreshAccessoryTable from:body"
                 hx-swap="innerHTML"
                 class="mt-6 bg-gray-50 p-4 rounded-md">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Accessories</h3>
                {% include 'hrmodules/offerletter/_accessory_table.html' with accessory_formset=accessory_formset includes_in_options=includes_in_options %}
            </div>

            <div class="mt-6">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>

            <div class="mt-8 flex justify-end space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Submit Offer
                </button>
            </div>
        </div>
    </form>
</div>

<script>
    // Live update of Staff Type "Casuals" logic for bonus and PF fields
    document.addEventListener('DOMContentLoaded', function() {
        const staffTypeSelect = document.getElementById('{{ form.staff_type.id_for_label }}');
        const bonusField = document.getElementById('{{ form.bonus.id_for_label }}');
        const attB1Field = document.getElementById('{{ form.att_bonus_per1.id_for_label }}');
        const attB2Field = document.getElementById('{{ form.att_bonus_per2.id_for_label }}');
        const pfEmpField = document.getElementById('{{ form.pf_employee_perc.id_for_label }}');
        const pfCompField = document.getElementById('{{ form.pf_company_perc.id_for_label }}');
        
        function applyCasualsLogic() {
            // Assuming 'Casuals' has ID 5 based on common HR systems or DB inspection
            // This should be dynamically fetched if possible, or hardcoded for exact match.
            // For now, let's assume staff_type_casuals_id is passed from view.
            const isCasuals = staffTypeSelect.value === '{{ staff_type_casuals_id }}'; 

            [bonusField, attB1Field, attB2Field, pfEmpField, pfCompField].forEach(field => {
                if (field) {
                    field.disabled = isCasuals;
                    if (isCasuals) {
                        field.value = '0';
                    }
                }
            });
        }
        
        staffTypeSelect.addEventListener('change', applyCasualsLogic);
        applyCasualsLogic(); // Apply on initial load
    });

    // Handle staff autocomplete suggestions
    htmx.on('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.responseURL.includes('staff-autocomplete')) {
            const targetId = evt.detail.target.id;
            const suggestionsContainerId = targetId === 'Txtinterviewedby' ? 'interviewed-by-suggestions' : 'authorized-by-suggestions';
            const suggestionsDiv = document.getElementById(suggestionsContainerId);
            suggestionsDiv.innerHTML = ''; // Clear previous suggestions
            const suggestions = evt.detail.xhr.responseText.split('\n').filter(s => s.trim() !== '');
            
            if (suggestions.length > 0) {
                suggestions.forEach(suggestion => {
                    const item = document.createElement('div');
                    item.className = 'px-3 py-2 cursor-pointer hover:bg-gray-200';
                    item.textContent = suggestion;
                    item.onclick = function() {
                        document.getElementById(targetId).value = suggestion;
                        suggestionsDiv.innerHTML = ''; // Clear suggestions on selection
                    };
                    suggestionsDiv.appendChild(item);
                });
            }
        }
    });

    // Clear suggestions if input is blurred or clicked outside
    document.addEventListener('click', function(event) {
        const interviewInput = document.getElementById('Txtinterviewedby');
        const authInput = document.getElementById('TxtAuthorizedby');
        const interviewSuggestions = document.getElementById('interviewed-by-suggestions');
        const authSuggestions = document.getElementById('authorized-by-suggestions');

        if (interviewInput && interviewSuggestions && !interviewInput.contains(event.target) && !interviewSuggestions.contains(event.target)) {
            interviewSuggestions.innerHTML = '';
        }
        if (authInput && authSuggestions && !authInput.contains(event.target) && !authSuggestions.contains(event.target)) {
            authSuggestions.innerHTML = '';
        }
    });

    // For DataTable after HTMX swap
    htmx.on('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'accessorySection') {
            $('#accessoryTable').DataTable({
                "paging": false, // Handled by Django formsets for row management
                "searching": false,
                "info": false,
                "lengthChange": false
            });
        }
    });

</script>
{% endblock %}
```

##### `hrmodules/templates/hrmodules/offerletter/_salary_breakdown.html` (Salary calculation partial)

```html
<div class="col-span-full" id="salaryBreakdownContent">
    <h3 class="text-lg font-semibold text-gray-800 mb-2">Salary Breakdown</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
        <!-- Monthly Components -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Monthly</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between"><span>Gross Salary:</span> <b>{{ calculated_salary.gross_salary|default:0|floatformat:2 }}</b></div>
                <div class="flex justify-between"><span>Basic (30%):</span> <span>{{ calculated_salary.g_basic|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>DA (20%):</span> <span>{{ calculated_salary.g_da|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>HRA (20%):</span> <span>{{ calculated_salary.g_hra|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Convenience (20%):</span> <span>{{ calculated_salary.g_convenience|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Education (5%):</span> <span>{{ calculated_salary.g_edu|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Medical (5%):</span> <span>{{ calculated_salary.g_wash|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Attend. Bonus - 1:</span> <span>{{ calculated_salary.att_bonus1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Attend. Bonus - 2:</span> <span>{{ calculated_salary.att_bonus2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Gratuity:</span> <span>{{ calculated_salary.gratuity_monthly|default:0|floatformat:2 }}</span></div>
            </div>
        </div>

        <!-- Annual Components -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Annual</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between"><span>Annual Salary:</span> <b>{{ calculated_salary.annual_salary|default:0|floatformat:2 }}</b></div>
                <div class="flex justify-between"><span>Annual Basic:</span> <span>{{ calculated_salary.an_basic|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual DA:</span> <span>{{ calculated_salary.an_da|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual HRA:</span> <span>{{ calculated_salary.an_hra|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Convenience:</span> <span>{{ calculated_salary.an_convenience|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Education:</span> <span>{{ calculated_salary.an_edu|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Medical:</span> <span>{{ calculated_salary.an_wash|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Bonus:</span> <span>{{ calculated_salary.annual_bonus|default:0|floatformat:2 }}</span></div>
            </div>
            <div class="mt-4">
                <h4 class="font-medium text-gray-600 mb-2">Other Annual Benefits</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between"><span>LTA:</span> <span>{{ calculated_salary.lta|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Ex Gratia:</span> <span>{{ calculated_salary.ex_gratia|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Loyalty Benefits:</span> <span>{{ calculated_salary.loyalty|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Vehicle Allowance:</span> <span>{{ calculated_salary.vehicle_allowance|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Paid Leaves:</span> <span>{{ calculated_salary.paid_leaves|default:0|floatformat:2 }}</span></div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 mt-6">
        <!-- PF & P.Tax -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Deductions</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between"><span>PF-Employee:</span> <span>{{ calculated_salary.pf_employee|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>PF-Company:</span> <span>{{ calculated_salary.pf_company|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>P. Tax:</span> <span>{{ calculated_salary.ptax|default:0|floatformat:2 }}</span></div>
            </div>
        </div>

        <!-- Take Home & CTC -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Summary</h4>
            <div class="space-y-2 text-sm font-bold">
                <div class="flex justify-between"><span>Take Home:</span> <span>{{ calculated_salary.take_home|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Take Home (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.take_home_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Take Home (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.take_home_att2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Take Home:</span> <span>{{ calculated_salary.take_home_annual|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Take Home (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.take_home_ann_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Take Home (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.take_home_ann_att2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>CTC:</span> <span>{{ calculated_salary.ctc|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>CTC (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.ctc_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>CTC (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.ctc_att2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual CTC:</span> <span>{{ calculated_salary.ctc_annual|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual CTC (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.ctc_ann_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual CTC (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.ctc_ann_att2|default:0|floatformat:2 }}</span></div>
            </div>
        </div>
    </div>

    <!-- Hidden fields for the calculation to pick up dynamic changes -->
    <input type="hidden" name="att_bonus_per1" value="{{ form_data.att_bonus_per1|default:0 }}">
    <input type="hidden" name="att_bonus_per2" value="{{ form_data.att_bonus_per2|default:0 }}">
    <input type="hidden" name="pf_employee_perc" value="{{ form_data.pf_employee_perc|default:0 }}">
    <input type="hidden" name="pf_company_perc" value="{{ form_data.pf_company_perc|default:0 }}">
    <input type="hidden" name="bonus" value="{{ form_data.bonus|default:0 }}">
    <input type="hidden" name="type_of" value="{{ form_data.type_of|default:'0' }}">
    <input type="hidden" name="staff_type" value="{{ form_data.staff_type|default:'' }}">
</div>
```

##### `hrmodules/templates/hrmodules/offerletter/_accessory_table.html` (Accessories formset partial)

```html
<div id="accessoryFormset" class="overflow-x-auto">
    <table id="accessoryTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Include In</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Perticulars</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for form in accessory_formset %}
            <tr {% if form.instance.pk %}id="accessory-{{ form.instance.pk }}"{% endif %}>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    {{ form.id }} {# Hidden ID field for existing instances #}
                    {{ form.includes_in }}
                    {% if form.includes_in.errors %}<p class="text-red-500 text-xs mt-1">{{ form.includes_in.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200">
                    {{ form.perticulars }}
                    {% if form.perticulars.errors %}<p class="text-red-500 text-xs mt-1">{{ form.perticulars.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    {{ form.qty }}
                    {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    {{ form.amount }}
                    {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    {# Calculate total dynamically on client-side with Alpine.js or HTMX #}
                    <span x-data="{ qty: parseFloat($el.closest('tr').querySelector('[name$=\"-qty\"]').value || 0), amount: parseFloat($el.closest('tr').querySelector('[name$=\"-amount\"]').value || 0) }"
                          x-init="$watch('$el.closest(\'tr\').querySelector(\'[name$=\"-qty\"]\').value', value => qty = parseFloat(value || 0)); $watch('$el.closest(\'tr\').querySelector(\'[name$=\"-amount\"]\').value', value => amount = parseFloat(value || 0));"
                          x-text="(qty * amount).toFixed(2)">
                        {% if form.instance.pk %}{{ form.instance.total|floatformat:2 }}{% else %}0.00{% endif %}
                    </span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {{ form.DELETE }}
                    <button type="button" 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                            hx-post="{% url 'hrmodules:offerletter_accessories_table_partial' %}" 
                            hx-vals='{"{{ form.DELETE.name }}": "on", "{{ form.prefix }}-TOTAL_FORMS": "{{ accessory_formset.total_form_count }}", "{{ form.prefix }}-INITIAL_FORMS": "{{ accessory_formset.initial_form_count }}", "{{ form.prefix }}-MIN_NUM_FORMS": "{{ accessory_formset.min_num }}", "{{ form.prefix }}-MAX_NUM_FORMS": "{{ accessory_formset.max_num }}", "accessory_formset_id_to_delete": "{{ form.instance.pk }}"}'
                            hx-target="#accessorySection"
                            hx-swap="innerHTML"
                            hx-trigger="click"
                            _="on htmx:afterSwap $dispatch('updateSalary')">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="7" class="py-2 px-4 bg-gray-50 border-t border-gray-200 text-right">
                    <button type="button" 
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                            hx-post="{% url 'hrmodules:offerletter_accessories_table_partial' %}" 
                            hx-vals='{"add_accessory": "true", "{{ accessory_formset.management_form.prefix }}-TOTAL_FORMS": "{{ accessory_formset.total_form_count }}", "{{ accessory_formset.management_form.prefix }}-INITIAL_FORMS": "{{ accessory_formset.initial_form_count }}", "{{ accessory_formset.management_form.prefix }}-MIN_NUM_FORMS": "{{ accessory_formset.min_num }}", "{{ accessory_formset.management_form.prefix }}-MAX_NUM_FORMS": "{{ accessory_formset.max_num }}"}'
                            hx-target="#accessorySection"
                            hx-swap="innerHTML"
                            _="on htmx:afterSwap $dispatch('updateSalary')">
                        Add New Accessory
                    </button>
                </td>
            </tr>
        </tfoot>
    </table>
    {{ accessory_formset.management_form }}
</div>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'accessorySection') {
            const accessoryTable = document.getElementById('accessoryTable');
            if ($.fn.DataTable.isDataTable(accessoryTable)) {
                $(accessoryTable).DataTable().destroy(); // Destroy previous DataTable instance
            }
            $(accessoryTable).DataTable({
                "paging": false,
                "searching": false,
                "info": false,
                "lengthChange": false,
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6] } // Disable sorting for SN and Actions columns
                ]
            });
        }
    });

    // Manually trigger recalculation on change of Qty or Amount in accessory rows
    document.addEventListener('change', function(event) {
        const target = event.target;
        if (target.matches('[name$="-qty"]') || target.matches('[name$="-amount"]') || target.matches('[name$="-includes_in"]')) {
            htmx.trigger(document.body, 'updateSalary');
        }
    });
</script>
```

##### `hrmodules/templates/hrmodules/offerletter/list.html` (Main Offer Letter List)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Offer Letters</h2>
        <a href="{% url 'hrmodules:offerletter_add' %}"
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
            Create New Offer
        </a>
    </div>
    
    <div id="offerLetterTableContainer"
         hx-trigger="load, refreshOfferLetterList from:body"
         hx-get="{% url 'hrmodules:offerletter_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Offer Letters...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed (not strictly used here, but good practice for frontend state)
    document.addEventListener('alpine:init', () => {
        Alpine.data('offerLetterList', () => ({
            // Any state for the list page
        }));
    });
</script>
{% endblock %}
```

##### `hrmodules/templates/hrmodules/offerletter/_offerletter_table.html` (Offer Letter List Partial)

```html
<table id="offerLetterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Salary</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in offer_letters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.designation }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.salary|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sys_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 flex justify-center space-x-2">
                <a href="{% url 'hrmodules:offerletter_edit' pk=obj.pk %}"
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md text-xs">
                    Edit
                </a>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-post="{% url 'hrmodules:offerletter_delete' pk=obj.pk %}"
                    hx-confirm="Are you sure you want to delete this offer letter for {{ obj.employee_name }}?"
                    hx-swap="none"
                    _="on htmx:afterRequest $dispatch('refreshOfferLetterList')">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No offer letters found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#offerLetterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

#### 4.5 URLs (`hrmodules/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**

*   Create paths for the main form (add/edit), salary calculation partial, accessory table partial, staff auto-complete, and the list/delete views.
*   Use consistent naming conventions.

```python
# hrmodules/urls.py
from django.urls import path
from .views import (
    OfferLetterCreateUpdateView, SalaryBreakdownPartialView, 
    OfferAccessoryTablePartialView, StaffAutoCompleteView,
    OfferLetterListView, OfferLetterTablePartialView, OfferLetterDeleteView
)

app_name = 'hrmodules' # Namespace for URLs

urlpatterns = [
    # Main Offer Letter Form (Create and Update)
    path('offer-letter/add/', OfferLetterCreateUpdateView.as_view(), name='offerletter_add'),
    path('offer-letter/edit/<int:pk>/', OfferLetterCreateUpdateView.as_view(), name='offerletter_edit'),
    
    # HTMX Endpoints for partial updates
    path('offer-letter/calculate-salary/', SalaryBreakdownPartialView.as_view(), name='calculate_salary_partial'),
    path('offer-letter/accessories-table/', OfferAccessoryTablePartialView.as_view(), name='offerletter_accessories_table_partial'),
    
    # Auto-complete endpoint
    path('staff-autocomplete/', StaffAutoCompleteView.as_view(), name='staff_autocomplete'),

    # List and Delete Views
    path('offer-letters/', OfferLetterListView.as_view(), name='offerletter_list'),
    path('offer-letters/table/', OfferLetterTablePartialView.as_view(), name='offerletter_table_partial'),
    path('offer-letters/delete/<int:pk>/', OfferLetterDeleteView.as_view(), name='offerletter_delete'),

    # Example for fetching PF slab
    path('get-pf-slab/', lambda r: JsonResponse(model_to_dict(PF_Slab.objects.filter(active=True).first())), name='get_pf_slab'),
]
```

#### 4.6 Tests (`hrmodules/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and data integrity.

**Instructions:**

*   Include tests for all model methods, especially the `calculate_salary_components`.
*   Test view rendering, form submission (valid/invalid), and HTMX interactions.
*   Aim for at least 80% test coverage.

```python
# hrmodules/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    OfferLetter, Designation, DutyHour, OTHour, OverTime, 
    EmployeeType, IncludesIn, PF_Slab, OfficeStaff, OfferAccessory
)
import json

class HRModuleSetupMixin(TestCase):
    """Mixin to set up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create lookup data
        cls.designation = Designation.objects.create(id=1, designation='Software Engineer')
        cls.duty_hour = DutyHour.objects.create(id=1, hours='9 AM - 6 PM')
        cls.ot_hour = OTHour.objects.create(id=1, hours='2 Hrs')
        cls.overtime_option = OverTime.objects.create(id=1, description='Applicable')
        cls.emp_type_sapl = EmployeeType.objects.create(id=1, description='SAPL')
        cls.emp_type_neha = EmployeeType.objects.create(id=2, description='NEHA')
        cls.emp_type_casuals = EmployeeType.objects.create(id=3, description='Casuals') # Assuming ID 3 for Casuals
        cls.includes_ctc = IncludesIn.objects.create(id=1, includes_in='CTC')
        cls.includes_take_home = IncludesIn.objects.create(id=2, includes_in='Take Home')
        cls.includes_both = IncludesIn.objects.create(id=3, includes_in='Both')
        cls.pf_slab = PF_Slab.objects.create(id=1, pf_employee=12.0, pf_company=12.0, active=True)
        cls.interviewer = OfficeStaff.objects.create(emp_id=101, employee_name='John Doe')
        cls.authorizer = OfficeStaff.objects.create(emp_id=102, employee_name='Jane Smith')

        # Create a sample OfferLetter instance
        cls.offer_letter = OfferLetter.objects.create(
            offer_id=1,
            fin_year_id=2023,
            comp_id=1,
            session_id='testuser',
            title='Mr.',
            employee_name='Test Employee',
            type_of=cls.emp_type_sapl.id,
            staff_type=cls.emp_type_sapl,
            salary=50000.00,
            duty_hrs=cls.duty_hour,
            ot_hrs=cls.ot_hour,
            overtime_applicable=cls.overtime_option,
            address='123 Test St',
            contact_no='1234567890',
            email_id='<EMAIL>',
            interviewed_by=cls.interviewer,
            authorized_by=cls.authorizer,
            reference_by='Referrer',
            designation=cls.designation,
            ex_gratia=1000.0,
            vehicle_allowance=500.0,
            lta=1200.0,
            loyalty=800.0,
            paid_leaves=15.0,
            header_text='Header content',
            footer_text='Footer content',
            remarks='Some remarks',
            bonus=2000.0, # Monthly bonus
            att_bonus_per1=10.0,
            att_bonus_per2=20.0,
            pf_employee_perc=12.0,
            pf_company_perc=12.0,
        )
        # Add an accessory to the sample offer letter
        OfferAccessory.objects.create(
            offer_master=cls.offer_letter,
            perticulars='Laptop',
            qty=1.0,
            amount=5000.0,
            includes_in=cls.includes_ctc
        )
        OfferAccessory.objects.create(
            offer_master=cls.offer_letter,
            perticulars='Mobile Allowance',
            qty=1.0,
            amount=1000.0,
            includes_in=cls.includes_take_home
        )

class OfferLetterModelTest(HRModuleSetupMixin):
    def test_offer_letter_creation(self):
        self.assertEqual(self.offer_letter.employee_name, 'Test Employee')
        self.assertEqual(self.offer_letter.designation, self.designation)
        self.assertAlmostEqual(self.offer_letter.salary, 50000.00)
        self.assertEqual(self.offer_letter.accessories.count(), 2)

    def test_offer_accessory_total_property(self):
        accessory = OfferAccessory.objects.get(perticulars='Laptop')
        self.assertAlmostEqual(accessory.total, 5000.0)

    def test_calculate_salary_components_basic(self):
        # Test with a basic salary calculation for SAPL type
        gross_salary = 50000.0
        type_of_id = self.emp_type_sapl.id
        staff_type_desc = self.emp_type_sapl.description
        att_bonus_per1 = 10.0
        att_bonus_per2 = 20.0
        pf_employee_perc = 12.0
        pf_company_perc = 12.0
        bonus_amount = 2000.0 # Monthly bonus
        ex_gratia = 1000.0
        lta = 1200.0
        loyalty = 800.0
        paid_leaves = 15.0

        accessories_data = [
            {'qty': 1.0, 'amount': 5000.0, 'includes_in_id': self.includes_ctc.id},
            {'qty': 1.0, 'amount': 1000.0, 'includes_in_id': self.includes_take_home.id},
            {'qty': 1.0, 'amount': 200.0, 'includes_in_id': self.includes_both.id},
        ]

        # Use a temporary instance for calculation
        temp_offer = OfferLetter(
            type_of=type_of_id,
            staff_type=self.emp_type_sapl, # Needed for gratuity if not None
        )
        calculated = temp_offer.calculate_salary_components(
            gross_salary=gross_salary,
            type_of_id=type_of_id,
            staff_type_desc=staff_type_desc,
            att_bonus_per1=att_bonus_per1,
            att_bonus_per2=att_bonus_per2,
            pf_employee_perc=pf_employee_perc,
            pf_company_perc=pf_company_perc,
            bonus_amount=bonus_amount,
            ex_gratia=ex_gratia,
            lta=lta,
            loyalty=loyalty,
            paid_leaves=paid_leaves,
            accessories_data=accessories_data
        )

        # Assert some key calculated values
        self.assertAlmostEqual(calculated['annual_salary'], 50000 * 12)
        self.assertAlmostEqual(calculated['g_basic'], 50000 * 0.30)
        self.assertAlmostEqual(calculated['att_bonus1'], 50000 * (10 / 100))
        # Add more assertions for specific values based on the expected calculations
        # Example for take home: (Gross + ExGr + Acc_TakeHome + Acc_Both) - (PFE + PTax)
        expected_pfe = min(15000 * (12 / 100), gross_salary * (12/100)) # Based on _get_pf_value example
        expected_ptax = 500 # Based on _get_ptax_value example (assuming > 25000)
        expected_take_home = round((gross_salary + ex_gratia + 1000.0 + 200.0) - (expected_pfe + expected_ptax), 2)
        self.assertAlmostEqual(calculated['take_home'], expected_take_home)

    def test_calculate_salary_components_casuals(self):
        # Test calculations for casuals, where certain fields should be zero
        gross_salary = 20000.0
        type_of_id = self.emp_type_sapl.id
        staff_type_desc = self.emp_type_casuals.description # Key difference
        att_bonus_per1 = 10.0 # Should be overridden to 0
        att_bonus_per2 = 20.0 # Should be overridden to 0
        pf_employee_perc = 12.0 # Should be overridden to 0
        pf_company_perc = 12.0 # Should be overridden to 0
        bonus_amount = 2000.0 # Should be overridden to 0
        ex_gratia = 0.0
        lta = 0.0
        loyalty = 0.0
        paid_leaves = 0.0
        accessories_data = []

        temp_offer = OfferLetter(
            type_of=type_of_id,
            staff_type=self.emp_type_casuals,
        )
        calculated = temp_offer.calculate_salary_components(
            gross_salary=gross_salary,
            type_of_id=type_of_id,
            staff_type_desc=staff_type_desc,
            att_bonus_per1=att_bonus_per1,
            att_bonus_per2=att_bonus_per2,
            pf_employee_perc=pf_employee_perc,
            pf_company_perc=pf_company_perc,
            bonus_amount=bonus_amount,
            ex_gratia=ex_gratia,
            lta=lta,
            loyalty=loyalty,
            paid_leaves=paid_leaves,
            accessories_data=accessories_data
        )

        self.assertAlmostEqual(calculated['att_bonus1'], 0.0)
        self.assertAlmostEqual(calculated['pf_employee'], 0.0)
        self.assertAlmostEqual(calculated['bonus_amount'], 0.0)
        self.assertAlmostEqual(calculated['gratuity_monthly'], 0.0) # Gratuity also 0 for Casuals

    def test_calculate_salary_components_neha_gratuity(self):
        # Test NEHA type where gratuity should be 0
        gross_salary = 40000.0
        type_of_id = self.emp_type_neha.id
        staff_type_desc = self.emp_type_neha.description

        temp_offer = OfferLetter(
            type_of=type_of_id,
            staff_type=self.emp_type_neha,
        )
        calculated = temp_offer.calculate_salary_components(
            gross_salary=gross_salary,
            type_of_id=type_of_id,
            staff_type_desc=staff_type_desc,
            att_bonus_per1=0, att_bonus_per2=0, pf_employee_perc=0, pf_company_perc=0,
            bonus_amount=0, ex_gratia=0, lta=0, loyalty=0, paid_leaves=0,
            accessories_data=[]
        )
        self.assertAlmostEqual(calculated['gratuity_monthly'], 0.0)
        self.assertAlmostEqual(calculated['gratuity_annual'], 0.0)

class OfferLetterViewsTest(HRModuleSetupMixin):
    def setUp(self):
        self.client = Client()
        # Mock session data as ASP.NET uses Session["compid"], etc.
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session['username'] = 'testuser'
        session.save()

    def _get_valid_form_data(self):
        return {
            'title': 'Mr.',
            'employee_name': 'New Applicant',
            'designation': self.designation.id,
            'duty_hrs': self.duty_hour.id,
            'ot_hrs': self.ot_hour.id,
            'overtime_applicable': self.overtime_option.id,
            'type_of': str(self.emp_type_sapl.id), # Dropdown values are strings
            'staff_type': self.emp_type_sapl.id,
            'contact_no': '9876543210',
            'address': '456 New Road',
            'email_id': '<EMAIL>',
            'interviewed_by_name': f'{self.interviewer.employee_name} [{self.interviewer.emp_id}]',
            'authorized_by_name': f'{self.authorizer.employee_name} [{self.authorizer.emp_id}]',
            'reference_by': 'New Referrer',
            'salary': 60000.0,
            'ex_gratia': 1500.0,
            'vehicle_allowance': 600.0,
            'lta': 1500.0,
            'loyalty': 1000.0,
            'paid_leaves': 20.0,
            'pf_employee_perc': 12.0,
            'pf_company_perc': 12.0,
            'att_bonus_per1': 10.0,
            'att_bonus_per2': 20.0,
            'bonus': 2500.0,
            'header_text': 'New Header Content',
            'footer_text': 'New Footer Content',
            'remarks': 'New Remarks',
            # Formset management data
            'accessories-TOTAL_FORMS': '0',
            'accessories-INITIAL_FORMS': '0',
            'accessories-MIN_NUM_FORMS': '0',
            'accessories-MAX_NUM_FORMS': '1000',
        }

    def test_offerletter_add_get(self):
        response = self.client.get(reverse('hrmodules:offerletter_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hrmodules/offerletter/form.html')
        self.assertIsInstance(response.context['form'], OfferLetterForm)
        self.assertIsInstance(response.context['accessory_formset'], OfferAccessoryFormSet)

    def test_offerletter_add_post_success(self):
        data = self._get_valid_form_data()
        initial_count = OfferLetter.objects.count()
        
        response = self.client.post(reverse('hrmodules:offerletter_add'), data, follow=True)
        self.assertEqual(response.status_code, 200) # Should be 200 after redirect/HX-Trigger handling
        self.assertEqual(OfferLetter.objects.count(), initial_count + 1)
        self.assertTrue(OfferLetter.objects.filter(employee_name='New Applicant').exists())
        self.assertContains(response, 'Offer Letter and accessories saved successfully!')

    def test_offerletter_add_post_with_accessories_success(self):
        data = self._get_valid_form_data()
        data.update({
            'accessories-TOTAL_FORMS': '1',
            'accessories-INITIAL_FORMS': '0',
            'accessories-0-perticulars': 'Test Accessory',
            'accessories-0-qty': '1',
            'accessories-0-amount': '1000',
            'accessories-0-includes_in': str(self.includes_ctc.id),
        })
        
        initial_offer_count = OfferLetter.objects.count()
        initial_accessory_count = OfferAccessory.objects.count()

        response = self.client.post(reverse('hrmodules:offerletter_add'), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(OfferLetter.objects.count(), initial_offer_count + 1)
        # Check that the accessory was saved with the new offer letter
        new_offer = OfferLetter.objects.get(employee_name='New Applicant')
        self.assertEqual(new_offer.accessories.count(), 1)
        self.assertEqual(OfferAccessory.objects.count(), initial_accessory_count + 1)

    def test_offerletter_add_post_invalid_data(self):
        data = self._get_valid_form_data()
        data['employee_name'] = '' # Make it invalid
        initial_count = OfferLetter.objects.count()
        
        response = self.client.post(reverse('hrmodules:offerletter_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertEqual(OfferLetter.objects.count(), initial_count) # No new object created
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Please correct the errors in the form.') # Message for general errors

    def test_offerletter_edit_get(self):
        response = self.client.get(reverse('hrmodules:offerletter_edit', args=[self.offer_letter.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hrmodules/offerletter/form.html')
        self.assertContains(response, self.offer_letter.employee_name)
        self.assertContains(response, self.offer_letter.header_text)

    def test_offerletter_edit_post_success(self):
        data = self._get_valid_form_data()
        data['employee_name'] = 'Updated Name'
        
        response = self.client.post(reverse('hrmodules:offerletter_edit', args=[self.offer_letter.pk]), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.offer_letter.refresh_from_db()
        self.assertEqual(self.offer_letter.employee_name, 'Updated Name')
        self.assertContains(response, 'Offer Letter and accessories saved successfully!')

    def test_salary_breakdown_partial_post(self):
        data = {
            'salary': '50000.0',
            'type_of': str(self.emp_type_sapl.id),
            'staff_type': str(self.emp_type_sapl.id),
            'att_bonus_per1': '10', 'att_bonus_per2': '20',
            'pf_employee_perc': '12', 'pf_company_perc': '12',
            'bonus': '2000', 'ex_gratia': '1000', 'lta': '0', 'loyalty': '0', 'paid_leaves': '0', 'vehicle_allowance': '0',
            'accessories-TOTAL_FORMS': '1',
            'accessories-INITIAL_FORMS': '0',
            'accessories-0-perticulars': 'Test Acc',
            'accessories-0-qty': '1',
            'accessories-0-amount': '100',
            'accessories-0-includes_in': str(self.includes_both.id),
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hrmodules:calculate_salary_partial'), data, **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hrmodules/offerletter/_salary_breakdown.html')
        self.assertContains(response, '50000.00') # Gross Salary
        self.assertContains(response, '15000.00') # Basic (30% of 50000)
        self.assertContains(response, '42500.00') # CTC (50000 + 2000 + 0 + 0 + (gratuity) + PFCompany + 1000 + 100) - based on basic calc in _get_gratuity_value

    def test_offer_accessory_table_partial_add_row(self):
        # Simulate adding a new empty row to the formset
        data = {
            'add_accessory': 'true',
            'accessories-TOTAL_FORMS': '0', # Initial empty formset
            'accessories-INITIAL_FORMS': '0',
            'accessories-MIN_NUM_FORMS': '0',
            'accessories-MAX_NUM_FORMS': '1000',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hrmodules:offerletter_accessories_table_partial'), data, **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hrmodules/offerletter/_accessory_table.html')
        self.assertContains(response, 'name="accessories-0-perticulars"') # Should have one new form

    def test_staff_autocomplete_view(self):
        response = self.client.get(reverse('hrmodules:staff_autocomplete'), {'q': 'John'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'{self.interviewer.employee_name} [{self.interviewer.emp_id}]', response.content.decode())

    def test_offerletter_list_view(self):
        response = self.client.get(reverse('hrmodules:offerletter_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hrmodules/offerletter/list.html')
        self.assertIn('offer_letters', response.context)
        self.assertContains(response, self.offer_letter.employee_name)

    def test_offerletter_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hrmodules:offerletter_table_partial'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hrmodules/offerletter/_offerletter_table.html')
        self.assertContains(response, self.offer_letter.employee_name)
        
    def test_offerletter_delete_view_post(self):
        offer_to_delete = OfferLetter.objects.create(
            fin_year_id=2023, comp_id=1, session_id='deluser', title='Mr.', 
            employee_name='Delete Me', type_of=1, staff_type=self.emp_type_sapl, salary=1000,
            address='abc', contact_no='123', email_id='<EMAIL>', 
            interviewed_by=self.interviewer, authorized_by=self.authorizer,
            header_text='h', footer_text='f'
        )
        initial_count = OfferLetter.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hrmodules:offerletter_delete', args=[offer_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(OfferLetter.objects.count(), initial_count - 1)
        self.assertFalse(OfferLetter.objects.filter(pk=offer_to_delete.pk).exists())
```

---

### Step 5: HTMX and Alpine.js Integration

**Task:** Ensure all dynamic interactions are handled using HTMX for server-side updates and Alpine.js for local UI state.

**Instructions (already integrated into templates and views):**

*   **Form Submission:** The main form uses `hx-post` to `.` (the current URL), which allows the `OfferLetterCreateUpdateView` to handle both `GET` (initial load/edit) and `POST` (submission). On successful save, `HX-Trigger` is used to refresh the entire form context, including reloading associated partials, mimicking the ASP.NET full postback behavior but with a cleaner HTMX swap.
*   **Salary Recalculation:**
    *   Input fields that affect salary (`salary`, `ex_gratia`, `att_bonus_per1`, etc., and dropdowns `type_of`, `staff_type`) have `hx-post` attributes targeting `{% url 'hrmodules:calculate_salary_partial' %}` and `hx-target="#salaryBreakdown"`.
    *   `hx-trigger="change delay:300ms, updateSalary"` ensures that calculations are triggered after a small delay on input changes, and also by a custom `updateSalary` event dispatched from Alpine.js or HTMX events.
*   **Accessories Management (Formset):**
    *   The `_accessory_table.html` partial itself is loaded into `#accessorySection` using HTMX.
    *   The "Add New Accessory" button uses `hx-post` to `{% url 'hrmodules:offerletter_accessories_table_partial' %}` to re-render the formset with a new empty form.
    *   The "Delete" button for each accessory row also uses `hx-post` to the same partial, passing the `DELETE` flag for the specific form.
    *   After adding/deleting accessories, `hx-trigger="endSettle from:#accessoryFormset, updateSalary"` ensures that the salary breakdown is re-calculated.
*   **Auto-Complete:**
    *   `Txtinterviewedby` and `TxtAuthorizedby` inputs use `hx-get` to `{% url 'hrmodules:staff_autocomplete' %}`.
    *   `hx-trigger="keyup changed delay:500ms"` sends the request after typing.
    *   `hx-target` is a `div` for suggestions, and `hx-swap="innerHTML"` populates it. Simple JavaScript then handles selection from suggestions.
*   **DataTables:**
    *   The `_offerletter_table.html` and `_accessory_table.html` partials include a `<script>` block that initializes DataTables on the respective table IDs.
    *   `htmx:afterSwap` event listener ensures DataTables is re-initialized after HTMX replaces content.
*   **Alpine.js:**
    *   Used for simple UI state management, e.g., enabling/disabling PF/Bonus fields based on "Casuals" staff type. This simplifies client-side interactivity without complex JavaScript frameworks.

---

### Final Notes

*   **Placeholders:** Replace `{{ staff_type_casuals_id }}` in `form.html` with the actual ID of the 'Casuals' `EmployeeType` from your database (e.g., pass it in the `OfferLetterCreateUpdateView` context).
*   **`fun` Class Functions:** The `_get_offer_component_value`, `_get_pf_value`, `_get_ptax_value`, `_get_gratuity_value` methods in `models.py` are placeholders. **Crucially, these need to be accurately reverse-engineered from your original `clsFunctions` C# code or database stored procedures/logic to ensure the salary calculation logic is identical.** This is a critical point for the automation tool's success.
*   **Error Handling:** The current views provide basic `messages.error` feedback. For production, more sophisticated error handling and user feedback mechanisms (e.g., toast notifications) might be desired.
*   **Security:** Ensure proper authentication and authorization are implemented in your Django project (e.g., using `LoginRequiredMixin` for views).
*   **Deployment:** Set up a robust Django deployment environment (e.g., Gunicorn/uWSGI + Nginx).
*   **Continuous Integration/Deployment:** Automate testing and deployment pipelines to ensure smooth updates.

This comprehensive plan provides a detailed roadmap for converting your ASP.NET Offer Letter functionality to a modern Django application. The focus on AI-assisted automation means these steps can be systematically converted and verified, significantly reducing manual effort and accelerating your modernization journey.