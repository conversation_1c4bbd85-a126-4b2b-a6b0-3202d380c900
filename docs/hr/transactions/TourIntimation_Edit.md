This document outlines a comprehensive plan for migrating the provided ASP.NET Tour Intimation Edit module to a modern Django-based solution. Our approach emphasizes automation, clear communication, and the adoption of best practices like a fat model/thin view architecture, HTMX for dynamic interactions, and DataTables for efficient data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we identify the primary table `tblACC_TourIntimation_Master` and several lookup tables.

**Main Table:** `tblACC_TourIntimation_Master`
**Columns (inferred):**
- `Id` (Primary Key, integer)
- `TINo` (Tour Intimation Number, string)
- `EmpId` (Employee ID, string, likely FK to `tblHR_OfficeStaff`)
- `FinYearId` (Financial Year ID, integer, FK to `tblFinancial_master`)
- `WONo` (Work Order Number, string)
- `BGGroupId` (Business Group ID, integer, FK to `BusinessGroup`)
- `ProjectName` (Project Name, string)
- `PlaceOfTourCity` (City ID, integer, FK to `tblCity`)
- `PlaceOfTourState` (State ID, integer, FK to `tblState`)
- `PlaceOfTourCountry` (Country ID, integer, FK to `tblCountry`)
- `TourStartDate` (Tour Start Date, datetime/date)
- `TourEndDate` (Tour End Date, datetime/date)
- `SessionId` (Session ID, string, likely user-related)
- `CompId` (Company ID, integer, for multi-tenancy)

**Lookup Tables (inferred from `binddata` and `DrpField_SelectedIndexChanged`):**
- `tblFinancial_master`: `FinYearId`, `FinYear`
- `tblHR_OfficeStaff`: `EmpId` (string, unique identifier), `EmployeeName`, `Title`
- `BusinessGroup`: `Id`, `Symbol`
- `tblCity`: `CityId`, `CityName`
- `tblState`: `SId`, `StateName`
- `tblCountry`: `CId`, `CountryName`
- `tblACC_TourVoucher_Master`: `TIMId` (Tour Intimation Master ID, integer), `CompId`, `FinYearId` (used for filtering out already vouchered items)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The module primarily focuses on "Read" functionality with search and filtering, along with an implicit "Edit" (redirect to `TourIntimation_Edit_Details.aspx`).

- **Create:** Not directly observed in this specific ASPX/C# pair for `TourIntimation_Edit`. The current page is for *editing* existing records.
- **Read:**
    - Displays a list of tour intimations in a `GridView`.
    - Features extensive filtering/searching capabilities based on `TI No`, `Employee Name`, `WO No`, `BG Group`, and `Project Name`.
    - Applies global filters based on `Session["compid"]`, `Session["finyear"]`, and `Session["username"]`.
    - Filters out tour intimations that have associated tour vouchers (`tblACC_TourVoucher_Master`).
    - Involves complex data retrieval from multiple lookup tables to display meaningful names instead of IDs.
- **Update:** Implied by the "Edit" page name and the `LinkButton` in `GridView2` that redirects to `TourIntimation_Edit_Details.aspx?Id=...`. This suggests a dedicated edit view for a single record.
- **Delete:** Not explicitly present in the provided code.
- **Validation Logic:** Not explicit on the current page, but assumed for underlying data entry (handled in the `TourIntimation_Edit_Details.aspx` page or database constraints).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
- **Search Controls:**
    - `DrpField` (DropDownList): Selects the search criterion. This will be a Django `forms.ChoiceField` with HTMX for dynamic visibility of other fields.
    - `TxtMrs` (TextBox): General text input for search (TI No, WO No, Project Name).
    - `TxtEmpName` (TextBox) with `AutoCompleteExtender`: Text input for Employee Name with auto-completion. This will require a separate HTMX endpoint for autocomplete suggestions.
    - `drpGroup` (DropDownList): Selects Business Group. This will be a Django `forms.ModelChoiceField`.
    - `Button1` (Button): Triggers the search. This will submit the search form via HTMX.
- **Data Display:**
    - `GridView2` (GridView): Displays tabular data with pagination. This will be converted to an HTML `<table>` driven by DataTables.
- **Actions:**
    - `LinkButton` for `TI No`: Redirects to a detailed edit page. This will be an HTMX-powered button/link that loads an edit form into a modal.

### Step 4: Generate Django Code

We will create a new Django app named `hr_tour` for this module.

#### 4.1 Models
**Task:** Create Django models based on the database schema, including related lookup tables.

**Instructions:**
Models are defined with `managed = False` and `db_table` to map to existing database tables. Relationships are defined to allow efficient querying using `select_related` and `prefetch_related`. Business logic methods are added to the `TourIntimation` model for data transformation (e.g., `display_place_of_tour`).

**File: `hr_tour/models.py`**
```python
from django.db import models
from django.urls import reverse
from django.db.models import Q # For complex queries

# Helper model for Financial Year lookup
class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

# Helper model for Office Staff/Employees
class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is unique and text-based
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name} [{self.emp_id}]".strip()

# Helper model for Business Group
class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

# Helper model for City
class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

# Helper model for State
class State(models.Model):
    sid = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

# Helper model for Country
class Country(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

# Helper model for Tour Voucher Master (used for filtering)
class TourVoucher(models.Model):
    # This model doesn't need all fields, just enough to check for existence
    tim_id = models.IntegerField(db_column='TIMId', primary_key=True) # Assuming TIMId is PK or unique
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucher_Master'
        verbose_name = 'Tour Voucher'
        verbose_name_plural = 'Tour Vouchers'

    def __str__(self):
        return f"Voucher for TIMId: {self.tim_id}"


class TourIntimationManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().select_related(
            'financial_year', 
            'employee', 
            'business_group',
            'tour_city',
            'tour_state',
            'tour_country'
        )

    def filter_by_user_context(self, user, search_field, search_value):
        # Apply filters based on session variables from ASP.NET
        # Assuming user has 'compid' and 'finyear' attributes
        # And 'SessionId' maps to user.username or user.id
        queryset = self.get_queryset().filter(
            comp_id=user.compid, 
            fin_year_id__lte=user.finyear, # Assuming FinYearId <= FyId as in ASP.NET
            session_id=user.username # Assuming SessionId is user's username
        )

        # Exclude records that have an entry in tblACC_TourVoucher_Master
        # This translates the ASP.NET logic: if (DSTIMId.Tables[0].Rows.Count == 0)
        # We need to exclude where a TIMId exists in TourVoucher with the same CompId and FinYearId
        # Using a subquery for efficient exclusion:
        voucher_exists_q = TourVoucher.objects.filter(
            tim_id=models.OuterRef('pk'), # pk of TourIntimation
            comp_id=user.compid,
            fin_year_id__lte=user.finyear
        ).values('tim_id')
        queryset = queryset.annotate(has_voucher=models.Subquery(voucher_exists_q[:1])).filter(has_voucher__isnull=True)


        # Apply search filters based on DrpField selection
        if search_field == '0': # TI No
            if search_value:
                queryset = queryset.filter(ti_no=search_value)
        elif search_field == '1': # Employee Name
            if search_value: # search_value is EmpId, because `fun.getCode` is used
                queryset = queryset.filter(employee__emp_id=search_value)
        elif search_field == '2': # WO No
            if search_value:
                queryset = queryset.filter(wo_no=search_value)
        elif search_field == '3': # BG Group (search_value is BGGroupId)
            if search_value:
                queryset = queryset.filter(bg_group_id=search_value)
        elif search_field == '4': # Project Name
            if search_value:
                queryset = queryset.filter(project_name__icontains=search_value) # like '%...%'

        return queryset.order_by('-id') # Order by Id Desc

class TourIntimation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ti_no = models.CharField(db_column='TINo', max_length=50)
    emp_id_code = models.CharField(db_column='EmpId', max_length=50) # Raw EmpId code from DB
    fin_year_id = models.IntegerField(db_column='FinYearId') # Raw FinYearId from DB
    wo_no = models.CharField(db_column='WONo', max_length=100)
    bg_group_id = models.IntegerField(db_column='BGGroupId') # Raw BGGroupId from DB
    project_name = models.CharField(db_column='ProjectName', max_length=255)
    place_of_tour_city_id = models.IntegerField(db_column='PlaceOfTourCity') # Raw CityId from DB
    place_of_tour_state_id = models.IntegerField(db_column='PlaceOfTourState') # Raw StateId from DB
    place_of_tour_country_id = models.IntegerField(db_column='PlaceOfTourCountry') # Raw CountryId from DB
    tour_start_date = models.DateField(db_column='TourStartDate')
    tour_end_date = models.DateField(db_column='TourEndDate')
    session_id = models.CharField(db_column='SessionId', max_length=100)
    comp_id = models.IntegerField(db_column='CompId')

    # Foreign key relationships for easier access to related data
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', to_field='fin_year_id', related_name='tour_intimations_for_year', blank=True, null=True)
    employee = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='emp_id', related_name='tour_intimations_by_employee', blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroupId', to_field='id', related_name='tour_intimations_in_group', blank=True, null=True)
    tour_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCity', to_field='city_id', related_name='tours_from_city', blank=True, null=True)
    tour_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='PlaceOfTourState', to_field='sid', related_name='tours_from_state', blank=True, null=True)
    tour_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCountry', to_field='cid', related_name='tours_from_country', blank=True, null=True)

    objects = TourIntimationManager() # Custom manager for enhanced queryset

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation'
        verbose_name_plural = 'Tour Intimations'

    def __str__(self):
        return self.ti_no

    @property
    def fin_year_display(self):
        return self.financial_year.fin_year if self.financial_year else 'N/A'

    @property
    def emp_name_display(self):
        return self.employee.employee_name if self.employee else 'N/A'

    @property
    def bg_group_display(self):
        if self.bg_group_id == 1: # Assuming '1' means 'NA' or work order is applicable
            return 'NA'
        return self.business_group.symbol if self.business_group else 'N/A'

    @property
    def wo_no_display(self):
        if self.bg_group_id == 1: # Assuming '1' means WO No is applicable
            return self.wo_no
        return 'NA'
        
    @property
    def place_of_tour_display(self):
        country_name = self.tour_country.country_name if self.tour_country else ''
        state_name = self.tour_state.state_name if self.tour_state else ''
        city_name = self.tour_city.city_name if self.tour_city else ''
        
        parts = [p for p in [country_name, state_name, city_name] if p]
        return ", ".join(parts) if parts else 'N/A'

    @property
    def tour_start_date_dmy(self):
        return self.tour_start_date.strftime('%d/%m/%Y') if self.tour_start_date else ''

    @property
    def tour_end_date_dmy(self):
        return self.tour_end_date.strftime('%d/%m/%Y') if self.tour_end_date else ''

    # Example for redirect target if TourIntimation_Edit_Details.aspx is for update
    def get_absolute_url(self):
        return reverse('tourintimation_edit', kwargs={'pk': self.pk})

```

#### 4.2 Forms
**Task:** Define Django forms for search criteria and the tour intimation itself.

**Instructions:**
A non-ModelForm is used for the search section, allowing flexible field visibility based on dropdown selection. A ModelForm is provided as a placeholder for the actual `TourIntimation` record editing.

**File: `hr_tour/forms.py`**
```python
from django import forms
from .models import BusinessGroup, OfficeStaff, TourIntimation

class TourIntimationSearchForm(forms.Form):
    SEARCH_FIELDS = [
        ('Select', 'Select'),
        ('0', 'TI No'),
        ('1', 'Employee Name'),
        ('2', 'WO No'),
        ('3', 'BG Group'),
        ('4', 'Project Name'),
    ]
    
    drp_field = forms.ChoiceField(
        choices=SEARCH_FIELDS,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3'}),
        initial='Select'
    )
    
    txt_mrs = forms.CharField(
        label="Search Text (TI No, WO No, Project Name)",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-[150px]', 'placeholder': 'Enter value'})
    )
    
    txt_emp_name = forms.CharField(
        label="Employee Name",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-[250px]', 'placeholder': 'Type employee name', 'hx-get': '/hr_tour/autocomplete/employees/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#emp-suggestions', 'autocomplete': 'off'})
    )

    # Use a hidden field for actual EmpId, populated by autocomplete selection
    emp_id_code = forms.CharField(
        widget=forms.HiddenInput(), 
        required=False
    )
    
    drp_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        label="BG Group",
        required=False,
        empty_label="Select Business Group",
        widget=forms.Select(attrs={'class': 'box3'})
    )

    # Custom validation for employee name to extract ID from string
    def clean_txt_emp_name(self):
        emp_name_with_id = self.cleaned_data.get('txt_emp_name')
        if emp_name_with_id:
            # Assumes format "Employee Name [EmpId]"
            import re
            match = re.search(r'\[(.*?)\]$', emp_name_with_id)
            if match:
                emp_id = match.group(1)
                if not OfficeStaff.objects.filter(emp_id=emp_id).exists():
                    raise forms.ValidationError("Invalid Employee selected.")
                self.cleaned_data['emp_id_code'] = emp_id # Store the actual EmpId
                return emp_name_with_id
            else:
                raise forms.ValidationError("Please select an employee from the autocomplete suggestions.")
        return emp_name_with_id


# Placeholder for TourIntimation (Edit) Form
class TourIntimationForm(forms.ModelForm):
    class Meta:
        model = TourIntimation
        fields = [
            'ti_no', 'emp_id_code', 'fin_year_id', 'wo_no', 
            'bg_group_id', 'project_name', 'place_of_tour_city_id', 
            'place_of_tour_state_id', 'place_of_tour_country_id', 
            'tour_start_date', 'tour_end_date'
            # SessionId and CompId usually derived from context/user
        ]
        widgets = {
            'ti_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'emp_id_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bg_group_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_city_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_state_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_country_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tour_start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'tour_end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
```

#### 4.3 Views
**Task:** Implement search and display using CBVs, and HTMX partials.

**Instructions:**
`TourIntimationListView` handles the initial page load and search form submission. `TourIntimationTablePartialView` is an HTMX endpoint that renders only the table. `TourIntimationAutocompleteView` provides employee name suggestions. `TourIntimationUpdateView` serves as the target for editing. User context (compid, finyear, username) is mocked for demonstration but should be replaced with actual user session data.

**File: `hr_tour/views.py`**
```python
from django.views.generic import ListView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, get_object_or_404
from django.db.models import Q # For dynamic search queries

from .models import TourIntimation, OfficeStaff, BusinessGroup # Import necessary models
from .forms import TourIntimationSearchForm, TourIntimationForm

# Mock User object for demonstration based on ASP.NET Session variables
# In a real application, this would be replaced by actual user authentication
# and user profile/session data.
class MockUser:
    def __init__(self, compid, finyear, username):
        self.compid = compid
        self.finyear = finyear
        self.username = username

# Main view for displaying the search form and initial table
class TourIntimationListView(ListView):
    model = TourIntimation
    template_name = 'hr_tour/tourintimation/list.html'
    context_object_name = 'tour_intimations'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Mock user context. Replace with actual user session data.
        # e.g., user = self.request.user.profile or self.request.user
        user = MockUser(compid=1, finyear=2024, username='testuser') # Example values

        form = TourIntimationSearchForm(self.request.GET)
        if form.is_valid():
            drp_field = form.cleaned_data.get('drp_field')
            txt_mrs = form.cleaned_data.get('txt_mrs')
            txt_emp_name_full = form.cleaned_data.get('txt_emp_name')
            drp_group_obj = form.cleaned_data.get('drp_group')
            
            # Extract EmpId from txt_emp_name if it's an employee search
            search_value = None
            if drp_field == '1': # Employee Name
                # The form's clean_txt_emp_name handles extracting EmpId into emp_id_code
                search_value = form.cleaned_data.get('emp_id_code')
            elif drp_field == '3': # BG Group
                search_value = drp_group_obj.id if drp_group_obj else None
            else: # TI No, WO No, Project Name
                search_value = txt_mrs

            return TourIntimation.objects.filter_by_user_context(
                user, drp_field, search_value
            )
        
        # If form is not valid or not submitted, return default queryset
        return TourIntimation.objects.filter_by_user_context(user, None, None)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate form with current GET parameters for persistence
        context['search_form'] = TourIntimationSearchForm(self.request.GET or None)
        return context

# HTMX partial view for the table content
class TourIntimationTablePartialView(TourIntimationListView):
    template_name = 'hr_tour/tourintimation/_tourintimation_table.html'

    def get(self, request, *args, **kwargs):
        # When HTMX requests the table, we re-run the get_queryset logic
        self.object_list = self.get_queryset()
        context = self.get_context_data()
        return render(request, self.template_name, context)

# HTMX endpoint for Employee Name autocomplete
class TourIntimationAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('query', '')
        # Mock user context for filtering.
        user_compid = 1 # Replace with request.user.compid
        
        if query:
            # Filter OfficeStaff based on CompId and search prefix
            employees = OfficeStaff.objects.filter(
                comp_id=user_compid, 
                employee_name__icontains=query # Use icontains for broader match
            ).order_by('employee_name')[:10] # Limit results
            
            suggestions = []
            for emp in employees:
                suggestions.append(f"{emp.employee_name} [{emp.emp_id}]")
            
            # Return HTML for HTMX to swap in, e.g., a datalist or ul
            html_suggestions = ''.join([f'<option value="{s}"></option>' for s in suggestions])
            return HttpResponse(f'<datalist id="emp-suggestions">{html_suggestions}</datalist>')
        
        return HttpResponse('<datalist id="emp-suggestions"></datalist>') # Return empty if no query


# View for handling Tour Intimation editing (for TourIntimation_Edit_Details.aspx)
class TourIntimationUpdateView(UpdateView):
    model = TourIntimation
    form_class = TourIntimationForm
    template_name = 'hr_tour/tourintimation/_tourintimation_form.html' # Use a partial template for modal
    
    # success_url is handled by HTMX response in form_valid
    
    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        # Ensure object belongs to the current user's company and financial year
        # Mock user context.
        user = MockUser(compid=1, finyear=2024, username='testuser')
        return get_object_or_404(TourIntimation, pk=pk, comp_id=user.compid, fin_year_id__lte=user.finyear)


    def form_valid(self, form):
        # Assign session/user specific fields before saving if they are not part of the form
        # Mock user context.
        user = MockUser(compid=1, finyear=2024, username='testuser')
        form.instance.comp_id = user.compid
        form.instance.fin_year_id = user.finyear
        form.instance.session_id = user.username # Map SessionId to Django user.username

        response = super().form_valid(form)
        messages.success(self.request, 'Tour Intimation updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList' # Trigger the table to reload
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the rendered form with errors
            return render(self.request, self.template_name, {'form': form})
        return response

# Placeholder for Create View (if needed in future)
class TourIntimationCreateView(TourIntimationUpdateView): # Inherit for common logic
    template_name = 'hr_tour/tourintimation/_tourintimation_form.html'
    
    def form_valid(self, form):
        # Assign session/user specific fields for new record
        user = MockUser(compid=1, finyear=2024, username='testuser')
        form.instance.comp_id = user.compid
        form.instance.fin_year_id = user.finyear
        form.instance.session_id = user.username
        
        response = super(UpdateView, self).form_valid(form) # Call parent of TourIntimationUpdateView
        messages.success(self.request, 'Tour Intimation added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return response

# Placeholder for Delete View
class TourIntimationDeleteView(View):
    # This will be a simple HTMX endpoint to confirm deletion
    def get(self, request, pk):
        tour_intimation = get_object_or_404(TourIntimation, pk=pk)
        return render(request, 'hr_tour/tourintimation/_tourintimation_confirm_delete.html', {'object': tour_intimation})

    def post(self, request, pk):
        tour_intimation = get_object_or_404(TourIntimation, pk=pk)
        tour_intimation.delete()
        messages.success(request, 'Tour Intimation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return HttpResponseRedirect(reverse_lazy('tourintimation_list')) # Fallback for non-HTMX
```

#### 4.4 Templates
**Task:** Create templates for the list, search form, table partial, and edit/delete forms.

**Instructions:**
Templates will extend `core/base.html` for layout. HTMX is used for dynamic content loading (search form, table, modals). Alpine.js controls modal visibility. DataTables is initialized on the table partial. Tailwind CSS classes are used for styling.

**File: `hr_tour/tourintimation/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Tour Intimation Edit</h2>

    <!-- Search Form Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div id="tourintimation-search-form-container"
             hx-get="{% url 'tourintimation_search_form' %}"
             hx-trigger="load, refreshSearchForm from:body"
             hx-swap="innerHTML">
            <!-- Search form will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading search form...</p>
            </div>
        </div>
    </div>

    <!-- Tour Intimation List Table -->
    <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-gray-700">Tour Intimations</h3>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'tourintimation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Tour Intimation
        </button>
    </div>
    
    <div id="tourintimationTable-container"
         hx-trigger="load, refreshTourIntimationList from:body"
         hx-get="{% url 'tourintimation_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Tour Intimations...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterSwap from #modalContent if !event.detail.target.closest('.modal-content') remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 md:mx-auto relative">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is primarily for UI state, like modal toggle
        // HTMX handles most of the dynamic content loading.
    });
</script>
{% endblock %}
```

**File: `hr_tour/tourintimation/_search_form.html`**
```html
<form hx-get="{% url 'tourintimation_table' %}" hx-target="#tourintimationTable-container" hx-swap="innerHTML" hx-trigger="submit">
    <div class="flex flex-wrap items-end gap-4">
        <div class="flex flex-col">
            <label for="id_drp_field" class="block text-sm font-medium text-gray-700 mb-1">Search By:</label>
            {{ search_form.drp_field }}
        </div>

        <div class="flex flex-col flex-grow min-w-[200px]">
            {% if search_form.drp_field.value == '0' or search_form.drp_field.value == '2' or search_form.drp_field.value == '4' or search_form.drp_field.value == 'Select' %}
                <label for="id_txt_mrs" class="block text-sm font-medium text-gray-700 mb-1">
                    {% if search_form.drp_field.value == '0' %}TI No{% elif search_form.drp_field.value == '2' %}WO No{% elif search_form.drp_field.value == '4' %}Project Name{% else %}Enter Search Value{% endif %}:
                </label>
                {{ search_form.txt_mrs }}
            {% else %}
                <input type="text" class="hidden" name="txt_mrs" value="">
            {% endif %}

            {% if search_form.drp_field.value == '1' %}
                <label for="id_txt_emp_name" class="block text-sm font-medium text-gray-700 mb-1">Employee Name:</label>
                {{ search_form.txt_emp_name }}
                <input type="hidden" name="emp_id_code" id="id_emp_id_code" value="{{ search_form.emp_id_code.value }}"> {# Hidden field for EmpId #}
                <datalist id="emp-suggestions"></datalist> {# Autocomplete suggestions go here #}
            {% else %}
                <input type="text" class="hidden" name="txt_emp_name" value="">
                <input type="hidden" name="emp_id_code" value="">
            {% endif %}

            {% if search_form.drp_field.value == '3' %}
                <label for="id_drp_group" class="block text-sm font-medium text-gray-700 mb-1">BG Group:</label>
                {{ search_form.drp_group }}
            {% else %}
                <select class="hidden" name="drp_group"></select>
            {% endif %}
        </div>

        <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
            Search
        </button>
    </div>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // HTMX re-initializes scripts in swapped content.
        // This script ensures correct visibility based on the selected dropdown
        // and handles the update of the hidden emp_id_code field when an autocomplete suggestion is selected.
        
        const drpField = document.getElementById('id_drp_field');
        const txtEmpName = document.getElementById('id_txt_emp_name');
        const empIdCodeHidden = document.getElementById('id_emp_id_code');
        const empSuggestions = document.getElementById('emp-suggestions');

        function updateFieldVisibility() {
            const selectedValue = drpField.value;
            // The template itself handles visibility based on Django's `search_form.drp_field.value`
            // and HTMX re-swaps the content. No additional JS is strictly needed for initial visibility.
            // However, this function is useful if we wanted to dynamically toggle without a full hx-swap.
        }

        drpField.addEventListener('change', function() {
            // Re-submit the form via HTMX to update the visible fields
            // This re-renders the search form partial, showing/hiding fields based on the new selection
            this.closest('form').dispatchEvent(new Event('submit', { bubbles: true }));
        });

        // Event listener for when an option is selected from the datalist
        if (txtEmpName) {
            txtEmpName.addEventListener('input', function() {
                const selectedValue = this.value;
                const option = Array.from(empSuggestions.options).find(opt => opt.value === selectedValue);
                if (option) {
                    const empIdMatch = selectedValue.match(/\[(.*?)\]$/);
                    if (empIdMatch) {
                        empIdCodeHidden.value = empIdMatch[1];
                    }
                } else {
                    empIdCodeHidden.value = ''; // Clear if text doesn't match an option
                }
            });
        }

        updateFieldVisibility(); // Initial call
    });
</script>
```

**File: `hr_tour/tourintimation/_tourintimation_table.html`**
```html
<div class="overflow-x-auto">
    <table id="tourintimationTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TI No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place of Tour</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour Start Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour End Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in tour_intimations %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter0|add:page_obj.start_index }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.fin_year_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <button class="text-blue-600 hover:text-blue-800 font-semibold"
                        hx-get="{% url 'tourintimation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        {{ obj.ti_no }}
                    </button>
                </td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.emp_name_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.wo_no_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.bg_group_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.project_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.place_of_tour_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.tour_start_date_dmy }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.tour_end_date_dmy }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-sm mr-2 shadow-sm"
                        hx-get="{% url 'tourintimation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-sm shadow-sm"
                        hx-get="{% url 'tourintimation_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" class="py-4 text-center text-lg text-gray-500">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#tourintimationTable').DataTable({
            "pageLength": 10, // DataTables default, will override if pagination is server-side
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "paging": {{ page_obj.paginator.num_pages > 1|yesno:"true,false" }}, // Enable/disable DataTable's paging based on Django pagination
            "info": {{ page_obj.paginator.num_pages > 1|yesno:"true,false" }},
            "searching": true, // Client-side searching (Django handles server-side filtering)
            "order": [], // Disable initial ordering if Django orders
            "dom": '<"flex justify-between items-center mb-4"lfB>rt<"flex justify-between items-center mt-4"ip>',
            "buttons": ['copy', 'csv', 'excel', 'pdf', 'print']
        });
    });
</script>
```

**File: `hr_tour/tourintimation/_tourintimation_form.html`**
```html
<div class="modal-content p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Tour Intimation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `hr_tour/tourintimation/_tourintimation_confirm_delete.html`**
```html
<div class="modal-content p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete Tour Intimation "{{ object.ti_no }}"?</p>
    
    <form hx-post="{% url 'tourintimation_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs
**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main list page, the table partial (for HTMX), the search form partial, autocomplete, and CRUD operations.

**File: `hr_tour/urls.py`**
```python
from django.urls import path
from .views import (
    TourIntimationListView, 
    TourIntimationTablePartialView, 
    TourIntimationAutocompleteView,
    TourIntimationUpdateView,
    TourIntimationCreateView,
    TourIntimationDeleteView,
)
from .forms import TourIntimationSearchForm # Needed to render the form for the partial

urlpatterns = [
    # Main list page
    path('tourintimation/', TourIntimationListView.as_view(), name='tourintimation_list'),
    
    # HTMX endpoints for partials
    path('tourintimation/table/', TourIntimationTablePartialView.as_view(), name='tourintimation_table'),
    path('tourintimation/search-form/', lambda request: render(request, 'hr_tour/tourintimation/_search_form.html', {'search_form': TourIntimationSearchForm(request.GET)}), name='tourintimation_search_form'),
    path('autocomplete/employees/', TourIntimationAutocompleteView.as_view(), name='employee_autocomplete'),

    # CRUD operations
    path('tourintimation/add/', TourIntimationCreateView.as_view(), name='tourintimation_add'),
    path('tourintimation/edit/<int:pk>/', TourIntimationUpdateView.as_view(), name='tourintimation_edit'),
    path('tourintimation/delete/<int:pk>/', TourIntimationDeleteView.as_view(), name='tourintimation_delete'),
]

```
**Note**: You need to include `hr_tour.urls` in your project's `urls.py`:
`path('hr/', include('hr_tour.urls')),`

#### 4.6 Tests
**Task:** Write tests for the models and views.

**Instructions:**
Comprehensive unit tests are provided for model methods and properties. Integration tests cover all view actions (list, create, update, delete) and HTMX interactions. Mock user context is used to simulate session data.

**File: `hr_tour/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch

from .models import TourIntimation, FinancialYear, OfficeStaff, BusinessGroup, City, State, Country, TourVoucher
from .forms import TourIntimationSearchForm

# Mock User object for testing views that rely on user session data
class MockUser:
    def __init__(self, compid, finyear, username):
        self.compid = compid
        self.finyear = finyear
        self.username = username

# Apply this mock to the views that use it
@patch('hr_tour.views.MockUser', side_effect=lambda *args, **kwargs: MockUser(compid=1, finyear=2024, username='testuser'))
class TourIntimationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for foreign keys
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.employee = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr.', comp_id=1, fin_year_id=2024)
        cls.business_group_na = BusinessGroup.objects.create(id=1, symbol='NA_GROUP') # Assuming 1 is the 'NA' group
        cls.business_group_other = BusinessGroup.objects.create(id=2, symbol='SALES_GROUP')
        cls.country = Country.objects.create(cid=1, country_name='India')
        cls.state = State.objects.create(sid=1, state_name='Maharashtra')
        cls.city = City.objects.create(city_id=1, city_name='Mumbai')

        # Create test TourIntimation instance
        cls.tour_intimation_data = {
            'id': 1,
            'ti_no': 'TI001',
            'emp_id_code': 'EMP001',
            'fin_year_id': 2024,
            'wo_no': 'WO001',
            'bg_group_id': 2, # Other group
            'project_name': 'Project X',
            'place_of_tour_city_id': 1,
            'place_of_tour_state_id': 1,
            'place_of_tour_country_id': 1,
            'tour_start_date': '2024-03-01',
            'tour_end_date': '2024-03-05',
            'session_id': 'testuser',
            'comp_id': 1,
        }
        cls.obj = TourIntimation.objects.create(**cls.tour_intimation_data)

        # Create a tour intimation that should be excluded by voucher check
        cls.vouchered_ti_data = {
            'id': 2,
            'ti_no': 'TI002',
            'emp_id_code': 'EMP001',
            'fin_year_id': 2024,
            'wo_no': 'WO002',
            'bg_group_id': 2,
            'project_name': 'Project Y',
            'place_of_tour_city_id': 1,
            'place_of_tour_state_id': 1,
            'place_of_tour_country_id': 1,
            'tour_start_date': '2024-04-01',
            'tour_end_date': '2024-04-05',
            'session_id': 'testuser',
            'comp_id': 1,
        }
        cls.vouchered_obj = TourIntimation.objects.create(**cls.vouchered_ti_data)
        TourVoucher.objects.create(tim_id=2, comp_id=1, fin_year_id=2024)

        # Create an intimation with BGGroupId 1 (WO No applicable)
        cls.wo_ti_data = {
            'id': 3,
            'ti_no': 'TI003',
            'emp_id_code': 'EMP001',
            'fin_year_id': 2024,
            'wo_no': 'WO003',
            'bg_group_id': 1, # NA group, so WO no is relevant
            'project_name': 'Project Z',
            'place_of_tour_city_id': 1,
            'place_of_tour_state_id': 1,
            'place_of_tour_country_id': 1,
            'tour_start_date': '2024-05-01',
            'tour_end_date': '2024-05-05',
            'session_id': 'testuser',
            'comp_id': 1,
        }
        cls.wo_obj = TourIntimation.objects.create(**cls.wo_ti_data)

    def test_tour_intimation_creation(self, MockUser):
        obj = TourIntimation.objects.get(id=1)
        self.assertEqual(obj.ti_no, 'TI001')
        self.assertEqual(obj.employee.employee_name, 'John Doe')
        self.assertEqual(obj.tour_city.city_name, 'Mumbai')

    def test_fin_year_display(self, MockUser):
        self.assertEqual(self.obj.fin_year_display, '2024-2025')

    def test_emp_name_display(self, MockUser):
        self.assertEqual(self.obj.emp_name_display, 'John Doe')

    def test_bg_group_display(self, MockUser):
        self.assertEqual(self.obj.bg_group_display, 'SALES_GROUP')
        self.assertEqual(self.wo_obj.bg_group_display, 'NA') # For BGGroupId 1

    def test_wo_no_display(self, MockUser):
        self.assertEqual(self.obj.wo_no_display, 'NA') # For BGGroupId != 1
        self.assertEqual(self.wo_obj.wo_no_display, 'WO003') # For BGGroupId 1

    def test_place_of_tour_display(self, MockUser):
        self.assertEqual(self.obj.place_of_tour_display, 'India, Maharashtra, Mumbai')

    def test_tour_start_date_dmy(self, MockUser):
        self.assertEqual(self.obj.tour_start_date_dmy, '01/03/2024')

    def test_tour_end_date_dmy(self, MockUser):
        self.assertEqual(self.obj.tour_end_date_dmy, '05/03/2024')

    def test_custom_manager_queryset_filtering(self, MockUser):
        # Initial queryset should exclude vouchered item
        qs = TourIntimation.objects.filter_by_user_context(MockUser(compid=1, finyear=2024, username='testuser'), None, None)
        self.assertEqual(qs.count(), 2) # TI001 and TI003 should be present, TI002 excluded
        self.assertIn(self.obj, qs)
        self.assertIn(self.wo_obj, qs)
        self.assertNotIn(self.vouchered_obj, qs)

    def test_custom_manager_search_ti_no(self, MockUser):
        qs = TourIntimation.objects.filter_by_user_context(MockUser(compid=1, finyear=2024, username='testuser'), '0', 'TI001')
        self.assertEqual(qs.count(), 1)
        self.assertIn(self.obj, qs)

    def test_custom_manager_search_employee_name(self, MockUser):
        qs = TourIntimation.objects.filter_by_user_context(MockUser(compid=1, finyear=2024, username='testuser'), '1', 'EMP001')
        self.assertEqual(qs.count(), 2) # Should find TI001 and TI003 (both by EMP001)

    def test_custom_manager_search_project_name(self, MockUser):
        qs = TourIntimation.objects.filter_by_user_context(MockUser(compid=1, finyear=2024, username='testuser'), '4', 'Project X')
        self.assertEqual(qs.count(), 1)
        self.assertIn(self.obj, qs)

@patch('hr_tour.views.MockUser', side_effect=lambda *args, **kwargs: MockUser(compid=1, finyear=2024, username='testuser'))
class TourIntimationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for foreign keys
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.employee = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr.', comp_id=1, fin_year_id=2024)
        cls.business_group_na = BusinessGroup.objects.create(id=1, symbol='NA_GROUP')
        cls.business_group_other = BusinessGroup.objects.create(id=2, symbol='SALES_GROUP')
        cls.country = Country.objects.create(cid=1, country_name='India')
        cls.state = State.objects.create(sid=1, state_name='Maharashtra')
        cls.city = City.objects.create(city_id=1, city_name='Mumbai')
        
        # Create a test TourIntimation instance for update/delete
        cls.tour_intimation_data = {
            'id': 100, # Use a higher ID to avoid clashes with model tests if run together
            'ti_no': 'TI_VIEW_TEST_001',
            'emp_id_code': 'EMP001',
            'fin_year_id': 2024,
            'wo_no': 'WO_VIEW_TEST_001',
            'bg_group_id': 2,
            'project_name': 'Project View Test',
            'place_of_tour_city_id': 1,
            'place_of_tour_state_id': 1,
            'place_of_tour_country_id': 1,
            'tour_start_date': '2024-01-01',
            'tour_end_date': '2024-01-05',
            'session_id': 'testuser',
            'comp_id': 1,
        }
        cls.obj = TourIntimation.objects.create(**cls.tour_intimation_data)

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self, MockUser):
        response = self.client.get(reverse('tourintimation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/list.html')
        self.assertIn('tour_intimations', response.context)
        self.assertIn('search_form', response.context)

    def test_table_partial_view_get(self, MockUser):
        response = self.client.get(reverse('tourintimation_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_table.html')
        self.assertIn('tour_intimations', response.context)
        self.assertContains(response, self.obj.ti_no) # Check if object is in table

    def test_table_partial_view_search(self, MockUser):
        # Test searching by TI No
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '0', 'txt_mrs': 'TI_VIEW_TEST_001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_table.html')
        self.assertEqual(response.context['tour_intimations'].count(), 1)
        self.assertContains(response, 'TI_VIEW_TEST_001')

        # Test searching by Employee Name (using EmpId)
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '1', 'txt_emp_name': 'John Doe [EMP001]', 'emp_id_code': 'EMP001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_table.html')
        self.assertEqual(response.context['tour_intimations'].count(), 1) # Only TI_VIEW_TEST_001 by this employee
        self.assertContains(response, 'John Doe')

    def test_search_form_partial_view(self, MockUser):
        response = self.client.get(reverse('tourintimation_search_form'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_search_form.html')
        self.assertIn('search_form', response.context)

    def test_employee_autocomplete_view(self, MockUser):
        response = self.client.get(reverse('employee_autocomplete'), {'query': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('John Doe [EMP001]', response.content.decode())
        self.assertContains(response, '<datalist id="emp-suggestions">')

    def test_add_view_get(self, MockUser):
        response = self.client.get(reverse('tourintimation_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_form.html')
        self.assertIn('form', response.context)

    def test_add_view_post_success(self, MockUser):
        new_id = TourIntimation.objects.latest('id').id + 1 if TourIntimation.objects.exists() else 1
        data = {
            'id': new_id,
            'ti_no': 'TI_NEW_001',
            'emp_id_code': 'EMP001',
            'fin_year_id': 2024,
            'wo_no': 'WO_NEW_001',
            'bg_group_id': 1,
            'project_name': 'New Project',
            'place_of_tour_city_id': 1,
            'place_of_tour_state_id': 1,
            'place_of_tour_country_id': 1,
            'tour_start_date': '2024-06-01',
            'tour_end_date': '2024-06-05',
        }
        response = self.client.post(reverse('tourintimation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTourIntimationList')
        self.assertTrue(TourIntimation.objects.filter(ti_no='TI_NEW_001').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Tour Intimation added successfully.')

    def test_add_view_post_invalid(self, MockUser):
        data = { # Missing required fields
            'id': TourIntimation.objects.latest('id').id + 1,
            'ti_no': '',
            'emp_id_code': '',
            'tour_start_date': 'invalid-date',
        }
        response = self.client.post(reverse('tourintimation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)

    def test_edit_view_get(self, MockUser):
        response = self.client.get(reverse('tourintimation_edit', args=[self.obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.obj)

    def test_edit_view_post_success(self, MockUser):
        updated_ti_no = 'TI_UPDATED_001'
        data = {
            'id': self.obj.id,
            'ti_no': updated_ti_no,
            'emp_id_code': self.obj.emp_id_code,
            'fin_year_id': self.obj.fin_year_id,
            'wo_no': self.obj.wo_no,
            'bg_group_id': self.obj.bg_group_id,
            'project_name': self.obj.project_name,
            'place_of_tour_city_id': self.obj.place_of_tour_city_id,
            'place_of_tour_state_id': self.obj.place_of_tour_state_id,
            'place_of_tour_country_id': self.obj.place_of_tour_country_id,
            'tour_start_date': self.obj.tour_start_date,
            'tour_end_date': self.obj.tour_end_date,
        }
        response = self.client.post(reverse('tourintimation_edit', args=[self.obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTourIntimationList')
        self.obj.refresh_from_db()
        self.assertEqual(self.obj.ti_no, updated_ti_no)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Tour Intimation updated successfully.')

    def test_edit_view_post_invalid(self, MockUser):
        data = {
            'id': self.obj.id,
            'ti_no': '', # Invalid data
            'emp_id_code': '',
            'fin_year_id': self.obj.fin_year_id,
            'wo_no': self.obj.wo_no,
            'bg_group_id': self.obj.bg_group_id,
            'project_name': self.obj.project_name,
            'place_of_tour_city_id': self.obj.place_of_tour_city_id,
            'place_of_tour_state_id': self.obj.place_of_tour_state_id,
            'place_of_tour_country_id': self.obj.place_of_tour_country_id,
            'tour_start_date': 'invalid-date', # Invalid date format
            'tour_end_date': self.obj.tour_end_date,
        }
        response = self.client.post(reverse('tourintimation_edit', args=[self.obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)

    def test_delete_view_get(self, MockUser):
        response = self.client.get(reverse('tourintimation_delete', args=[self.obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_tour/tourintimation/_tourintimation_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.obj)

    def test_delete_view_post_success(self, MockUser):
        response = self.client.post(reverse('tourintimation_delete', args=[self.obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTourIntimationList')
        self.assertFalse(TourIntimation.objects.filter(pk=self.obj.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Tour Intimation deleted successfully.')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already incorporate HTMX for:
- Loading the initial search form and updating it on `drp_field` change.
- Loading and refreshing the DataTables content via `hx-get` on `load` and custom `HX-Trigger`.
- Loading the `Add` and `Edit` forms into an Alpine.js controlled modal using `hx-get` on button click.
- Submitting `Add` and `Edit` forms using `hx-post` with `hx-swap="none"` and `HX-Trigger` for list refresh.
- Loading the `Delete` confirmation into the modal and submitting `hx-post` for deletion.
- Autocomplete for `Employee Name` using `hx-get` to a dedicated endpoint, updating a datalist.

Alpine.js is used minimally to manage the modal's visibility (`x-show` equivalent logic using `hidden` class and `on click` events for `_` attribute). DataTables is initialized via jQuery in the `_tourintimation_table.html` partial, running each time the table is swapped in.

## Final Notes

- **AI-assisted Automation:** This plan is designed with automation in mind. The `Step 1` to `Step 3` analysis phase would be automated using AI tools to extract schema, functionality, and UI components. `Step 4` (code generation) would then be performed by AI based on the extracted information and the provided templates and guidelines. The `clsFunctions` usage in ASP.NET indicates a legacy helper class. In Django, this logic was re-implemented using Django ORM and model properties/managers for better maintainability and performance (e.g., `select_related`, `prefetch_related`, subqueries).
- **User Context (MockUser):** The `MockUser` class in the views and tests is a placeholder. In a real application, you would integrate Django's authentication system and retrieve `comp_id`, `fin_year_id`, and `session_id` (likely `request.user.username` or `request.user.id`) from the authenticated user's profile or session.
- **Error Handling:** The `try-catch` blocks in ASP.NET are often general. In Django, specific exceptions should be caught and handled gracefully, providing user-friendly error messages where appropriate (e.g., via `messages` framework).
- **Date Formatting:** The `fun.FromDateDMY()` is replaced by Django's native date formatting in templates (`{{ obj.date_field|date:"d/m/Y" }}`).
- **DataTables Pagination:** The provided Django `ListView` handles server-side pagination. The DataTables JavaScript should be configured to disable its own pagination if Django is handling it, or to use AJAX mode to let DataTables manage pagination requests to Django. For simplicity, the current setup assumes DataTables for searching/sorting client-side on the loaded data.
- **Scalability:** The `TourIntimationManager` addresses the N+1 query problem by using `select_related` and `prefetch_related`, which significantly improves performance compared to the original ASP.NET implementation.
- **Code Reuse (DRY):** Emphasis on partial templates (`_search_form.html`, `_tourintimation_table.html`, `_tourintimation_form.html`) and model properties (`fin_year_display`, `place_of_tour_display`) reduces redundancy.