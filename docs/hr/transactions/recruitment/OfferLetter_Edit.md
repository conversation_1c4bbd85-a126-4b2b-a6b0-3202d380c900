## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for Offer Letter Management

This document outlines a detailed strategy to modernize the ASP.NET Offer Letter Edit/Increment module into a robust Django application. Our approach prioritizes automation, leverages modern Django patterns, and integrates cutting-edge frontend technologies like HTMX and Alpine.js to deliver a highly interactive and performant user experience without relying on traditional JavaScript frameworks.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (HR Offer Letter).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code primarily interacts with `tblHR_Offer_Master` as the main table. It also performs lookups against several auxiliary tables: `tblHR_EmpType`, `tblHR_Designation`, `tblHR_DutyHour`, and `tblHR_OfficeStaff`. The `binddata` method in the C# code-behind reveals the actual columns used and how they are transformed or joined.

**Identified Tables and Columns:**

*   **Main Table:** `tblHR_Offer_Master`
    *   `OfferId` (Primary Key, integer) - Used as `Id` in GridView.
    *   `SysDate` (DateTime) - Used as `Date`.
    *   `TypeOf` (integer) - Mapped to 'SAPL' or 'NEHA' in UI.
    *   `StaffType` (integer) - Foreign key/ID to `tblHR_EmpType`.
    *   `Title` (string) - Part of the concatenated `Employee Name`.
    *   `EmployeeName` (string) - Part of the concatenated `Employee Name`, also used for search/autocomplete.
    *   `Designation` (integer) - Foreign key/ID to `tblHR_Designation`.
    *   `DutyHrs` (integer) - Foreign key/ID to `tblHR_DutyHour`.
    *   `InterviewedBy` (integer) - Foreign key/ID to `tblHR_OfficeStaff` (using `EmpId`).
    *   `ContactNo` (string) - Used as `Contact No`.
    *   `salary` (decimal/money) - Used as `Gross Salary`.
    *   `CompId` (integer) - Company ID for filtering.
    *   `FinYearId` (string) - Financial Year ID for filtering.

*   **Auxiliary/Lookup Tables (inferred from joins/lookups in `binddata`):**
    *   `tblHR_EmpType`: `Id` (PK), `Description`
    *   `tblHR_Designation`: `Id` (PK), `Type` (represents designation description)
    *   `tblHR_DutyHour`: `Id` (PK), `Hours`
    *   `tblHR_OfficeStaff`: `EmpId` (PK), `EmployeeName`, `OfferId` (used to determine 'Status')

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**

*   **Read (R):**
    *   The `binddata` method dynamically constructs a `DataTable` by querying `tblHR_Offer_Master` and performing several lookups/joins with `tblHR_EmpType`, `tblHR_Designation`, `tblHR_DutyHour`, and `tblHR_OfficeStaff`.
    *   Filtering is applied based on `CompId`, `FinYearId`, and optionally `EmployeeName` (from `TextBox1`).
    *   Pagination is handled by `GridView2_PageIndexChanging`.
    *   Autocomplete for `EmployeeName` is provided by the `GetCompletionList` WebMethod.
*   **Create (C):**
    *   The `SqlDataSource1` has an `InsertCommand`, but no explicit UI element (like a "New" button or an insert row in the `GridView`) is visible in the provided ASPX. We will provide a standard Django CreateView for completeness.
*   **Update (U):**
    *   The `GridView2` has an "Edit" `LinkButton` which triggers `CommandName="Select"`.
    *   `GridView2_RowCommand` handles "Select" by redirecting to `OfferLetter_Edit_Details.aspx` with `offid` (OfferId). This implies editing happens on a separate details page. We will implement an `UpdateView` that loads a form into a modal for a seamless HTMX experience.
    *   An "Increment" `LinkButton` is also present, redirecting similarly. We will provide a placeholder for this action as it implies a specific business logic not fully detailed in the current scope, and keep the focus on CRUD.
    *   `SqlDataSource1` has an `UpdateCommand`.
*   **Delete (D):**
    *   `SqlDataSource1` has a `DeleteCommand`.
    *   `GridView2_RowDataBound` adds an `onclick` confirmation, implying a delete action is possible, though a direct delete button for each row isn't explicitly visible in the provided `ItemTemplate` for column 0. We will implement a `DeleteView` and a corresponding delete button.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Search/Filter:**
    *   `TextBox1` (`asp:TextBox`): Employee Name input for searching.
    *   `TxtEmpName_AutoCompleteExtender` (`cc1:AutoCompleteExtender`): Provides autocomplete functionality for `TextBox1`.
    *   `Button1` (`asp:Button`): Triggers the search.
*   **Data Display:**
    *   `GridView2` (`asp:GridView`): Displays the list of offer letters. Features include:
        *   Pagination (`AllowPaging`, `PageSize`).
        *   Custom columns (`TemplateField` for derived values and action links).
        *   "Edit" `LinkButton`: Navigates to a detail/edit page.
        *   "Increment" `LinkButton`: Navigates to a detail/increment page.
*   **Styling:** Custom CSS files are used for `yui-datatable-theme` and general styles. These will be replaced by Tailwind CSS and DataTables.
*   **JavaScript:** `PopUpMsg.js` (likely for confirmation dialogs) and `loadingNotifier.js` (for visual feedback during AJAX calls). These functionalities will be natively handled by HTMX for dynamic content loading and Alpine.js for UI state.

### Step 4: Generate Django Code

We will create a new Django app named `hr` for the HR-related functionalities.

#### 4.1 Models (`hr/models.py`)

We'll define models for `OfferMaster` and the auxiliary tables (`EmployeeType`, `Designation`, `DutyHour`, `OfficeStaff`), all set to `managed=False` to map to the existing database schema. The `OfferMaster` model will be "fat," incorporating methods and properties to handle the complex data lookups and derivations previously done in the C# `binddata` method.

```python
from django.db import models
from django.utils import timezone

# Dummy models for lookups, assuming they exist in the existing database
# These models are set to managed=False and map to the specific table names.
class EmployeeType(models.Model):
    id = models.SmallIntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False  # Do not manage this table's schema through Django migrations
        db_table = 'tblHR_EmpType'
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'

    def __str__(self):
        return self.description

class Designation(models.Model):
    id = models.SmallIntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255) # 'Type' is the column name for designation description

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.type

class DutyHour(models.Model):
    id = models.SmallIntegerField(db_column='Id', primary_key=True)
    hours = models.CharField(db_column='Hours', max_length=50) # Assuming Hours can be a string like "8 hrs"

    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'Duty Hour'
        verbose_name_plural = 'Duty Hours'

    def __str__(self):
        return self.hours

class OfficeStaff(models.Model):
    # This model is used to check for 'Confirmed' status of an offer letter
    emp_id = models.SmallIntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    # The 'OfferId' here is for checking if a staff member was created from an offer.
    # It acts as a reverse lookup for the offer status.
    offer_id = models.IntegerField(db_column='OfferId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class OfferMaster(models.Model):
    # Choices for the 'TypeOf' field, mapping integer IDs to descriptive strings
    TYPE_OF_CHOICES = [
        (1, 'SAPL'),
        (2, 'NEHA'),
    ]

    offer_id = models.IntegerField(db_column='OfferId', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate')
    type_of = models.SmallIntegerField(db_column='TypeOf', choices=TYPE_OF_CHOICES)
    staff_type_id = models.SmallIntegerField(db_column='StaffType') # Stores ID, description looked up dynamically
    title = models.CharField(db_column='Title', max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    designation_id = models.SmallIntegerField(db_column='Designation') # Stores ID
    duty_hrs_id = models.SmallIntegerField(db_column='DutyHrs') # Stores ID
    interviewed_by_id = models.SmallIntegerField(db_column='InterviewedBy') # Stores ID
    contact_no = models.CharField(db_column='ContactNo', max_length=50, null=True, blank=True)
    salary = models.DecimalField(db_column='salary', max_digits=10, decimal_places=2)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.CharField(db_column='FinYearId', max_length=10)

    class Meta:
        managed = False  # Critical: Tells Django not to manage this table's schema
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Letter'
        verbose_name_plural = 'Offer Letters'

    def __str__(self):
        return f"Offer ID: {self.offer_id} - {self.employee_name}"

    # --- Fat Model: Business logic and data presentation properties ---

    @property
    def formatted_date(self):
        """Returns the SysDate formatted as DD/MM/YYYY."""
        return self.sys_date.strftime("%d/%m/%Y") if self.sys_date else ''

    @property
    def type_of_display(self):
        """Returns the descriptive string for 'TypeOf'."""
        return dict(self.TYPE_OF_CHOICES).get(self.type_of, 'Unknown')

    @property
    def employee_type_description(self):
        """Looks up and returns the description of the employee type."""
        try:
            return EmployeeType.objects.get(id=self.staff_type_id).description
        except EmployeeType.DoesNotExist:
            return "N/A"

    @property
    def full_employee_name(self):
        """Combines title and employee name for display."""
        return f"{self.title} {self.employee_name}".strip()

    @property
    def designation_description(self):
        """Looks up and returns the description of the designation."""
        try:
            return Designation.objects.get(id=self.designation_id).type
        except Designation.DoesNotExist:
            return "N/A"

    @property
    def duty_hours_description(self):
        """Looks up and returns the duty hours description."""
        try:
            return DutyHour.objects.get(id=self.duty_hrs_id).hours
        except DutyHour.DoesNotExist:
            return "N/A"

    @property
    def interviewed_by_name(self):
        """Looks up and returns the name of the interviewer."""
        try:
            return OfficeStaff.objects.get(emp_id=self.interviewed_by_id).employee_name
        except OfficeStaff.DoesNotExist:
            return "N/A"

    @property
    def gross_salary_display(self):
        """Formats the gross salary for display (e.g., with commas)."""
        return f"{self.salary:,.2f}" if self.salary is not None else "0.00"

    @property
    def status(self):
        """Determines if the offer is 'Confirmed' based on OfficeStaff entry."""
        return "Confirm" if OfficeStaff.objects.filter(offer_id=self.offer_id).exists() else "Pending"

    @classmethod
    def get_filtered_offers(cls, emp_name_filter=None, comp_id=None, fin_year_id=None):
        """
        Class method to replicate the main data retrieval logic from `binddata`.
        Applies filters for company, financial year, and employee name.
        """
        queryset = cls.objects.all()

        # Apply filtering based on original ASP.NET logic
        # For demonstration, use default values for comp_id and fin_year_id if not provided
        # In a real app, these would come from the current user's session/profile
        actual_comp_id = comp_id if comp_id is not None else 1 # Example default
        actual_fin_year_id = fin_year_id if fin_year_id is not None else '2024' # Example default

        queryset = queryset.filter(comp_id=actual_comp_id)
        # Original code used `FinYearId<='` which implies lexicographical or numeric comparison.
        # Assuming it's numeric or year-based for now.
        queryset = queryset.filter(fin_year_id__lte=actual_fin_year_id)

        if emp_name_filter:
            queryset = queryset.filter(employee_name__icontains=emp_name_filter) # Case-insensitive partial match

        # Order by OfferId Desc as in original
        queryset = queryset.order_by('-offer_id')
        return queryset

```

#### 4.2 Forms (`hr/forms.py`)

A Django ModelForm will be created for `OfferMaster`, using Tailwind CSS classes for styling. We will include all fields necessary for typical CRUD operations.

```python
from django import forms
from .models import OfferMaster, EmployeeType, Designation, DutyHour, OfficeStaff

class OfferMasterForm(forms.ModelForm):
    # Fetch choices dynamically from lookup tables for dropdowns
    staff_type_id = forms.ModelChoiceField(
        queryset=EmployeeType.objects.all(),
        to_field_name='id', # Use 'id' as the value passed to the model field
        label="Employee Type",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    designation_id = forms.ModelChoiceField(
        queryset=Designation.objects.all(),
        to_field_name='id',
        label="Designation",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    duty_hrs_id = forms.ModelChoiceField(
        queryset=DutyHour.objects.all(),
        to_field_name='id',
        label="Duty Hours",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    interviewed_by_id = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.all(),
        to_field_name='emp_id',
        label="Interviewed By",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = OfferMaster
        fields = [
            'sys_date', 'type_of', 'staff_type_id', 'title', 'employee_name',
            'designation_id', 'duty_hrs_id', 'interviewed_by_id', 'contact_no',
            'salary', 'comp_id', 'fin_year_id'
        ]
        widgets = {
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'type_of': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'salary': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'sys_date': 'Date',
            'type_of': 'Type Of',
            'title': 'Title',
            'employee_name': 'Employee Name',
            'contact_no': 'Contact No',
            'salary': 'Gross Salary',
            'comp_id': 'Company ID',
            'fin_year_id': 'Financial Year ID',
        }

    # Custom validation example (if needed, e.g., for specific date ranges)
    def clean_salary(self):
        salary = self.cleaned_data['salary']
        if salary <= 0:
            raise forms.ValidationError("Salary must be a positive value.")
        return salary

```

#### 4.3 Views (`hr/views.py`)

Views will be thin, primarily handling HTTP requests and delegating business logic to the models. HTMX requests will receive `HX-Trigger` headers for dynamic UI updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from .models import OfferMaster, OfficeStaff # Import OfficeStaff for autocomplete
from .forms import OfferMasterForm
from django.db.models import Q # For complex queries in autocomplete

class OfferMasterListView(ListView):
    """
    Renders the main page for Offer Letters.
    This view itself doesn't provide the table data directly, but a container that HTMX loads.
    """
    model = OfferMaster
    template_name = 'hr/offermaster/list.html'
    context_object_name = 'offerletters' # Used for initial context if needed, but table is HTMX loaded

    def get_queryset(self):
        # Initial queryset for the main page (could be empty or a limited set)
        return OfferMaster.objects.none() # DataTables will load data via HTMX endpoint

class OfferMasterTablePartialView(ListView):
    """
    HTMX endpoint to render just the DataTables portion of the Offer Letter list.
    This keeps the main view thin and allows for dynamic refreshing.
    """
    model = OfferMaster
    template_name = 'hr/offermaster/_offermaster_table.html'
    context_object_name = 'offerletters' # Name for the list of objects in the template

    def get_queryset(self):
        """
        Fetches the filtered and ordered data using the OfferMaster model's class method.
        This method replaces the C# binddata logic.
        """
        emp_name_filter = self.request.GET.get('employee_name', '')
        # In a real application, comp_id and fin_year_id would come from user session/profile
        # For now, using example values or placeholder logic.
        comp_id = self.request.session.get('compid', 1) # Example: Get from session, default to 1
        fin_year_id = self.request.session.get('finyear', '2024') # Example: Get from session, default to '2024'

        return OfferMaster.get_filtered_offers(
            emp_name_filter=emp_name_filter,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )

class OfferMasterCreateView(CreateView):
    """
    Handles creation of new Offer Letters. Renders a form in a modal via HTMX.
    """
    model = OfferMaster
    form_class = OfferMasterForm
    template_name = 'hr/offermaster/_offermaster_form.html' # Partial template for modal
    # success_url is not strictly needed for HTMX, as we return 204 with HX-Trigger

    def form_valid(self, form):
        # Before saving, handle auto-generated PK if needed (e.g., if OfferId is auto-incrementing)
        # For simplicity, if PK is auto-incrementing in DB, you might omit it from form
        # or have a specific handling for getting the next ID. Assuming it's DB managed.
        # If OfferId is truly a primary key and needs to be set, ensure your form handles it
        # or set it before saving. Here we assume it's set by DB or form.
        response = super().form_valid(form)
        messages.success(self.request, 'Offer Letter added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOfferLettersList' # Custom event to refresh the list
                }
            )
        return response # Fallback for non-HTMX requests

class OfferMasterUpdateView(UpdateView):
    """
    Handles updating existing Offer Letters. Renders a form in a modal via HTMX.
    """
    model = OfferMaster
    form_class = OfferMasterForm
    template_name = 'hr/offermaster/_offermaster_form.html' # Partial template for modal
    pk_url_kwarg = 'pk' # The URL keyword argument for the primary key is 'pk' by default
    # success_url is not strictly needed for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Offer Letter updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOfferLettersList' # Custom event to refresh the list
                }
            )
        return response

class OfferMasterDeleteView(DeleteView):
    """
    Handles deleting Offer Letters. Renders a confirmation in a modal via HTMX.
    """
    model = OfferMaster
    template_name = 'hr/offermaster/_offermaster_confirm_delete.html' # Partial template for modal
    pk_url_kwarg = 'pk'
    # success_url is not strictly needed for HTMX

    def delete(self, request, *args, **kwargs):
        """Override delete to add HTMX-specific response."""
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Offer Letter deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOfferLettersList' # Custom event to refresh the list
                }
            )
        return response

class EmployeeNameAutocompleteView(View):
    """
    HTMX endpoint to provide autocomplete suggestions for employee names.
    This replaces the ASP.NET AutoCompleteExtender's GetCompletionList.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        suggestions = []
        if prefix_text:
            # Query OfferMaster for distinct employee names matching the prefix
            # Assuming 'employee_name' is the field to search
            # Original code filtered by CompId as well, we should replicate that.
            comp_id = request.session.get('compid', 1) # Example: Get from session, default to 1

            names = OfferMaster.objects.filter(
                Q(employee_name__icontains=prefix_text) & Q(comp_id=comp_id)
            ).values_list('employee_name', flat=True).distinct()

            # Ensure only unique names are returned and sorted
            suggestions = sorted(list(set(names)))
            # The original code resized array to 10 max. We can limit here if needed.
            # suggestions = suggestions[:10] # Uncomment to limit suggestions

        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates

Templates will adhere to DRY principles, using `core/base.html` for common structure and partial templates for HTMX-loaded content. Tailwind CSS will be used for styling, and DataTables for list presentation.

**1. List Template (`hr/offermaster/list.html`)**

This is the main page that hosts the DataTables and the modal for CRUD operations.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 space-y-4 sm:space-y-0">
        <h2 class="text-3xl font-extrabold text-gray-800">Offer Letter Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'offermaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .block to #modal then add .opacity-100 to #modal backdrop-filter transition duration-300 ease-in-out then add .scale-100 to #modalContent transform transition duration-300 ease-in-out">
            Add New Offer Letter
        </button>
    </div>

    {# Search and Autocomplete Input #}
    <div class="mb-6">
        <div x-data="{ searchTerm: '', suggestions: [], showSuggestions: false, selectedIndex: -1 }"
             @click.away="showSuggestions = false">
            <label for="employeeSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Employee Name:</label>
            <div class="relative">
                <input type="text" id="employeeSearch" name="employee_name"
                       x-model="searchTerm"
                       @input.debounce.300ms="
                            if (searchTerm.length > 0) {
                                hx_get('/hr/offermaster/autocomplete/?prefixText=' + searchTerm)
                                    .then(response => response.json())
                                    .then(data => {
                                        suggestions = data;
                                        showSuggestions = true;
                                        selectedIndex = -1;
                                    })
                            } else {
                                suggestions = [];
                                showSuggestions = false;
                            }
                       "
                       @keydown.arrow-down.prevent="if(showSuggestions) selectedIndex = (selectedIndex + 1) % suggestions.length"
                       @keydown.arrow-up.prevent="if(showSuggestions) selectedIndex = (selectedIndex - 1 + suggestions.length) % suggestions.length"
                       @keydown.enter.prevent="if(selectedIndex !== -1) { searchTerm = suggestions[selectedIndex]; showSuggestions = false; $dispatch('search-triggered'); }"
                       class="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                       placeholder="Type employee name to search...">
                <ul x-show="showSuggestions && suggestions.length > 0"
                    class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">
                    <template x-for="(suggestion, index) in suggestions" :key="index">
                        <li @click="searchTerm = suggestion; showSuggestions = false; $dispatch('search-triggered');"
                            :class="{ 'bg-blue-100': index === selectedIndex }"
                            class="px-4 py-2 cursor-pointer hover:bg-blue-50">
                            <span x-text="suggestion"></span>
                        </li>
                    </template>
                </ul>
            </div>
            <button
                class="mt-3 bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                hx-get="{% url 'offermaster_table' %}?employee_name={{ '{' }}{{ '{' }}searchTerm{{ '}' }}{{ '}' }}"
                hx-target="#offermasterTable-container"
                hx-swap="innerHTML"
                hx-indicator="#loadingIndicator"
                hx-trigger="click, search-triggered from:root">
                Search
            </button>
        </div>
    </div>

    {# Container for the DataTable, loaded via HTMX #}
    <div id="offermasterTable-container"
         hx-trigger="load, refreshOfferLettersList from:body" {# Triggers initial load and list refreshes #}
         hx-get="{% url 'offermaster_table' %}"
         hx-swap="innerHTML">
        {# Loading indicator for HTMX #}
        <div id="loadingIndicator" class="htmx-indicator text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Offer Letters...</p>
        </div>
    </div>

    {# Modal for forms (Create, Update, Delete) #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center opacity-0 hidden transition-opacity duration-300 ease-in-out"
         _="on click if event.target.id == 'modal' remove .block from me then remove .opacity-100 from me then remove .scale-100 from #modalContent"
         x-data="{ isOpen: false }"
         x-init="$watch('isOpen', value => { if(value) { $el.classList.remove('hidden'); requestAnimationFrame(() => $el.classList.add('opacity-100')); } else { $el.classList.remove('opacity-100'); $el.addEventListener('transitionend', () => { if (!$el.classList.contains('opacity-100')) $el.classList.add('hidden'); }, {once: true}); } })"
         @refreshOfferLettersList.document="isOpen = false"> {# Close modal on list refresh #}
        <div id="modalContent" class="bg-white p-8 rounded-xl shadow-2xl max-w-3xl w-full transform scale-95 transition-transform duration-300 ease-in-out"
             _="on htmx:afterOnLoad remove .scale-95 from me add .scale-100 to me"
             @click.stop=""> {# Prevent clicks inside modal from closing it #}
            {# Content loaded here via HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() {
                this.isOpen = true;
            },
            close() {
                this.isOpen = false;
            }
        }));
    });
</script>
{% endblock %}

```

**2. Table Partial Template (`hr/offermaster/_offermaster_table.html`)**

This template renders only the table, designed to be loaded by HTMX. It initializes DataTables on the client side.

```html
<div class="bg-white shadow-xl rounded-lg overflow-hidden">
    {% if offerletters %}
    <table id="offermasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Offer Id</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type Of</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Duty Hrs</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Interviewed by</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Contact No</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Gross Salary</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in offerletters %}
            <tr class="hover:bg-gray-50 transition duration-150 ease-in-out">
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.formatted_date }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.offer_id }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.type_of_display }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.employee_type_description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.full_employee_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.designation_description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.duty_hours_description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.interviewed_by_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.contact_no }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.gross_salary_display }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.status }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-4 transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_edit' obj.offer_id %}" {# Use obj.offer_id for PK #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 mr-4 transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_increment' obj.offer_id %}" {# Placeholder for Increment #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Increment
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_delete' obj.offer_id %}" {# Use obj.offer_id for PK #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="text-center text-gray-500 py-8 text-lg">No data to display !</p>
    {% endif %}
</div>

<script>
    // Re-initialize DataTable every time this partial is loaded via HTMX
    // Destroy previous instance if it exists to prevent re-initialization issues
    if ($.fn.DataTable.isDataTable('#offermasterTable')) {
        $('#offermasterTable').DataTable().destroy();
    }
    $(document).ready(function() {
        $('#offermasterTable').DataTable({
            "pageLength": 20, // Matches original PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 12] } // SN and Actions columns not sortable
            ],
            "language": {
                "emptyTable": "No data to display !"
            }
        });
    });
</script>
```

**3. Form Partial Template (`hr/offermaster/_offermaster_form.html`)**

Used for both create and update operations within the modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Offer Letter
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-loading-indicator">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {% for field in form %}
            <div class="field-wrapper">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .block from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                Save Offer Letter
            </button>
        </div>
        {# Loading indicator for form submission #}
        <div id="form-loading-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Saving...</p>
        </div>
    </form>
</div>
```

**4. Delete Confirmation Partial Template (`hr/offermaster/_offermaster_confirm_delete.html`)**

Used for the delete confirmation modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Offer Letter for <strong>"{{ object.full_employee_name }}" (ID: {{ object.offer_id }})</strong>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'offermaster_delete' object.offer_id %}" hx-swap="none" hx-indicator="#delete-loading-indicator">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .block from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
        {# Loading indicator for delete #}
        <div id="delete-loading-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Deleting...</p>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`hr/urls.py`)

URL patterns for all views, including HTMX-specific endpoints.

```python
from django.urls import path
from .views import (
    OfferMasterListView,
    OfferMasterTablePartialView,
    OfferMasterCreateView,
    OfferMasterUpdateView,
    OfferMasterDeleteView,
    EmployeeNameAutocompleteView
)

urlpatterns = [
    # Main list page for offer letters
    path('offermaster/', OfferMasterListView.as_view(), name='offermaster_list'),

    # HTMX endpoint for the DataTables content
    path('offermaster/table/', OfferMasterTablePartialView.as_view(), name='offermaster_table'),

    # HTMX endpoint for adding a new offer letter (form in modal)
    path('offermaster/add/', OfferMasterCreateView.as_view(), name='offermaster_add'),

    # HTMX endpoint for editing an existing offer letter (form in modal)
    path('offermaster/edit/<int:pk>/', OfferMasterUpdateView.as_view(), name='offermaster_edit'),

    # Placeholder for increment functionality (could be a separate view/modal later)
    # Redirects to a new details page as per original ASP.NET
    # For now, this will just load the edit form
    path('offermaster/increment/<int:pk>/', OfferMasterUpdateView.as_view(), name='offermaster_increment'),

    # HTMX endpoint for deleting an offer letter (confirmation in modal)
    path('offermaster/delete/<int:pk>/', OfferMasterDeleteView.as_view(), name='offermaster_delete'),

    # HTMX endpoint for employee name autocomplete
    path('offermaster/autocomplete/', EmployeeNameAutocompleteView.as_view(), name='employee_name_autocomplete'),
]

```

#### 4.6 Tests (`hr/tests.py`)

Comprehensive unit tests for model methods and properties, and integration tests for all views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from decimal import Decimal
from unittest.mock import patch, MagicMock

# Mock out the database interactions for lookup models
# In a real scenario, you'd populate these with test data
@patch('hr.models.EmployeeType.objects.get')
@patch('hr.models.Designation.objects.get')
@patch('hr.models.DutyHour.objects.get')
@patch('hr.models.OfficeStaff.objects.get')
@patch('hr.models.OfficeStaff.objects.filter')
class OfferMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for OfferMaster, using a unique offer_id for each
        cls.offer1 = OfferMaster.objects.create(
            offer_id=101,
            sys_date=datetime(2023, 1, 15, 10, 0, 0),
            type_of=1, # SAPL
            staff_type_id=1,
            title='Mr.',
            employee_name='John Doe',
            designation_id=1,
            duty_hrs_id=1,
            interviewed_by_id=1,
            contact_no='1234567890',
            salary=Decimal('50000.00'),
            comp_id=1,
            fin_year_id='2023'
        )
        cls.offer2 = OfferMaster.objects.create(
            offer_id=102,
            sys_date=datetime(2023, 2, 20, 11, 0, 0),
            type_of=2, # NEHA
            staff_type_id=2,
            title='Ms.',
            employee_name='Jane Smith',
            designation_id=2,
            duty_hrs_id=2,
            interviewed_by_id=2,
            contact_no='0987654321',
            salary=Decimal('75000.00'),
            comp_id=1,
            fin_year_id='2023'
        )
        cls.offer3 = OfferMaster.objects.create(
            offer_id=103,
            sys_date=datetime(2024, 3, 10, 12, 0, 0),
            type_of=1,
            staff_type_id=1,
            title='Dr.',
            employee_name='John Williams',
            designation_id=1,
            duty_hrs_id=1,
            interviewed_by_id=1,
            contact_no='1122334455',
            salary=Decimal('60000.00'),
            comp_id=2, # Different company ID
            fin_year_id='2024'
        )
        # For Offer 101, simulate it being "Confirmed"
        cls.confirmed_staff = MagicMock()
        cls.confirmed_staff.exists.return_value = True

        # For Offer 102, simulate it being "Pending"
        cls.pending_staff = MagicMock()
        cls.pending_staff.exists.return_value = False


    def setUp(self):
        super().setUp()
        # Mock lookup model objects for property tests
        self.mock_employee_type = MagicMock(spec=EmployeeType)
        self.mock_employee_type.description = "Full Time"
        self.mock_designation = MagicMock(spec=Designation)
        self.mock_designation.type = "Software Engineer"
        self.mock_duty_hour = MagicMock(spec=DutyHour)
        self.mock_duty_hour.hours = "8 hrs"
        self.mock_office_staff = MagicMock(spec=OfficeStaff)
        self.mock_office_staff.employee_name = "Interviewer Name"

    def test_offermaster_creation(self, mock_office_staff_filter, mock_office_staff_get, mock_duty_hour_get, mock_designation_get, mock_employee_type_get):
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.employee_name, 'John Doe')
        self.assertEqual(obj.salary, Decimal('50000.00'))
        self.assertEqual(obj.comp_id, 1)

    def test_formatted_date_property(self, *mocks):
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.formatted_date, '15/01/2023')

    def test_type_of_display_property(self, *mocks):
        obj1 = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj1.type_of_display, 'SAPL')
        obj2 = OfferMaster.objects.get(offer_id=102)
        self.assertEqual(obj2.type_of_display, 'NEHA')

    def test_employee_type_description_property(self, mock_office_staff_filter, mock_office_staff_get, mock_duty_hour_get, mock_designation_get, mock_employee_type_get):
        mock_employee_type_get.return_value = self.mock_employee_type
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.employee_type_description, "Full Time")
        mock_employee_type_get.assert_called_with(id=1)

    def test_employee_type_description_not_found(self, mock_office_staff_filter, mock_office_staff_get, mock_duty_hour_get, mock_designation_get, mock_employee_type_get):
        mock_employee_type_get.side_effect = EmployeeType.DoesNotExist
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.employee_type_description, "N/A")

    def test_full_employee_name_property(self, *mocks):
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.full_employee_name, 'Mr. John Doe')

    def test_designation_description_property(self, mock_office_staff_filter, mock_office_staff_get, mock_duty_hour_get, mock_designation_get, mock_employee_type_get):
        mock_designation_get.return_value = self.mock_designation
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.designation_description, "Software Engineer")
        mock_designation_get.assert_called_with(id=1)

    def test_duty_hours_description_property(self, mock_office_staff_filter, mock_office_staff_get, mock_duty_hour_get, mock_designation_get, mock_employee_type_get):
        mock_duty_hour_get.return_value = self.mock_duty_hour
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.duty_hours_description, "8 hrs")
        mock_duty_hour_get.assert_called_with(id=1)

    def test_interviewed_by_name_property(self, mock_office_staff_filter, mock_office_staff_get, mock_duty_hour_get, mock_designation_get, mock_employee_type_get):
        mock_office_staff_get.return_value = self.mock_office_staff
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.interviewed_by_name, "Interviewer Name")
        mock_office_staff_get.assert_called_with(emp_id=1)

    def test_gross_salary_display_property(self, *mocks):
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.gross_salary_display, '50,000.00')
        obj.salary = None
        self.assertEqual(obj.gross_salary_display, '0.00')

    def test_status_property_confirmed(self, mock_office_staff_filter, *mocks):
        mock_office_staff_filter.return_value = self.confirmed_staff
        obj = OfferMaster.objects.get(offer_id=101)
        self.assertEqual(obj.status, "Confirm")
        mock_office_staff_filter.assert_called_with(offer_id=101)

    def test_status_property_pending(self, mock_office_staff_filter, *mocks):
        mock_office_staff_filter.return_value = self.pending_staff
        obj = OfferMaster.objects.get(offer_id=102)
        self.assertEqual(obj.status, "Pending")
        mock_office_staff_filter.assert_called_with(offer_id=102)

    def test_get_filtered_offers_no_filter(self, *mocks):
        offers = OfferMaster.get_filtered_offers(comp_id=1, fin_year_id='2023')
        # Expecting offer1 and offer2, ordered descending by offer_id
        self.assertIn(self.offer1, offers)
        self.assertIn(self.offer2, offers)
        self.assertEqual(offers[0], self.offer2) # Offer 102
        self.assertEqual(offers[1], self.offer1) # Offer 101
        self.assertEqual(len(offers), 2)


    def test_get_filtered_offers_with_employee_name(self, *mocks):
        offers = OfferMaster.get_filtered_offers(emp_name_filter='John', comp_id=1, fin_year_id='2023')
        self.assertIn(self.offer1, offers)
        self.assertNotIn(self.offer2, offers)
        self.assertEqual(len(offers), 1)

    def test_get_filtered_offers_different_company(self, *mocks):
        offers = OfferMaster.get_filtered_offers(comp_id=2, fin_year_id='2024')
        self.assertIn(self.offer3, offers)
        self.assertEqual(len(offers), 1)

    def test_get_filtered_offers_fin_year_filter(self, *mocks):
        # Assume FinYearId is numeric or sorts correctly. '2024' <= '2024'
        offers = OfferMaster.get_filtered_offers(comp_id=1, fin_year_id='2023')
        self.assertIn(self.offer1, offers)
        self.assertIn(self.offer2, offers)
        self.assertEqual(len(offers), 2)

        offers = OfferMaster.get_filtered_offers(comp_id=1, fin_year_id='2022') # Should return none if 2023 is the earliest
        self.assertEqual(len(offers), 0)


@patch('hr.models.EmployeeType.objects.all')
@patch('hr.models.Designation.objects.all')
@patch('hr.models.DutyHour.objects.all')
@patch('hr.models.OfficeStaff.objects.all')
@patch('hr.models.OfficeStaff.objects.filter') # For status property in list view
class OfferMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.offer1 = OfferMaster.objects.create(
            offer_id=101,
            sys_date=datetime(2023, 1, 15, 10, 0, 0),
            type_of=1, # SAPL
            staff_type_id=1,
            title='Mr.',
            employee_name='John Doe',
            designation_id=1,
            duty_hrs_id=1,
            interviewed_by_id=1,
            contact_no='1234567890',
            salary=Decimal('50000.00'),
            comp_id=1,
            fin_year_id='2023'
        )
        cls.offer2 = OfferMaster.objects.create(
            offer_id=102,
            sys_date=datetime(2023, 2, 20, 11, 0, 0),
            type_of=2, # NEHA
            staff_type_id=2,
            title='Ms.',
            employee_name='Jane Smith',
            designation_id=2,
            duty_hrs_id=2,
            interviewed_by_id=2,
            contact_no='0987654321',
            salary=Decimal('75000.00'),
            comp_id=1,
            fin_year_id='2023'
        )

        # Mock objects for lookup tables for form fields
        cls.mock_emp_type = MagicMock()
        cls.mock_emp_type.id = 1
        cls.mock_emp_type.description = "Full Time"

        cls.mock_designation = MagicMock()
        cls.mock_designation.id = 1
        cls.mock_designation.type = "Software Engineer"

        cls.mock_duty_hour = MagicMock()
        cls.mock_duty_hour.id = 1
        cls.mock_duty_hour.hours = "8 hrs"

        cls.mock_office_staff = MagicMock()
        cls.mock_office_staff.emp_id = 1
        cls.mock_office_staff.employee_name = "Interviewer One"

    def setUp(self):
        self.client = Client()
        # Ensure session variables are set for the view's get_queryset
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = '2023'
        session.save()

        # Reset mocks before each test
        # These are used by OfferMasterForm and OfferMasterTablePartialView
        self.mocks = {
            'hr.models.EmployeeType.objects.all': MagicMock(return_value=[self.mock_emp_type]),
            'hr.models.Designation.objects.all': MagicMock(return_value=[self.mock_designation]),
            'hr.models.DutyHour.objects.all': MagicMock(return_value=[self.mock_duty_hour]),
            'hr.models.OfficeStaff.objects.all': MagicMock(return_value=[self.mock_office_staff]),
            'hr.models.OfficeStaff.objects.filter': MagicMock(return_value=MagicMock(exists=lambda: False)), # Default to pending status
        }
        for path, mock_obj in self.mocks.items():
            patcher = patch(path, new=mock_obj)
            patcher.start()
            self.addCleanup(patcher.stop) # Ensure mocks are stopped after each test

    def test_list_view(self, *mocks):
        response = self.client.get(reverse('offermaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/offermaster/list.html')
        # No 'offerletters' in context directly, as it's loaded via HTMX

    def test_table_partial_view_get(self, *mocks):
        response = self.client.get(reverse('offermaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/offermaster/_offermaster_table.html')
        self.assertTrue('offerletters' in response.context)
        self.assertEqual(len(response.context['offerletters']), 2) # Should fetch all 2 offers for comp_id=1, fin_year_id=2023

    def test_table_partial_view_search(self, *mocks):
        response = self.client.get(reverse('offermaster_table') + '?employee_name=John')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/offermaster/_offermaster_table.html')
        self.assertTrue('offerletters' in response.context)
        self.assertEqual(len(response.context['offerletters']), 1)
        self.assertEqual(response.context['offerletters'][0].employee_name, 'John Doe')

    def test_create_view_get(self, *mocks):
        response = self.client.get(reverse('offermaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/offermaster/_offermaster_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self, *mocks):
        # Mock OfferMaster.objects.create to prevent actual DB creation in tests
        with patch('hr.models.OfferMaster.objects.create') as mock_create:
            mock_created_obj = MagicMock(offer_id=103)
            mock_create.return_value = mock_created_obj
            data = {
                'sys_date': '2024-05-01',
                'type_of': 1,
                'staff_type_id': 1, # Use ID from mock
                'title': 'Mr.',
                'employee_name': 'New Employee',
                'designation_id': 1, # Use ID from mock
                'duty_hrs_id': 1, # Use ID from mock
                'interviewed_by_id': 1, # Use ID from mock
                'contact_no': '9988776655',
                'salary': '80000.00',
                'comp_id': 1,
                'fin_year_id': '2024'
            }
            response = self.client.post(reverse('offermaster_add'), data, HTTP_HX_REQUEST='true')
            self.assertEqual(response.status_code, 204) # HTMX success code
            self.assertEqual(response['HX-Trigger'], 'refreshOfferLettersList')
            mock_create.assert_called_once()
            # Verify form data was passed to create method, excluding offer_id if it's auto-assigned
            # This is complex to test perfectly with mock_create, focus on status code and trigger

    def test_create_view_post_invalid(self, *mocks):
        data = {
            'sys_date': '2024-05-01',
            'type_of': 1,
            'staff_type_id': 1,
            'title': 'Mr.',
            'employee_name': 'New Employee',
            'designation_id': 1,
            'duty_hrs_id': 1,
            'interviewed_by_id': 1,
            'contact_no': '9988776655',
            'salary': '-100.00', # Invalid salary
            'comp_id': 1,
            'fin_year_id': '2024'
        }
        response = self.client.post(reverse('offermaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr/offermaster/_offermaster_form.html')
        self.assertContains(response, 'Salary must be a positive value.')

    def test_update_view_get(self, *mocks):
        response = self.client.get(reverse('offermaster_edit', args=[self.offer1.offer_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/offermaster/_offermaster_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.offer1)

    def test_update_view_post_success(self, *mocks):
        data = {
            'sys_date': '2023-01-15', # Existing date
            'type_of': 1,
            'staff_type_id': 1,
            'title': 'Mr.',
            'employee_name': 'John Doe Updated', # Changed name
            'designation_id': 1,
            'duty_hrs_id': 1,
            'interviewed_by_id': 1,
            'contact_no': '1234567890',
            'salary': '55000.00', # Changed salary
            'comp_id': 1,
            'fin_year_id': '2023'
        }
        response = self.client.post(reverse('offermaster_edit', args=[self.offer1.offer_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshOfferLettersList')
        # Re-fetch the object to verify update
        updated_offer = OfferMaster.objects.get(offer_id=self.offer1.offer_id)
        self.assertEqual(updated_offer.employee_name, 'John Doe Updated')
        self.assertEqual(updated_offer.salary, Decimal('55000.00'))

    def test_delete_view_get(self, *mocks):
        response = self.client.get(reverse('offermaster_delete', args=[self.offer1.offer_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/offermaster/_offermaster_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.offer1)

    def test_delete_view_post_success(self, *mocks):
        # Ensure the object exists before deletion attempt
        self.assertTrue(OfferMaster.objects.filter(offer_id=self.offer1.offer_id).exists())
        response = self.client.post(reverse('offermaster_delete', args=[self.offer1.offer_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshOfferLettersList')
        self.assertFalse(OfferMaster.objects.filter(offer_id=self.offer1.offer_id).exists()) # Verify deletion

    def test_employee_name_autocomplete(self, *mocks):
        response = self.client.get(reverse('employee_name_autocomplete') + '?prefixText=jo', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('John Doe', data)
        self.assertNotIn('Jane Smith', data)
        self.assertEqual(len(data), 1) # Should only have John Doe

    def test_employee_name_autocomplete_empty_prefix(self, *mocks):
        response = self.client.get(reverse('employee_name_autocomplete'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 0)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions and Implementation:**

1.  **HTMX for Dynamic Updates:**
    *   **List Loading:** The `offermaster_list.html` uses `hx-get="{% url 'offermaster_table' %}"` with `hx-trigger="load, refreshOfferLettersList from:body"` on a `div#offermasterTable-container`. This ensures the table loads automatically on page load and refreshes whenever the `refreshOfferLettersList` custom event is triggered (after CRUD operations).
    *   **Form Modals:** Buttons for "Add New Offer Letter", "Edit", "Increment", and "Delete" use `hx-get` to load the respective form/confirmation partials (`_offermaster_form.html`, `_offermaster_confirm_delete.html`) into `div#modalContent`.
    *   **Form Submission:** Forms within the modal use `hx-post="{{ request.path }}"` with `hx-swap="none"`. Upon successful submission (Django `form_valid`), the views return `HttpResponse(status=204)` with `HX-Trigger: 'refreshOfferLettersList'` header. This tells HTMX to do nothing (`hx-swap="none"`) but then trigger the `refreshOfferLettersList` event, which reloads the main table and closes the modal.
    *   **Loading Indicators:** `hx-indicator` attributes are used on buttons and forms to display a loading spinner (`htmx-indicator` class) during AJAX calls.

2.  **Alpine.js for UI State Management:**
    *   **Modal Visibility:** The main modal `div#modal` uses `x-data` and `x-init` with custom `on click` handlers to manage its `hidden` class and `opacity` for smooth transitions. It also listens for `refreshOfferLettersList` to automatically close the modal.
    *   **Autocomplete Logic:** The employee search input uses an Alpine.js component (`x-data="{ searchTerm: '', suggestions: [], showSuggestions: false, selectedIndex: -1 }"`) to manage the search term, suggestions list, and UI state (showing/hiding suggestions, keyboard navigation). It dispatches a `search-triggered` event when the search term changes or a suggestion is selected, which HTMX picks up to re-fetch the table.

3.  **DataTables for List Views:**
    *   The `_offermaster_table.html` partial contains a `<table>` with `id="offermasterTable"`.
    *   A `<script>` block within this partial includes `$(document).ready(function() { $('#offermasterTable').DataTable({...}); });`. This ensures DataTables initializes every time the table content is loaded via HTMX. A check `$.fn.DataTable.isDataTable` and `destroy()` is added to prevent re-initialization errors if the table is swapped multiple times.
    *   Pagination, sorting, and client-side filtering are handled automatically by DataTables. Page size is set to 20, matching the original ASP.NET `PageSize`.

4.  **No Additional JavaScript:** All dynamic interactions and UI enhancements are achieved exclusively using HTMX and Alpine.js, eliminating the need for complex custom JavaScript or heavy frontend frameworks.

### Final Notes

*   **Placeholders:** `[APP_NAME]` is `hr`, `[MODEL_NAME]` is `OfferMaster`, `[MODEL_NAME_LOWER]` is `offermaster`, `[MODEL_NAME_PLURAL]` is `OfferLetters`, `[MODEL_NAME_PLURAL_LOWER]` is `offerletters`. `[FRIENDLY_NAME]` and `[FRIENDLY_NAME_PLURAL]` are handled by `verbose_name` in models.
*   **DRY Templates:** Achieved by using `_offermaster_table.html`, `_offermaster_form.html`, and `_offermaster_confirm_delete.html` as partials.
*   **Fat Model, Thin View:** Complex data aggregation and lookups from the `binddata` method are encapsulated within `OfferMaster` model properties and the `get_filtered_offers` class method. Views remain concise, focusing on request/response handling.
*   **Comprehensive Tests:** Unit tests cover model properties and class methods. Integration tests validate view functionality for CRUD operations and HTMX interactions, ensuring robust and maintainable code.
*   **Business Benefits:** This modernization provides a significantly improved user experience (fast, dynamic, no full page reloads), reduced development and maintenance costs (simplified frontend stack, clear separation of concerns), and a future-proof architecture based on modern Django best practices. The automated conversion approach minimizes manual effort and potential errors.