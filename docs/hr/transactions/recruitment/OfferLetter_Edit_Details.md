## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for Offer Letter Module

This document outlines the modernization plan for migrating the `OfferLetter_Edit_Details.aspx` and its associated C# code-behind to a modern Django-based solution. The focus is on leveraging AI-assisted automation for a systematic transition, adhering to the "fat model, thin view" principle, and integrating HTMX and Alpine.js for a dynamic, interactive user experience without extensive traditional JavaScript.

### Business Value of Django Modernization:

Migrating this ASP.NET application to Django offers significant business advantages:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms to a modern, actively maintained framework, improving long-term sustainability and reducing maintenance costs.
2.  **Enhanced Scalability & Performance:** Django's architecture is inherently more scalable, leading to better application performance, especially for future growth.
3.  **Improved Developer Productivity:** Python and Django offer a highly productive development environment, enabling faster feature development and easier onboarding for new developers.
4.  **Better User Experience:** HTMX and Alpine.js provide a highly responsive, single-page application (SPA)-like experience without the complexity of traditional JavaScript frameworks, leading to increased user satisfaction and efficiency.
5.  **Cost Efficiency:** Leveraging open-source technologies like Django, PostgreSQL (or similar), HTMX, and Alpine.js reduces licensing costs associated with proprietary Microsoft technologies.
6.  **Maintainability & Reliability:** The "fat model, thin view" approach, coupled with comprehensive testing, leads to more organized, reliable, and easier-to-maintain code.
7.  **Future-Proofing:** Adopting a popular and modern stack ensures the application remains relevant and adaptable to future business needs and technological advancements.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   NEVER include base.html template code in your output - assume it already exists.
-   Focus ONLY on component-specific code for the current module.
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

## AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we identify the following primary tables and their relationships:

*   **`tblHR_Offer_Master`**: This is the core table storing offer letter details.
    *   **Inferred Columns:** `OfferId` (likely PK, unique identifier), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `Title`, `EmployeeName`, `StaffType` (FK to `tblHR_EmpType`), `TypeOf` (employee type, custom lookup), `salary` (Gross Salary), `DutyHrs` (FK to `tblHR_DutyHour`), `OTHrs` (FK to `tblHR_OTHour`), `OverTime` (FK to `tblHR_OverTime`), `Address`, `ContactNo`, `EmailId`, `InterviewedBy` (FK to `tblHR_OfficeStaff`), `AuthorizedBy` (FK to `tblHR_OfficeStaff`), `ReferenceBy`, `Designation` (FK to `tblHR_Designation`), `ExGratia`, `VehicleAllowance`, `LTA`, `Loyalty`, `PaidLeaves`, `Remarks`, `HeaderText`, `FooterText`, `Bonus`, `AttBonusPer1`, `AttBonusPer2`, `PFEmployee`, `PFCompany`, `Increment`, `IncrementForTheYear`, `EffectFrom`. All monetary values will be `DecimalField`. Dates will be `DateField`. IDs will be `IntegerField` or `ForeignKey`. Text fields will be `CharField` or `TextField`.
*   **`tblHR_Offer_Accessories`**: Stores additional particulars/accessories related to a specific offer letter.
    *   **Inferred Columns:** `Id` (PK), `MId` (FK to `tblHR_Offer_Master.OfferId`), `Perticulars`, `Qty`, `Amount`, `IncludesIn` (FK to `tblHR_IncludesIn`).
*   **`tblHR_Increment_Master`**: Stores historical data for increment letters. This will mirror `tblHR_Offer_Master`.
*   **`tblHR_Increment_Accessories`**: Stores historical data for accessories associated with increment letters. This will mirror `tblHR_Offer_Accessories`.

**Lookup Tables (referenced by `SqlDataSource` and dropdowns):**
*   `tblHR_Designation` (for `DrpDesignation`)
*   `tblHR_DutyHour` (for `DrpDutyHrs`)
*   `tblHR_OTHour` (for `DrpOTHrs`)
*   `tblHR_OverTime` (for `DrpOvertime`)
*   `tblHR_EmpType` (for `DrpEmpType`)
*   `tblHR_IncludesIn` (for `IncludeIn`)
*   `tblHR_OfficeStaff` (for `Txtinterviewedby`, `TxtAuthorizedby`)
*   `tblHR_PF_Slab`
*   `tblCompany_master`

### Step 2: Identify Backend Functionality

**Task:** Determine the core CRUD operations and business logic from the ASP.NET code-behind.

**Instructions:**

*   **Read (R):**
    *   `Page_Load`: Populates the form fields and `GridView1` (accessories) based on `OfferId` from `tblHR_Offer_Master` and `tblHR_Offer_Accessories`.
    *   Dropdowns are populated from various `tblHR_` lookup tables.
*   **Create (C):**
    *   `GridView1_RowCommand` (specifically "Add" and "Add1"): Inserts new accessory records into `tblHR_Offer_Accessories`.
    *   `BtnIncrement_Click`: This is a complex "create" operation, as it copies the *current* `tblHR_Offer_Master` and its accessories to `tblHR_Increment_Master` and `tblHR_Increment_Accessories` to create a historical record *before* updating the main offer.
*   **Update (U):**
    *   `BtnSubmit_Click`: Updates the main offer letter details in `tblHR_Offer_Master`.
    *   `GridView1_RowUpdating`: Updates existing accessory records in `tblHR_Offer_Accessories`.
*   **Delete (D):**
    *   `GridView1_RowDeleting`: Deletes accessory records from `tblHR_Offer_Accessories`.
*   **Calculations & Business Logic:**
    *   `CalSalary` method: This is the central calculation engine, performing complex computations for basic, DA, HRA, PF, PTax, Gratuity, Attendance Bonus, Take Home, and CTC. It depends on `GrossSalary`, `EmployeeType`, `TypeOf`, `AttBonusPer1`, `AttBonusPer2`, `PFEmployee`, `PFCompany`, `ExGratia`, `Loyalty`, `LTA`, `VehicleAllowance`, and the sums from `tblHR_Offer_Accessories`. This entire logic *must* be encapsulated within the `OfferLetter` Django model.
    *   Conditional field visibility/enabling (`txtHeader`, `txtFooter`, `txtIncrementForTheYear`, `txtEffectFrom`, PF/Bonus fields) based on `OI` (Increment flag) and `DrpEmpType` selection.
    *   Email sending upon update or increment.
*   **Validation:** `RequiredFieldValidator` and `RegularExpressionValidator` are present for numerous fields. These will be translated into Django form validations.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map to Django templates and frontend interactions.

**Instructions:**

*   **Main Form (Offer Letter Details):**
    *   `asp:Label` for `lblOfferIncrement`, `lblOfferId`, `Label2` (status message).
    *   `asp:DropDownList`: `DrpDesignation`, `DrpDutyHrs`, `DrpOTHrs`, `DrpOvertime`, `DrpTitle`, `DrpEmpTypeOf`, `DrpEmpType`.
    *   `asp:TextBox`: `TxtName`, `TxtContactNo`, `TxtAddress`, `TxtEmail`, `TxtReferencedby`, `TxtGrossSalry`, `txtHeader`, `txtFooter`, `TxtRemarks`, `txtIncrementForTheYear`, `txtEffectFrom`, `TxtGLTA`, `TxtGVehAll`, `TxtGGratia`, `TxtAnnLOYAlty`, `TxtAnnpaidleaves`, `txtPFEmployee`, `txtPFCompany`, `txtAttB1`, `txtAttB2`, `txtBonus`.
    *   `cc1:CalendarExtender`: For `txtEffectFrom`.
    *   `cc1:AutoCompleteExtender`: For `Txtinterviewedby`, `TxtAuthorizedby`.
    *   **Calculated Output Labels:** `TxtGSal`, `TxtANNualSal`, `TxtGBasic`, `TxtAnBasic`, `TxtGDA`, `TxtAnDA`, `TxtGHRA`, `TxtANHRA`, `TxtGConvenience`, `TxtANConvenience`, `TxtGEdu`, `TxtANEDU`, `TxtGWash`, `TxtANWash`, `TxtGATTBN1`, `TxtGATTBN2`, `TxtGEmpPF`, `TxtGCompPF`, `TxtGPTax`, `lblGratuaty`, `TxtAnnBonus`, `TxtAnnGratuaty`, `lblTH`, `lblTH1`, `lblTH2`, `lblTHAnn`, `lblTHAnn1`, `lblTHAnn2`, `lblCTC`, `lblCTC1`, `lblCTC2`, `lblCTCAnn`, `lblCTCAnn1`, `lblCTCAnn2`. These will be dynamically updated via HTMX.
*   **Accessories Grid (`GridView1`):**
    *   A table displaying `Perticulars`, `Qty`, `Amount`, `Total`.
    *   In-line editing, adding (via footer/empty data template), and deleting. This will be converted to a DataTables instance with HTMX-driven modal forms for CRUD.
*   **Buttons:** `ButtonSubmit` (Calculate), `BtnSubmit` (Update), `BtnIncrement` (Increment), `Button1` (Cancel). These will trigger HTMX POST requests or redirects.

---

### Step 4: Generate Django Code

We will create a Django application named `hr_offer`.

#### 4.1 Models (`hr_offer/models.py`)

This section defines the Django models for the offer letter system, mapping to the existing SQL Server tables. Crucially, the complex salary calculation logic from `CalSalary` in the ASP.NET code-behind will be embedded here as methods within the `OfferLetter` model, adhering to the "fat model" principle.

```python
from django.db import models
from django.db.models import Sum
from decimal import Decimal, ROUND_HALF_UP
import datetime

# --- Lookup Models (Corresponding to SqlDataSource tables) ---

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    designation = models.CharField(db_column='Designation', max_length=255) # Assuming from DataTextField
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True) # From SqlDataSource3 SelectCommand

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.symbol} - {self.designation}" if self.symbol else self.designation

class DutyHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.CharField(db_column='Hours', max_length=50) # Assuming from DataTextField

    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'Duty Hour'
        verbose_name_plural = 'Duty Hours'

    def __str__(self):
        return self.hours

class OTHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.CharField(db_column='Hours', max_length=50) # Assuming from DataTextField

    class Meta:
        managed = False
        db_table = 'tblHR_OTHour'
        verbose_name = 'OT Hour'
        verbose_name_plural = 'OT Hours'

    def __str__(self):
        return self.hours

class OverTimeType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255) # Assuming from DataTextField

    class Meta:
        managed = False
        db_table = 'tblHR_OverTime'
        verbose_name = 'Overtime Type'
        verbose_name_plural = 'Overtime Types'

    def __str__(self):
        return self.description

class EmployeeType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255) # Assuming from DataTextField

    class Meta:
        managed = False
        db_table = 'tblHR_EmpType'
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'

    def __str__(self):
        return self.description

class IncludesIn(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    includes_in = models.CharField(db_column='IncludesIn', max_length=50) # Assuming from DataTextField

    class Meta:
        managed = False
        db_table = 'tblHR_IncludesIn'
        verbose_name = 'Includes In'
        verbose_name_plural = 'Includes In'

    def __str__(self):
        return self.includes_in

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is char
    title = models.CharField(db_column='Title', max_length=10)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    # Add other fields as necessary if they exist in tblHR_OfficeStaff
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title} {self.employee_name} [{self.emp_id}]"

class PF_Slab(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pf_employee_per = models.DecimalField(db_column='PFEmployee', max_digits=5, decimal_places=2) # Assuming percentage
    pf_company_per = models.DecimalField(db_column='PFCompany', max_digits=5, decimal_places=2) # Assuming percentage
    active = models.BooleanField(db_column='Active', default=True) # Assuming active status
    
    class Meta:
        managed = False
        db_table = 'tblHR_PF_Slab'
        verbose_name = 'PF Slab'
        verbose_name_plural = 'PF Slabs'

    def __str__(self):
        return f"PF Slab {self.id} (Emp: {self.pf_employee_per}%, Comp: {self.pf_company_per}%)"


# --- Main Models ---

class OfferLetter(models.Model):
    # Core fields from tblHR_Offer_Master
    offer_id = models.CharField(db_column='OfferId', primary_key=True, max_length=50) # Assuming OfferId is the PK
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # User's session/username
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True) # Mr./Mrs./Miss.
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    staff_type = models.ForeignKey(EmployeeType, on_delete=models.PROTECT, db_column='StaffType', related_name='offer_letters_staff')
    type_of = models.IntegerField(db_column='TypeOf') # Value 1:SAPL, 2:NEHA (based on DrpEmpTypeOf)
    gross_salary = models.DecimalField(db_column='salary', max_digits=18, decimal_places=2) # Gross Salary
    duty_hrs = models.ForeignKey(DutyHour, on_delete=models.PROTECT, db_column='DutyHrs')
    ot_hrs = models.ForeignKey(OTHour, on_delete=models.PROTECT, db_column='OTHrs')
    overtime_applicable = models.ForeignKey(OverTimeType, on_delete=models.PROTECT, db_column='OverTime')
    address = models.TextField(db_column='Address')
    contact_no = models.CharField(db_column='ContactNo', max_length=50)
    email_id = models.EmailField(db_column='EmailId', max_length=255)
    interviewed_by = models.ForeignKey(OfficeStaff, on_delete=models.PROTECT, db_column='InterviewedBy', related_name='interviews_taken')
    authorized_by = models.ForeignKey(OfficeStaff, on_delete=models.PROTECT, db_column='AuthorizedBy', related_name='offers_authorized')
    reference_by = models.CharField(db_column='ReferenceBy', max_length=255, blank=True, null=True)
    designation = models.ForeignKey(Designation, on_delete=models.PROTECT, db_column='Designation')

    # Monetary components
    ex_gratia = models.DecimalField(db_column='ExGratia', max_digits=18, decimal_places=2, default=Decimal('0.00'))
    vehicle_allowance = models.DecimalField(db_column='VehicleAllowance', max_digits=18, decimal_places=2, default=Decimal('0.00'))
    lta = models.DecimalField(db_column='LTA', max_digits=18, decimal_places=2, default=Decimal('0.00'))
    loyalty = models.DecimalField(db_column='Loyalty', max_digits=18, decimal_places=2, default=Decimal('0.00'))
    paid_leaves = models.DecimalField(db_column='PaidLeaves', max_digits=18, decimal_places=2, default=Decimal('0.00')) # Assuming decimal as per TxtAnnpaidleaves
    bonus = models.DecimalField(db_column='Bonus', max_digits=18, decimal_places=2, default=Decimal('0.00'))
    att_bonus_per1 = models.DecimalField(db_column='AttBonusPer1', max_digits=5, decimal_places=2, default=Decimal('0.00')) # Percentage
    att_bonus_per2 = models.DecimalField(db_column='AttBonusPer2', max_digits=5, decimal_places=2, default=Decimal('0.00')) # Percentage
    pf_employee_per = models.DecimalField(db_column='PFEmployee', max_digits=5, decimal_places=2, default=Decimal('0.00')) # Percentage
    pf_company_per = models.DecimalField(db_column='PFCompany', max_digits=5, decimal_places=2, default=Decimal('0.00')) # Percentage

    # Text fields
    header_text = models.TextField(db_column='HeaderText', blank=True, null=True)
    footer_text = models.TextField(db_column='FooterText', blank=True, null=True)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)

    # Increment related fields
    increment = models.IntegerField(db_column='Increment', default=0) # Number of increments
    increment_for_the_year = models.CharField(db_column='IncrementForTheYear', max_length=50, blank=True, null=True) # Ex- 2014 - 2015
    effect_from = models.DateField(db_column='EffectFrom', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Letter'
        verbose_name_plural = 'Offer Letters'

    def __str__(self):
        return f"Offer for {self.employee_name} ({self.offer_id})"

    # --- Salary Calculation Logic (Fat Model) ---
    def _calculate_component(self, percentage, type_of_id, is_annual=False):
        # Placeholder for fun.Offer_Cal logic.
        # This function needs to mimic the complex logic in the original clsFunctions.Offer_Cal.
        # For demonstration, a simplified percentage calculation is used.
        # In a real migration, this would involve detailed analysis of fun.Offer_Cal.
        if type_of_id == 1: # SAPL
            # These percentages are illustrative; actual logic depends on Offer_Cal implementation.
            if percentage == 1: return self.gross_salary * Decimal('0.30') # Basic (30%)
            if percentage == 2: return self.gross_salary * Decimal('0.20') # DA (20%)
            if percentage == 3: return self.gross_salary * Decimal('0.20') # HRA (20%)
            if percentage == 4: return self.gross_salary * Decimal('0.20') # Convenience (20%)
            if percentage == 5: return self.gross_salary * Decimal('0.05') # Education (5%)
            if percentage == 6: return self.gross_salary * Decimal('0.05') # Medical (5%)
            # Attendance bonus 1 & 2 are calculated directly using AttBonusPer1/2.
            # Gratuity depends on the Gratuity_Cal function.
        elif type_of_id == 2: # NEHA
             # Different percentages for NEHA, if applicable, based on original fun.Offer_Cal
             if percentage == 1: return self.gross_salary * Decimal('0.25') # Basic (example)
             if percentage == 2: return self.gross_salary * Decimal('0.15') # DA (example)
             # ... and so on.

        return Decimal('0.00')

    def _calculate_pf(self, is_employee_pf, pf_rate):
        # Placeholder for fun.Pf_Cal logic.
        # In a real migration, analyze Pf_Cal's exact rules (e.g., max salary cap for PF).
        # For demonstration, a direct percentage calculation is used.
        return self.gross_salary * (pf_rate / Decimal('100.00'))

    def _calculate_p_tax(self, gross_plus_att_bonus1_plus_ex_gratia):
        # Placeholder for fun.PTax_Cal logic.
        # This is typically slab-based. For example:
        # if amount <= 5000: return 0
        # elif amount <= 10000: return 150
        # ... and so on.
        # Assuming a simple fixed value or a placeholder for now.
        if gross_plus_att_bonus1_plus_ex_gratia > 25000: # Example slab
            return Decimal('200.00')
        elif gross_plus_att_bonus1_plus_ex_gratia > 15000:
            return Decimal('150.00')
        return Decimal('0.00')

    def _calculate_gratuity(self, is_annual=False):
        # Placeholder for fun.Gratuity_Cal logic.
        # Gratuity is typically calculated as (15 * last_drawn_salary * years_of_service) / 26
        # Assuming a simplified percentage for now if actual calculation is not available.
        if self.type_of == 2: # NEHA type employees have 0 gratuity based on C# code
            return Decimal('0.00')
        
        monthly_gratuity = self.gross_salary * Decimal('0.0481') # Example percentage (15/26 * 12 * factor)
        return monthly_gratuity * (12 if is_annual else 1) # This needs to match the actual formula

    def calculate_salary_components(self):
        gross_salary = self.gross_salary
        if not gross_salary:
            return {} # Return empty if gross salary is not set

        # Derived values (Monthly)
        basic = self._calculate_component(1, self.type_of)
        da = self._calculate_component(2, self.type_of)
        hra = self._calculate_component(3, self.type_of)
        convenience = self._calculate_component(4, self.type_of)
        education = self._calculate_component(5, self.type_of)
        medical = self._calculate_component(6, self.type_of)

        att_bonus1 = gross_salary * (self.att_bonus_per1 / Decimal('100.00'))
        att_bonus2 = gross_salary * (self.att_bonus_per2 / Decimal('100.00'))
        
        # PF depends on employee type (Casuals vs others) and entered PF percentages
        pfe = Decimal('0.00')
        pfc = Decimal('0.00')
        bonus_monthly = self.bonus
        if self.staff_type.description != "Casuals": # "5" is Casuals type in original
            pfe = self._calculate_pf(True, self.pf_employee_per)
            pfc = self._calculate_pf(False, self.pf_company_per)
        else: # Casuals have 0 PF and bonus, etc.
            att_bonus1 = Decimal('0.00')
            att_bonus2 = Decimal('0.00')
            bonus_monthly = Decimal('0.00') # monthly bonus not annual for calculations here

        # PTax calculation
        ptax_base = gross_salary + att_bonus1 + self.ex_gratia
        p_tax = self._calculate_p_tax(ptax_base)

        gratuity = self._calculate_gratuity(is_annual=False)
        
        # Aggregate Accessories
        accessories_data = self.offer_accessories.aggregate(
            ctc_amt=Sum('qty') * Sum('amount', filter=models.Q(includes_in__id=1)), # IncludesIn=1 (CTC)
            take_home_amt=Sum('qty') * Sum('amount', filter=models.Q(includes_in__id=2)), # IncludesIn=2 (Take Home)
            both_amt=Sum('qty') * Sum('amount', filter=models.Q(includes_in__id=3)) # IncludesIn=3 (Both)
        )
        accessories_ctc = accessories_data.get('ctc_amt') or Decimal('0.00')
        accessories_take_home = accessories_data.get('take_home_amt') or Decimal('0.00')
        accessories_both = accessories_data.get('both_amt') or Decimal('0.00')
        
        # Take Home Salary
        take_home = round((gross_salary + self.ex_gratia + accessories_take_home + accessories_both) - (pfe + p_tax), 2)
        take_home_att1 = round(take_home + att_bonus1, 2)
        take_home_att2 = round(take_home + att_bonus2, 2)

        # CTC (Cost to Company)
        # Note: Original C# had fun.Bonus_Cal which was not clear from code, assuming self.bonus * 12
        # and Gratuity_Cal again. Ensure this matches original C# logic precisely.
        ctc_base = round(gross_salary + bonus_monthly + self.loyalty + self.lta +
                         gratuity + pfc + self.ex_gratia + self.vehicle_allowance, 2)
        ctc = ctc_base + accessories_ctc + accessories_both
        ctc_att1 = round(ctc + att_bonus1, 2)
        ctc_att2 = round(ctc + att_bonus2, 2)

        # Annual Values
        annual_gross_salary = gross_salary * 12
        annual_basic = basic * 12
        annual_da = da * 12
        annual_hra = hra * 12
        annual_convenience = convenience * 12
        annual_education = education * 12
        annual_medical = medical * 12
        annual_bonus = bonus_monthly * 12 # Based on C# `Bonus = Convert.ToDouble(txtBonus.Text) * 12;`
        annual_gratuity = self._calculate_gratuity(is_annual=True) # If gratuity is already annual, adjust here.

        return {
            'gross_salary_monthly': gross_salary.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'gross_salary_annual': annual_gross_salary.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'basic_monthly': basic.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'basic_annual': annual_basic.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'da_monthly': da.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'da_annual': annual_da.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'hra_monthly': hra.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'hra_annual': annual_hra.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'convenience_monthly': convenience.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'convenience_annual': annual_convenience.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'education_monthly': education.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'education_annual': annual_education.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'medical_monthly': medical.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'medical_annual': annual_medical.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'att_bonus1_monthly': att_bonus1.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'att_bonus2_monthly': att_bonus2.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'pf_employee_monthly': pfe.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'pf_company_monthly': pfc.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'p_tax_monthly': p_tax.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'gratuity_monthly': gratuity.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'gratuity_annual': annual_gratuity.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'bonus_annual': annual_bonus.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'take_home_monthly': take_home.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'take_home_att1_monthly': take_home_att1.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'take_home_att2_monthly': take_home_att2.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'take_home_annual': (take_home * 12).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'take_home_att1_annual': (take_home_att1 * 12).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'take_home_att2_annual': (take_home_att2 * 12).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'ctc_monthly': ctc.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'ctc_att1_monthly': ctc_att1.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'ctc_att2_monthly': ctc_att2.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'ctc_annual': (ctc * 12).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'ctc_att1_annual': (ctc_att1 * 12).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
            'ctc_att2_annual': (ctc_att2 * 12).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
        }


class OfferAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    offer_master = models.ForeignKey(OfferLetter, on_delete=models.CASCADE, db_column='MId', related_name='offer_accessories')
    particulars = models.CharField(db_column='Perticulars', max_length=255)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=2)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)
    # Total column is calculated, not stored.
    includes_in = models.ForeignKey(IncludesIn, on_delete=models.PROTECT, db_column='IncludesIn')

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'Offer Accessory'
        verbose_name_plural = 'Offer Accessories'

    def __str__(self):
        return self.particulars

    @property
    def total(self):
        return (self.qty * self.amount).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

# --- Increment Models (History) ---

class IncrementLetter(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Auto-incrementing primary key
    offer_id = models.CharField(db_column='OfferId', max_length=50) # Link to original offer ID
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    staff_type = models.ForeignKey(EmployeeType, on_delete=models.PROTECT, db_column='StaffType', related_name='increment_letters_staff')
    type_of = models.IntegerField(db_column='TypeOf')
    gross_salary = models.DecimalField(db_column='salary', max_digits=18, decimal_places=2)
    duty_hrs = models.ForeignKey(DutyHour, on_delete=models.PROTECT, db_column='DutyHrs', related_name='increment_letters_duty')
    ot_hrs = models.ForeignKey(OTHour, on_delete=models.PROTECT, db_column='OTHrs', related_name='increment_letters_ot')
    overtime_applicable = models.ForeignKey(OverTimeType, on_delete=models.PROTECT, db_column='OverTime', related_name='increment_letters_overtime')
    address = models.TextField(db_column='Address')
    contact_no = models.CharField(db_column='ContactNo', max_length=50)
    email_id = models.EmailField(db_column='EmailId', max_length=255)
    interviewed_by = models.ForeignKey(OfficeStaff, on_delete=models.PROTECT, db_column='InterviewedBy', related_name='increment_interviews')
    authorized_by = models.ForeignKey(OfficeStaff, on_delete=models.PROTECT, db_column='AuthorizedBy', related_name='increment_authorizations')
    reference_by = models.CharField(db_column='ReferenceBy', max_length=255, blank=True, null=True)
    designation = models.ForeignKey(Designation, on_delete=models.PROTECT, db_column='Designation', related_name='increment_letters_designation')

    ex_gratia = models.DecimalField(db_column='ExGratia', max_digits=18, decimal_places=2)
    vehicle_allowance = models.DecimalField(db_column='VehicleAllowance', max_digits=18, decimal_places=2)
    lta = models.DecimalField(db_column='LTA', max_digits=18, decimal_places=2)
    loyalty = models.DecimalField(db_column='Loyalty', max_digits=18, decimal_places=2)
    paid_leaves = models.DecimalField(db_column='PaidLeaves', max_digits=18, decimal_places=2)
    bonus = models.DecimalField(db_column='Bonus', max_digits=18, decimal_places=2)
    att_bonus_per1 = models.DecimalField(db_column='AttBonusPer1', max_digits=5, decimal_places=2)
    att_bonus_per2 = models.DecimalField(db_column='AttBonusPer2', max_digits=5, decimal_places=2)
    pf_employee_per = models.DecimalField(db_column='PFEmployee', max_digits=5, decimal_places=2)
    pf_company_per = models.DecimalField(db_column='PFCompany', max_digits=5, decimal_places=2)

    header_text = models.TextField(db_column='HeaderText', blank=True, null=True)
    footer_text = models.TextField(db_column='FooterText', blank=True, null=True)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)

    increment = models.IntegerField(db_column='Increment', default=0)
    increment_for_the_year = models.CharField(db_column='IncrementForTheYear', max_length=50, blank=True, null=True)
    effect_from = models.DateField(db_column='EffectFrom', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Master'
        verbose_name = 'Increment Letter'
        verbose_name_plural = 'Increment Letters'

    def __str__(self):
        return f"Increment for {self.employee_name} (Offer: {self.offer_id}, Inc: {self.increment})"

class IncrementAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    increment_master = models.ForeignKey(IncrementLetter, on_delete=models.CASCADE, db_column='MId', related_name='increment_accessories')
    offer_mid = models.CharField(db_column='OfferMId', max_length=50) # The original OfferId
    particulars = models.CharField(db_column='Perticulars', max_length=255)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=2)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)
    includes_in = models.ForeignKey(IncludesIn, on_delete=models.PROTECT, db_column='IncludesIn', related_name='increment_accessories_includes_in')

    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Accessories'
        verbose_name = 'Increment Accessory'
        verbose_name_plural = 'Increment Accessories'

    def __str__(self):
        return self.particulars

    @property
    def total(self):
        return (self.qty * self.amount).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
```

#### 4.2 Forms (`hr_offer/forms.py`)

This section defines Django forms for the main Offer Letter details and the accessories. Widgets are applied for Tailwind CSS styling. Custom validation mirrors the ASP.NET validators.

```python
from django import forms
from .models import OfferLetter, OfferAccessory, EmployeeType, Designation, DutyHour, OTHour, OverTimeType, IncludesIn, OfficeStaff
from decimal import Decimal
import re

class OfferLetterForm(forms.ModelForm):
    # Overriding fields to use specific models for dropdowns and for cleaner display
    staff_type = forms.ModelChoiceField(
        queryset=EmployeeType.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'change, keyup delay:300ms', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'})
    )
    type_of = forms.ChoiceField(
        choices=[('0', 'Select'), ('1', 'SAPL'), ('2', 'NEHA')],
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'change, keyup delay:300ms', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'})
    )
    designation = forms.ModelChoiceField(
        queryset=Designation.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    duty_hrs = forms.ModelChoiceField(
        queryset=DutyHour.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    ot_hrs = forms.ModelChoiceField(
        queryset=OTHour.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    overtime_applicable = forms.ModelChoiceField(
        queryset=OverTimeType.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    interviewed_by = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.all(),
        empty_label="Select Employee", # Simplified from AutocompleteExtender
        widget=forms.Select(attrs={'class': 'box3'})
    )
    authorized_by = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.all(),
        empty_label="Select Employee", # Simplified from AutocompleteExtender
        widget=forms.Select(attrs={'class': 'box3'})
    )

    class Meta:
        model = OfferLetter
        fields = [
            'title', 'employee_name', 'designation', 'duty_hrs', 'ot_hrs', 'overtime_applicable',
            'contact_no', 'address', 'email_id', 'staff_type', 'type_of',
            'interviewed_by', 'authorized_by', 'reference_by', 'gross_salary',
            'header_text', 'footer_text', 'remarks', 'increment_for_the_year', 'effect_from',
            'ex_gratia', 'vehicle_allowance', 'lta', 'loyalty', 'paid_leaves',
            'bonus', 'att_bonus_per1', 'att_bonus_per2', 'pf_employee_per', 'pf_company_per'
        ]
        widgets = {
            'title': forms.Select(choices=[('Mr.', 'Mr.'), ('Mrs.', 'Mrs.'), ('Miss.', 'Miss.')], attrs={'class': 'box3'}),
            'employee_name': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Employee Name'}),
            'contact_no': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Contact Number'}),
            'address': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'placeholder': 'Address'}),
            'email_id': forms.EmailInput(attrs={'class': 'box3', 'placeholder': 'Email Id'}),
            'reference_by': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Reference By'}),
            'gross_salary': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'header_text': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'placeholder': 'Header Text'}),
            'footer_text': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'placeholder': 'Footer Text'}),
            'remarks': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Remarks'}),
            'increment_for_the_year': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Ex- 2014 - 2015'}),
            'effect_from': forms.DateInput(attrs={'class': 'box3', 'type': 'date'}), # HTML5 date input
            
            # Monetary fields, include HTMX trigger for calculation
            'ex_gratia': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'vehicle_allowance': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'lta': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'loyalty': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'paid_leaves': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'bonus': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'att_bonus_per1': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'att_bonus_per2': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'pf_employee_per': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'pf_company_per': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
        }
        labels = {
            'gross_salary': 'Gross Salary',
            'att_bonus_per1': 'Attend Bonus - 1 (%)',
            'att_bonus_per2': 'Attend Bonus - 2 (%)',
            'pf_employee_per': 'PF-Employee (%)',
            'pf_company_per': 'PF-Company (%)',
            'ex_gratia': 'Ex Gratia',
            'vehicle_allowance': 'Vehicle Allowance',
            'lta': 'LTA',
            'loyalty': 'Loyalty Benefits',
            'paid_leaves': 'Paid Leaves',
            'bonus': 'Bonus (Monthly)',
            'header_text': 'Header',
            'footer_text': 'Footer',
            'increment_for_the_year': 'Increment For The Year',
            'effect_from': 'Effect From',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply general Tailwind/ERP CSS classes to all fields
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.CheckboxInput, forms.RadioSelect, forms.Select, forms.DateInput)):
                field.widget.attrs.update({'class': 'box3'})
            # Set default values for number fields as per ASP.NET
            if isinstance(field, forms.DecimalField) and field.initial is None:
                field.initial = Decimal('0.00')

    def clean(self):
        cleaned_data = super().clean()
        # Custom validations similar to ASP.NET RequiredFieldValidator and RegularExpressionValidator
        # Example: Email format validation (already handled by EmailField, but can be customized)
        email = cleaned_data.get('email_id')
        if email and not re.match(r"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$", email):
            self.add_error('email_id', 'Enter a valid email address.')
        
        # Example: Gross Salary validation (already handled by DecimalField, but can be customized)
        gross_salary = cleaned_data.get('gross_salary')
        if gross_salary is not None and not (Decimal('0') <= gross_salary <= Decimal('999999999999999.99')): # Max 15 digits integer, 3 decimal
             self.add_error('gross_salary', 'Gross Salary must be a valid number (max 15 digits integer, 2 decimal).')

        # Conditional validation for increment fields
        if self.instance and self.instance.increment > 0 or self.data.get('action') == 'increment':
            if not cleaned_data.get('increment_for_the_year'):
                self.add_error('increment_for_the_year', 'This field is required for increment letters.')
            if not cleaned_data.get('effect_from'):
                self.add_error('effect_from', 'This field is required for increment letters.')

        # Ensure mandatory fields for Update/Increment are filled
        if self.data.get('action') in ['update', 'increment']:
            required_fields = ['employee_name', 'address', 'gross_salary', 'header_text', 'footer_text']
            for field_name in required_fields:
                if not cleaned_data.get(field_name):
                    self.add_error(field_name, 'This field is required.')
            if cleaned_data.get('type_of') == '0':
                self.add_error('type_of', 'Please select a Type of Employee.')

        return cleaned_data

class OfferAccessoryForm(forms.ModelForm):
    includes_in = forms.ModelChoiceField(
        queryset=IncludesIn.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    class Meta:
        model = OfferAccessory
        fields = ['particulars', 'qty', 'amount', 'includes_in']
        widgets = {
            'particulars': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Particulars'}),
            'qty': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'placeholder': 'Qty'}),
            'amount': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'placeholder': 'Amount'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        qty = cleaned_data.get('qty')
        amount = cleaned_data.get('amount')

        # Validation similar to fun.NumberValidationQty
        if qty is not None and not (Decimal('0') <= qty <= Decimal('999999999999999.99')):
            self.add_error('qty', 'Quantity must be a valid number.')
        if amount is not None and not (Decimal('0') <= amount <= Decimal('999999999999999.99')):
            self.add_error('amount', 'Amount must be a valid number.')
        
        return cleaned_data

```

#### 4.3 Views (`hr_offer/views.py`)

The views handle requests, interact with models and forms, and render templates. They are kept thin, delegating complex logic to the models. HTMX requests are managed to provide dynamic updates without full page reloads.

```python
from django.views.generic import DetailView, UpdateView, FormView, ListView, CreateView, DeleteView
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from django.core.mail import send_mail
from django.conf import settings # Assuming company email settings are in Django settings

from .models import OfferLetter, OfferAccessory, IncrementLetter, IncrementAccessory, PF_Slab, OfficeStaff, Company
from .forms import OfferLetterForm, OfferAccessoryForm

# Helper to get current user/company/finyear from session (replace with actual logic)
def get_session_data(request):
    # In a real ERP, CompId, FinYearId, SessionId would come from authenticated user's profile
    # or a system-wide setting. For now, assume placeholders or static values for demonstration.
    comp_id = getattr(request.user, 'comp_id', 1) # Example: Get from user profile
    fin_year_id = getattr(request.user, 'fin_year_id', 1) # Example: Get from user profile
    session_id = request.user.emp_id if request.user.is_authenticated else 'ANON' # Assuming EmpId for session_id
    return comp_id, fin_year_id, session_id

class OfferLetterDetailView(UpdateView):
    model = OfferLetter
    form_class = OfferLetterForm
    template_name = 'hr_offer/offerletter_detail.html'
    context_object_name = 'offer_letter'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        offer_letter = self.get_object()
        
        # Determine if it's an increment view (OI flag from ASP.NET QueryString)
        # Assuming 'OI' is passed as a URL parameter for this view
        context['is_increment_view'] = self.request.GET.get('OI') == '1'
        
        # Populate form fields with data from the instance
        context['offer_id_label'] = offer_letter.offer_id
        context['offer_increment_label'] = "Increment Letter" if context['is_increment_view'] else "Offer Letter - Edit"

        # Pass accessories form for adding new items
        context['accessory_form'] = OfferAccessoryForm()

        # Initial calculation results for the first load
        context['salary_components'] = offer_letter.calculate_salary_components()

        return context

    def form_valid(self, form):
        offer_letter = form.save(commit=False)
        comp_id, fin_year_id, session_id = get_session_data(self.request)
        offer_letter.comp_id = comp_id
        offer_letter.fin_year_id = fin_year_id
        offer_letter.session_id = session_id
        offer_letter.sys_date = datetime.date.today()
        offer_letter.sys_time = datetime.datetime.now().time()

        action = self.request.POST.get('action') # 'update' or 'increment'

        if action == 'increment':
            # Create historical record (tblHR_Increment_Master and tblHR_Increment_Accessories)
            # This logic needs to be robust and potentially transactional
            with transaction.atomic():
                # Get the current state of the OfferLetter before update
                old_offer_letter = OfferLetter.objects.get(pk=self.get_object().pk)
                
                # Increment the counter and create historical record
                offer_letter.increment += 1
                
                IncrementLetter.objects.create(
                    offer_id=old_offer_letter.offer_id,
                    sys_date=old_offer_letter.sys_date,
                    sys_time=old_offer_letter.sys_time,
                    session_id=old_offer_letter.session_id,
                    comp_id=old_offer_letter.comp_id,
                    fin_year_id=old_offer_letter.fin_year_id,
                    title=old_offer_letter.title,
                    employee_name=old_offer_letter.employee_name,
                    staff_type=old_offer_letter.staff_type,
                    type_of=old_offer_letter.type_of,
                    gross_salary=old_offer_letter.gross_salary,
                    duty_hrs=old_offer_letter.duty_hrs,
                    ot_hrs=old_offer_letter.ot_hrs,
                    overtime_applicable=old_offer_letter.overtime_applicable,
                    address=old_offer_letter.address,
                    contact_no=old_offer_letter.contact_no,
                    email_id=old_offer_letter.email_id,
                    interviewed_by=old_offer_letter.interviewed_by,
                    authorized_by=old_offer_letter.authorized_by,
                    reference_by=old_offer_letter.reference_by,
                    designation=old_offer_letter.designation,
                    ex_gratia=old_offer_letter.ex_gratia,
                    vehicle_allowance=old_offer_letter.vehicle_allowance,
                    lta=old_offer_letter.lta,
                    loyalty=old_offer_letter.loyalty,
                    paid_leaves=old_offer_letter.paid_leaves,
                    bonus=old_offer_letter.bonus,
                    att_bonus_per1=old_offer_letter.att_bonus_per1,
                    att_bonus_per2=old_offer_letter.att_bonus_per2,
                    pf_employee_per=old_offer_letter.pf_employee_per,
                    pf_company_per=old_offer_letter.pf_company_per,
                    header_text=old_offer_letter.header_text,
                    footer_text=old_offer_letter.footer_text,
                    remarks=old_offer_letter.remarks,
                    increment=old_offer_letter.increment,
                    increment_for_the_year=old_offer_letter.increment_for_the_year,
                    effect_from=old_offer_letter.effect_from,
                )
                
                new_increment_letter = IncrementLetter.objects.latest('id') # Get the newly created increment letter
                
                for accessory in old_offer_letter.offer_accessories.all():
                    IncrementAccessory.objects.create(
                        increment_master=new_increment_letter,
                        offer_mid=accessory.offer_master.offer_id, # Link to original offer ID
                        particulars=accessory.particulars,
                        qty=accessory.qty,
                        amount=accessory.amount,
                        includes_in=accessory.includes_in
                    )
                
                offer_letter.save() # Save the updated offer letter
                messages.success(self.request, 'Increment Letter generated and Offer updated successfully.')
                self._send_offer_email(offer_letter, 'Increment Letter', session_id)
                
        elif action == 'update':
            offer_letter.save()
            messages.success(self.request, 'Employee data updated successfully.')
            self._send_offer_email(offer_letter, 'Offer Letter Change', session_id)

        # Redirect to the main edit page or a list page
        if self.request.headers.get('HX-Request'):
            # HTMX request, send a trigger to refresh the entire page or redirect client-side
            return HttpResponse(status=204, headers={'HX-Trigger': 'offerLetterUpdated'}) # Custom event to trigger JS redirect
        return redirect(reverse_lazy('offerletter_edit', kwargs={'pk': offer_letter.offer_id}))


    def _send_offer_email(self, offer_letter, subject_prefix, changed_by_emp_id):
        # Fetch sender and recipient emails from tblCompany_master/OfficeStaff
        try:
            # Assuming Company model exists and has ErpSysmail
            company = Company.objects.get(comp_id=offer_letter.comp_id) # Replace with actual logic to get company
            from_email = company.erp_sys_mail if hasattr(company, 'erp_sys_mail') else settings.DEFAULT_FROM_EMAIL
            smtp_server = company.mail_server_ip if hasattr(company, 'mail_server_ip') else None # Django handles SMTP config

            # Fetch changed by name
            changed_by_staff = OfficeStaff.objects.get(emp_id=changed_by_emp_id)
            changed_by_name = str(changed_by_staff)
            
            # Recipient emails (hardcoded in ASP.NET, should be configurable)
            to_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
            bcc_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

            subject = f"{subject_prefix}: {offer_letter.employee_name}"
            body = (
                f"The {subject_prefix.lower()} of {offer_letter.employee_name} "
                f"is generated/changed/updated by {changed_by_name}.<br><br><br><br>"
                f"This is Auto generated mail by ERP system, please do not reply.<br><br> Thank you."
            )
            
            send_mail(
                subject,
                body,
                from_email,
                to_emails,
                html_message=body,
                fail_silently=False,
                # connection=SMTPConnection(host=smtp_server, ...) if not using Django default settings
            )
        except Exception as e:
            messages.error(self.request, f"Email sending failed: {e}")
            # Log the error for debugging


# HTMX endpoint for rendering the accessory table
class OfferAccessoryTablePartialView(DetailView):
    model = OfferLetter
    template_name = 'hr_offer/_offeraccessory_table.html'
    context_object_name = 'offer_letter'

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Trigger re-calculation on table refresh if form inputs change
        # This is important because accessory changes affect total salary
        self.object.calculate_salary_components()
        return super().get(request, *args, **kwargs)


# HTMX endpoint for adding a new accessory
class OfferAccessoryCreateHTMXView(CreateView):
    model = OfferAccessory
    form_class = OfferAccessoryForm
    template_name = 'hr_offer/_offeraccessory_form.html' # Partial template for modal

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['offer_id'] = self.kwargs['offer_id']
        return context

    def form_valid(self, form):
        offer_letter = get_object_or_404(OfferLetter, pk=self.kwargs['offer_id'])
        accessory = form.save(commit=False)
        accessory.offer_master = offer_letter
        accessory.save()
        messages.success(self.request, 'Accessory added successfully.')
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshAccessoryTable, refreshSalaryCalculation'})

    def form_invalid(self, form):
        return render(self.request, self.template_name, {
            'form': form,
            'offer_id': self.kwargs['offer_id']
        }, status=400) # Render with errors


# HTMX endpoint for updating an accessory
class OfferAccessoryUpdateHTMXView(UpdateView):
    model = OfferAccessory
    form_class = OfferAccessoryForm
    template_name = 'hr_offer/_offeraccessory_form.html' # Partial template for modal
    pk_url_kwarg = 'accessory_pk' # Use a different kwarg to avoid conflict with offer_id

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['offer_id'] = self.kwargs['offer_id']
        return context

    def form_valid(self, form):
        form.save()
        messages.success(self.request, 'Accessory updated successfully.')
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshAccessoryTable, refreshSalaryCalculation'})

    def form_invalid(self, form):
        return render(self.request, self.template_name, {
            'form': form,
            'offer_id': self.kwargs['offer_id']
        }, status=400)


# HTMX endpoint for deleting an accessory
class OfferAccessoryDeleteHTMXView(DeleteView):
    model = OfferAccessory
    template_name = 'hr_offer/_confirm_delete_accessory.html' # Partial template for modal
    pk_url_kwarg = 'accessory_pk'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['offer_id'] = self.kwargs['offer_id']
        return context

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Accessory deleted successfully.')
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshAccessoryTable, refreshSalaryCalculation'})


# HTMX endpoint for salary calculation
class CalculateSalaryHTMXView(DetailView):
    model = OfferLetter
    template_name = 'hr_offer/_salary_calculation_results.html' # Partial for calculation results
    context_object_name = 'offer_letter'

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        
        # Manually update model instance with form data from HTMX request for calculation
        # This is a GET request with form data, so it's in request.GET
        # Note: This is simplified. For full robustness, you might need a dedicated form for these inputs.
        
        # Loop through all relevant form fields in request.GET and apply them
        # to a temporary instance or the object itself if safe for GET requests
        form_data = {
            'gross_salary': request.GET.get('gross_salary', self.object.gross_salary),
            'type_of': request.GET.get('type_of', self.object.type_of),
            'staff_type': request.GET.get('staff_type', self.object.staff_type.pk if self.object.staff_type else None), # Assuming PK as value
            'ex_gratia': request.GET.get('ex_gratia', self.object.ex_gratia),
            'vehicle_allowance': request.GET.get('vehicle_allowance', self.object.vehicle_allowance),
            'lta': request.GET.get('lta', self.object.lta),
            'loyalty': request.GET.get('loyalty', self.object.loyalty),
            'paid_leaves': request.GET.get('paid_leaves', self.object.paid_leaves),
            'bonus': request.GET.get('bonus', self.object.bonus),
            'att_bonus_per1': request.GET.get('att_bonus_per1', self.object.att_bonus_per1),
            'att_bonus_per2': request.GET.get('att_bonus_per2', self.object.att_bonus_per2),
            'pf_employee_per': request.GET.get('pf_employee_per', self.object.pf_employee_per),
            'pf_company_per': request.GET.get('pf_company_per', self.object.pf_company_per),
        }
        
        # Convert incoming string values to Decimal/int as required by the model
        for key, value in form_data.items():
            if value is None:
                continue
            if key in ['gross_salary', 'ex_gratia', 'vehicle_allowance', 'lta', 'loyalty', 'paid_leaves', 'bonus', 'att_bonus_per1', 'att_bonus_per2', 'pf_employee_per', 'pf_company_per']:
                try:
                    setattr(self.object, key, Decimal(value))
                except (ValueError, TypeError):
                    setattr(self.object, key, Decimal('0.00')) # Fallback to 0 if invalid
            elif key == 'type_of':
                try:
                    setattr(self.object, key, int(value))
                except (ValueError, TypeError):
                    pass # Keep existing
            elif key == 'staff_type' and value:
                 try:
                    # Need to retrieve the actual EmployeeType instance
                    employee_type_instance = EmployeeType.objects.get(pk=int(value))
                    setattr(self.object, key, employee_type_instance)
                 except (ValueError, EmployeeType.DoesNotExist):
                    pass # Keep existing or set to None

        # Re-calculate using the updated (but unsaved) object
        context = self.get_context_data(object=self.object)
        context['salary_components'] = self.object.calculate_salary_components()
        return self.render_to_response(context)

# Custom error view to capture messages from response triggers
def handle_htmx_messages(request):
    # This view won't be directly rendered.
    # It's a placeholder for HTMX to potentially swap messages into.
    # Messages are usually handled client-side with an Alpine.js component.
    return HttpResponse("")
```

#### 4.4 Templates (`hr_offer/templates/hr_offer/`)

Templates are designed for HTMX and Alpine.js. `base.html` (not included here) would set up Alpine.js, HTMX, and DataTables CDNs.

**`offerletter_detail.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold fontcsswhite" style="background:url(/static/images/hdbg.JPG); padding: 5px;">
            <label id="lblOfferIncrement">{{ offer_increment_label }}</label>&nbsp;&nbsp;&nbsp;&nbsp;
            Offer Id: <label id="lblOfferId">{{ offer_id_label }}</label>
        </h2>
    </div>

    {% if messages %}
    <div x-data="{ show: true }" x-init="setTimeout(() => show = false, 5000)" x-show="show" 
         class="mb-4 p-3 rounded-md shadow-sm bg-green-100 border border-green-400 text-green-700">
        {% for message in messages %}
            <p>{{ message }}</p>
        {% endfor %}
    </div>
    {% endif %}

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <form hx-post="{% url 'offerletter_edit' offer_letter.pk %}" hx-swap="none" hx-indicator="#form-indicator">
            {% csrf_token %}
            <input type="hidden" name="action" id="form-action" value="update">
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Main Offer Details -->
                <div>
                    <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">Designation</label>
                    {{ form.designation }}
                    {% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                    <div class="flex items-center space-x-2">
                        {{ form.title }}
                        {{ form.employee_name }}
                    </div>
                    {% if form.title.errors %}<p class="text-red-500 text-xs mt-1">{{ form.title.errors }}</p>{% endif %}
                    {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                </div>
                <div x-data="{ showHeaderFooter: {{ not is_increment_view and offer_letter.increment == 0 | lower }} }">
                    <label x-show="showHeaderFooter" for="{{ form.header_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Header</label>
                    <textarea x-show="showHeaderFooter" name="{{ form.header_text.name }}" id="{{ form.header_text.id_for_label }}" class="box3 h-24" placeholder="Header Text">{{ form.header_text.value|default:"" }}</textarea>
                    {% if form.header_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.header_text.errors }}</p>{% endif %}

                    <label x-show="showHeaderFooter" for="{{ form.footer_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mt-4">Footer</label>
                    <textarea x-show="showHeaderFooter" name="{{ form.footer_text.name }}" id="{{ form.footer_text.id_for_label }}" class="box3 h-24" placeholder="Footer Text">{{ form.footer_text.value|default:"" }}</textarea>
                    {% if form.footer_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.footer_text.errors }}</p>{% endif %}

                    <div x-data="{ showIncrementFields: {{ is_increment_view or offer_letter.increment > 0 | lower }} }">
                        <label x-show="showIncrementFields" for="{{ form.increment_for_the_year.id_for_label }}" class="block text-sm font-medium text-gray-700 mt-4 font-bold">Effect From For The Year</label>
                        <input x-show="showIncrementFields" type="text" name="{{ form.increment_for_the_year.name }}" id="{{ form.increment_for_the_year.id_for_label }}" class="box3 w-24" value="{{ form.increment_for_the_year.value|default:"" }}" placeholder="Ex- 2014 - 2015">
                        {% if form.increment_for_the_year.errors %}<p class="text-red-500 text-xs mt-1">{{ form.increment_for_the_year.errors }}</p>{% endif %}

                        <label x-show="showIncrementFields" for="{{ form.effect_from.id_for_label }}" class="block text-sm font-medium text-gray-700 mt-4 font-bold">Effect From</label>
                        <input x-show="showIncrementFields" type="date" name="{{ form.effect_from.name }}" id="{{ form.effect_from.id_for_label }}" class="box3 w-24" value="{{ form.effect_from.value|default:""|date:'Y-m-d' }}">
                        {% if form.effect_from.errors %}<p class="text-red-500 text-xs mt-1">{{ form.effect_from.errors }}</p>{% endif %}
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label for="{{ form.duty_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">Duty Hours</label>
                    {{ form.duty_hrs }}
                    {% if form.duty_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.duty_hrs.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Nos.</label>
                    {{ form.contact_no }}
                    {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.ot_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Hours</label>
                    {{ form.ot_hrs }}
                    {% if form.ot_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ot_hrs.errors }}</p>{% endif %}
                </div>
                <div class="row-span-2">
                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                    {{ form.address }}
                    {% if form.address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.address.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.overtime_applicable.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Applicable</label>
                    {{ form.overtime_applicable }}
                    {% if form.overtime_applicable.errors %}<p class="text-red-500 text-xs mt-1">{{ form.overtime_applicable.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.staff_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type of Employee</label>
                    {{ form.type_of }}
                    {{ form.staff_type }}
                    {% if form.type_of.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_of.errors }}</p>{% endif %}
                    {% if form.staff_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.staff_type.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.email_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Email Id</label>
                    {{ form.email_id }}
                    {% if form.email_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email_id.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.interviewed_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Interviewed By</label>
                    {{ form.interviewed_by }}
                    {% if form.interviewed_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.interviewed_by.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.authorized_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Authorized By</label>
                    {{ form.authorized_by }}
                    {% if form.authorized_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.authorized_by.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.reference_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Reference By</label>
                    {{ form.reference_by }}
                    {% if form.reference_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reference_by.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.gross_salary.id_for_label }}" class="block text-sm font-medium text-gray-700">Gross Salary</label>
                    {{ form.gross_salary }}
                    {% if form.gross_salary.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gross_salary.errors }}</p>{% endif %}
                </div>
            </div>

            <div class="mb-6 border p-4 rounded-md">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Salary Components</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
                     id="salary-calculation-results"
                     hx-trigger="refreshSalaryCalculation from:body"
                     hx-get="{% url 'calculate_salary_partial' offer_letter.pk %}"
                     hx-swap="innerHTML">
                    <!-- Initial rendering of salary components -->
                    {% include 'hr_offer/_salary_calculation_results.html' with salary_components=salary_components offer_letter=offer_letter %}
                </div>
            </div>

            <div class="mb-6">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>

            <div class="flex justify-end space-x-4 mt-6">
                <button type="button" class="redbox"
                    onclick="document.getElementById('form-action').value='calculate';
                             htmx.trigger(this.form, 'submit');"
                    hx-post="{% url 'calculate_salary_partial' offer_letter.pk %}"
                    hx-target="#salary-calculation-results"
                    hx-swap="innerHTML">
                    Calculate
                </button>
                <button type="submit" class="redbox" 
                        onclick="document.getElementById('form-action').value='update';">
                    Update
                </button>
                {% if is_increment_view %}
                <button type="submit" class="redbox"
                        onclick="document.getElementById('form-action').value='increment';">
                    Increment
                </button>
                {% endif %}
                <a href="{% url 'offerletter_list' %}" class="redbox">Cancel</a>
            </div>
            <div id="form-indicator" class="htmx-indicator">Loading...</div>
        </form>
    </div>

    <div class="mt-8 bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Offer Accessories</h3>
        <div id="offer-accessories-table-container"
             hx-trigger="load, refreshAccessoryTable from:body"
             hx-get="{% url 'offeraccessory_table_partial' offer_letter.pk %}"
             hx-swap="innerHTML">
            <!-- Accessories table will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Accessories...</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal for form -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize DataTables if a table is swapped in
        if (evt.detail.target.id === 'offer-accessories-table-container') {
            $('#offerAccessoryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Client-side redirect after successful main form submission
    document.body.addEventListener('offerLetterUpdated', function(evt) {
        // This means the main form submission was successful
        // ASP.NET used Response.Redirect, so we simulate a full page navigation
        // You can make this smarter, e.g., only redirect if not an HTMX form, or use HX-Redirect header
        window.location.href = "{% url 'offerletter_list' %}?msg=Employee data updated."; // Example redirect
    });
</script>
{% endblock %}
```

**`_salary_calculation_results.html` (Partial for Salary Calculation)**

```html
<table class="w-full text-sm">
    <thead>
        <tr>
            <th class="py-1 px-2 text-left font-bold style4">Description</th>
            <th class="py-1 px-2 text-left font-bold">%</th>
            <th class="py-1 px-2 text-left font-bold">Per Month</th>
            <th class="py-1 px-2 text-left font-bold" colspan="2">Annual</th>
            <th class="py-1 px-2 text-left font-bold" colspan="2"></th>
            <th class="py-1 px-2 text-left font-bold">Per Month</th>
            <th class="py-1 px-2 text-left font-bold">Annual</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="style5">Gross Salary</td>
            <td>100</td>
            <td><label>{{ salary_components.gross_salary_monthly|floatformat:2 }}</label></td>
            <td colspan="2"><label>{{ salary_components.gross_salary_annual|floatformat:2 }}</label></td>
            <td colspan="2">LTA</td>
            <td><label>{{ offer_letter.lta|floatformat:2 }}</label></td>
            <td>Take Home</td>
            <td><label>{{ salary_components.take_home_monthly|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.take_home_annual|floatformat:2 }}</label></td>
        </tr>
        <tr>
            <td class="style5">Basic</td>
            <td>30</td>
            <td><label>{{ salary_components.basic_monthly|floatformat:2 }}</label></td>
            <td colspan="2"><label>{{ salary_components.basic_annual|floatformat:2 }}</label></td>
            <td colspan="2">Ex Gratia</td>
            <td><label>{{ offer_letter.ex_gratia|floatformat:2 }}</label></td>
            <td>Take Home (With Attd. Bonus - 1)*</td>
            <td><label>{{ salary_components.take_home_att1_monthly|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.take_home_att1_annual|floatformat:2 }}</label></td>
        </tr>
        <tr>
            <td class="style5">DA</td>
            <td>20</td>
            <td><label>{{ salary_components.da_monthly|floatformat:2 }}</label></td>
            <td colspan="2"><label>{{ salary_components.da_annual|floatformat:2 }}</label></td>
            <td colspan="2">Loyalty Benefits</td>
            <td><label>{{ offer_letter.loyalty|floatformat:2 }}</label></td>
            <td>Take Home (With Attd. Bonus - 2)*</td>
            <td><label>{{ salary_components.take_home_att2_monthly|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.take_home_att2_annual|floatformat:2 }}</label></td>
        </tr>
        <tr>
            <td class="style5">HRA</td>
            <td>20</td>
            <td><label>{{ salary_components.hra_monthly|floatformat:2 }}</label></td>
            <td colspan="2"><label>{{ salary_components.hra_annual|floatformat:2 }}</label></td>
            <td colspan="2">Vehicle Allowance</td>
            <td><label>{{ offer_letter.vehicle_allowance|floatformat:2 }}</label></td>
            <td>CTC</td>
            <td><label>{{ salary_components.ctc_monthly|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.ctc_annual|floatformat:2 }}</label></td>
        </tr>
        <tr>
            <td class="style5">Convenience</td>
            <td>20</td>
            <td><label>{{ salary_components.convenience_monthly|floatformat:2 }}</label></td>
            <td colspan="2"><label>{{ salary_components.convenience_annual|floatformat:2 }}</label></td>
            <td colspan="2">Paid Leaves</td>
            <td><label>{{ offer_letter.paid_leaves|floatformat:2 }}</label></td>
            <td>CTC (With Attd. Bonus - 1)*</td>
            <td><label>{{ salary_components.ctc_att1_monthly|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.ctc_att1_annual|floatformat:2 }}</label></td>
        </tr>
        <tr>
            <td class="style5">Education</td>
            <td>5</td>
            <td><label>{{ salary_components.education_monthly|floatformat:2 }}</label></td>
            <td colspan="2"><label>{{ salary_components.education_annual|floatformat:2 }}</label></td>
            <td colspan="2">PF-Employee (%)</td>
            <td><label>{{ salary_components.pf_employee_monthly|floatformat:2 }}</label></td>
            <td>CTC (With Attd. Bonus - 2)*</td>
            <td><label>{{ salary_components.ctc_att2_monthly|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.ctc_att2_annual|floatformat:2 }}</label></td>
        </tr>
        <tr>
            <td class="style5">Medical</td>
            <td>5</td>
            <td><label>{{ salary_components.medical_monthly|floatformat:2 }}</label></td>
            <td colspan="2"><label>{{ salary_components.medical_annual|floatformat:2 }}</label></td>
            <td colspan="2">PF-Company (%)</td>
            <td><label>{{ salary_components.pf_company_monthly|floatformat:2 }}</label></td>
            <!-- Empty cells as per original design for GridView -->
            <td colspan="3" rowspan="5" width="590px"></td>
        </tr>
        <tr>
            <td class="style5">Attend Bonus - 1</td>
            <td></td>
            <td><label>{{ salary_components.att_bonus1_monthly|floatformat:2 }}</label></td>
            <td colspan="2"></td>
            <td colspan="2">P. Tax</td>
            <td><label>{{ salary_components.p_tax_monthly|floatformat:2 }}</label></td>
        </tr>
        <tr>
            <td class="style5">Attend Bonus - 2</td>
            <td></td>
            <td><label>{{ salary_components.att_bonus2_monthly|floatformat:2 }}</label></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style5">Bonus</td>
            <td colspan="2"></td>
            <td><label>{{ offer_letter.bonus|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.bonus_annual|floatformat:2 }}</label></td>
            <td colspan="2"></td>
        </tr>
        <tr>
            <td class="style5">Gratuity</td>
            <td colspan="2"></td>
            <td><label>{{ salary_components.gratuity_monthly|floatformat:2 }}</label></td>
            <td><label>{{ salary_components.gratuity_annual|floatformat:2 }}</label></td>
            <td colspan="2"></td>
        </tr>
    </tbody>
</table>
```

**`_offeraccessory_table.html` (Partial for Accessories DataTables)**

```html
<table id="offerAccessoryTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Include In</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for accessory in offer_letter.offer_accessories.all %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ accessory.includes_in }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ accessory.particulars }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ accessory.qty|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ accessory.amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ accessory.total|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'offeraccessory_edit_htmx' offer_letter.pk accessory.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'offeraccessory_delete_htmx' offer_letter.pk accessory.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-2 px-4 text-center border-b border-gray-200">No accessories added yet.</td>
        </tr>
        {% endfor %}
    </tbody>
    <tfoot>
        <tr>
            <td colspan="2">
                <button class="redbox w-full"
                    hx-get="{% url 'offeraccessory_add_htmx' offer_letter.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add Accessory
                </button>
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tfoot>
</table>

<!-- DataTables initialization will be handled by JavaScript in the main template's afterSwap event -->
```

**`_offeraccessory_form.html` (Partial for Accessory CRUD Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {% if form.instance.pk %}Edit{% else %}Add{% endif %} Offer Accessory
    </h3>
    <form hx-post="{% if form.instance.pk %}{% url 'offeraccessory_edit_htmx' offer_id=offer_id accessory_pk=form.instance.pk %}{% else %}{% url 'offeraccessory_add_htmx' offer_id=offer_id %}{% endif %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_confirm_delete_accessory.html` (Partial for Accessory Delete Confirmation Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this accessory "{{ object.particulars }}"?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-delete="{% url 'offeraccessory_delete_htmx' offer_id=offer_id accessory_pk=object.pk %}"
            hx-target="body"
            hx-swap="none"
            _="on click remove .is-active from #modal"
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`hr_offer/urls.py`)

This defines the URL patterns for our `hr_offer` application.

```python
from django.urls import path
from .views import (
    OfferLetterDetailView,
    OfferAccessoryTablePartialView,
    OfferAccessoryCreateHTMXView,
    OfferAccessoryUpdateHTMXView,
    OfferAccessoryDeleteHTMXView,
    CalculateSalaryHTMXView,
)

urlpatterns = [
    # Main Offer Letter Edit/Increment View
    path('offer-letter/<str:pk>/edit/', OfferLetterDetailView.as_view(), name='offerletter_edit'),
    
    # HTMX endpoints for Offer Accessories (CRUD for the inline grid)
    path('offer-letter/<str:offer_id>/accessories/table/', OfferAccessoryTablePartialView.as_view(), name='offeraccessory_table_partial'),
    path('offer-letter/<str:offer_id>/accessories/add/', OfferAccessoryCreateHTMXView.as_view(), name='offeraccessory_add_htmx'),
    path('offer-letter/<str:offer_id>/accessories/<int:accessory_pk>/edit/', OfferAccessoryUpdateHTMXView.as_view(), name='offeraccessory_edit_htmx'),
    path('offer-letter/<str:offer_id>/accessories/<int:accessory_pk>/delete/', OfferAccessoryDeleteHTMXView.as_view(), name='offeraccessory_delete_htmx'),

    # HTMX endpoint for dynamic salary calculation
    path('hr-offer/calculate-salary/<str:pk>/', CalculateSalaryHTMXView.as_view(), name='calculate_salary_partial'),
    
    # Placeholder for a list view (not implemented in source but implied by redirect)
    path('offer-letters/', OfferLetterDetailView.as_view(), name='offerletter_list'), # Redirect target, adjust as needed.
]
```

#### 4.6 Tests (`hr_offer/tests.py`)

Comprehensive tests for models and views ensure reliability and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from decimal import Decimal
from datetime import date, time
import random

# Import all models needed for testing
from .models import (
    OfferLetter, OfferAccessory, IncrementLetter, IncrementAccessory,
    Designation, DutyHour, OTHour, OverTimeType, EmployeeType, IncludesIn,
    OfficeStaff, PF_Slab, Company
)

class OfferLetterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required lookup data
        cls.designation = Designation.objects.create(id=1, designation='Software Engineer', symbol='SE')
        cls.duty_hour = DutyHour.objects.create(id=1, hours='9-5')
        cls.ot_hour = OTHour.objects.create(id=1, hours='No OT')
        cls.overtime_type = OverTimeType.objects.create(id=1, description='Not Applicable')
        cls.employee_type_sapl = EmployeeType.objects.create(id=1, description='SAPL')
        cls.employee_type_neha = EmployeeType.objects.create(id=2, description='NEHA')
        cls.employee_type_casual = EmployeeType.objects.create(id=5, description='Casuals') # Based on C# logic
        cls.includes_in_ctc = IncludesIn.objects.create(id=1, includes_in='CTC')
        cls.includes_in_take_home = IncludesIn.objects.create(id=2, includes_in='Take Home')
        cls.includes_in_both = IncludesIn.objects.create(id=3, includes_in='Both')
        cls.interviewer = OfficeStaff.objects.create(emp_id='EMP001', title='Mr.', employee_name='John Doe')
        cls.authorizer = OfficeStaff.objects.create(emp_id='EMP002', title='Ms.', employee_name='Jane Smith')
        cls.pf_slab = PF_Slab.objects.create(id=1, pf_employee_per=12.00, pf_company_per=12.00, active=True)
        cls.company = Company.objects.create(comp_id=1, mail_server_ip='localhost', erp_sys_mail='<EMAIL>') # Assuming Compnay model exists

        # Create a sample OfferLetter instance
        cls.offer_letter = OfferLetter.objects.create(
            offer_id='OFF001',
            comp_id=1,
            fin_year_id=1,
            title='Mr.',
            employee_name='Test Employee',
            staff_type=cls.employee_type_sapl,
            type_of=1, # SAPL
            gross_salary=Decimal('50000.00'),
            duty_hrs=cls.duty_hour,
            ot_hrs=cls.ot_hour,
            overtime_applicable=cls.overtime_type,
            address='123 Test St',
            contact_no='1234567890',
            email_id='<EMAIL>',
            interviewed_by=cls.interviewer,
            authorized_by=cls.authorizer,
            reference_by='Self',
            designation=cls.designation,
            ex_gratia=Decimal('1000.00'),
            vehicle_allowance=Decimal('500.00'),
            lta=Decimal('750.00'),
            loyalty=Decimal('200.00'),
            paid_leaves=Decimal('15.00'),
            bonus=Decimal('1000.00'),
            att_bonus_per1=Decimal('10.00'),
            att_bonus_per2=Decimal('20.00'),
            pf_employee_per=Decimal('12.00'),
            pf_company_per=Decimal('12.00'),
            header_text='Welcome to the company.',
            footer_text='Sign below.',
            remarks='Initial offer.',
            increment=0,
            sys_date=date.today(),
            sys_time=time(10, 0, 0)
        )
        # Add some accessories
        OfferAccessory.objects.create(
            id=1,
            offer_master=cls.offer_letter,
            particulars='Laptop',
            qty=Decimal('1.00'),
            amount=Decimal('50000.00'),
            includes_in=cls.includes_in_ctc # CTC
        )
        OfferAccessory.objects.create(
            id=2,
            offer_master=cls.offer_letter,
            particulars='Mobile Allowance',
            qty=Decimal('1.00'),
            amount=Decimal('1000.00'),
            includes_in=cls.includes_in_take_home # Take Home
        )
        OfferAccessory.objects.create(
            id=3,
            offer_master=cls.offer_letter,
            particulars='Internet Allowance',
            qty=Decimal('1.00'),
            amount=Decimal('500.00'),
            includes_in=cls.includes_in_both # Both
        )

    def test_offerletter_creation(self):
        self.assertEqual(self.offer_letter.employee_name, 'Test Employee')
        self.assertEqual(self.offer_letter.gross_salary, Decimal('50000.00'))
        self.assertIsNotNone(self.offer_letter.pk)
        self.assertEqual(self.offer_letter.offer_accessories.count(), 3)

    def test_offeraccessory_total_property(self):
        accessory = OfferAccessory.objects.get(id=1)
        self.assertEqual(accessory.total, Decimal('50000.00'))

    def test_calculate_salary_components_sapl(self):
        # Test basic SAPL calculation
        components = self.offer_letter.calculate_salary_components()
        self.assertIn('gross_salary_monthly', components)
        self.assertEqual(components['gross_salary_monthly'], Decimal('50000.00'))
        # Add more specific assertions based on the exact calculations
        # Example: Basic (30% of Gross)
        self.assertEqual(components['basic_monthly'], Decimal('15000.00'))
        # Example: PF Employee (12% of Gross)
        self.assertEqual(components['pf_employee_monthly'], Decimal('6000.00'))

        # Test total CTC/Take Home with accessories
        # Accessories: CTC (50000), Take Home (1000), Both (500)
        # Assuming minimal other deductions/additions for this test
        self.assertAlmostEqual(components['ctc_monthly'],
            round(self.offer_letter.gross_salary + self.offer_letter.bonus + self.offer_letter.loyalty +
                  self.offer_letter.lta + self.offer_letter.gratuity_monthly +
                  components['pf_company_monthly'] + self.offer_letter.ex_gratia +
                  self.offer_letter.vehicle_allowance + Decimal('50000.00') + Decimal('500.00'), 2)
            , places=2)
        
        # Test for casuals (staff_type.id=5, no PF, AttBonus, Bonus)
        casual_offer = OfferLetter.objects.create(
            offer_id='OFF002',
            comp_id=1,
            fin_year_id=1,
            title='Mr.',
            employee_name='Casual Worker',
            staff_type=self.employee_type_casual,
            type_of=1, # SAPL
            gross_salary=Decimal('10000.00'),
            duty_hrs=self.duty_hour,
            ot_hrs=self.ot_hour,
            overtime_applicable=self.overtime_type,
            address='123 Test St',
            contact_no='1234567890',
            email_id='<EMAIL>',
            interviewed_by=self.interviewer,
            authorized_by=self.authorizer,
            designation=self.designation,
            ex_gratia=Decimal('0.00'),
            vehicle_allowance=Decimal('0.00'),
            lta=Decimal('0.00'),
            loyalty=Decimal('0.00'),
            paid_leaves=Decimal('0.00'),
            bonus=Decimal('0.00'),
            att_bonus_per1=Decimal('0.00'),
            att_bonus_per2=Decimal('0.00'),
            pf_employee_per=Decimal('0.00'),
            pf_company_per=Decimal('0.00'),
            sys_date=date.today(),
            sys_time=time(10, 0, 0)
        )
        casual_components = casual_offer.calculate_salary_components()
        self.assertEqual(casual_components['pf_employee_monthly'], Decimal('0.00'))
        self.assertEqual(casual_components['pf_company_monthly'], Decimal('0.00'))
        self.assertEqual(casual_components['att_bonus1_monthly'], Decimal('0.00'))
        self.assertEqual(casual_components['att_bonus2_monthly'], Decimal('0.00'))
        self.assertEqual(casual_components['bonus_annual'], Decimal('0.00')) # Bonus should be 0 if 'Casuals'

    def test_increment_letter_creation(self):
        # Create a new offer letter for increment test
        new_offer = OfferLetter.objects.create(
            offer_id='OFF003',
            comp_id=1, fin_year_id=1, title='Mr.', employee_name='Inc Test',
            staff_type=self.employee_type_sapl, type_of=1, gross_salary=Decimal('40000.00'),
            duty_hrs=self.duty_hour, ot_hrs=self.ot_hour, overtime_applicable=self.overtime_type,
            address='Address', contact_no='123', email_id='<EMAIL>',
            interviewed_by=self.interviewer, authorized_by=self.authorizer, designation=self.designation,
            increment=0, sys_date=date.today(), sys_time=time(10,0,0)
        )
        OfferAccessory.objects.create(
            id=4, offer_master=new_offer, particulars='Test Acc', qty=Decimal('1'), amount=Decimal('100'), includes_in=self.includes_in_ctc
        )

        # Simulate increment operation (this happens in the view, but test model effect)
        old_increment_count = new_offer.increment
        new_offer.increment += 1
        new_offer.gross_salary = Decimal('45000.00') # Simulate salary increase
        new_offer.increment_for_the_year = '2023-2024'
        new_offer.effect_from = date(2023, 1, 1)
        new_offer.save()

        # This part of the test would be in the view's test to call IncrementLetter.objects.create
        # For model test, we'll manually create the increment record based on the 'old' offer.
        IncrementLetter.objects.create(
            offer_id=new_offer.offer_id,
            sys_date=date.today(), sys_time=time(10, 0, 0),
            session_id='TEST', comp_id=1, fin_year_id=1,
            title=new_offer.title, employee_name=new_offer.employee_name, staff_type=new_offer.staff_type,
            type_of=new_offer.type_of, gross_salary=Decimal('40000.00'), # Old salary
            duty_hrs=new_offer.duty_hrs, ot_hrs=new_offer.ot_hrs, overtime_applicable=new_offer.overtime_applicable,
            address=new_offer.address, contact_no=new_offer.contact_no, email_id=new_offer.email_id,
            interviewed_by=new_offer.interviewed_by, authorized_by=new_offer.authorized_by,
            reference_by=new_offer.reference_by, designation=new_offer.designation,
            ex_gratia=new_offer.ex_gratia, vehicle_allowance=new_offer.vehicle_allowance,
            lta=new_offer.lta, loyalty=new_offer.loyalty, paid_leaves=new_offer.paid_leaves,
            bonus=new_offer.bonus, att_bonus_per1=new_offer.att_bonus_per1,
            att_bonus_per2=new_offer.att_bonus_per2, pf_employee_per=new_offer.pf_employee_per,
            pf_company_per=new_offer.pf_company_per, header_text=new_offer.header_text,
            footer_text=new_offer.footer_text, remarks=new_offer.remarks,
            increment=old_increment_count, # Old increment count
            increment_for_the_year=new_offer.increment_for_the_year,
            effect_from=new_offer.effect_from
        )
        
        self.assertEqual(IncrementLetter.objects.filter(offer_id='OFF003').count(), 1)
        inc_letter = IncrementLetter.objects.get(offer_id='OFF003')
        self.assertEqual(inc_letter.gross_salary, Decimal('40000.00')) # Check old salary preserved
        self.assertEqual(new_offer.increment, 1) # Check new offer has incremented

class OfferLetterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required lookup data
        cls.designation = Designation.objects.create(id=1, designation='Software Engineer', symbol='SE')
        cls.duty_hour = DutyHour.objects.create(id=1, hours='9-5')
        cls.ot_hour = OTHour.objects.create(id=1, hours='No OT')
        cls.overtime_type = OverTimeType.objects.create(id=1, description='Not Applicable')
        cls.employee_type_sapl = EmployeeType.objects.create(id=1, description='SAPL')
        cls.includes_in_ctc = IncludesIn.objects.create(id=1, includes_in='CTC')
        cls.interviewer = OfficeStaff.objects.create(emp_id='EMP001', title='Mr.', employee_name='John Doe')
        cls.authorizer = OfficeStaff.objects.create(emp_id='EMP002', title='Ms.', employee_name='Jane Smith')
        cls.pf_slab = PF_Slab.objects.create(id=1, pf_employee_per=12.00, pf_company_per=12.00, active=True)
        cls.company = Company.objects.create(comp_id=1, mail_server_ip='localhost', erp_sys_mail='<EMAIL>') # Assuming Compnay model exists

        # Create a sample OfferLetter instance
        cls.offer_letter = OfferLetter.objects.create(
            offer_id='OFF001',
            comp_id=1,
            fin_year_id=1,
            title='Mr.',
            employee_name='Test Employee',
            staff_type=cls.employee_type_sapl,
            type_of=1,
            gross_salary=Decimal('50000.00'),
            duty_hrs=cls.duty_hour,
            ot_hrs=cls.ot_hour,
            overtime_applicable=cls.overtime_type,
            address='123 Test St',
            contact_no='1234567890',
            email_id='<EMAIL>',
            interviewed_by=cls.interviewer,
            authorized_by=cls.authorizer,
            reference_by='Self',
            designation=cls.designation,
            ex_gratia=Decimal('1000.00'),
            vehicle_allowance=Decimal('500.00'),
            lta=Decimal('750.00'),
            loyalty=Decimal('200.00'),
            paid_leaves=Decimal('15.00'),
            bonus=Decimal('1000.00'),
            att_bonus_per1=Decimal('10.00'),
            att_bonus_per2=Decimal('20.00'),
            pf_employee_per=Decimal('12.00'),
            pf_company_per=Decimal('12.00'),
            header_text='Welcome to the company.',
            footer_text='Sign below.',
            remarks='Initial offer.',
            increment=0,
            sys_date=date.today(),
            sys_time=time(10, 0, 0)
        )
        # Add some accessories
        OfferAccessory.objects.create(
            id=1,
            offer_master=cls.offer_letter,
            particulars='Laptop',
            qty=Decimal('1.00'),
            amount=Decimal('50000.00'),
            includes_in=cls.includes_in_ctc
        )

    def setUp(self):
        self.client = Client()
        # Mock user authentication if necessary
        # self.client.force_login(User.objects.create_user(username='testuser'))

    def test_offerletter_detail_view_get(self):
        response = self.client.get(reverse('offerletter_edit', kwargs={'pk': self.offer_letter.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/offerletter_detail.html')
        self.assertContains(response, 'Test Employee')
        self.assertIsInstance(response.context['form'], OfferLetterForm)
        self.assertFalse(response.context['is_increment_view'])

    def test_offerletter_detail_view_get_increment_mode(self):
        response = self.client.get(reverse('offerletter_edit', kwargs={'pk': self.offer_letter.pk}) + '?OI=1')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/offerletter_detail.html')
        self.assertTrue(response.context['is_increment_view'])
        self.assertContains(response, 'Increment Letter') # Check label text

    def test_offerletter_update_post(self):
        data = {
            'action': 'update',
            'title': 'Ms.',
            'employee_name': 'Updated Name',
            'designation': self.designation.pk,
            'duty_hrs': self.duty_hour.pk,
            'ot_hrs': self.ot_hour.pk,
            'overtime_applicable': self.overtime_type.pk,
            'contact_no': '9876543210',
            'address': 'New Address',
            'email_id': '<EMAIL>',
            'staff_type': self.employee_type_sapl.pk,
            'type_of': '1',
            'interviewed_by': self.interviewer.pk,
            'authorized_by': self.authorizer.pk,
            'reference_by': 'New Ref',
            'gross_salary': '60000.00',
            'header_text': 'Updated Header',
            'footer_text': 'Updated Footer',
            'remarks': 'Updated remarks.',
            'ex_gratia': '1200.00', 'vehicle_allowance': '600.00', 'lta': '800.00',
            'loyalty': '250.00', 'paid_leaves': '16.00', 'bonus': '1200.00',
            'att_bonus_per1': '11.00', 'att_bonus_per2': '21.00',
            'pf_employee_per': '12.00', 'pf_company_per': '12.00',
            'increment_for_the_year': '', 'effect_from': '' # Not required for simple update
        }
        response = self.client.post(reverse('offerletter_edit', kwargs={'pk': self.offer_letter.pk}), data)
        self.offer_letter.refresh_from_db()
        self.assertEqual(self.offer_letter.employee_name, 'Updated Name')
        self.assertEqual(self.offer_letter.gross_salary, Decimal('60000.00'))
        self.assertEqual(response.status_code, 302) # Redirect after successful POST
        messages = list(get_messages(response.wsgi_request))
        self.assertIn('Employee data updated successfully.', [str(m) for m in messages])

    def test_offerletter_increment_post(self):
        initial_increment_count = self.offer_letter.increment
        data = {
            'action': 'increment',
            'title': 'Mr.',
            'employee_name': 'Test Employee', # Keeping original name
            'designation': self.designation.pk,
            'duty_hrs': self.duty_hour.pk,
            'ot_hrs': self.ot_hour.pk,
            'overtime_applicable': self.overtime_type.pk,
            'contact_no': '1234567890',
            'address': '123 Test St',
            'email_id': '<EMAIL>',
            'staff_type': self.employee_type_sapl.pk,
            'type_of': '1',
            'interviewed_by': self.interviewer.pk,
            'authorized_by': self.authorizer.pk,
            'reference_by': 'Self',
            'gross_salary': '55000.00', # New gross salary
            'header_text': 'New Header Text for Increment',
            'footer_text': 'New Footer Text for Increment',
            'remarks': 'Incremented offer.',
            'ex_gratia': '1000.00', 'vehicle_allowance': '500.00', 'lta': '750.00',
            'loyalty': '200.00', 'paid_leaves': '15.00', 'bonus': '1000.00',
            'att_bonus_per1': '10.00', 'att_bonus_per2': '20.00',
            'pf_employee_per': '12.00', 'pf_company_per': '12.00',
            'increment_for_the_year': '2023-2024',
            'effect_from': '2023-01-01'
        }
        response = self.client.post(reverse('offerletter_edit', kwargs={'pk': self.offer_letter.pk}), data)
        self.assertEqual(response.status_code, 302) # Redirect
        self.offer_letter.refresh_from_db()
        self.assertEqual(self.offer_letter.increment, initial_increment_count + 1)
        self.assertEqual(self.offer_letter.gross_salary, Decimal('55000.00')) # Verify updated salary
        self.assertTrue(IncrementLetter.objects.filter(offer_id=self.offer_letter.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertIn('Increment Letter generated and Offer updated successfully.', [str(m) for m in messages])

    def test_offeraccessory_table_partial_view(self):
        response = self.client.get(reverse('offeraccessory_table_partial', kwargs={'offer_id': self.offer_letter.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/_offeraccessory_table.html')
        self.assertContains(response, 'Laptop') # Check if accessory is rendered

    def test_offeraccessory_add_htmx(self):
        data = {
            'particulars': 'New Accessory',
            'qty': '2.00',
            'amount': '250.00',
            'includes_in': self.includes_in_take_home.pk
        }
        response = self.client.post(reverse('offeraccessory_add_htmx', kwargs={'offer_id': self.offer_letter.pk}), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(OfferAccessory.objects.filter(particulars='New Accessory', offer_master=self.offer_letter).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshAccessoryTable', response.headers['HX-Trigger'])

    def test_offeraccessory_update_htmx(self):
        accessory = OfferAccessory.objects.get(particulars='Laptop')
        data = {
            'particulars': 'Updated Laptop',
            'qty': '1.00',
            'amount': '55000.00',
            'includes_in': self.includes_in_ctc.pk
        }
        response = self.client.post(reverse('offeraccessory_edit_htmx', kwargs={'offer_id': self.offer_letter.pk, 'accessory_pk': accessory.pk}), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        accessory.refresh_from_db()
        self.assertEqual(accessory.particulars, 'Updated Laptop')
        self.assertEqual(accessory.amount, Decimal('55000.00'))
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshAccessoryTable', response.headers['HX-Trigger'])

    def test_offeraccessory_delete_htmx(self):
        accessory_to_delete = OfferAccessory.objects.get(particulars='Laptop')
        response = self.client.delete(reverse('offeraccessory_delete_htmx', kwargs={'offer_id': self.offer_letter.pk, 'accessory_pk': accessory_to_delete.pk}), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(OfferAccessory.objects.filter(pk=accessory_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshAccessoryTable', response.headers['HX-Trigger'])

    def test_calculate_salary_htmx_view(self):
        # Simulate a GET request with form data for calculation
        get_data = {
            'gross_salary': '52000.00',
            'type_of': '1',
            'staff_type': self.employee_type_sapl.pk,
            'ex_gratia': '1100.00',
            'att_bonus_per1': '12.00',
            # Add other relevant fields that affect calculation
        }
        response = self.client.get(reverse('calculate_salary_partial', kwargs={'pk': self.offer_letter.pk}), get_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/_salary_calculation_results.html')
        self.assertContains(response, '52,000.00') # Check if new gross salary is reflected
        # Add more specific assertions to verify calculation results if possible, e.g.,
        # self.assertContains(response, f"<td><label>{Decimal('52000.00') * Decimal('0.30'):.2f}</label></td>") # Basic

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Interactions:**
    *   **Main Form Submission:** The `Update` and `Increment` buttons use `hx-post` to submit the form. `hx-swap="none"` prevents direct content swap, and a custom `HX-Trigger` (`offerLetterUpdated`) is sent for client-side redirection after a successful action (mimicking ASP.NET's `Response.Redirect`).
    *   **Salary Calculation:** Input fields (like `gross_salary`, percentages, dropdowns) have `hx-get` to `{% url 'calculate_salary_partial' offer_letter.pk %}` with `hx-trigger="keyup delay:300ms, change"`. This sends the form data to the server, which recalculates and returns the `_salary_calculation_results.html` partial, swapping it into `#salary-calculation-results`.
    *   **Accessories Table:** The `_offeraccessory_table.html` partial is loaded via `hx-get` on `load` and `refreshAccessoryTable` custom event.
    *   **Accessory CRUD Modals:**
        *   `Add Accessory` button has `hx-get` to `offeraccessory_add_htmx` targetting `#modalContent` and activating the modal with `_ = on click add .is-active to #modal`.
        *   `Edit` and `Delete` buttons on each row similarly `hx-get` their respective URLs.
        *   Form submissions within the modal (`_offeraccessory_form.html`, `_confirm_delete_accessory.html`) use `hx-post` or `hx-delete` with `hx-swap="none"`. Upon success, `HX-Trigger` sends `refreshAccessoryTable` and `refreshSalaryCalculation` events to update the main page's table and calculations, and the modal is closed using `_ = on click remove .is-active from #modal`.
    *   **Messages:** Django's `messages` framework is used, and a simple Alpine.js `x-data` component displays them, fading out after 5 seconds.

*   **Alpine.js for UI State Management:**
    *   **Modal Control:** A main `div` with `id="modal"` uses Alpine.js `x-data="{ show: false }"` and `x-show="show"` for its visibility. The `hx-get` calls use `_="on click add .is-active to #modal"` to show the modal (or `remove .is-active` to close it).
    *   **Conditional Field Visibility:** `x-data` and `x-show` directives are used to control the visibility of `header_text`, `footer_text`, and increment-specific fields (`increment_for_the_year`, `effect_from`) based on `is_increment_view` context and `offer_letter.increment` value, mirroring the ASP.NET `Visible` property.

*   **DataTables for List Views:**
    *   The `_offeraccessory_table.html` partial is designed to be a DataTables instance.
    *   The JavaScript to initialize DataTables (`$('#offerAccessoryTable').DataTable(...)`) is placed within a `script` tag inside the partial. This script is triggered whenever the partial is loaded or re-swapped by HTMX. A `htmx:afterSwap` event listener in the main template ensures DataTables is re-initialized correctly after the table partial is loaded.

*   **DRY Template Inheritance:** All templates extend `core/base.html` (as per the instructions, `base.html` code is not provided). This ensures all necessary CDN links for HTMX, Alpine.js, and DataTables are loaded once.

---

## Final Notes

*   **Placeholder for `fun.Connection()`:** The ASP.NET `clsFunctions` class contained database connection logic and various helper methods. In Django, this is handled by the ORM via `settings.DATABASES`. The `get_session_data` helper in `views.py` is a placeholder for retrieving `CompId`, `FinYearId`, and `SessionId` which would typically come from an authenticated user's profile or a centralized configuration in a real ERP system.
*   **Employee Auto-completion:** The `AutoCompleteExtender` was simplified to a standard `forms.Select` for `interviewed_by` and `authorized_by`. For true auto-completion, a more advanced solution like `django-autocomplete-light` integrated with HTMX (e.g., a custom HTMX endpoint that returns filtered options) would be required.
*   **Email Configuration:** The `_send_offer_email` function assumes `Company` model and `settings.DEFAULT_FROM_EMAIL`. Ensure your Django project's `settings.py` is configured for email sending.
*   **Monetary Precision:** Django's `DecimalField` is used for all monetary values, ensuring precise calculations as needed in financial applications. The `quantize` method is used for rounding.
*   **Robustness:** For a production-ready application, error handling, logging, and security best practices (e.g., proper authentication, authorization, input sanitization beyond basic form validation) would need to be thoroughly implemented.
*   **Database Migrations:** Remember to run `python manage.py makemigrations` and `python manage.py migrate` after setting up your `settings.py` to point to the existing SQL Server database (using `managed = False` and `db_table` means Django won't create tables but will map to them).