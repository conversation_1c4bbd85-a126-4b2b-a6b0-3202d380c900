The transition from legacy ASP.NET applications to modern Django-based solutions using AI-assisted automation is a strategic move that delivers significant business value. By leveraging Django 5.0+, HTMX, and Alpine.js, we create highly interactive, performant, and maintainable web applications. This approach minimizes manual coding, reduces development time, and ensures a robust, scalable architecture.

For the `MobileBills_Edit.aspx` module, the core functionality involves displaying and editing mobile bill records for employees based on a selected month. This includes data aggregation from multiple tables, dynamic UI updates (toggling edit fields), and batch updates. Our Django modernization plan focuses on transforming these features into a clean, modular, and testable structure.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following database tables and their implied structure:

-   **`tblHR_MobileBill`**: Stores individual mobile bill entries.
    -   `Id` (Primary Key, INT)
    -   `CompId` (INT, Company ID)
    -   `FinYearId` (INT, Financial Year ID)
    -   `SessionId` (VARCHAR, User Session ID)
    -   `EmpId` (VARCHAR, Employee ID, foreign key to `tblHR_OfficeStaff`)
    -   `BillMonth` (INT, Month of the bill)
    -   `BillAmt` (DECIMAL, Bill Amount)
    -   `Taxes` (INT, Foreign Key to `tblExciseser_Master.Id`)
    -   `SysDate` (DATE, System Date of record)
    -   `SysTime` (TIME, System Time of record)

-   **`tblHR_OfficeStaff`**: Contains employee master data.
    -   `UserID` (Primary Key, INT)
    -   `EmpId` (VARCHAR, Employee ID, used as unique identifier for joins)
    -   `EmployeeName` (VARCHAR)
    -   `MobileNo` (INT, Foreign Key to `tblHR_CoporateMobileNo.Id`)

-   **`tblHR_CoporateMobileNo`**: Stores corporate mobile number details and limits.
    -   `Id` (Primary Key, INT)
    -   `MobileNo` (VARCHAR, The actual mobile number)
    -   `LimitAmt` (DECIMAL, Limit amount for the mobile number)

-   **`tblExciseser_Master`**: Stores tax master data.
    -   `Id` (Primary Key, INT)
    -   `Value` (DECIMAL, Tax percentage/value)

### Step 2: Identify Backend Functionality

The ASP.NET module performs the following core functionalities:

-   **Read (Display List):**
    -   Populates a dropdown with available months.
    -   Loads a grid (GridView) displaying mobile bill details for the selected month, combining employee information, mobile number details, existing bill amounts, and taxes. This involves complex joins and data aggregation.
    -   Calculates "Excess Amount" dynamically based on Bill Amount, Taxes, and Limit Amount.
-   **Update (Batch Edit):**
    -   Allows users to select multiple rows via checkboxes.
    -   When a row's checkbox is checked, it enables input fields for Bill Amount and Taxes for that specific row.
    -   A single "Update" button at the footer of the grid processes all selected rows, updating their `BillAmt` and `Taxes` in `tblHR_MobileBill`.
-   **Validation:**
    -   Numeric validation for `BillAmt`.
    -   Required field validation for `BillAmt`.

### Step 3: Infer UI Components

The ASP.NET UI components translate to Django as follows:

-   **Month Filter (`asp:DropDownList`):** A standard HTML `<select>` element. Its `AutoPostBack` and `onselectedindexchanged` behavior will be replaced by HTMX `hx-get` to dynamically reload the main data table.
-   **Data Grid (`asp:GridView`):** This will be an HTML `<table>` managed by DataTables for client-side interactivity (sorting, searching, pagination). HTMX will be used to load this table content and update individual rows.
-   **Inline Edit/Display Toggle (`asp:TextBox` vs. `asp:Label`, `asp:CheckBox`):** This dynamic visibility will be handled by Alpine.js `x-show` directives within each table row, triggered by HTMX requests or local state changes.
-   **Validation (`asp:RegularExpressionValidator`, `asp:RequiredFieldValidator`):** Django forms will handle server-side validation. Client-side validation can be integrated with Alpine.js or native HTML5 validation attributes, but the primary validation occurs on the server.
-   **Update Button (`asp:Button`):** An HTML `<button>` that, when clicked, submits a form containing all editable rows. This will likely involve a Django formset submitted via HTMX.
-   **Message Display (`asp:Label`):** Django's messages framework, rendered in `base.html` and triggered by HTMX responses.

---

### Step 4: Generate Django Code

We'll organize the Django application in a module named `hr_mobile_bills`.

#### 4.1 Models (`hr_mobile_bills/models.py`)

We map the identified tables to Django models with `managed = False` to connect to the existing database schema. We also add a custom manager and methods for business logic, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import F # For F expressions in queries
from decimal import Decimal

# Helper for session/user specific context (e.g., CompId, FinYearId).
# In a real app, this would likely come from request.user or a user profile.
# For simplicity in models, we'll assume these are passed or retrieved from a global context (e.g. settings).
# Best practice: pass these as arguments to model/manager methods.

class TaxMaster(models.Model):
    """
    Maps to tblExciseser_Master. Stores tax percentages.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.DecimalField(db_column='Value', max_digits=10, decimal_places=2, default=Decimal('0.00'))

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Tax Master'
        verbose_name_plural = 'Tax Masters'

    def __str__(self):
        return f"{self.value}%"

class CorporateMobileNo(models.Model):
    """
    Maps to tblHR_CoporateMobileNo. Stores corporate mobile numbers and their limits.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=50)
    limit_amt = models.DecimalField(db_column='LimitAmt', max_digits=18, decimal_places=2, default=Decimal('0.00'))

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        return self.mobile_no

class OfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff. Stores employee details.
    """
    user_id = models.IntegerField(db_column='UserID', primary_key=True)
    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    mobile_no_fk = models.ForeignKey(
        CorporateMobileNo,
        on_delete=models.DO_NOTHING, # Or models.SET_NULL, depending on DB schema
        db_column='MobileNo',
        related_name='office_staff_mobile',
        null=True, blank=True
    )

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class MobileBillQuerySet(models.QuerySet):
    """
    Custom QuerySet for MobileBill model to encapsulate data retrieval logic
    that mimics the complex joins and data preparation of the ASP.NET DDLMonth.
    """
    def get_aggregated_bill_data(self, comp_id, fin_year_id, bill_month):
        """
        Retrieves aggregated mobile bill data for a specific month,
        joining across multiple tables and calculating derived fields.
        """
        # Get all relevant staff members with their corporate mobile details
        # Filter for MobileNo != 1 as seen in ASP.NET code
        staff_base_query = OfficeStaff.objects.filter(
            mobile_no_fk__isnull=False,  # Exclude staff without a corporate mobile number
            # The ASP.NET query had "tblHR_OfficeStaff.MobileNo!=1". This usually means
            # a dummy/default mobile ID. If MobileNo is an FK, then mobile_no_fk__pk != 1
            # Assuming '1' is the ID of a dummy/non-existent mobile number entry in CorporateMobileNo
            mobile_no_fk__pk__ne=1 # Adjust this filter if '1' is not the specific dummy ID
        ).select_related('mobile_no_fk').values(
            'user_id', 'emp_id', 'employee_name',
            'mobile_no_fk__mobile_no', 'mobile_no_fk__limit_amt', 'mobile_no_fk__id' # Include fk ID for mobile_no
        ).order_by('employee_name') # Order for consistent display

        # Get existing mobile bills for the selected month and company/finyear,
        # mapped by emp_id for efficient lookup.
        existing_bills_dict = self.filter(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            bill_month=bill_month
        ).select_related('taxes_fk').in_bulk(field_name='emp_id')

        results = []
        for staff in staff_base_query:
            emp_id = staff['emp_id']
            mobile_bill_instance = existing_bills_dict.get(emp_id)

            bill_amt = mobile_bill_instance.bill_amt if mobile_bill_instance else None
            taxes_value = mobile_bill_instance.taxes_fk.value if mobile_bill_instance and mobile_bill_instance.taxes_fk else None
            taxes_id = mobile_bill_instance.taxes_fk.id if mobile_bill_instance and mobile_bill_instance.taxes_fk else None
            
            limit_amt = staff['mobile_no_fk__limit_amt']
            excess_amount = Decimal('0.00')

            # Calculate excess amount using the MobileBill model method
            if mobile_bill_instance:
                excess_amount = mobile_bill_instance.calculate_excess_amount(limit_amt)
            
            # Prepare a dictionary representing a single row in the DataTables view
            results.append({
                'user_id': staff['user_id'], # Used for GridView DataKeyNames
                'emp_id': emp_id,
                'employee_name': staff['employee_name'],
                'mobile_no': staff['mobile_no_fk__mobile_no'],
                'limit_amt': limit_amt,
                'bill_amt': bill_amt,
                'taxes_value': taxes_value,
                'taxes_id': taxes_id,
                'excess_amount': excess_amount,
                'has_existing_bill': mobile_bill_instance is not None, # Flag if a bill record exists for this month/employee
                'mobile_bill_db_id': mobile_bill_instance.id if mobile_bill_instance else None # The actual ID from tblHR_MobileBill
            })
        return results

class MobileBill(models.Model):
    """
    Maps to tblHR_MobileBill. Stores actual mobile bill records.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    emp_id = models.CharField(db_column='EmpId', max_length=50)
    bill_month = models.IntegerField(db_column='BillMonth')
    bill_amt = models.DecimalField(db_column='BillAmt', max_digits=18, decimal_places=2, default=Decimal('0.00'))
    taxes_fk = models.ForeignKey(
        TaxMaster,
        on_delete=models.DO_NOTHING, # Or models.SET_NULL, depending on DB schema
        db_column='Taxes',
        related_name='mobile_bills_tax',
        null=True, blank=True
    )
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')

    objects = MobileBillQuerySet.as_manager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblHR_MobileBill'
        verbose_name = 'Mobile Bill'
        verbose_name_plural = 'Mobile Bills'
        # This unique_together ensures that for a given company, financial year,
        # employee, and month, there is only one mobile bill record.
        unique_together = (('comp_id', 'fin_year_id', 'emp_id', 'bill_month'),)

    def __str__(self):
        return f"Bill for EmpId: {self.emp_id} in Month: {self.bill_month} (ID: {self.id})"

    def calculate_excess_amount(self, limit_amt: Decimal) -> Decimal:
        """
        Calculates the excess amount for this mobile bill.
        Business logic from ASP.NET: CalAmt = (((BillAmt) * (100)) / (Taxes + 100))
        ExcessAmt = (CalAmt - LimitAmt)
        """
        if not self.bill_amt or self.bill_amt <= 0:
            return Decimal('0.00')

        tax_rate = self.taxes_fk.value if self.taxes_fk and self.taxes_fk.value is not None else Decimal('0.00')

        # Prevent division by zero if (tax_rate + 100) is zero (highly unlikely for percentages)
        if (tax_rate + 100) == 0:
            return Decimal('0.00')

        cal_amt = (self.bill_amt * Decimal('100.00')) / (tax_rate + Decimal('100.00'))
        excess_amt = cal_amt - limit_amt
        return max(Decimal('0.00'), excess_amt.quantize(Decimal('0.01'))) # Round to 2 decimal places

    @classmethod
    def get_available_months(cls, comp_id, fin_year_id):
        """
        Retrieves a list of available months (1-12) where mobile bills exist,
        or just all months if no specific filtering on existing bills is needed.
        For simplicity, we'll return all 12 months, as the ASP.NET code populated it from a function.
        In a real scenario, this might query distinct months from tblHR_MobileBill.
        """
        # Example of fetching distinct months from DB
        # distinct_months = cls.objects.filter(comp_id=comp_id, fin_year_id=fin_year_id).values_list('bill_month', flat=True).distinct().order_by('bill_month')
        # return [(m, str(m)) for m in distinct_months]
        
        # Simulating fun.GetMonth which likely returns a predefined list or reads from a static table
        return [
            (1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'),
            (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'),
            (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')
        ]

```

#### 4.2 Forms (`hr_mobile_bills/forms.py`)

We'll need a form for the month selection and a formset for batch editing the mobile bill records.

```python
from django import forms
from .models import MobileBill, TaxMaster
from decimal import Decimal

class MonthSelectionForm(forms.Form):
    """
    Form for selecting the bill month.
    """
    bill_month = forms.ChoiceField(
        label="Month Of Bill",
        choices=[], # Populated dynamically in view
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )

    def __init__(self, *args, **kwargs):
        months_choices = kwargs.pop('months_choices', [])
        super().__init__(*args, **kwargs)
        self.fields['bill_month'].choices = months_choices

class MobileBillRowForm(forms.ModelForm):
    """
    Form for a single row in the mobile bill grid, for editing BillAmt and Taxes.
    This will be used in a formset.
    """
    # Override fields to apply styling and validation specific to the UI
    bill_amt = forms.DecimalField(
        label="Bill Amt",
        required=False, # It becomes required when checkbox is checked, handled client-side by Alpine.js
        max_digits=18,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Bill Amount',
            'x-bind:class': "{ 'border-red-500': form.errors.bill_amt }", # Alpine.js for error styling
            'x-show': 'row.isChecked', # Alpine.js to show/hide
        })
    )
    taxes = forms.ModelChoiceField(
        queryset=TaxMaster.objects.all(),
        label="Taxes",
        required=False,
        empty_label="Select Tax",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-show': 'row.isChecked',
        })
    )
    
    # Custom fields for handling data from the aggregated query, not direct model fields
    # These fields are for display and contextual data within the formset, not direct save.
    user_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    emp_id = forms.CharField(widget=forms.HiddenInput(), required=True) # Important for identifying the bill to update
    employee_name = forms.CharField(label="Emp Name", widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm'}), required=False)
    mobile_no = forms.CharField(label="Mobile No", widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm'}), required=False)
    limit_amt = forms.DecimalField(label="Limit Amt", max_digits=18, decimal_places=2, widget=forms.NumberInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm'}), required=False)
    excess_amount = forms.DecimalField(label="Excess Amount", max_digits=18, decimal_places=2, widget=forms.NumberInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm', 'x-show': 'row.isChecked'}), required=False)
    
    # Checkbox for enabling/disabling edit fields, driven by Alpine.js
    is_checked = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500',
            'x-model': 'row.isChecked', # Alpine.js model binding
            'hx-post': 'this.dataset.url', # Trigger post to update local state if needed
            'hx-trigger': 'change',
            'hx-swap': 'none', # No swap, Alpine.js handles UI
        })
    )
    
    # Field to hold the actual MobileBill DB ID, for updates
    mobile_bill_db_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)


    class Meta:
        model = MobileBill
        # These are the *only* fields that directly map to MobileBill model
        fields = ['bill_amt', 'taxes_fk']
        labels = {
            'taxes_fk': 'Taxes',
        }
    
    def __init__(self, *args, **kwargs):
        initial_data = kwargs.pop('initial', {})
        super().__init__(*args, **kwargs)

        # Populate non-model fields with initial data
        self.fields['user_id'].initial = initial_data.get('user_id')
        self.fields['emp_id'].initial = initial_data.get('emp_id')
        self.fields['employee_name'].initial = initial_data.get('employee_name')
        self.fields['mobile_no'].initial = initial_data.get('mobile_no')
        self.fields['limit_amt'].initial = initial_data.get('limit_amt')
        self.fields['excess_amount'].initial = initial_data.get('excess_amount')
        self.fields['is_checked'].initial = initial_data.get('has_existing_bill', False)
        self.fields['mobile_bill_db_id'].initial = initial_data.get('mobile_bill_db_id')

        # Conditional validation based on is_checked
        # This will be handled in the view or formset clean method for batch validation.
        # Here we only set initial states for styling.
        if initial_data.get('has_existing_bill'):
            # If a bill exists, inputs are initially visible and required
            self.fields['bill_amt'].required = True
            self.fields['taxes'].required = True
        else:
            # If no bill exists, checkbox is visible, inputs are hidden and not required by default
            # They become required when checkbox is checked.
            self.fields['is_checked'].widget.attrs['class'] += ' inline-block' # Make sure checkbox is visible
            self.fields['bill_amt'].widget.attrs['x-show'] = 'row.isChecked'
            self.fields['taxes'].widget.attrs['x-show'] = 'row.isChecked'
            self.fields['excess_amount'].widget.attrs['x-show'] = 'row.isChecked'


    def clean(self):
        cleaned_data = super().clean()
        is_checked = cleaned_data.get('is_checked')
        bill_amt = cleaned_data.get('bill_amt')
        taxes = cleaned_data.get('taxes')

        # Apply required validation if checkbox is checked
        if is_checked:
            if not bill_amt:
                self.add_error('bill_amt', 'Bill Amount is required when checked.')
            if not taxes:
                self.add_error('taxes', 'Taxes is required when checked.')
            
            # Additional regex validation from ASP.NET: ^\d{1,15}(\.\d{0,3})?$
            # DecimalField handles basic numeric validation.
            # max_digits and decimal_places constrain the format, but 3 decimal places
            # might need custom validation if DecimalField(decimal_places=2) is too strict.
            # Assuming 2 decimal places for Currency in Django DecimalField.
            if bill_amt is not None and not isinstance(bill_amt, Decimal):
                 # This check should be redundant if forms.DecimalField works as expected,
                 # but mimics ASP.NET's type check and regex.
                try:
                    Decimal(str(bill_amt)) # Attempt conversion to ensure numeric
                except ValueError:
                    self.add_error('bill_amt', 'Bill Amount must be a valid number.')
        
        return cleaned_data


# Create a formset for batch editing multiple MobileBill instances
MobileBillFormSet = forms.formset_factory(
    MobileBillRowForm,
    extra=0, # No extra blank forms by default
    can_delete=False # Not deleting, only updating/creating
)

```

#### 4.3 Views (`hr_mobile_bills/views.py`)

We need a main view to display the form and the data table. Given the batch update functionality, a `FormView` for the month selection and a `ListView` for the DataTables data, potentially combined, would be suitable. The actual `GridView1_RowCommand` (Update) logic will be handled within the main view's `post` method or a dedicated HTMX endpoint.

```python
from django.views.generic import ListView, FormView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.utils import timezone # For SysDate, SysTime
from decimal import Decimal

from .models import MobileBill, TaxMaster, OfficeStaff
from .forms import MonthSelectionForm, MobileBillFormSet, MobileBillRowForm

# For CompId and FinYearId, assuming these are obtained from the session/user context.
# In a real application, you'd get these from request.session or request.user.profile
# For demonstration, we'll use placeholder values.
def get_user_context(request):
    """Placeholder for retrieving user-specific context like CompId, FinYearId."""
    # This would typically come from user session, profile, or organization settings.
    # Example:
    # comp_id = request.user.profile.company_id
    # fin_year_id = request.user.profile.financial_year_id
    # session_id = request.user.username # Or request.session.session_key
    return {
        'comp_id': 1, # Placeholder
        'fin_year_id': 1, # Placeholder
        'session_id': 'admin_user', # Placeholder
    }

class MobileBillEditView(FormView):
    """
    Main view to display the month selection form and manage the mobile bill list.
    Handles month selection and the batch update of mobile bills.
    """
    template_name = 'hr_mobile_bills/mobilebill_edit_list.html'
    form_class = MonthSelectionForm
    success_url = reverse_lazy('mobile_bills_edit_list')

    def get_initial(self):
        initial = super().get_initial()
        # Pre-select month if passed in URL query string, similar to ASP.NET
        if 'm' in self.request.GET:
            initial['bill_month'] = self.request.GET['m']
        return initial

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        user_context = get_user_context(self.request)
        kwargs['months_choices'] = MobileBill.get_available_months(
            user_context['comp_id'], user_context['fin_year_id']
        )
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get selected month from form or query param, default to current month if not set
        selected_month = None
        if self.request.method == 'GET' and self.request.GET.get('bill_month'):
            selected_month = int(self.request.GET['bill_month'])
        elif self.request.method == 'POST' and 'bill_month' in self.request.POST:
            try:
                selected_month = int(self.request.POST['bill_month'])
            except ValueError:
                pass # Handle invalid month gracefully, maybe default to current or show error
        
        if not selected_month:
            selected_month = timezone.now().month # Default to current month

        context['selected_month'] = selected_month
        
        # Messages from success redirect, similar to ASP.NET Request.QueryString["n"]
        if self.request.GET.get('n'):
            messages.success(self.request, self.request.GET['n'])

        return context

    def post(self, request, *args, **kwargs):
        """
        Handles the batch update of mobile bills when the 'Update' button is pressed.
        """
        month_form = MonthSelectionForm(request.POST, months_choices=MobileBill.get_available_months(1,1)) # Hardcoded for demo
        
        if month_form.is_valid():
            selected_month = int(month_form.cleaned_data['bill_month'])
        else:
            selected_month = timezone.now().month # Fallback

        user_context = get_user_context(request)
        comp_id = user_context['comp_id']
        fin_year_id = user_context['fin_year_id']
        session_id = user_context['session_id']
        
        # Retrieve aggregated data to pass as initial for the formset
        # This data assembly is crucial for the formset to know which rows to process
        initial_data_for_formset = MobileBill.objects.get_aggregated_bill_data(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            bill_month=selected_month
        )

        formset = MobileBillFormSet(request.POST, initial=initial_data_for_formset)
        
        if formset.is_valid():
            updated_count = 0
            for form in formset:
                if form.cleaned_data.get('is_checked') and form.has_changed():
                    emp_id = form.cleaned_data['emp_id']
                    bill_amt = form.cleaned_data['bill_amt']
                    taxes_fk = form.cleaned_data['taxes']
                    mobile_bill_db_id = form.cleaned_data.get('mobile_bill_db_id')

                    try:
                        # Attempt to get existing bill, or create a new one if it doesn't exist
                        # Use the unique_together constraint to identify or create
                        mobile_bill, created = MobileBill.objects.update_or_create(
                            comp_id=comp_id,
                            fin_year_id=fin_year_id,
                            emp_id=emp_id,
                            bill_month=selected_month,
                            defaults={
                                'bill_amt': bill_amt,
                                'taxes_fk': taxes_fk,
                                'session_id': session_id,
                                'sys_date': timezone.localdate(),
                                'sys_time': timezone.localtime().time()
                            }
                        )
                        updated_count += 1
                    except Exception as e:
                        messages.error(request, f"Error updating bill for {emp_id}: {e}")
                        # Continue processing other forms even if one fails
                        
            if updated_count > 0:
                messages.success(request, f"{updated_count} record(s) updated successfully.")
            else:
                messages.info(request, "No records selected or changed for update.")

            # Respond with HTMX trigger for refresh, or redirect for full page
            if request.headers.get('HX-Request'):
                # On successful HTMX formset submission, trigger a client-side event
                # to refresh the table without full page reload.
                # Use HX-Trigger-After-Swap to ensure messages are rendered first.
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': '{"refreshMobileBillTable":true, "showMessages":true}'
                    }
                )
            else:
                # Fallback for non-HTMX requests (should not happen with this setup)
                return redirect(self.get_success_url())
        else:
            # Formset is not valid. Re-render the form with errors.
            # This is tricky with HTMX and formsets. HTMX usually expects
            # a partial template with the form rendered.
            # For simplicity, we'll re-render the whole page with context,
            # or ideally, render just the table partial with errors.
            messages.error(request, "Please correct the errors below.")
            context = self.get_context_data(form=month_form) # Pass the month form
            context['formset'] = formset # Pass the invalid formset for rendering errors
            context['selected_month'] = selected_month # Ensure month is set correctly
            if request.headers.get('HX-Request'):
                # If it's an HTMX request, return the partial table template
                # with errors embedded.
                return self.render_to_response(context)
            else:
                return self.render_to_response(context)


class MobileBillTablePartialView(TemplateView):
    """
    HTMX-specific view to render only the mobile bill table part.
    This will be loaded dynamically when the month changes or after an update.
    """
    template_name = 'hr_mobile_bills/_mobilebill_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        selected_month = self.request.GET.get('month')
        if not selected_month:
            selected_month = timezone.now().month # Default to current month if not provided
        
        try:
            selected_month = int(selected_month)
        except (ValueError, TypeError):
            selected_month = timezone.now().month # Fallback for invalid month
            
        user_context = get_user_context(self.request)
        comp_id = user_context['comp_id']
        fin_year_id = user_context['fin_year_id']

        # Get the aggregated data for the table
        mobile_bill_data = MobileBill.objects.get_aggregated_bill_data(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            bill_month=selected_month
        )
        
        # Prepare initial data for the formset from the aggregated data
        initial_formset_data = []
        for row_data in mobile_bill_data:
            initial_formset_data.append({
                'user_id': row_data['user_id'],
                'emp_id': row_data['emp_id'],
                'employee_name': row_data['employee_name'],
                'mobile_no': row_data['mobile_no'],
                'limit_amt': row_data['limit_amt'],
                'bill_amt': row_data['bill_amt'],
                'taxes_fk': row_data['taxes_id'], # Pass ID for ModelChoiceField
                'taxes': row_data['taxes_id'], # For hidden taxes value
                'excess_amount': row_data['excess_amount'],
                'is_checked': row_data['has_existing_bill'],
                'mobile_bill_db_id': row_data['mobile_bill_db_id'],
            })
        
        # Instantiate the formset with initial data
        # For HTMX requests, we're generating the formset directly.
        # This will be submitted to the POST method of MobileBillEditView.
        formset = MobileBillFormSet(initial=initial_formset_data, prefix='mobile_bill_formset')
        
        context['formset'] = formset
        context['taxes_choices'] = TaxMaster.objects.all() # For rendering dropdowns manually if needed
        context['selected_month'] = selected_month
        
        return context

```

#### 4.4 Templates

We'll create two main templates: `mobilebill_edit_list.html` for the main page layout, and `_mobilebill_table.html` as a partial to be loaded dynamically via HTMX.

**`hr_mobile_bills/templates/hr_mobile_bills/mobilebill_edit_list.html`**
This template manages the month selection and acts as a container for the dynamically loaded table.

```html
{% extends 'core/base.html' %}

{% block title %}Mobile Bill - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="bg-blue-600 text-white p-4 rounded-t-lg mb-4">
            <h1 class="text-xl font-bold">Mobile Bill - Edit</h1>
        </div>

        <!-- Month Selection Form -->
        <form id="month-form" hx-get="{% url 'mobile_bills_table_partial' %}" hx-target="#mobile-bill-table-container" hx-swap="innerHTML" hx-trigger="change from:#id_bill_month" class="mb-6 flex items-center space-x-4">
            {% csrf_token %}
            <div>
                <label for="{{ form.bill_month.id_for_label }}" class="block text-sm font-bold text-gray-700 mb-1">Month Of Bill</label>
                {{ form.bill_month }}
            </div>
            <!-- Hidden input to pass selected month on initial load/reload -->
            <input type="hidden" name="month" x-model="selectedMonth" />
            <div x-data="{ selectedMonth: {{ selected_month|default:'' }} }" x-init="document.getElementById('id_bill_month').value = selectedMonth;"></div>
        </form>

        <!-- Message Display Area (e.g., from messages framework) -->
        {% if messages %}
            <div id="messages" class="mb-4" hx-swap-oob="outerHTML:#messages">
                {% for message in messages %}
                    <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Mobile Bill Table Container -->
        <div id="mobile-bill-table-container"
             hx-trigger="load, refreshMobileBillTable from:body"
             hx-get="{% url 'mobile_bills_table_partial' %}?month={{ selected_month|default:'' }}"
             hx-swap="innerHTML">
            <!-- Initial loading spinner -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading Mobile Bill Data...</p>
            </div>
        </div>
        
    </div>
</div>

<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'mobile-bill-table-container') {
            // Reinitialize DataTables after new content is swapped in
            // Check if DataTables library is loaded and the table element exists
            if (typeof $.fn.DataTable === 'function' && $('#mobileBillTable').length) {
                $('#mobileBillTable').DataTable({
                    "pageLength": 20, // Initial page size same as ASP.NET GridView
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "destroy": true, // Destroy existing instance if any
                    "responsive": true
                });
            }
        }
    });

    // Listen for custom event to show messages (e.g., after formset submission)
    document.body.addEventListener('showMessages', function() {
        // HTMX usually handles out-of-band swaps for messages.
        // This is a fallback/extra trigger if you want to ensure message visibility or fade.
        console.log("Messages triggered!");
    });
</script>
{% endblock %}
```

**`hr_mobile_bills/templates/hr_mobile_bills/_mobilebill_table.html`**
This partial template renders the DataTables content, including the formset for batch updates.

```html
<form hx-post="{% url 'mobile_bills_edit_list' %}" hx-swap="none" id="mobile-bill-update-form">
    {% csrf_token %}
    <input type="hidden" name="bill_month" value="{{ selected_month }}" />

    {{ formset.management_form }}

    <table id="mobileBillTable" class="min-w-full bg-white border border-gray-200 shadow-sm">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Limit Amt</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Amt</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taxes</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excess Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for form in formset %}
            <tr x-data="{ row: { isChecked: {{ form.is_checked.value|lower }} } }">
                <td class="py-3 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-center">
                    {{ form.is_checked }}
                    {{ form.mobile_bill_db_id }} {# Hidden field to carry DB ID for update #}
                    {{ form.emp_id }} {# Hidden field to carry EmpId #}
                    {{ form.user_id }} {# Hidden field to carry UserId #}
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-center">{{ form.employee_name.value }}</td>
                <td class="py-3 px-4 border-b border-gray-200">{{ form.employee_name.value }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-center">{{ form.mobile_no.value }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-right">{{ form.limit_amt.value|default_if_none:"0.00" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-right relative">
                    <span x-show="!row.isChecked" class="inline-block w-full text-right py-2">{{ form.bill_amt.value|default_if_none:"0.00" }}</span>
                    <span x-show="row.isChecked">
                        {{ form.bill_amt }}
                        {% if form.bill_amt.errors %}<span class="text-red-500 text-xs absolute -bottom-1 left-0">{{ form.bill_amt.errors.as_text }}</span>{% endif %}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-right relative">
                    <span x-show="!row.isChecked" class="inline-block w-full text-right py-2">{{ form.taxes_fk.value|default_if_none:"0.00" }}%</span>
                    <span x-show="row.isChecked">
                        {{ form.taxes }}
                         {% if form.taxes.errors %}<span class="text-red-500 text-xs absolute -bottom-1 left-0">{{ form.taxes.errors.as_text }}</span>{% endif %}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-right">
                    <span x-show="row.isChecked">{{ form.excess_amount.value|default_if_none:"0.00" }}</span>
                    <span x-show="!row.isChecked">{{ form.excess_amount.value|default_if_none:"0.00" }}</span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="9" class="py-3 px-4 border-t border-gray-200 text-right">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Update All Checked
                    </button>
                </td>
            </tr>
        </tfoot>
    </table>
</form>

<script>
    // Initialize DataTables if not already initialized
    // This script block will run each time the partial is swapped in.
    // The htmx:afterSwap listener in the main template handles this more robustly.
    // This inline script primarily helps with initial render or direct partial loads.
    if (typeof $.fn.DataTable === 'function' && !$.fn.DataTable.isDataTable('#mobileBillTable')) {
        $('#mobileBillTable').DataTable({
            "pageLength": 20,
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true
        });
    }
</script>
```

#### 4.5 URLs (`hr_mobile_bills/urls.py`)

Define the URL patterns for the views.

```python
from django.urls import path
from .views import MobileBillEditView, MobileBillTablePartialView

urlpatterns = [
    path('mobile_bills/edit/', MobileBillEditView.as_view(), name='mobile_bills_edit_list'),
    path('mobile_bills/table_partial/', MobileBillTablePartialView.as_view(), name='mobile_bills_table_partial'),
]

```

#### 4.6 Tests (`hr_mobile_bills/tests.py`)

Comprehensive tests for models, forms, and views are crucial. We will include unit tests for model methods and integration tests for view functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
import datetime

from .models import TaxMaster, CorporateMobileNo, OfficeStaff, MobileBill
from .forms import MobileBillRowForm, MobileBillFormSet

class MobileBillModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.tax_10 = TaxMaster.objects.create(id=1, value=Decimal('10.00'))
        cls.tax_5 = TaxMaster.objects.create(id=2, value=Decimal('5.00'))
        cls.corporate_mobile_1 = CorporateMobileNo.objects.create(id=101, mobile_no='9876543210', limit_amt=Decimal('500.00'))
        cls.corporate_mobile_dummy = CorporateMobileNo.objects.create(id=1, mobile_no='0000000000', limit_amt=Decimal('0.00')) # Dummy as per ASP.NET

        cls.staff_john = OfficeStaff.objects.create(user_id=1, emp_id='EMP001', employee_name='John Doe', mobile_no_fk=cls.corporate_mobile_1)
        cls.staff_jane = OfficeStaff.objects.create(user_id=2, emp_id='EMP002', employee_name='Jane Smith', mobile_no_fk=cls.corporate_mobile_1)
        cls.staff_no_mobile = OfficeStaff.objects.create(user_id=3, emp_id='EMP003', employee_name='Bob Johnson', mobile_no_fk=cls.corporate_mobile_dummy)


        cls.bill_john_jan = MobileBill.objects.create(
            id=1, comp_id=1, fin_year_id=1, session_id='testuser', emp_id='EMP001',
            bill_month=1, bill_amt=Decimal('600.00'), taxes_fk=cls.tax_10,
            sys_date=timezone.localdate(), sys_time=timezone.localtime().time()
        )
        cls.bill_jane_jan = MobileBill.objects.create(
            id=2, comp_id=1, fin_year_id=1, session_id='testuser', emp_id='EMP002',
            bill_month=1, bill_amt=Decimal('450.00'), taxes_fk=cls.tax_5,
            sys_date=timezone.localdate(), sys_time=timezone.localtime().time()
        )

    def test_mobile_bill_creation(self):
        bill = MobileBill.objects.get(id=1)
        self.assertEqual(bill.emp_id, 'EMP001')
        self.assertEqual(bill.bill_amt, Decimal('600.00'))
        self.assertEqual(bill.taxes_fk.value, Decimal('10.00'))
        self.assertEqual(bill.bill_month, 1)

    def test_calculate_excess_amount(self):
        # Test case 1: Bill with excess
        # BillAmt = 600, Taxes = 10%, LimitAmt = 500
        # CalAmt = (600 * 100) / (10 + 100) = 60000 / 110 = 545.4545...
        # Excess = 545.45 - 500 = 45.45
        excess = self.bill_john_jan.calculate_excess_amount(self.corporate_mobile_1.limit_amt)
        self.assertAlmostEqual(excess, Decimal('45.45'))

        # Test case 2: Bill within limit (or no excess)
        # BillAmt = 450, Taxes = 5%, LimitAmt = 500
        # CalAmt = (450 * 100) / (5 + 100) = 45000 / 105 = 428.5714...
        # Excess = 428.57 - 500 = -71.43 -> Should be 0
        excess = self.bill_jane_jan.calculate_excess_amount(self.corporate_mobile_1.limit_amt)
        self.assertAlmostEqual(excess, Decimal('0.00'))

        # Test case 3: Zero bill amount
        zero_bill = MobileBill.objects.create(
            id=3, comp_id=1, fin_year_id=1, session_id='testuser', emp_id='EMP003',
            bill_month=2, bill_amt=Decimal('0.00'), taxes_fk=self.tax_10,
            sys_date=timezone.localdate(), sys_time=timezone.localtime().time()
        )
        excess = zero_bill.calculate_excess_amount(Decimal('100.00'))
        self.assertEqual(excess, Decimal('0.00'))

    def test_get_aggregated_bill_data(self):
        # This tests the complex query logic in the custom manager
        current_month = self.bill_john_jan.bill_month # January (1)
        data = MobileBill.objects.get_aggregated_bill_data(
            comp_id=1, fin_year_id=1, bill_month=current_month
        )
        
        self.assertEqual(len(data), 2) # John Doe, Jane Smith should be in the list (Bob has dummy mobile)
        
        # Check John Doe's data
        john_data = next((item for item in data if item['emp_id'] == 'EMP001'), None)
        self.assertIsNotNone(john_data)
        self.assertEqual(john_data['employee_name'], 'John Doe')
        self.assertEqual(john_data['bill_amt'], Decimal('600.00'))
        self.assertEqual(john_data['taxes_value'], Decimal('10.00'))
        self.assertEqual(john_data['taxes_id'], self.tax_10.id)
        self.assertAlmostEqual(john_data['excess_amount'], Decimal('45.45'))
        self.assertTrue(john_data['has_existing_bill'])

        # Check Jane Smith's data
        jane_data = next((item for item in data if item['emp_id'] == 'EMP002'), None)
        self.assertIsNotNone(jane_data)
        self.assertEqual(jane_data['employee_name'], 'Jane Smith')
        self.assertEqual(jane_data['bill_amt'], Decimal('450.00'))
        self.assertEqual(jane_data['taxes_value'], Decimal('5.00'))
        self.assertEqual(jane_data['taxes_id'], self.tax_5.id)
        self.assertAlmostEqual(jane_data['excess_amount'], Decimal('0.00'))
        self.assertTrue(jane_data['has_existing_bill'])
        
        # Test for a month with no existing bills
        data_feb = MobileBill.objects.get_aggregated_bill_data(
            comp_id=1, fin_year_id=1, bill_month=2 # February
        )
        self.assertEqual(len(data_feb), 2) # Still lists John & Jane, but no bill data
        john_data_feb = next((item for item in data_feb if item['emp_id'] == 'EMP001'), None)
        self.assertIsNone(john_data_feb['bill_amt'])
        self.assertFalse(john_data_feb['has_existing_bill'])
        self.assertEqual(john_data_feb['excess_amount'], Decimal('0.00'))


class MobileBillViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Ensure base data for views is present
        cls.tax_10 = TaxMaster.objects.create(id=1, value=Decimal('10.00'))
        cls.tax_5 = TaxMaster.objects.create(id=2, value=Decimal('5.00'))
        cls.corporate_mobile_1 = CorporateMobileNo.objects.create(id=101, mobile_no='9876543210', limit_amt=Decimal('500.00'))
        cls.corporate_mobile_dummy = CorporateMobileNo.objects.create(id=1, mobile_no='0000000000', limit_amt=Decimal('0.00'))

        cls.staff_john = OfficeStaff.objects.create(user_id=1, emp_id='EMP001', employee_name='John Doe', mobile_no_fk=cls.corporate_mobile_1)
        cls.staff_jane = OfficeStaff.objects.create(user_id=2, emp_id='EMP002', employee_name='Jane Smith', mobile_no_fk=cls.corporate_mobile_1)
        cls.staff_no_mobile = OfficeStaff.objects.create(user_id=3, emp_id='EMP003', employee_name='Bob Johnson', mobile_no_fk=cls.corporate_mobile_dummy)

        cls.bill_john_jan = MobileBill.objects.create(
            id=1, comp_id=1, fin_year_id=1, session_id='testuser', emp_id='EMP001',
            bill_month=1, bill_amt=Decimal('600.00'), taxes_fk=cls.tax_10,
            sys_date=timezone.localdate(), sys_time=timezone.localtime().time()
        )

    def setUp(self):
        self.client = Client()
        # Mocking user context for CompId, FinYearId in views
        self.comp_id = 1
        self.fin_year_id = 1
        self.session_id = 'testuser'

    def test_mobile_bill_edit_list_view_get(self):
        response = self.client.get(reverse('mobile_bills_edit_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_mobile_bills/mobilebill_edit_list.html')
        self.assertIsInstance(response.context['form'], MobileBillRowForm.__class__.__bases__[0]) # Check form type (MonthSelectionForm)
        self.assertIn('selected_month', response.context)

    def test_mobile_bill_table_partial_view_get(self):
        response = self.client.get(reverse('mobile_bills_table_partial'), {'month': 1}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_mobile_bills/_mobilebill_table.html')
        self.assertIn('formset', response.context)
        self.assertEqual(len(response.context['formset']), 2) # Should show 2 rows (John, Jane)

    def test_mobile_bill_update_post_single_record(self):
        # Simulate a formset submission to update John Doe's bill
        # Ensure a formset is correctly constructed with `prefix`
        
        # Initial data for formset (what the user would have seen initially)
        initial_data = MobileBill.objects.get_aggregated_bill_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, bill_month=1
        )
        
        # Data to simulate POST request (only John Doe is checked and updated)
        # Formset expects management form data
        post_data = {
            'mobile_bill_formset-TOTAL_FORMS': len(initial_data),
            'mobile_bill_formset-INITIAL_FORMS': len(initial_data),
            'mobile_bill_formset-MIN_NUM_FORMS': 0,
            'mobile_bill_formset-MAX_NUM_FORMS': 1000, # A reasonable max
            # John Doe's row (index 0, assuming stable order from initial_data)
            'mobile_bill_formset-0-user_id': self.staff_john.user_id,
            'mobile_bill_formset-0-emp_id': self.staff_john.emp_id,
            'mobile_bill_formset-0-employee_name': self.staff_john.employee_name,
            'mobile_bill_formset-0-mobile_no': self.corporate_mobile_1.mobile_no,
            'mobile_bill_formset-0-limit_amt': self.corporate_mobile_1.limit_amt,
            'mobile_bill_formset-0-excess_amount': Decimal('45.45'), # Original calculated
            'mobile_bill_formset-0-mobile_bill_db_id': self.bill_john_jan.id,
            'mobile_bill_formset-0-is_checked': 'on', # Checkbox is checked
            'mobile_bill_formset-0-bill_amt': '700.00', # New Bill Amount
            'mobile_bill_formset-0-taxes_fk': self.tax_5.id, # New Taxes

            # Jane Smith's row (index 1) - not checked, no changes
            'mobile_bill_formset-1-user_id': self.staff_jane.user_id,
            'mobile_bill_formset-1-emp_id': self.staff_jane.emp_id,
            'mobile_bill_formset-1-employee_name': self.staff_jane.employee_name,
            'mobile_bill_formset-1-mobile_no': self.corporate_mobile_1.mobile_no,
            'mobile_bill_formset-1-limit_amt': self.corporate_mobile_1.limit_amt,
            'mobile_bill_formset-1-excess_amount': Decimal('0.00'),
            'mobile_bill_formset-1-mobile_bill_db_id': self.bill_jane_jan.id,
            'mobile_bill_formset-1-is_checked': '', # Not checked
            'mobile_bill_formset-1-bill_amt': '450.00', # Original
            'mobile_bill_formset-1-taxes_fk': self.tax_5.id, # Original
            
            # Month selection form field
            'bill_month': 1,
        }

        response = self.client.post(reverse('mobile_bills_edit_list'), post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code for no content swap
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMobileBillTable', response.headers['HX-Trigger'])

        # Verify the database update
        updated_bill_john = MobileBill.objects.get(id=self.bill_john_jan.id)
        self.assertEqual(updated_bill_john.bill_amt, Decimal('700.00'))
        self.assertEqual(updated_bill_john.taxes_fk.id, self.tax_5.id)

        # Verify Jane's bill was not changed
        unchanged_bill_jane = MobileBill.objects.get(id=self.bill_jane_jan.id)
        self.assertEqual(unchanged_bill_jane.bill_amt, Decimal('450.00'))
        self.assertEqual(unchanged_bill_jane.taxes_fk.id, self.tax_5.id)

    def test_mobile_bill_update_post_new_record(self):
        # Simulate creating a new bill for EMP003 (Bob Johnson), who currently has no bill for month 1
        # First, ensure Bob has no bill for month 1
        self.assertFalse(MobileBill.objects.filter(emp_id='EMP003', bill_month=1).exists())

        initial_data = MobileBill.objects.get_aggregated_bill_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, bill_month=1
        )
        # Add Bob's initial data manually (assuming it's filtered out of default initial_data)
        # In a real scenario, get_aggregated_bill_data should include ALL staff,
        # even if they don't have a bill for the month, to allow creating new ones.
        # Let's adjust get_aggregated_bill_data to return all relevant staff, existing bill or not.
        # (This was already done in the model definition for the manager method)

        # Bob's data from get_aggregated_bill_data (assuming he would be the 3rd row, index 2)
        # This requires `get_aggregated_bill_data` to return all staff, not just those with bills.
        bob_initial_data = {
            'user_id': self.staff_no_mobile.user_id,
            'emp_id': self.staff_no_mobile.emp_id,
            'employee_name': self.staff_no_mobile.employee_name,
            'mobile_no': self.corporate_mobile_dummy.mobile_no,
            'limit_amt': self.corporate_mobile_dummy.limit_amt,
            'bill_amt': None,
            'taxes_value': None,
            'taxes_id': None,
            'excess_amount': Decimal('0.00'),
            'has_existing_bill': False,
            'mobile_bill_db_id': None
        }
        # Assuming initial_data would contain John, Jane, Bob
        initial_data.append(bob_initial_data)

        post_data = {
            'mobile_bill_formset-TOTAL_FORMS': len(initial_data),
            'mobile_bill_formset-INITIAL_FORMS': len(initial_data),
            'mobile_bill_formset-MIN_NUM_FORMS': 0,
            'mobile_bill_formset-MAX_NUM_FORMS': 1000,
            # John Doe's row (unchanged)
            'mobile_bill_formset-0-user_id': self.staff_john.user_id,
            'mobile_bill_formset-0-emp_id': self.staff_john.emp_id,
            'mobile_bill_formset-0-employee_name': self.staff_john.employee_name,
            'mobile_bill_formset-0-mobile_no': self.corporate_mobile_1.mobile_no,
            'mobile_bill_formset-0-limit_amt': self.corporate_mobile_1.limit_amt,
            'mobile_bill_formset-0-excess_amount': Decimal('45.45'),
            'mobile_bill_formset-0-mobile_bill_db_id': self.bill_john_jan.id,
            'mobile_bill_formset-0-is_checked': '',
            'mobile_bill_formset-0-bill_amt': '600.00',
            'mobile_bill_formset-0-taxes_fk': self.tax_10.id,

            # Jane Smith's row (unchanged)
            'mobile_bill_formset-1-user_id': self.staff_jane.user_id,
            'mobile_bill_formset-1-emp_id': self.staff_jane.emp_id,
            'mobile_bill_formset-1-employee_name': self.staff_jane.employee_name,
            'mobile_bill_formset-1-mobile_no': self.corporate_mobile_1.mobile_no,
            'mobile_bill_formset-1-limit_amt': self.corporate_mobile_1.limit_amt,
            'mobile_bill_formset-1-excess_amount': Decimal('0.00'),
            'mobile_bill_formset-1-mobile_bill_db_id': self.bill_jane_jan.id,
            'mobile_bill_formset-1-is_checked': '',
            'mobile_bill_formset-1-bill_amt': '450.00',
            'mobile_bill_formset-1-taxes_fk': self.tax_5.id,

            # Bob Johnson's new bill (index 2)
            'mobile_bill_formset-2-user_id': self.staff_no_mobile.user_id,
            'mobile_bill_formset-2-emp_id': self.staff_no_mobile.emp_id,
            'mobile_bill_formset-2-employee_name': self.staff_no_mobile.employee_name,
            'mobile_bill_formset-2-mobile_no': self.corporate_mobile_dummy.mobile_no,
            'mobile_bill_formset-2-limit_amt': self.corporate_mobile_dummy.limit_amt,
            'mobile_bill_formset-2-excess_amount': Decimal('0.00'), # Initial
            'mobile_bill_formset-2-mobile_bill_db_id': '', # No existing DB ID
            'mobile_bill_formset-2-is_checked': 'on', # Checkbox is checked
            'mobile_bill_formset-2-bill_amt': '150.00', # New Bill Amount
            'mobile_bill_formset-2-taxes_fk': self.tax_10.id, # New Taxes
            
            # Month selection form field
            'bill_month': 1,
        }

        response = self.client.post(reverse('mobile_bills_edit_list'), post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code for no content swap
        self.assertTrue(MobileBill.objects.filter(emp_id='EMP003', bill_month=1).exists())
        new_bill_bob = MobileBill.objects.get(emp_id='EMP003', bill_month=1)
        self.assertEqual(new_bill_bob.bill_amt, Decimal('150.00'))
        self.assertEqual(new_bill_bob.taxes_fk.id, self.tax_10.id)

    def test_mobile_bill_update_post_validation_error(self):
        # Simulate a formset submission with invalid data (e.g., missing required field)
        initial_data = MobileBill.objects.get_aggregated_bill_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, bill_month=1
        )
        post_data = {
            'mobile_bill_formset-TOTAL_FORMS': len(initial_data),
            'mobile_bill_formset-INITIAL_FORMS': len(initial_data),
            'mobile_bill_formset-MIN_NUM_FORMS': 0,
            'mobile_bill_formset-MAX_NUM_FORMS': 1000,
            # John Doe's row (index 0) - checked but BillAmt missing
            'mobile_bill_formset-0-user_id': self.staff_john.user_id,
            'mobile_bill_formset-0-emp_id': self.staff_john.emp_id,
            'mobile_bill_formset-0-employee_name': self.staff_john.employee_name,
            'mobile_bill_formset-0-mobile_no': self.corporate_mobile_1.mobile_no,
            'mobile_bill_formset-0-limit_amt': self.corporate_mobile_1.limit_amt,
            'mobile_bill_formset-0-excess_amount': Decimal('45.45'),
            'mobile_bill_formset-0-mobile_bill_db_id': self.bill_john_jan.id,
            'mobile_bill_formset-0-is_checked': 'on',
            'mobile_bill_formset-0-bill_amt': '', # Invalid: empty bill_amt
            'mobile_bill_formset-0-taxes_fk': self.tax_5.id,
            
            # Jane Smith's row (unchanged)
            'mobile_bill_formset-1-user_id': self.staff_jane.user_id,
            'mobile_bill_formset-1-emp_id': self.staff_jane.emp_id,
            'mobile_bill_formset-1-employee_name': self.staff_jane.employee_name,
            'mobile_bill_formset-1-mobile_no': self.corporate_mobile_1.mobile_no,
            'mobile_bill_formset-1-limit_amt': self.corporate_mobile_1.limit_amt,
            'mobile_bill_formset-1-excess_amount': Decimal('0.00'),
            'mobile_bill_formset-1-mobile_bill_db_id': self.bill_jane_jan.id,
            'mobile_bill_formset-1-is_checked': '',
            'mobile_bill_formset-1-bill_amt': '450.00',
            'mobile_bill_formset-1-taxes_fk': self.tax_5.id,

            'bill_month': 1,
        }
        
        response = self.client.post(reverse('mobile_bills_edit_list'), post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders partial with errors
        self.assertTemplateUsed(response, 'hr_mobile_bills/mobilebill_edit_list.html') # Main template gets re-rendered
        self.assertContains(response, 'Bill Amount is required when checked.')
        # Check that the bill was NOT updated
        original_bill_john = MobileBill.objects.get(id=self.bill_john_jan.id)
        self.assertEqual(original_bill_john.bill_amt, Decimal('600.00')) # Should remain unchanged
```

---

### Step 5: HTMX and Alpine.js Integration

-   **HTMX:**
    -   `MonthSelectionForm` uses `hx-get` to trigger a request to `mobile_bills_table_partial` when the dropdown `change` event occurs. The `hx-target` and `hx-swap` ensure only the table container is updated.
    -   The main `MobileBillEditView` `post` method sends an `HX-Trigger` header (`refreshMobileBillTable`) upon successful formset submission. This event is caught by the `div` containing the `mobile-bill-table-container`, which then re-fetches its content, effectively refreshing the DataTables.
    -   The `form` for the DataTables (`mobile-bill-update-form`) uses `hx-post` and `hx-swap="none"` for submission, allowing the server to handle the updates and then trigger a refresh.
    -   Messages are handled with `hx-swap-oob="outerHTML:#messages"` from `messages.html` (which would be included in `base.html`).

-   **Alpine.js:**
    -   Each table row (`<tr>`) uses `x-data="{ row: { isChecked: ... } }"` to manage its local state, specifically the `isChecked` property.
    -   The `asp:CheckBox` equivalent (`form.is_checked`) uses `x-model="row.isChecked"` to bind its checked state to the Alpine.js `row.isChecked` property.
    -   The `asp:TextBox` (`form.bill_amt`) and `asp:DropDownList` (`form.taxes`) use `x-show="row.isChecked"` to dynamically show or hide based on the checkbox state, replicating the `getValData()` functionality of ASP.NET.
    -   Error messages for individual fields can be conditionally displayed using `x-bind:class` for styling and `x-show` for the error text if `form.errors.field_name` is populated.

-   **DataTables:**
    -   The `_mobilebill_table.html` partial includes a `$(document).ready` script to initialize DataTables on the `mobileBillTable`.
    -   A global `htmx:afterSwap` event listener in `mobilebill_edit_list.html` ensures that DataTables is re-initialized whenever new content is swapped into the table container, preserving search, sort, and pagination capabilities.
    -   The `pageLength` and `lengthMenu` are set to match the ASP.NET `PageSize` and provide user options.

This comprehensive plan transforms the legacy ASP.NET application into a modern, maintainable Django solution, emphasizing automation, clear separation of concerns, and a highly interactive user experience without excessive JavaScript.