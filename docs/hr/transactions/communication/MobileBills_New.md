The transition from legacy ASP.NET applications to modern Django-based solutions using AI-assisted automation is a strategic move to enhance business agility, reduce operational costs, and improve system maintainability. This plan outlines a comprehensive approach to modernize your ASP.NET Mobile Bills application into a robust, scalable Django system.

By adopting Django with a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for lightweight client-side logic, and DataTables for superior data presentation, we ensure a highly efficient, user-friendly, and maintainable application. This approach minimizes traditional JavaScript, simplifying development and ongoing support.

**Business Benefits:**

*   **Improved User Experience:** Modern, responsive interfaces with instant feedback via HTMX and Alpine.js, ensuring a smooth and efficient bill entry process.
*   **Reduced Development Costs:** Leveraging Django's rapid development capabilities and established best practices leads to faster feature delivery and lower maintenance overhead.
*   **Enhanced Scalability:** Django's robust architecture allows the application to easily scale with growing business needs and user demands.
*   **Simplified Maintenance:** Clear separation of concerns, comprehensive test coverage, and a focus on DRY principles result in a codebase that is easier to understand, debug, and extend.
*   **Future-Proofing:** Moving to an open-source, widely adopted framework like Django mitigates vendor lock-in and ensures access to a vast community and continuous innovation.
*   **Data Integrity:** Strong model-level validation and business logic ensure that data entered into the system is accurate and consistent.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with four primary tables: `tblHR_OfficeStaff`, `tblHR_CoporateMobileNo`, `tblHR_MobileBill`, and `tblExciseser_Master`. These tables are joined to display employee information, mobile details, existing bill amounts, and tax rates. The core functionality involves inserting new mobile bill entries and calculating excess amounts based on bill and limit amounts.

**Extracted Schema:**

*   **`tblHR_OfficeStaff`**
    *   `UserID` (PK, int) - Primary key, used to identify staff.
    *   `EmpId` (string/char) - Employee's identifier.
    *   `EmployeeName` (string/char) - Employee's full name.
    *   `MobileNo` (int) - Foreign key referencing `tblHR_CoporateMobileNo.Id`.
    *   `CompId` (int) - Company ID for multi-company support.

*   **`tblHR_CoporateMobileNo`**
    *   `Id` (PK, int) - Primary key.
    *   `MobileNo` (string/char) - The corporate mobile number.
    *   `LimitAmt` (decimal) - The allowed mobile bill limit for this number.

*   **`tblHR_MobileBill`**
    *   `Id` (PK, int) - Primary key for the mobile bill record.
    *   `SysDate` (date) - System date of record creation.
    *   `SysTime` (time) - System time of record creation.
    *   `CompId` (int) - Company ID.
    *   `FinYearId` (int) - Financial Year ID.
    *   `SessionId` (string) - User session ID (username).
    *   `EmpId` (string/char) - Employee ID (used for linking, but `UserID` from `OfficeStaff` is the main relation).
    *   `BillMonth` (int) - Month of the bill.
    *   `BillAmt` (decimal) - The actual bill amount.
    *   `Taxes` (int) - Foreign key referencing `tblExciseser_Master.Id`.

*   **`tblExciseser_Master`**
    *   `Id` (PK, int) - Primary key for the tax master.
    *   `Value` (decimal/float) - The tax percentage or value.
    *   `LiveSerTax` (bool/int) - Indicates if the tax is live for service tax (value '1' means true).
    *   `Live` (bool/int) - General live status (value '1' means true).

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET application provides a composite view:
*   **Read (Display)**: Fetches and displays a list of employees with corporate mobile numbers and their limits. For a selected month, it checks if a mobile bill already exists for each employee and displays the bill amount, taxes, and calculated excess amount if present.
*   **Create (Bulk Insert)**: Allows users to select multiple employees via checkboxes, enter a `BillAmt` and `Taxes` for each selected employee, and then perform a bulk insert operation to create new `MobileBill` records.
*   **Filtering**: The `DropDownList1` (Month selector) allows filtering the displayed data by bill month, triggering a full page re-render in ASP.NET.
*   **Dynamic UI**: Checkbox `CheckBox1` controls the visibility of input fields (`TxtBillAmt`, `DDLTaxes`) and validation controls for specific rows.
*   **Calculations**: The `ExcessAmount` is calculated server-side based on `BillAmt` and `Taxes`.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET controls translate to standard HTML elements enhanced with Tailwind CSS, HTMX, and Alpine.js for dynamic behavior.

*   **Month Selection:** `asp:DropDownList` -> Standard HTML `<select>` element, which will trigger an HTMX request on change to update the table content.
*   **Data Grid:** `asp:GridView` -> HTML `<table>` element, styled with Tailwind CSS and initialized with DataTables for client-side functionality (sorting, searching, pagination).
*   **Data Display:** `asp:Label` controls -> Plain HTML `<span>` or `<td>` elements to display data.
*   **Input Fields for Bill Entry:** `asp:TextBox` (`TxtBillAmt`), `asp:DropDownList` (`DDLTaxes`) -> Standard HTML `<input type="number">` and `<select>` elements.
*   **Row Selection/Visibility Toggle:** `asp:CheckBox` (`CheckBox1`) -> HTML `<input type="checkbox">` combined with Alpine.js to control the visibility of associated input fields within the row.
*   **Bulk Insert Button:** `asp:Button` (`BtnInsert`) -> HTML `<button type="submit">` within a form wrapping the table, triggering an HTMX `POST` request.
*   **Validation:** `asp:RegularExpressionValidator`, `asp:RequiredFieldValidator` -> Handled by Django forms (server-side) and potentially Alpine.js for client-side validation hints without full form submission.
*   **Messages:** `asp:Label` (`lblMessage`) -> Django messages framework displayed in the base template.

---

### Step 4: Generate Django Code

**Application Name:** `hr_mobile_bills`

#### 4.1 Models (`hr_mobile_bills/models.py`)

**Explanation:** Models are defined with `managed = False` and `db_table` to map directly to the existing database tables. Relationships (`ForeignKey`) are established where applicable. The `MobileBill` model includes methods to encapsulate the business logic for calculating the `excess_amount` and deriving the net amount from the gross bill and taxes, adhering to the "fat model" principle.

```python
from django.db import models
from django.utils import timezone
from datetime import date

class OfficeStaff(models.Model):
    # UserID is the primary key and the identifier used in relationships
    user_id = models.IntegerField(db_column='UserID', primary_key=True)
    emp_id = models.CharField(db_column='EmpId', max_length=50) # Assuming max length for EmpId
    employee_name = models.CharField(db_column='EmployeeName', max_length=255) # Assuming max length
    mobile_no_id = models.IntegerField(db_column='MobileNo') # FK to CorporateMobileNo.Id
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff Member'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

    @property
    def corporate_mobile_details(self):
        """Fetches the associated CorporateMobileNo details."""
        try:
            return CorporateMobileNo.objects.get(id=self.mobile_no_id)
        except CorporateMobileNo.DoesNotExist:
            return None

class CorporateMobileNo(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=20) # Assuming max length
    limit_amount = models.DecimalField(db_column='LimitAmt', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        return self.mobile_no

class ExciseServiceTax(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.DecimalField(db_column='Value', max_digits=5, decimal_places=2) # Assuming tax percentage like 10.00
    live_service_tax = models.BooleanField(db_column='LiveSerTax')
    live = models.BooleanField(db_column='Live')

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service Tax'
        verbose_name_plural = 'Excise Service Taxes'

    def __str__(self):
        return f"{self.value}%"

class MobileBill(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Inferred PK
    sys_date = models.DateField(db_column='SysDate', default=date.today)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now().time)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=100) # Assuming max length for username
    # EmpId in DB, but better to link to OfficeStaff's primary key (UserID)
    # The ASP.NET code used EmpId for linking, assuming EmpId is unique in OfficeStaff.
    # If EmpId is not unique in OfficeStaff and UserID is the PK, this should be a FK to OfficeStaff.UserID.
    # Assuming EmpId is unique and suitable for FK. If not, map to UserID.
    employee = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='emp_id')
    bill_month = models.IntegerField(db_column='BillMonth')
    bill_amount = models.DecimalField(db_column='BillAmt', max_digits=18, decimal_places=2)
    tax = models.ForeignKey(ExciseServiceTax, on_delete=models.DO_NOTHING, db_column='Taxes')

    class Meta:
        managed = False
        db_table = 'tblHR_MobileBill'
        verbose_name = 'Mobile Bill'
        verbose_name_plural = 'Mobile Bills'
        # Add a unique constraint to prevent duplicate bills for same employee, month, company, fin year
        unique_together = [['employee', 'bill_month', 'comp_id', 'fin_year_id']]

    def __str__(self):
        return f"Bill for {self.employee.employee_name} ({self.bill_month})"

    def calculate_net_bill_amount(self):
        """Calculates the bill amount before taxes from the gross bill amount."""
        if self.tax and self.tax.value > 0:
            tax_rate = self.tax.value / 100
            # Formula: Net = Gross - (Gross * (TaxRate / (TaxRate + 1))) -> Net = Gross / (1 + TaxRate)
            # The ASP.NET formula: BillAmt - ((BillAmt * Taxes) / (Taxes + 100))
            # which means Gross - (Gross * (TaxValue / (TaxValue + 100)))
            # If Taxes is a percentage (e.g., 10 for 10%), then Taxes/100 is the rate.
            # So, Gross - (Gross * (Rate / (Rate + 1))) is more like it.
            # Or perhaps, Gross = Net * (1 + Rate), so Net = Gross / (1 + Rate).
            # The ASP.NET formula: CalAmt = BillAmt - ((BillAmt * Taxes) / (Taxes + 100));
            # If Taxes is 10 (meaning 10% tax on net), then Gross = Net * (1 + 0.10) = Net * 1.10
            # If Taxes is 10 (meaning 10% tax on gross), then Net = Gross * (1 - 0.10) = Gross * 0.90
            # Re-evaluating ASP.NET's formula: `CalAmt = BillAmt - ((BillAmt * Taxes) / (Taxes + 100))`
            # Example: BillAmt=110, Taxes=10. CalAmt = 110 - (110 * 10 / 110) = 110 - 10 = 100.
            # This means `BillAmt` is Gross, and `Taxes` is a percentage that's "baked in" to arrive at a Net.
            # So, `Taxes` is like the tax percentage of the *net* amount, and `BillAmt` is gross.
            # Net = BillAmt / (1 + Taxes/100)
            return self.bill_amount / (1 + (self.tax.value / 100))
        return self.bill_amount # If no tax, net is same as gross

    @property
    def excess_amount(self):
        """Calculates the excess amount compared to the limit."""
        net_bill_amount = self.calculate_net_bill_amount()
        limit_amount = self.employee.corporate_mobile_details.limit_amount if self.employee.corporate_mobile_details else 0

        if net_bill_amount > limit_amount:
            return round(net_bill_amount - limit_amount, 2)
        return 0.00
```

#### 4.2 Forms (`hr_mobile_bills/forms.py`)

**Explanation:** A form is defined for adding/editing a single `MobileBill` entry, which would typically be used in a modal. For the bulk insert functionality on the main table, a custom approach will be needed to handle the dynamic creation of form data for multiple entries within the `MobileBillListView` itself. The `MobileBillForm` will be a ModelForm with widgets for Tailwind styling.

```python
from django import forms
from .models import MobileBill, ExciseServiceTax
from django.db.models import Max

class MobileBillForm(forms.ModelForm):
    # Additional fields if needed for context (e.g., employee name, limit)
    # These would be read-only or used for display purposes, not for saving directly.

    class Meta:
        model = MobileBill
        # 'employee', 'bill_month', 'comp_id', 'fin_year_id' should be handled by view context
        # 'sys_date', 'sys_time', 'session_id' will be set automatically by view/model
        fields = ['bill_amount', 'tax']
        widgets = {
            'bill_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter Bill Amount',
                'min': '0', # Add HTML5 validation
                'step': '0.01'
            }),
            'tax': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate the tax dropdown with live excise service taxes
        self.fields['tax'].queryset = ExciseServiceTax.objects.filter(live=True).order_by('value')
        # Set a default value if available, mimicking the ASP.NET behavior
        default_tax = ExciseServiceTax.objects.filter(live=True).first()
        if default_tax:
            self.fields['tax'].initial = default_tax.id

    def clean_bill_amount(self):
        bill_amount = self.cleaned_data['bill_amount']
        if bill_amount < 0:
            raise forms.ValidationError("Bill amount cannot be negative.")
        return bill_amount
    
    # This form is designed for a single MobileBill.
    # For bulk operations, the view will process individual form data or use a formset if needed.

# Month selection form (not a ModelForm)
class BillMonthForm(forms.Form):
    # Dummy method to get months, replace with actual DB retrieval or utility
    def get_months_for_dropdown():
        # In a real app, this would come from a lookup table or calculated based on financial year
        # For this example, we'll use a simple range
        return [(i, date(2000, i, 1).strftime('%B')) for i in range(1, 13)]
        
    bill_month = forms.ChoiceField(
        choices=get_months_for_dropdown(),
        widget=forms.Select(attrs={
            'id': 'id_bill_month', # Important for HTMX targeting
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': '{{% url "hr_mobile_bills:mobilebill_table" %}}', # HTMX endpoint
            'hx-target': '#mobileBillTable-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
            'name': 'bill_month', # Ensure name is set for POST/GET data
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add a "Select" option if it was in original ASP.NET dropdown.
        # This will need to be handled by the view to prevent data loading if "Select" is chosen.
        # choices = [('', 'Select')] + list(self.fields['bill_month'].choices)
        # self.fields['bill_month'].choices = choices
```

#### 4.3 Views (`hr_mobile_bills/views.py`)

**Explanation:**
- `MobileBillListView`: The main page, rendering the month selector and a container for the table which is loaded via HTMX.
- `MobileBillTablePartialView`: An HTMX-specific view that fetches and renders only the table content, handling the month filtering and deciding whether to show input fields or labels for each employee row. It also handles the complex bulk insertion logic.
- `MobileBillUpdateView` and `MobileBillDeleteView`: Standard CBVs for individual bill management, accessed via modals.

```python
from django.views.generic import View, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, get_object_or_404
from django.db import transaction
from django.db.models import Max # For generating new PK
from django.contrib.auth.mixins import LoginRequiredMixin # Add for authentication

from .models import OfficeStaff, CorporateMobileNo, MobileBill, ExciseServiceTax
from .forms import MobileBillForm, BillMonthForm
from datetime import date # For default dates

# Placeholder for session variables (replace with actual session/context management)
# In a real application, comp_id and fin_year_id would come from user's session or profile
# For demonstration, let's use hardcoded values or pass them via GET/POST for testing.
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR_ID = 2023 # Example

# This view handles the main page with the month dropdown
class MobileBillListView(LoginRequiredMixin, View):
    template_name = 'hr_mobile_bills/mobilebill_list.html'

    def get(self, request, *args, **kwargs):
        form = BillMonthForm(request.GET or None)
        context = {
            'bill_month_form': form,
        }
        return render(request, self.template_name, context)

# This view renders the table content, which is dynamically loaded by HTMX
class MobileBillTablePartialView(LoginRequiredMixin, View):
    template_name = 'hr_mobile_bills/_mobilebill_table.html'

    def get_context_data(self, request):
        bill_month_value = request.GET.get('bill_month')
        if bill_month_value:
            bill_month_value = int(bill_month_value)
        
        # Simulating session variables for CompId and FinYearId
        comp_id = DEFAULT_COMP_ID # Replace with request.session.get('compid') or similar
        fin_year_id = DEFAULT_FIN_YEAR_ID # Replace with request.session.get('finyear') or similar

        # Get employees with corporate mobile numbers and a limit amount
        employees_data = []
        office_staff_with_mobile = OfficeStaff.objects.filter(
            corporate_mobile_details__isnull=False,
            corporate_mobile_details__limit_amount__gt=0,
            mobile_no_id__ne=1, # Equivalent to MobileNo != 1
            comp_id=comp_id
        ).prefetch_related('corporate_mobile_details') # Optimize query

        # Fetch existing mobile bills for the selected month
        existing_bills = {}
        if bill_month_value:
            bills_queryset = MobileBill.objects.filter(
                bill_month=bill_month_value,
                comp_id=comp_id,
                fin_year_id=fin_year_id
            ).select_related('tax', 'employee')
            for bill in bills_queryset:
                existing_bills[bill.employee.emp_id] = bill

        # Get all live excise taxes for dropdowns
        all_taxes = ExciseServiceTax.objects.filter(live=True).order_by('value')
        default_tax_id = all_taxes.first().id if all_taxes.exists() else None

        for staff in office_staff_with_mobile:
            mobile_details = staff.corporate_mobile_details
            bill = existing_bills.get(staff.emp_id)
            
            row_data = {
                'staff': staff,
                'mobile_details': mobile_details,
                'existing_bill': bill,
                'bill_amount_form': MobileBillForm(prefix=f'bill_{staff.user_id}'), # For bulk processing
                'taxes_options': all_taxes,
                'default_tax_id': default_tax_id,
                'excess_amount': bill.excess_amount if bill else 0.00
            }
            employees_data.append(row_data)

        context = {
            'employees_data': employees_data,
            'selected_bill_month': bill_month_value,
        }
        return context

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(request)
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        # Handle the bulk insert operation
        # This will process form data for multiple rows submitted from the table
        comp_id = DEFAULT_COMP_ID
        fin_year_id = DEFAULT_FIN_YEAR_ID
        session_id = request.user.username if request.user.is_authenticated else 'anonymous'
        selected_bill_month = request.POST.get('bill_month_selected') # Hidden input or passed from dropdown

        if not selected_bill_month or selected_bill_month == 'Select':
            messages.error(request, "Please select a valid bill month.")
            # Reload the table with an error message
            response = render(request, self.template_name, self.get_context_data(request))
            response.headers['HX-Retarget'] = '#mobileBillTable-container'
            response.headers['HX-Reswap'] = 'innerHTML'
            return response

        selected_bill_month = int(selected_bill_month)
        inserted_count = 0
        errors = []

        # Find the next available ID for tblHR_MobileBill
        # This assumes Id is an INT PK and is NOT auto-incrementing in the legacy DB.
        # If it is auto-incrementing, remove this logic.
        max_id_obj = MobileBill.objects.all().aggregate(Max('id'))
        next_id = (max_id_obj['id__max'] or 0) + 1


        with transaction.atomic():
            for key, value in request.POST.items():
                if key.startswith('bill_amount_'):
                    parts = key.split('_')
                    if len(parts) == 3 and parts[2].isdigit():
                        user_id = int(parts[2])
                        bill_amount_str = value
                        tax_id_key = f'tax_id_{user_id}'
                        tax_id_str = request.POST.get(tax_id_key)
                        
                        if bill_amount_str and tax_id_str:
                            try:
                                employee_obj = OfficeStaff.objects.get(user_id=user_id, comp_id=comp_id)
                                tax_obj = ExciseServiceTax.objects.get(id=int(tax_id_str))
                                bill_amount = float(bill_amount_str) # Convert to float/Decimal for validation

                                # Check if a bill already exists for this employee for this month
                                if MobileBill.objects.filter(
                                    employee=employee_obj,
                                    bill_month=selected_bill_month,
                                    comp_id=comp_id,
                                    fin_year_id=fin_year_id
                                ).exists():
                                    errors.append(f"Bill already exists for {employee_obj.employee_name} for the selected month.")
                                    continue
                                
                                # Validate bill amount and tax
                                if not (bill_amount > 0 and bill_amount < 1_000_000_000): # Example validation
                                    errors.append(f"Invalid bill amount for {employee_obj.employee_name}.")
                                    continue
                                if not tax_obj: # Ensure tax exists
                                    errors.append(f"Invalid tax selected for {employee_obj.employee_name}.")
                                    continue
                                
                                # Create new MobileBill instance
                                MobileBill.objects.create(
                                    id=next_id, # If PK is manual
                                    sys_date=date.today(),
                                    sys_time=timezone.now().time(),
                                    comp_id=comp_id,
                                    fin_year_id=fin_year_id,
                                    session_id=session_id,
                                    employee=employee_obj,
                                    bill_month=selected_bill_month,
                                    bill_amount=bill_amount,
                                    tax=tax_obj
                                )
                                next_id += 1 # Increment for next manual PK
                                inserted_count += 1
                            except OfficeStaff.DoesNotExist:
                                errors.append(f"Employee with UserID {user_id} not found.")
                            except ExciseServiceTax.DoesNotExist:
                                errors.append(f"Tax with ID {tax_id_str} not found for UserID {user_id}.")
                            except ValueError:
                                errors.append(f"Invalid numeric input for UserID {user_id}.")
                            except Exception as e:
                                errors.append(f"Error processing UserID {user_id}: {e}")

        if inserted_count > 0:
            messages.success(request, f"Successfully inserted {inserted_count} mobile bills.")
        if errors:
            for error in errors:
                messages.error(request, error)
        
        # After processing, trigger a refresh of the table.
        # This will call the GET method of this view again via HTMX.
        response = HttpResponse(status=204) # No content, tells HTMX to do nothing more than trigger
        response.headers['HX-Trigger'] = 'refreshMobileBillTable'
        return response


# These views handle individual MobileBill records, typically via modals
class MobileBillUpdateView(LoginRequiredMixin, UpdateView):
    model = MobileBill
    form_class = MobileBillForm
    template_name = 'hr_mobile_bills/_mobilebill_form.html' # Use partial for modal
    # success_url not needed as HTMX will trigger refresh

    def get_object(self, queryset=None):
        # Override to get object based on pk which is MobileBill.id
        # or maybe the employee's user_id and month for unique identification
        # For simplicity, assuming PK is passed in URL
        return get_object_or_404(MobileBill, id=self.kwargs['pk'])

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Mobile Bill updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to close modal and trigger refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMobileBillTable'
                }
            )
        return response # Fallback for non-HTMX request

class MobileBillDeleteView(LoginRequiredMixin, DeleteView):
    model = MobileBill
    template_name = 'hr_mobile_bills/_mobilebill_confirm_delete.html' # Use partial for modal
    # success_url not needed as HTMX will trigger refresh

    def get_object(self, queryset=None):
        return get_object_or_404(MobileBill, id=self.kwargs['pk'])

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Mobile Bill deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMobileBillTable'
                }
            )
        return response # Fallback for non-HTMX request

```

#### 4.4 Templates (`hr_mobile_bills/templates/hr_mobile_bills/`)

**Explanation:**
- `mobilebill_list.html`: The main page that extends `core/base.html` and sets up the month filter dropdown with HTMX and the container for the dynamically loaded table. It includes the modal structure for add/edit/delete operations.
- `_mobilebill_table.html`: A partial template rendered by `MobileBillTablePartialView`. It contains the DataTables-enabled HTML table. Each row conditionally displays input fields (with Alpine.js for local visibility toggle) or static labels based on whether a bill exists for the selected month. The "Insert" button submits the entire form.
- `_mobilebill_form.html`: A partial for the `MobileBillForm`, used in a modal for updating individual bills.
- `_mobilebill_confirm_delete.html`: A partial for delete confirmation, also used in a modal.

**`hr_mobile_bills/templates/hr_mobile_bills/mobilebill_list.html`:**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Mobile Bills - New Entry</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex items-center space-x-4">
            <label for="id_bill_month" class="block text-sm font-medium text-gray-700">Month Of Bill:</label>
            {{ bill_month_form.bill_month }}
        </div>
    </div>
    
    <form hx-post="{% url 'hr_mobile_bills:mobilebill_table' %}" hx-swap="none" id="mobileBillForm">
        {% csrf_token %}
        <input type="hidden" name="bill_month_selected" value="" id="bill_month_selected_hidden">
        
        <div id="mobileBillTable-container"
             hx-trigger="load, refreshMobileBillTable from:body"
             hx-get="{% url 'hr_mobile_bills:mobilebill_table' %}"
             hx-target="#mobileBillTable-container"
             hx-swap="innerHTML">
            <!-- Initial loading state -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading mobile bill data...</p>
            </div>
        </div>
    </form>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
    >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto"
             _="on htmx:afterSwap put .is-active on #modal">
            <!-- Content will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterRequest', function(evt) {
        // Close modal after successful HTMX form submission
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.add('hidden'); // Close modal
            }
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        const billMonthDropdown = document.getElementById('id_bill_month');
        const hiddenBillMonthInput = document.getElementById('bill_month_selected_hidden');

        if (billMonthDropdown && hiddenBillMonthInput) {
            // Update hidden input on page load and on change
            hiddenBillMonthInput.value = billMonthDropdown.value;
            billMonthDropdown.addEventListener('change', function() {
                hiddenBillMonthInput.value = this.value;
            });
        }
    });
</script>
{% endblock %}
```

**`hr_mobile_bills/templates/hr_mobile_bills/_mobilebill_table.html`:**

```html
{% load humanize %} {# Optional: for better number formatting if needed #}

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="mobileBillTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-3%">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-3%">CK</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">Emp ID</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-25%">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Mobile No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Limit Amt</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Bill Amt</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Taxes</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Excess Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in employees_data %}
            <tr x-data="{ showInputs: false, billAmount: '', taxId: '{{ row.default_tax_id|default:"" }}' }">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {% if not row.existing_bill %}
                    <input type="checkbox" 
                           class="form-checkbox h-4 w-4 text-blue-600"
                           x-model="showInputs"
                           name="checkbox_selected_{{ row.staff.user_id }}">
                    {% else %}
                    <span class="text-gray-400" title="Bill already exists"></span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ row.staff.emp_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ row.staff.employee_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ row.mobile_details.mobile_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.mobile_details.limit_amount|intcomma }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {% if row.existing_bill %}
                        {{ row.existing_bill.bill_amount|intcomma }}
                    {% else %}
                        <input type="number" step="0.01" min="0" 
                               name="bill_amount_{{ row.staff.user_id }}" 
                               x-model="billAmount"
                               x-bind:class="showInputs ? 'block' : 'hidden'" 
                               class="w-28 border border-gray-300 rounded-md px-2 py-1 text-right text-sm"
                               placeholder="0.00">
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {% if row.existing_bill %}
                        {{ row.existing_bill.tax.value|intcomma }}%
                    {% else %}
                        <select name="tax_id_{{ row.staff.user_id }}" 
                                x-model="taxId"
                                x-bind:class="showInputs ? 'block' : 'hidden'"
                                class="w-28 border border-gray-300 rounded-md px-2 py-1 text-sm">
                            {% for tax in row.taxes_options %}
                                <option value="{{ tax.id }}" {% if tax.id == row.default_tax_id %}selected{% endif %}>{{ tax.value|intcomma }}%</option>
                            {% endfor %}
                        </select>
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {% if row.existing_bill %}
                        <span class="text-red-600 font-bold">{{ row.excess_amount|intcomma }}</span>
                    {% else %}
                        <span x-show="showInputs" class="text-sm text-gray-500">Calculate</span>
                        <span x-show="!showInputs" class="text-sm text-gray-500">N/A</span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {% if row.existing_bill %}
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md text-xs"
                            hx-get="{% url 'hr_mobile_bills:mobilebill_edit' pk=row.existing_bill.id %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-xs mt-1"
                            hx-get="{% url 'hr_mobile_bills:mobilebill_delete' pk=row.existing_bill.id %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    {% else %}
                        <span class="text-gray-400">N/A</span>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="10" class="py-4 px-4 bg-gray-50 text-center">
                    {% if not employees_data %}
                        <span class="text-gray-500">No employees found matching criteria.</span>
                    {% else %}
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                                hx-confirm="Are you sure you want to insert selected mobile bills?"
                                hx-target="#mobileBillTable-container" {# Target self to update table after submit #}
                                hx-swap="innerHTML show:top" {# Swap content and scroll to top of target #}
                                hx-indicator="#loadingIndicator"> {# Show a loading indicator #}
                            Insert Selected Bills
                        </button>
                        <div id="loadingIndicator" class="htmx-indicator ml-3 inline-block">
                            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                            <span class="ml-2">Processing...</span>
                        </div>
                    {% endif %}
                </td>
            </tr>
        </tfoot>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX swaps the table content
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#mobileBillTable')) {
            $('#mobileBillTable').DataTable().destroy();
        }
        $('#mobileBillTable').DataTable({
            "pageLength": 20, // Corresponds to ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "ordering": true,
            "searching": true,
            "paging": true,
            "info": true
        });
    });

    // Ensure the hidden month input is updated when the dropdown changes
    document.getElementById('id_bill_month').addEventListener('change', function() {
        document.getElementById('bill_month_selected_hidden').value = this.value;
    });
</script>
```

**`hr_mobile_bills/templates/hr_mobile_bills/_mobilebill_form.html`:**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Mobile Bill</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {# Hidden fields for context of the specific bill being edited #}
            <input type="hidden" name="employee" value="{{ form.instance.employee.user_id }}">
            <input type="hidden" name="bill_month" value="{{ form.instance.bill_month }}">
            <input type="hidden" name="comp_id" value="{{ form.instance.comp_id }}">
            <input type="hidden" name="fin_year_id" value="{{ form.instance.fin_year_id }}">

            {# Display employee name (read-only) #}
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Employee Name:</label>
                <p class="mt-1 text-gray-900">{{ form.instance.employee.employee_name }}</p>
            </div>

            {# Display bill month (read-only) #}
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Bill Month:</label>
                <p class="mt-1 text-gray-900">{{ form.instance.bill_month|date:"F" }}</p> {# Assuming bill_month is an integer representing month #}
            </div>

            {# Form fields for Bill Amount and Taxes #}
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**`hr_mobile_bills/templates/hr_mobile_bills/_mobilebill_confirm_delete.html`:**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the mobile bill for <strong>{{ object.employee.employee_name }}</strong> (Month: {{ object.bill_month }})?</p>
    
    <form hx-post="{% url 'hr_mobile_bills:mobilebill_delete' pk=object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`hr_mobile_bills/urls.py`)

**Explanation:** Defines URL patterns for the main list view, the HTMX partial for the table, and the individual CRUD operations for `MobileBill` instances.

```python
from django.urls import path
from .views import MobileBillListView, MobileBillTablePartialView, MobileBillUpdateView, MobileBillDeleteView

app_name = 'hr_mobile_bills'

urlpatterns = [
    # Main page for mobile bills, including month filter
    path('mobile_bills/', MobileBillListView.as_view(), name='mobilebill_list'),
    
    # HTMX endpoint for the mobile bill table content (reads and handles bulk inserts)
    path('mobile_bills/table/', MobileBillTablePartialView.as_view(), name='mobilebill_table'),
    
    # Individual MobileBill update (for existing entries)
    path('mobile_bills/edit/<int:pk>/', MobileBillUpdateView.as_view(), name='mobilebill_edit'),
    
    # Individual MobileBill delete (for existing entries)
    path('mobile_bills/delete/<int:pk>/', MobileBillDeleteView.as_view(), name='mobilebill_delete'),
]
```

#### 4.6 Tests (`hr_mobile_bills/tests.py`)

**Explanation:** Comprehensive unit tests for model methods (like `calculate_net_bill_amount` and `excess_amount`) and integration tests for all views (GET and POST requests, including HTMX-specific interactions and bulk inserts). A `LoginRequiredMixin` was added to views, so tests now require a logged-in user or a test client that simulates authentication.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from datetime import date, time
from decimal import Decimal

from .models import OfficeStaff, CorporateMobileNo, ExciseServiceTax, MobileBill
from .views import DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID # Access constants from views

User = get_user_model() # Get the currently active user model

class MobileBillModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data first
        cls.corp_mobile = CorporateMobileNo.objects.create(id=101, mobile_no='9876543210', limit_amount=Decimal('500.00'))
        cls.office_staff = OfficeStaff.objects.create(
            user_id=1, emp_id='EMP001', employee_name='John Doe', 
            mobile_no_id=cls.corp_mobile.id, comp_id=DEFAULT_COMP_ID
        )
        cls.excise_tax_10 = ExciseServiceTax.objects.create(id=1, value=Decimal('10.00'), live_service_tax=True, live=True)
        cls.excise_tax_0 = ExciseServiceTax.objects.create(id=2, value=Decimal('0.00'), live_service_tax=False, live=True)

        # Create a sample MobileBill
        cls.mobile_bill = MobileBill.objects.create(
            id=1,
            sys_date=date.today(),
            sys_time=time(10, 0, 0),
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            session_id='testuser',
            employee=cls.office_staff,
            bill_month=1, # January
            bill_amount=Decimal('550.00'), # Gross amount for 500 net + 10% tax
            tax=cls.excise_tax_10
        )
        # Create a bill that is below limit
        cls.mobile_bill_below_limit = MobileBill.objects.create(
            id=2,
            sys_date=date.today(),
            sys_time=time(11, 0, 0),
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            session_id='testuser',
            employee=OfficeStaff.objects.create(
                user_id=2, emp_id='EMP002', employee_name='Jane Smith',
                mobile_no_id=CorporateMobileNo.objects.create(id=102, mobile_no='9998887776', limit_amount=Decimal('600.00')).id,
                comp_id=DEFAULT_COMP_ID
            ),
            bill_month=1,
            bill_amount=Decimal('440.00'), # Gross amount for 400 net + 10% tax
            tax=cls.excise_tax_10
        )
        # Create a bill with zero tax
        cls.mobile_bill_zero_tax = MobileBill.objects.create(
            id=3,
            sys_date=date.today(),
            sys_time=time(12, 0, 0),
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            session_id='testuser',
            employee=OfficeStaff.objects.create(
                user_id=3, emp_id='EMP003', employee_name='Alice Brown',
                mobile_no_id=CorporateMobileNo.objects.create(id=103, mobile_no='1112223334', limit_amount=Decimal('300.00')).id,
                comp_id=DEFAULT_COMP_ID
            ),
            bill_month=1,
            bill_amount=Decimal('250.00'),
            tax=cls.excise_tax_0
        )

    def test_mobile_bill_creation(self):
        self.assertEqual(self.mobile_bill.bill_amount, Decimal('550.00'))
        self.assertEqual(self.mobile_bill.employee.employee_name, 'John Doe')
        self.assertEqual(self.mobile_bill.tax.value, Decimal('10.00'))

    def test_calculate_net_bill_amount(self):
        # Gross 550, Tax 10%. Net = 550 / (1 + 0.10) = 550 / 1.10 = 500
        self.assertEqual(self.mobile_bill.calculate_net_bill_amount(), Decimal('500.00'))
        # Gross 440, Tax 10%. Net = 440 / (1 + 0.10) = 440 / 1.10 = 400
        self.assertEqual(self.mobile_bill_below_limit.calculate_net_bill_amount(), Decimal('400.00'))
        # Gross 250, Tax 0%. Net = 250
        self.assertEqual(self.mobile_bill_zero_tax.calculate_net_bill_amount(), Decimal('250.00'))

    def test_excess_amount(self):
        # Mobile bill 1: Net 500, Limit 500. Excess = 0
        self.assertEqual(self.mobile_bill.excess_amount, Decimal('0.00'))
        
        # Change limit to create excess
        self.mobile_bill.employee.corporate_mobile_details.limit_amount = Decimal('450.00')
        self.mobile_bill.employee.corporate_mobile_details.save()
        self.assertEqual(self.mobile_bill.excess_amount, Decimal('50.00'))

        # Mobile bill below limit: Net 400, Limit 600. Excess = 0
        self.assertEqual(self.mobile_bill_below_limit.excess_amount, Decimal('0.00'))
        
        # Mobile bill with zero tax, below limit: Net 250, Limit 300. Excess = 0
        self.assertEqual(self.mobile_bill_zero_tax.excess_amount, Decimal('0.00'))
        
    def test_unique_together_constraint(self):
        # Attempt to create a duplicate bill for the same employee, month, company, fin year
        with self.assertRaises(Exception) as cm: # Expecting IntegrityError or similar DB error
            MobileBill.objects.create(
                id=4, # Assuming manual ID management, ensure it's unique here
                sys_date=date.today(),
                sys_time=time(13, 0, 0),
                comp_id=DEFAULT_COMP_ID,
                fin_year_id=DEFAULT_FIN_YEAR_ID,
                session_id='testuser',
                employee=self.office_staff, # Same employee
                bill_month=1, # Same month
                bill_amount=Decimal('600.00'),
                tax=self.excise_tax_10
            )
        # Check if the error message suggests a unique constraint violation if possible
        # (This is database specific, so just catching general Exception for legacy DBs)
        self.assertTrue("unique" in str(cm.exception).lower() or "duplicate" in str(cm.exception).lower())

class MobileBillViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user
        cls.user = User.objects.create_user(username='testuser', password='password123')

        # Create dependent data
        cls.corp_mobile_1 = CorporateMobileNo.objects.create(id=101, mobile_no='9876543210', limit_amount=Decimal('500.00'))
        cls.staff_1 = OfficeStaff.objects.create(
            user_id=1, emp_id='EMP001', employee_name='John Doe', 
            mobile_no_id=cls.corp_mobile_1.id, comp_id=DEFAULT_COMP_ID
        )
        cls.corp_mobile_2 = CorporateMobileNo.objects.create(id=102, mobile_no='9998887776', limit_amount=Decimal('600.00'))
        cls.staff_2 = OfficeStaff.objects.create(
            user_id=2, emp_id='EMP002', employee_name='Jane Smith', 
            mobile_no_id=cls.corp_mobile_2.id, comp_id=DEFAULT_COMP_ID
        )
        # An employee without mobile number with limit > 0
        cls.staff_no_mobile = OfficeStaff.objects.create(
            user_id=3, emp_id='EMP003', employee_name='No Mobile',
            mobile_no_id=999, # A non-existent mobile ID or one with limit 0
            comp_id=DEFAULT_COMP_ID
        )
        CorporateMobileNo.objects.create(id=999, mobile_no='0000000000', limit_amount=Decimal('0.00'))


        cls.excise_tax_10 = ExciseServiceTax.objects.create(id=1, value=Decimal('10.00'), live_service_tax=True, live=True)
        cls.excise_tax_15 = ExciseServiceTax.objects.create(id=2, value=Decimal('15.00'), live_service_tax=True, live=True)
        cls.excise_tax_inactive = ExciseServiceTax.objects.create(id=3, value=Decimal('5.00'), live_service_tax=True, live=False) # Not live

        # Create an existing bill for staff_1 for January
        cls.existing_bill = MobileBill.objects.create(
            id=100,
            sys_date=date.today(),
            sys_time=time(10, 0, 0),
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID,
            session_id='testuser',
            employee=cls.staff_1,
            bill_month=1,
            bill_amount=Decimal('550.00'),
            tax=cls.excise_tax_10
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')

    def test_list_view_get(self):
        response = self.client.get(reverse('hr_mobile_bills:mobilebill_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_mobile_bills/mobilebill_list.html')
        self.assertContains(response, 'Mobile Bills - New Entry') # Check for title

    def test_table_partial_view_get_no_month(self):
        response = self.client.get(reverse('hr_mobile_bills:mobilebill_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_mobile_bills/_mobilebill_table.html')
        # Check if table content is loaded, without specific month filtering
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Jane Smith')
        # Check that existing bill is shown as label, not inputs
        self.assertContains(response, '{{ row.existing_bill.bill_amount|intcomma }}')
        self.assertNotContains(response, 'name="bill_amount_EMP001"') # Should not have input for EMP001

    def test_table_partial_view_get_with_month(self):
        # Request for month 1 (January)
        response = self.client.get(reverse('hr_mobile_bills:mobilebill_table'), {'bill_month': 1}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_mobile_bills/_mobilebill_table.html')
        
        # John Doe (staff_1) has an existing bill for January
        self.assertContains(response, 'John Doe')
        self.assertContains(response, '550.00') # Existing bill amount for John Doe
        self.assertNotContains(response, 'name="bill_amount_EMP001"') # No input for John Doe

        # Jane Smith (staff_2) does NOT have an existing bill for January
        self.assertContains(response, 'Jane Smith')
        self.assertContains(response, 'name="bill_amount_2"') # Should show input for Jane Smith
        self.assertContains(response, 'name="tax_id_2"') # Should show tax dropdown for Jane Smith
        
        # Inactive tax should not be in options for new entries
        self.assertNotContains(response, '<option value="3">5.00%</option>')


    def test_table_partial_view_post_bulk_insert_success(self):
        # Data for inserting a bill for Jane Smith (EMP002) for February
        initial_bill_count = MobileBill.objects.count()
        post_data = {
            'bill_month_selected': '2', # February
            'bill_amount_2': '660.00', # Gross amount for 600 net + 10% tax
            'tax_id_2': self.excise_tax_10.id,
            'checkbox_selected_2': 'on', # Simulate checkbox checked
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hr_mobile_bills:mobilebill_table'), post_data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(MobileBill.objects.count(), initial_bill_count + 1)
        
        new_bill = MobileBill.objects.get(employee=self.staff_2, bill_month=2)
        self.assertEqual(new_bill.bill_amount, Decimal('660.00'))
        self.assertEqual(new_bill.tax.value, Decimal('10.00'))
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMobileBillTable', response.headers['HX-Trigger'])

    def test_table_partial_view_post_bulk_insert_existing_bill(self):
        # Attempt to insert a bill for John Doe for January, which already exists
        initial_bill_count = MobileBill.objects.count()
        post_data = {
            'bill_month_selected': '1', # January
            'bill_amount_1': '600.00',
            'tax_id_1': self.excise_tax_10.id,
            'checkbox_selected_1': 'on',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hr_mobile_bills:mobilebill_table'), post_data, **headers)

        self.assertEqual(response.status_code, 204) # Still 204 for HTMX, errors handled by messages
        self.assertEqual(MobileBill.objects.count(), initial_bill_count) # No new bill should be created
        
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertTrue(any("Bill already exists" in str(m) for m in messages_list))
        self.assertIn('HX-Trigger', response.headers)

    def test_table_partial_view_post_bulk_insert_invalid_data(self):
        # Attempt to insert with invalid bill amount
        initial_bill_count = MobileBill.objects.count()
        post_data = {
            'bill_month_selected': '3', # March
            'bill_amount_2': '-100.00', # Invalid negative amount
            'tax_id_2': self.excise_tax_10.id,
            'checkbox_selected_2': 'on',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hr_mobile_bills:mobilebill_table'), post_data, **headers)

        self.assertEqual(response.status_code, 204)
        self.assertEqual(MobileBill.objects.count(), initial_bill_count)
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertTrue(any("Invalid bill amount" in str(m) for m in messages_list))
        self.assertIn('HX-Trigger', response.headers)
        
    def test_update_view_get(self):
        url = reverse('hr_mobile_bills:mobilebill_edit', args=[self.existing_bill.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_mobile_bills/_mobilebill_form.html')
        self.assertContains(response, 'Edit Mobile Bill')
        self.assertContains(response, 'name="bill_amount"')
        self.assertContains(response, f'value="{self.existing_bill.bill_amount}"')

    def test_update_view_post_success(self):
        url = reverse('hr_mobile_bills:mobilebill_edit', args=[self.existing_bill.id])
        updated_amount = Decimal('600.00')
        post_data = {
            'bill_amount': updated_amount,
            'tax': self.excise_tax_15.id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, post_data, **headers)

        self.assertEqual(response.status_code, 204)
        self.existing_bill.refresh_from_db()
        self.assertEqual(self.existing_bill.bill_amount, updated_amount)
        self.assertEqual(self.existing_bill.tax, self.excise_tax_15)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMobileBillTable', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        url = reverse('hr_mobile_bills:mobilebill_edit', args=[self.existing_bill.id])
        post_data = {
            'bill_amount': '-100.00', # Invalid negative amount
            'tax': self.excise_tax_10.id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, post_data, **headers)
        
        self.assertEqual(response.status_code, 200) # Form errors will re-render partial with 200
        self.assertTemplateUsed(response, 'hr_mobile_bills/_mobilebill_form.html')
        self.assertContains(response, 'Bill amount cannot be negative.') # Check for form error message

    def test_delete_view_get(self):
        url = reverse('hr_mobile_bills:mobilebill_delete', args=[self.existing_bill.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_mobile_bills/_mobilebill_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.existing_bill.employee.employee_name)

    def test_delete_view_post_success(self):
        url = reverse('hr_mobile_bills:mobilebill_delete', args=[self.existing_bill.id])
        initial_bill_count = MobileBill.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(MobileBill.objects.count(), initial_bill_count - 1)
        self.assertFalse(MobileBill.objects.filter(id=self.existing_bill.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMobileBillTable', response.headers['HX-Trigger'])

    def test_access_denied_for_unauthenticated_users(self):
        self.client.logout() # Log out the test user

        # Test list view
        response = self.client.get(reverse('hr_mobile_bills:mobilebill_list'))
        self.assertEqual(response.status_code, 302) # Should redirect to login

        # Test table partial view
        response = self.client.get(reverse('hr_mobile_bills:mobilebill_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 302) # Should redirect to login

        # Test update view
        response = self.client.get(reverse('hr_mobile_bills:mobilebill_edit', args=[self.existing_bill.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 302) # Should redirect to login

        # Test delete view
        response = self.client.get(reverse('hr_mobile_bills:mobilebill_delete', args=[self.existing_bill.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 302) # Should redirect to login
```

---

### Step 5: HTMX and Alpine.js Integration

**Explanation:**

*   **HTMX:**
    *   The `BillMonthForm`'s `select` element uses `hx-get` to `mobilebill_table` and `hx-target`/`hx-swap` to update the `mobileBillTable-container` div when its value changes. This replaces the ASP.NET `AutoPostBack`.
    *   The "Insert Selected Bills" button inside the `_mobilebill_table.html` form uses `hx-post` to the same `mobilebill_table` URL. This view then handles the bulk insert logic. `hx-swap="none"` combined with `HX-Trigger` header in the Django view ensures the modal closes and the table refreshes after successful submission.
    *   Edit and Delete buttons in `_mobilebill_table.html` use `hx-get` to fetch the respective form/confirmation partials into a modal (`#modalContent`).
    *   `HX-Trigger: refreshMobileBillTable` header is sent by the Django views (`MobileBillTablePartialView` after bulk insert, `MobileBillUpdateView`, `MobileBillDeleteView`) upon successful operation, which then triggers a re-fetch of the `mobileBillTable-container` to update the list.
    *   `hx-indicator` is used to show a loading spinner during HTMX requests.

*   **Alpine.js:**
    *   In `_mobilebill_table.html`, each table row `<tr>` uses `x-data="{ showInputs: false, billAmount: '', taxId: '' }"`.
    *   The "CK" checkbox (`<input type="checkbox" x-model="showInputs">`) controls the `showInputs` state for that specific row.
    *   The `TxtBillAmt` (`<input type="number" x-bind:class="showInputs ? 'block' : 'hidden'">`) and `DDLTaxes` (`<select x-bind:class="showInputs ? 'block' : 'hidden'">`) fields are conditionally shown/hidden based on `showInputs` using `x-bind:class`. This replicates the ASP.NET `Visible` property behavior client-side without full page postbacks.
    *   The main modal in `mobilebill_list.html` uses `x-data` and `x-show` for simple visibility control and `x-transition` for smooth opening/closing animations. The `on click` listener with `_` (Hyperscript) provides a fallback way to hide the modal if the backdrop is clicked.

*   **DataTables:**
    *   The `<table>` element in `_mobilebill_table.html` has `id="mobileBillTable"`.
    *   A `$(document).ready()` block within the `_mobilebill_table.html` script tag (which runs after HTMX injects the content) initializes DataTables on this table ID. `destroy()` is called first to prevent re-initialization issues.
    *   DataTables handles client-side searching, sorting, and pagination, providing an enhanced user experience over the basic ASP.NET GridView.

---

### Final Notes

This comprehensive plan transforms the legacy ASP.NET Mobile Bills application into a modern, efficient Django solution. By meticulously analyzing the existing code and applying Django best practices, we ensure a smooth migration focused on automation, maintainability, and enhanced user experience. The emphasis on "fat models, thin views," HTMX, Alpine.js, and DataTables will result in a robust, performant, and scalable system ready for future growth.