## ASP.NET to Django Conversion Script: News & Notices Module

This document outlines a comprehensive modernization plan to transition the existing ASP.NET News & Notices module to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI for execution, and focuses on delivering business value through a streamlined, maintainable, and scalable application.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET code-behind's `BtnUpload_Click` method contains the `fun.insert` statement, which explicitly names the target table and its columns.

- **Table Name:** `tblHR_News_Notices`

- **Identified Columns:**
    - `SysDate`: System date of creation (from `fun.getCurrDate()`).
    - `SysTime`: System time of creation (from `fun.getCurrTime()`).
    - `SessionId`: User/session ID (from `Session["username"]`).
    - `CompId`: Company ID (from `Session["compid"]`).
    - `FinYearId`: Financial Year ID (from `Session["finyear"]`).
    - `Title`: News/Notice title (from `TxtNewsTitle.Text`).
    - `InDetails`: News/Notice description (from `txtNews.Text`).
    - `FromDate`: Start date for display (from `TxtFromDate.Text`).
    - `ToDate`: End date for display (from `TxtToDate.Text`).
    - `FileName`: Name of the uploaded file (from `Path.GetFileName(myfile.FileName)`).
    - `FileSize`: Size of the uploaded file (from `mydata.Length`).
    - `ContentType`: MIME type of the uploaded file (from `myfile.ContentType`).
    - `FileData`: Binary content of the uploaded file (from `mydata`).
    - An implicit `id` (primary key) column is assumed for ORM mapping.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and validation logic in the ASP.NET code.

**Instructions:**
The provided ASP.NET page is primarily focused on creating new "News & Notices" entries.

-   **Create (Add New):**
    -   **Trigger:** `BtnUpload_Click` event.
    -   **Inputs:** `TxtNewsTitle`, `txtNews`, `TxtFromDate`, `TxtToDate`, `FileUpload1`.
    -   **Data Processing:** Retrieves current date/time, user session details, parses dates, reads file data.
    -   **Database Operation:** `INSERT` into `tblHR_News_Notices` with all identified columns.
    -   **Validation:**
        -   `TxtNewsTitle`, `txtNews`, `TxtFromDate`, `TxtToDate` are required.
        -   `TxtFromDate` and `TxtToDate` are validated for `dd-MM-yyyy` format using `RegularExpressionValidator` and `fun.DateValidation()`.
    -   **File Handling:** Reads file into a byte array (`mydata`) and stores its name, size, type, and content directly in the database.
    -   **Redirection:** Redirects to the same page (`Page.Request.Url.ToString()`) after successful submission.

-   **Read (List/View):** Not explicitly shown in this `.aspx` file, but a "News & Notices" module would require a list view to display existing items. This will be implemented using DataTables.

-   **Update (Edit):** Not explicitly shown, but implied for a complete CRUD module.

-   **Delete:** Not explicitly shown, but implied for a complete CRUD module.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django template mapping.

**Instructions:**
The ASP.NET controls will be mapped to standard HTML input types and styled with Tailwind CSS, with dynamic behavior handled by HTMX and Alpine.js.

-   **`TxtNewsTitle` (TextBox):** Will become an `<input type="text">` for the news title.
-   **`txtNews` (TextBox, TextMode="MultiLine"):** Will become a `<textarea>` for the news description.
-   **`TxtFromDate` (TextBox) & `TxtToDate` (TextBox) with `CalendarExtender`:** Will become `<input type="date">` fields. To enforce `dd-MM-yyyy` format, `input_formats` will be set in the Django form.
-   **`FileUpload1` (FileUpload):** Will become an `<input type="file">` for file uploads.
-   **`BtnUpload` (Button):** Will become a `<button type="submit">` for form submission.
-   **`RequiredFieldValidator` & `RegularExpressionValidator`:** These validations will be replicated in Django forms (`required=True` and custom `RegexValidator` or `input_formats`).
-   **General Layout:** The table-based layout will be converted to a modern flexbox/grid layout using Tailwind CSS for responsiveness and clarity.
-   **Master Page (`MasterPage.master`):** Corresponds to Django's base template inheritance (e.g., `core/base.html`).

### Step 4: Generate Django Code

**Application Name:** `hr_news`
**Model Name:** `NewsNotice`
**Friendly Name:** News Notice
**Friendly Name Plural:** News Notices
**Model Name Lower:** `newsnotice`
**Model Name Plural Lower:** `newsnotices`

#### 4.1 Models (`hr_news/models.py`)

**Task:** Create a Django model `NewsNotice` based on `tblHR_News_Notices`. Business logic for populating system fields and handling file data will be embedded here.

```python
from django.db import models
from datetime import date, time
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings

class NewsNotice(models.Model):
    # Core fields derived from ASP.NET page
    title = models.CharField(db_column='Title', max_length=255, verbose_name="Title")
    in_details = models.TextField(db_column='InDetails', verbose_name="Description")
    from_date = models.DateField(db_column='FromDate', verbose_name="Date on Display (From)")
    to_date = models.DateField(db_column='ToDate', verbose_name="Date on Display (To)")

    # File Upload Fields (storing binary data directly as per original ASP.NET)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True, verbose_name="File Name")
    file_size = models.IntegerField(db_column='FileSize', blank=True, null=True, verbose_name="File Size (Bytes)")
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True, verbose_name="Content Type")
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True, verbose_name="File Data")

    # System/Audit Fields (populated automatically via save_with_context)
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True, verbose_name="System Date")
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True, verbose_name="System Time")
    session_id = models.CharField(db_column='SessionId', max_length=255, verbose_name="Session User ID") # Maps to username
    comp_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")
    fin_year_id = models.IntegerField(db_column='FinYearId', verbose_name="Financial Year ID")

    class Meta:
        managed = False  # Set to False as it maps to an existing database table
        db_table = 'tblHR_News_Notices'
        verbose_name = 'News Notice'
        verbose_name_plural = 'News Notices'
        ordering = ['-sys_date', '-sys_time'] # Default ordering for list views

    def __str__(self):
        return self.title if self.title else f"News Notice {self.pk}"

    def save_with_context(self, uploaded_file: UploadedFile = None, request=None, *args, **kwargs):
        """
        Custom save method to handle populating system fields and file data
        from the request context, adhering to 'fat model' principle.
        """
        # Populate system fields from request, mirroring ASP.NET Session variables
        if request and request.user.is_authenticated:
            self.session_id = request.user.username
            # Assuming company_id and financial_year_id are attributes of the User model
            # or a related profile. Use defaults if not found.
            self.comp_id = getattr(request.user, 'company_id', 1) # Default to 1 if not found
            self.fin_year_id = getattr(request.user, 'financial_year_id', 1) # Default to 1 if not found
        else:
            # Fallback for anonymous users or testing context
            self.session_id = 'anonymous_user'
            self.comp_id = 1
            self.fin_year_id = 1

        # Handle file upload data
        if uploaded_file:
            self.file_name = uploaded_file.name
            self.file_size = uploaded_file.size
            self.content_type = uploaded_file.content_type
            # Read the entire file content into the BinaryField
            self.file_data = uploaded_file.read()
        else:
            # If no file is uploaded, ensure fields are null/empty for clarity
            if not self.pk: # Only clear if it's a new instance and no file
                self.file_name = None
                self.file_size = None
                self.content_type = None
                self.file_data = None


        # Let Django handle auto_now_add for sys_date and sys_time
        super().save(*args, **kwargs)

    def get_file_download_url(self):
        """
        Provides a URL to download the associated file data.
        This would typically map to a view that streams the file_data.
        """
        if self.file_name and self.file_data:
            return f"/hr-news/newsnotice/{self.pk}/download/"
        return None

```

#### 4.2 Forms (`hr_news/forms.py`)

**Task:** Define a Django `ModelForm` for `NewsNotice`, including widgets with Tailwind CSS classes and specific date input handling.

```python
from django import forms
from .models import NewsNotice
from django.core.validators import RegexValidator

class NewsNoticeForm(forms.ModelForm):
    # Add a separate FileField for upload, which will be processed in the model's save method
    upload_file = forms.FileField(
        required=False,
        label="Upload File",
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none',
            'hx-post': 'true', # Example for HTMX if file upload needs special handling (not directly used here)
        })
    )

    class Meta:
        model = NewsNotice
        fields = ['title', 'in_details', 'from_date', 'to_date'] # Exclude system and file data fields
        # Widgets with Tailwind CSS classes
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter news title',
            }),
            'in_details': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-28',
                'placeholder': 'Enter news description',
            }),
            'from_date': forms.DateInput(
                attrs={
                    'type': 'date', # HTML5 date picker
                    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                    'readonly': 'readonly' # Mimic ASP.NET readonly attribute
                },
                format='%Y-%m-%d' # Format for HTML5 date input value
            ),
            'to_date': forms.DateInput(
                attrs={
                    'type': 'date', # HTML5 date picker
                    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                    'readonly': 'readonly' # Mimic ASP.NET readonly attribute
                },
                format='%Y-%m-%d' # Format for HTML5 date input value
            ),
        }

    # Custom clean methods for date validation and consistency, mirroring ASP.NET RegExValidator
    def clean_from_date(self):
        from_date = self.cleaned_data['from_date']
        # No explicit RegExValidator needed if using type="date" and Django's DateInput.
        # Django's DateField handles basic date validity.
        # If the input format was dd-MM-yyyy for text input, input_formats would be crucial.
        # Since we use type="date", browsers enforce YYYY-MM-DD input.
        return from_date

    def clean_to_date(self):
        to_date = self.cleaned_data['to_date']
        from_date = self.cleaned_data.get('from_date')

        if from_date and to_date and to_date < from_date:
            raise forms.ValidationError("To Date cannot be earlier than From Date.")
        return to_date

```

#### 4.3 Views (`hr_news/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views, keeping methods concise and delegating logic to the model. An additional view for serving binary file data is also included.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect, Http404
from django.shortcuts import get_object_or_404
from .models import NewsNotice
from .forms import NewsNoticeForm
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

class NewsNoticeListView(ListView):
    model = NewsNotice
    template_name = 'hr_news/newsnotice/list.html'
    context_object_name = 'news_notices'

class NewsNoticeTablePartialView(ListView):
    """
    Renders only the table rows, specifically for HTMX updates.
    """
    model = NewsNotice
    template_name = 'hr_news/newsnotice/_newsnotice_table.html'
    context_object_name = 'news_notices'

class NewsNoticeCreateView(CreateView):
    model = NewsNotice
    form_class = NewsNoticeForm
    template_name = 'hr_news/newsnotice/form.html'
    success_url = reverse_lazy('newsnotice_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # 1. Prepare instance without saving to DB yet (commit=False)
        self.object = form.save(commit=False)

        # 2. Get uploaded file from request (form doesn't save it directly to model's BinaryField)
        uploaded_file = self.request.FILES.get('upload_file')

        # 3. Delegate system fields and file data handling to the model
        self.object.save_with_context(uploaded_file=uploaded_file, request=self.request)

        # 4. Add success message
        messages.success(self.request, 'News & Notice added successfully.')

        # 5. Handle HTMX response
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,  # No content, signals HTMX to just trigger headers
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList' # Custom event for HTMX to listen to
                }
            )
        # 6. Fallback for non-HTMX requests (standard redirect)
        return HttpResponseRedirect(self.get_success_url())

class NewsNoticeUpdateView(UpdateView):
    model = NewsNotice
    form_class = NewsNoticeForm
    template_name = 'hr_news/newsnotice/form.html'
    success_url = reverse_lazy('newsnotice_list') # Fallback for non-HTMX requests

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # If there's an existing file, pass its data to the form for display, if needed.
        # For simplicity, we are not pre-populating file input for update.
        # A new file upload will replace the old one.
        return kwargs

    def form_valid(self, form):
        # 1. Prepare instance without saving to DB yet (commit=False)
        self.object = form.save(commit=False)

        # 2. Get uploaded file from request (if any new file is uploaded)
        uploaded_file = self.request.FILES.get('upload_file')

        # 3. Delegate system fields and file data handling to the model
        # Note: sys_date, sys_time, session_id, comp_id, fin_year_id are not updated on update.
        # The save_with_context needs to distinguish between create/update for these fields.
        # For update, we only update file_data if a new file is provided.
        if uploaded_file:
            self.object.save_with_context(uploaded_file=uploaded_file, request=self.request)
        else:
            self.object.save() # Just save the instance if no new file or system fields update

        # 4. Add success message
        messages.success(self.request, 'News & Notice updated successfully.')

        # 5. Handle HTMX response
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList'
                }
            )
        # 6. Fallback for non-HTMX requests
        return HttpResponseRedirect(self.get_success_url())

class NewsNoticeDeleteView(DeleteView):
    model = NewsNotice
    template_name = 'hr_news/newsnotice/confirm_delete.html'
    success_url = reverse_lazy('newsnotice_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        # Perform the actual delete operation
        response = super().delete(request, *args, **kwargs)

        # Add success message
        messages.success(self.request, 'News & Notice deleted successfully.')

        # Handle HTMX response
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshNewsNoticeList'
                }
            )
        # Fallback for non-HTMX requests
        return response # super().delete() returns HttpResponseRedirect normally

class NewsNoticeDownloadFileView(View):
    """
    View to stream the binary file data stored in the database.
    """
    def get(self, request, pk):
        news_notice = get_object_or_404(NewsNotice, pk=pk)
        if not news_notice.file_data:
            raise Http404("No file attached to this news notice.")

        response = HttpResponse(news_notice.file_data, content_type=news_notice.content_type)
        response['Content-Disposition'] = f'attachment; filename="{news_notice.file_name}"'
        response['Content-Length'] = news_notice.file_size
        return response

```

#### 4.4 Templates (`hr_news/templates/hr_news/newsnotice/`)

**Task:** Create reusable templates for listing, adding/editing, and deleting News Notices, utilizing HTMX, Alpine.js, and DataTables.

**File: `hr_news/templates/hr_news/newsnotice/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 sm:mb-0">News Notices</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'newsnotice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
        >
            Add New News Notice
        </button>
    </div>

    <div
        id="newsnoticeTable-container"
        hx-trigger="load, refreshNewsNoticeList from:body"
        hx-get="{% url 'newsnotice_table' %}"
        hx-swap="innerHTML"
        class="bg-white shadow-lg rounded-lg overflow-hidden"
    >
        <!-- Initial loading state for HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading News Notices...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden items-center justify-center transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then wait 200ms then add .hidden to me and remove .flex from me">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 my-8 transform transition-all duration-300 scale-95 opacity-0"
             _="on modal shown set my @class to 'bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 my-8 transform transition-all duration-300 scale-100 opacity-100'">
            <!-- Content loaded via HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js is typically loaded in base.html -->
<script>
    // Example Alpine.js for general UI state if needed, but HTMX handles most of it
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });
</script>
{% endblock %}
```

**File: `hr_news/templates/hr_news/newsnotice/_newsnotice_table.html`**
```html
<div class="p-6">
    <table id="newsnoticeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in news_notices %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.title }}</td>
                <td class="py-4 px-6 whitespace-normal text-sm text-gray-900">{{ obj.in_details|truncatechars:100 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.from_date|date:"d-m-Y" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.to_date|date:"d-m-Y" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm">
                    {% if obj.file_name %}
                        <a href="{{ obj.get_file_download_url }}" class="text-blue-600 hover:underline">{{ obj.file_name }} ({{ obj.file_size|floatformat:0 }} bytes)</a>
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
                    >
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No news notices found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    // Check if jQuery is loaded (usually by DataTables CDN in base.html)
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(function($) {
            // Destroy existing DataTable instance if it exists to avoid reinitialization errors
            if ($.fn.DataTable.isDataTable('#newsnoticeTable')) {
                $('#newsnoticeTable').DataTable().destroy();
            }
            $('#newsnoticeTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "responsive": true // Make table responsive
            });
        });
    } else {
        console.warn("jQuery or DataTables not loaded. DataTables initialization skipped.");
    }
</script>
```

**File: `hr_news/templates/hr_news/newsnotice/form.html`**
```html
<div class="p-6 bg-white rounded-xl shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 text-center">{{ form.instance.pk|yesno:'Edit,Add' }} News Notice</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-encoding="multipart/form-data">
        {% csrf_token %}
        
        <div class="space-y-5">
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.title.label }}
                    {% if form.title.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.title.errors|join:", " }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.in_details.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.in_details.label }}
                    {% if form.in_details.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.in_details }}
                {% if form.in_details.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.in_details.errors|join:", " }}</p>
                {% endif %}
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.from_date.label }}
                        {% if form.from_date.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors|join:", " }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.to_date.label }}
                        {% if form.to_date.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors|join:", " }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div>
                <label for="{{ form.upload_file.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.upload_file.label }}
                    {% if form.upload_file.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.upload_file }}
                {% if form.instance.file_name and not form.instance.file_data == None %}
                    <p class="text-xs text-gray-500 mt-1">Current file: <a href="{{ form.instance.get_file_download_url }}" class="text-blue-600 hover:underline">{{ form.instance.file_name }}</a></p>
                {% endif %}
                {% if form.upload_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.upload_file.errors|join:", " }}</p>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
            <div class="p-4 text-red-700 bg-red-100 rounded-lg" role="alert">
                <p class="font-bold">Error:</p>
                {{ form.non_field_errors }}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                _="on click remove .opacity-100 from #modal then wait 200ms then add .hidden to #modal and remove .flex from #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
            >
                {{ form.instance.pk|yesno:'Update,Create' }} News Notice
            </button>
        </div>
    </form>
</div>
```

**File: `hr_news/templates/hr_news/newsnotice/confirm_delete.html`**
```html
<div class="p-6 bg-white rounded-xl shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 text-center">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8 text-center">Are you sure you want to delete the news notice: <strong>"{{ newsnotice.title }}"</strong>?</p>
    
    <form hx-post="{% url 'newsnotice_delete' newsnotice.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                _="on click remove .opacity-100 from #modal then wait 200ms then add .hidden to #modal and remove .flex from #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out"
            >
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`hr_news/urls.py`)

**Task:** Define URL patterns for all News Notice views, including HTMX partials and the file download endpoint.

```python
from django.urls import path
from .views import (
    NewsNoticeListView,
    NewsNoticeTablePartialView,
    NewsNoticeCreateView,
    NewsNoticeUpdateView,
    NewsNoticeDeleteView,
    NewsNoticeDownloadFileView,
)

urlpatterns = [
    # List view for News Notices
    path('newsnotice/', NewsNoticeListView.as_view(), name='newsnotice_list'),
    
    # HTMX endpoint for the table content
    path('newsnotice/table/', NewsNoticeTablePartialView.as_view(), name='newsnotice_table'),
    
    # Create new News Notice
    path('newsnotice/add/', NewsNoticeCreateView.as_view(), name='newsnotice_add'),
    
    # Edit existing News Notice
    path('newsnotice/edit/<int:pk>/', NewsNoticeUpdateView.as_view(), name='newsnotice_edit'),
    
    # Delete News Notice
    path('newsnotice/delete/<int:pk>/', NewsNoticeDeleteView.as_view(), name='newsnotice_delete'),

    # Download attached file
    path('newsnotice/<int:pk>/download/', NewsNoticeDownloadFileView.as_view(), name='newsnotice_download'),
]
```

#### 4.6 Tests (`hr_news/tests.py`)

**Task:** Write comprehensive unit tests for the `NewsNotice` model and integration tests for all News Notice views, ensuring robust coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import NewsNotice
from datetime import date, time
from io import BytesIO
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth.models import User # Assuming Django's User model for SessionId

class NewsNoticeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user for session context
        cls.test_user = User.objects.create_user(username='testuser', password='password')
        # Simulate user profile data if it were to exist
        cls.test_user.company_id = 101
        cls.test_user.financial_year_id = 2023

        # Create a sample NewsNotice without file data initially
        NewsNotice.objects.create(
            title='Initial News',
            in_details='Details of initial news.',
            from_date=date(2023, 1, 1),
            to_date=date(2023, 1, 31),
            session_id='setup_user', # Direct set for setup data
            comp_id=1,
            fin_year_id=1,
        )
        # Create another for testing file upload later
        NewsNotice.objects.create(
            title='File Test News',
            in_details='News for file upload testing.',
            from_date=date(2024, 1, 1),
            to_date=date(2024, 1, 31),
            session_id='setup_user',
            comp_id=1,
            fin_year_id=1,
        )

    def test_news_notice_creation(self):
        obj = NewsNotice.objects.get(pk=1)
        self.assertEqual(obj.title, 'Initial News')
        self.assertEqual(obj.in_details, 'Details of initial news.')
        self.assertEqual(obj.from_date, date(2023, 1, 1))
        self.assertEqual(obj.to_date, date(2023, 1, 31))
        self.assertIsNotNone(obj.sys_date)
        self.assertIsNotNone(obj.sys_time)
        self.assertEqual(obj.session_id, 'setup_user')
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.fin_year_id, 1)
        self.assertIsNone(obj.file_data)
        self.assertIsNone(obj.file_name)

    def test_news_notice_str_method(self):
        obj = NewsNotice.objects.get(pk=1)
        self.assertEqual(str(obj), 'Initial News')

    def test_save_with_context_no_file(self):
        # Test save_with_context without a file and with request context
        new_obj = NewsNotice(
            title='Context News',
            in_details='Details from context news.',
            from_date=date(2023, 2, 1),
            to_date=date(2023, 2, 28),
        )
        
        # Simulate a request object
        class MockRequest:
            def __init__(self, user):
                self.user = user
                self.FILES = {} # No files for this test

        mock_request = MockRequest(self.test_user)
        
        new_obj.save_with_context(request=mock_request)
        new_obj.refresh_from_db()

        self.assertEqual(new_obj.session_id, 'testuser')
        self.assertEqual(new_obj.comp_id, 1) # Default from model if no profile
        self.assertEqual(new_obj.fin_year_id, 1) # Default from model if no profile
        self.assertIsNone(new_obj.file_data)

    def test_save_with_context_with_file(self):
        # Test save_with_context with file data
        obj_with_file = NewsNotice.objects.get(pk=2) # Use the second object
        file_content = b"This is a test file content."
        uploaded_file = SimpleUploadedFile(
            "test_document.txt",
            file_content,
            content_type="text/plain"
        )
        
        class MockRequest:
            def __init__(self, user):
                self.user = user
                self.FILES = {'upload_file': uploaded_file} # Simulate file in request

        mock_request = MockRequest(self.test_user)

        # Call save_with_context directly
        obj_with_file.save_with_context(uploaded_file=uploaded_file, request=mock_request)
        obj_with_file.refresh_from_db()

        self.assertEqual(obj_with_file.file_name, "test_document.txt")
        self.assertEqual(obj_with_file.file_size, len(file_content))
        self.assertEqual(obj_with_file.content_type, "text/plain")
        self.assertEqual(obj_with_file.file_data, file_content)
        self.assertEqual(obj_with_file.session_id, 'testuser')


class NewsNoticeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user and log them in for view tests
        cls.user = User.objects.create_user(username='viewer', password='testpassword')
        cls.user.company_id = 101 # Simulate custom user attribute
        cls.user.financial_year_id = 2023 # Simulate custom user attribute

        # Create sample news notices for listing and other operations
        for i in range(1, 4):
            NewsNotice.objects.create(
                title=f'News Item {i}',
                in_details=f'Details for news item {i}.',
                from_date=date(2023, 1, i),
                to_date=date(2023, 1, i + 10),
                session_id='testuser',
                comp_id=1,
                fin_year_id=1,
            )

    def setUp(self):
        self.client = Client()
        self.client.login(username='viewer', password='testpassword') # Log in the test user for all tests

    def test_list_view(self):
        response = self.client.get(reverse('newsnotice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_news/newsnotice/list.html')
        self.assertTrue('news_notices' in response.context)
        self.assertEqual(len(response.context['news_notices']), 3)

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('newsnotice_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_news/newsnotice/_newsnotice_table.html')
        self.assertTrue('news_notices' in response.context)
        self.assertEqual(len(response.context['news_notices']), 3)
        self.assertContains(response, '<tbody>') # Check for table body rendering

    def test_create_view_get(self):
        response = self.client.get(reverse('newsnotice_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_news/newsnotice/form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        file_content = b"Sample file for upload."
        uploaded_file = SimpleUploadedFile("sample.txt", file_content, content_type="text/plain")

        data = {
            'title': 'New News Item',
            'in_details': 'Details for new news item.',
            'from_date': '2023-03-01',
            'to_date': '2023-03-15',
            'upload_file': uploaded_file, # Pass the file
        }
        response = self.client.post(reverse('newsnotice_add'), data, follow=True) # follow=True to check redirect
        self.assertEqual(response.status_code, 200) # Should be 200 after redirect
        self.assertRedirects(response, reverse('newsnotice_list'))
        self.assertTrue(NewsNotice.objects.filter(title='New News Item').exists())

        new_obj = NewsNotice.objects.get(title='New News Item')
        self.assertEqual(new_obj.file_name, "sample.txt")
        self.assertEqual(new_obj.file_data, file_content)
        self.assertEqual(new_obj.session_id, self.user.username)
        self.assertEqual(new_obj.comp_id, self.user.company_id)
        self.assertEqual(new_obj.fin_year_id, self.user.financial_year_id)
        self.assertIn('News & Notice added successfully.', [m.message for m in response.context['messages']])


    def test_create_view_post_success_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'title': 'HTMX News',
            'in_details': 'Details for HTMX news.',
            'from_date': '2023-04-01',
            'to_date': '2023-04-30',
        }
        response = self.client.post(reverse('newsnotice_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX returns 204 No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshNewsNoticeList')
        self.assertTrue(NewsNotice.objects.filter(title='HTMX News').exists())

    def test_create_view_post_validation_error(self):
        data = { # Missing required fields
            'title': '',
            'in_details': 'Details for invalid news.',
            'from_date': '2023-05-01',
            'to_date': '2023-04-01', # To date before From date
        }
        response = self.client.post(reverse('newsnotice_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr_news/newsnotice/form.html')
        self.assertFalse(NewsNotice.objects.filter(in_details='Details for invalid news.').exists())
        self.assertFormError(response, 'form', 'title', ['This field is required.'])
        self.assertFormError(response, 'form', 'to_date', ['To Date cannot be earlier than From Date.'])

    def test_update_view_get(self):
        obj = NewsNotice.objects.get(pk=1)
        response = self.client.get(reverse('newsnotice_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_news/newsnotice/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success(self):
        obj = NewsNotice.objects.get(pk=1)
        file_content_updated = b"Updated file content."
        updated_file = SimpleUploadedFile("updated.pdf", file_content_updated, content_type="application/pdf")

        data = {
            'title': 'Updated News Item',
            'in_details': 'Updated details.',
            'from_date': '2023-06-01',
            'to_date': '2023-06-30',
            'upload_file': updated_file,
        }
        response = self.client.post(reverse('newsnotice_edit', args=[obj.pk]), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertRedirects(response, reverse('newsnotice_list'))
        
        obj.refresh_from_db()
        self.assertEqual(obj.title, 'Updated News Item')
        self.assertEqual(obj.file_name, 'updated.pdf')
        self.assertEqual(obj.file_data, file_content_updated)
        self.assertIn('News & Notice updated successfully.', [m.message for m in response.context['messages']])

    def test_update_view_post_success_htmx(self):
        obj = NewsNotice.objects.get(pk=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'title': 'HTMX Updated News',
            'in_details': 'Details for HTMX updated news.',
            'from_date': '2023-07-01',
            'to_date': '2023-07-31',
        }
        response = self.client.post(reverse('newsnotice_edit', args=[obj.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshNewsNoticeList')
        
        obj.refresh_from_db()
        self.assertEqual(obj.title, 'HTMX Updated News')

    def test_delete_view_get(self):
        obj = NewsNotice.objects.get(pk=1)
        response = self.client.get(reverse('newsnotice_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_news/newsnotice/confirm_delete.html')
        self.assertTrue('newsnotice' in response.context)
        self.assertEqual(response.context['newsnotice'], obj)

    def test_delete_view_post_success(self):
        obj_to_delete = NewsNotice.objects.create(
            title='Delete Me',
            in_details='This will be deleted.',
            from_date=date(2023, 8, 1),
            to_date=date(2023, 8, 10),
            session_id='testuser',
            comp_id=1,
            fin_year_id=1,
        )
        response = self.client.post(reverse('newsnotice_delete', args=[obj_to_delete.pk]), follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertRedirects(response, reverse('newsnotice_list'))
        self.assertFalse(NewsNotice.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertIn('News & Notice deleted successfully.', [m.message for m in response.context['messages']])

    def test_delete_view_post_success_htmx(self):
        obj_to_delete_htmx = NewsNotice.objects.create(
            title='Delete Me HTMX',
            in_details='This will be deleted by HTMX.',
            from_date=date(2023, 9, 1),
            to_date=date(2023, 9, 10),
            session_id='testuser',
            comp_id=1,
            fin_year_id=1,
        )
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('newsnotice_delete', args=[obj_to_delete_htmx.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshNewsNoticeList')
        self.assertFalse(NewsNotice.objects.filter(pk=obj_to_delete_htmx.pk).exists())

    def test_download_file_view(self):
        file_content = b"Downloadable content."
        news_with_file = NewsNotice.objects.create(
            title='Download Test',
            in_details='Test file download.',
            from_date=date(2024, 1, 1),
            to_date=date(2024, 1, 15),
            file_name="test_download.txt",
            file_size=len(file_content),
            content_type="text/plain",
            file_data=file_content,
            session_id='testuser',
            comp_id=1,
            fin_year_id=1,
        )
        response = self.client.get(reverse('newsnotice_download', args=[news_with_file.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/plain')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_download.txt"')
        self.assertEqual(response.content, file_content)

    def test_download_file_view_no_file(self):
        obj_no_file = NewsNotice.objects.get(pk=1) # Object with no file
        response = self.client.get(reverse('newsnotice_download', args=[obj_no_file.pk]))
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates are specifically designed for HTMX and Alpine.js:

-   **HTMX for Dynamic Interactions:**
    -   `hx-get` is used on buttons (`Add New News Notice`, `Edit`, `Delete`) to fetch forms/confirmation dialogs into the `#modalContent` div without full page reloads.
    -   `hx-post` is used on forms to submit data. `hx-swap="none"` with a `204 No Content` response and `HX-Trigger` header is used to signal HTMX to perform no DOM swap but instead trigger a custom event (`refreshNewsNoticeList`).
    -   The `newsnoticeTable-container` uses `hx-trigger="load, refreshNewsNoticeList from:body"` to automatically load the initial table and refresh it whenever the `refreshNewsNoticeList` custom event is fired from the body (after a successful create, update, or delete operation). This ensures the table always reflects the latest data.
    -   `hx-encoding="multipart/form-data"` is explicitly added to the form for file uploads.

-   **Alpine.js for UI State Management (Modal):**
    -   The modal (`#modal`) uses Alpine.js's `x-data` attribute or `_` (hyperscript) for managing its visibility.
    -   The `on click` handlers trigger the modal to open (`add .flex`, `remove .hidden`).
    -   Closing the modal (clicking outside or 'Cancel' button) involves removing Alpine/Tailwind classes to hide it gracefully.
    -   The `modalContent` div uses `on modal shown` to apply transition effects when the modal appears, creating a smooth user experience.

-   **DataTables for List Views:**
    -   The `_newsnotice_table.html` partial contains the `<table>` element with `id="newsnoticeTable"`.
    -   A `<script>` block within this partial initializes DataTables on `$(document).ready()`. This ensures DataTables is initialized every time the table content is reloaded via HTMX, providing client-side searching, sorting, and pagination features. The script also includes a check to destroy any existing DataTable instance to prevent reinitialization errors during HTMX swaps.

-   **Seamless User Experience:**
    -   All CRUD operations (Add, Edit, Delete) are performed within a modal window, preventing full page reloads and providing a smoother single-page application feel.
    -   Success messages are handled by Django's messages framework and are displayed on the main list page after the HTMX refresh.

### Final Notes

-   **Placeholders:** All placeholders like `[APP_NAME]`, `[MODEL_NAME]`, `[FIELD1]` etc., have been replaced with concrete values derived from the ASP.NET analysis.
-   **DRY Templates:** Templates are structured to be reusable. `_newsnotice_table.html` and `form.html` are partials designed to be loaded dynamically via HTMX. The base layout is handled by `core/base.html` as per guidelines.
-   **Fat Model, Thin View:** The complex logic for populating system fields and handling file data is encapsulated within the `NewsNotice.save_with_context` method, ensuring views remain concise and focused on dispatching requests.
-   **Comprehensive Tests:** Unit tests cover model behavior, including the custom `save_with_context` method and file data handling. Integration tests cover all CRUD views (GET and POST, including HTMX specific behaviors) and the file download view, ensuring high test coverage.
-   **Business Benefits:** This modernized Django solution offers:
    -   **Improved Maintainability:** Clean, structured code with clear separation of concerns.
    -   **Enhanced User Experience:** Fast, dynamic interactions using HTMX and Alpine.js, minimizing page reloads.
    -   **Scalability:** Built on a robust framework suitable for growing applications.
    -   **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms to modern web development patterns.
    -   **Faster Development Cycles:** Leveraging Django's built-in features and conventions for rapid implementation.
    -   **Stronger Data Integrity:** Centralized business logic in models and robust form validation.