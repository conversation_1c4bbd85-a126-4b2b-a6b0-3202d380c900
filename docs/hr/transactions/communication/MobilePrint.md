This document outlines a comprehensive plan for modernizing your legacy ASP.NET application, specifically the `MobilePrint.aspx` module, to a robust and scalable Django-based solution. Our approach emphasizes AI-assisted automation, ensuring a smooth transition with minimal manual intervention, resulting in a more efficient, maintainable, and user-friendly system.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code's SQL query.

**Instructions:**
The provided ASP.NET code retrieves data for a report using a complex SQL `SELECT` statement joining multiple tables. While the `MobilePrint.aspx` page itself is a report viewer, for a complete modernization, we will map the underlying source tables to Django models.

**Identified Tables and Inferred Columns:**

1.  **`tblCompanyMaster`** (Inferred from `fun.CompAdd(CompId)` and `Session["compid"]`):
    *   `Id` (Primary Key, INT)
    *   `CompanyName` (VARCHAR)
    *   `CompanyAddress` (TEXT)
    *   *(Assumed for Company related data)*

2.  **`tblHR_OfficeStaff`**:
    *   `Id` (Primary Key, INT)
    *   `EmpId` (VARCHAR/NVARCHAR, Unique)
    *   `EmployeeName` (VARCHAR/NVARCHAR)
    *   `MobileNo` (INT - Foreign Key to `tblHR_CoporateMobileNo.Id`)
    *   `CompId` (INT - Foreign Key to `tblCompanyMaster.Id`)

3.  **`tblHR_CoporateMobileNo`**:
    *   `Id` (Primary Key, INT)
    *   `MobileNo` (VARCHAR/NVARCHAR)
    *   `LimitAmt` (FLOAT/DECIMAL)

4.  **`tblHR_MobileBill`**:
    *   `Id` (Primary Key, INT)
    *   `EmpId` (INT - Foreign Key to `tblHR_OfficeStaff.Id`)
    *   `BillAmt` (FLOAT/DECIMAL)
    *   `Taxes` (INT - Foreign Key to `tblExciseser_Master.Id`)
    *   `BillMonth` (VARCHAR/NVARCHAR - e.g., "Jan-2023")
    *   `FinYearId` (INT)
    *   `CompId` (INT - Foreign Key to `tblCompanyMaster.Id`)

5.  **`tblExciseser_Master`**:
    *   `Id` (Primary Key, INT)
    *   `Value` (FLOAT/DECIMAL - Represents tax percentage/value)
    *   `TaxName` (VARCHAR/NVARCHAR - Assumed, for descriptive purposes)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The `MobilePrint.aspx` module is primarily a **Read** operation, focusing on displaying a report. It takes parameters from the URL (`Months`) and Session (`compid`, `finyear`) to filter and present data.

*   **Read**: The core functionality involves fetching joined data from `tblHR_OfficeStaff`, `tblHR_CoporateMobileNo`, `tblHR_MobileBill`, and `tblExciseser_Master` based on `CompId`, `FinYearId`, and `BillMonth`, then formatting it for display.
*   **Data Retrieval Logic**: The `DDLMonth` method in the C# code-behind orchestrates the data retrieval, filtering, and parameter passing to the Crystal Report.
*   **No Explicit CRUD**: This specific ASP.NET page does not show explicit Create, Update, or Delete operations on the underlying data. However, for a comprehensive modernization plan, we will generate the full CRUD scaffolding for the central `MobileBill` entity in Django, assuming it's part of a broader module for managing these records.
*   **Session Management**: The ASP.NET application uses `Session` to persist `CompId`, `FinYearId`, and report objects. Django will use `request.session` for user-specific data (like `CompId`, `FinYearId`) and handle report data generation on each request, leveraging HTMX for efficient partial updates.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and propose Django equivalents.

**Instructions:**
The ASP.NET page uses a `CrystalReportViewer` control to render a pre-designed report. In Django, this will be replaced by a dynamic HTML table enhanced with DataTables for interactivity.

*   **Report Display**: The `CrystalReportViewer` will be replaced by a standard HTML `<table>` element.
*   **Data Presentation**: The table will be powered by **DataTables**, providing client-side search, sorting, and pagination, mimicking typical grid view functionalities.
*   **Filtering**: The `Months` parameter, currently passed via query string, will be exposed as a dropdown filter on the Django page, allowing users to select the report month directly. Session-based `CompId` and `FinYearId` will be handled implicitly based on the logged-in user's context.
*   **Dynamic Interactions**: **HTMX** will be used to load the report table dynamically when filters change, avoiding full page reloads. It will also facilitate CRUD operations for the `MobileBill` records via modals if we were to add them.
*   **UI State Management**: **Alpine.js** can be used for simple UI interactions like managing modal visibility or showing/hiding loading indicators, although HTMX handles much of the dynamic content loading.

### Step 4: Generate Django Code

We will create a Django application named `hr_reports`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
We define four models to represent the primary tables involved in the report query. These models will use `managed = False` and `db_table` to map directly to your existing database tables. The business logic for retrieving report-specific data is encapsulated within a class method of the `MobileBill` model, adhering to the "fat model" principle.

```python
# hr_reports/models.py
from django.db import models
from django.db.models import F

class Company(models.Model):
    # This model represents tblCompanyMaster, inferred from the usage of CompId
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255, verbose_name="Company Name")
    address = models.TextField(db_column='CompanyAddress', verbose_name="Company Address")

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

    def get_address(self):
        """
        Mimics the fun.CompAdd(CompId) functionality, retrieving company address.
        """
        return self.address


class OfficeStaff(models.Model):
    # Maps to tblHR_OfficeStaff
    id = models.IntegerField(db_column='Id', primary_key=True)
    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True, verbose_name="Employee ID")
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, verbose_name="Employee Name")
    mobile_no_fk = models.ForeignKey(
        'CoporateMobileNo',
        on_delete=models.DO_NOTHING, # Use DO_NOTHING for managed=False to reflect existing DB behavior
        db_column='MobileNo', # This is the foreign key column name in tblHR_OfficeStaff
        related_name='staff_members',
        null=True, blank=True, # Assuming MobileNo could be nullable if not assigned
        verbose_name="Corporate Mobile Number"
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.DO_NOTHING,
        db_column='CompId',
        related_name='office_staff',
        null=True, blank=True,
        verbose_name="Company"
    )

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name


class CoporateMobileNo(models.Model):
    # Maps to tblHR_CoporateMobileNo
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=20, verbose_name="Mobile Number")
    limit_amount = models.FloatField(db_column='LimitAmt', verbose_name="Limit Amount")

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        return self.mobile_no


class ExciseMaster(models.Model):
    # Maps to tblExciseser_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.FloatField(db_column='Value', verbose_name="Tax Value (%)")
    # Added a placeholder field for tax name if it exists, common in such tables
    tax_name = models.CharField(db_column='TaxName', max_length=100, default='Unknown Tax', verbose_name="Tax Name")

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Master'
        verbose_name_plural = 'Excise Masters'

    def __str__(self):
        return f"{self.tax_name} ({self.value}%)"


class MobileBill(models.Model):
    # Maps to tblHR_MobileBill
    id = models.IntegerField(db_column='Id', primary_key=True)
    employee = models.ForeignKey(
        OfficeStaff,
        on_delete=models.DO_NOTHING,
        db_column='EmpId',
        related_name='mobile_bills',
        verbose_name="Employee"
    )
    bill_amount = models.FloatField(db_column='BillAmt', verbose_name="Bill Amount")
    taxes = models.ForeignKey(
        ExciseMaster,
        on_delete=models.DO_NOTHING,
        db_column='Taxes',
        related_name='mobile_bills',
        verbose_name="Taxes"
    )
    bill_month = models.CharField(db_column='BillMonth', max_length=20, verbose_name="Bill Month") # e.g., "Jan-2023"
    financial_year_id = models.IntegerField(db_column='FinYearId', verbose_name="Financial Year ID")
    company = models.ForeignKey(
        Company,
        on_delete=models.DO_NOTHING,
        db_column='CompId',
        related_name='mobile_bill_records',
        null=True, blank=True,
        verbose_name="Company"
    )

    class Meta:
        managed = False
        db_table = 'tblHR_MobileBill'
        verbose_name = 'Mobile Bill'
        verbose_name_plural = 'Mobile Bills'

    def __str__(self):
        return f"Bill for {self.employee.employee_name} ({self.bill_month})"

    @classmethod
    def get_report_data(cls, company_id, financial_year_id, bill_month):
        """
        Retrieves and formats data for the Mobile Bill Report, replicating the complex SQL query.
        This method embodies the 'fat model' principle by containing the data fetching logic.
        """
        # Ensure company_id and financial_year_id are integers for filtering
        try:
            company_id = int(company_id)
            financial_year_id = int(financial_year_id)
        except (TypeError, ValueError):
            return [] # Return empty if parameters are invalid

        # Replicate the JOIN query using Django ORM's select_related and annotation for derived fields
        # The F() expression allows referencing other model fields directly in annotations.
        report_queryset = cls.objects.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            bill_month=bill_month
        ).select_related(
            'employee',
            'employee__mobile_no_fk',
            'taxes'
        ).annotate(
            # Annotate with the exact fields as expected in the original report output
            # Accessing fields through relationships:
            EmpId=F('employee__emp_id'),
            EmployeeName=F('employee__employee_name'),
            MobileNo=F('employee__mobile_no_fk__mobile_no'),
            LimitAmt=F('employee__mobile_no_fk__limit_amount'),
            BillAmt=F('bill_amount'),
            TaxValue=F('taxes__value'),
            CompId=F('company_id'),
            # Calculate Net Bill Amount as an example of a derived field for the report
            # Assumes 'Value' from ExciseMaster is a percentage (e.g., 5 for 5%)
            NetBillAmount=F('bill_amount') * (1 + F('taxes__value') / 100)
        ).values(
            'EmpId', 'EmployeeName', 'MobileNo', 'LimitAmt', 'BillAmt', 'TaxValue', 'CompId', 'NetBillAmount'
        ).order_by('EmployeeName')

        return list(report_queryset)

    @classmethod
    def get_available_months(cls, company_id, financial_year_id):
        """
        Returns a list of distinct BillMonths available for a given company and financial year.
        Used to populate the month filter dropdown.
        """
        try:
            company_id = int(company_id)
            financial_year_id = int(financial_year_id)
        except (TypeError, ValueError):
            return []
            
        return cls.objects.filter(
            company_id=company_id,
            financial_year_id=financial_year_id
        ).values_list('bill_month', flat=True).distinct().order_by('bill_month')

```

#### 4.2 Forms

**Task:** Define a Django form for `MobileBill` for future CRUD capabilities.

**Instructions:**
Even though the original page is read-only, we provide a `ModelForm` for `MobileBill` to enable full CRUD operations as part of the modernization, adhering to the provided template for form generation.

```python
# hr_reports/forms.py
from django import forms
from .models import MobileBill, OfficeStaff, CoporateMobileNo, ExciseMaster, Company

class MobileBillForm(forms.ModelForm):
    # Add a custom field for the corporate mobile number to allow selection/input
    # This might be more complex if corporate mobile is directly managed here,
    # but for simplicity, we'll assume it's pre-existing.
    # The ForeignKey in MobileBill points to OfficeStaff, so we need to select an employee.
    
    class Meta:
        model = MobileBill
        fields = ['employee', 'bill_amount', 'taxes', 'bill_month', 'financial_year_id', 'company']
        widgets = {
            'employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'taxes': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_month': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., Jan-2023'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Custom validation example (e.g., ensure bill_month format)
    def clean_bill_month(self):
        bill_month = self.cleaned_data['bill_month']
        # Simple regex check for Month-YYYY format, e.g., "Jan-2023"
        import re
        if not re.match(r'^[A-Za-z]{3}-\d{4}$', bill_month):
            raise forms.ValidationError("Bill month must be in 'MMM-YYYY' format (e.g., Jan-2023).")
        return bill_month

```

#### 4.3 Views

**Task:** Implement the report display and CRUD operations using Class-Based Views (CBVs).

**Instructions:**
The primary view `MobileBillReportView` handles the report display and filtering. Other views (`Create`, `Update`, `Delete`) are included to provide full CRUD functionality for `MobileBill` records as part of the overall modernization, even though they weren't explicitly on the original ASP.NET page. Views are kept thin, delegating logic to models.

```python
# hr_reports/views.py
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.db.models import F

from .models import MobileBill, Company
from .forms import MobileBillForm

# Assume dummy session values for demonstration
# In a real application, these would come from the user's session after login.
DUMMY_COMP_ID = 1
DUMMY_FIN_YEAR_ID = 2023

class MobileBillReportView(TemplateView):
    """
    Main view to display the mobile bill report and handle month filtering.
    This replaces the CrystalReportViewer page.
    """
    template_name = 'hr_reports/mobilebill/report_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # In a real app, comp_id and fin_year_id would come from request.session
        # For this example, we use dummy values.
        comp_id = self.request.session.get('compid', DUMMY_COMP_ID)
        fin_year_id = self.request.session.get('finyear', DUMMY_FIN_YEAR_ID)

        selected_month = self.request.GET.get('month', '')
        available_months = MobileBill.get_available_months(comp_id, fin_year_id)

        # If no month is selected, try to default to the latest available month or first one
        if not selected_month and available_months:
            # Assuming months are sortable alphabetically/chronologically if in MMM-YYYY format
            selected_month = sorted(list(available_months), reverse=True)[0] # Get latest month

        context['available_months'] = available_months
        context['selected_month'] = selected_month
        context['company_address'] = self._get_company_address(comp_id)

        # The actual report data is fetched by MobileBillReportTablePartialView
        # This view only sets up the main page and filters.
        return context
    
    def _get_company_address(self, company_id):
        """Helper to get company address, mimicking fun.CompAdd(CompId)"""
        try:
            company = Company.objects.get(id=company_id)
            return company.get_address()
        except Company.DoesNotExist:
            return "Company address not found."


class MobileBillReportTablePartialView(ListView):
    """
    Partial view to load the DataTables content via HTMX.
    This fetches the actual report data based on selected filters.
    """
    model = MobileBill
    template_name = 'hr_reports/mobilebill/_report_table.html'
    context_object_name = 'report_items'

    def get_queryset(self):
        # In a real app, comp_id and fin_year_id would come from request.session
        comp_id = self.request.session.get('compid', DUMMY_COMP_ID)
        fin_year_id = self.request.session.get('finyear', DUMMY_FIN_YEAR_ID)
        bill_month = self.request.GET.get('month') # Get month from HTMX request parameters

        if not bill_month:
            # If month not provided in HTMX request, try to get default from available months
            available_months = MobileBill.get_available_months(comp_id, fin_year_id)
            if available_months:
                bill_month = sorted(list(available_months), reverse=True)[0] # Default to latest month

        if comp_id and fin_year_id and bill_month:
            return MobileBill.get_report_data(comp_id, fin_year_id, bill_month)
        return [] # Return empty if essential parameters are missing


# --- CRUD Views for MobileBill (as per template requirements) ---

class MobileBillListView(ListView):
    """
    View to list all MobileBill records.
    Used for a general management screen, separate from the report.
    """
    model = MobileBill
    template_name = 'hr_reports/mobilebill/list.html'
    context_object_name = 'mobile_bills' # Renamed for clarity in list template

class MobileBillCreateView(CreateView):
    """
    View to create a new MobileBill record via a modal form (HTMX).
    """
    model = MobileBill
    form_class = MobileBillForm
    template_name = 'hr_reports/mobilebill/form.html'
    success_url = reverse_lazy('mobile_bill_list') # Redirects to list page on full refresh

    def form_valid(self, form):
        # Set session-dependent fields before saving
        form.instance.company_id = self.request.session.get('compid', DUMMY_COMP_ID)
        form.instance.financial_year_id = self.request.session.get('finyear', DUMMY_FIN_YEAR_ID)
        response = super().form_valid(form)
        messages.success(self.request, 'Mobile Bill added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMobileBillList'
                }
            )
        return response

class MobileBillUpdateView(UpdateView):
    """
    View to update an existing MobileBill record via a modal form (HTMX).
    """
    model = MobileBill
    form_class = MobileBillForm
    template_name = 'hr_reports/mobilebill/form.html'
    success_url = reverse_lazy('mobile_bill_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Mobile Bill updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMobileBillList'
                }
            )
        return response

class MobileBillDeleteView(DeleteView):
    """
    View to delete a MobileBill record via a confirmation modal (HTMX).
    """
    model = MobileBill
    template_name = 'hr_reports/mobilebill/confirm_delete.html'
    success_url = reverse_lazy('mobile_bill_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Mobile Bill deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMobileBillList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for the report view and the CRUD operations.

**Instructions:**
Templates follow a DRY approach, extending `core/base.html` and using partials for reusable components like forms and data tables. HTMX attributes are used for dynamic content loading, and DataTables JavaScript is included for enhanced table functionality.

**`hr_reports/mobilebill/report_list.html`** (Main Report Display Page)
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Mobile Bill Report</h2>
        <div class="mt-4 md:mt-0 flex items-center space-x-4">
            <label for="month-select" class="block text-sm font-medium text-gray-700">Select Month:</label>
            <select id="month-select" name="month"
                    class="block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    hx-get="{% url 'mobile_bill_report_table' %}"
                    hx-target="#reportTable-container"
                    hx-trigger="change"
                    hx-indicator="#report-loading-indicator">
                {% for month in available_months %}
                <option value="{{ month }}" {% if month == selected_month %}selected{% endif %}>{{ month }}</option>
                {% empty %}
                <option value="">No months available</option>
                {% endfor %}
            </select>
        </div>
    </div>

    <div class="bg-gray-100 p-4 rounded-lg mb-6 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700 mb-2">Company Information:</h3>
        <p class="text-gray-600">{{ company_address|linebreaksbr }}</p>
    </div>
    
    <div id="report-loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-blue-500">Loading Report...</p>
    </div>

    <div id="reportTable-container"
         hx-trigger="load, reloadMobileBillReport from:body"
         hx-get="{% url 'mobile_bill_report_table' %}?month={{ selected_month }}"
         hx-swap="innerHTML">
        <!-- Report table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading initial report data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init if needed for modal or other UI state on this page
    document.addEventListener('alpine:init', () => {
        // No specific Alpine components needed for this page directly
    });
</script>
{% endblock %}

```

**`hr_reports/mobilebill/_report_table.html`** (Partial for Report DataTables)
```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="mobileBillReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Limit Amt</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Amt</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Value (%)</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Bill Amt</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in report_items %}
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.EmpId }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.EmployeeName }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.MobileNo }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.LimitAmt|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.BillAmt|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.TaxValue|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.NetBillAmount|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.CompId }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="px-6 py-4 text-center text-sm text-gray-500">No report data available for the selected month.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#mobileBillReportTable')) {
            $('#mobileBillReportTable').DataTable().destroy();
        }
        $('#mobileBillReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            "order": [[2, 'asc']] // Order by Employee Name by default
        });
    });
</script>
```

**`hr_reports/mobilebill/list.html`** (Main List View for Mobile Bill Management)
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Mobile Bills Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'mobile_bill_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Mobile Bill
        </button>
    </div>

    <div id="mobileBillTable-container"
         hx-trigger="load, refreshMobileBillList from:body"
         hx-get="{% url 'mobile_bill_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Mobile Bills...</p>
        </div>
    </div>

    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state
        // (e.g., specific form interactions within the modal)
    });

    // Close modal on HX-Trigger if needed (e.g., after a successful form submission)
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.requestHeaders['HX-Trigger'] && evt.detail.requestHeaders['HX-Trigger'].includes('refreshMobileBillList')) {
            // If the response is a 204 (no content) and triggers a list refresh, close the modal.
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Add event listener to close modal if messages are shown (e.g., after non-HTMX form submission)
    document.body.addEventListener('show.bs.modal', function() {
        // This is a placeholder. Real implementation depends on how messages are displayed
        // and if they trigger a modal close.
    });

</script>
{% endblock %}
```

**`hr_reports/mobilebill/_mobile_bill_table.html`** (Partial for Mobile Bill Management DataTables)
```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="mobileBillTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Month</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Amount</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Value</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in mobile_bills %}
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ obj.employee.employee_name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ obj.bill_month }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ obj.bill_amount|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ obj.taxes.value|floatformat:2 }}%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'mobile_bill_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                        hx-get="{% url 'mobile_bill_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No mobile bills found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#mobileBillTable')) {
            $('#mobileBillTable').DataTable().destroy();
        }
        $('#mobileBillTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            "order": [[1, 'asc']] // Order by Employee Name by default
        });
    });
</script>
```

**`hr_reports/mobilebill/form.html`** (Partial for Mobile Bill Create/Update Form)
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Mobile Bill</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ form.non_field_errors }}</span>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-150 ease-in-out">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**`hr_reports/mobilebill/confirm_delete.html`** (Partial for Mobile Bill Delete Confirmation)
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Mobile Bill for <strong>{{ object.employee.employee_name }}</strong> for <strong>{{ object.bill_month }}</strong>?</p>
    
    <form hx-post="{% url 'mobile_bill_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main report page, the HTMX-loaded report table partial, and the full CRUD operations for `MobileBill` records.

```python
# hr_reports/urls.py
from django.urls import path
from .views import (
    MobileBillReportView, MobileBillReportTablePartialView,
    MobileBillListView, MobileBillCreateView, MobileBillUpdateView, MobileBillDeleteView
)

urlpatterns = [
    # Mobile Bill Report URLs (replaces original ASP.NET page)
    path('mobile-bill-report/', MobileBillReportView.as_view(), name='mobile_bill_report_list'),
    path('mobile-bill-report/table/', MobileBillReportTablePartialView.as_view(), name='mobile_bill_report_table'),

    # Mobile Bill Management (CRUD) URLs (added for full modernization)
    path('mobile-bills/', MobileBillListView.as_view(), name='mobile_bill_list'),
    path('mobile-bills/add/', MobileBillCreateView.as_view(), name='mobile_bill_add'),
    path('mobile-bills/edit/<int:pk>/', MobileBillUpdateView.as_view(), name='mobile_bill_edit'),
    path('mobile-bills/delete/<int:pk>/', MobileBillDeleteView.as_view(), name='mobile_bill_delete'),
    # HTMX partial for the list table
    path('mobile-bills/table/', MobileBillListView.as_view(template_name='hr_reports/mobilebill/_mobile_bill_table.html'), name='mobile_bill_table'),
]

```

#### 4.6 Tests

**Task:** Write comprehensive tests for the models and views.

**Instructions:**
Unit tests for model methods ensure business logic correctness. Integration tests for views verify proper rendering, form submission, and HTMX interactions. These tests aim for at least 80% coverage.

```python
# hr_reports/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, OfficeStaff, CoporateMobileNo, ExciseMaster, MobileBill
from django.db.utils import ProgrammingError # For handling managed=False model setup issues in tests

# Ensure the app name is correct for reverse lookups
APP_NAME = 'hr_reports'

class CompanyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a dummy company for testing
        cls.company = Company.objects.create(
            id=1,
            name='Test Company',
            address='123 Test Street, Test City'
        )

    def test_company_creation(self):
        self.assertEqual(self.company.name, 'Test Company')
        self.assertEqual(self.company.address, '123 Test Street, Test City')
        self.assertEqual(str(self.company), 'Test Company')

    def test_get_address_method(self):
        self.assertEqual(self.company.get_address(), '123 Test Street, Test City')

    def test_meta_options(self):
        self.assertFalse(self.company._meta.managed)
        self.assertEqual(self.company._meta.db_table, 'tblCompanyMaster')
        self.assertEqual(self.company._meta.verbose_name, 'Company')
        self.assertEqual(self.company._meta.verbose_name_plural, 'Companies')


class CoporateMobileNoModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.corp_mobile = CoporateMobileNo.objects.create(
            id=101,
            mobile_no='9876543210',
            limit_amount=500.00
        )

    def test_corp_mobile_creation(self):
        self.assertEqual(self.corp_mobile.mobile_no, '9876543210')
        self.assertEqual(self.corp_mobile.limit_amount, 500.00)
        self.assertEqual(str(self.corp_mobile), '9876543210')

    def test_meta_options(self):
        self.assertFalse(self.corp_mobile._meta.managed)
        self.assertEqual(self.corp_mobile._meta.db_table, 'tblHR_CoporateMobileNo')


class ExciseMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.excise_tax = ExciseMaster.objects.create(
            id=1,
            value=18.0,
            tax_name='GST'
        )

    def test_excise_master_creation(self):
        self.assertEqual(self.excise_tax.value, 18.0)
        self.assertEqual(self.excise_tax.tax_name, 'GST')
        self.assertEqual(str(self.excise_tax), 'Tax: GST (18.0%)')

    def test_meta_options(self):
        self.assertFalse(self.excise_tax._meta.managed)
        self.assertEqual(self.excise_tax._meta.db_table, 'tblExciseser_Master')


class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='Test Company', address='Test Address')
        cls.corp_mobile = CoporateMobileNo.objects.create(id=101, mobile_no='9876543210', limit_amount=500.00)
        cls.staff = OfficeStaff.objects.create(
            id=1,
            emp_id='EMP001',
            employee_name='John Doe',
            mobile_no_fk=cls.corp_mobile,
            company=cls.company
        )

    def test_staff_creation(self):
        self.assertEqual(self.staff.emp_id, 'EMP001')
        self.assertEqual(self.staff.employee_name, 'John Doe')
        self.assertEqual(self.staff.mobile_no_fk, self.corp_mobile)
        self.assertEqual(self.staff.company, self.company)
        self.assertEqual(str(self.staff), 'John Doe')

    def test_meta_options(self):
        self.assertFalse(self.staff._meta.managed)
        self.assertEqual(self.staff._meta.db_table, 'tblHR_OfficeStaff')


class MobileBillModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='Test Company', address='Test Address')
        cls.corp_mobile = CoporateMobileNo.objects.create(id=101, mobile_no='9876543210', limit_amount=500.00)
        cls.staff = OfficeStaff.objects.create(id=1, emp_id='EMP001', employee_name='John Doe', mobile_no_fk=cls.corp_mobile, company=cls.company)
        cls.excise = ExciseMaster.objects.create(id=1, value=10.0, tax_name='VAT')
        cls.mobile_bill = MobileBill.objects.create(
            id=1,
            employee=cls.staff,
            bill_amount=100.00,
            taxes=cls.excise,
            bill_month='Jan-2023',
            financial_year_id=2023,
            company=cls.company
        )
        # Add another bill for a different month/employee to test month filtering
        cls.staff2 = OfficeStaff.objects.create(id=2, emp_id='EMP002', employee_name='Jane Smith', mobile_no_fk=cls.corp_mobile, company=cls.company)
        cls.mobile_bill2 = MobileBill.objects.create(
            id=2,
            employee=cls.staff2,
            bill_amount=150.00,
            taxes=cls.excise,
            bill_month='Feb-2023',
            financial_year_id=2023,
            company=cls.company
        )
        cls.mobile_bill3 = MobileBill.objects.create(
            id=3,
            employee=cls.staff,
            bill_amount=120.00,
            taxes=cls.excise,
            bill_month='Mar-2023',
            financial_year_id=2024, # Different financial year
            company=cls.company
        )


    def test_mobile_bill_creation(self):
        self.assertEqual(self.mobile_bill.bill_amount, 100.00)
        self.assertEqual(self.mobile_bill.bill_month, 'Jan-2023')
        self.assertEqual(self.mobile_bill.employee, self.staff)
        self.assertEqual(str(self.mobile_bill), 'Bill for John Doe (Jan-2023)')

    def test_meta_options(self):
        self.assertFalse(self.mobile_bill._meta.managed)
        self.assertEqual(self.mobile_bill._meta.db_table, 'tblHR_MobileBill')

    def test_get_report_data(self):
        # Test with existing data
        report_data = MobileBill.get_report_data(self.company.id, 2023, 'Jan-2023')
        self.assertEqual(len(report_data), 1)
        self.assertEqual(report_data[0]['EmployeeName'], 'John Doe')
        self.assertEqual(report_data[0]['BillAmt'], 100.00)
        self.assertAlmostEqual(report_data[0]['NetBillAmount'], 110.00) # 100 * (1 + 10/100)

        # Test with no matching data
        no_data = MobileBill.get_report_data(self.company.id, 2023, 'Dec-2023')
        self.assertEqual(len(no_data), 0)

        # Test with invalid parameters
        invalid_data = MobileBill.get_report_data('abc', 2023, 'Jan-2023')
        self.assertEqual(len(invalid_data), 0)

    def test_get_available_months(self):
        # Test for correct company and financial year
        months = MobileBill.get_available_months(self.company.id, 2023)
        self.assertIn('Jan-2023', months)
        self.assertIn('Feb-2023', months)
        self.assertNotIn('Mar-2023', months) # Mar-2023 is for FinYear 2024
        self.assertEqual(len(months), 2)

        # Test for different financial year
        months_2024 = MobileBill.get_available_months(self.company.id, 2024)
        self.assertIn('Mar-2023', months_2024)
        self.assertEqual(len(months_2024), 1)

        # Test with no months for given criteria
        no_months = MobileBill.get_available_months(self.company.id, 9999)
        self.assertEqual(len(no_months), 0)

        # Test with invalid parameters
        invalid_months = MobileBill.get_available_months('xyz', 2023)
        self.assertEqual(len(invalid_months), 0)


class MobileBillViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up global dummy session values for tests
        cls.dummy_comp_id = 1
        cls.dummy_fin_year_id = 2023
        cls.company = Company.objects.create(id=cls.dummy_comp_id, name='Test Company', address='Test Address')
        cls.corp_mobile = CoporateMobileNo.objects.create(id=101, mobile_no='9876543210', limit_amount=500.00)
        cls.staff = OfficeStaff.objects.create(id=1, emp_id='EMP001', employee_name='John Doe', mobile_no_fk=cls.corp_mobile, company=cls.company)
        cls.excise = ExciseMaster.objects.create(id=1, value=10.0, tax_name='VAT')
        cls.mobile_bill = MobileBill.objects.create(
            id=1,
            employee=cls.staff,
            bill_amount=100.00,
            taxes=cls.excise,
            bill_month='Jan-2023',
            financial_year_id=cls.dummy_fin_year_id,
            company=cls.company
        )
        cls.mobile_bill_feb = MobileBill.objects.create(
            id=2,
            employee=cls.staff,
            bill_amount=120.00,
            taxes=cls.excise,
            bill_month='Feb-2023',
            financial_year_id=cls.dummy_fin_year_id,
            company=cls.company
        )
        # Mock session values as views directly use them
        from hr_reports import views as hr_reports_views
        hr_reports_views.DUMMY_COMP_ID = cls.dummy_comp_id
        hr_reports_views.DUMMY_FIN_YEAR_ID = cls.dummy_fin_year_id

    def setUp(self):
        self.client = Client()
        # Set session data for each test to simulate login context
        session = self.client.session
        session['compid'] = self.dummy_comp_id
        session['finyear'] = self.dummy_fin_year_id
        session.save()

    # --- Report Views Tests ---
    def test_mobile_bill_report_view_get(self):
        response = self.client.get(reverse('mobile_bill_report_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/report_list.html')
        self.assertIn('available_months', response.context)
        self.assertIn('selected_month', response.context)
        self.assertIn('company_address', response.context)
        # Check if default month is selected (latest, 'Feb-2023' in this case)
        self.assertEqual(response.context['selected_month'], 'Feb-2023')

    def test_mobile_bill_report_view_get_with_month_param(self):
        response = self.client.get(reverse('mobile_bill_report_list') + '?month=Jan-2023')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['selected_month'], 'Jan-2023')

    def test_mobile_bill_report_table_partial_view_get(self):
        response = self.client.get(reverse('mobile_bill_report_table'), {'month': 'Jan-2023'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/_report_table.html')
        self.assertIn('report_items', response.context)
        self.assertEqual(len(response.context['report_items']), 1)
        self.assertEqual(response.context['report_items'][0]['EmployeeName'], 'John Doe')

    def test_mobile_bill_report_table_partial_view_no_month(self):
        # Should default to latest month if no month is provided in HTMX request
        response = self.client.get(reverse('mobile_bill_report_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/_report_table.html')
        self.assertIn('report_items', response.context)
        self.assertEqual(len(response.context['report_items']), 1)
        self.assertEqual(response.context['report_items'][0]['BillAmt'], 120.00) # Feb-2023 bill

    # --- CRUD Views Tests ---
    def test_mobile_bill_list_view_get(self):
        response = self.client.get(reverse('mobile_bill_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/list.html')
        self.assertIn('mobile_bills', response.context)
        self.assertEqual(len(response.context['mobile_bills']), 2) # jan and feb bills

    def test_mobile_bill_table_partial_view_get(self):
        response = self.client.get(reverse('mobile_bill_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/_mobile_bill_table.html')
        self.assertIn('mobile_bills', response.context)
        self.assertEqual(len(response.context['mobile_bills']), 2)

    def test_mobile_bill_create_view_get(self):
        response = self.client.get(reverse('mobile_bill_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/form.html')
        self.assertIn('form', response.context)

    def test_mobile_bill_create_view_post_htmx_success(self):
        initial_count = MobileBill.objects.count()
        data = {
            'employee': self.staff.id,
            'bill_amount': 200.00,
            'taxes': self.excise.id,
            'bill_month': 'Apr-2023',
            'financial_year_id': self.dummy_fin_year_id,
            'company': self.company.id,
        }
        response = self.client.post(reverse('mobile_bill_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertEqual(MobileBill.objects.count(), initial_count + 1)
        self.assertTrue(MobileBill.objects.filter(bill_month='Apr-2023').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMobileBillList', response.headers['HX-Trigger'])

    def test_mobile_bill_create_view_post_non_htmx_success(self):
        initial_count = MobileBill.objects.count()
        data = {
            'employee': self.staff.id,
            'bill_amount': 210.00,
            'taxes': self.excise.id,
            'bill_month': 'May-2023',
            'financial_year_id': self.dummy_fin_year_id,
            'company': self.company.id,
        }
        response = self.client.post(reverse('mobile_bill_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertEqual(MobileBill.objects.count(), initial_count + 1)
        self.assertTrue(MobileBill.objects.filter(bill_month='May-2023').exists())
        self.assertRedirects(response, reverse('mobile_bill_list'))

    def test_mobile_bill_create_view_post_invalid(self):
        initial_count = MobileBill.objects.count()
        data = {
            'employee': self.staff.id,
            'bill_amount': 'invalid', # Invalid data
            'taxes': self.excise.id,
            'bill_month': 'Jun-2023',
            'financial_year_id': self.dummy_fin_year_id,
            'company': self.company.id,
        }
        response = self.client.post(reverse('mobile_bill_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertEqual(MobileBill.objects.count(), initial_count) # No new object created

    def test_mobile_bill_update_view_get(self):
        response = self.client.get(reverse('mobile_bill_edit', args=[self.mobile_bill.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.mobile_bill)

    def test_mobile_bill_update_view_post_htmx_success(self):
        data = {
            'employee': self.staff.id,
            'bill_amount': 110.00, # Updated amount
            'taxes': self.excise.id,
            'bill_month': 'Jan-2023',
            'financial_year_id': self.dummy_fin_year_id,
            'company': self.company.id,
        }
        response = self.client.post(reverse('mobile_bill_edit', args=[self.mobile_bill.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.mobile_bill.refresh_from_db()
        self.assertEqual(self.mobile_bill.bill_amount, 110.00)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMobileBillList', response.headers['HX-Trigger'])

    def test_mobile_bill_delete_view_get(self):
        response = self.client.get(reverse('mobile_bill_delete', args=[self.mobile_bill.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, f'{APP_NAME}/mobilebill/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.mobile_bill)

    def test_mobile_bill_delete_view_post_htmx_success(self):
        initial_count = MobileBill.objects.count()
        response = self.client.post(reverse('mobile_bill_delete', args=[self.mobile_bill.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(MobileBill.objects.count(), initial_count - 1)
        self.assertFalse(MobileBill.objects.filter(id=self.mobile_bill.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMobileBillList', response.headers['HX-Trigger'])

    def test_mobile_bill_delete_view_post_non_htmx_success(self):
        initial_count = MobileBill.objects.count()
        response = self.client.post(reverse('mobile_bill_delete', args=[self.mobile_bill_feb.id]))
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertEqual(MobileBill.objects.count(), initial_count - 1)
        self.assertFalse(MobileBill.objects.filter(id=self.mobile_bill_feb.id).exists())
        self.assertRedirects(response, reverse('mobile_bill_list'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
HTMX and Alpine.js are integral to providing a modern, dynamic user experience without extensive custom JavaScript.

*   **HTMX for Report Filtering**:
    *   The `month-select` dropdown in `report_list.html` uses `hx-get` to `{% url 'mobile_bill_report_table' %}` and `hx-target="#reportTable-container"` with `hx-trigger="change"`. This means whenever the month selection changes, HTMX sends an AJAX request to fetch the updated table content, replacing the old content.
    *   The `hx-indicator` attribute provides visual feedback during loading.
    *   The `_report_table.html` partial is self-contained with its DataTables initialization script, ensuring DataTables is re-initialized whenever the content is swapped.
*   **HTMX for CRUD Modals**:
    *   "Add New", "Edit", and "Delete" buttons in `list.html` use `hx-get` to fetch their respective forms (`mobile_bill_add`, `mobile_bill_edit`, `mobile_bill_delete`) into the `#modalContent` div.
    *   The `_="on click add .is-active to #modal"` Hyperscript snippet (integrated with HTMX) adds a class to show the modal when the button is clicked.
    *   Form submissions (`form hx-post` in `form.html` and `confirm_delete.html`) are handled by HTMX. On successful submission (HTTP 204 No Content), the server sends an `HX-Trigger` header (`refreshMobileBillList`) which prompts the main `mobileBillTable-container` to refetch its content, updating the list automatically.
    *   The `hx-on::after-request` on the form handles closing the modal automatically after a successful HTMX submission (status 204).
    *   The modal itself has `_="on click if event.target.id == 'modal' remove .is-active from me"` to close it when clicking outside.
*   **DataTables for List Views**:
    *   Both the main `mobileBillReportTable` and the `mobileBillTable` for CRUD management are initialized with `$(document).ready(function() { ... }).DataTable();`.
    *   The `destroy()` method is used before re-initialization in the partial templates to prevent errors when HTMX re-swaps content.
    *   Provides built-in search, sorting, and pagination without server-side processing for smaller to medium datasets.
*   **Alpine.js**:
    *   Used minimally for showing/hiding modals (`x-data="{ showModal: false }"` or `_="on click add .is-active to #modal"` for simple class toggling, as shown in the templates). It complements HTMX for small client-side UI states.

### Final Notes

*   **Placeholders**: `[APP_NAME]`, `[MODEL_NAME_LOWER]`, `[MODEL_NAME_PLURAL_LOWER]`, `[FIELD_LABEL]`, etc., have been replaced with concrete names (`hr_reports`, `mobile_bill`, `mobile_bills`, `Employee Name`, `Bill Month`, etc.) based on the analysis.
*   **DRY Principles**: Templates extend `core/base.html`, and `_report_table.html` and `_mobile_bill_table.html` are partials loaded by HTMX to prevent code duplication for table rendering. Forms are also rendered as partials (`form.html`, `confirm_delete.html`).
*   **Fat Model, Thin View**: Business logic like `get_report_data` and `get_available_months` is placed in the `MobileBill` model. Views primarily handle HTTP requests, form instantiation, and rendering, keeping them concise (5-15 lines for core logic).
*   **Test Coverage**: Comprehensive unit tests cover model behaviors, and integration tests ensure views and HTMX interactions function as expected.
*   **Business Value**: This migration transforms a legacy, proprietary report viewer into a modern, web-based, interactive data display and management system.
    *   **Reduced Licensing Costs**: Eliminates Crystal Reports dependency.
    *   **Improved Maintainability**: Standardized Django framework, Python, and open-source frontend tools (HTMX, Alpine.js, DataTables) are easier to maintain and extend.
    *   **Enhanced User Experience**: Dynamic filtering and responsive tables provide a smoother, faster interaction than traditional postback-driven pages.
    *   **Scalability**: Django's architecture allows for easier scaling compared to monolithic ASP.NET Web Forms applications.
    *   **Future-Proofing**: Adopts widely supported, actively developed technologies, reducing technical debt.
    *   **Automation Focus**: The entire plan is structured to be implemented through AI-assisted automation, minimizing manual coding and potential human errors during the transition.