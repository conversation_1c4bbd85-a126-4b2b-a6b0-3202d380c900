This comprehensive modernization plan outlines the strategic transition of your legacy ASP.NET Offer Letter Print functionality to a robust, modern Django application. Our approach leverages Django's strengths in backend development, coupled with a highly interactive frontend stack (HTMX, Alpine.js, DataTables) to deliver a high-performance, maintainable, and scalable solution.

We prioritize automated conversion processes and clear, actionable instructions designed to be understood by all stakeholders, ensuring a smooth and efficient migration.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The primary data source is `tblHR_Offer_Master`. The ASP.NET code extensively joins and transforms data from several lookup tables to present a consolidated view and generate reports.

**Core Table (`tblHR_Offer_Master` - will become `OfferLetter` model):**
*   **Table Name:** `tblHR_Offer_Master`
*   **Key Columns:** `OfferId` (Primary Key, Integer), `SysDate` (Date/Time), `TypeOf` (Integer), `StaffType` (Integer), `Title` (String), `EmployeeName` (String), `Designation` (Integer), `DutyHrs` (Integer), `InterviewedBy` (String/ID), `ContactNo` (String), `salary` (Decimal/Float), `Increment` (Integer), `Bonus` (Decimal/Float), `ExGratia` (Decimal/Float), `VehicleAllowance` (Decimal/Float), `LTA` (Decimal/Float), `Loyalty` (Decimal/Float), `AttBonusPer1` (Decimal/Float), `AttBonusPer2` (Decimal/Float), `PFEmployee` (Decimal/Float), `PFCompany` (Decimal/Float), `OTHrs` (Integer), `OverTime` (Integer), `CompId` (Integer), `FinYearId` (String).

**Related Lookup Tables (will become separate Django models):**
*   `tblHR_EmpType` (Id, Description)
*   `tblHR_Designation` (Id, Type)
*   `tblHR_DutyHour` (Id, Hours)
*   `tblHR_OfficeStaff` (EmpId, EmployeeName, OfferId, ResignationDate, Department, BGGroup, Grade, CompId, FinYearId)
*   `tblHR_OTHour` (Id, Hours)
*   `tblHR_OverTime` (Id, Description)
*   `tblHR_Grade` (Id, Symbol)
*   `tblHR_Offer_Accessories` (Id, MId, IncludesIn, Qty, Amount)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET page primarily focuses on **displaying and reporting** offer letter data. There are no direct data modification (Create, Update, Delete) operations exposed on this specific page for the offer letters themselves.

1.  **List/Search Offer Letters:**
    *   **Purpose:** Display a paginated list of offer letters, filterable by employee name.
    *   **Input:** Optional `Employee Name` for search.
    *   **Output:** A table presenting a consolidated view of offer letter details, incorporating data from various related lookup tables.
    *   **Key Logic:** Dynamic data fetching, data transformation (e.g., date formatting, company type mapping), and conditional status determination ("Confirm").
    *   **Interactions:** Initial load, search button, "View All" button (for resetting filter).
2.  **Offer Letter Details/Action (Print/Redirect):**
    *   **Purpose:** Navigate to a separate page for printing or detailed viewing of a selected offer letter.
    *   **Input:** `OfferId` and `Increment` value from the selected row.
    *   **Output:** Redirects to a new URL with query parameters.
    *   **Interaction:** "Select" link button for each row in the list.
3.  **Comprehensive Salary Report Export:**
    *   **Purpose:** Generate and download an Excel report containing a detailed breakdown of salary components for employees with offer letters.
    *   **Input:** None (triggered by "View All" button's secondary function).
    *   **Output:** An Excel (`.xls`) file.
    *   **Key Logic:** Extensive calculations involving various percentages and fixed amounts, derived from `tblHR_Offer_Master` and `tblHR_Offer_Accessories` data. This involves significant business logic for salary computation (Basic, DA, HRA, PF, PTax, Gratuity, CTC, etc.).
4.  **Employee Name Autocomplete:**
    *   **Purpose:** Provide real-time suggestions for employee names in the search box.
    *   **Input:** Partial employee name string.
    *   **Output:** A list of matching employee names.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles in the user interface.

**Inferred UI Components for Django:**

*   **Search and Report Section:**
    *   An input field for "Employee Name" (`TextBox1`) with **HTMX-powered autocomplete**.
    *   A "Search" button (`Button1`) to trigger list filtering via HTMX.
    *   A "View All" button (`btnViewAll`) that will also act as an **Excel report download trigger**.
*   **Dynamic Data Table:**
    *   The `GridView2` will be replaced by a standard HTML table rendered with **Django templates** and enhanced by **DataTables.js** for client-side search, sort, and pagination.
    *   Each row will contain a "Select" button to trigger the detail view redirect.
    *   Each row will also include a dynamic "Increment" dropdown. This can be pre-populated or dynamically loaded per row using HTMX.
*   **Modals:** While not explicitly used as modals in the ASP.NET page, for a modern UX, any future CRUD operations or confirmation dialogs would use HTMX-driven modals with Alpine.js.

### Step 4: Generate Django Code

We will create a new Django application, `hr_offer`, to host this functionality.

#### 4.1 Models (`hr_offer/models.py`)

**Task:** Create Django models representing the database schema. All business logic related to data transformation, calculations, and lookups will reside within the `OfferLetter` model or its custom manager, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime
from decimal import Decimal

# Helper class to encapsulate complex calculations, replicating ASP.NET's 'clsFunctions'
# This promotes separation of concerns within the Fat Model paradigm, keeping the model clean
# while centralizing calculation logic.
class OfferCalculationService:
    @staticmethod
    def get_basic_salary(gross_salary, staff_type):
        # Placeholder for actual business rules for Basic Salary calculation.
        # Example: Based on ASP.NET 'fun.Offer_Cal(GrossSalary, 1, 1, StaffType)'
        if staff_type == 1: # Example: Assuming 'StaffType' 1 has a different calculation
            return gross_salary * Decimal('0.40')
        return gross_salary * Decimal('0.35') # Default

    @staticmethod
    def get_da(gross_salary, staff_type):
        # Placeholder for DA calculation.
        # Example: Based on ASP.NET 'fun.Offer_Cal(GrossSalary, 2, 1, StaffType)'
        if staff_type == 1:
            return gross_salary * Decimal('0.20')
        return gross_salary * Decimal('0.25')

    @staticmethod
    def get_hra(gross_salary, offer_type):
        # Placeholder for HRA calculation.
        # Example: Based on ASP.NET 'fun.Offer_Cal(GrossSalary, 2, 1, TypeOf)'
        if offer_type == 1: # SAPL
            return gross_salary * Decimal('0.15')
        return gross_salary * Decimal('0.10') # NEHA

    @staticmethod
    def get_conveyance(gross_salary, offer_type):
        # Placeholder for Conveyance calculation.
        # Example: Based on ASP.NET 'fun.Offer_Cal(GrossSalary, 4, 1, TypeOf)'
        if offer_type == 1:
            return Decimal('1600.00')
        return Decimal('800.00')

    @staticmethod
    def get_education_allowance(gross_salary, offer_type):
        # Placeholder for Education Allowance calculation.
        # Example: Based on ASP.NET 'fun.Offer_Cal(GrossSalary, 5, 1, TypeOf)'
        if offer_type == 1:
            return Decimal('1000.00')
        return Decimal('500.00')

    @staticmethod
    def get_medical_allowance(gross_salary, offer_type):
        # Placeholder for Medical Allowance calculation.
        # Example: Based on ASP.NET 'fun.Offer_Cal(GrossSalary, 6, 1, TypeOf)'
        if offer_type == 1:
            return Decimal('1250.00')
        return Decimal('625.00')

    @staticmethod
    def get_pf_employee(gross_salary, pf_emp_percent):
        # Placeholder for Employee PF calculation.
        # Example: Based on ASP.NET 'fun.Pf_Cal(GrossSalary, 1, PFEmployeePercent)'
        return (gross_salary * pf_emp_percent) / Decimal('100.00') if pf_emp_percent else Decimal('0.00')

    @staticmethod
    def get_pf_company(gross_salary, pf_comp_percent):
        # Placeholder for Company PF calculation.
        # Example: Based on ASP.NET 'fun.Pf_Cal(GrossSalary, 2, PFCompanyPercent)'
        return (gross_salary * pf_comp_percent) / Decimal('100.00') if pf_comp_percent else Decimal('0.00')
    
    @staticmethod
    def get_ptax(total_income):
        # Placeholder for Professional Tax (PTax) calculation.
        # Example: Based on ASP.NET 'fun.PTax_Cal'
        if total_income > Decimal('25000.00'): # Example slab
            return Decimal('200.00')
        return Decimal('0.00')

    @staticmethod
    def get_gratuity(gross_salary, offer_type):
        # Placeholder for Gratuity calculation.
        # Example: Based on ASP.NET 'fun.Gratuity_Cal'
        # This is often complex; assuming a simple percentage for this example.
        if offer_type == 1: # SAPL
            return gross_salary * Decimal('0.0481') # Approx 15/26 days per month * 12 months / 12 years (simplified)
        return gross_salary * Decimal('0.0481') # Same for NEHA for simplicity


# Lookup Tables - mapped to Django models
class EmployeeType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_EmpType'
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'

    def __str__(self):
        return self.description or 'N/A'

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.type or 'N/A'

class DutyHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.CharField(db_column='Hours', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'Duty Hour'
        verbose_name_plural = 'Duty Hours'

    def __str__(self):
        return self.hours or 'N/A'

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    # The ASP.NET code shows tblHR_OfficeStaff having an OfferId, which points to tblHR_Offer_Master
    # This implies OfficeStaff can be linked to an OfferLetter, and its presence indicates confirmation.
    offer = models.ForeignKey('OfferLetter', models.DO_NOTHING, db_column='OfferId', blank=True, null=True)
    resignation_date = models.CharField(db_column='ResignationDate', max_length=50, blank=True, null=True)
    department = models.CharField(db_column='Department', max_length=255, blank=True, null=True)
    bg_group = models.CharField(db_column='BGGroup', max_length=255, blank=True, null=True)
    grade = models.ForeignKey('Grade', models.DO_NOTHING, db_column='Grade', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.CharField(db_column='FinYearId', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name or 'N/A'

class OTHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.CharField(db_column='Hours', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OTHour'
        verbose_name = 'OT Hour'
        verbose_name_plural = 'OT Hours'

    def __str__(self):
        return self.hours or 'N/A'

class OverTime(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OverTime'
        verbose_name = 'Overtime Type'
        verbose_name_plural = 'Overtime Types'

    def __str__(self):
        return self.description or 'N/A'

class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.symbol or 'N/A'

class OfferAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an Id PK
    offer = models.ForeignKey('OfferLetter', models.DO_NOTHING, db_column='MId')
    includes_in = models.CharField(db_column='IncludesIn', max_length=50, blank=True, null=True) # 1: CTC, 2: TakeHome, 3: Both
    qty = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=2, blank=True, null=True)
    amount = models.DecimalField(db_column='Amount', max_digits=10, decimal_places=2, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'Offer Accessory'
        verbose_name_plural = 'Offer Accessories'

    def __str__(self):
        return f"Accessory for Offer {self.offer.offer_id}"

class OfferLetterManager(models.Manager):
    """
    Custom manager to encapsulate the complex data retrieval and transformation
    logic from the ASP.NET binddata function (for listing) and btnViewAll_Click (for reporting).
    This keeps the main OfferLetter model cleaner and centralizes querying logic.
    """
    def get_offer_list_display_data(self, company_id, financial_year_id, employee_name=None):
        queryset = self.get_queryset().filter(
            comp_id=company_id,
            fin_year_id__lte=financial_year_id
        ).order_by('-offer_id')

        if employee_name:
            queryset = queryset.filter(employee_name__icontains=employee_name)
        
        # Use select_related to fetch related objects in a single query for efficiency
        queryset = queryset.select_related(
            'staff_type_rel', 
            'designation_rel', 
            'duty_hours_rel', 
            'interviewed_by_staff'
        ).prefetch_related(
            'officestaff_set' # To check confirmation status efficiently
        )
        
        # The list items are now full OfferLetter objects with pre-fetched relations,
        # allowing properties on the model to handle display logic.
        return queryset

    def get_salary_report_data(self, company_id, financial_year_id):
        # This replicates the comprehensive report generation logic from ASP.NET's btnViewAll_Click
        # It's important to carefully translate the SQL joins and filters.
        
        # The original SQL implies that only offers linked to an OfficeStaff record 
        # that meets specific criteria are included in the report.
        queryset = self.get_queryset().filter(
            officestaff__resignation_date__exact='', # Assuming empty string means not resigned
            officestaff__emp_id__ne='Sapl0001', # Exclude specific EmpId
            officestaff__comp_id=company_id,
            officestaff__fin_year_id__lte=financial_year_id
        ).select_related(
            'staff_type_rel', 
            'designation_rel', 
            'duty_hours_rel', 
            'ot_hours_rel', 
            'overtime_rel'
        ).prefetch_related(
            'officestaff_set', # For accessing EmpId, EmployeeName, Grade from OfficeStaff
            'offeraccessory_set' # For accessories calculations
        ).order_by('officestaff__emp_id') # Order by OfficeStaff EmpId as in original SQL

        report_data = []
        for offer in queryset:
            # Get the relevant OfficeStaff instance that satisfied the join/filter conditions
            # If multiple OfficeStaff records link to one offer, pick the one that matched the query.
            # Assuming there's a primary match or first valid match is acceptable.
            office_staff_instance = offer.officestaff_set.filter(
                resignation_date__exact='',
                emp_id__ne='Sapl0001',
                comp_id=company_id,
                fin_year_id__lte=financial_year_id
            ).first()

            if not office_staff_instance:
                continue # Should not happen if queryset was constructed correctly

            # Prepare initial data for the report row
            data = {
                'emp_id': office_staff_instance.emp_id,
                'employee_name': f"{offer.title or ''} {office_staff_instance.employee_name or ''}".strip(),
                'gross_salary': offer.salary,
                'duty_hours': offer.duty_hours_rel.hours if offer.duty_hours_rel else 'N/A',
                'ot_hours': offer.ot_hours_rel.hours if offer.ot_hours_rel else 'N/A',
                'overtime_type': offer.overtime_rel.description if offer.overtime_rel else 'N/A',
                'designation': offer.designation_rel.type if offer.designation_rel else 'N/A',
            }

            # --- Start complex salary calculations (replicate C# fun.Offer_Cal, etc.) ---
            gross_salary_dec = offer.salary if offer.salary is not None else Decimal('0.00')
            
            data['basic'] = OfferCalculationService.get_basic_salary(gross_salary_dec, offer.staff_type)
            data['da'] = OfferCalculationService.get_da(gross_salary_dec, offer.staff_type)
            data['hra'] = OfferCalculationService.get_hra(gross_salary_dec, offer.type_of)
            data['conveyance'] = OfferCalculationService.get_conveyance(gross_salary_dec, offer.type_of)
            data['education'] = OfferCalculationService.get_education_allowance(gross_salary_dec, offer.type_of)
            data['medical_allowance'] = OfferCalculationService.get_medical_allowance(gross_salary_dec, offer.type_of)
            
            data['att_bonus_per1'] = offer.att_bonus_per1
            att_bonus1 = (gross_salary_dec * (offer.att_bonus_per1 or Decimal('0.00'))) / Decimal('100.00')
            data['attendance_bonus'] = att_bonus1
            
            data['att_bonus_per2'] = offer.att_bonus_per2
            att_bonus2 = (gross_salary_dec * (offer.att_bonus_per2 or Decimal('0.00'))) / Decimal('100.00')
            data['attendance_bonus2'] = att_bonus2
            
            data['ex_gratia'] = offer.ex_gratia or Decimal('0.00')

            data['pf_emp_per'] = offer.pf_employee_percent
            pf_employee_amount = OfferCalculationService.get_pf_employee(gross_salary_dec, offer.pf_employee_percent)
            data['pf_employee'] = pf_employee_amount
            
            data['pf_comp_per'] = offer.pf_company_percent
            pf_company_amount = OfferCalculationService.get_pf_company(gross_salary_dec, offer.pf_company_percent)
            data['pf_company'] = pf_company_amount

            total_income_for_ptax = gross_salary_dec + (offer.bonus or Decimal('0.00')) + data['ex_gratia']
            ptax = OfferCalculationService.get_ptax(total_income_for_ptax)
            data['ptax'] = ptax

            accessories_amt_ctc = Decimal('0.00')
            accessories_amt_takehome = Decimal('0.00')
            accessories_amt_both = Decimal('0.00')
            
            for acc in offer.offeraccessory_set.all(): # Already prefetched
                total_acc_amount = (acc.qty or Decimal('0.00')) * (acc.amount or Decimal('0.00'))
                if acc.includes_in == '1': # CTC
                    accessories_amt_ctc += total_acc_amount
                elif acc.includes_in == '2': # TakeHome
                    accessories_amt_takehome += total_acc_amount
                elif acc.includes_in == '3': # Both
                    accessories_amt_both += total_acc_amount
            
            data['take_home'] = round(
                (gross_salary_dec + data['ex_gratia'] + accessories_amt_takehome + accessories_amt_both) - (pf_employee_amount + ptax)
            , 2)
            
            data['take_home_with_attend1'] = round(data['take_home'] + att_bonus1, 2)
            data['take_home_with_attend2'] = round(data['take_home'] + att_bonus2, 2)

            data['loyalty'] = offer.loyalty or Decimal('0.00')
            data['lta'] = offer.lta or Decimal('0.00')
            data['bonus'] = offer.bonus or Decimal('0.00')
            
            gratuity = OfferCalculationService.get_gratuity(gross_salary_dec, offer.type_of)
            data['gratuity'] = gratuity
            
            data['vehicle_allowance'] = offer.vehicle_allowance or Decimal('0.00')

            ctc = round(
                gross_salary_dec + data['bonus'] + data['loyalty'] + data['lta'] + gratuity + pf_company_amount + data['ex_gratia'] + accessories_amt_ctc + accessories_amt_both
            , 2)
            data['ctc'] = ctc

            data['ctc_attend_bonus1'] = round(ctc + att_bonus1, 2)
            data['ctc_attend_bonus2'] = round(ctc + att_bonus2, 2)
            # --- End complex calculations ---

            report_data.append(data)
        return report_data


class OfferLetter(models.Model):
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    type_of = models.IntegerField(db_column='TypeOf', blank=True, null=True) # 1: SAPL, 2: NEHA
    staff_type = models.IntegerField(db_column='StaffType', blank=True, null=True) # FK
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    designation = models.IntegerField(db_column='Designation', blank=True, null=True) # FK
    duty_hrs = models.IntegerField(db_column='DutyHrs', blank=True, null=True) # FK
    interviewed_by = models.CharField(db_column='InterviewedBy', max_length=50, blank=True, null=True) # Assuming EmpId string
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    salary = models.DecimalField(db_column='salary', max_digits=18, decimal_places=2, blank=True, null=True) # Gross Salary
    increment = models.IntegerField(db_column='Increment', blank=True, null=True)
    bonus = models.DecimalField(db_column='Bonus', max_digits=18, decimal_places=2, blank=True, null=True)
    ex_gratia = models.DecimalField(db_column='ExGratia', max_digits=18, decimal_places=2, blank=True, null=True)
    vehicle_allowance = models.DecimalField(db_column='VehicleAllowance', max_digits=18, decimal_places=2, blank=True, null=True)
    lta = models.DecimalField(db_column='LTA', max_digits=18, decimal_places=2, blank=True, null=True)
    loyalty = models.DecimalField(db_column='Loyalty', max_digits=18, decimal_places=2, blank=True, null=True)
    att_bonus_per1 = models.DecimalField(db_column='AttBonusPer1', max_digits=5, decimal_places=2, blank=True, null=True)
    att_bonus_per2 = models.DecimalField(db_column='AttBonusPer2', max_digits=5, decimal_places=2, blank=True, null=True)
    pf_employee_percent = models.DecimalField(db_column='PFEmployee', max_digits=5, decimal_places=2, blank=True, null=True) # Stored as percent in DB
    pf_company_percent = models.DecimalField(db_column='PFCompany', max_digits=5, decimal_places=2, blank=True, null=True) # Stored as percent in DB
    ot_hrs = models.IntegerField(db_column='OTHrs', blank=True, null=True) # FK
    over_time = models.IntegerField(db_column='OverTime', blank=True, null=True) # FK
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.CharField(db_column='FinYearId', max_length=10, blank=True, null=True)

    # Foreign Key Relationships for easy access
    staff_type_rel = models.ForeignKey(EmployeeType, models.DO_NOTHING, db_column='StaffType', related_name='offer_letters_by_staff_type', blank=True, null=True)
    designation_rel = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', related_name='offer_letters_by_designation', blank=True, null=True)
    duty_hours_rel = models.ForeignKey(DutyHour, models.DO_NOTHING, db_column='DutyHrs', related_name='offer_letters_by_duty_hours', blank=True, null=True)
    ot_hours_rel = models.ForeignKey(OTHour, models.DO_NOTHING, db_column='OTHrs', related_name='offer_letters_by_ot_hours', blank=True, null=True)
    overtime_rel = models.ForeignKey(OverTime, models.DO_NOTHING, db_column='OverTime', related_name='offer_letters_by_overtime', blank=True, null=True)
    # Assuming InterviewedBy stores EmpId which links to OfficeStaff's EmpId
    interviewed_by_staff = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='InterviewedBy', to_field='emp_id', related_name='offers_interviewed', blank=True, null=True)

    objects = OfferLetterManager() # Custom manager for complex queries

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Letter'
        verbose_name_plural = 'Offer Letters'

    def __str__(self):
        return f"Offer {self.offer_id} - {self.employee_name}"

    # --- Fat Model: Properties for derived display data and business logic ---
    @property
    def formatted_sys_date(self):
        """Formats SysDate to DD-MM-YYYY, replicating ASP.NET's fun.FromDateDMY."""
        return self.sys_date.strftime('%d-%m-%Y') if self.sys_date else 'N/A'

    @property
    def type_of_company(self):
        """Maps TypeOf integer to 'SAPL' or 'NEHA'."""
        if self.type_of == 1:
            return 'SAPL'
        elif self.type_of == 2:
            return 'NEHA'
        return 'N/A'

    @property
    def employee_type_display(self):
        """Retrieves employee type description from related EmployeeType model."""
        return self.staff_type_rel.description if self.staff_type_rel else 'N/A'

    @property
    def full_employee_name(self):
        """Concatenates Title and EmployeeName."""
        return f"{self.title or ''} {self.employee_name or ''}".strip()

    @property
    def designation_display(self):
        """Retrieves designation type from related Designation model."""
        return self.designation_rel.type if self.designation_rel else 'N/A'

    @property
    def duty_hours_display(self):
        """Retrieves duty hours from related DutyHour model."""
        return self.duty_hours_rel.hours if self.duty_hours_rel else 'N/A'

    @property
    def interviewed_by_name(self):
        """Retrieves interviewer's name from related OfficeStaff model."""
        return self.interviewed_by_staff.employee_name if self.interviewed_by_staff else 'N/A'

    @property
    def confirmation_status(self):
        """Determines 'Confirm' status based on existence of related OfficeStaff record."""
        # This checks if there is any OfficeStaff entry pointing to this OfferLetter.
        # This aligns with the ASP.NET logic where `tblHR_OfficeStaff having OfferId` meant "Confirm".
        return 'Confirm' if self.officestaff_set.exists() else ''
    
    def get_increment_options(self):
        """Generates a list of increment options for the dropdown (decrementing from current increment)."""
        return list(range(self.increment, -1, -1)) if self.increment is not None else []

```

#### 4.2 Forms (`hr_offer/forms.py`)

**Task:** Define a Django form for user input. Since this page primarily has a search box, we'll create a simple form for that. No full CRUD form is needed for this specific ASP.NET page's functionality.

```python
from django import forms
from .models import OfferLetter

class OfferLetterSearchForm(forms.Form):
    employee_name = forms.CharField(
        required=False,
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            # HTMX attributes for autocomplete
            'hx-get': "{% url 'hr_offer:autocomplete_employee_name' %}", 
            'hx-trigger': "keyup changed delay:500ms, search", # Trigger on keyup, with a delay
            'hx-target': "#employee-suggestions", # Target for suggestions
            'hx-swap': "innerHTML",
            'autocomplete': 'off', # Prevent browser autocomplete interference
            # Alpine.js for keyboard navigation in suggestions (assuming suggestions are in a div below)
            '@keydown.arrow-down': '$el.nextElementSibling.firstElementChild.focus()',
            '@keydown.arrow-up': '$el.nextElementSibling.lastElementChild.focus()',
        })
    )

```

#### 4.3 Views (`hr_offer/views.py`)

**Task:** Implement the list display, search, report generation, and autocomplete functionalities using Django Class-Based Views (CBVs). Views will remain thin, delegating complex data operations to models.

```python
import pandas as pd
from django.views.generic import ListView, View
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.urls import reverse
from django.conf import settings # To access settings like COMP_ID and FIN_YEAR_ID
from django.template.loader import render_to_string
from django.contrib import messages
from django.db.models import Q # For complex queries if needed

from .models import OfferLetter, OfficeStaff
from .forms import OfferLetterSearchForm

# Assuming these are accessible from session or settings, replicating ASP.NET's session variables
# For production, these would be retrieved from user session, settings, or multi-tenancy context.
DEFAULT_COMPANY_ID = getattr(settings, 'DEFAULT_COMP_ID', 1)
DEFAULT_FINANCIAL_YEAR_ID = getattr(settings, 'DEFAULT_FIN_YEAR_ID', '2024')

class OfferLetterListView(ListView):
    """
    Main view to display the Offer Letter list page.
    This view renders the initial page with the search form and an empty container for the table.
    The actual table content is loaded via HTMX.
    """
    model = OfferLetter
    template_name = 'hr_offer/offerletter/list.html'
    context_object_name = 'offer_letters' # Although the table is loaded separately, this is good practice.

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = OfferLetterSearchForm(self.request.GET)
        return context

class OfferLetterTablePartialView(View):
    """
    HTMX-specific view to return the partial HTML for the offer letter table.
    This is triggered on page load, search, or refresh events.
    """
    def get(self, request, *args, **kwargs):
        form = OfferLetterSearchForm(request.GET)
        employee_name = None
        if form.is_valid():
            employee_name = form.cleaned_data.get('employee_name')
        
        # Use a placeholder for dynamic CompanyId and FinYearId
        # In a real app, these would come from user's session/profile/tenant context
        company_id = DEFAULT_COMPANY_ID
        financial_year_id = DEFAULT_FINANCIAL_YEAR_ID

        offer_letters = OfferLetter.objects.get_offer_list_display_data(
            company_id=company_id,
            financial_year_id=financial_year_id,
            employee_name=employee_name
        )
        
        # Render the partial table template
        context = {
            'offer_letters': offer_letters,
            'current_company_id': company_id, # Pass context for any internal logic if needed
            'current_financial_year_id': financial_year_id,
        }
        return HttpResponse(render_to_string('hr_offer/offerletter/_offerletter_table.html', context, request))

class OfferLetterDetailRedirectView(View):
    """
    Handles the "Select" action, redirecting to a print/details page with parameters.
    Replicates the Response.Redirect behavior.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            offer_letter = OfferLetter.objects.get(pk=pk)
            # The 'Increment' value came from a dropdown in the GridView row in ASP.NET.
            # In Django, it would be passed as a query param from the HTMX trigger.
            increment = request.GET.get('increment', offer_letter.increment) # Default to stored increment
            
            # Replicate other ASP.NET query parameters if they are truly dynamic
            # For now, using placeholders for T, Key, ModId, SubModId
            random_key = 'randomly_generated_string' # Replace with actual random key generation if critical
            
            # Construct the URL for the details/print page.
            # Assuming 'hr_offer:offerletter_print_details' is the name of the target URL pattern.
            # The target page would need to be migrated separately if it's not just a static print.
            details_url = reverse('hr_offer:offerletter_print_details', kwargs={'pk': pk})
            
            # Add query parameters manually as Django's reverse doesn't support arbitrary params easily
            # Use urllib.parse.urlencode for robust parameter handling if many params.
            query_params = f"increment={increment}&T=1&Key={random_key}&ModId=12&SubModId=25"
            full_redirect_url = f"{details_url}?{query_params}"

            return redirect(full_redirect_url)
        except OfferLetter.DoesNotExist:
            messages.error(request, "Offer Letter not found.")
            return redirect(reverse('hr_offer:offerletter_list')) # Redirect back to list on error

class OfferLetterReportExportView(View):
    """
    Handles the generation and export of the comprehensive salary report to Excel.
    Replicates the btnViewAll_Click complex logic and Excel export.
    """
    def get(self, request, *args, **kwargs):
        company_id = DEFAULT_COMPANY_ID
        financial_year_id = DEFAULT_FINANCIAL_YEAR_ID

        report_data = OfferLetter.objects.get_salary_report_data(
            company_id=company_id,
            financial_year_id=financial_year_id
        )

        if not report_data:
            messages.warning(request, "No data to export for the selected criteria.")
            # For HTMX, a 204 No Content can trigger client-side message or simply do nothing
            return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'}) # Example HTMX trigger
            # For non-HTMX, a redirect with a message:
            # return redirect(reverse('hr_offer:offerletter_list'))

        # Convert list of dictionaries to pandas DataFrame
        df = pd.DataFrame(report_data)

        # Prepare Excel response
        response = HttpResponse(content_type='application/vnd.ms-excel')
        filename = f"OfferData_{timezone.now().strftime('%d-%m-%Y')}.xls"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # Use pandas to_excel to write to the response body
        # Ensure the Excel writer is compatible (.xls, e.g., 'xlwt' engine)
        # For modern XLSX, 'openpyxl' is preferred, but original was .xls
        try:
            df.to_excel(response, index=False, engine='xlwt')
        except ImportError:
            messages.error(request, "xlwt library not found. Please install it to enable Excel export (pip install xlwt).")
            return HttpResponse(status=500, headers={'HX-Trigger': 'showMessage'})
        except Exception as e:
            messages.error(request, f"Error generating Excel file: {e}")
            return HttpResponse(status=500, headers={'HX-Trigger': 'showMessage'})

        return response

class EmployeeNameAutocompleteView(View):
    """
    Provides autocomplete suggestions for employee names.
    This replaces the ASP.NET WebMethod GetCompletionList.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        if not prefix_text:
            return HttpResponse("") # Return empty if no text

        # Use a placeholder for dynamic CompanyId
        company_id = DEFAULT_COMPANY_ID

        # Query unique employee names from OfferLetter, filtering by company_id and prefix
        # Use distinct() for unique names and icontains for case-insensitive partial match
        # Limit the results for performance.
        employee_names = OfferLetter.objects.filter(
            comp_id=company_id,
            employee_name__icontains=prefix_text
        ).values_list('employee_name', flat=True).distinct()[:10] # Limit to 10 suggestions as in ASP.NET comments

        # Prepare HTML for HTMX to swap in for suggestions
        html_suggestions = ""
        if employee_names:
            # Wrap in an Alpine.js component for keyboard navigation and selection
            # The 'x-trap' directive ensures focus stays within the dropdown for keyboard navigation
            html_suggestions = f"""
            <div x-data="{{}}" @click.outside="$el.innerHTML = ''" 
                 class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"
                 x-trap.noscroll>
                <ul class="max-h-60 overflow-y-auto py-1">
            """
            for name in sorted(employee_names): # ASP.NET Array.Sort(main)
                html_suggestions += f"""
                    <li>
                        <button type="button" 
                                class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                                @click="$event.target.closest('form').querySelector('input[name=\\'employee_name\\']').value = '{name}'; $event.target.closest('div').innerHTML = '';"
                                hx-get="{{% url 'hr_offer:offerletter_table' %}}?employee_name={name}"
                                hx-target="#offerletterTable-container"
                                hx-swap="innerHTML"
                                hx-indicator="#loadingIndicator">
                            {name}
                        </button>
                    </li>
                """
            html_suggestions += "</ul></div>"
        
        return HttpResponse(html_suggestions)

# Placeholder for the detail view (OfferLetter_Print_Details.aspx)
# This would be a separate migration if it's complex.
# For now, a simple placeholder.
class OfferLetterPrintDetailsView(View):
    def get(self, request, pk, *args, **kwargs):
        offer_id = pk
        increment = request.GET.get('increment')
        t_param = request.GET.get('T')
        key_param = request.GET.get('Key')
        mod_id = request.GET.get('ModId')
        sub_mod_id = request.GET.get('SubModId')

        try:
            offer_letter = OfferLetter.objects.get(pk=offer_id)
            context = {
                'offer_letter': offer_letter,
                'increment': increment,
                't_param': t_param,
                'key_param': key_param,
                'mod_id': mod_id,
                'sub_mod_id': sub_mod_id,
                'message': f"Displaying details for Offer ID: {offer_id} with Increment: {increment}. This page would typically contain the actual print layout."
            }
            return HttpResponse(render_to_string('hr_offer/offerletter/print_details.html', context, request))
        except OfferLetter.DoesNotExist:
            messages.error(request, "Offer Letter not found.")
            return redirect(reverse('hr_offer:offerletter_list'))

```

#### 4.4 Templates

**Task:** Create HTML templates for each view. These templates will extend `core/base.html` and utilize HTMX, Alpine.js, and DataTables for a dynamic user experience.

**Template Files:**

1.  **`hr_offer/offerletter/list.html`**: The main page displaying the search bar and the container for the data table.
2.  **`hr_offer/offerletter/_offerletter_table.html`**: A partial template that renders just the DataTables table, loaded via HTMX.
3.  **`hr_offer/offerletter/_employee_suggestions.html`**: A partial template for autocomplete suggestions.
4.  **`hr_offer/offerletter/print_details.html`**: A placeholder for the redirected detail/print view.

**`hr_offer/offerletter/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-5">Offer Letter - Print</h2>
        
        <form class="space-y-4" hx-get="{% url 'hr_offer:offerletter_table' %}" hx-target="#offerletterTable-container" hx-swap="innerHTML" hx-indicator="#loadingIndicator">
            {% csrf_token %}
            <div class="flex items-end space-x-4">
                <div class="flex-grow relative">
                    <label for="{{ search_form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.employee_name.label }}
                    </label>
                    {{ search_form.employee_name }}
                    {# Autocomplete suggestions will be loaded here via HTMX into a separate div #}
                    <div id="employee-suggestions"></div>
                </div>
                
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Search
                </button>
                <button type="button" 
                        class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'hr_offer:offerletter_table' %}"
                        hx-target="#offerletterTable-container"
                        hx-swap="innerHTML"
                        hx-indicator="#loadingIndicator">
                    View All
                </button>
                <a href="{% url 'hr_offer:offerletter_report_export' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out inline-flex items-center"
                   hx-boost="false"> {# Disable HTMX boost to force full page navigation for download #}
                    Export Report
                </a>
            </div>
        </form>
    </div>

    <div id="offerletterTable-container"
         hx-trigger="load, refreshOfferLetterList from:body" {# Load on page load and custom event #}
         hx-get="{% url 'hr_offer:offerletter_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div id="loadingIndicator" class="text-center p-8 hidden" hx-indicator>
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading data...</p>
        </div>
        <div class="text-center p-8">
            <p class="text-gray-500">Loading offer letter data...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<script>
    // Custom event listener for messages, adapting for HTMX triggers
    document.body.addEventListener('showMessage', function(evt) {
        // You would typically have a dedicated message display area (e.g., in base.html)
        // For simplicity, using alert or a temporary div for this example
        let messageText = 'An operation completed successfully.';
        if (evt.detail && evt.detail.messages) {
            messageText = evt.detail.messages.join('<br>');
        } else if (evt.detail && evt.detail.message) {
            messageText = evt.detail.message;
        }
        // Example: Display messages in a div or use a toast library
        console.log("Message from server:", messageText);
        // You can use Alpine.js for a more integrated toast notification system here.
    });

    // Handle Alpine.js for dropdowns/modals as needed (e.g., if dynamically loaded)
    document.addEventListener('alpine:init', () => {
        // Alpine.js components would be defined here if needed for dynamic UI state within rows
    });
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css" rel="stylesheet">
{% endblock %}
```

**`hr_offer/offerletter/_offerletter_table.html`**

```html
{% load humanize %} {# Optional, for formatting numbers if needed #}

<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="offerletterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Id</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type Of</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Type</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Increment</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duty Hrs</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interviewed by</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Salary</th>
                <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for offer in offer_letters %}
            <tr x-data="{ selectedIncrement: '{{ offer.increment|default_if_none:0 }}' }"> {# Alpine.js to hold selected increment #}
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.formatted_sys_date }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ offer.offer_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.type_of_company }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.employee_type_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    <select x-model="selectedIncrement" class="block w-full py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        {% for inc_option in offer.get_increment_options %}
                            <option value="{{ inc_option }}">{{ inc_option }}</option>
                        {% endfor %}
                    </select>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.full_employee_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.designation_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.duty_hours_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.interviewed_by_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.contact_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ offer.salary|intcomma }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ offer.confirmation_status }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-center">
                    <a href="{% url 'hr_offer:offerletter_detail_redirect' pk=offer.offer_id %}?increment={{ offer.increment|default_if_none:0 }}"
                       class="text-blue-600 hover:text-blue-900 px-3 py-1 border border-blue-600 rounded-md text-xs">
                        Select
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="14" class="py-8 text-center text-gray-500 text-lg">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization must happen after the table is loaded into the DOM
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#offerletterTable')) {
            $('#offerletterTable').DataTable({
                "pageLength": 17, // Replicate ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "responsive": true, // Make table responsive
                "pagingType": "simple_numbers", // Basic pagination
                "info": true, // Show "Showing X to Y of Z entries"
                "searching": true, // Enable search box
                "ordering": true // Enable column sorting
            });
        }
    });

    // Re-initialize DataTables if HTMX swaps the table
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'offerletterTable-container') {
            $('#offerletterTable').DataTable({
                "pageLength": 17,
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "responsive": true,
                "pagingType": "simple_numbers",
                "info": true,
                "searching": true,
                "ordering": true
            });
        }
    });
</script>
```

**`hr_offer/offerletter/print_details.html`** (Placeholder for the redirected page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Offer Letter Details</h2>
        <p class="text-gray-700">{{ message }}</p>
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-800">Selected Offer Details:</h3>
            <p><strong>Offer ID:</strong> {{ offer_letter.offer_id }}</p>
            <p><strong>Employee Name:</strong> {{ offer_letter.full_employee_name }}</p>
            <p><strong>Date:</strong> {{ offer_letter.formatted_sys_date }}</p>
            <p><strong>Gross Salary:</strong> {{ offer_letter.salary }}</p>
            <p><strong>Increment Value:</strong> {{ increment }}</p>
            <p><strong>Type Of Company:</strong> {{ offer_letter.type_of_company }}</p>
            <p><strong>Key Parameter (Example):</strong> {{ key_param }}</p>
            {# Add more offer_letter details as needed for the print view #}
        </div>
        <div class="mt-8">
            <a href="{% url 'hr_offer:offerletter_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Back to List
            </a>
            <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-md shadow-sm ml-4 transition duration-150 ease-in-out">
                Print
            </button>
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`hr_offer/urls.py`)

**Task:** Define URL patterns to map incoming requests to the appropriate views.

```python
from django.urls import path
from .views import (
    OfferLetterListView, 
    OfferLetterTablePartialView,
    OfferLetterDetailRedirectView,
    OfferLetterReportExportView,
    EmployeeNameAutocompleteView,
    OfferLetterPrintDetailsView,
)

app_name = 'hr_offer' # Namespace for URLs

urlpatterns = [
    # Main list page
    path('offerletter/', OfferLetterListView.as_view(), name='offerletter_list'),
    
    # HTMX endpoint for the data table (partial refresh)
    path('offerletter/table/', OfferLetterTablePartialView.as_view(), name='offerletter_table'),
    
    # Endpoint for the "Select" action, redirecting to details/print
    path('offerletter/details/<int:pk>/', OfferLetterDetailRedirectView.as_view(), name='offerletter_detail_redirect'),
    
    # Placeholder for the actual details/print page (which is the target of the redirect)
    path('offerletter/print_details/<int:pk>/', OfferLetterPrintDetailsView.as_view(), name='offerletter_print_details'),

    # Endpoint for exporting the salary report
    path('offerletter/report/export/', OfferLetterReportExportView.as_view(), name='offerletter_report_export'),
    
    # HTMX endpoint for employee name autocomplete suggestions
    path('offerletter/autocomplete/employee_name/', EmployeeNameAutocompleteView.as_view(), name='autocomplete_employee_name'),
]
```

#### 4.6 Tests (`hr_offer/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and maintainability. Target at least 80% test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.conf import settings
from decimal import Decimal
import pandas as pd
import io

from .models import OfferLetter, EmployeeType, Designation, DutyHour, OfficeStaff, OTHour, OverTime, Grade, OfferAccessory, OfferCalculationService

# Set default settings for tests if not already configured
# This helps ensure tests run consistently
if not hasattr(settings, 'DEFAULT_COMP_ID'):
    settings.DEFAULT_COMP_ID = 1
if not hasattr(settings, 'DEFAULT_FIN_YEAR_ID'):
    settings.DEFAULT_FIN_YEAR_ID = '2024'

class OfferLetterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required related data for tests
        cls.emp_type = EmployeeType.objects.create(id=1, description="Permanent")
        cls.designation = Designation.objects.create(id=1, type="Software Engineer")
        cls.duty_hour = DutyHour.objects.create(id=1, hours="8 Hrs")
        cls.ot_hour = OTHour.objects.create(id=1, hours="2 Hrs")
        cls.overtime = OverTime.objects.create(id=1, description="Standard OT")
        cls.grade = Grade.objects.create(id=1, symbol="A")
        cls.interviewed_by_staff = OfficeStaff.objects.create(
            emp_id='EMP001', employee_name='John Doe', comp_id=settings.DEFAULT_COMP_ID, fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )

        # Create a test OfferLetter instance
        cls.offer1 = OfferLetter.objects.create(
            offer_id=101,
            sys_date='2023-01-15',
            type_of=1, # SAPL
            staff_type=cls.emp_type.id,
            title='Mr.',
            employee_name='Alice Smith',
            designation=cls.designation.id,
            duty_hrs=cls.duty_hour.id,
            interviewed_by=cls.interviewed_by_staff.emp_id,
            contact_no='1234567890',
            salary=Decimal('50000.00'),
            increment=5,
            bonus=Decimal('1000.00'),
            ex_gratia=Decimal('500.00'),
            vehicle_allowance=Decimal('200.00'),
            lta=Decimal('300.00'),
            loyalty=Decimal('100.00'),
            att_bonus_per1=Decimal('5.00'),
            att_bonus_per2=Decimal('10.00'),
            pf_employee_percent=Decimal('12.00'),
            pf_company_percent=Decimal('13.00'),
            ot_hrs=cls.ot_hour.id,
            over_time=cls.overtime.id,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )
        
        # Create another offer for testing search and different values
        cls.offer2 = OfferLetter.objects.create(
            offer_id=102,
            sys_date='2023-02-20',
            type_of=2, # NEHA
            staff_type=cls.emp_type.id,
            title='Ms.',
            employee_name='Bob Johnson',
            designation=cls.designation.id,
            duty_hrs=cls.duty_hour.id,
            interviewed_by=cls.interviewed_by_staff.emp_id,
            contact_no='0987654321',
            salary=Decimal('60000.00'),
            increment=3,
            bonus=Decimal('1200.00'),
            ex_gratia=Decimal('600.00'),
            vehicle_allowance=Decimal('250.00'),
            lta=Decimal('350.00'),
            loyalty=Decimal('120.00'),
            att_bonus_per1=Decimal('6.00'),
            att_bonus_per2=Decimal('11.00'),
            pf_employee_percent=Decimal('12.00'),
            pf_company_percent=Decimal('13.00'),
            ot_hrs=cls.ot_hour.id,
            over_time=cls.overtime.id,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )

        # Create an OfficeStaff record linked to offer1 to test confirmation status
        OfficeStaff.objects.create(
            emp_id='EMP002',
            employee_name='Confirmed Employee',
            offer=cls.offer1,
            resignation_date='',
            department='HR',
            bg_group='GroupA',
            grade=cls.grade,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )

        # Create OfferAccessory for offer1
        OfferAccessory.objects.create(id=1, offer=cls.offer1, includes_in='1', qty=1, amount=Decimal('500.00')) # CTC
        OfferAccessory.objects.create(id=2, offer=cls.offer1, includes_in='2', qty=2, amount=Decimal('100.00')) # TakeHome
        OfferAccessory.objects.create(id=3, offer=cls.offer1, includes_in='3', qty=1, amount=Decimal('200.00')) # Both

    def test_offer_letter_creation(self):
        offer = OfferLetter.objects.get(offer_id=101)
        self.assertEqual(offer.employee_name, 'Alice Smith')
        self.assertEqual(offer.salary, Decimal('50000.00'))
        self.assertEqual(offer.staff_type_rel.description, 'Permanent')

    def test_formatted_sys_date_property(self):
        offer = OfferLetter.objects.get(offer_id=101)
        self.assertEqual(offer.formatted_sys_date, '15-01-2023')

    def test_type_of_company_property(self):
        offer_sapl = OfferLetter.objects.get(offer_id=101)
        offer_neha = OfferLetter.objects.get(offer_id=102)
        self.assertEqual(offer_sapl.type_of_company, 'SAPL')
        self.assertEqual(offer_neha.type_of_company, 'NEHA')

    def test_full_employee_name_property(self):
        offer = OfferLetter.objects.get(offer_id=101)
        self.assertEqual(offer.full_employee_name, 'Mr. Alice Smith')

    def test_confirmation_status_property(self):
        offer_confirmed = OfferLetter.objects.get(offer_id=101)
        offer_unconfirmed = OfferLetter.objects.get(offer_id=102) # No OfficeStaff linked
        self.assertEqual(offer_confirmed.confirmation_status, 'Confirm')
        self.assertEqual(offer_unconfirmed.confirmation_status, '')

    def test_get_increment_options_method(self):
        offer = OfferLetter.objects.get(offer_id=101)
        self.assertEqual(offer.get_increment_options(), [5, 4, 3, 2, 1, 0])

    def test_get_offer_list_display_data(self):
        offers_data = OfferLetter.objects.get_offer_list_display_data(
            company_id=settings.DEFAULT_COMP_ID, 
            financial_year_id=settings.DEFAULT_FIN_YEAR_ID
        )
        self.assertEqual(offers_data.count(), 2)
        self.assertEqual(offers_data.first().offer_id, 102) # Ordered by OfferId Desc

        filtered_offers_data = OfferLetter.objects.get_offer_list_display_data(
            company_id=settings.DEFAULT_COMP_ID, 
            financial_year_id=settings.DEFAULT_FIN_YEAR_ID,
            employee_name='Alice'
        )
        self.assertEqual(filtered_offers_data.count(), 1)
        self.assertEqual(filtered_offers_data.first().employee_name, 'Alice Smith')

    def test_get_salary_report_data(self):
        report_data = OfferLetter.objects.get_salary_report_data(
            company_id=settings.DEFAULT_COMP_ID, 
            financial_year_id=settings.DEFAULT_FIN_YEAR_ID
        )
        self.assertEqual(len(report_data), 1) # Only offer1 should be in report due to OfficeStaff link
        report_row = report_data[0]

        self.assertEqual(report_row['emp_id'], 'EMP002') # Linked OfficeStaff EmpId
        self.assertEqual(report_row['gross_salary'], Decimal('50000.00'))
        
        # Test a few calculation fields (these will depend on the exact logic in OfferCalculationService)
        # Using simplified calculation in OfferCalculationService:
        # Basic: 50000 * 0.40 = 20000
        self.assertAlmostEqual(report_row['basic'], Decimal('20000.00'))
        # AttBonus1: 50000 * 5% = 2500
        self.assertAlmostEqual(report_row['attendance_bonus'], Decimal('2500.00'))
        # PF Employee: 50000 * 12% = 6000
        self.assertAlmostEqual(report_row['pf_employee'], Decimal('6000.00'))
        # ExGratia
        self.assertAlmostEqual(report_row['ex_gratia'], Decimal('500.00'))
        # Accessories: CTC (500), TakeHome (200), Both (200)
        # Take Home = (Gross + ExGratia + Accessories_TakeHome + Accessories_Both) - (PFEmp + PTax)
        # Assuming PTax = 200 (for >25000)
        # TH = (50000 + 500 + 200 + 200) - (6000 + 200) = 50900 - 6200 = 44700
        self.assertAlmostEqual(report_row['take_home'], Decimal('44700.00'))
        # CTC = (Gross + Bonus + Loyalty + LTA + Gratuity + PFComp + ExGratia + Acc_CTC + Acc_Both)
        # PF Company: 50000 * 13% = 6500
        # Gratuity: 50000 * 0.0481 = 2405
        # CTC = 50000 + 1000 + 100 + 300 + 2405 + 6500 + 500 + 500 + 200 = 61505
        self.assertAlmostEqual(report_row['ctc'], Decimal('61505.00'))


class OfferLetterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required related data for tests
        cls.emp_type = EmployeeType.objects.create(id=1, description="Permanent")
        cls.designation = Designation.objects.create(id=1, type="Software Engineer")
        cls.duty_hour = DutyHour.objects.create(id=1, hours="8 Hrs")
        cls.ot_hour = OTHour.objects.create(id=1, hours="2 Hrs")
        cls.overtime = OverTime.objects.create(id=1, description="Standard OT")
        cls.grade = Grade.objects.create(id=1, symbol="A")
        cls.interviewed_by_staff = OfficeStaff.objects.create(
            emp_id='INT001', employee_name='Interviewer One', comp_id=settings.DEFAULT_COMP_ID, fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )

        # Create a test OfferLetter instance
        cls.offer1 = OfferLetter.objects.create(
            offer_id=101,
            sys_date='2023-01-15',
            type_of=1,
            staff_type=cls.emp_type.id,
            title='Mr.',
            employee_name='Alice Smith',
            designation=cls.designation.id,
            duty_hrs=cls.duty_hour.id,
            interviewed_by=cls.interviewed_by_staff.emp_id,
            contact_no='1234567890',
            salary=Decimal('50000.00'),
            increment=5,
            bonus=Decimal('1000.00'),
            ex_gratia=Decimal('500.00'),
            vehicle_allowance=Decimal('200.00'),
            lta=Decimal('300.00'),
            loyalty=Decimal('100.00'),
            att_bonus_per1=Decimal('5.00'),
            att_bonus_per2=Decimal('10.00'),
            pf_employee_percent=Decimal('12.00'),
            pf_company_percent=Decimal('13.00'),
            ot_hrs=cls.ot_hour.id,
            over_time=cls.overtime.id,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )
        
        cls.offer2 = OfferLetter.objects.create(
            offer_id=102,
            sys_date='2023-02-20',
            type_of=2,
            staff_type=cls.emp_type.id,
            title='Ms.',
            employee_name='Bob Johnson',
            designation=cls.designation.id,
            duty_hrs=cls.duty_hour.id,
            interviewed_by=cls.interviewed_by_staff.emp_id,
            contact_no='0987654321',
            salary=Decimal('60000.00'),
            increment=3,
            bonus=Decimal('1200.00'),
            ex_gratia=Decimal('600.00'),
            vehicle_allowance=Decimal('250.00'),
            lta=Decimal('350.00'),
            loyalty=Decimal('120.00'),
            att_bonus_per1=Decimal('6.00'),
            att_bonus_per2=Decimal('11.00'),
            pf_employee_percent=Decimal('12.00'),
            pf_company_percent=Decimal('13.00'),
            ot_hrs=cls.ot_hour.id,
            over_time=cls.overtime.id,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )
        
        OfficeStaff.objects.create(
            emp_id='EMP002',
            employee_name='Confirmed Employee',
            offer=cls.offer1,
            resignation_date='', # Not resigned
            department='HR',
            bg_group='GroupA',
            grade=cls.grade,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )

    def setUp(self):
        self.client = Client()

    def test_offerletter_list_view(self):
        response = self.client.get(reverse('hr_offer:offerletter_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/offerletter/list.html')
        self.assertContains(response, 'Offer Letter - Print')
        self.assertContains(response, '<div id="offerletterTable-container"') # Check for HTMX target

    def test_offerletter_table_partial_view_no_search(self):
        response = self.client.get(reverse('hr_offer:offerletter_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/offerletter/_offerletter_table.html')
        self.assertContains(response, 'Alice Smith')
        self.assertContains(response, 'Bob Johnson')
        self.assertContains(response, 'Offer 101') # Check for offer ID
        self.assertContains(response, 'Offer 102')

    def test_offerletter_table_partial_view_with_search(self):
        response = self.client.get(reverse('hr_offer:offerletter_table'), {'employee_name': 'Alice'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/offerletter/_offerletter_table.html')
        self.assertContains(response, 'Alice Smith')
        self.assertNotContains(response, 'Bob Johnson')

    def test_offerletter_detail_redirect_view(self):
        url = reverse('hr_offer:offerletter_detail_redirect', kwargs={'pk': self.offer1.offer_id})
        # Simulate passing increment via query param
        response = self.client.get(f"{url}?increment=2")
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertTrue(response.url.startswith(reverse('hr_offer:offerletter_print_details', kwargs={'pk': self.offer1.offer_id})))
        self.assertIn('increment=2', response.url)

    def test_offerletter_detail_redirect_view_not_found(self):
        url = reverse('hr_offer:offerletter_detail_redirect', kwargs={'pk': 9999})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Still redirects to list
        self.assertEqual(response.url, reverse('hr_offer:offerletter_list'))
        # Check for messages framework if used in middleware
        # self.assertIn('Offer Letter not found', [m.message for m in get_messages(response.wsgi_request)])

    def test_offerletter_report_export_view(self):
        url = reverse('hr_offer:offerletter_report_export')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.ms-excel')
        self.assertTrue(response['Content-Disposition'].startswith('attachment; filename="OfferData_'))
        
        # Read the Excel file and verify content
        try:
            df = pd.read_excel(io.BytesIO(response.content), engine='xlrd') # Use xlrd for .xls
        except ImportError:
            self.fail("xlrd library not found. Cannot test Excel content. Please install it (pip install xlrd).")
        except Exception as e:
            self.fail(f"Could not read Excel file: {e}")

        self.assertEqual(len(df), 1) # Only one offer should be in report based on OfficeStaff filtering
        self.assertEqual(df.loc[0, 'emp_id'], 'EMP002')
        self.assertEqual(df.loc[0, 'gross_salary'], Decimal('50000.00'))

    def test_offerletter_report_export_view_no_data(self):
        # Delete existing OfficeStaff to simulate no data for report
        OfficeStaff.objects.all().delete()
        url = reverse('hr_offer:offerletter_report_export')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 204) # No content if no data
        # Check for messages framework if used in middleware
        # self.assertIn('No data to export', [m.message for m in get_messages(response.wsgi_request)])

    def test_employee_name_autocomplete_view(self):
        url = reverse('hr_offer:autocomplete_employee_name')
        response = self.client.get(url, {'prefixText': 'al'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alice Smith')
        self.assertNotContains(response, 'Bob Johnson')
        self.assertContains(response, 'button') # Ensure it returns HTML buttons

        response = self.client.get(url, {'prefixText': 'zzyy'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.strip(), b'') # Empty content if no match

    def test_offerletter_print_details_view(self):
        url = reverse('hr_offer:offerletter_print_details', kwargs={'pk': self.offer1.offer_id})
        response = self.client.get(url, {'increment': '2'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_offer/offerletter/print_details.html')
        self.assertContains(response, f'Offer ID: {self.offer1.offer_id}')
        self.assertContains(response, 'Increment Value: 2')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX for dynamic updates:**
    *   The main list page (`list.html`) uses `hx-get` on `offerletterTable-container` with `hx-trigger="load, refreshOfferLetterList from:body"` to dynamically load the table content.
    *   The "Search" and "View All" buttons use `hx-get` to re-fetch the table content into the `#offerletterTable-container`, ensuring the table is refreshed without a full page reload.
    *   The `employee_name` input uses `hx-get` for autocomplete suggestions, targeting a separate `div` (`#employee-suggestions`).
    *   The "Select" links within the table directly navigate to the detail page (as they did in ASP.NET), so no HTMX swap is involved there, only a standard `href`.
*   **Alpine.js for UI state management:**
    *   Used in `_offerletter_table.html` with `x-data="{ selectedIncrement: ... }"` to manage the state of the "Increment" dropdown within each row.
    *   Used in `list.html` for autocomplete suggestions, with `@click.outside` to hide suggestions and `@keydown` for keyboard navigation.
*   **DataTables for list views:**
    *   Initialized in `_offerletter_table.html` within a `<script>` tag and re-initialized using an `htmx:afterSwap` event listener. This ensures DataTables correctly applies to the dynamically loaded table content, providing client-side search, sorting, and pagination.
*   **DRY templates:** `_offerletter_table.html` is a partial template loaded by HTMX, avoiding redundant HTML. `core/base.html` (assumed to exist) provides the common layout.
*   **Strict separation of concerns:** Business logic (calculations, complex data queries) is entirely in `models.py` (`OfferLetter` properties and `OfferLetterManager` methods). Views (`views.py`) are thin, primarily handling requests, delegating to models, and rendering templates.
*   **No custom JavaScript:** All dynamic interactions are achieved through HTMX attributes and basic Alpine.js directives, minimizing the need for imperative JavaScript.

---

## Final Notes

This comprehensive plan provides a clear pathway for migrating your ASP.NET Offer Letter Print module to Django. By following these structured steps, leveraging automation, and adhering to modern best practices, your organization can achieve a more robust, scalable, and maintainable application with an enhanced user experience. Remember to replace placeholder values for `DEFAULT_COMPANY_ID` and `DEFAULT_FINANCIAL_YEAR_ID` with actual dynamic values derived from your application's session or authentication context.