## ASP.NET to Django Conversion Script:

This document outlines a comprehensive modernization plan to transition the `OfficeStaff_Edit.aspx` and its C# code-behind to a modern Django application. We will leverage Django's "Fat Model, Thin View" architecture, HTMX for dynamic interactions, Alpine.js for lightweight frontend state, and DataTables for enhanced data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, particularly the `GridView2` columns, the `GetCompletionList` method, and the `binddata` method's use of `Sp_Staff_Grid`, `tblHR_Departments`, and `BusinessGroup`, we can infer the following:

**Primary Table for Staff Information:** `tblHR_OfficeStaff` (for autocomplete and potentially the base data for the stored procedure).
**Lookup Tables:** `tblHR_Departments` and `BusinessGroup`.
**Stored Procedure:** `Sp_Staff_Grid` is the main data source for the grid. Its output structure defines the fields displayed.

**Inferred Schema:**

*   **`tblHR_OfficeStaff` (for autocomplete and core employee data):**
    *   `EmpId` (Primary Key, CharField/IntegerField, often composite with CompId/FinId)
    *   `EmployeeName` (CharField)
    *   `UserId` (CharField/IntegerField)
    *   `CompId` (IntegerField)
    *   `FinId` (IntegerField)
    *   Potentially `Department` (FK to `tblHR_Departments` ID)
    *   Potentially `BGGroup` (FK to `BusinessGroup` ID)

*   **`tblHR_Departments` (for department lookups):**
    *   `Id` (Primary Key, IntegerField)
    *   `Description` (CharField)

*   **`BusinessGroup` (for business group lookups):**
    *   `Id` (Primary Key, IntegerField)
    *   `Symbol` (CharField)

*   **`Sp_Staff_Grid` Output Columns (which define our `OfficeStaff` model for display):**
    *   `FinYear` (CharField/IntegerField, from `Session["finyear"]`)
    *   `UserId` (CharField/IntegerField)
    *   `EmpId` (CharField/IntegerField)
    *   `EmployeeName` (CharField)
    *   `DeptName` (CharField, likely from join on `tblHR_Departments`)
    *   `BGgroup` (CharField, likely from join on `BusinessGroup`)
    *   `Designation` (CharField)
    *   `MobileNo` (CharField)
    *   `JoiningDate` (DateField)
    *   `ResignationDate` (DateField)

For the purpose of this migration, we will create a Django model `OfficeStaff` that represents the *output schema of the `Sp_Staff_Grid` stored procedure*, as this is what's displayed. We'll also create helper models for `Department` and `BusinessGroup` to facilitate lookups.

### Step 2: Identify Backend Functionality

The ASP.NET page primarily performs a **Read (Search and List)** operation.

*   **Read:**
    *   Data is retrieved using the `Sp_Staff_Grid` stored procedure.
    *   Search criteria include:
        *   `Employee Name` (filtered by `EmpId` derived from `TxtEmpName` using an autocomplete).
        *   `Department Name` (filtered by Department ID derived from `TxtMrs` by looking up `tblHR_Departments`).
        *   `BG Group` (filtered by Business Group ID derived from `TxtMrs` by looking up `BusinessGroup`).
    *   Pagination is handled by `GridView2`.
    *   `CompId` and `FinId` are passed as session-based parameters to the stored procedure.
*   **Autocomplete:** `TxtEmpName` has an autocomplete feature (`GetCompletionList` web method) that queries `tblHR_OfficeStaff` based on `EmployeeName` and returns `EmployeeName [EmpId]`.
*   **Navigation:** The "Select" link (`HyperLinkField`) navigates to `OfficeStaff_Edit_Details.aspx?EmpId={0}&ModId=12&SubModId=24` for detailed editing. This is an external link, not part of the current page's CRUD.
*   **Dynamic UI:** The visibility of `TxtMrs` and `TxtEmpName` changes based on the `DrpField` selection.

No direct Create, Update, or Delete operations are performed on this specific page.

### Step 3: Infer UI Components

*   **Search Form:**
    *   `DrpField` (`DropDownList`): Maps to a Django `forms.ChoiceField` (Employee Name, Dept Name, BG Group).
    *   `TxtMrs` (`TextBox`): Maps to a `forms.CharField`, visible when `DrpField` is Dept Name or BG Group.
    *   `TxtEmpName` (`TextBox` with AutoCompleteExtender): Maps to a `forms.CharField`, visible when `DrpField` is Employee Name. This will be integrated with HTMX for dynamic suggestions.
    *   `Button1` (`Button`): Maps to an HTMX `hx-post` or `hx-get` trigger.
*   **Data Display:**
    *   `GridView2` (`GridView`): Maps to a DataTables-enhanced HTML `<table>`.
    *   Columns are mapped directly to table headers and data cells.
    *   The "Select" column will be a standard `<a>` tag or HTMX `hx-redirect` button.
*   **Dynamic UI:** The visibility logic for search textboxes will be handled by Alpine.js in the template.

### Step 4: Generate Django Code

We will create a new Django app, e.g., `hr_staff`.

#### 4.1 Models (`hr_staff/models.py`)

We'll define models for `OfficeStaff` (representing the data displayed in the grid), `Department`, and `BusinessGroup` (for search lookups). A custom manager will handle calling the stored procedure and parsing the results.

```python
from django.db import models, connection
from django.db.models import Manager
import re

# Helper function to extract ID from "Name [ID]" format
def extract_id_from_name_id(text_with_id):
    """
    Mimics fun.getCode() by extracting the ID from a string like "Employee Name [123]".
    Returns the extracted ID as a string, or None if not found.
    """
    match = re.search(r'\[(\w+)\]$', text_with_id)
    if match:
        return match.group(1)
    return None

class Department(models.Model):
    """
    Represents the tblHR_Departments table for lookup purposes.
    Assumes 'Id' is the primary key and 'Description' is the name.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class BusinessGroup(models.Model):
    """
    Represents the BusinessGroup table for lookup purposes.
    Assumes 'Id' is the primary key and 'Symbol' is the name.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class OfficeStaffManager(Manager):
    """
    Custom manager to interact with the Sp_Staff_Grid stored procedure.
    """
    def search_staff_grid(self, comp_id, fin_id, search_type, search_value):
        x_param = '' # For Department/Business Group
        y_param = '' # For Employee ID

        if search_type == '0':  # Employee Name
            if search_value:
                # Extract EmpId from "Employee Name [EmpId]" format
                emp_id_code = extract_id_from_name_id(search_value)
                if emp_id_code:
                    y_param = f" AND EmpId='{emp_id_code}'"
        elif search_type == '1': # Dept Name
            if search_value:
                try:
                    dept = Department.objects.using(self.db).get(description=search_value)
                    x_param = f" AND Department='{dept.id}'"
                except Department.DoesNotExist:
                    pass # Or handle error: department not found
        elif search_type == '2': # BG Group
            if search_value:
                try:
                    bg_group = BusinessGroup.objects.using(self.db).get(symbol=search_value)
                    x_param = f" AND BGGroup='{bg_group.id}'"
                except BusinessGroup.DoesNotExist:
                    pass # Or handle error: business group not found

        with connection.cursor() as cursor:
            # Note: Parameter names must match stored procedure definition
            # Ensure correct data types for parameters based on your SQL Server SP
            cursor.execute("""
                EXEC Sp_Staff_Grid @CompId=%s, @FinId=%s, @x=%s, @y=%s
            """, [comp_id, fin_id, x_param, y_param])
            
            columns = [col[0] for col in cursor.description]
            results = [
                dict(zip(columns, row))
                for row in cursor.fetchall()
            ]
        
        # Manually create OfficeStaff instances from the dictionary results
        # This is because the Sp_Staff_Grid output does not directly map to a single DB table
        # We are effectively creating a 'virtual' model for the grid display
        staff_list = []
        for row_data in results:
            # Map column names from SP output to model fields
            staff_list.append(self.model(
                pk=row_data.get('EmpId'), # Using EmpId as a logical PK for the displayed data
                fin_year=row_data.get('FinYear'),
                user_id=row_data.get('UserId'),
                emp_id=row_data.get('EmpId'),
                employee_name=row_data.get('EmployeeName'),
                dept_name=row_data.get('DeptName'),
                bg_group=row_data.get('BGgroup'),
                designation=row_data.get('Designation'),
                mobile_no=row_data.get('MobileNo'),
                joining_date=row_data.get('JoiningDate'),
                resignation_date=row_data.get('ResignationDate'),
            ))
        return staff_list

class OfficeStaff(models.Model):
    """
    Represents the structure of the data returned by Sp_Staff_Grid.
    It does not directly map to a single database table, but rather
    the result of a stored procedure. managed=False is used defensively.
    """
    # These fields correspond to the columns in the GridView and SP output
    # We use EmpId as a logical primary key for the purposes of identification
    # in the view, although it's not a primary key in a managed table.
    # We prefix with 'sp_' to denote they come from the stored procedure output
    # or are effectively 'denormalized' for display.
    # Note: Using pk=None to make it clear this isn't a saved model instance normally.
    # For a list display, we can use EmpId as an internal identifier.
    
    # We define id field, as Django ORM often expects one even for managed=False
    # However, since this model is just for displaying SP results, we won't interact
    # with it via typical ORM saves/updates.
    # Let's use EmpId as a unique identifier for URL routing.
    # It's better to explicitly map fields returned by SP.
    fin_year = models.CharField(max_length=10, null=True, blank=True) # Assuming string, could be IntegerField
    user_id = models.CharField(max_length=50, null=True, blank=True)
    emp_id = models.CharField(primary_key=True, max_length=50, db_column='EmpId') # Use EmpId as primary_key for this model for routing
    employee_name = models.CharField(max_length=255, null=True, blank=True)
    dept_name = models.CharField(max_length=255, null=True, blank=True)
    bg_group = models.CharField(max_length=255, null=True, blank=True)
    designation = models.CharField(max_length=255, null=True, blank=True)
    mobile_no = models.CharField(max_length=20, null=True, blank=True)
    joining_date = models.DateField(null=True, blank=True)
    resignation_date = models.DateField(null=True, blank=True)

    objects = OfficeStaffManager() # Attach the custom manager

    class Meta:
        managed = False  # Django will not manage this table's schema (since it's SP output)
        # db_table = 'tblHR_OfficeStaff' # This model is for SP output, not directly the table.
                                        # If it were directly mapping tblHR_OfficeStaff,
                                        # we would set db_table.
        verbose_name = 'Office Staff (SP Output)'
        verbose_name_plural = 'Office Staff (SP Output)'

    def __str__(self):
        return f"{self.employee_name} ({self.emp_id})"

    @staticmethod
    def get_employee_autocomplete_suggestions(prefix_text, comp_id):
        """
        Mimics GetCompletionList WebMethod.
        Queries tblHR_OfficeStaff for employee names and IDs.
        """
        # This assumes tblHR_OfficeStaff is a real table you can query directly.
        # If not, you might need another stored procedure or raw SQL here.
        # Assuming tblHR_OfficeStaff is defined somewhere else for ORM query.
        
        # For simplicity, we'll assume a direct ORM query on tblHR_OfficeStaff model,
        # or it could be done via raw SQL/SP. Let's assume a real table `tblHR_OfficeStaff_Actual`
        # exists and contains these fields.
        
        # If this model also maps to tblHR_OfficeStaff for autocomplete, it needs to be defined
        # properly for ORM use, or use raw SQL.
        
        # Let's assume there's an actual `tblHR_OfficeStaff_Actual` model for the autocomplete.
        # For now, let's use a dummy query or mock the data if not directly available.
        # Given the previous context, it's safer to define a dedicated model for tblHR_OfficeStaff_Actual.
        
        # Re-evaluating: The `GetCompletionList` method queries `tblHR_OfficeStaff`.
        # So, we need `OfficeStaff_Actual` to represent the underlying `tblHR_OfficeStaff` table.
        # The `OfficeStaff` model defined above is *only* for the SP results.
        
        # Option 1: Define a separate model `OfficeStaffActual` for the underlying table.
        # Option 2: If the `OfficeStaff` model *could* be used for direct queries,
        # then it would need a `db_table='tblHR_OfficeStaff'` and an actual PK, etc.
        # Given `managed=False` for SP output, Option 1 is cleaner.

        # For this response, let's define `OfficeStaffActual` for the autocomplete.
        # (This is an important clarification based on careful ASP.NET analysis)
        
        # This method would ideally be in a manager for `OfficeStaffActual`
        # but is placed here for context of the `GetCompletionList` migration.
        
        # DUMMY implementation (replace with actual ORM/raw SQL for tblHR_OfficeStaff_Actual)
        # Assuming OfficeStaffActual is already defined and queryable.
        
        results = OfficeStaffActual.objects.filter(
            employee_name__icontains=prefix_text,
            comp_id=comp_id
        ).values('emp_id', 'employee_name')[:10] # Limit to 10 suggestions
        
        suggestions = [
            f"{entry['employee_name']} [{entry['emp_id']}]"
            for entry in results
        ]
        return suggestions

class OfficeStaffActual(models.Model):
    """
    Represents the actual tblHR_OfficeStaff table for direct queries
    like the autocomplete functionality.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    # Add other fields as per tblHR_OfficeStaff if needed for other functionality
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Actual Office Staff'
        verbose_name_plural = 'Actual Office Staff Records'

    def __str__(self):
        return f"{self.employee_name} [{self.emp_id}]"

```

#### 4.2 Forms (`hr_staff/forms.py`)

A non-model form for the search criteria.

```python
from django import forms

class StaffSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'Dept Name'),
        ('2', 'BG Group'),
    ]

    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-model': 'selectedField'})
    )
    search_text_mrs = forms.CharField(
        label="Search Text (Dept/BG)",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-show': "selectedField === '1' || selectedField === '2'"})
    )
    search_text_emp = forms.CharField(
        label="Search Text (Employee)",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': "/hr_staff/staff/autocomplete/employee/", 'hx-trigger': "keyup changed delay:500ms, search", 'hx-target': "#autocomplete-results", 'hx-indicator': "#autocomplete-loading", 'x-show': "selectedField === '0'"})
    )

    # Optional: Add autocomplete results container if needed
    # (Usually handled directly in template with HTMX/Alpine.js)
```

#### 4.3 Views (`hr_staff/views.py`)

We need a main view for the page and partial views for the table and autocomplete.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from .models import OfficeStaff, OfficeStaffActual # Ensure OfficeStaffActual is imported
from .forms import StaffSearchForm
from django.db import connection

class OfficeStaffListView(TemplateView):
    template_name = 'hr_staff/officestaff/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = StaffSearchForm(self.request.GET or None)
        context['page_title'] = 'Staff - Edit'
        # Assuming comp_id and fin_id are available from session or user profile
        # For simplicity, hardcoding for now, but should be retrieved from session
        # or user model as in ASP.NET code: Session["compid"], Session["finyear"]
        # Example: request.session.get('compid'), request.session.get('finyear')
        context['comp_id'] = self.request.session.get('compid', 1) # Default or fetch from user profile
        context['fin_id'] = self.request.session.get('finyear', '2023-24') # Default or fetch from user profile
        
        # Initial load of messages if query string 'msg' exists
        msg = self.request.GET.get('msg')
        if msg:
            messages.info(self.request, msg)

        return context

class OfficeStaffTablePartialView(View):
    """
    Renders the DataTables table content via HTMX.
    """
    def get(self, request, *args, **kwargs):
        form = StaffSearchForm(request.GET)
        staff_list = []
        if form.is_valid():
            search_field = form.cleaned_data.get('search_field')
            search_value_mrs = form.cleaned_data.get('search_text_mrs')
            search_value_emp = form.cleaned_data.get('search_text_emp')
            
            # Determine which search value to use based on search_field
            search_value_to_use = ''
            if search_field == '0': # Employee Name
                search_value_to_use = search_value_emp
            elif search_field in ['1', '2']: # Dept Name, BG Group
                search_value_to_use = search_value_mrs

            comp_id = request.session.get('compid', 1) # Get from session or default
            fin_id = request.session.get('finyear', '2023-24') # Get from session or default
            
            # Call the custom manager method to get staff data from stored procedure
            staff_list = OfficeStaff.objects.search_staff_grid(
                comp_id=comp_id, 
                fin_id=fin_id, 
                search_type=search_field, 
                search_value=search_value_to_use
            )
        
        return render(request, 'hr_staff/officestaff/_officestaff_table.html', {
            'officestaff_list': staff_list
        })

    def post(self, request, *args, **kwargs):
        """
        Handles POST requests for search, similar to Button1_Click.
        This will typically trigger a refresh of the table via HTMX.
        """
        # The form submission itself just needs to trigger the table partial reload.
        # The GET request for the table partial will then handle the search logic.
        # This is a common pattern for HTMX-driven forms that update lists.
        
        # We process the form data and then return an empty 204 response
        # with an HX-Trigger header to tell the client to refresh the table.
        form = StaffSearchForm(request.POST)
        if form.is_valid():
            # The actual search will happen in the GET request to /staff/table/
            # This POST is just to update state / trigger refresh.
            # We don't need to return data directly here.
            
            # Example: If there's any state to save, it would go here.
            # In this case, the form data is passed via GET params to the table view.
            
            # For simplicity, we just return a status 204 No Content with a trigger.
            response = HttpResponse(status=204)
            response['HX-Trigger'] = 'refreshOfficeStaffList'
            return response
        else:
            # If form is invalid, you might want to return the form with errors
            # or log the error. For an HTMX search, typically we'd re-render
            # the form part with errors, but for a simple search filter, 
            # it might not be strictly necessary if errors are simple.
            # For this simple search, if invalid, just re-render the table with current state.
            return render(request, 'hr_staff/officestaff/_officestaff_table.html', {
                'form': form, # Pass form with errors
                'officestaff_list': [] # No results on invalid search
            })

class EmployeeAutocompleteView(View):
    """
    Provides autocomplete suggestions for employee names, similar to GetCompletionList.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        comp_id = request.session.get('compid', 1) # Fetch from session or default

        if not prefix_text:
            return JsonResponse([], safe=False)

        # Call the static method on OfficeStaffActual for suggestions
        suggestions = OfficeStaffActual.get_employee_autocomplete_suggestions(prefix_text, comp_id)
        
        # HTMX typically expects a list of options in specific HTML format
        # or JSON for client-side processing (e.g., Alpine.js/Datatables integration).
        # For simple <datalist> or custom dropdowns, return HTML.
        # For a standard AJAX autocomplete, return JSON.
        
        # Let's return JSON for a robust client-side autocomplete using Alpine.js.
        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates (`hr_staff/templates/hr_staff/officestaff/`)

**`list.html`** (Main page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ selectedField: '0' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">{{ page_title }}</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Staff</h3>
        <form hx-post="{% url 'hr_staff:officestaff_table' %}" hx-swap="none" hx-trigger="submit from #searchForm">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div class="mb-4">
                    <label for="{{ form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_field.label }}
                    </label>
                    {{ form.search_field }}
                    {% if form.search_field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.search_field.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <label for="{{ form.search_text_mrs.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_text_mrs.label }}
                    </label>
                    {{ form.search_text_mrs }}
                    {% if form.search_text_mrs.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.search_text_mrs.errors }}</p>
                    {% endif %}
                </div>

                <div class="mb-4" x-data="{
                    autocompleteResults: [],
                    selectedIndex: -1,
                    selectResult(index) {
                        if (index !== -1) {
                            this.$refs.employeeInput.value = this.autocompleteResults[index];
                            this.autocompleteResults = []; // Clear results after selection
                            this.selectedIndex = -1;
                        }
                    },
                    handleKeydown(event) {
                        if (this.autocompleteResults.length === 0) return;
                        if (event.key === 'ArrowDown') {
                            event.preventDefault();
                            this.selectedIndex = (this.selectedIndex + 1) % this.autocompleteResults.length;
                        } else if (event.key === 'ArrowUp') {
                            event.preventDefault();
                            this.selectedIndex = (this.selectedIndex - 1 + this.autocompleteResults.length) % this.autocompleteResults.length;
                        } else if (event.key === 'Enter') {
                            event.preventDefault();
                            this.selectResult(this.selectedIndex);
                        } else if (event.key === 'Escape') {
                            this.autocompleteResults = [];
                            this.selectedIndex = -1;
                        }
                    }
                }">
                    <label for="{{ form.search_text_emp.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_text_emp.label }}
                    </label>
                    <input 
                        type="text" 
                        id="{{ form.search_text_emp.id_for_label }}" 
                        name="{{ form.search_text_emp.name }}"
                        class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        hx-get="{% url 'hr_staff:employee_autocomplete' %}"
                        hx-target="#autocomplete-results"
                        hx-trigger="keyup changed delay:500ms, search"
                        hx-indicator="#autocomplete-loading"
                        value="{{ form.search_text_emp.value|default:'' }}"
                        x-show="selectedField === '0'"
                        x-ref="employeeInput"
                        @keydown="handleKeydown"
                        @input="autocompleteResults = []"
                        @focus="autocompleteResults = []"
                    >
                    <div id="autocomplete-loading" class="htmx-indicator mt-1 text-blue-500 text-xs">Loading...</div>
                    <ul id="autocomplete-results" 
                        class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
                        x-show="autocompleteResults.length > 0">
                        <!-- HTMX will swap results here -->
                    </ul>
                    {% if form.search_text_emp.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.search_text_emp.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="md:col-span-3 text-right">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <div id="staffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body"
         hx-get="{% url 'hr_staff:officestaff_table' %}"
         hx-target="this"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading staff data...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is included in base.html. No additional Alpine.js specific initialization here.
    // The DataTables initialization is handled within the _officestaff_table.html partial
    // because the table itself is loaded dynamically.
</script>
{% endblock %}
```

**`_officestaff_table.html`** (Partial for the DataTables table)

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    {% if officestaff_list %}
    <table id="officeStaffTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resignation Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in officestaff_list %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.emp_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-left">{{ obj.employee_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.dept_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.bg_group }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.designation }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.mobile_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.joining_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.resignation_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">
                    <a href="/module/hr/transactions/officestaff_edit_details/?EmpId={{ obj.emp_id }}&ModId=12&SubModId=24" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                        Select
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="fontcss text-center text-red-700 text-lg py-8">No data to display !</p>
    {% endif %}
</div>

<script>
// DataTables initialization
// This script runs after HTMX has swapped in the new content
$(document).ready(function() {
    $('#officeStaffTable').DataTable({
        "pageLength": 20, // As per ASP.NET GridView PageSize
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 9] } // SN and Actions columns
        ]
    });
});
</script>
```

**`_autocomplete_results.html`** (Partial for autocomplete suggestions, swapped into `autocomplete-results`)

```html
{% for suggestion in suggestions %}
<li class="px-4 py-2 hover:bg-blue-500 hover:text-white cursor-pointer"
    x-bind:class="{ 'bg-blue-500 text-white': selectedIndex === {{ forloop.counter0 }} }"
    @click="selectResult({{ forloop.counter0 }})">
    {{ suggestion }}
</li>
{% endfor %}
{% if not suggestions %}
<li class="px-4 py-2 text-gray-500">No suggestions</li>
{% endif %}
```
*Note: The `EmployeeAutocompleteView` was designed to return JSON, which is better for client-side JavaScript handling. If HTMX is to directly swap HTML, the `EmployeeAutocompleteView` would need to render `_autocomplete_results.html` directly with `suggestions` in context, and `JsonResponse` removed.*

#### 4.5 URLs (`hr_staff/urls.py`)

```python
from django.urls import path
from .views import OfficeStaffListView, OfficeStaffTablePartialView, EmployeeAutocompleteView

app_name = 'hr_staff' # Define app_name for namespacing

urlpatterns = [
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    path('staff/autocomplete/employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

```
Remember to include these URLs in your project's main `urls.py`:
`path('hr_staff/', include('hr_staff.urls')),`

#### 4.6 Tests (`hr_staff/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date
from .models import OfficeStaff, Department, BusinessGroup, OfficeStaffActual, extract_id_from_name_id

class OfficeStaffModelTest(TestCase):
    def setUp(self):
        # Mock database connection for stored procedure calls
        # This is crucial for testing models that rely on raw SQL/SPs
        self.mock_cursor = MagicMock()
        self.mock_connection = MagicMock()
        self.mock_connection.cursor.return_value.__enter__.return_value = self.mock_cursor
        
        # Patch the connection object for the duration of the test
        self.connection_patch = patch('hr_staff.models.connection', self.mock_connection)
        self.connection_patch.start()

        # Create dummy data for lookup tables if they were managed (or mock them)
        # For managed=False models, you'd typically pre-populate your test DB
        # or mock the lookup queries.
        Department.objects.create(id=1, description='HR')
        Department.objects.create(id=2, description='Finance')
        BusinessGroup.objects.create(id=10, symbol='A')
        BusinessGroup.objects.create(id=20, symbol='B')
        
        # Create some OfficeStaffActual data for autocomplete
        OfficeStaffActual.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=1)
        OfficeStaffActual.objects.create(emp_id='EMP002', employee_name='Jane Smith', comp_id=1)
        OfficeStaffActual.objects.create(emp_id='EMP003', employee_name='Peter Jones', comp_id=2)

    def tearDown(self):
        self.connection_patch.stop()

    def test_extract_id_from_name_id(self):
        self.assertEqual(extract_id_from_name_id("Employee Name [123]"), "123")
        self.assertEqual(extract_id_from_name_id("Another Employee [XYZ]"), "XYZ")
        self.assertIsNone(extract_id_from_name_id("No ID Here"))
        self.assertIsNone(extract_id_from_name_id("Employee [123"))

    def test_officestaff_search_staff_grid_employee_name(self):
        # Mock the stored procedure results
        self.mock_cursor.description = [
            ('FinYear',), ('UserId',), ('EmpId',), ('EmployeeName',), ('DeptName',),
            ('BGgroup',), ('Designation',), ('MobileNo',), ('JoiningDate',), ('ResignationDate',)
        ]
        self.mock_cursor.fetchall.return_value = [
            ('2023-24', 'U001', 'EMP001', 'John Doe', 'HR', 'A', 'Manager', '12345', date(2020, 1, 1), None)
        ]

        staff_list = OfficeStaff.objects.search_staff_grid(1, '2023-24', '0', 'John Doe [EMP001]')
        self.assertEqual(len(staff_list), 1)
        self.assertEqual(staff_list[0].employee_name, 'John Doe')
        self.mock_cursor.execute.assert_called_once_with(
            """
                EXEC Sp_Staff_Grid @CompId=%s, @FinId=%s, @x=%s, @y=%s
            """, [1, '2023-24', '', " AND EmpId='EMP001'"]
        )

    def test_officestaff_search_staff_grid_department(self):
        self.mock_cursor.description = [
            ('FinYear',), ('UserId',), ('EmpId',), ('EmployeeName',), ('DeptName',),
            ('BGgroup',), ('Designation',), ('MobileNo',), ('JoiningDate',), ('ResignationDate',)
        ]
        self.mock_cursor.fetchall.return_value = [
            ('2023-24', 'U002', 'EMP002', 'Jane Smith', 'Finance', 'B', 'Analyst', '67890', date(2021, 5, 15), None)
        ]
        
        staff_list = OfficeStaff.objects.search_staff_grid(1, '2023-24', '1', 'Finance')
        self.assertEqual(len(staff_list), 1)
        self.assertEqual(staff_list[0].dept_name, 'Finance')
        self.mock_cursor.execute.assert_called_once_with(
            """
                EXEC Sp_Staff_Grid @CompId=%s, @FinId=%s, @x=%s, @y=%s
            """, [1, '2023-24', " AND Department='2'", '']
        )
        
    def test_officestaff_actual_autocomplete_suggestions(self):
        suggestions = OfficeStaffActual.get_employee_autocomplete_suggestions('john', 1)
        self.assertIn('John Doe [EMP001]', suggestions)
        self.assertNotIn('Jane Smith [EMP002]', suggestions)
        self.assertNotIn('Peter Jones [EMP003]', suggestions)
        
        suggestions_all_comp_id = OfficeStaffActual.get_employee_autocomplete_suggestions('jane', 1)
        self.assertIn('Jane Smith [EMP002]', suggestions_all_comp_id)

        suggestions_other_comp_id = OfficeStaffActual.get_employee_autocomplete_suggestions('peter', 2)
        self.assertIn('Peter Jones [EMP003]', suggestions_other_comp_id)
        self.assertNotIn('John Doe [EMP001]', suggestions_other_comp_id)


class OfficeStaffViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('hr_staff:officestaff_list')
        self.table_url = reverse('hr_staff:officestaff_table')
        self.autocomplete_url = reverse('hr_staff:employee_autocomplete')

        # Mock the session variables typically set in ASP.NET
        self.client.session['compid'] = 1
        self.client.session['finyear'] = '2023-24'

        # Mock the stored procedure call in the manager for view tests
        self.patcher = patch('hr_staff.models.OfficeStaffManager.search_staff_grid')
        self.mock_search_staff_grid = self.patcher.start()
        
        # Mock the autocomplete suggestions
        self.autocomplete_patcher = patch('hr_staff.models.OfficeStaffActual.get_employee_autocomplete_suggestions')
        self.mock_autocomplete_suggestions = self.autocomplete_patcher.start()

        # Setup mock return values for default scenarios
        self.mock_search_staff_grid.return_value = [
            OfficeStaff(fin_year='2023-24', user_id='U001', emp_id='EMP001', employee_name='John Doe', dept_name='HR', bg_group='A', designation='Manager', mobile_no='123', joining_date=date(2020,1,1), resignation_date=None)
        ]
        self.mock_autocomplete_suggestions.return_value = ['John Doe [EMP001]', 'Jane Smith [EMP002]']

    def tearDown(self):
        self.patcher.stop()
        self.autocomplete_patcher.stop()

    def test_officestaff_list_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/list.html')
        self.assertContains(response, 'Staff - Edit')
        self.assertContains(response, 'Loading staff data...') # Initial HTMX loading message

    def test_officestaff_list_view_get_with_msg(self):
        response = self.client.get(self.list_url + '?msg=Operation Successful!')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Operation Successful!') # Check for messages

    def test_officestaff_table_partial_view_get_initial_load(self):
        response = self.client.get(self.table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_staff/officestaff/_officestaff_table.html')
        self.mock_search_staff_grid.assert_called_once_with(
            comp_id=1, fin_id='2023-24', search_type=None, search_value=''
        )
        self.assertContains(response, 'John Doe')

    def test_officestaff_table_partial_view_post_search(self):
        # This POST triggers a refresh of the table via HTMX
        response = self.client.post(self.table_url, {
            'search_field': '0',
            'search_text_emp': 'John Doe [EMP001]'
        }, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOfficeStaffList')
        
        # The actual data retrieval will happen in the subsequent GET request triggered by HX-Trigger

    def test_employee_autocomplete_view(self):
        response = self.client.get(self.autocomplete_url, {'q': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('John Doe [EMP001]', response.json())
        self.mock_autocomplete_suggestions.assert_called_once_with('john', 1)

    def test_employee_autocomplete_view_no_query(self):
        response = self.client.get(self.autocomplete_url, {'q': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertEqual(response.json(), [])
        self.mock_autocomplete_suggestions.assert_not_called()

```

### Step 5: HTMX and Alpine.js Integration

*   **`list.html`:**
    *   Uses `hx-post` on the form to trigger the search. `hx-swap="none"` and `hx-trigger="submit from #searchForm"` on the form, combined with `HX-Trigger` on the `POST` response from `OfficeStaffTablePartialView`, ensure the list updates.
    *   The `staffTable-container` uses `hx-trigger="load, refreshOfficeStaffList from:body"` and `hx-get="{% url 'hr_staff:officestaff_table' %}"` to load the table on initial page load and whenever `refreshOfficeStaffList` is triggered.
    *   Alpine.js `x-data="{ selectedField: '0' }"` on the main container manages the state for the dropdown. `x-model="selectedField"` binds the dropdown. `x-show` directives on the text inputs dynamically show/hide them based on `selectedField`.
    *   The `TxtEmpName` input leverages HTMX for autocomplete suggestions, using `hx-get`, `hx-target` (pointing to an `ul` below it), `hx-trigger` (`keyup changed delay:500ms, search`), and `hx-indicator`. Alpine.js manages selection from the autocomplete list.
*   **`_officestaff_table.html`:**
    *   The DataTables `$(document).ready()` initialization is placed directly within this partial. Since HTMX replaces the inner HTML, the `$(document).ready()` ensures DataTables is re-initialized every time the table content is swapped in.
*   **No custom JavaScript:** All dynamic behavior is handled by HTMX and Alpine.js attributes, eliminating the need for separate JavaScript files like `loadingNotifier.js` or manual DOM manipulation for search/display.

### Final Notes

*   **Placeholders:** `CompId` and `FinId` (from ASP.NET `Session["compid"]` and `Session["finyear"]`) are currently hardcoded or default to `1` and `'2023-24'` in the Django views. In a real application, these should be properly retrieved from Django's `request.session` or user profile attributes after user authentication and session management are in place.
*   **`OfficeStaff_Edit_Details.aspx`:** The "Select" hyperlink in the Django template directly mimics the ASP.NET navigation. You would then build a new Django view and template for this "detail/edit" page, following similar "Fat Model, Thin View" principles.
*   **Error Handling:** The current `binddata` in C# uses empty `try-catch` blocks which swallows errors. In Django, proper error handling (e.g., logging, displaying user-friendly messages, raising exceptions) is crucial.
*   **Database connection:** Django's ORM handles database connections. The `connection.cursor()` is used for direct stored procedure calls. Ensure your `settings.py` is configured for the correct database (e.g., MSSQL via `django-mssql-backend`).
*   **Security:** Ensure proper input sanitization and parameterization for all SQL interactions (which `connection.cursor().execute()` handles for you if parameters are passed correctly).
*   **Scalability:** This approach (DataTables for client-side, HTMX for updates) scales well for moderately sized datasets. For very large datasets, server-side processing with DataTables might be required, which would involve more complex `hx-get` to the `_officestaff_table.html` with DataTables specific parameters. However, for the given ASP.NET setup (20 records per page), client-side DataTables is sufficient.