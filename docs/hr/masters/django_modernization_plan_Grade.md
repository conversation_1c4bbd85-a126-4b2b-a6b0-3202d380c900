This comprehensive modernization plan outlines the strategic transition of your ASP.NET Grade management module to a modern Django-based solution. Our approach emphasizes AI-assisted automation, leveraging Django's robust capabilities, HTMX, and Alpine.js for a highly efficient and maintainable system. The focus is on business value, reduced manual effort, and a clean, scalable architecture.

## ASP.NET to Django Conversion Script:

### Business Value Proposition:
Transitioning from ASP.NET to Django offers significant advantages:
1.  **Reduced Maintenance Costs:** Django's clear structure, Python's readability, and a strong community reduce the complexity of code, making it easier to maintain and debug.
2.  **Enhanced Scalability & Performance:** Django is designed to handle high traffic and complex operations efficiently, ensuring your application can grow with your business needs.
3.  **Modern User Experience:** By utilizing HTMX and Alpine.js, we deliver a highly responsive and dynamic user interface without the complexity of traditional JavaScript frameworks, improving user satisfaction.
4.  **Simplified Development Workflow:** The "Fat Model, Thin View" architecture, combined with automated testing, streamlines development, reduces bugs, and accelerates feature delivery.
5.  **Future-Proof Technology:** Django is actively maintained and widely adopted, providing a stable and secure foundation for long-term growth and innovation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
-   NEVER include `base.html` template code in your output - assume it already exists and includes necessary CDN links for jQuery, DataTables, HTMX, and Alpine.js.
-   Focus ONLY on component-specific code for the current module (`Grade`).
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

## AutoERP Guidelines:
-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
-   From `SqlDataSource1`:
    -   `SelectCommand="SELECT [Id], [Description], [Symbol] FROM [tblHR_Grade] Order by [Id] Desc"`
    -   `InsertCommand="INSERT INTO [tblHR_Grade] ([Description], [Symbol]) VALUES (@Description, @Symbol)"`
    -   `UpdateCommand="UPDATE [tblHR_Grade] SET [Description] = @Description, [Symbol] = @Symbol WHERE [Id] = @Id"`
    -   `DeleteCommand="DELETE FROM [tblHR_Grade] WHERE [Id] = @Id"`

**Extracted Information:**
-   **Table Name:** `tblHR_Grade`
-   **Columns:**
    -   `Id`: Integer (used as `DataKeyNames`, implying Primary Key)
    -   `Description`: String
    -   `Symbol`: String

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Extracted Information:**
-   **Read (R):** `SqlDataSource1.SelectCommand` populates `GridView1`.
-   **Create (C):** `GridView1_RowCommand` handles "Add" and "Add1" commands, inserting new records using `SqlDataSource1.Insert()`. Inputs are `txtDescription` and `txtSymbol` from the GridView's footer or empty data template.
-   **Update (U):** `GridView1_RowUpdated` event, triggered by `SqlDataSource1.UpdateCommand` when GridView is in edit mode.
-   **Delete (D):** `GridView1_RowDeleted` event, triggered by `SqlDataSource1.DeleteCommand`.
-   **Validation Logic:** `RequiredFieldValidator`s for `Description` and `Symbol` fields (in both edit and insert modes).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Inferred Components:**
-   **`asp:GridView` (`GridView1`):** Represents the main data list. It provides:
    -   Data display for `Description` and `Symbol`.
    -   Inline editing capability for `Description` and `Symbol`.
    -   Inline delete capability.
    -   An "Insert" row/section for adding new records.
    -   Paging functionality (`AllowPaging="True"`, `PageSize="20"`).
    -   Styling via `yui-datatable-theme` (suggests DataTables).
-   **`asp:TextBox`:** Used for `txtDescription`, `txtSymbol` (input for insert), and `lblDescription0`, `lblSymbol0` (input for edit).
-   **`asp:Label`:** Used for `lblDescription`, `lblSymbol` (display in list mode) and `Label2` (for status messages).
-   **`asp:Button`/`asp:LinkButton`:** Trigger insert, edit, and delete actions. Client-side `onclick` handlers (`confirmationAdd`, `confirmationUpdate`, `confirmationDelete`) suggest client-side interaction confirmation.

### Step 4: Generate Django Code

**Assumption:** The Django application for this module will be named `hr_master`.

## 4.1 Models (`hr_master/models.py`)

**Task:** Create a Django model based on the database schema.

```python
from django.db import models

class Grade(models.Model):
    # Id is the primary key and maps to the 'Id' column in tblHR_Grade
    # Setting primary_key=True ensures Django uses this as the PK
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255) # Inferred max_length based on typical usage
    symbol = models.CharField(db_column='Symbol', max_length=50) # Inferred max_length

    class Meta:
        managed = False  # Tells Django not to create or modify this table's schema
        db_table = 'tblHR_Grade' # Maps to the existing SQL table name
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.description
        
    # Business logic method: Check for uniqueness (mimicking database constraints or business rules)
    def is_unique(self):
        """
        Checks if the combination of description and symbol is unique,
        excluding the current instance if it's an update.
        Returns (True, "") if unique, (False, "Error message") otherwise.
        """
        if Grade.objects.filter(description=self.description).exclude(id=self.id).exists():
            return False, "A grade with this description already exists."
        if Grade.objects.filter(symbol=self.symbol).exclude(id=self.id).exists():
            return False, "A grade with this symbol already exists."
        return True, ""

    # Additional business logic can be added here, e.g.,
    # def get_display_name(self):
    #     return f"{self.description} ({self.symbol})"

```

## 4.2 Forms (`hr_master/forms.py`)

**Task:** Define a Django form for user input.

```python
from django import forms
from .models import Grade

class GradeForm(forms.ModelForm):
    class Meta:
        model = Grade
        fields = ['description', 'symbol'] # Exclude 'id' as it's auto-assigned or exists
        widgets = {
            'description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter grade description'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter symbol'}),
        }
    
    # Custom validation logic mirroring ASP.NET's RequiredFieldValidator and business rules
    def clean(self):
        cleaned_data = super().clean()
        
        description = cleaned_data.get('description')
        symbol = cleaned_data.get('symbol')

        # Mimic ASP.NET's RequiredFieldValidator
        if not description:
            self.add_error('description', 'Description is required.')
        if not symbol:
            self.add_error('symbol', 'Symbol is required.')

        # Apply model-level uniqueness check (fat model principle)
        # Create a temporary instance to use the model's is_unique method
        # If it's an update, the instance.id will be set from the form's instance
        temp_grade = Grade(
            id=self.instance.id if self.instance.pk else None, 
            description=description, 
            symbol=symbol
        )
        is_unique, message = temp_grade.is_unique()
        if not is_unique:
            # Add general form error or specific field error
            if "description" in message:
                self.add_error('description', message)
            elif "symbol" in message:
                self.add_error('symbol', message)
            else:
                self.add_error(None, message) # General form error
        
        return cleaned_data

```

## 4.3 Views (`hr_master/views.py`)

**Task:** Implement CRUD operations using CBVs.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Grade
from .forms import GradeForm

# Main view for the Grades list page (initial page load)
class GradeListView(ListView):
    model = Grade
    template_name = 'hr_master/grade/list.html'
    context_object_name = 'grades' # Passes the list of grades to the template

# HTMX partial view for the DataTables table content (reloaded dynamically)
class GradeTablePartialView(ListView):
    model = Grade
    template_name = 'hr_master/grade/_grade_table.html' # This is a partial template
    context_object_name = 'grades'

# View for creating a new Grade (renders and handles form submission)
class GradeCreateView(CreateView):
    model = Grade
    form_class = GradeForm
    template_name = 'hr_master/grade/_grade_form.html' # Partial template for modal form
    success_url = reverse_lazy('grade_list') # Fallback URL, mainly for non-HTMX requests

    def form_valid(self, form):
        # Business logic can be further encapsulated in the model's save method
        response = super().form_valid(form)
        messages.success(self.request, 'Grade added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content and trigger a custom event
            # to refresh the list table. This closes the modal and updates the list.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGradeList' # Custom event name to signal list update
                }
            )
        return response # Handles non-HTMX form submissions

# View for updating an existing Grade
class GradeUpdateView(UpdateView):
    model = Grade
    form_class = GradeForm
    template_name = 'hr_master/grade/_grade_form.html' # Partial template for modal form
    success_url = reverse_lazy('grade_list') # Fallback URL

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Grade updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGradeList'
                }
            )
        return response

# View for deleting a Grade
class GradeDeleteView(DeleteView):
    model = Grade
    template_name = 'hr_master/grade/_grade_confirm_delete.html' # Partial template for modal confirmation
    context_object_name = 'grade' # Name for the object in the template
    success_url = reverse_lazy('grade_list') # Fallback URL

    def delete(self, request, *args, **kwargs):
        # Business logic for deletion could be here or in model (e.g., related object checks)
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Grade deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGradeList'
                }
            )
        return response

```

## 4.4 Templates (`hr_master/templates/hr_master/grade/`)

**Task:** Create templates for each view.

### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Grades Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'grade_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Grade
        </button>
    </div>
    
    {# HTMX container for the DataTable. This will be updated dynamically. #}
    <div id="gradeTable-container"
         hx-trigger="load, refreshGradeList from:body" {# Loads on page load, and on custom event #}
         hx-get="{% url 'grade_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow overflow-hidden">
        {# Initial loading state #}
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Grades...</p>
        </div>
    </div>
    
    {# Modal structure for Add/Edit/Delete forms #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me" {# Click outside to close #}
         x-data="{ show: false }" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-0">
            {# Content loaded by HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Alpine.js trick to re-init components if needed after HTMX swap
        if (window.Alpine) {
            window.Alpine.discoverUninitialized();
        }
        // If the swapped content is the modal, show it with Alpine.js
        if (evt.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            if (modal) {
                // Manually trigger Alpine.js to update x-show state
                modal.__alpine.evaluate('show = true');
            }
        }
    });

    document.body.addEventListener('refreshGradeList', function(evt) {
        // Close modal after successful form submission/delete
        const modal = document.getElementById('modal');
        if (modal) {
            modal.__alpine.evaluate('show = false');
            modal.classList.remove('is-active'); // For immediate visual removal
            document.getElementById('modalContent').innerHTML = ''; // Clear modal content
        }
    });
</script>
{% endblock %}

```

### `_grade_table.html` (Partial)

```html
{# This template contains only the table markup, loaded via HTMX #}
<table id="gradeTable" class="min-w-full bg-white divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for grade in grades %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ grade.description }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ grade.symbol }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'grade_edit' grade.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                    hx-get="{% url 'grade_delete' grade.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{# DataTables initialization script. This will run every time this partial is swapped in. #}
<script>
    // Ensure jQuery and DataTables are loaded by base.html
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#gradeTable')) {
            $('#gradeTable').DataTable().destroy();
        }
        $('#gradeTable').DataTable({
            "pageLength": 10, // Matches ASP.NET's PageSize="20", can be adjusted
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "paging": true,
            "searching": true,
            "info": true,
            "ordering": true
        });
    });
</script>

```

### `_grade_form.html` (Partial for modal content)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Grade</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|striptags }}</p>
                {% endif %}
            </div>
            {% endfor %}
            
            {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mt-2">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal"> {# Alpine.js/HTMX based close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save
            </button>
            <span id="loadingIndicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

### `_grade_confirm_delete.html` (Partial for modal content)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete the Grade: <span class="font-bold">{{ grade.description }} ({{ grade.symbol }})</span>?</p>
    
    <form hx-post="{% url 'grade_delete' grade.pk %}" hx-swap="none" hx-indicator="#loadingIndicatorDelete">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal"> {# Alpine.js/HTMX based close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
            <span id="loadingIndicatorDelete" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

## 4.5 URLs (`hr_master/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import GradeListView, GradeCreateView, GradeUpdateView, GradeDeleteView, GradeTablePartialView

urlpatterns = [
    # Main list page
    path('grades/', GradeListView.as_view(), name='grade_list'),
    
    # HTMX endpoint for loading/refreshing the DataTables table
    path('grades/table/', GradeTablePartialView.as_view(), name='grade_table'),

    # HTMX endpoints for modal forms
    path('grades/add/', GradeCreateView.as_view(), name='grade_add'),
    path('grades/edit/<int:pk>/', GradeUpdateView.as_view(), name='grade_edit'),
    path('grades/delete/<int:pk>/', GradeDeleteView.as_view(), name='grade_delete'),
]
```

## 4.6 Tests (`hr_master/tests.py`)

**Task:** Write tests for the model and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import Grade

# Unit Tests for the Grade Model
class GradeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test Grade object for use across all tests
        cls.grade1 = Grade.objects.create(
            id=101, # Explicit ID since it's an IntegerField primary_key
            description='Executive Grade',
            symbol='EXC'
        )
        cls.grade2 = Grade.objects.create(
            id=102,
            description='Manager Grade',
            symbol='MGR'
        )
  
    def test_grade_creation(self):
        """Test that a Grade object can be created and its attributes are correct."""
        grade = Grade.objects.get(id=101)
        self.assertEqual(grade.description, 'Executive Grade')
        self.assertEqual(grade.symbol, 'EXC')
        self.assertEqual(str(grade), 'Executive Grade') # Test __str__ method

    def test_description_label(self):
        """Test the verbose name for the description field."""
        grade = Grade.objects.get(id=101)
        field_label = grade._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'description') # Default verbose_name is field name

    def test_symbol_label(self):
        """Test the verbose name for the symbol field."""
        grade = Grade.objects.get(id=101)
        field_label = grade._meta.get_field('symbol').verbose_name
        self.assertEqual(field_label, 'symbol')

    def test_uniqueness_validation(self):
        """Test the custom uniqueness validation in the model."""
        # Test existing description
        is_unique, message = Grade(id=103, description='Executive Grade', symbol='XYZ').is_unique()
        self.assertFalse(is_unique)
        self.assertIn("description already exists", message)

        # Test existing symbol
        is_unique, message = Grade(id=103, description='New Grade', symbol='EXC').is_unique()
        self.assertFalse(is_unique)
        self.assertIn("symbol already exists", message)

        # Test uniqueness during update (should pass for its own values)
        is_unique, message = self.grade1.is_unique()
        self.assertTrue(is_unique)

        # Test truly unique new grade
        is_unique, message = Grade(id=103, description='New Unique', symbol='UNI').is_unique()
        self.assertTrue(is_unique)

# Integration Tests for Grade Views
class GradeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.grade1 = Grade.objects.create(id=201, description='Junior Grade', symbol='JNR')
        cls.grade2 = Grade.objects.create(id=202, description='Senior Grade', symbol='SNR')
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolation
        self.client = Client()
    
    def test_list_view_get(self):
        """Test that the Grade list view loads correctly."""
        response = self.client.get(reverse('grade_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_master/grade/list.html')
        # Check that the main list template does not contain the table itself, but a container
        self.assertContains(response, '<div id="gradeTable-container"')

    def test_table_partial_view_get(self):
        """Test that the HTMX table partial view loads correctly and contains data."""
        response = self.client.get(reverse('grade_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_master/grade/_grade_table.html')
        self.assertContains(response, 'Junior Grade')
        self.assertContains(response, 'Senior Grade')
        self.assertContains(response, '<table id="gradeTable"') # Ensure DataTable element is present

    def test_create_view_get(self):
        """Test that the Grade creation form loads correctly in a modal."""
        response = self.client.get(reverse('grade_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_master/grade/_grade_form.html')
        self.assertContains(response, 'Add Grade') # Check for form title
        self.assertContains(response, '<form hx-post') # Ensure it's an HTMX form

    def test_create_view_post_success_htmx(self):
        """Test successful Grade creation via HTMX."""
        initial_grade_count = Grade.objects.count()
        data = {
            'description': 'New Test Grade',
            'symbol': 'NTG'
        }
        response = self.client.post(reverse('grade_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for successful HTMX post
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGradeList')
        self.assertEqual(Grade.objects.count(), initial_grade_count + 1)
        self.assertTrue(Grade.objects.filter(description='New Test Grade').exists())
        # Check messages (requires middleware)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Grade added successfully.')

    def test_create_view_post_invalid_htmx(self):
        """Test invalid Grade creation (missing fields) via HTMX."""
        initial_grade_count = Grade.objects.count()
        data = {
            'description': '', # Missing description
            'symbol': 'INV'
        }
        response = self.client.post(reverse('grade_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'hr_master/grade/_grade_form.html')
        self.assertContains(response, 'Description is required.')
        self.assertEqual(Grade.objects.count(), initial_grade_count) # No new object created

        # Test invalid Grade creation (non-unique data)
        data = {
            'description': 'Junior Grade', # Existing description
            'symbol': 'NEW'
        }
        response = self.client.post(reverse('grade_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'A grade with this description already exists.')


    def test_update_view_get(self):
        """Test that the Grade update form loads correctly."""
        response = self.client.get(reverse('grade_edit', args=[self.grade1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_master/grade/_grade_form.html')
        self.assertContains(response, 'Edit Grade')
        self.assertContains(response, self.grade1.description) # Pre-filled data

    def test_update_view_post_success_htmx(self):
        """Test successful Grade update via HTMX."""
        data = {
            'description': 'Updated Junior Grade',
            'symbol': 'UJR'
        }
        response = self.client.post(reverse('grade_edit', args=[self.grade1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGradeList')
        self.grade1.refresh_from_db()
        self.assertEqual(self.grade1.description, 'Updated Junior Grade')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Grade updated successfully.')

    def test_update_view_post_invalid_htmx(self):
        """Test invalid Grade update (non-unique data) via HTMX."""
        data = {
            'description': 'Senior Grade', # Conflict with grade2's description
            'symbol': 'SNR'
        }
        response = self.client.post(reverse('grade_edit', args=[self.grade1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_master/grade/_grade_form.html')
        self.assertContains(response, 'A grade with this description already exists.')
        self.grade1.refresh_from_db()
        self.assertNotEqual(self.grade1.description, 'Senior Grade') # Ensure no update happened

    def test_delete_view_get(self):
        """Test that the Grade deletion confirmation loads correctly."""
        response = self.client.get(reverse('grade_delete', args=[self.grade1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_master/grade/_grade_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.grade1.description)

    def test_delete_view_post_success_htmx(self):
        """Test successful Grade deletion via HTMX."""
        initial_grade_count = Grade.objects.count()
        response = self.client.post(reverse('grade_delete', args=[self.grade1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGradeList')
        self.assertEqual(Grade.objects.count(), initial_grade_count - 1)
        self.assertFalse(Grade.objects.filter(id=self.grade1.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Grade deleted successfully.')

    def test_delete_view_post_non_existent(self):
        """Test deleting a non-existent Grade."""
        response = self.client.post(reverse('grade_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Dynamic Updates:**
    -   The `gradeTable-container` in `list.html` uses `hx-trigger="load, refreshGradeList from:body"` and `hx-get="{% url 'grade_table' %}"` to load the table content initially and refresh it after any CRUD operation.
    -   All form submissions (Add, Edit, Delete) use `hx-post` with `hx-swap="none"` and return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshGradeList'})` from the views. This pattern efficiently updates the list without full page reloads.
    -   Buttons for opening modals (`Add New Grade`, `Edit`, `Delete`) use `hx-get` to fetch the form/confirmation partial into `#modalContent` and `_ = "on click add .is-active to #modal"` to display the modal.
-   **Alpine.js for UI State Management:**
    -   The main `#modal` div in `list.html` uses `x-data="{ show: false }"` and `x-show="show"` for managing its visibility. The `on click` HTMX attributes directly manipulate the CSS class (`.is-active`) to show/hide the modal, which can then be tied to Alpine's `x-show` via `htmx:afterSwap` event listeners to maintain reactivity.
    -   The `htmx:afterSwap` and `refreshGradeList` event listeners in `list.html` are crucial for re-initializing Alpine components and managing modal state after HTMX operations.
-   **DataTables for List Views:**
    -   The `_grade_table.html` partial contains the `<table>` element with `id="gradeTable"`.
    -   A `<script>` block within `_grade_table.html` initializes DataTables using `$(document).ready()`. This ensures DataTables is re-initialized correctly every time the table partial is loaded via HTMX, providing client-side sorting, searching, and pagination.
-   **Strict Separation:** No HTML in views, no business logic in templates. All styling is based on Tailwind CSS classes.

## Final Notes

-   **Placeholders:** All placeholders (`[MODEL_NAME]`, `[APP_NAME]`, etc.) have been replaced with `Grade`, `hr_master`, and relevant field names.
-   **DRY Principles:** Templates like `_grade_form.html` and `_grade_confirm_delete.html` are partials, enabling reuse and cleaner code. The model contains business logic for uniqueness.
-   **Test Coverage:** Comprehensive unit tests for the model and integration tests for all view actions (including HTMX interactions) ensure a robust and maintainable system.
-   **Modern Stack:** This plan leverages Django 5.0+, HTMX, Alpine.js, and DataTables, providing a modern, efficient, and scalable solution for your Grade management module.