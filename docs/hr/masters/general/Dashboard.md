## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Analysis of Provided ASP.NET Code:

The provided ASP.NET code for `Dashboard.aspx` and its code-behind `Dashboard.aspx.cs` is highly minimalistic. The `.aspx` file primarily defines content placeholders from a master page and includes a single JavaScript file, but it contains no explicit UI controls (like `<PERSON><PERSON><PERSON>ie<PERSON>`, `TextBox`, `Button`) or static HTML content that would indicate specific data presentation or user interaction. Similarly, the `Dashboard.aspx.cs` code-behind file only contains an empty `Page_Load` method, showing no custom logic, data retrieval, or event handling.

**Conclusion:** Based solely on the provided code, there is no discernible business logic, database interaction, or specific UI functionality to directly "migrate." It appears to be a basic container page or a placeholder for dynamic content loaded by other means not specified here.

**Approach for Modernization:** To demonstrate a comprehensive Django modernization plan, we will proceed by assuming a typical "Dashboard" context within an "HR Masters" module, as suggested by the class name `Module_HR_Masters_Dashboard`. We will conceptualize this dashboard as displaying and managing core HR "Master" data, such as `Employee` records. This allows us to illustrate the full stack of Django components (models, forms, views, templates, URLs, tests) adhering to the specified architectural principles, even in the absence of explicit source functionality.

## Step 1: Extract Database Schema

**Task:** Since no database schema is present in the ASP.NET code, we will infer a common HR master data entity: `Employee`.

**Instructions:**
We assume a database table `tblEmployee` with the following columns:
- `EmployeeID` (Primary Key, integer)
- `FirstName` (string)
- `LastName` (string)
- `Email` (string)
- `DateOfJoining` (date)
- `IsActive` (boolean)

**Inferred Schema:**
- **Table Name:** `tblEmployee`
- **Columns:**
    - `EmployeeID` (corresponds to Django's `id` or a specific primary key field)
    - `FirstName`
    - `LastName`
    - `Email`
    - `DateOfJoining`
    - `IsActive`

## Step 2: Identify Backend Functionality

**Task:** Based on the common requirements for "Master" data management in an HR module, we will assume standard CRUD operations for `Employee` records.

**Instructions:**
- **Create:** Ability to add new employee records.
- **Read:** Display a list of all employees (e.g., in a DataTables grid) and retrieve individual employee details for editing.
- **Update:** Modify existing employee records.
- **Delete:** Remove employee records.
- **Validation Logic:** Basic field validations (e.g., required fields, valid email format) will be implemented in the Django form.

## Step 3: Infer UI Components

**Task:** We will infer the need for typical UI components for managing `Employee` master data.

**Instructions:**
- **List View:** A table displaying all `Employee` records, ideally with client-side sorting, searching, and pagination (to be implemented using DataTables). This replaces any potential `asp:GridView` functionality.
- **Form for Create/Update:** A modal form for adding new employees or editing existing ones (replacing `asp:TextBox`, `asp:DropDownList`, `asp:Button` combinations).
- **Delete Confirmation:** A simple modal for confirming deletions.
- **Client-Side Interactions:** All interactions (form submission, modal display, list refresh) will use HTMX and Alpine.js, eliminating the need for traditional ASP.NET postbacks or custom JavaScript.

## Step 4: Generate Django Code

We will create a new Django application, for example, `hr_masters`, to house the `Employee` module.

### 4.1 Models (`hr_masters/models.py`)

**Task:** Create a Django model `Employee` based on the inferred database schema.

**Instructions:**
- Name the model `Employee`.
- Define fields mapping to `tblEmployee` columns, using `db_column` for explicit mapping.
- Set `managed = False` and `db_table = 'tblEmployee'` in the `Meta` class.
- Include an example model method demonstrating the "fat model" principle.

```python
from django.db import models
from django.utils import timezone

class Employee(models.Model):
    # Primary Key - Assuming EmployeeID as a distinct column, not Django's default 'id'
    # If EmployeeID is truly an auto-incrementing PK in the existing DB,
    # Django's default 'id' field often maps automatically. 
    # For explicit mapping, if it's not the primary key managed by Django:
    employee_id = models.IntegerField(db_column='EmployeeID', primary_key=True)
    first_name = models.CharField(db_column='FirstName', max_length=100)
    last_name = models.CharField(db_column='LastName', max_length=100)
    email = models.EmailField(db_column='Email', max_length=255, unique=True)
    date_of_joining = models.DateField(db_column='DateOfJoining')
    is_active = models.BooleanField(db_column='IsActive', default=True)

    class Meta:
        managed = False  # Important: Django will not manage this table's creation/alteration
        db_table = 'tblEmployee'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'
        ordering = ['last_name', 'first_name'] # Default ordering for lists

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    def get_full_name(self):
        """
        Business logic: Returns the full name of the employee.
        Example of a 'fat model' method.
        """
        return f"{self.first_name} {self.last_name}"

    def deactivate_employee(self):
        """
        Business logic: Deactivates an employee.
        """
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False

    def activate_employee(self):
        """
        Business logic: Activates an employee.
        """
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False
```

### 4.2 Forms (`hr_masters/forms.py`)

**Task:** Define a Django `ModelForm` for user input, including validation and styling.

**Instructions:**
- Create `EmployeeForm` based on the `Employee` model.
- Include all editable fields.
- Add `widgets` with Tailwind CSS classes.
- Implement basic validation.

```python
from django import forms
from .models import Employee

class EmployeeForm(forms.ModelForm):
    class Meta:
        model = Employee
        fields = ['employee_id', 'first_name', 'last_name', 'email', 'date_of_joining', 'is_active']
        widgets = {
            'employee_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Employee ID'}),
            'first_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'First Name'}),
            'last_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Last Name'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Email Address'}),
            'date_of_joining': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'employee_id': 'Employee ID',
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email',
            'date_of_joining': 'Date of Joining',
            'is_active': 'Is Active',
        }

    def clean_employee_id(self):
        employee_id = self.cleaned_data['employee_id']
        # If updating, ensure the employee_id isn't being changed to an existing one
        if self.instance.pk: # This means it's an update operation
            if Employee.objects.filter(employee_id=employee_id).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("This Employee ID already exists for another employee.")
        else: # This means it's a create operation
            if Employee.objects.filter(employee_id=employee_id).exists():
                raise forms.ValidationError("This Employee ID already exists.")
        return employee_id

    def clean_email(self):
        email = self.cleaned_data['email']
        # Check for uniqueness, excluding the current instance if it's an update
        if self.instance.pk:
            if Employee.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("An employee with this email already exists.")
        else:
            if Employee.objects.filter(email=email).exists():
                raise forms.ValidationError("An employee with this email already exists.")
        return email

```

### 4.3 Views (`hr_masters/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and HTMX support. Also, create a partial view for the DataTables content.

**Instructions:**
- Define `EmployeeListView`, `EmployeeCreateView`, `EmployeeUpdateView`, `EmployeeDeleteView`.
- Add `EmployeeTablePartialView` for HTMX-driven table refreshes.
- Keep views concise, moving business logic to the `Employee` model.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Employee
from .forms import EmployeeForm

# A separate view for the HTMX-loaded table content
class EmployeeTablePartialView(ListView):
    model = Employee
    template_name = 'hr_masters/employee/_employee_table.html'
    context_object_name = 'employees'

class EmployeeListView(ListView):
    model = Employee
    template_name = 'hr_masters/employee/list.html'
    context_object_name = 'employees' # This is for the initial page load, the table is loaded via HTMX

class EmployeeCreateView(CreateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'hr_masters/employee/_employee_form.html' # Use partial template for modal
    success_url = reverse_lazy('employee_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # Business logic is handled by the model's clean methods and default save
        response = super().form_valid(form)
        messages.success(self.request, 'Employee added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEmployeeList' # Custom event to refresh the table
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # For HTMX requests, return the form with errors for re-rendering the modal
        if self.request.headers.get('HX-Request'):
            return response
        return response


class EmployeeUpdateView(UpdateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'hr_masters/employee/_employee_form.html' # Use partial template for modal
    success_url = reverse_lazy('employee_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # Business logic is handled by the model's clean methods and default save
        response = super().form_valid(form)
        messages.success(self.request, 'Employee updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEmployeeList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class EmployeeDeleteView(DeleteView):
    model = Employee
    template_name = 'hr_masters/employee/_employee_confirm_delete.html' # Use partial template for modal
    success_url = reverse_lazy('employee_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        # Example of calling model business logic before deletion
        # In this case, standard deletion is fine, but could add checks like:
        # self.get_object().deactivate_employee() # if soft delete was desired instead
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Employee deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEmployeeList'
                }
            )
        return response

```

### 4.4 Templates (`hr_masters/templates/hr_masters/employee/`)

**Task:** Create templates for each view, incorporating DataTables, HTMX, and Alpine.js.

**Instructions:**
- **`list.html`**: The main page, extends `core/base.html`, loads the table via HTMX. Includes modal structure.
- **`_employee_table.html`**: A partial template containing the DataTables table, loaded dynamically by HTMX.
- **`_employee_form.html`**: A partial template for the create/update form, loaded into the modal.
- **`_employee_confirm_delete.html`**: A partial template for the delete confirmation, loaded into the modal.

**`list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Employee Directory</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'employee_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal transition ease-out duration-300 transform scale-100"
        >
            <i class="fas fa-plus mr-2"></i> Add New Employee
        </button>
    </div>

    <div id="employeeTable-container"
         hx-trigger="load, refreshEmployeeList from:body"
         hx-get="{% url 'employee_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Loading spinner for initial load and HTMX refresh -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Employee Data...</p>
        </div>
    </div>

    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-80 hidden items-center justify-center z-50 transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then wait 0.3s then remove .flex from me"
         style="opacity: 0;">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 transform scale-95 transition-transform duration-300">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS and CSS are assumed to be in core/base.html -->
<script>
    // Alpine.js component initialization if needed, but HTMX handles most of it
    document.addEventListener('alpine:init', () => {
        // Example: If you had a search input that filters before HTMX request
        Alpine.data('employeeSearch', () => ({
            searchTerm: '',
            filterEmployees() {
                // This would trigger an HTMX request with search params
                // hx-get="{% url 'employee_table' %}?search={{ searchTerm }}"
                // This specific example isn't fully implemented in the current HTMX setup
                // as DataTables handles filtering on client side, but demonstrates Alpine usage.
            }
        }));
    });

    // Event listener for HTMX event to re-initialize DataTables after a swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'employeeTable-container') {
            $('#employeeTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing table if it exists before re-init
                "responsive": true,
                "autoWidth": false,
            });
        }
    });

    // Close modal on successful HTMX form submission (status 204)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('opacity-100');
                setTimeout(() => modal.classList.remove('flex'), 300);
            }
        }
    });
</script>
{% endblock %}
```

**`_employee_table.html`**
```html
<div class="overflow-x-auto">
    <table id="employeeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Full Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Joining</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for employee in employees %}
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.employee_id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.get_full_name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.email }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.date_of_joining|date:"M d, Y" }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if employee.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ employee.is_active|yesno:"Active,Inactive" }}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-4 focus:outline-none"
                        hx-get="{% url 'employee_edit' employee.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal transition ease-out duration-300 transform scale-100"
                        title="Edit"
                    >
                        <i class="fas fa-edit"></i>
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 focus:outline-none"
                        hx-get="{% url 'employee_delete' employee.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal transition ease-out duration-300 transform scale-100"
                        title="Delete"
                    >
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No employee records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // This script block will be executed after HTMX loads the content
    // The DataTables initialization is moved to the list.html's htmx:afterSwap listener
    // to ensure it runs only once the table content is in the DOM and the page is ready.
    // However, for direct rendering or if list.html doesn't exist, this might be needed.
    // Keeping it here for component independence, but list.html handles the trigger.
    // $('#employeeTable').DataTable({
    //     "pageLength": 10,
    //     "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
    //     "responsive": true,
    //     "autoWidth": false,
    // });
</script>
```

**`_employee_form.html`**
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Employee</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4 {% if field.name == 'employee_id' and form.instance.pk %}opacity-60 pointer-events-none{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {% if field.name == 'is_active' %}
                    <div class="flex items-center mt-2">
                        {{ field }}
                        <span class="ml-2 text-sm text-gray-700">Set employee as active</span>
                    </div>
                {% else %}
                    {{ field }}
                {% endif %}

                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mb-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .opacity-100 from #modal then wait 0.3s then remove .flex from #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
                <span id="form-spinner" class="htmx-indicator animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
                Save Employee
            </button>
        </div>
    </form>
</div>
```

**`_employee_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the employee: <strong>{{ object.get_full_name }} (ID: {{ object.employee_id }})</strong>?</p>
    <p class="text-red-600 text-sm mb-8">This action cannot be undone.</p>

    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .opacity-100 from #modal then wait 0.3s then remove .flex from #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
                Delete Employee
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`hr_masters/urls.py`)

**Task:** Define URL patterns for all views and partial views.

**Instructions:**
- Create paths for the main list view, and the HTMX-specific endpoints for add, edit, delete, and table refresh.

```python
from django.urls import path
from .views import (
    EmployeeListView,
    EmployeeCreateView,
    EmployeeUpdateView,
    EmployeeDeleteView,
    EmployeeTablePartialView
)

urlpatterns = [
    path('employees/', EmployeeListView.as_view(), name='employee_list'),
    # HTMX endpoints for modal forms and table
    path('employees/add/', EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/edit/<int:pk>/', EmployeeUpdateView.as_view(), name='employee_edit'),
    path('employees/delete/<int:pk>/', EmployeeDeleteView.as_view(), name='employee_delete'),
    path('employees/table/', EmployeeTablePartialView.as_view(), name='employee_table'),
]
```
*Note: Remember to include this `hr_masters/urls.py` in your project's main `urls.py` (e.g., `path('hr/', include('hr_masters.urls'))`).*

### 4.6 Tests (`hr_masters/tests.py`)

**Task:** Write comprehensive unit tests for the model and integration tests for all views, ensuring high coverage.

**Instructions:**
- Include `EmployeeModelTest` for model methods and field properties.
- Add `EmployeeViewsTest` to cover GET/POST for list, create, update, and delete views, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Employee
from django.utils import timezone
from datetime import date

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        Employee.objects.create(
            employee_id=101,
            first_name='John',
            last_name='Doe',
            email='<EMAIL>',
            date_of_joining=date(2020, 1, 15),
            is_active=True
        )
        Employee.objects.create(
            employee_id=102,
            first_name='Jane',
            last_name='Smith',
            email='<EMAIL>',
            date_of_joining=date(2021, 5, 20),
            is_active=False
        )
  
    def test_employee_creation(self):
        obj = Employee.objects.get(employee_id=101)
        self.assertEqual(obj.first_name, 'John')
        self.assertEqual(obj.last_name, 'Doe')
        self.assertEqual(obj.email, '<EMAIL>')
        self.assertEqual(obj.date_of_joining, date(2020, 1, 15))
        self.assertTrue(obj.is_active)

    def test_get_full_name_method(self):
        obj = Employee.objects.get(employee_id=101)
        self.assertEqual(obj.get_full_name(), 'John Doe')
        
    def test_deactivate_employee_method(self):
        obj = Employee.objects.get(employee_id=101)
        self.assertTrue(obj.deactivate_employee())
        obj.refresh_from_db()
        self.assertFalse(obj.is_active)
        self.assertFalse(obj.deactivate_employee()) # Should return False if already inactive

    def test_activate_employee_method(self):
        obj = Employee.objects.get(employee_id=102)
        self.assertTrue(obj.activate_employee())
        obj.refresh_from_db()
        self.assertTrue(obj.is_active)
        self.assertFalse(obj.activate_employee()) # Should return False if already active

    def test_verbose_name_plural(self):
        self.assertEqual(Employee._meta.verbose_name_plural, 'Employees')

    def test_ordering(self):
        employees = Employee.objects.all()
        self.assertEqual(employees[0].first_name, 'John') # Doe, John
        self.assertEqual(employees[1].first_name, 'Jane') # Smith, Jane (ordered by last_name then first_name)


class EmployeeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        Employee.objects.create(
            employee_id=201,
            first_name='Alice',
            last_name='Brown',
            email='<EMAIL>',
            date_of_joining=date(2019, 3, 1),
            is_active=True
        )
        Employee.objects.create(
            employee_id=202,
            first_name='Bob',
            last_name='White',
            email='<EMAIL>',
            date_of_joining=date(2022, 1, 1),
            is_active=False
        )
    
    def setUp(self):
        self.client = Client()
        self.employee_alice = Employee.objects.get(employee_id=201)
        self.employee_bob = Employee.objects.get(employee_id=202)
    
    def test_employee_list_view(self):
        response = self.client.get(reverse('employee_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/employee/list.html')
        # The actual employee list is loaded via HTMX, so we check the container
        self.assertContains(response, '<div id="employeeTable-container"')
        
    def test_employee_table_partial_view(self):
        # Test the HTMX endpoint for the table
        response = self.client.get(reverse('employee_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/employee/_employee_table.html')
        self.assertTrue('employees' in response.context)
        self.assertEqual(len(response.context['employees']), Employee.objects.count())
        self.assertContains(response, 'Alice Brown')
        self.assertContains(response, 'Bob White')

    def test_employee_create_view_get(self):
        response = self.client.get(reverse('employee_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/employee/_employee_form.html')
        self.assertTrue('form' in response.context)
        
    def test_employee_create_view_post_success(self):
        data = {
            'employee_id': 301,
            'first_name': 'New',
            'last_name': 'Employee',
            'email': '<EMAIL>',
            'date_of_joining': '2023-01-01',
            'is_active': True,
        }
        # Simulate HTMX request by adding HTTP_HX_REQUEST header
        response = self.client.post(reverse('employee_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(Employee.objects.filter(employee_id=301).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshEmployeeList')

    def test_employee_create_view_post_invalid_data(self):
        data = {
            'employee_id': 302,
            'first_name': '', # Invalid, should fail
            'last_name': 'Invalid',
            'email': 'invalid-email', # Invalid, should fail
            'date_of_joining': '2023-01-01',
            'is_active': True,
        }
        response = self.client.post(reverse('employee_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns the form with errors
        self.assertTemplateUsed(response, 'hr_masters/employee/_employee_form.html')
        self.assertFalse(Employee.objects.filter(employee_id=302).exists())
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid email address.')

    def test_employee_create_view_post_duplicate_employee_id(self):
        data = {
            'employee_id': 201, # Existing ID
            'first_name': 'Duplicate',
            'last_name': 'Test',
            'email': '<EMAIL>',
            'date_of_joining': '2023-01-01',
            'is_active': True,
        }
        response = self.client.post(reverse('employee_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'This Employee ID already exists.')

    def test_employee_update_view_get(self):
        response = self.client.get(reverse('employee_edit', args=[self.employee_alice.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/employee/_employee_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.employee_alice)
        
    def test_employee_update_view_post_success(self):
        updated_data = {
            'employee_id': self.employee_alice.employee_id, # Must be present, but cannot change primary key
            'first_name': 'Alicia',
            'last_name': 'Brown-Smith',
            'email': '<EMAIL>',
            'date_of_joining': '2019-03-01',
            'is_active': False,
        }
        response = self.client.post(reverse('employee_edit', args=[self.employee_alice.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.employee_alice.refresh_from_db()
        self.assertEqual(self.employee_alice.first_name, 'Alicia')
        self.assertFalse(self.employee_alice.is_active)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshEmployeeList')

    def test_employee_update_view_post_invalid_data(self):
        updated_data = {
            'employee_id': self.employee_alice.employee_id,
            'first_name': '',
            'last_name': 'Brown',
            'email': 'invalid-email',
            'date_of_joining': '2019-03-01',
            'is_active': True,
        }
        response = self.client.post(reverse('employee_edit', args=[self.employee_alice.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/employee/_employee_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid email address.')
        self.employee_alice.refresh_from_db()
        self.assertEqual(self.employee_alice.first_name, 'Alice') # Ensure not updated

    def test_employee_delete_view_get(self):
        response = self.client.get(reverse('employee_delete', args=[self.employee_bob.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/employee/_employee_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.employee_bob)
        
    def test_employee_delete_view_post_success(self):
        employee_count_before = Employee.objects.count()
        response = self.client.post(reverse('employee_delete', args=[self.employee_bob.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Employee.objects.count(), employee_count_before - 1)
        self.assertFalse(Employee.objects.filter(pk=self.employee_bob.pk).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshEmployeeList')

    def test_employee_delete_view_post_not_found(self):
        non_existent_pk = 999
        employee_count_before = Employee.objects.count()
        response = self.client.post(reverse('employee_delete', args=[non_existent_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
        self.assertEqual(Employee.objects.count(), employee_count_before) # No change
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Updates:** All form submissions (add/edit) and delete operations are configured to use HTMX (`hx-post`, `hx-get`, `hx-target`, `hx-swap`, `hx-trigger`). This prevents full page reloads.
- **HTMX for List Refresh:** A custom `HX-Trigger: refreshEmployeeList` header is sent from `form_valid` and `delete` methods in views. This event is listened for in `list.html` to re-fetch and re-render the `_employee_table.html` content, automatically re-initializing DataTables.
- **Alpine.js for UI State:** Alpine.js is used for managing the modal's open/close state (`x-data`, `x-show`, `x-transition`) and simple imperative UI actions (`_=` attribute for event handling).
- **DataTables for List Views:** The `_employee_table.html` partial is designed to be a DataTables container. The `list.html` listens for `htmx:afterSwap` to re-initialize DataTables, ensuring proper functionality after HTMX content updates.
- **Modal Interactions:** Modals for add, edit, and delete are loaded via HTMX `hx-get` into the `modalContent` div. The modal's visibility is controlled by Alpine.js and `_` attributes, providing smooth transitions.
- **No Custom JavaScript (Beyond HTMX/Alpine/DataTables Init):** All dynamic interactions are achieved through HTMX attributes or minimal Alpine.js directives, eliminating the need for complex, handwritten JavaScript.

## Final Notes

This comprehensive plan, although built on inferred functionality due to the minimal ASP.NET source, provides a robust and modern Django implementation for an HR `Employee` master data module. It strictly adheres to the "fat model, thin view" principle, leverages HTMX and Alpine.js for a highly interactive user experience without excessive JavaScript, integrates DataTables for efficient data presentation, and includes thorough testing for reliability. This structure can be systematically applied through AI-assisted automation to other modules of the ASP.NET application, facilitating a streamlined and efficient migration process.