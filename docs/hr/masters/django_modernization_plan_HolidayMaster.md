This comprehensive modernization plan outlines the automated conversion of your ASP.NET `HolidayMaster` module to a modern Django application, leveraging Django 5.0+, HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience. Our approach prioritizes automation, minimizing manual coding and ensuring a systematic transition.

### Business Value of Modernization:

*   **Enhanced User Experience:** Delivers a fast, responsive interface with dynamic updates without full page reloads, akin to a single-page application (SPA) but with simpler development.
*   **Reduced Maintenance Costs:** Moves away from legacy ASP.NET Web Forms, which are costly to maintain and scale. Django, with its clear structure and active community, significantly lowers long-term operational expenses.
*   **Improved Scalability and Performance:** Django's robust ORM and architecture are designed for modern web applications, providing better performance and easier scaling compared to older ASP.NET patterns.
*   **Simplified Development:** Adopts a "fat model, thin view" approach, centralizing business logic for easier understanding, testing, and future enhancements.
*   **Future-Proof Technology:** Transitions to a widely adopted, open-source framework, ensuring long-term viability, access to a vast talent pool, and continuous innovation.
*   **Developer Productivity:** Leverages powerful tools like HTMX and Alpine.js, which reduce the need for complex JavaScript frameworks, speeding up frontend development.
*   **Data Consistency:** Ensures data integrity through Django's ORM and robust form validation, mirroring and enhancing the original ASP.NET validation logic.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the `SqlDataSource1` in the ASP.NET code, the database table and its columns are identified:

*   **Table Name:** `tblHR_Holiday_Master`
*   **Columns:**
    *   `Id` (Primary Key, Integer)
    *   `HDate` (String, e.g., 'DD-MM-YYYY')
    *   `Title` (String)
    *   `SysDate` (String, System Date)
    *   `SysTime` (String, System Time)
    *   `SessionId` (String, User Session ID/Username)
    *   `CompId` (Integer, Company ID)
    *   `FinYearId` (Integer, Financial Year ID)

### Step 2: Identify Backend Functionality

The ASP.NET code implements standard CRUD (Create, Read, Update, Delete) operations for Holiday records:

*   **Create (Add):** Triggered by "Insert" buttons in both the GridView's footer and its empty data template. Inserts a new holiday record into `tblHR_Holiday_Master`. The `HDate` and `Title` are user inputs, while `SysDate`, `SysTime`, `SessionId`, `CompId`, and `FinYearId` are auto-populated from system/session data.
*   **Read (Select):** Data is fetched from `tblHR_Holiday_Master` filtered by `CompId` and `FinYearId`, then ordered by `Id` in descending order. This data is displayed in the `GridView1`.
*   **Update (Edit):** Triggered when a row in the GridView is put into "Edit" mode and subsequently updated. Modifies an existing holiday record in `tblHR_Holiday_Master`. `Title` and `HDate` are editable, with `SysDate` and `SysTime` updated on modify.
*   **Delete (Delete):** Triggered by the "Delete" link button in the GridView. Removes a holiday record from `tblHR_Holiday_Master` based on its `Id`.
*   **Validation:** Client-side validation for `HDate` (date format `DD-MM-YYYY`) and `Title` (required field) is present using `RegularExpressionValidator` and `RequiredFieldValidator`. Server-side validation implicit through `SqlDataSource` parameters.
*   **Session Management:** `CompId`, `FinYearId`, and `SessionId` (username) are retrieved from ASP.NET `Session` variables and used in CRUD operations.
*   **Date/Time Handling:** `fun.getCurrDate()` and `fun.getCurrTime()` are used for system dates/times, and `fun.FromDate()` is used to convert `DD-MM-YYYY` string dates for database interaction.

### Step 3: Infer UI Components

The ASP.NET UI components and their roles are mapped to their Django/HTMX/Alpine.js equivalents:

*   **`asp:GridView`:** This translates to a Django template (`_holiday_table.html`) displaying an HTML table, enhanced with `DataTables` for client-side features like pagination, sorting, and search. HTMX will be used to dynamically load and refresh this table.
*   **`asp:TextBox` (for `HDate`, `Title`):** Becomes standard HTML `<input type="text">` fields with Tailwind CSS classes. For dates, a `type="date"` input or a text input paired with a simple JavaScript date picker (using Alpine.js or a small library) for `DD-MM-YYYY` format will be used.
*   **`AjaxControlToolkit:CalendarExtender`:** Replaced by the native HTML `type="date"` input (if modern browsers are targeted and `YYYY-MM-DD` is acceptable for input) or a lightweight JavaScript date picker integrated with Alpine.js to handle `DD-MM-YYYY` input/display.
*   **`asp:Button`, `asp:LinkButton`:** Converted to standard HTML `<button>` elements. HTMX attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`, `hx-confirm`) will replace server-side click events and client-side JavaScript calls (`OnClientClick`).
*   **`asp:Label` (for `lblMessage`):** Django's messages framework will handle success/error notifications, rendered in the main template.
*   **Modals:** Instead of separate pages or complex Ajax postbacks, HTMX will load forms and delete confirmations into a modal overlay controlled by Alpine.js.

---

### Step 4: Generate Django Code

We will structure the Django application, named `hr`, with the following files:

#### 4.1 Models (`hr/models.py`)

This model directly maps to the existing `tblHR_Holiday_Master` table. We use `managed = False` to tell Django not to manage the table's schema (i.e., not create/alter it via migrations). The `holiday_date` property provides a convenient way to work with actual Python `date` objects, while `holiday_date_str` handles the `DD-MM-YYYY` string storage. The `prepare_for_save` method encapsulates the business logic for populating system fields, keeping views clean.

```python
from django.db import models
from datetime import datetime, date

class Holiday(models.Model):
    # Primary key, mapped from existing 'Id' column
    id = models.IntegerField(primary_key=True, db_column='Id')
    
    # User-editable fields
    title = models.CharField(db_column='Title', max_length=255)
    # HDate is stored as string in DB, but we'll use a property for date object access
    holiday_date_str = models.CharField(db_column='HDate', max_length=10) 

    # System-managed fields, populated automatically on save
    sys_date_str = models.CharField(db_column='SysDate', max_length=10)
    sys_time_str = models.CharField(db_column='SysTime', max_length=8)
    session_id = models.CharField(db_column='SessionId', max_length=255)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblHR_Holiday_Master'  # Explicitly map to the existing table
        verbose_name = 'Holiday'
        verbose_name_plural = 'Holidays'

    def __str__(self):
        """Returns the holiday title for display."""
        return self.title

    @property
    def holiday_date(self) -> date | None:
        """Converts the stored 'DD-MM-YYYY' string to a datetime.date object."""
        try:
            return datetime.strptime(self.holiday_date_str, '%d-%m-%Y').date()
        except (ValueError, TypeError):
            return None # Return None if parsing fails

    @holiday_date.setter
    def holiday_date(self, value: date):
        """Sets the holiday_date_str from a datetime.date object."""
        if isinstance(value, date):
            self.holiday_date_str = value.strftime('%d-%m-%Y')
        else:
            # Handle cases where value might not be a date object (e.g., from form)
            # This setter primarily for internal model use, form handles conversion
            pass 

    def prepare_for_save(self, request_user, company_id: int, financial_year_id: int):
        """
        Populates system-related fields (SysDate, SysTime, SessionId, CompId, FinYearId)
        before saving the model instance. This encapsulates the business logic as part of the model.
        """
        # Assign session/user data
        self.session_id = request_user.username if request_user and request_user.is_authenticated else 'anonymous'
        self.company_id = company_id
        self.financial_year_id = financial_year_id
        
        # Assign current system date and time
        current_time = datetime.now()
        self.sys_date_str = current_time.strftime('%d-%m-%Y')
        self.sys_time_str = current_time.strftime('%H:%M:%S')

```

#### 4.2 Forms (`hr/forms.py`)

A Django `ModelForm` is created to handle user input for `Holiday` objects. The `holiday_date` field is defined as a `DateField` to leverage Django's date validation and widget capabilities, and then converted to the `DD-MM-YYYY` string format for model storage in the `clean_holiday_date` method.

```python
from django import forms
from .models import Holiday
from datetime import datetime, date

class HolidayForm(forms.ModelForm):
    # Use a DateField for proper date handling in the form,
    # despite the model storing it as a string.
    holiday_date = forms.DateField(
        label="Date", # Label for the form field
        widget=forms.DateInput(attrs={
            'type': 'date', # This provides a native date picker in modern browsers
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }),
        # Allow parsing 'DD-MM-YYYY' and default 'YYYY-MM-DD' formats
        input_formats=['%d-%m-%Y', '%Y-%m-%d'], 
        error_messages={'invalid': 'Please enter date in DD-MM-YYYY format (e.g., 01-01-2023).'}
    )

    class Meta:
        model = Holiday
        # Specify fields that the user can edit through this form.
        # 'holiday_date' in the form maps to 'holiday_date_str' in the model via clean/save.
        fields = ['title', 'holiday_date'] 
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter holiday title'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If the form is being used to edit an existing instance,
        # populate the 'holiday_date' form field using the model's 'holiday_date' property.
        if self.instance.pk and self.instance.holiday_date_str:
            self.initial['holiday_date'] = self.instance.holiday_date 

    def clean_holiday_date(self) -> str:
        """
        Custom cleaning method for holiday_date.
        Ensures the date is converted to the 'DD-MM-YYYY' string format expected by the model.
        """
        date_obj: date = self.cleaned_data['holiday_date']
        return date_obj.strftime('%d-%m-%Y')

    def save(self, commit=True):
        """
        Overrides the default save method to ensure the cleaned 'holiday_date' 
        (which is now a 'DD-MM-YYYY' string) is correctly assigned to the model's 
        'holiday_date_str' field before saving.
        """
        instance: Holiday = super().save(commit=False)
        # Assign the cleaned and formatted date string to the model field
        instance.holiday_date_str = self.cleaned_data['holiday_date'] 
        if commit:
            instance.save()
        return instance

```

#### 4.3 Views (`hr/views.py`)

Django Class-Based Views (CBVs) are used for standard CRUD operations, adhering to the thin view principle. The `prepare_for_save` method on the model is called here to populate system fields, keeping view logic minimal. HTMX-specific responses (status 204 with `HX-Trigger`) are used for seamless partial updates. A dedicated `HolidayTablePartialView` renders just the table content for HTMX refreshes.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import Holiday
from .forms import HolidayForm

class HolidayTablePartialView(ListView):
    """
    View to render only the holiday table content.
    Used by HTMX to refresh the table without a full page reload.
    """
    model = Holiday
    template_name = 'hr/holiday/_holiday_table.html'
    context_object_name = 'holidays'

    def get_queryset(self):
        """
        Filters holidays based on company and financial year from session,
        mimicking the ASP.NET SqlDataSource's SELECT command.
        """
        # Retrieve session values. Defaults are provided for testing/initial setup.
        company_id = self.request.session.get('compid', 1) 
        financial_year_id = self.request.session.get('finyear', 1) 
        
        # Filter and order the queryset as per original ASP.NET logic
        return Holiday.objects.filter(
            company_id=company_id, 
            financial_year_id=financial_year_id
        ).order_by('-id')

class HolidayListView(ListView):
    """
    Main view to display the Holiday Master page.
    The actual table content is loaded via HTMX by HolidayTablePartialView.
    """
    model = Holiday
    template_name = 'hr/holiday/list.html'
    context_object_name = 'holidays' # Name of the queryset variable in the template

    def get_queryset(self):
        # This queryset is not directly used for the table rendering (HTMX does that),
        # but is good practice to define for the main view's context.
        return Holiday.objects.all().order_by('-id')

class HolidayCreateView(CreateView):
    """
    View for adding a new holiday. Handles both GET (display form) and POST (process form) requests.
    """
    model = Holiday
    form_class = HolidayForm
    template_name = 'hr/holiday/_holiday_form.html' # Renders as a partial inside a modal
    success_url = reverse_lazy('holiday_list') # Standard redirect URL, used if not HTMX

    def form_valid(self, form):
        """
        Called when the form is valid. Populates system fields on the model instance
        before saving, centralizing business logic in the model's `prepare_for_save` method.
        """
        # Retrieve context values for system fields (e.g., from session or user profile)
        company_id = self.request.session.get('compid', 1)
        financial_year_id = self.request.session.get('finyear', 1)
        request_user = self.request.user # Django's User object

        # Delegate system field population to the model instance (Fat Model principle)
        form.instance.prepare_for_save(request_user, company_id, financial_year_id)
        
        # Save the form instance (which now includes system fields)
        response = super().form_valid(form)
        
        # Add a success message to be displayed to the user
        messages.success(self.request, 'Holiday added successfully.')

        # If it's an HTMX request, send a 204 No Content response with a trigger
        # to refresh the holiday list on the client side.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshHolidayList'}
            )
        return response

    def form_invalid(self, form):
        """
        Called when the form is invalid. If an HTMX request, re-renders the form 
        with validation errors so HTMX can swap it back into the modal.
        """
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class HolidayUpdateView(UpdateView):
    """
    View for editing an existing holiday. Handles both GET and POST requests.
    """
    model = Holiday
    form_class = HolidayForm
    template_name = 'hr/holiday/_holiday_form.html' # Renders as a partial inside a modal
    success_url = reverse_lazy('holiday_list')

    def form_valid(self, form):
        """
        Called when the form is valid. Populates system fields and updates the holiday.
        """
        company_id = self.request.session.get('compid', 1)
        financial_year_id = self.request.session.get('finyear', 1)
        request_user = self.request.user
        
        # Update system fields on the model instance (Fat Model principle)
        form.instance.prepare_for_save(request_user, company_id, financial_year_id)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Holiday updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshHolidayList'}
            )
        return response

    def form_invalid(self, form):
        """
        Called when the form is invalid. If an HTMX request, re-renders the form 
        with validation errors.
        """
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class HolidayDeleteView(DeleteView):
    """
    View for deleting a holiday. Displays a confirmation form and handles deletion.
    """
    model = Holiday
    template_name = 'hr/holiday/_holiday_confirm_delete.html' # Renders as a partial inside a modal
    success_url = reverse_lazy('holiday_list')

    def delete(self, request, *args, **kwargs):
        """
        Deletes the holiday. Sends a 204 No Content response with a trigger
        for HTMX after successful deletion.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Holiday deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshHolidayList'}
            )
        return response

```

#### 4.4 Templates (`hr/templates/hr/holiday/`)

These templates provide the HTML structure for the Holiday Master page and its interactive components. They utilize HTMX for dynamic content loading (e.g., table refreshes, form modals) and Alpine.js for basic UI state management (like showing/hiding modals). DataTables is used for the list view's interactive features.

**`list.html`**: The main page that sets up the container for the HTMX-loaded table and the modal.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Holiday Master</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md"
            hx-get="{% url 'holiday_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Alpine.js for modal display #}
            Add New Holiday
        </button>
    </div>
    
    <!-- Message display area for Django messages -->
    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- HTMX will load the holiday table here -->
    <div id="holidayTable-container"
         hx-trigger="load, refreshHolidayList from:body" {# Loads on page load, and on custom event #}
         hx-get="{% url 'holiday_table' %}"
         hx-swap="innerHTML">
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading holidays...</p>
        </div>
    </div>
    
    <!-- Modal structure for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"> {# Click outside to close #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here for more complex UI states,
        // though the modal example here is primarily driven by HTMX + _hyperscript.
    });
</script>
{% endblock %}

```

**`_holiday_table.html`**: A partial template that renders the DataTables-enabled table. It's designed to be loaded dynamically by HTMX.

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="holidayTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in holidays %}
            <tr class="hover:bg-gray-50">
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.holiday_date_str }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.title }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-4"
                        hx-get="{% url 'holiday_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'holiday_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-4 px-6 text-center text-gray-500">No holidays found.</td>
            </tr>
            <tr>
                <td colspan="4" class="py-4 px-6 text-center">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md"
                        hx-get="{% url 'holiday_add' %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Add First Holiday
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table is loaded by HTMX
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#holidayTable')) {
            $('#holidayTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers",
                "responsive": true
            });
        }
    });
</script>

```

**`_holiday_form.html`**: A partial template for the create/update form, loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Holiday</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-md"
                _="on click remove .is-active from #modal"> {# Close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md">
                Save
            </button>
        </div>
    </form>
</div>

```

**`_holiday_confirm_delete.html`**: A partial template for the delete confirmation, loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-5 text-gray-700">Are you sure you want to delete the holiday "<span class="font-semibold">{{ object.title }}</span>" ({{ object.holiday_date_str }})?</p>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-md"
                _="on click remove .is-active from #modal"> {# Close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md">
                Delete
            </button>
        </div>
    </form>
</div>

```

#### 4.5 URLs (`hr/urls.py`)

URL patterns for the `hr` application, defining clear endpoints for list, create, update, delete operations, and the HTMX-specific table partial.

```python
from django.urls import path
from .views import HolidayListView, HolidayCreateView, HolidayUpdateView, HolidayDeleteView, HolidayTablePartialView

urlpatterns = [
    # Main list view (full page load)
    path('holiday/', HolidayListView.as_view(), name='holiday_list'),
    
    # HTMX-specific endpoint for refreshing the table content
    path('holiday/table/', HolidayTablePartialView.as_view(), name='holiday_table'),
    
    # Endpoints for CRUD operations, primarily used by HTMX in modals
    path('holiday/add/', HolidayCreateView.as_view(), name='holiday_add'),
    path('holiday/edit/<int:pk>/', HolidayUpdateView.as_view(), name='holiday_edit'),
    path('holiday/delete/<int:pk>/', HolidayDeleteView.as_view(), name='holiday_delete'),
]

```

#### 4.6 Tests (`hr/tests.py`)

Comprehensive unit tests for the `Holiday` model and integration tests for all associated views are provided to ensure functionality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, datetime
from unittest.mock import MagicMock
from .models import Holiday

# Mock a user for views that require authentication or user details
class MockUser:
    def __init__(self, username, is_authenticated=True):
        self.username = username
        self.is_authenticated = is_authenticated

class HolidayModelTest(TestCase):
    """
    Unit tests for the Holiday model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all model tests
        Holiday.objects.create(
            id=1,
            title='Independence Day',
            holiday_date_str='15-08-2023',
            sys_date_str='15-08-2023',
            sys_time_str='10:30:00',
            session_id='testuser',
            company_id=1,
            financial_year_id=1
        )
        Holiday.objects.create(
            id=2,
            title='Republic Day',
            holiday_date_str='26-01-2024',
            sys_date_str='26-01-2024',
            sys_time_str='09:00:00',
            session_id='testuser',
            company_id=1,
            financial_year_id=1
        )
  
    def test_holiday_creation(self):
        """Tests that a Holiday instance is created correctly."""
        holiday = Holiday.objects.get(id=1)
        self.assertEqual(holiday.title, 'Independence Day')
        self.assertEqual(holiday.holiday_date_str, '15-08-2023')
        self.assertEqual(holiday.company_id, 1)
        self.assertEqual(holiday.financial_year_id, 1)
        
    def test_title_verbose_name(self):
        """Tests the verbose name of the 'title' field."""
        holiday = Holiday.objects.get(id=1)
        field_label = holiday._meta.get_field('title').verbose_name
        self.assertEqual(field_label, 'title') # Default if not explicitly set
        
    def test_holiday_date_property(self):
        """Tests the `holiday_date` property for correct string to date conversion."""
        holiday = Holiday.objects.get(id=1)
        self.assertEqual(holiday.holiday_date, date(2023, 8, 15))

    def test_holiday_date_property_setter(self):
        """Tests the `holiday_date` setter for correct date to string conversion."""
        holiday = Holiday.objects.get(id=1)
        new_date = date(2024, 1, 1)
        holiday.holiday_date = new_date
        self.assertEqual(holiday.holiday_date_str, '01-01-2024')

    def test_prepare_for_save(self):
        """Tests the `prepare_for_save` method for populating system fields."""
        holiday = Holiday(id=3, title='New Holiday Test')
        mock_user = MockUser(username='newuser')
        mock_company_id = 2
        mock_fin_year_id = 2
        
        holiday.prepare_for_save(mock_user, mock_company_id, mock_fin_year_id)
        
        self.assertEqual(holiday.session_id, 'newuser')
        self.assertEqual(holiday.company_id, 2)
        self.assertEqual(holiday.financial_year_id, 2)
        
        # Check date/time formats (cannot assert exact time as it's dynamic)
        self.assertTrue(datetime.strptime(holiday.sys_date_str, '%d-%m-%Y'))
        self.assertTrue(datetime.strptime(holiday.sys_time_str, '%H:%M:%S'))


class HolidayViewsTest(TestCase):
    """
    Integration tests for Holiday views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all view tests
        Holiday.objects.create(
            id=101,
            title='Christmas',
            holiday_date_str='25-12-2023',
            sys_date_str='20-12-2023',
            sys_time_str='14:00:00',
            session_id='admin_user',
            company_id=1,
            financial_year_id=1
        )
        Holiday.objects.create(
            id=102,
            title='New Year',
            holiday_date_str='01-01-2024',
            sys_date_str='20-12-2023',
            sys_time_str='14:00:00',
            session_id='admin_user',
            company_id=1,
            financial_year_id=1
        )
    
    def setUp(self):
        # Set up a test client for each test method
        self.client = Client()
        # Mock session variables that views might rely on
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 1
        session['username'] = 'testuser'
        session.save()

        # Mock the request.user for views that access it
        self.client.force_login(MockUser(username='testuser'))

    def test_list_view_get(self):
        """Tests the main Holiday list view (full page load)."""
        response = self.client.get(reverse('holiday_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/holiday/list.html')
        # Check for presence of the HTMX container for the table
        self.assertContains(response, 'id="holidayTable-container"')
        
    def test_table_partial_view_get(self):
        """Tests the HTMX partial view that renders only the table."""
        response = self.client.get(reverse('holiday_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/holiday/_holiday_table.html')
        self.assertTrue('holidays' in response.context)
        self.assertContains(response, 'Christmas')
        self.assertContains(response, 'New Year')
        # Ensure DataTables initialization script is present
        self.assertContains(response, '$(document).ready(function() {')

    def test_create_view_get_htmx(self):
        """Tests GET request for create view via HTMX (to load form in modal)."""
        response = self.client.get(reverse('holiday_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/holiday/_holiday_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success_htmx(self):
        """Tests successful POST request for create view via HTMX."""
        data = {
            'title': 'New Year Celebration',
            'holiday_date': '01-01-2025', # DD-MM-YYYY format
        }
        response = self.client.post(reverse('holiday_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX 'No Content' status for success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshHolidayList')
        self.assertTrue(Holiday.objects.filter(title='New Year Celebration', holiday_date_str='01-01-2025').exists())
        
    def test_create_view_post_invalid_htmx(self):
        """Tests invalid POST request for create view via HTMX (re-renders form with errors)."""
        data = {
            'title': '', # Invalid: required field
            'holiday_date': 'invalid-date-format', # Invalid date format
        }
        response = self.client.post(reverse('holiday_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Re-renders form with errors
        self.assertTemplateUsed(response, 'hr/holiday/_holiday_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Please enter date in DD-MM-YYYY format')

    def test_update_view_get_htmx(self):
        """Tests GET request for update view via HTMX."""
        holiday = Holiday.objects.get(id=101)
        response = self.client.get(reverse('holiday_edit', args=[holiday.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/holiday/_holiday_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.title, 'Christmas')
        self.assertEqual(response.context['form'].initial['holiday_date'], date(2023, 12, 25))

    def test_update_view_post_success_htmx(self):
        """Tests successful POST request for update view via HTMX."""
        holiday = Holiday.objects.get(id=101)
        data = {
            'title': 'Christmas Holiday Updated',
            'holiday_date': '25-12-2023',
        }
        response = self.client.post(reverse('holiday_edit', args=[holiday.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshHolidayList')
        holiday.refresh_from_db() # Refresh instance to get updated data
        self.assertEqual(holiday.title, 'Christmas Holiday Updated')
        self.assertEqual(holiday.holiday_date_str, '25-12-2023')

    def test_delete_view_get_htmx(self):
        """Tests GET request for delete view via HTMX (to load confirmation)."""
        holiday = Holiday.objects.get(id=101)
        response = self.client.get(reverse('holiday_delete', args=[holiday.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/holiday/_holiday_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].title, 'Christmas')

    def test_delete_view_post_success_htmx(self):
        """Tests successful POST request for delete view via HTMX."""
        holiday_to_delete = Holiday.objects.get(id=102)
        response = self.client.post(reverse('holiday_delete', args=[holiday_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshHolidayList')
        self.assertFalse(Holiday.objects.filter(id=102).exists())

```

### Step 5: HTMX and Alpine.js Integration

The core of the interactive experience comes from HTMX and Alpine.js:

*   **HTMX for Dynamic Updates:**
    *   The entire `holidayTable-container` in `list.html` uses `hx-trigger="load, refreshHolidayList from:body"` and `hx-get="{% url 'holiday_table' %}"` to load the table content dynamically on page load and whenever a `refreshHolidayList` custom event is triggered (after successful CRUD operations).
    *   "Add", "Edit", and "Delete" buttons use `hx-get` to fetch the respective forms (`_holiday_form.html`, `_holiday_confirm_delete.html`) into the `#modalContent` div.
    *   Form submissions (`hx-post`) in the modal use `hx-swap="none"` and trigger the `HX-Trigger` header (`refreshHolidayList`) from the server. This allows Django messages to flash on the main page while the table refreshes.
    *   The modal cancellation buttons use `_hyperscript` (`_="on click remove .is-active from #modal"`) to hide the modal without server interaction.
*   **Alpine.js for UI State:**
    *   Used simply with `_hyperscript` (which is tightly integrated with HTMX and Alpine.js concepts) to manage the modal's `hidden` class, showing it when `hx-get` is initiated and hiding it on cancel or click outside. For more complex UI state, full Alpine.js components would be defined.
*   **DataTables for List Views:**
    *   The `_holiday_table.html` partial includes a JavaScript snippet that initializes the `holidayTable` with DataTables, providing out-of-the-box search, sorting, and pagination. This script runs every time the partial is loaded by HTMX.
*   **Seamless Interaction:** All user interactions (adding, editing, deleting records) occur without full page reloads, providing a smooth and modern user experience. The use of `status=204` and `HX-Trigger` ensures that the browser's history is not cluttered and only the necessary parts of the page are updated.

### Final Notes

This plan provides a detailed, automated pathway for converting the ASP.NET Holiday Master module to Django. By following these guidelines, the organization can transition to a modern, maintainable, and scalable web application while significantly reducing the manual effort typically associated with such migrations. The emphasis on "fat models," "thin views," and exclusive use of HTMX/Alpine.js ensures a clean, efficient, and future-proof codebase.