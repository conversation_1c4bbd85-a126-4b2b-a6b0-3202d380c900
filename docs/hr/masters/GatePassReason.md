## ASP.NET to Django Conversion Script: Gate Pass Reason Management

This document outlines a strategic plan for modernizing your existing ASP.NET Gate Pass Reason application into a robust, scalable, and maintainable Django solution. Our approach prioritizes automation, leveraging conversational AI for seamless execution, and focuses on delivering tangible business benefits through modern web technologies.

**Business Value Proposition:**

Migrating to Django 5.0+ with HTMX and Alpine.js will transform your Gate Pass Reason management by:

*   **Boosting Efficiency:** Real-time data updates and interactive forms without full page reloads dramatically improve user experience and reduce operational friction.
*   **Reducing Maintenance Costs:** A clean, modular Django architecture with strict separation of concerns makes the application easier to understand, debug, and enhance, minimizing future development expenses.
*   **Enhancing Scalability:** Django's robust framework is designed to handle increasing data volumes and user traffic, future-proofing your application.
*   **Improving Data Integrity:** Django's ORM and forms provide built-in validation and security features, ensuring data accuracy and preventing common vulnerabilities.
*   **Future-Proofing Technology:** Adopting modern, open-source technologies like Django, HTMX, Alpine.js, and Tailwind CSS provides a highly flexible and adaptable foundation for future innovations.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is configured for Tailwind CSS.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the `SqlDataSource1` component in `GatePassReason.aspx`, we can clearly identify the database table and its structure.

*   **Table Name:** `tblGatePass_Reason`
*   **Columns:**
    *   `Id` (used as `DataKeyNames` and in `SelectCommand`, `UpdateCommand`, `DeleteCommand`) - inferred as an integer primary key.
    *   `Reason` (used in `SelectCommand`, `InsertCommand`, `UpdateCommand`) - inferred as a string.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

By analyzing the `SqlDataSource1` and `GridView1` event handlers:

*   **Create (Add):** Triggered by `btnInsert` in `GridView1`'s `FooterTemplate` (command "Add") or `EmptyDataTemplate` (command "Add1"). Handled by `GridView1_RowCommand` which uses `SqlDataSource1.Insert()`.
*   **Read (Retrieve):** Performed by `SqlDataSource1.SelectCommand` to populate `GridView1`.
*   **Update (Edit):** Initiated by `CommandField ShowEditButton`. Handled by `GridView1_RowUpdating` which contains manual SQL for updating `tblGatePass_Reason`.
*   **Delete:** Initiated by `CommandField ShowDeleteButton`. Handled by `GridView1_RowDeleted` and `SqlDataSource1.DeleteCommand`.
*   **Validation:** `RequiredFieldValidator` ensures the `Reason` field is not empty on insert/update.
*   **Business Logic:** `GridView1_RowDataBound` includes a specific rule: records with `Id` 19 or 33 have their edit and delete buttons hidden. This logic must be replicated in the Django model.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django component mapping.

**Instructions:**

*   **`GridView1`:** This highly interactive grid is the core UI component. It features pagination, sorting (implied by `SortExpression` on `TemplateField`), editing, deleting, and adding rows. This will be replaced by a combination of Django's `ListView` and a DataTables JavaScript component on the frontend, powered by HTMX for dynamic updates.
*   **`asp:TextBox` (`txtDescription1`, `txtDescription2`, `txtDescription3`):** Used for inputting the 'Reason'. These will map directly to `forms.TextInput` widgets in Django forms, styled with Tailwind CSS.
*   **`asp:Button` / `asp:LinkButton`:** Trigger actions like Insert, Edit, Delete. These will be converted to standard HTML `<button>` elements with HTMX attributes to trigger Django views and update page sections without full reloads.
*   **`asp:Label` (`lblMessage`):** Displays status messages. This will be handled by Django's `messages` framework, displayed via HTMX success/error responses.
*   **Client-side `PopUpMsg.js`:** The JavaScript calls like `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()` will be replaced by more modern and integrated modal confirmations using Alpine.js for UI state and HTMX for interaction.

---

### Step 4: Generate Django Code

We will create a new Django application, let's call it `hr_masters`, to house this module.

#### 4.1 Models (`hr_masters/models.py`)

**Task:** Create a Django model representing `tblGatePass_Reason` with integrated business logic.

**Instructions:**

The `GatePassReason` model will map to the existing `tblGatePass_Reason` table. We'll add a `can_be_modified` property to encapsulate the business rule identified in `GridView1_RowDataBound` (hiding edit/delete for specific IDs).

```python
from django.db import models

class GatePassReason(models.Model):
    # 'Id' is typically the default primary key in Django, so we map 'Id' to 'id'.
    # Django automatically creates an 'id' field if not specified.
    # We specify 'id' to explicitly map it to the existing 'Id' column if it's not autoincrementing.
    # If it's autoincrementing, Django handles it by default. Assuming 'Id' is the primary key.
    id = models.IntegerField(db_column='Id', primary_key=True)
    reason = models.CharField(db_column='Reason', max_length=255, unique=True, verbose_name="Gate Pass Reason") # Max_length inferred from common string field usage. Added unique constraint as it's a reason.

    class Meta:
        managed = False  # Important: Tells Django not to manage this table's schema
        db_table = 'tblGatePass_Reason' # The actual table name in your database
        verbose_name = 'Gate Pass Reason'
        verbose_name_plural = 'Gate Pass Reasons'
        ordering = ['-id'] # Matches original order by Id DESC

    def __str__(self):
        return self.reason
    
    @property
    def can_be_modified(self):
        """
        Business logic: Returns True if the record can be edited or deleted.
        Based on the ASP.NET code, IDs 19 and 33 cannot be modified.
        """
        return self.id not in [19, 33]

    def save(self, *args, **kwargs):
        """
        Custom save method to handle ID assignment for new objects if primary key is not auto-incrementing.
        If your 'Id' column in the database is an IDENTITY column (auto-incrementing),
        you can remove the `id = models.IntegerField(db_column='Id', primary_key=True)`
        and let Django manage the primary key automatically.
        For existing tables, it's safer to map it explicitly as above.
        If Id is auto-incrementing AND primary_key=True is set, Django will try to save new objects without an ID,
        allowing the DB to assign one.
        """
        super().save(*args, **kwargs)

```

#### 4.2 Forms (`hr_masters/forms.py`)

**Task:** Define a Django form for `GatePassReason` with appropriate styling and validation.

**Instructions:**

A `ModelForm` will be used to streamline creation and update operations. It will automatically handle validation based on the model field definitions.

```python
from django import forms
from .models import GatePassReason

class GatePassReasonForm(forms.ModelForm):
    class Meta:
        model = GatePassReason
        fields = ['reason'] # Only 'reason' is editable by the user
        widgets = {
            'reason': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter gate pass reason',
            }),
        }
        
    def clean_reason(self):
        """
        Custom validation for reason field if needed,
        e.g., to ensure uniqueness case-insensitively, or specific formats.
        Model already has unique=True.
        """
        reason = self.cleaned_data['reason']
        # Example of additional cleaning/validation:
        # if not reason.strip():
        #     raise forms.ValidationError("Reason cannot be empty.")
        return reason

```

#### 4.3 Views (`hr_masters/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views, keeping them thin and leveraging HTMX.

**Instructions:**

Views will be compact, primarily handling form instantiation, success/error messages, and HTMX responses. All business logic, including the `can_be_modified` check, resides in the model or is handled in the template.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from .models import GatePassReason
from .forms import GatePassReasonForm

# View for displaying the main list page with a container for the HTMX-loaded table
class GatePassReasonListView(ListView):
    model = GatePassReason
    template_name = 'hr_masters/gatepassreason/list.html'
    context_object_name = 'gatepass_reasons' # Renamed for clarity in template

    # This view primarily serves the base HTML structure.
    # The actual table data is loaded via HTMX into a partial view.

# View for serving the HTMX-loaded table partial
class GatePassReasonTablePartialView(ListView):
    model = GatePassReason
    template_name = 'hr_masters/gatepassreason/_gatepassreason_table.html'
    context_object_name = 'gatepass_reasons'

    # The list view's default queryset handles pagination and ordering.
    # DataTables will handle client-side filtering and sorting.

class GatePassReasonCreateView(CreateView):
    model = GatePassReason
    form_class = GatePassReasonForm
    template_name = 'hr_masters/gatepassreason/_gatepassreason_form.html' # Use partial template for modal
    success_url = reverse_lazy('gatepassreason_list') # Fallback, HTMX usually handles redirection

    def form_valid(self, form):
        # Business logic for saving can be moved to model.save() if more complex
        response = super().form_valid(form)
        messages.success(self.request, 'Gate Pass Reason added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX
                headers={'HX-Trigger': 'refreshGatePassReasonList'} # Custom event to refresh the table
            )
        return response # Fallback for non-HTMX requests

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # Return the form partial with errors for HTMX to swap in
            return response
        return response

class GatePassReasonUpdateView(UpdateView):
    model = GatePassReason
    form_class = GatePassReasonForm
    template_name = 'hr_masters/gatepassreason/_gatepassreason_form.html'
    context_object_name = 'gatepassreason' # For consistent template variable
    success_url = reverse_lazy('gatepassreason_list')

    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # Enforce business logic from ASP.NET: Cannot edit specific IDs
        if not obj.can_be_modified:
            # You might want to redirect to a different page or show an error
            messages.error(self.request, "This gate pass reason cannot be modified.")
            if self.request.headers.get('HX-Request'):
                return HttpResponse(status=403) # Forbidden
            raise Http404("This gate pass reason cannot be modified.")
        return obj

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Gate Pass Reason updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshGatePassReasonList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class GatePassReasonDeleteView(DeleteView):
    model = GatePassReason
    template_name = 'hr_masters/gatepassreason/_gatepassreason_confirm_delete.html'
    context_object_name = 'gatepassreason'
    success_url = reverse_lazy('gatepassreason_list')

    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # Enforce business logic from ASP.NET: Cannot delete specific IDs
        if not obj.can_be_modified:
            messages.error(self.request, "This gate pass reason cannot be deleted.")
            if self.request.headers.get('HX-Request'):
                return HttpResponse(status=403) # Forbidden
            raise Http404("This gate pass reason cannot be deleted.")
        return obj

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Gate Pass Reason deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshGatePassReasonList'}
            )
        return response

```

#### 4.4 Templates (`hr_masters/templates/hr_masters/gatepassreason/`)

**Task:** Create modular, HTMX-enabled templates for CRUD operations.

**Instructions:**

These templates will use Tailwind CSS for styling and HTMX for dynamic content loading, specifically for modals and table refreshes. `core/base.html` is assumed to contain all necessary CDN links for HTMX, Alpine.js, and DataTables.

**`list.html`**
This is the main page for the Gate Pass Reasons. It sets up the modal for forms and will load the actual table content via HTMX.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Gate Pass Reasons</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300"
            hx-get="{% url 'gatepassreason_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Reason
        </button>
    </div>
    
    <!-- Messages container for Django messages -->
    {% if messages %}
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 text-sm rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="gatepassreasonTable-container"
         hx-trigger="load, refreshGatePassReasonList from:body"
         hx-get="{% url 'gatepassreason_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Initial loading state -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Gate Pass Reasons...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center transition-opacity duration-300 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ open: false }" x-show="open" x-cloak>
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full transform transition-all duration-300 scale-95"
             @click.away="open = false"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            open: false,
            init() {
                // HTMX events to control modal visibility
                document.body.addEventListener('htmx:afterSwap', (event) => {
                    if (event.detail.target.id === 'modalContent') {
                        this.open = true; // Open modal after content is swapped
                    }
                });
                document.body.addEventListener('htmx:afterRequest', (event) => {
                    // Close modal after form submission (success or failure)
                    if (event.detail.elt.closest('#modalContent')) { // Check if the request came from inside the modal
                        const hxTrigger = event.detail.xhr.getResponseHeader('HX-Trigger');
                        if (hxTrigger && hxTrigger.includes('refreshGatePassReasonList')) {
                            this.open = false; // Close modal on successful operation
                        }
                    }
                });
                // Close modal if HTMX request fails or a 204 status (no content) is returned,
                // indicating a successful action that doesn't need to update the form.
                document.body.addEventListener('htmx:responseError', (event) => {
                    if (event.detail.xhr.status === 403) { // Forbidden, as handled in views
                        this.open = false;
                        alert(event.detail.xhr.responseText || "Operation not allowed.");
                    }
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`_gatepassreason_table.html`**
This partial template contains the DataTables structure and is loaded dynamically by HTMX.

```html
<table id="gatepassreasonTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in gatepass_reasons %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.reason }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                {% if obj.can_be_modified %}
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300"
                    hx-get="{% url 'gatepassreason_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-300"
                    hx-get="{% url 'gatepassreason_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
                {% else %}
                <span class="text-gray-400 text-xs">System Defined</span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-6 text-center text-gray-500">No gate pass reasons found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors on HTMX swap
        if ($.fn.DataTable.isDataTable('#gatepassreasonTable')) {
            $('#gatepassreasonTable').DataTable().destroy();
        }
        $('#gatepassreasonTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for items per page
            "responsive": true, // Make table responsive
            "searching": true, // Enable search box
            "ordering": true, // Enable column sorting
            "paging": true // Enable pagination
        });
    });
</script>
```

**`_gatepassreason_form.html`**
This partial renders the form for adding or editing a `GatePassReason` object within the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5 border-b pb-3">
        {% if form.instance.pk %}Edit Gate Pass Reason{% else %}Add New Gate Pass Reason{% endif %}
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300">
                <i class="fas fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

**`_gatepassreason_confirm_delete.html`**
This partial provides a confirmation prompt for deleting a `GatePassReason` object within the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Gate Pass Reason: "<span class="font-semibold">{{ gatepassreason.reason }}</span>"?
        This action cannot be undone.
    </p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'gatepassreason_delete' gatepassreason.pk %}" 
            hx-swap="none"
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300">
            <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`hr_masters/urls.py`)

**Task:** Define URL patterns for all `GatePassReason` views, including HTMX partials.

**Instructions:**

These URLs will route requests to the appropriate Django views. The `name` attributes are crucial for `reverse_lazy` in views and `url` tags in templates.

```python
from django.urls import path
from .views import (
    GatePassReasonListView, 
    GatePassReasonTablePartialView,
    GatePassReasonCreateView, 
    GatePassReasonUpdateView, 
    GatePassReasonDeleteView
)

urlpatterns = [
    path('gatepassreason/', GatePassReasonListView.as_view(), name='gatepassreason_list'),
    path('gatepassreason/table/', GatePassReasonTablePartialView.as_view(), name='gatepassreason_table'), # HTMX-specific for table refresh
    path('gatepassreason/add/', GatePassReasonCreateView.as_view(), name='gatepassreason_add'),
    path('gatepassreason/edit/<int:pk>/', GatePassReasonUpdateView.as_view(), name='gatepassreason_edit'),
    path('gatepassreason/delete/<int:pk>/', GatePassReasonDeleteView.as_view(), name='gatepassreason_delete'),
]

```

#### 4.6 Tests (`hr_masters/tests.py`)

**Task:** Write comprehensive unit tests for the model and integration tests for the views.

**Instructions:**

These tests ensure the correctness of the model's business logic and the views' functionality, including their interaction with HTMX.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from http import HTTPStatus

from .models import GatePassReason

class GatePassReasonModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        GatePassReason.objects.create(id=1, reason='Visiting Client')
        GatePassReason.objects.create(id=19, reason='System Default Reason 1')
        GatePassReason.objects.create(id=33, reason='System Default Reason 2')
        GatePassReason.objects.create(id=100, reason='Personal Work')
  
    def test_gate_pass_reason_creation(self):
        reason1 = GatePassReason.objects.get(id=1)
        reason100 = GatePassReason.objects.get(id=100)
        self.assertEqual(reason1.reason, 'Visiting Client')
        self.assertEqual(reason100.reason, 'Personal Work')
        self.assertEqual(GatePassReason.objects.count(), 4) # Verify count after setup

    def test_reason_label(self):
        reason = GatePassReason.objects.get(id=1)
        field_label = reason._meta.get_field('reason').verbose_name
        self.assertEqual(field_label, 'Gate Pass Reason')
        
    def test_can_be_modified_property(self):
        # Test editable reasons
        reason1 = GatePassReason.objects.get(id=1)
        reason100 = GatePassReason.objects.get(id=100)
        self.assertTrue(reason1.can_be_modified)
        self.assertTrue(reason100.can_be_modified)

        # Test non-editable reasons (as per ASP.NET logic)
        reason19 = GatePassReason.objects.get(id=19)
        reason33 = GatePassReason.objects.get(id=33)
        self.assertFalse(reason19.can_be_modified)
        self.assertFalse(reason33.can_be_modified)

    def test_str_method(self):
        reason = GatePassReason.objects.get(id=1)
        self.assertEqual(str(reason), 'Visiting Client')


class GatePassReasonViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        GatePassReason.objects.create(id=10, reason='Medical Appointment')
        GatePassReason.objects.create(id=19, reason='System Reason 1') # Non-editable
        GatePassReason.objects.create(id=33, reason='System Reason 2') # Non-editable
        GatePassReason.objects.create(id=20, reason='Client Meeting')

    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('gatepassreason_list'))
        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertTemplateUsed(response, 'hr_masters/gatepassreason/list.html')
        self.assertIn('gatepass_reasons', response.context)
        self.assertEqual(len(response.context['gatepass_reasons']), 4) # All objects

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('gatepassreason_table'))
        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertTemplateUsed(response, 'hr_masters/gatepassreason/_gatepassreason_table.html')
        self.assertIn('gatepass_reasons', response.context)
        self.assertEqual(len(response.context['gatepass_reasons']), 4)
        self.assertContains(response, 'Medical Appointment')
        self.assertContains(response, 'Client Meeting')

    def test_create_view_get(self):
        response = self.client.get(reverse('gatepassreason_add'))
        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertTemplateUsed(response, 'hr_masters/gatepassreason/_gatepassreason_form.html')
        self.assertIn('form', response.context)
        self.assertIsNone(response.context['form'].instance.pk) # Ensure it's an add form

    def test_create_view_post_success(self):
        new_id = GatePassReason.objects.latest('id').id + 1 # Simulate next available ID
        data = {'id': new_id, 'reason': 'New Reason for Testing'} # Explicitly pass ID
        response = self.client.post(reverse('gatepassreason_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, HTTPStatus.NO_CONTENT) # HTMX success status
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshGatePassReasonList')
        
        # Verify object was created
        self.assertTrue(GatePassReason.objects.filter(reason='New Reason for Testing').exists())
        self.assertEqual(GatePassReason.objects.count(), 5) # 4 existing + 1 new
        
        # Check messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Gate Pass Reason added successfully.')

    def test_create_view_post_invalid(self):
        data = {'id': 999, 'reason': ''} # Invalid data (empty reason)
        response = self.client.post(reverse('gatepassreason_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, HTTPStatus.OK) # HTMX swaps back form with errors
        self.assertContains(response, 'This field is required.') # Check for validation error message
        self.assertEqual(GatePassReason.objects.count(), 4) # No new object created
        
    def test_update_view_get(self):
        obj = GatePassReason.objects.get(id=10)
        response = self.client.get(reverse('gatepassreason_edit', args=[obj.id]))
        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertTemplateUsed(response, 'hr_masters/gatepassreason/_gatepassreason_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success(self):
        obj = GatePassReason.objects.get(id=10)
        data = {'reason': 'Updated Medical Appointment'}
        response = self.client.post(reverse('gatepassreason_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, HTTPStatus.NO_CONTENT)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshGatePassReasonList')
        
        obj.refresh_from_db()
        self.assertEqual(obj.reason, 'Updated Medical Appointment')
        self.assertEqual(GatePassReason.objects.count(), 4) # Count should remain the same

    def test_update_view_post_invalid(self):
        obj = GatePassReason.objects.get(id=10)
        data = {'reason': ''} # Invalid data (empty reason)
        response = self.client.post(reverse('gatepassreason_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertContains(response, 'This field is required.')
        
        obj.refresh_from_db()
        self.assertNotEqual(obj.reason, '') # Ensure it wasn't updated

    def test_update_view_post_forbidden(self):
        obj = GatePassReason.objects.get(id=19) # Non-editable ID
        data = {'reason': 'Attempt to update system reason'}
        response = self.client.post(reverse('gatepassreason_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, HTTPStatus.FORBIDDEN) # Returns 403
        self.assertFalse('HX-Trigger' in response.headers) # No trigger on forbidden

        obj.refresh_from_db()
        self.assertNotEqual(obj.reason, 'Attempt to update system reason') # Ensure no update happened

    def test_delete_view_get(self):
        obj = GatePassReason.objects.get(id=20)
        response = self.client.get(reverse('gatepassreason_delete', args=[obj.id]))
        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertTemplateUsed(response, 'hr_masters/gatepassreason/_gatepassreason_confirm_delete.html')
        self.assertIn('gatepassreason', response.context)
        self.assertEqual(response.context['gatepassreason'], obj)
        
    def test_delete_view_post_success(self):
        obj_to_delete = GatePassReason.objects.get(id=20)
        response = self.client.post(reverse('gatepassreason_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, HTTPStatus.NO_CONTENT)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshGatePassReasonList')
        
        self.assertFalse(GatePassReason.objects.filter(id=20).exists()) # Verify object is deleted
        self.assertEqual(GatePassReason.objects.count(), 3) # Count decreased
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Gate Pass Reason deleted successfully.')

    def test_delete_view_post_forbidden(self):
        obj_to_delete = GatePassReason.objects.get(id=19) # Non-deletable ID
        response = self.client.post(reverse('gatepassreason_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, HTTPStatus.FORBIDDEN)
        self.assertTrue(GatePassReason.objects.filter(id=19).exists()) # Verify object still exists

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided templates and views already incorporate the core principles:

*   **HTMX for dynamic updates:**
    *   `hx-get` is used to load forms into the modal and to refresh the data table.
    *   `hx-post` is used for form submissions and delete confirmations.
    *   `hx-swap="innerHTML"` is used for updating the modal content or the table container.
    *   `hx-swap="none"` and `hx-trigger` are used for successful form submissions to close the modal and trigger a table refresh.
*   **Alpine.js for UI state management:**
    *   `x-data="{ open: false }"` on the modal div manages its visibility.
    *   `_="on click add .is-active to #modal"` and `_="on click remove .is-active from me"` provide declarative control over modal display based on HTMX interactions and user clicks.
    *   `@click.away` on the modal content ensures clicking outside closes the modal.
    *   Alpine.js listens to HTMX events (`htmx:afterSwap`, `htmx:afterRequest`) to manage the `open` state of the modal and messages.
*   **DataTables for list views:**
    *   The `_gatepassreason_table.html` partial initializes DataTables on the rendered table, providing out-of-the-box search, sort, and pagination.
    *   The JavaScript within this partial ensures DataTables is correctly re-initialized after HTMX swaps the table content.
*   **No full page reloads:** All CRUD operations and list refreshes happen asynchronously via HTMX, providing a smooth, app-like experience.

---

## Final Notes

*   **Database Connection:** Ensure your `settings.py` is configured to connect to your existing ASP.NET database using the appropriate database backend (e.g., `mssql` for SQL Server, `psycopg2` for PostgreSQL, etc.). Remember to specify the `ENGINE` and `NAME` (database name), `USER`, `PASSWORD`, `HOST`, `PORT` as per your `LocalSqlServer` connection string.
*   **Project Structure:** This plan assumes you'll integrate this `hr_masters` app into an existing Django project. Remember to add `hr_masters` to `INSTALLED_APPS` in `settings.py` and include its URLs in your project's `urls.py`.
*   **Static Files:** Ensure your Tailwind CSS setup is complete and your static files are correctly served in Django.
*   **Security:** Implement Django's built-in security features, such as CSRF protection (automatically handled by `{% csrf_token %}` in forms) and proper input validation.
*   **Error Handling:** The current views provide basic error handling for `form_invalid` and forbidden actions. Consider more robust error logging and user-friendly error messages as needed.
*   **Extensibility:** The "Fat Model, Thin View" approach makes it easy to add more complex business logic to your `GatePassReason` model without bloating your views.

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Gate Pass Reason module to a modern Django application, setting the stage for future scalability and ease of maintenance.