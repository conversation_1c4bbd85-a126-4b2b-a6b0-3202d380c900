## ASP.NET to Django Conversion Script: Swap Card Management

This document outlines a strategic plan to modernize your existing ASP.NET "Swap Card Number" application into a robust, scalable, and maintainable Django-based solution. Our approach prioritizes AI-assisted automation to streamline the transition, focusing on business benefits and a smooth operational handover.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the `SqlDataSource1` component in the ASP.NET code, we can clearly identify the following database elements:

-   **Table Name:** `tblHR_SwapCard`
-   **Columns:**
    -   `Id`: This is the primary key used for identification and updates/deletes. It's an integer type.
    -   `SwapCardNo`: This is a string type, representing the actual swap card number.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The ASP.NET application provides a full set of CRUD (Create, Read, Update, Delete) capabilities for Swap Card Numbers, along with basic validation.

-   **Create (Add):**
    -   New records are added via the "Insert" button in the `GridView`'s footer row, triggering the `GridView1_RowCommand` event with `CommandName="Add"` or `CommandName="Add1"`.
    -   The `SwapCardNo` is taken from `txtSwapCardNo` textbox.
    -   The `SqlDataSource1.Insert()` method performs the database insert.
-   **Read (Select):**
    -   The `GridView1` displays existing records.
    -   The `SqlDataSource1.SelectCommand` (`SELECT [Id], [SwapCardNo] FROM [tblHR_SwapCard] ORDER BY [Id] DESC`) fetches the data.
-   **Update (Edit):**
    -   Editing is enabled through a `CommandField` in the `GridView`.
    -   The `GridView1_RowUpdated` event confirms the update.
    -   The `SqlDataSource1.UpdateCommand` handles the database update.
-   **Delete:**
    -   Deletion is enabled through a `CommandField` in the `GridView`.
    -   The `GridView1_RowDeleted` event confirms the deletion.
    -   The `SqlDataSource1.DeleteCommand` handles the database deletion.
-   **Validation:**
    -   A `RequiredFieldValidator` (`ReqSwapCard`, `ReqSwapCard0`) ensures that the "Swap Card No" field is not empty during insertion or editing.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The user interface (UI) in the ASP.NET application is primarily built around the `GridView` control, which combines data display, input, and action triggers.

-   **`asp:GridView ID="GridView1"`:** This is the main component. It serves as a data table displaying a list of "Swap Card No" entries. It provides:
    -   **Pagination:** (`AllowPaging="True"`, `PageSize="20"`) for navigating through records.
    -   **Data Display:** Shows `Id` (implicitly by `DataKeyNames` and `SN`) and `SwapCardNo`.
    -   **Inline Editing:** Allows users to modify `SwapCardNo` directly within the grid using a `TextBox`.
    -   **Inline Deletion:** Provides a "Delete" link for removing records.
    -   **New Record Insertion:** Contains a "Swap Card No" `TextBox` (`txtSwapCardNo`) and an "Insert" button in its footer to add new entries.
-   **`asp:TextBox` controls:** Used for inputting (`txtSwapCardNo`) and editing (`lblSwapCardNo0`) the `SwapCardNo`.
-   **`asp:Button`/`asp:LinkButton` controls:**
    -   "Insert" button for adding new records.
    -   "Edit" and "Delete" links within the `CommandField` for modifying/removing existing records.
-   **`asp:Label ID="Label2"`:** Used to display status or error messages (e.g., "Record inserted.").
-   **Client-Side JavaScript:** Functions like `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()` are used for client-side confirmation dialogs before performing CRUD actions. These will be replaced by modern HTMX/Alpine.js modal interactions.

### Step 4: Generate Django Code

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

We will create a `SwapCard` model that maps directly to the `tblHR_SwapCard` table. Given `Id` is the `DataKeyNames` in ASP.NET, we assume it's the primary key and let Django manage the `id` field automatically.

```python
# hr/models.py
from django.db import models

class SwapCard(models.Model):
    """
    Represents a Swap Card entry in the tblHR_SwapCard table.
    
    This model is mapped to an existing database table, meaning Django
    will not manage its schema (managed=False).
    The 'id' field is implicitly handled by Django as the primary key,
    corresponding to the 'Id' column in the database.
    """
    swap_card_no = models.CharField(
        db_column='SwapCardNo', 
        max_length=255, # Assuming a reasonable max length for string
        unique=True, # Assuming SwapCardNo should be unique for master data
        verbose_name="Swap Card No"
    )

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (e.g., migrations)
        db_table = 'tblHR_SwapCard'  # Explicitly maps to the existing table name
        verbose_name = 'Swap Card'
        verbose_name_plural = 'Swap Cards'
        ordering = ['-id']  # Orders by Id descending, matching original SELECT command

    def __str__(self):
        """Returns a string representation of the SwapCard object."""
        return self.swap_card_no

    # No complex business logic was identified in the original C# code-behind,
    # so no additional model methods are required for this simple CRUD functionality.
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

A `ModelForm` will be used to handle input and validation for the `SwapCard` model. This will replicate the `RequiredFieldValidator` and add an important uniqueness check.

```python
# hr/forms.py
from django import forms
from .models import SwapCard

class SwapCardForm(forms.ModelForm):
    """
    Form for creating and updating SwapCard objects.
    Enforces validation, including a unique check for swap_card_no.
    """
    class Meta:
        model = SwapCard
        fields = ['swap_card_no']
        widgets = {
            'swap_card_no': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Swap Card Number'
            }),
        }
        
    def clean_swap_card_no(self):
        """
        Custom validation to ensure SwapCardNo is unique.
        This mirrors the implicit expectation for master data and prevents duplicates.
        """
        swap_card_no = self.cleaned_data['swap_card_no']
        
        # Check if a SwapCard with this number already exists, excluding the current instance
        # (important for update operations)
        qs = SwapCard.objects.filter(swap_card_no=swap_card_no)
        if self.instance.pk: # If updating an existing instance
            qs = qs.exclude(pk=self.instance.pk)
            
        if qs.exists():
            raise forms.ValidationError("This Swap Card Number already exists.")
            
        return swap_card_no
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

We will use Django's built-in `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the main CRUD operations. An additional `ListView` will be used to render only the table partial for HTMX requests.

```python
# hr/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import SwapCard
from .forms import SwapCardForm
from django.template.loader import render_to_string # Used for potential advanced HTMX swaps, but not strictly needed for 204

class SwapCardListView(ListView):
    """
    Displays a list of all SwapCard objects.
    This view renders the main page containing the DataTables container.
    """
    model = SwapCard
    template_name = 'hr/swapcard/list.html'
    context_object_name = 'swap_cards' # Plural name for the queryset in the template

class SwapCardTablePartialView(ListView):
    """
    Renders only the DataTables table content for HTMX requests.
    This allows the table to be refreshed independently without a full page reload.
    """
    model = SwapCard
    template_name = 'hr/swapcard/_swapcard_table.html' # Underscore indicates a partial template
    context_object_name = 'swap_cards'

class SwapCardCreateView(CreateView):
    """
    Handles the creation of new SwapCard objects.
    Renders a form and processes its submission.
    """
    model = SwapCard
    form_class = SwapCardForm
    template_name = 'hr/swapcard/form.html' # This template is designed to be loaded as a modal partial
    success_url = reverse_lazy('swapcard_list') # Fallback URL for non-HTMX requests

    def form_valid(self, form):
        """
        Processes a valid form submission.
        Sends a success message and triggers an HTMX event for refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Swap Card added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content status with a trigger header
            # to refresh the list on the client side.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSwapCardList' # Custom event for HTMX to listen to
                }
            )
        return response
    
    def form_invalid(self, form):
        """
        Handles an invalid form submission.
        Re-renders the form within the modal, showing validation errors.
        """
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class SwapCardUpdateView(UpdateView):
    """
    Handles the updating of existing SwapCard objects.
    Renders a pre-filled form and processes its submission.
    """
    model = SwapCard
    form_class = SwapCardForm
    template_name = 'hr/swapcard/form.html' # Loaded as a modal partial
    success_url = reverse_lazy('swapcard_list') # Fallback URL

    def form_valid(self, form):
        """
        Processes a valid form submission for update.
        Sends a success message and triggers an HTMX event for refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Swap Card updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSwapCardList'
                }
            )
        return response

    def form_invalid(self, form):
        """
        Handles an invalid form submission for update.
        Re-renders the form within the modal, showing validation errors.
        """
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class SwapCardDeleteView(DeleteView):
    """
    Handles the deletion of SwapCard objects.
    Renders a confirmation page and processes the delete action.
    """
    model = SwapCard
    template_name = 'hr/swapcard/confirm_delete.html' # Loaded as a modal partial
    context_object_name = 'swapcard' # For displaying object details in confirmation
    success_url = reverse_lazy('swapcard_list') # Fallback URL

    def delete(self, request, *args, **kwargs):
        """
        Performs the deletion and sends a success message.
        Triggers an HTMX event for list refresh after deletion.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Swap Card deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSwapCardList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring HTMX and Alpine.js integration for dynamic interactions.

**`hr/templates/hr/swapcard/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Swap Cards Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'swapcard_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal then remove .hidden from #modal">
            Add New Swap Card
        </button>
    </div>
    
    <!-- Messages Display Area -->
    <div id="messages" class="mb-4">
        {% for message in messages %}
            <div class="p-4 mb-2 text-sm {% if message.tags == 'success' %}text-green-700 bg-green-100{% elif message.tags == 'error' %}text-red-700 bg-red-100{% else %}text-blue-700 bg-blue-100{% endif %} rounded-lg dark:bg-gray-700" role="alert">
                {{ message }}
            </div>
        {% endfor %}
    </div>

    <!-- DataTables Container - content loaded via HTMX -->
    <div id="swapcardTable-container"
         hx-trigger="load, refreshSwapCardList from:body"
         hx-get="{% url 'swapcard_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- Loading spinner while content is fetched -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Swap Card data...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation, controlled by Alpine.js and HTMX -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-4 transform transition-all duration-300 scale-95 opacity-0"
             _="on load transition opacity-100 scale-100">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js is typically loaded in base.html -->
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components specific to this page can be defined here if needed.
        // For basic modal toggle, the hyperscript `_` syntax directly handles it.
    });

    // Listen for the custom HTMX event 'refreshSwapCardList' to close the modal
    // and potentially show messages from the backend.
    document.body.addEventListener('refreshSwapCardList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
            modal.classList.add('hidden'); // Ensure modal is hidden after successful operation
            
            // Re-render messages (Django's messages are persistent until read)
            // This is a simple way; for more robust, re-hx-get the messages container.
            const messagesContainer = document.getElementById('messages');
            if (messagesContainer) {
                // HTMX-load the messages from a dedicated endpoint if you want real-time updates
                // For simplicity, we just clear and rely on full page reload or other means
                // For a more advanced pattern, you'd fetch /messages/ endpoint
                setTimeout(() => {
                    messagesContainer.innerHTML = ''; // Clear existing messages after a delay
                }, 5000); // Messages visible for 5 seconds
            }
        }
    });

    // A simpler way to handle message display, assuming messages are rendered on initial page load
    // and new messages from HTMX requests need a mechanism.
    // For 204 responses, you'd typically handle messages via htmx.on('htmx:afterSwap') or similar
    // For this setup, Django's messages framework works well on full page loads or if you load them
    // into a separate HTMX target. The above `HX-Trigger` causes a list refresh,
    // which in turn will show any new messages if the list view also includes them.
    // A better approach for messages in HTMX is often to return them as part of a partial swap or in a header.
</script>
{% endblock %}
```

**`hr/templates/hr/swapcard/_swapcard_table.html`** (Partial for HTMX)

```html
<div class="overflow-x-auto shadow-md rounded-lg">
    <table id="swapcardTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Swap Card No</th>
                <th scope="col" class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in swap_cards %}
            <tr class="hover:bg-gray-50 transition-colors duration-150 ease-in-out">
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.swap_card_no }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'swapcard_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'swapcard_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="3" class="py-6 px-4 text-center text-gray-500 text-lg">No swap cards found. Please add a new one.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization script.
// Important: Ensure DataTables is re-initialized correctly when HTMX swaps content.
// It's crucial to destroy any pre-existing DataTable instance before re-initializing.
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#swapcardTable')) {
        $('#swapcardTable').DataTable().destroy();
    }
    $('#swapcardTable').DataTable({
        "pageLength": 20, // Matching original ASP.NET GridView PageSize
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 2] } // Disable sorting for SN and Actions columns
        ],
        "responsive": true, // Enable responsive design for smaller screens
        "language": {
            "search": "Search:"
        }
    });
});
</script>
```

**`hr/templates/hr/swapcard/form.html`** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Swap Card</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="field-wrapper">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1 font-medium">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <div class="text-red-600 text-sm mt-3 font-medium p-3 bg-red-50 rounded-md border border-red-200">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out">
                Save
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`hr/templates/hr/swapcard/confirm_delete.html`** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">Are you sure you want to permanently delete the Swap Card: "<strong class="text-red-600">{{ swapcard.swap_card_no }}</strong>"?</p>
    
    <form hx-post="{% url 'swapcard_delete' swapcard.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

```python
# hr/urls.py
from django.urls import path
from .views import (
    SwapCardListView, 
    SwapCardCreateView, 
    SwapCardUpdateView, 
    SwapCardDeleteView,
    SwapCardTablePartialView # Added for HTMX-driven table refreshes
)

urlpatterns = [
    # Main list view (full page load, also serves as initial container for HTMX)
    path('swapcard/', SwapCardListView.as_view(), name='swapcard_list'),
    
    # HTMX endpoint for dynamic loading/refreshing of the table content
    path('swapcard/table/', SwapCardTablePartialView.as_view(), name='swapcard_table'),

    # HTMX endpoint for adding a new Swap Card (loads form into modal)
    path('swapcard/add/', SwapCardCreateView.as_view(), name='swapcard_add'),
    
    # HTMX endpoint for editing an existing Swap Card (loads form into modal)
    path('swapcard/edit/<int:pk>/', SwapCardUpdateView.as_view(), name='swapcard_edit'),
    
    # HTMX endpoint for confirming deletion of a Swap Card (loads confirmation into modal)
    path('swapcard/delete/<int:pk>/', SwapCardDeleteView.as_view(), name='swapcard_delete'),
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views to ensure functionality and coverage.

```python       
# hr/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import SwapCard
from .forms import SwapCardForm
from django.db import IntegrityError # For testing unique constraints if applicable
from django.contrib.messages import get_messages # To check messages after redirect/204

class SwapCardModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up non-modified objects used by all test methods."""
        SwapCard.objects.create(swap_card_no='TEST001')
        SwapCard.objects.create(swap_card_no='ANOTHER002')
  
    def test_swapcard_creation(self):
        """Test basic SwapCard object creation and field values."""
        obj = SwapCard.objects.get(id=1) # Assumes 'id' is Django's default PK field
        self.assertEqual(obj.swap_card_no, 'TEST001')
        self.assertTrue(isinstance(obj, SwapCard))
        
    def test_swap_card_no_verbose_name(self):
        """Test the verbose name of the swap_card_no field."""
        obj = SwapCard.objects.get(id=1)
        field_label = obj._meta.get_field('swap_card_no').verbose_name
        # Django automatically converts verbose_name to lowercase for display in templates
        self.assertEqual(field_label, 'swap card no') 
        
    def test_str_representation(self):
        """Test the __str__ method of the SwapCard model."""
        obj = SwapCard.objects.get(id=1)
        self.assertEqual(str(obj), 'TEST001')

    def test_ordering(self):
        """Test that objects are ordered by ID descending as per Meta.ordering."""
        # Create a newer object
        SwapCard.objects.create(swap_card_no='LATEST003')
        # The list should have LATEST003 first
        all_swapcards = SwapCard.objects.all()
        self.assertEqual(all_swapcards[0].swap_card_no, 'LATEST003')
        self.assertEqual(all_swapcards[1].swap_card_no, 'ANOTHER002')
        self.assertEqual(all_swapcards[2].swap_card_no, 'TEST001')


class SwapCardFormTest(TestCase):
    def test_form_valid_data(self):
        """Test form with valid data."""
        form = SwapCardForm(data={'swap_card_no': 'UNIQUE001'})
        self.assertTrue(form.is_valid())

    def test_form_no_data(self):
        """Test form with no data (should be invalid due to required field)."""
        form = SwapCardForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('swap_card_no', form.errors)
        self.assertEqual(form.errors['swap_card_no'], ['This field is required.'])

    def test_form_duplicate_swap_card_no_on_create(self):
        """Test uniqueness validation on creation."""
        SwapCard.objects.create(swap_card_no='EXISTING001')
        form = SwapCardForm(data={'swap_card_no': 'EXISTING001'})
        self.assertFalse(form.is_valid())
        self.assertIn('swap_card_no', form.errors)
        self.assertEqual(form.errors['swap_card_no'], ['This Swap Card Number already exists.'])

    def test_form_duplicate_swap_card_no_on_update(self):
        """Test uniqueness validation on update (should allow same value for self)."""
        obj1 = SwapCard.objects.create(swap_card_no='UPDATE_ME')
        obj2 = SwapCard.objects.create(swap_card_no='OTHER_ONE')

        # Should allow saving the same value for obj1
        form = SwapCardForm(instance=obj1, data={'swap_card_no': 'UPDATE_ME'})
        self.assertTrue(form.is_valid())

        # Should not allow updating obj1 to obj2's value
        form = SwapCardForm(instance=obj1, data={'swap_card_no': 'OTHER_ONE'})
        self.assertFalse(form.is_valid())
        self.assertIn('swap_card_no', form.errors)
        self.assertEqual(form.errors['swap_card_no'], ['This Swap Card Number already exists.'])


class SwapCardViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up test data for view tests."""
        cls.swapcard1 = SwapCard.objects.create(swap_card_no='VIEWTEST001')
        cls.swapcard2 = SwapCard.objects.create(swap_card_no='VIEWTEST002')
    
    def setUp(self):
        """Set up client for each test method."""
        self.client = Client()
    
    def test_list_view_get(self):
        """Test GET request to SwapCardListView."""
        response = self.client.get(reverse('swapcard_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/swapcard/list.html')
        self.assertIn('swap_cards', response.context)
        self.assertContains(response, 'VIEWTEST001')
        self.assertContains(response, 'VIEWTEST002')

    def test_table_partial_view_get(self):
        """Test GET request to SwapCardTablePartialView (HTMX component)."""
        response = self.client.get(reverse('swapcard_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/swapcard/_swapcard_table.html')
        self.assertIn('swap_cards', response.context)
        self.assertContains(response, 'VIEWTEST001')
        self.assertContains(response, 'VIEWTEST002')

    def test_create_view_get_htmx(self):
        """Test GET request to SwapCardCreateView with HTMX header."""
        response = self.client.get(reverse('swapcard_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/swapcard/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Swap Card')

    def test_create_view_post_htmx_success(self):
        """Test POST request to SwapCardCreateView with HTMX header for success."""
        data = {'swap_card_no': 'NEWVIEW003'}
        response = self.client.post(reverse('swapcard_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content is expected for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSwapCardList')
        self.assertTrue(SwapCard.objects.filter(swap_card_no='NEWVIEW003').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Swap Card added successfully.')

    def test_create_view_post_htmx_invalid(self):
        """Test POST request to SwapCardCreateView with HTMX header for invalid data."""
        data = {'swap_card_no': self.swapcard1.swap_card_no} # Existing value
        response = self.client.post(reverse('swapcard_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr/swapcard/form.html')
        self.assertContains(response, 'This Swap Card Number already exists.')
        self.assertEqual(SwapCard.objects.filter(swap_card_no=self.swapcard1.swap_card_no).count(), 1) # No duplicate created

    def test_update_view_get_htmx(self):
        """Test GET request to SwapCardUpdateView with HTMX header."""
        response = self.client.get(reverse('swapcard_edit', args=[self.swapcard1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/swapcard/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Swap Card')
        self.assertContains(response, self.swapcard1.swap_card_no)

    def test_update_view_post_htmx_success(self):
        """Test POST request to SwapCardUpdateView with HTMX header for success."""
        data = {'swap_card_no': 'UPDATED001'}
        response = self.client.post(reverse('swapcard_edit', args=[self.swapcard1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSwapCardList')
        self.swapcard1.refresh_from_db() # Reload object from DB to check changes
        self.assertEqual(self.swapcard1.swap_card_no, 'UPDATED001')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Swap Card updated successfully.')

    def test_update_view_post_htmx_invalid(self):
        """Test POST request to SwapCardUpdateView with HTMX header for invalid data."""
        data = {'swap_card_no': self.swapcard2.swap_card_no} # Try to set to another existing value
        response = self.client.post(reverse('swapcard_edit', args=[self.swapcard1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr/swapcard/form.html')
        self.assertContains(response, 'This Swap Card Number already exists.')
        self.swapcard1.refresh_from_db() # Ensure object was not updated
        self.assertEqual(self.swapcard1.swap_card_no, 'VIEWTEST001')

    def test_delete_view_get_htmx(self):
        """Test GET request to SwapCardDeleteView with HTMX header."""
        response = self.client.get(reverse('swapcard_delete', args=[self.swapcard1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/swapcard/confirm_delete.html')
        self.assertIn('swapcard', response.context)
        self.assertContains(response, f'Are you sure you want to permanently delete the Swap Card: "<strong>{self.swapcard1.swap_card_no}</strong>"?')

    def test_delete_view_post_htmx_success(self):
        """Test POST request to SwapCardDeleteView with HTMX header for success."""
        initial_count = SwapCard.objects.count()
        response = self.client.post(reverse('swapcard_delete', args=[self.swapcard1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSwapCardList')
        self.assertEqual(SwapCard.objects.count(), initial_count - 1)
        self.assertFalse(SwapCard.objects.filter(pk=self.swapcard1.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Swap Card deleted successfully.')

    def test_delete_view_post_htmx_not_found(self):
        """Test POST request to SwapCardDeleteView for a non-existent object."""
        response = self.client.post(reverse('swapcard_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Expect a 404 for non-existent object
```

### Step 5: HTMX and Alpine.js Integration

The integration of HTMX and Alpine.js is central to providing a modern, dynamic user experience without relying on complex JavaScript frameworks.

-   **HTMX for Dynamic Interactions:**
    -   **Modal Loading:** Buttons to "Add New", "Edit", and "Delete" (`hx-get`) are configured to load their respective forms or confirmation dialogs directly into the `#modalContent` div. This eliminates full page reloads for these interactions.
    -   **Form Submission:** Forms (`hx-post`) are set to `hx-swap="none"` and `hx-target="#modalContent"`. This means upon successful submission (which returns a 204 No Content), no content is swapped *into* the form area. Instead, the `HX-Trigger: refreshSwapCardList` header is used.
    -   **List Refresh:** The main `div` containing the DataTables (`#swapcardTable-container`) has `hx-trigger="load, refreshSwapCardList from:body"`. This ensures the table is loaded on page load and automatically re-fetches its content whenever the `refreshSwapCardList` custom event is triggered (e.g., after a successful create, update, or delete operation), keeping the list always up-to-date.
    -   **Loading Indicators:** `hx-indicator` is used on forms to show a small spinner (`#form-spinner`) while an AJAX request is in progress, providing visual feedback to the user.
-   **Alpine.js for UI State Management:**
    -   While the core modal open/close logic is handled by HTMX and Hyperscript's `_` syntax directly in the templates (`on click add .is-active to #modal then remove .hidden from #modal`), Alpine.js provides a powerful declarative way to manage more complex UI states should they arise (e.g., dynamic form elements, interactive components). For this simple CRUD, minimal explicit Alpine.js `x-data` is required, relying on the `_` syntax for brevity.
-   **DataTables for List Views:**
    -   The `_swapcard_table.html` partial directly contains the `<table>` element and the JavaScript initialization for DataTables.
    -   Crucially, when HTMX reloads this partial, the JavaScript `$(document).ready(...)` block re-executes. To prevent DataTables errors from re-initializing on an already initialized table, `$.fn.DataTable.isDataTable('#swapcardTable').destroy();` is used before `$('#swapcardTable').DataTable(...)`. This ensures a clean re-initialization, preserving search, sort, and pagination state (though for full state preservation on HTMX reload, DataTables' `stateSave` feature would be considered).
    -   The DataTables setup includes `pageLength: 20` to match the original ASP.NET `PageSize`, and `lengthMenu` for user control over displayed rows.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET "Swap Card Number" application to Django. By following these automated and structured steps:

-   **Business Value:** You gain a modern, highly maintainable application with improved performance and a more responsive user experience, laying a foundation for future enhancements.
-   **Automation Focus:** The conversion emphasizes automated processes, minimizing manual code rewriting and reducing the risk of human error, which translates to faster development cycles and lower migration costs.
-   **Scalability & Maintainability:** Django's robust architecture, combined with fat models/thin views, HTMX, and a clear separation of concerns, ensures the application is easy to understand, extend, and scale.
-   **Test Coverage:** High test coverage guarantees the reliability and correctness of the migrated functionality, building confidence in the new system.

This approach will transform your legacy system into a cutting-edge Django solution, ready for the demands of a modern enterprise environment.