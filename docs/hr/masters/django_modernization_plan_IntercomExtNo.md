## ASP.NET to Django Conversion Script: Intercom Extension Number Management

This modernization plan outlines the strategic transition of the ASP.NET `IntercomExtNo` module to a modern Django-based application. Our focus is on leveraging AI-assisted automation to streamline the migration, ensuring a robust, scalable, and maintainable solution. The new system will utilize Django 5.0+, HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience, while adhering to a "fat model, thin view" architectural pattern for clean code and clear separation of concerns.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET `SqlDataSource` components reveal the underlying database structure.

- **Main Table:** `tblHR_IntercomExt`
  - **Id:** Integer, Primary Key (implied by `DataKeyNames="Id"` and `WHERE [Id] = @Id`).
  - **ExtNo:** String (implied by `asp:TextBox` and `Type="String"` in `InsertParameters`).
  - **Department:** Integer (implied by `asp:DropDownList` `DataValueField="Id"` and `Type="Int32"` in `InsertParameters`), acts as a Foreign Key.

- **Lookup Table:** `tblHR_Departments`
  - **Id:** Integer, Primary Key.
  - **Symbol:** String (used as `Dept` in the dropdown, `SELECT [Id],[Symbol] as Dept`).

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The `SqlDataSource` commands and `GridView` event handlers define the core functionalities.

- **Create (Add):**
    - Triggered by `btnInsert` with `CommandName="Add"` or `CommandName="Add1"`.
    - Inserts a new record into `tblHR_IntercomExt` using `SqlDataSource1.InsertCommand`.
    - Fields captured: `ExtNo` (from `txtExtNo` textbox) and `Department` (from `ddDepartment` dropdown).
    - Basic validation: `ExtNo` and `Department` must not be empty.
- **Read (List):**
    - The `GridView1` is populated using `SqlDataSource1.SelectCommand`.
    - Retrieves `Id`, `ExtNo` from `tblHR_IntercomExt` and `Symbol` (aliased as `Dept`) from `tblHR_Departments`.
    - Joins `tblHR_IntercomExt` and `tblHR_Departments` on `Department` = `Id`.
    - Orders by `Id` in descending order.
    - Paging is enabled (`AllowPaging="True" PageSize="20"`).
- **Update (Edit):**
    - Triggered by `CommandField ShowEditButton="True"`.
    - Updates an existing record in `tblHR_IntercomExt` using `SqlDataSource1.UpdateCommand`.
    - Fields updated: `ExtNo` and `Department`. (Note: The C# code retrieves `ExtNo` from the footer row's `txtExtNo` on update, which is likely an oversight; in Django, it will be handled correctly from the edited row's form data).
- **Delete:**
    - Triggered by `CommandField ShowDeleteButton="True"`.
    - Deletes a record from `tblHR_IntercomExt` using `SqlDataSource1.DeleteCommand` based on `Id`.
- **Validation:** `RequiredFieldValidator` is used for the `ExtNo` field on both insert and update.
- **Messages:** `Label2` is used to display "Record inserted/deleted/updated." messages.
- **Client-side Confirmations:** `GridView1_RowDataBound` adds JavaScript `onclick` confirmations for edit and delete actions.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The visual elements and their interactions indicate the required Django components.

- **Grid/Table:** The `asp:GridView` will be replaced by an HTML `<table>` managed by DataTables.
- **Input Fields:**
    - `asp:TextBox` for `ExtNo` will become Django `forms.TextInput`.
    - `asp:DropDownList` for `Department` will become Django `forms.Select` or `forms.ModelChoiceField`.
- **Action Buttons:** `asp:Button` and `asp:LinkButton` for Insert, Edit, and Delete will be standard HTML `<button>` elements with HTMX attributes to trigger dynamic interactions (e.g., loading modals, submitting forms).
- **Messages:** Django's `messages` framework will be used to display feedback to the user, potentially integrated with HTMX `HX-Trigger` to show toast notifications.
- **Pop-ups:** Client-side JavaScript pop-ups (`PopUpMsg.js`) will be replaced by Alpine.js for modal management combined with HTMX for loading modal content.

### Step 4: Generate Django Code

**App Name:** `hr_masters` (or simply `hr`)

---

#### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**
We'll define two models: `IntercomExtNo` for the main data and `Department` for the lookup. The `managed = False` setting indicates that Django will not create or manage these tables, assuming they already exist in the database.

```python
# hr_masters/models.py
from django.db import models

class Department(models.Model):
    """
    Represents a Department, mapping to tblHR_Departments.
    Used for the dropdown list in IntercomExtNo.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100) # Assuming max_length based on typical usage

    class Meta:
        managed = False  # Django will not manage this table's creation/deletion
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.symbol

class IntercomExtNo(models.Model):
    """
    Represents an Intercom Extension Number, mapping to tblHR_IntercomExt.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is auto-incremented in DB
    ext_no = models.CharField(db_column='ExtNo', max_length=50) # Assuming max_length for ExtNo
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department') # DO_NOTHING since DB manages FK

    class Meta:
        managed = False  # Django will not manage this table's creation/deletion
        db_table = 'tblHR_IntercomExt'
        verbose_name = 'Intercom Extension Number'
        verbose_name_plural = 'Intercom Extension Numbers'

    def __str__(self):
        return f"{self.ext_no} ({self.department.symbol})"

    # Business logic methods can be added here if needed, e.g.,
    # def is_ext_no_valid(self):
    #    return self.ext_no.isdigit() and len(self.ext_no) == 4
```

---

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
A `ModelForm` will be created for `IntercomExtNo`. The `Department` field will be a `ModelChoiceField` to link to the `Department` model. Widgets will be added with Tailwind CSS classes for styling.

```python
# hr_masters/forms.py
from django import forms
from .models import IntercomExtNo, Department

class IntercomExtNoForm(forms.ModelForm):
    """
    Form for creating and updating IntercomExtNo records.
    """
    department = forms.ModelChoiceField(
        queryset=Department.objects.all().order_by('symbol'),
        empty_label="Select Department",
        label="Department",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    class Meta:
        model = IntercomExtNo
        fields = ['ext_no', 'department']
        widgets = {
            'ext_no': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 1234'
            }),
        }
        labels = {
            'ext_no': 'Ext. No',
        }

    def clean_ext_no(self):
        ext_no = self.cleaned_data['ext_no']
        # Example of custom validation: ensure ExtNo is numeric if desired, or check for uniqueness
        if not ext_no.isdigit():
            raise forms.ValidationError("Extension number must contain only digits.")
        
        # Check for uniqueness, excluding the current instance during an update
        query = IntercomExtNo.objects.filter(ext_no=ext_no)
        if self.instance.pk: # If updating an existing instance
            query = query.exclude(pk=self.instance.pk)
        
        if query.exists():
            raise forms.ValidationError("This extension number already exists.")
            
        return ext_no
```

---

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
Views will be thin, delegating business logic to the models and forms. HTMX headers will be checked to return appropriate responses (e.g., status 204 with `HX-Trigger` for successful HTMX form submissions).

```python
# hr_masters/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import IntercomExtNo, Department
from .forms import IntercomExtNoForm
from django.shortcuts import get_object_or_404

class IntercomExtNoListView(ListView):
    """
    Displays a list of all Intercom Extension Numbers.
    """
    model = IntercomExtNo
    template_name = 'hr_masters/intercomextno/list.html'
    context_object_name = 'intercom_ext_numbers'
    # Default ordering; DataTable will handle client-side sorting
    queryset = IntercomExtNo.objects.select_related('department').order_by('-id') 

class IntercomExtNoTablePartialView(ListView):
    """
    Renders only the table content for HTMX requests to refresh the list.
    """
    model = IntercomExtNo
    template_name = 'hr_masters/intercomextno/_table.html'
    context_object_name = 'intercom_ext_numbers'
    queryset = IntercomExtNo.objects.select_related('department').order_by('-id')

class IntercomExtNoCreateView(CreateView):
    """
    Handles creation of a new Intercom Extension Number.
    """
    model = IntercomExtNo
    form_class = IntercomExtNoForm
    template_name = 'hr_masters/intercomextno/_form.html' # Use partial template for modal
    success_url = reverse_lazy('intercomextno_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Intercom Extension Number added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshIntercomExtNoList'}
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request, render the form again with errors
            return response
        return response


class IntercomExtNoUpdateView(UpdateView):
    """
    Handles updating an existing Intercom Extension Number.
    """
    model = IntercomExtNo
    form_class = IntercomExtNoForm
    template_name = 'hr_masters/intercomextno/_form.html' # Use partial template for modal
    success_url = reverse_lazy('intercomextno_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Intercom Extension Number updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshIntercomExtNoList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class IntercomExtNoDeleteView(DeleteView):
    """
    Handles deletion of an Intercom Extension Number.
    """
    model = IntercomExtNo
    template_name = 'hr_masters/intercomextno/_confirm_delete.html' # Use partial template for modal
    success_url = reverse_lazy('intercomextno_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Intercom Extension Number deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshIntercomExtNoList'}
            )
        return response
```

---

#### 4.4 Templates

**Task:** Create templates for each view, leveraging partials for HTMX and base template inheritance.

**Instructions:**
All templates will extend `core/base.html`. DataTables will be used for the list view. HTMX will manage loading forms into modals and refreshing the table dynamically. Alpine.js will control the modal's visibility.

```html
{# hr_masters/templates/hr_masters/intercomextno/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Intercom Ext. No</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'intercomextno_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then remove .hidden from #modal">
            Add New Intercom Ext. No
        </button>
    </div>
    
    <div id="intercomextnoTable-container"
         hx-trigger="load, refreshIntercomExtNoList from:body"
         hx-get="{% url 'intercomextno_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Intercom Ext. Numbers...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden items-center justify-center z-50"
         x-data="{ showModal: false }"
         x-init="document.getElementById('modalContent').addEventListener('htmx:afterOnLoad', function() { showModal = true; })"
         x-show="showModal"
         x-on:click.self="showModal = false"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" 
             class="bg-white p-8 rounded-lg shadow-xl max-w-lg w-full m-4"
             hx-on::after-request="if(event.detail.successful) {
                document.getElementById('modal').classList.add('hidden'); 
                document.getElementById('modal').classList.remove('flex');
             }">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# DataTables CDN JS and custom script for initialization #}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.0.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.0.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/select/2.0.0/js/dataTables.select.min.js"></script>
<script>
    // Initialize DataTables after HTMX loads the table
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'intercomextnoTable-container') {
            $('#intercomextnoTable').DataTable({
                "pageLength": 20, // Match ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "pagingType": "full_numbers"
            });
        }
    });

    // Custom event listener for HX-Trigger to refresh the entire page (or specific elements)
    document.body.addEventListener('refreshIntercomExtNoList', function() {
        // This will trigger the hx-get on #intercomextnoTable-container
        // The modal should also be closed here.
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    });
</script>
{% endblock %}

{% block extra_css %}
{# DataTables CDN CSS #}
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
{% endblock %}
```

```html
{# hr_masters/templates/hr_masters/intercomextno/_table.html #}
<table id="intercomextnoTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ext. No</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in intercom_ext_numbers %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.ext_no }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.department.symbol }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'intercomextno_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then remove .hidden from #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                    hx-get="{% url 'intercomextno_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then remove .hidden from #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-4 text-center text-sm text-gray-500">No Intercom Extension Numbers found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

```

```html
{# hr_masters/templates/hr_masters/intercomextno/_form.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{% if form.instance.pk %}Edit{% else %}Add{% endif %} Intercom Ext. No</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.ext_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.ext_no.label }}
                </label>
                {{ form.ext_no }}
                {% if form.ext_no.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.ext_no.errors|join:", " }}</p>
                {% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.department.label }}
                </label>
                {{ form.department }}
                {% if form.department.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.department.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm mb-4">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# hr_masters/templates/hr_masters/intercomextno/_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete "<strong>{{ object.ext_no }} ({{ object.department.symbol }})</strong>"? This action cannot be undone.</p>
    
    <form hx-post="{% url 'intercomextno_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

---

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
The `hr_masters` app will have its own `urls.py` file to manage paths for Intercom Extension Number functionality.

```python
# hr_masters/urls.py
from django.urls import path
from .views import (
    IntercomExtNoListView,
    IntercomExtNoTablePartialView,
    IntercomExtNoCreateView,
    IntercomExtNoUpdateView,
    IntercomExtNoDeleteView,
)

urlpatterns = [
    path('intercom-ext-no/', IntercomExtNoListView.as_view(), name='intercomextno_list'),
    path('intercom-ext-no/table/', IntercomExtNoTablePartialView.as_view(), name='intercomextno_table'),
    path('intercom-ext-no/add/', IntercomExtNoCreateView.as_view(), name='intercomextno_add'),
    path('intercom-ext-no/edit/<int:pk>/', IntercomExtNoUpdateView.as_view(), name='intercomextno_edit'),
    path('intercom-ext-no/delete/<int:pk>/', IntercomExtNoDeleteView.as_view(), name='intercomextno_delete'),
]

# In your project's main urls.py (e.g., myproject/urls.py), you would include:
# from django.urls import path, include
# urlpatterns = [
#     path('hr/', include('hr_masters.urls')),
# ]
```

---

#### 4.6 Tests

**Task:** Write tests for the models and views.

**Instructions:**
Comprehensive unit tests will cover model methods and field properties. Integration tests will verify the behavior of all CRUD views, including HTMX interactions.

```python
# hr_masters/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import IntercomExtNo, Department
from .forms import IntercomExtNoForm

class DepartmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a Department for FK reference
        cls.department1 = Department.objects.create(id=1, symbol='IT')
        cls.department2 = Department.objects.create(id=2, symbol='HR')

    def test_department_creation(self):
        dept = Department.objects.get(id=1)
        self.assertEqual(dept.symbol, 'IT')

    def test_department_str_representation(self):
        dept = Department.objects.get(id=1)
        self.assertEqual(str(dept), 'IT')

    def test_department_verbose_name(self):
        self.assertEqual(Department._meta.verbose_name, 'Department')
        self.assertEqual(Department._meta.verbose_name_plural, 'Departments')

class IntercomExtNoModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.department = Department.objects.create(id=1, symbol='IT')
        IntercomExtNo.objects.create(id=1, ext_no='1001', department=cls.department)

    def test_intercomextno_creation(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        self.assertEqual(ext_no_obj.ext_no, '1001')
        self.assertEqual(ext_no_obj.department, self.department)

    def test_intercomextno_str_representation(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        self.assertEqual(str(ext_no_obj), '1001 (IT)')

    def test_intercomextno_verbose_name(self):
        self.assertEqual(IntercomExtNo._meta.verbose_name, 'Intercom Extension Number')
        self.assertEqual(IntercomExtNo._meta.verbose_name_plural, 'Intercom Extension Numbers')

    def test_ext_no_field_label(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        field_label = ext_no_obj._meta.get_field('ext_no').verbose_name
        self.assertEqual(field_label, 'ext no') # Django's default verbose name for 'ext_no'

    def test_department_field_label(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        field_label = ext_no_obj._meta.get_field('department').verbose_name
        self.assertEqual(field_label, 'department')

class IntercomExtNoFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.department = Department.objects.create(id=1, symbol='IT')

    def test_form_valid_data(self):
        form = IntercomExtNoForm(data={'ext_no': '1002', 'department': self.department.id})
        self.assertTrue(form.is_valid())

    def test_form_missing_ext_no(self):
        form = IntercomExtNoForm(data={'department': self.department.id})
        self.assertFalse(form.is_valid())
        self.assertIn('ext_no', form.errors)

    def test_form_missing_department(self):
        form = IntercomExtNoForm(data={'ext_no': '1003'})
        self.assertFalse(form.is_valid())
        self.assertIn('department', form.errors)

    def test_form_duplicate_ext_no_creation(self):
        IntercomExtNo.objects.create(id=1, ext_no='1004', department=self.department)
        form = IntercomExtNoForm(data={'ext_no': '1004', 'department': self.department.id})
        self.assertFalse(form.is_valid())
        self.assertIn('ext_no', form.errors)
        self.assertIn('This extension number already exists.', form.errors['ext_no'])

    def test_form_duplicate_ext_no_update(self):
        existing_ext_no = IntercomExtNo.objects.create(id=1, ext_no='1005', department=self.department)
        Department.objects.create(id=2, symbol='HR') # Create another department for update
        form = IntercomExtNoForm(instance=existing_ext_no, data={'ext_no': '1005', 'department': 2}) # Same ext_no, different department
        self.assertTrue(form.is_valid()) # Should be valid as it's the same instance

        # Test with a different existing ext_no
        IntercomExtNo.objects.create(id=2, ext_no='1006', department=self.department)
        form_duplicate = IntercomExtNoForm(instance=existing_ext_no, data={'ext_no': '1006', 'department': self.department.id})
        self.assertFalse(form_duplicate.is_valid())
        self.assertIn('This extension number already exists.', form_duplicate.errors['ext_no'])


class IntercomExtNoViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.department = Department.objects.create(id=1, symbol='IT')
        IntercomExtNo.objects.create(id=1, ext_no='1001', department=cls.department)
        IntercomExtNo.objects.create(id=2, ext_no='1002', department=cls.department)

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('intercomextno_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/intercomextno/list.html')
        self.assertIn('intercom_ext_numbers', response.context)
        self.assertEqual(len(response.context['intercom_ext_numbers']), 2) # Check number of objects

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('intercomextno_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/intercomextno/_table.html')
        self.assertIn('intercom_ext_numbers', response.context)
        self.assertEqual(len(response.context['intercom_ext_numbers']), 2)

    def test_create_view_get(self):
        response = self.client.get(reverse('intercomextno_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/intercomextno/_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], IntercomExtNoForm)

    def test_create_view_post_valid_data(self):
        new_department = Department.objects.create(id=3, symbol='Support')
        data = {'ext_no': '1003', 'department': new_department.id}
        response = self.client.post(reverse('intercomextno_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after success (non-HTMX)
        self.assertTrue(IntercomExtNo.objects.filter(ext_no='1003').exists())

    def test_create_view_post_invalid_data(self):
        data = {'ext_no': '', 'department': self.department.id} # Missing ExtNo
        response = self.client.post(reverse('intercomextno_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors (non-HTMX)
        self.assertFalse(IntercomExtNo.objects.filter(ext_no='').exists())
        self.assertIn('form', response.context)
        self.assertIn('ext_no', response.context['form'].errors)

    def test_create_view_htmx_post_valid_data(self):
        new_department = Department.objects.create(id=4, symbol='Sales')
        data = {'ext_no': '1004', 'department': new_department.id}
        response = self.client.post(reverse('intercomextno_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshIntercomExtNoList')
        self.assertTrue(IntercomExtNo.objects.filter(ext_no='1004').exists())

    def test_create_view_htmx_post_invalid_data(self):
        data = {'ext_no': 'abc', 'department': self.department.id} # Invalid ExtNo
        response = self.client.post(reverse('intercomextno_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors via HTMX swap
        self.assertIn('form', response.context)
        self.assertIn('ext_no', response.context['form'].errors)
        self.assertContains(response, 'Extension number must contain only digits.', html=True)

    def test_update_view_get(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        response = self.client.get(reverse('intercomextno_edit', args=[ext_no_obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/intercomextno/_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, ext_no_obj)

    def test_update_view_post_valid_data(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        data = {'ext_no': '2001', 'department': self.department.id}
        response = self.client.post(reverse('intercomextno_edit', args=[ext_no_obj.id]), data)
        self.assertEqual(response.status_code, 302) # Redirect after success (non-HTMX)
        ext_no_obj.refresh_from_db()
        self.assertEqual(ext_no_obj.ext_no, '2001')

    def test_update_view_htmx_post_valid_data(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        data = {'ext_no': '2002', 'department': self.department.id}
        response = self.client.post(reverse('intercomextno_edit', args=[ext_no_obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshIntercomExtNoList')
        ext_no_obj.refresh_from_db()
        self.assertEqual(ext_no_obj.ext_no, '2002')

    def test_delete_view_get(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        response = self.client.get(reverse('intercomextno_delete', args=[ext_no_obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/intercomextno/_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], ext_no_obj)

    def test_delete_view_post(self):
        ext_no_obj = IntercomExtNo.objects.get(id=1)
        response = self.client.post(reverse('intercomextno_delete', args=[ext_no_obj.id]))
        self.assertEqual(response.status_code, 302) # Redirect after success (non-HTMX)
        self.assertFalse(IntercomExtNo.objects.filter(id=1).exists())

    def test_delete_view_htmx_post(self):
        ext_no_obj = IntercomExtNo.objects.get(id=2) # Use the second object to avoid conflict with previous delete test
        response = self.client.post(reverse('intercomextno_delete', args=[ext_no_obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshIntercomExtNoList')
        self.assertFalse(IntercomExtNo.objects.filter(id=2).exists())
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already include the necessary HTMX attributes and Alpine.js setup for modal management.

- **HTMX for Dynamic Updates:**
    - `hx-get` on "Add New" button and "Edit"/"Delete" buttons within the table to load partial forms (`_form.html`, `_confirm_delete.html`) into the `#modalContent` div.
    - `hx-post` on forms (`_form.html`, `_confirm_delete.html`) to submit data without full page reloads.
    - `hx-swap="outerHTML" hx-target="#modalContent"` for forms to re-render the form itself (with validation errors) if submission fails, or to swap nothing (implicitly close modal if successful) if successful.
    - `HX-Trigger` header (`refreshIntercomExtNoList`) returned by views upon successful CRUD operations.
    - `hx-trigger="load, refreshIntercomExtNoList from:body"` on the `intercomextnoTable-container` to automatically load the initial table and refresh it when the `refreshIntercomExtNoList` event is triggered.
- **Alpine.js for UI State Management (Modal):**
    - The main modal (`#modal`) uses `x-data="{ showModal: false }"` to manage its visibility.
    - `x-init` listens for `htmx:afterOnLoad` on `modalContent` to set `showModal = true` when content is loaded, making the modal visible.
    - `x-show="showModal"` ties visibility directly to the `showModal` state.
    - `x-on:click.self="showModal = false"` allows clicking outside the modal content to close it.
    - Buttons for "Cancel" or successful HTMX form submission use `_="on click remove .flex from #modal then add .hidden to #modal"` to directly hide the modal, or the HTMX trigger closes it after success.
- **DataTables for List Views:**
    - The `_table.html` partial provides the bare `<table>` structure.
    - The `list.html` includes the DataTables JavaScript library and initializes it using `$(document).ready()` and an `htmx:afterSwap` event listener. This ensures DataTables is initialized *after* HTMX has loaded the table content into the DOM.
    - `pageLength` and `lengthMenu` are configured to match the ASP.NET `PageSize` and provide flexibility.
    - `destroy: true` is crucial for HTMX reloads to properly reinitialize DataTables.

---

### Final Notes

This comprehensive plan provides a clear roadmap for modernizing the ASP.NET `IntercomExtNo` module to Django. By focusing on AI-assisted automation and leveraging modern web technologies, the migration will result in a highly performant, maintainable, and user-friendly application. The use of "fat models, thin views," HTMX, Alpine.js, and DataTables ensures a clean architecture, dynamic interactions, and a rich user experience without the complexity of traditional JavaScript frameworks. The inclusion of extensive tests ensures the reliability and correctness of the migrated functionality.