## ASP.NET to Django Conversion Script: Business Group Management

This plan outlines the modernization of the "Business Group" module from a legacy ASP.NET application to a robust, modern Django solution. By leveraging cutting-edge technologies like HTMX, Alpine.js, and DataTables, we aim to deliver a highly interactive, efficient, and maintainable system with minimal manual coding.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`hr_masters`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the `SqlDataSource` definition in `BusinessGroup.aspx`, we can identify the following:

- **Table Name:** `BusinessGroup`
- **Columns:**
    - `Id`: Primary Key, Integer (likely auto-incrementing).
    - `Name`: String.
    - `Symbol`: String.
    - `Incharge`: String.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

- **Create:** The `GridView1_RowCommand` method handles the "Add" command, retrieving `Name`, `Symbol`, and `Incharge` from footer textboxes and inserting them into the `BusinessGroup` table via `LocalSqlServer.Insert()`. Basic validation checks if fields are non-empty.
- **Read:** The `GridView1` is bound to `LocalSqlServer` with `SelectCommand="SELECT * FROM [BusinessGroup]"`, displaying all records. Paging is enabled.
- **Update:** The `GridView1` has `ShowEditButton="True"` and `UpdateCommand` defined in `LocalSqlServer` to update `Name`, `Symbol`, and `Incharge` based on `Id`.
- **Delete:** The `GridView1` has `ShowDeleteButton="True"` and `DeleteCommand` defined in `LocalSqlServer` to delete records based on `Id`.
- **Validation Logic:** `RequiredFieldValidator` for `Name`, `Symbol`, and `Incharge` in the ASPX, and an explicit non-empty check in the `GridView1_RowCommand` for insert.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

- **`asp:GridView`:** This component is used for displaying a list of "Business Group" records with built-in paging, editing, and deleting capabilities directly within the grid. This will be replaced by a combination of Django's `ListView` for initial page load, DataTables for enhanced client-side table functionality (sorting, searching, pagination), and HTMX for dynamic updates.
- **`asp:TextBox` (in FooterTemplate and EditItemTemplate):** Used for inputting `Name`, `Symbol`, and `Incharge` for creating new records or editing existing ones. These will be replaced by standard HTML `<input type="text">` elements rendered by Django Forms, styled with Tailwind CSS.
- **`asp:Button`/`asp:LinkButton`:** Used for triggering "Insert," "Edit," and "Delete" actions. These will be replaced by standard HTML `<button>` elements with HTMX attributes to trigger partial requests and manage modal states using Alpine.js.
- **`asp:RequiredFieldValidator`:** Client-side validation for required fields. This will be handled by Django Forms' built-in validation, which can then be presented to the user.
- **`PopUpMsg.js` and `loadingNotifier.js`:** These JavaScript files indicate custom client-side interactions and loading indicators. These functionalities will be replaced by HTMX's capabilities for dynamic content loading, error handling, and `hx-indicator` attributes, coupled with Alpine.js for simple UI state management (like showing/hiding modals) without writing custom JavaScript.
- **`asp:Label ID="lblMessage"`:** Used for displaying status messages (e.g., "Record Updated"). This will be replaced by Django's messages framework, displayed using HTMX and Alpine.js.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `hr_masters`, to house this module.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**

The `BusinessGroup` model will directly map to the existing `BusinessGroup` table. We use `managed = False` to indicate that Django should not manage the table's schema, as it already exists.

```python
# hr_masters/models.py
from django.db import models

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)  # Assuming Id is the PK
    name = models.CharField(db_column='Name', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=255)
    incharge = models.CharField(db_column='Incharge', max_length=255)

    class Meta:
        managed = False  # Tells Django not to create or delete this table
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.name

    # Example of a 'fat model' method (if business logic were present)
    def some_business_logic_method(self):
        """
        Placeholder for any complex business logic related to a BusinessGroup.
        For instance, calculating derived properties or validating complex rules.
        """
        # In a real scenario, this would contain logic not suitable for views.
        return f"Business Group: {self.name} (Symbol: {self.symbol})"

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**

A `ModelForm` will be used for easy creation and updating of `BusinessGroup` instances. We will add Tailwind CSS classes to the widgets for consistent styling.

```python
# hr_masters/forms.py
from django import forms
from .models import BusinessGroup

class BusinessGroupForm(forms.ModelForm):
    class Meta:
        model = BusinessGroup
        fields = ['name', 'symbol', 'incharge']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'incharge': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_name(self):
        name = self.cleaned_data.get('name')
        # Example of custom validation: ensure name is unique (case-insensitive)
        # This assumes 'Name' is unique in the original ASP.NET application based on typical master data patterns
        if self.instance.pk: # If updating an existing instance
            if BusinessGroup.objects.filter(name__iexact=name).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("A Business Group with this name already exists.")
        else: # If creating a new instance
            if BusinessGroup.objects.filter(name__iexact=name).exists():
                raise forms.ValidationError("A Business Group with this name already exists.")
        return name

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

We will create four CBVs for the main CRUD operations and an additional `TemplateView` to render the DataTables partial, allowing HTMX to update only the table content. Each view will be kept concise, adhering to the "thin view" principle.

```python
# hr_masters/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import BusinessGroup
from .forms import BusinessGroupForm

class BusinessGroupListView(ListView):
    model = BusinessGroup
    template_name = 'hr_masters/businessgroup/list.html'
    context_object_name = 'businessgroups' # Renamed for consistency

class BusinessGroupTablePartialView(ListView):
    model = BusinessGroup
    template_name = 'hr_masters/businessgroup/_businessgroup_table.html'
    context_object_name = 'businessgroups'

    def get_queryset(self):
        # This can be extended to include search/filter logic if needed
        return BusinessGroup.objects.all().order_by('name')


class BusinessGroupCreateView(CreateView):
    model = BusinessGroup
    form_class = BusinessGroupForm
    template_name = 'hr_masters/businessgroup/_businessgroup_form.html' # This will be loaded as a partial
    success_url = reverse_lazy('businessgroup_list') # Redirection after non-HTMX submission

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Business Group added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBusinessGroupList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX requests, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class BusinessGroupUpdateView(UpdateView):
    model = BusinessGroup
    form_class = BusinessGroupForm
    template_name = 'hr_masters/businessgroup/_businessgroup_form.html' # This will be loaded as a partial
    success_url = reverse_lazy('businessgroup_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Business Group updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBusinessGroupList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class BusinessGroupDeleteView(DeleteView):
    model = BusinessGroup
    template_name = 'hr_masters/businessgroup/_businessgroup_confirm_delete.html' # This will be loaded as a partial
    success_url = reverse_lazy('businessgroup_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Business Group deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBusinessGroupList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

All templates will extend `core/base.html` (not shown) and utilize HTMX for dynamic interactions and Alpine.js for UI state management (like modals). DataTables will be used for the list view.

```html
<!-- hr_masters/templates/hr_masters/businessgroup/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Business Groups</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'businessgroup_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Business Group
        </button>
    </div>
    
    <div id="businessgroupTable-container"
         hx-trigger="load, refreshBusinessGroupList from:body"
         hx-get="{% url 'businessgroup_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Business Groups...</p>
        </div>
    </div>
    
    <!-- Global Modal Structure (can be in base.html or here if specific to this page) -->
    <!-- Alpine.js for showing/hiding, HTMX for content loading -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 hidden is-active:flex"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transition-transform duration-300 transform scale-95 is-active:scale-100">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery and DataTables JS/CSS in core/base.html or via CDN -->
<!-- Example CDNs that would be in base.html:
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css"/>
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
-->
<script>
    // This script block should be in core/base.html if DataTables is used globally.
    // If not, ensure jQuery and DataTables libraries are loaded before this.
    // This specific script ensures DataTables re-initializes on HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'businessgroupTable-container') {
            $('#businessgroupTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance to re-initialize
            });
        }
    });

    // Simple Alpine.js for modal behavior if needed, but mostly handled by HTMX and _hyperscript now
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            open: false,
            toggle() {
                this.open = !this.open;
            }
        }));
    });
</script>
{% endblock %}
```

```html
<!-- hr_masters/templates/hr_masters/businessgroup/_businessgroup_table.html (Partial) -->
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="businessgroupTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Incharge</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for bg in businessgroups %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ bg.name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ bg.symbol }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ bg.incharge }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'businessgroup_edit' bg.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                        hx-get="{% url 'businessgroup_delete' bg.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-6 text-center text-gray-500">No Business Groups found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script will be handled by the listener in list.html's extra_js -->
```

```html
<!-- hr_masters/templates/hr_masters/businessgroup/_businessgroup_form.html (Partial) -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Business Group</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <div class="text-red-600 text-xs mt-1">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-2">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

```html
<!-- hr_masters/templates/hr_masters/businessgroup/_businessgroup_confirm_delete.html (Partial) -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Business Group: <strong>"{{ businessgroup.name }}"</strong>?</p>
    
    <form hx-post="{% url 'businessgroup_delete' businessgroup.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-2">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**

These URLs will be placed in `hr_masters/urls.py` and included in your project's main `urls.py`.

```python
# hr_masters/urls.py
from django.urls import path
from .views import (
    BusinessGroupListView,
    BusinessGroupTablePartialView,
    BusinessGroupCreateView,
    BusinessGroupUpdateView,
    BusinessGroupDeleteView,
)

urlpatterns = [
    path('businessgroup/', BusinessGroupListView.as_view(), name='businessgroup_list'),
    path('businessgroup/table/', BusinessGroupTablePartialView.as_view(), name='businessgroup_table'),
    path('businessgroup/add/', BusinessGroupCreateView.as_view(), name='businessgroup_add'),
    path('businessgroup/edit/<int:pk>/', BusinessGroupUpdateView.as_view(), name='businessgroup_edit'),
    path('businessgroup/delete/<int:pk>/', BusinessGroupDeleteView.as_view(), name='businessgroup_delete'),
]

```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**

These tests ensure the correct behavior of the model and all CRUD operations, including HTMX interactions.

```python
# hr_masters/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import BusinessGroup
from .forms import BusinessGroupForm

class BusinessGroupModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test BusinessGroup instance for all tests in this class
        BusinessGroup.objects.create(
            id=1,  # Assuming ID is explicitly set for existing DB, or let DB handle it if truly auto-incrementing
            name='Test Business Group A',
            symbol='TBA',
            incharge='John Doe',
        )
        BusinessGroup.objects.create(
            id=2,
            name='Test Business Group B',
            symbol='TBB',
            incharge='Jane Smith',
        )

    def test_businessgroup_creation(self):
        # Retrieve the created object and verify its attributes
        bg = BusinessGroup.objects.get(name='Test Business Group A')
        self.assertEqual(bg.name, 'Test Business Group A')
        self.assertEqual(bg.symbol, 'TBA')
        self.assertEqual(bg.incharge, 'John Doe')

    def test_name_label(self):
        # Test verbose_name for a field
        bg = BusinessGroup.objects.get(id=1)
        field_label = bg._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'name') # Default verbose_name if not explicitly set

    def test_str_method(self):
        # Test the __str__ method of the model
        bg = BusinessGroup.objects.get(id=1)
        self.assertEqual(str(bg), 'Test Business Group A')

    def test_unique_name_validation(self):
        # Test custom clean_name validation for uniqueness
        form_data = {'name': 'test business group a', 'symbol': 'XYZ', 'incharge': 'Someone'}
        form = BusinessGroupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('A Business Group with this name already exists.', form.errors['name'])
        
        # Test update case where name is unique, even if case differs
        bg_to_update = BusinessGroup.objects.get(id=1)
        form_data_update = {'name': 'Test Business Group B', 'symbol': 'Updated', 'incharge': 'Updated Person'}
        form_update = BusinessGroupForm(data=form_data_update, instance=bg_to_update)
        self.assertFalse(form_update.is_valid()) # Should fail because 'Test Business Group B' already exists for ID 2
        self.assertIn('A Business Group with this name already exists.', form_update.errors['name'])


class BusinessGroupViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        BusinessGroup.objects.create(
            id=1,
            name='Existing Group 1',
            symbol='EG1',
            incharge='Old Manager',
        )
        BusinessGroup.objects.create(
            id=2,
            name='Existing Group 2',
            symbol='EG2',
            incharge='New Manager',
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('businessgroup_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/list.html')
        self.assertIn('businessgroups', response.context)
        self.assertEqual(response.context['businessgroups'].count(), 2) # Should see 2 initial objects

    def test_table_partial_view(self):
        # Test the HTMX-loaded partial table view
        response = self.client.get(reverse('businessgroup_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_table.html')
        self.assertIn('businessgroups', response.context)
        self.assertEqual(response.context['businessgroups'].count(), 2)
        self.assertContains(response, 'Existing Group 1') # Check if content is rendered

    def test_create_view_get(self):
        response = self.client.get(reverse('businessgroup_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        initial_count = BusinessGroup.objects.count()
        data = {'name': 'New Group', 'symbol': 'NG', 'incharge': 'Creator'}
        response = self.client.post(reverse('businessgroup_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content for HTMX success
        self.assertEqual(response.status_code, 204)
        self.assertTrue(BusinessGroup.objects.filter(name='New Group').exists())
        self.assertEqual(BusinessGroup.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBusinessGroupList')

    def test_create_view_post_invalid(self):
        initial_count = BusinessGroup.objects.count()
        # Missing required fields
        data = {'name': '', 'symbol': 'NG', 'incharge': 'Creator'}
        response = self.client.post(reverse('businessgroup_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 200 OK with form re-rendered for HTMX invalid submission
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_form.html')
        self.assertFalse(BusinessGroup.objects.filter(name='').exists())
        self.assertEqual(BusinessGroup.objects.count(), initial_count)
        self.assertContains(response, 'This field is required.') # Check for validation error message

    def test_create_view_post_duplicate_name(self):
        initial_count = BusinessGroup.objects.count()
        # Try to create with an existing name
        data = {'name': 'Existing Group 1', 'symbol': 'DUPE', 'incharge': 'Duplicator'}
        response = self.client.post(reverse('businessgroup_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_form.html')
        self.assertEqual(BusinessGroup.objects.count(), initial_count)
        self.assertContains(response, 'A Business Group with this name already exists.')


    def test_update_view_get(self):
        bg = BusinessGroup.objects.get(id=1)
        response = self.client.get(reverse('businessgroup_edit', args=[bg.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.name, 'Existing Group 1')

    def test_update_view_post_success(self):
        bg = BusinessGroup.objects.get(id=1)
        data = {'name': 'Updated Group 1', 'symbol': 'UG1', 'incharge': 'Updated Manager'}
        response = self.client.post(reverse('businessgroup_edit', args=[bg.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        bg.refresh_from_db()
        self.assertEqual(bg.name, 'Updated Group 1')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBusinessGroupList')

    def test_update_view_post_invalid(self):
        bg = BusinessGroup.objects.get(id=1)
        original_name = bg.name
        data = {'name': '', 'symbol': 'UG1', 'incharge': 'Updated Manager'} # Invalid data
        response = self.client.post(reverse('businessgroup_edit', args=[bg.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_form.html')
        bg.refresh_from_db()
        self.assertEqual(bg.name, original_name) # Ensure no change occurred
        self.assertContains(response, 'This field is required.')

    def test_update_view_post_duplicate_name(self):
        bg_to_update = BusinessGroup.objects.get(id=1)
        original_name = bg_to_update.name
        # Try to update with a name that conflicts with another existing group
        data = {'name': 'Existing Group 2', 'symbol': 'EG2', 'incharge': 'Manager X'}
        response = self.client.post(reverse('businessgroup_edit', args=[bg_to_update.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_form.html')
        bg_to_update.refresh_from_db()
        self.assertEqual(bg_to_update.name, original_name) # Ensure no change occurred
        self.assertContains(response, 'A Business Group with this name already exists.')


    def test_delete_view_get(self):
        bg = BusinessGroup.objects.get(id=1)
        response = self.client.get(reverse('businessgroup_delete', args=[bg.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/businessgroup/_businessgroup_confirm_delete.html')
        self.assertIn('businessgroup', response.context)
        self.assertEqual(response.context['businessgroup'].name, 'Existing Group 1')

    def test_delete_view_post_success(self):
        initial_count = BusinessGroup.objects.count()
        bg_to_delete = BusinessGroup.objects.get(id=1)
        response = self.client.post(reverse('businessgroup_delete', args=[bg_to_delete.id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BusinessGroup.objects.filter(id=bg_to_delete.id).exists())
        self.assertEqual(BusinessGroup.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBusinessGroupList')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

- **HTMX for Dynamic Updates:**
    - The main `list.html` uses `hx-get` to fetch the table content from `{% url 'businessgroup_table' %}`. This initial load also triggers `hx-trigger="load"`.
    - After any successful Create, Update, or Delete operation, the Django views respond with `status=204` and an `HX-Trigger` header set to `refreshBusinessGroupList`. This causes the table container to re-fetch its content, effectively refreshing the DataTables.
    - Buttons for Add, Edit, and Delete in the `_businessgroup_table.html` partial use `hx-get` to fetch the respective form/confirmation templates into the `#modalContent` div.
    - Form submissions in `_businessgroup_form.html` and `_businessgroup_confirm_delete.html` use `hx-post` with `hx-swap="none"` because the Django view handles the `HX-Trigger` to refresh the parent list, rather than swapping content directly in the modal.
    - HTMX indicators (`hx-indicator`) are added to buttons/forms to show loading spinners.

- **Alpine.js for UI State Management:**
    - The modal visibility (`hidden`/`flex` classes) is primarily controlled by `_hyperscript` using `on click add .is-active to #modal` and `on click remove .is-active from me`. This provides a clean way to manage the modal without explicit JavaScript.
    - Alpine.js is included as a fallback or for more complex UI state if needed, but for simple modal show/hide, _hyperscript works effectively with HTMX.

- **DataTables for List Views:**
    - The `_businessgroup_table.html` includes the `<table>` element with `id="businessgroupTable"`.
    - A JavaScript block in `list.html` (which assumes jQuery and DataTables libraries are loaded in `core/base.html`) initializes DataTables on this table ID.
    - Crucially, `document.body.addEventListener('htmx:afterSwap', ...)` is used to re-initialize DataTables after the partial table HTML is loaded via HTMX, ensuring dynamic content properly supports DataTables features like pagination, search, and sort. `destroy: true` is vital for re-initialization.

- **DRY Templates:**
    - The `list.html` acts as the main page.
    - `_businessgroup_table.html`, `_businessgroup_form.html`, and `_businessgroup_confirm_delete.html` are partials, loaded dynamically via HTMX, reducing redundancy in HTML structure.

### Final Notes

- **Placeholders:** All placeholders like `[MODEL_NAME]`, `[APP_NAME]`, `[FIELD1]` have been replaced with `BusinessGroup`, `hr_masters`, `name`, `symbol`, `incharge`, etc., based on the ASP.NET analysis.
- **Business Logic:** The original ASP.NET code had minimal explicit business logic beyond basic validation and CRUD. In Django, this logic would ideally reside in the `BusinessGroup` model methods (`some_business_logic_method` is an example). Form-level validation is placed in the `BusinessGroupForm`.
- **Styling:** Tailwind CSS classes (e.g., `bg-blue-600`, `rounded-md`, `shadow-sm`) are integrated directly into the HTML templates for a modern, responsive design.
- **Accessibility:** Ensure proper ARIA attributes are added to modals and interactive elements for improved accessibility in a real-world application.
- **Error Handling:** The views include basic `form_invalid` handling for HTMX requests, re-rendering the form with errors. More sophisticated error display (e.g., toast notifications via HTMX) can be added.
- **Security:** Ensure Django's built-in CSRF protection is always used (`{% csrf_token %}`). Authentication and authorization would be implemented via Django's auth system and decorators (`@login_required`, `LoginRequiredMixin`) as needed for a production application.