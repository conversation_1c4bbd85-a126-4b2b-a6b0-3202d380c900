## ASP.NET to Django Conversion Script: PF Slab Management

This document outlines the modernization plan to transition your legacy ASP.NET PF Slab management module to a modern Django-based solution. Our approach prioritizes automation, leverages AI-assisted tools for efficient code conversion, and focuses on delivering business value through improved maintainability, scalability, and user experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code extensively uses `tblHR_PF_Slab`. The GridView columns and SQL statements (e.g., `SELECT * FROM [tblHR_PF_Slab]`, `INSERT`, `UPDATE`, `DELETE`) reveal the schema.

**Extracted Schema:**
- **Table Name:** `tblHR_PF_Slab`
- **Columns:**
    - `Id`: Integer, Primary Key (auto-incrementing).
    - `PFEmployee`: Numeric (decimal/float), allows up to 15 digits total, with up to 3 decimal places.
    - `PFCompany`: Numeric (decimal/float), allows up to 15 digits total, with up to 3 decimal places.
    - `Active`: Integer (0 or 1), representing a Boolean state.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and associated business logic in the ASP.NET code.

**Analysis:**
The C# code-behind reveals standard CRUD operations managed by the `GridView1` control's events.

**Identified Functionality:**
- **Create (Add):** Triggered by `GridView1_RowCommand` (for `Add` and `Add1` commands, from footer and empty data template respectively). Inserts new records into `tblHR_PF_Slab`.
- **Read (List):** `FillAccegrid()` fetches all records from `tblHR_PF_Slab` and binds them to the GridView. Records are ordered by `Id` in descending order.
- **Update (Edit):** Triggered by `GridView1_RowUpdating`. Updates existing records in `tblHR_PF_Slab` based on `Id`.
- **Delete:** Triggered by `GridView1_RowDeleting`. Deletes records from `tblHR_PF_Slab` based on `Id`.
- **Validation:**
    - `PFEmployee` and `PFCompany` fields are mandatory and validated as numeric (`^\d{1,15}(\.\d{0,3})?$`).
    - **Crucial Business Logic:** Only one `PF_Slab` record is allowed to be `Active=1` at any given time. The ASP.NET UI dynamically disables the `Active` checkbox for new entries or inactive entries if an active slab already exists. If an entry is already active, its checkbox remains enabled in edit mode. This means the system enforces "at most one active" slab, preventing users from creating or activating a second one while one is already active.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern Django/HTMX/Alpine.js equivalents.

**Analysis:**
The `.aspx` file primarily uses an `asp:GridView` for data display and inline editing/insertion. Input fields are `TextBox` and `CheckBox`.

**Inferred UI Components and Django Mapping:**
- **`asp:GridView`:** This will be replaced by a standard HTML `<table>` element enhanced with the DataTables.js library for pagination, sorting, and searching.
- **`asp:TemplateField`s:** These will become `<td>` elements displaying model fields.
- **`asp:TextBox` (for `PFEmployee`, `PFCompany`):** HTML `<input type="text">` elements, styled with Tailwind CSS classes, within Django forms.
- **`asp:CheckBox` (for `Active`):** HTML `<input type="checkbox">` element, styled with Tailwind CSS classes, within Django forms.
- **`asp:Button`, `asp:LinkButton` (for CRUD actions):** Standard HTML `<button>` elements with HTMX attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`) to handle dynamic interactions without full page reloads.
- **Client-side Pop-up Messages (`PopUpMsg.js`):** Will be handled by Django's `messages` framework and potentially Alpine.js for interactive toast notifications, or simply by HTMX swapping content. The `lblMessage` for status updates will be handled by Django's `messages` framework integrated with HTMX.
- **Modal Functionality:** The ASP.NET inline editing/adding will be converted to modal forms using HTMX to load form content and Alpine.js to manage modal visibility.

---

## Step 4: Generate Django Code

Based on the analysis, we will create a new Django app, let's call it `hr_masters`, to house this `PF_Slab` module.

### 4.1 Models (`hr_masters/models.py`)

This file defines the data structure for PF Slabs, mapping directly to your existing `tblHR_PF_Slab` database table.
The `save` method handles the critical business logic of ensuring only one PF Slab can be active at a time. If an existing active slab is updated, it remains active. If an inactive slab is set to active, it will deactivate any previously active slab.

```python
from django.db import models
from django.core.exceptions import ValidationError

class PF_Slab(models.Model):
    """
    Represents a PF Slab entry in the HR Masters module.
    Maps to the existing tblHR_PF_Slab database table.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    pf_employee = models.DecimalField(db_column='PFEmployee', max_digits=15, decimal_places=3, verbose_name="PF Employee Share")
    pf_company = models.DecimalField(db_column='PFCompany', max_digits=15, decimal_places=3, verbose_name="PF Company Share")
    active = models.BooleanField(db_column='Active', default=False, verbose_name="Is Active?")

    class Meta:
        managed = False  # Set to False as the table already exists
        db_table = 'tblHR_PF_Slab'
        verbose_name = 'PF Slab'
        verbose_name_plural = 'PF Slabs'
        ordering = ['-id'] # Order by ID descending, matching original behavior

    def __str__(self):
        return f"PF Slab (Emp: {self.pf_employee}, Comp: {self.pf_company}, Active: {self.active})"

    def save(self, *args, **kwargs):
        """
        Custom save method to ensure only one PF Slab is active at a time.
        If this slab is being set to active, all other slabs will be deactivated.
        If an already active slab is being updated, it remains the sole active one.
        If an inactive slab is being updated (and remains inactive), no change to other slabs.
        """
        if self.active:
            # If this slab is being made active, deactivate all other active slabs
            PF_Slab.objects.filter(active=True).exclude(pk=self.pk).update(active=False)
        
        # If this slab is inactive and no other active slabs exist, it can be saved as inactive.
        # If this slab is inactive and other active slabs exist, it can be saved as inactive.
        
        super().save(*args, **kwargs)

    def clean(self):
        """
        Custom clean method for model-level validation.
        Ensures that an inactive slab cannot be made active if another slab is already active,
        unless this slab itself is the one that's already active.
        This handles the ASP.NET UI's checkbox disabling logic.
        """
        if self.active and not self.pk:  # Creating a new active slab
            if PF_Slab.objects.filter(active=True).exists():
                raise ValidationError("Cannot create a new active PF Slab. An active slab already exists.")
        elif self.active and self.pk:  # Updating an existing slab to active
            if PF_Slab.objects.filter(active=True).exclude(pk=self.pk).exists():
                raise ValidationError("Cannot make this PF Slab active. Another slab is already active.")
```

### 4.2 Forms (`hr_masters/forms.py`)

This file defines the Django form for interacting with the `PF_Slab` model. It handles input rendering and includes custom validation for the `PFEmployee`, `PFCompany`, and `Active` fields.

```python
from django import forms
from .models import PF_Slab
import re

class PF_SlabForm(forms.ModelForm):
    """
    Form for creating and updating PF_Slab instances.
    Includes custom validation rules derived from ASP.NET RegularExpressionValidator.
    """
    class Meta:
        model = PF_Slab
        fields = ['pf_employee', 'pf_company', 'active']
        widgets = {
            'pf_employee': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter PF Employee Share'
            }),
            'pf_company': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter PF Company Share'
            }),
            'active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded',
                'x-model': 'activeStatus', # Alpine.js binding for dynamic UI
                'x-bind:disabled': 'activeStatusDisabled' # Alpine.js binding for dynamic disabling
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically disable the 'active' checkbox based on existing active slabs,
        # mirroring ASP.NET behavior for UI feedback.
        # Server-side validation (in clean_active) remains the primary safeguard.
        active_slab_exists = PF_Slab.objects.filter(active=True).exists()
        
        # Default Alpine.js state for the checkbox and its disabled status
        self.fields['active'].widget.attrs['x-init'] = f"activeStatus = {str(self.initial.get('active', False)).lower()}; activeStatusDisabled = false;"

        if self.instance.pk:  # If this is an update form for an existing instance
            if self.instance.active:
                # If the current instance is active, allow it to be toggled
                self.fields['active'].widget.attrs['x-init'] = f"activeStatus = true; activeStatusDisabled = false;"
            elif active_slab_exists and not self.instance.active:
                # If another active slab exists and this one is inactive, disable its checkbox
                self.fields['active'].widget.attrs['x-init'] = f"activeStatus = false; activeStatusDisabled = true;"
        else:  # If this is a create form for a new instance
            if active_slab_exists:
                # If an active slab exists, disable the checkbox for new entries
                self.fields['active'].widget.attrs['x-init'] = f"activeStatus = false; activeStatusDisabled = true;"

    def clean_pf_employee(self):
        pf_employee = self.cleaned_data['pf_employee']
        # Regex from ASP.NET: ^\d{1,15}(\.\d{0,3})?$
        if not re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", str(pf_employee)):
            raise forms.ValidationError("PF Employee must be a number with up to 15 digits and 3 decimal places.")
        return pf_employee

    def clean_pf_company(self):
        pf_company = self.cleaned_data['pf_company']
        # Regex from ASP.NET: ^\d{1,15}(\.\d{0,3})?$
        if not re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", str(pf_company)):
            raise forms.ValidationError("PF Company must be a number with up to 15 digits and 3 decimal places.")
        return pf_company

    def clean(self):
        cleaned_data = super().clean()
        active = cleaned_data.get('active')

        # Model's clean method handles this more robustly, but form can also add specific UI-driven checks.
        # This form's `clean` method is primarily for field-specific validation.
        # The model's `clean` method provides the ultimate validation for the active status.
        return cleaned_data
```

### 4.3 Views (`hr_masters/views.py`)

These Class-Based Views (CBVs) handle the CRUD operations. They are designed to be "thin" (5-15 lines per method), delegating business logic to the model and handling HTMX responses.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import PF_Slab
from .forms import PF_SlabForm

# Base class for HTMX responses to keep views DRY and concise
class HTMXFormMixin:
    """
    Mixin to handle HTMX-specific responses for form submissions.
    Triggers a 'refreshPF_SlabList' event on success.
    """
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'{self.model._meta.verbose_name} saved successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,  # No Content
                headers={'HX-Trigger': 'refreshPF_SlabList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If it's an HTMX request, just return the form partial with errors
            return self.render_to_response(self.get_context_data(form=form))
        return response

class PF_SlabListView(ListView):
    """
    Displays a list of all PF Slabs.
    Main entry point for the PF Slab module.
    """
    model = PF_Slab
    template_name = 'hr_masters/pf_slab/list.html'
    context_object_name = 'pf_slabs'
    # No custom logic here, keeps the view thin.

class PF_SlabTablePartialView(ListView):
    """
    Partial view to render the DataTables table for PF Slabs.
    Loaded via HTMX to dynamically update the list.
    """
    model = PF_Slab
    template_name = 'hr_masters/pf_slab/_pf_slab_table.html'
    context_object_name = 'pf_slabs'
    # No custom logic here, keeps the view thin.

class PF_SlabCreateView(HTMXFormMixin, CreateView):
    """
    Handles creation of new PF Slab entries.
    Loads the form into a modal via HTMX.
    """
    model = PF_Slab
    form_class = PF_SlabForm
    template_name = 'hr_masters/pf_slab/_pf_slab_form.html' # Use partial template for HTMX
    success_url = reverse_lazy('pf_slab_list') # Fallback for non-HTMX requests


class PF_SlabUpdateView(HTMXFormMixin, UpdateView):
    """
    Handles updating existing PF Slab entries.
    Loads the form into a modal via HTMX.
    """
    model = PF_Slab
    form_class = PF_SlabForm
    template_name = 'hr_masters/pf_slab/_pf_slab_form.html' # Use partial template for HTMX
    success_url = reverse_lazy('pf_slab_list') # Fallback for non-HTMX requests


class PF_SlabDeleteView(DeleteView):
    """
    Handles deletion of PF Slab entries.
    Loads the confirmation into a modal via HTMX.
    """
    model = PF_Slab
    template_name = 'hr_masters/pf_slab/_pf_slab_confirm_delete.html' # Use partial template for HTMX
    success_url = reverse_lazy('pf_slab_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to send HTMX-specific response.
        """
        verbose_name = self.get_object()._meta.verbose_name
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'{verbose_name} deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,  # No Content
                headers={'HX-Trigger': 'refreshPF_SlabList'}
            )
        return response

```

### 4.4 Templates

Templates are organized within `hr_masters/templates/hr_masters/pf_slab/`. They are designed to be DRY, utilizing partials and HTMX for dynamic content updates.

#### `hr_masters/templates/hr_masters/pf_slab/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">PF Slabs</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'pf_slab_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New PF Slab
        </button>
    </div>
    
    <div id="pf_slabTable-container"
         hx-trigger="load, refreshPF_SlabList from:body"
         hx-get="{% url 'pf_slab_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading PF Slabs...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }"
         x-init="() => { 
             $watch('show', value => { 
                 if (value) { 
                     document.body.style.overflow = 'hidden'; 
                 } else { 
                     document.body.style.overflow = ''; 
                 } 
             }); 
             htmx.on('htmx:afterSwap', (evt) => {
                 if (evt.target.id === 'modalContent' && evt.detail.elt.outerHTML.includes('form')) {
                     $dispatch('open-modal');
                 }
             });
             htmx.on('htmx:responseError', (evt) => {
                 if (evt.detail.xhr.status === 400 && evt.detail.elt.closest('#modalContent')) {
                     // If form submission fails with 400, keep modal open
                     $dispatch('open-modal');
                 }
             });
             htmx.on('htmx:afterSettle', (evt) => {
                 // Close modal only if swap was triggered by a successful form submission
                 if (evt.detail.xhr.status === 204 && evt.detail.requestConfig.target && evt.detail.requestConfig.target.id === 'modalContent') {
                     $dispatch('close-modal');
                 }
             });
         }"
         x-show="show" 
         x-on:open-modal.window="show = true" 
         x-on:close-modal.window="show = false" 
         style="display: none;">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             @click.away="show = false; $dispatch('close-modal');">
            <!-- Form content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed here, handled directly in template attributes.
    });
</script>
{% endblock %}
```

#### `hr_masters/templates/hr_masters/pf_slab/_pf_slab_table.html`

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="pf_slabTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Employee Share</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Company Share</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Is Active?</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in pf_slabs %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.pf_employee }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.pf_company }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% if obj.active %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                    {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">No</span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3 transition duration-150 ease-in-out"
                        hx-get="{% url 'pf_slab_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 transition duration-150 ease-in-out"
                        hx-get="{% url 'pf_slab_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">
                    No PF Slabs found. Click "Add New PF Slab" to create one.
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Only initialize DataTables if the table is not empty
    if ($('#pf_slabTable tbody tr').length > 0 && $('#pf_slabTable tbody tr td').attr('colspan') != '5') {
        $('#pf_slabTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions columns
            ]
        });
    }
});
</script>
```

#### `hr_masters/templates/hr_masters/pf_slab/_pf_slab_form.html`

```html
<div class="p-6" x-data="{ activeStatus: {% if form.instance.active %}true{% else %}false{% endif %}, activeStatusDisabled: {% if form.fields.active.widget.attrs.disabled %}true{% else %}false{% endif %} }">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} PF Slab</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            <div>
                <label for="{{ form.pf_employee.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.pf_employee.label }}
                </label>
                <div class="mt-1">
                    {{ form.pf_employee }}
                </div>
                {% if form.pf_employee.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.pf_employee.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.pf_company.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.pf_company.label }}
                </label>
                <div class="mt-1">
                    {{ form.pf_company }}
                </div>
                {% if form.pf_company.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.pf_company.errors }}</p>
                {% endif %}
            </div>

            <div class="sm:col-span-2 flex items-center">
                {{ form.active }}
                <label for="{{ form.active.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
                    {{ form.active.label }}
                </label>
            </div>
            {% if form.active.errors %}
            <div class="sm:col-span-2">
                <p class="text-red-500 text-xs mt-1">{{ form.active.errors }}</p>
            </div>
            {% endif %}

            {% if form.non_field_errors %}
            <div class="sm:col-span-2 text-red-500 text-xs mt-1">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click $dispatch('close-modal')">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `hr_masters/templates/hr_masters/pf_slab/_pf_slab_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this PF Slab?</p>
    
    <div class="mb-4">
        <p><strong>PF Employee Share:</strong> {{ object.pf_employee }}</p>
        <p><strong>PF Company Share:</strong> {{ object.pf_company }}</p>
        <p><strong>Is Active:</strong> {{ object.active|yesno:"Yes,No" }}</p>
    </div>

    <form hx-post="{% url 'pf_slab_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click $dispatch('close-modal')">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`hr_masters/urls.py`)

This file defines the URL patterns that map requests to the Django views.

```python
from django.urls import path
from .views import (
    PF_SlabListView, 
    PF_SlabCreateView, 
    PF_SlabUpdateView, 
    PF_SlabDeleteView,
    PF_SlabTablePartialView
)

urlpatterns = [
    # Main list view for PF Slabs
    path('pf-slab/', PF_SlabListView.as_view(), name='pf_slab_list'),
    
    # HTMX endpoint for the DataTables partial
    path('pf-slab/table/', PF_SlabTablePartialView.as_view(), name='pf_slab_table'),
    
    # HTMX endpoint for adding a new PF Slab (loads form in modal)
    path('pf-slab/add/', PF_SlabCreateView.as_view(), name='pf_slab_add'),
    
    # HTMX endpoint for editing an existing PF Slab (loads form in modal)
    path('pf-slab/edit/<int:pk>/', PF_SlabUpdateView.as_view(), name='pf_slab_edit'),
    
    # HTMX endpoint for deleting a PF Slab (loads confirmation in modal)
    path('pf-slab/delete/<int:pk>/', PF_SlabDeleteView.as_view(), name='pf_slab_delete'),
]

```

### 4.6 Tests (`hr_masters/tests.py`)

Comprehensive unit tests for the `PF_Slab` model and integration tests for all associated views are crucial for ensuring data integrity and application functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from django.core.exceptions import ValidationError
from .models import PF_Slab
from .forms import PF_SlabForm

class PF_SlabModelTest(TestCase):
    """
    Unit tests for the PF_Slab model, covering field attributes,
    string representation, and custom save/clean logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for models that might be used across tests
        PF_Slab.objects.create(pf_employee='100.000', pf_company='50.000', active=False)
        PF_Slab.objects.create(pf_employee='120.500', pf_company='60.250', active=True) # An active slab

    def test_pf_slab_creation(self):
        """Test basic creation and field assignments."""
        slab = PF_Slab.objects.get(id=1)
        self.assertEqual(slab.pf_employee, 100.000)
        self.assertEqual(slab.pf_company, 50.000)
        self.assertFalse(slab.active)

    def test_pf_employee_field_properties(self):
        """Test verbose name and max_digits/decimal_places for pf_employee."""
        slab = PF_Slab.objects.get(id=1)
        field = slab._meta.get_field('pf_employee')
        self.assertEqual(field.verbose_name, 'PF Employee Share')
        self.assertEqual(field.max_digits, 15)
        self.assertEqual(field.decimal_places, 3)

    def test_pf_company_field_properties(self):
        """Test verbose name and max_digits/decimal_places for pf_company."""
        slab = PF_Slab.objects.get(id=1)
        field = slab._meta.get_field('pf_company')
        self.assertEqual(field.verbose_name, 'PF Company Share')
        self.assertEqual(field.max_digits, 15)
        self.assertEqual(field.decimal_places, 3)

    def test_active_field_properties(self):
        """Test verbose name for active field."""
        slab = PF_Slab.objects.get(id=1)
        field = slab._meta.get_field('active')
        self.assertEqual(field.verbose_name, 'Is Active?')

    def test_str_method(self):
        """Test the __str__ method for human-readable representation."""
        slab = PF_Slab.objects.get(id=1)
        expected_str = "PF Slab (Emp: 100.000, Comp: 50.000, Active: False)"
        self.assertEqual(str(slab), expected_str)

    def test_db_table_and_managed(self):
        """Verify Meta options for existing database table."""
        self.assertEqual(PF_Slab._meta.db_table, 'tblHR_PF_Slab')
        self.assertFalse(PF_Slab._meta.managed)

    def test_single_active_slab_on_create(self):
        """
        Test that only one slab can be active at a time during creation.
        Model's clean method should prevent creating a new active slab if one already exists.
        """
        # A slab with active=True already exists from setUpTestData
        new_active_slab = PF_Slab(pf_employee='10.000', pf_company='5.000', active=True)
        with self.assertRaises(ValidationError) as cm:
            new_active_slab.full_clean() # Calls model's clean method
        self.assertIn("Cannot create a new active PF Slab. An active slab already exists.", str(cm.exception))

    def test_single_active_slab_on_update(self):
        """
        Test that only one slab can be active at a time when updating.
        Model's clean method should prevent setting an inactive slab to active
        if another active slab exists.
        """
        # slab_to_activate is inactive, slab_active is active
        slab_to_activate = PF_Slab.objects.create(pf_employee='10.000', pf_company='5.000', active=False)
        slab_active = PF_Slab.objects.get(active=True) # Get the one created in setUpTestData

        slab_to_activate.active = True
        with self.assertRaises(ValidationError) as cm:
            slab_to_activate.full_clean() # Calls model's clean method
        self.assertIn("Cannot make this PF Slab active. Another slab is already active.", str(cm.exception))

        # Test that the currently active slab can be saved (remaining active or becoming inactive)
        slab_active.pf_employee = '125.000' # Update a field
        slab_active.full_clean() # Should not raise error
        slab_active.save() # Should not raise error
        self.assertTrue(PF_Slab.objects.get(id=slab_active.id).active) # Should still be active

        slab_active.active = False # Now deactivate it
        slab_active.full_clean() # Should not raise error
        slab_active.save() # Should not raise error
        self.assertFalse(PF_Slab.objects.get(id=slab_active.id).active) # Should now be inactive

    def test_model_save_deactivates_others(self):
        """
        Test that saving an active slab deactivates any other existing active slabs.
        This tests the model's save() method.
        """
        # Create two active slabs (will fail due to clean, so manually bypass clean for this test)
        # We need to ensure PF_Slab.objects.get(active=True) works as expected.
        # Let's ensure only one active slab exists before testing the save logic.
        PF_Slab.objects.all().update(active=False) # Ensure no active slabs initially
        
        slab1 = PF_Slab.objects.create(pf_employee='100', pf_company='50', active=True)
        slab2 = PF_Slab.objects.create(pf_employee='110', pf_company='55', active=False)
        slab3 = PF_Slab.objects.create(pf_employee='130', pf_company='65', active=False)

        self.assertEqual(PF_Slab.objects.filter(active=True).count(), 1)
        self.assertTrue(PF_Slab.objects.get(pk=slab1.pk).active)

        # Now, activate slab2. This should deactivate slab1.
        slab2.active = True
        slab2.save() # This calls the custom save method

        self.assertTrue(PF_Slab.objects.get(pk=slab2.pk).active)
        self.assertFalse(PF_Slab.objects.get(pk=slab1.pk).active)
        self.assertEqual(PF_Slab.objects.filter(active=True).count(), 1)


class PF_SlabFormTest(TestCase):
    """
    Unit tests for the PF_SlabForm, covering field validation.
    """
    @classmethod
    def setUpTestData(cls):
        PF_Slab.objects.create(pf_employee='120.500', pf_company='60.250', active=True) # An active slab for testing

    def test_valid_form_data(self):
        """Test form with all valid data."""
        data = {
            'pf_employee': '123.456',
            'pf_company': '789.012',
            'active': False, # Setting to false, so it should be valid
        }
        form = PF_SlabForm(data=data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors.as_data()}")

    def test_invalid_pf_employee_non_numeric(self):
        """Test validation for non-numeric pf_employee."""
        data = {
            'pf_employee': 'abc',
            'pf_company': '789.012',
            'active': False,
        }
        form = PF_SlabForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('pf_employee', form.errors)
        self.assertEqual(form.errors['pf_employee'], ['PF Employee must be a number with up to 15 digits and 3 decimal places.'])

    def test_invalid_pf_employee_precision(self):
        """Test validation for pf_employee with too many decimal places."""
        data = {
            'pf_employee': '123.4567',
            'pf_company': '789.012',
            'active': False,
        }
        form = PF_SlabForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('pf_employee', form.errors)
        self.assertEqual(form.errors['pf_employee'], ['PF Employee must be a number with up to 15 digits and 3 decimal places.'])

    def test_invalid_pf_company_non_numeric(self):
        """Test validation for non-numeric pf_company."""
        data = {
            'pf_employee': '123.456',
            'pf_company': 'xyz',
            'active': False,
        }
        form = PF_SlabForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('pf_company', form.errors)
        self.assertEqual(form.errors['pf_company'], ['PF Company must be a number with up to 15 digits and 3 decimal places.'])

    def test_invalid_pf_company_precision(self):
        """Test validation for pf_company with too many decimal places."""
        data = {
            'pf_employee': '123.456',
            'pf_company': '789.0123',
            'active': False,
        }
        form = PF_SlabForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('pf_company', form.errors)
        self.assertEqual(form.errors['pf_company'], ['PF Company must be a number with up to 15 digits and 3 decimal places.'])

    def test_active_field_disabled_on_create_if_active_exists(self):
        """Test that the active field is disabled for new forms if an active slab exists."""
        form = PF_SlabForm() # No instance, so it's a create form
        self.assertIn('x-bind:disabled="activeStatusDisabled"', form.fields['active'].widget.attrs['x-init'])
        self.assertIn('activeStatusDisabled = true;', form.fields['active'].widget.attrs['x-init']) # Should be disabled because an active slab exists from setUpTestData

    def test_active_field_disabled_on_edit_inactive_if_active_exists(self):
        """Test that the active field is disabled for inactive slabs if an active slab exists."""
        inactive_slab = PF_Slab.objects.create(pf_employee='10', pf_company='5', active=False)
        form = PF_SlabForm(instance=inactive_slab)
        self.assertIn('x-bind:disabled="activeStatusDisabled"', form.fields['active'].widget.attrs['x-init'])
        self.assertIn('activeStatusDisabled = true;', form.fields['active'].widget.attrs['x-init'])

    def test_active_field_enabled_on_edit_active_slab(self):
        """Test that the active field is enabled for the current active slab during edit."""
        active_slab = PF_Slab.objects.get(active=True)
        form = PF_SlabForm(instance=active_slab)
        self.assertIn('x-bind:disabled="activeStatusDisabled"', form.fields['active'].widget.attrs['x-init'])
        self.assertIn('activeStatusDisabled = false;', form.fields['active'].widget.attrs['x-init'])


class PF_SlabViewsTest(TestCase):
    """
    Integration tests for PF_Slab views, covering CRUD operations and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        cls.active_slab = PF_Slab.objects.create(pf_employee='120.500', pf_company='60.250', active=True)
        cls.inactive_slab = PF_Slab.objects.create(pf_employee='100.000', pf_company='50.000', active=False)

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        """Test PF_Slab list view renders correctly."""
        response = self.client.get(reverse('pf_slab_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/pf_slab/list.html')
        self.assertContains(response, 'PF Slabs')
        self.assertContains(response, 'Add New PF Slab')

    def test_table_partial_view(self):
        """Test PF_Slab table partial view loads via HTMX."""
        response = self.client.get(reverse('pf_slab_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/pf_slab/_pf_slab_table.html')
        self.assertContains(response, 'PF Employee Share') # Table header
        self.assertContains(response, str(self.active_slab.pf_employee))
        self.assertContains(response, str(self.inactive_slab.pf_employee))

    def test_create_view_get_htmx(self):
        """Test GET request for create view (HTMX)."""
        response = self.client.get(reverse('pf_slab_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/pf_slab/_pf_slab_form.html')
        self.assertContains(response, 'Add PF Slab')
        self.assertContains(response, 'name="pf_employee"')
        self.assertContains(response, 'name="pf_company"')
        self.assertContains(response, 'name="active"')

    def test_create_view_post_valid_htmx(self):
        """Test POST request for create view with valid data (HTMX)."""
        data = {
            'pf_employee': '150.000',
            'pf_company': '75.000',
            'active': 'off', # Should be off as one is already active
        }
        # Simulate a form submission with HTMX headers
        response = self.client.post(reverse('pf_slab_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content, successful HTMX response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPF_SlabList')
        
        # Verify object was created
        self.assertTrue(PF_Slab.objects.filter(pf_employee='150.000').exists())
        self.assertEqual(PF_Slab.objects.filter(pf_employee='150.000').first().active, False) # Should be inactive

    def test_create_view_post_invalid_htmx(self):
        """Test POST request for create view with invalid data (HTMX)."""
        data = {
            'pf_employee': 'abc', # Invalid data
            'pf_company': '75.000',
            'active': 'off',
        }
        response = self.client.post(reverse('pf_slab_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should return form partial with errors
        self.assertTemplateUsed(response, 'hr_masters/pf_slab/_pf_slab_form.html')
        self.assertContains(response, 'PF Employee must be a number with up to 15 digits and 3 decimal places.')
        self.assertFalse(PF_Slab.objects.filter(pf_employee='abc').exists()) # Ensure not created

    def test_create_view_post_active_when_already_exists(self):
        """Test POST request to create an active slab when one already exists."""
        data = {
            'pf_employee': '200.000',
            'pf_company': '100.000',
            'active': 'on', # Try to set to active
        }
        response = self.client.post(reverse('pf_slab_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should return form partial with errors
        self.assertContains(response, 'Cannot create a new active PF Slab. An active slab already exists.')
        self.assertFalse(PF_Slab.objects.filter(pf_employee='200.000').exists())

    def test_update_view_get_htmx(self):
        """Test GET request for update view (HTMX)."""
        response = self.client.get(reverse('pf_slab_edit', args=[self.inactive_slab.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/pf_slab/_pf_slab_form.html')
        self.assertContains(response, 'Edit PF Slab')
        self.assertContains(response, str(self.inactive_slab.pf_employee))

    def test_update_view_post_valid_htmx(self):
        """Test POST request for update view with valid data (HTMX)."""
        data = {
            'pf_employee': '110.000',
            'pf_company': '55.000',
            'active': 'off',
        }
        response = self.client.post(reverse('pf_slab_edit', args=[self.inactive_slab.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPF_SlabList')
        
        updated_slab = PF_Slab.objects.get(pk=self.inactive_slab.pk)
        self.assertEqual(updated_slab.pf_employee, 110.000)
        self.assertEqual(updated_slab.active, False)

    def test_update_view_post_activate_and_deactivate_old_htmx(self):
        """
        Test that activating an inactive slab deactivates the previously active one.
        This tests the model's save method's behavior.
        """
        # Ensure the initial state: self.active_slab is active, self.inactive_slab is inactive
        self.assertTrue(PF_Slab.objects.get(pk=self.active_slab.pk).active)
        self.assertFalse(PF_Slab.objects.get(pk=self.inactive_slab.pk).active)
        self.assertEqual(PF_Slab.objects.filter(active=True).count(), 1)

        data = {
            'pf_employee': str(self.inactive_slab.pf_employee), # Keep same value
            'pf_company': str(self.inactive_slab.pf_company),   # Keep same value
            'active': 'on', # Set inactive slab to active
        }
        response = self.client.post(reverse('pf_slab_edit', args=[self.inactive_slab.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Successful update
        self.assertIn('HX-Trigger', response.headers)

        # Verify new slab is active and old active slab is deactivated
        updated_inactive_slab = PF_Slab.objects.get(pk=self.inactive_slab.pk)
        old_active_slab = PF_Slab.objects.get(pk=self.active_slab.pk)
        self.assertTrue(updated_inactive_slab.active)
        self.assertFalse(old_active_slab.active)
        self.assertEqual(PF_Slab.objects.filter(active=True).count(), 1)


    def test_update_view_post_invalid_htmx(self):
        """Test POST request for update view with invalid data (HTMX)."""
        data = {
            'pf_employee': 'invalid_num',
            'pf_company': '55.000',
            'active': 'off',
        }
        response = self.client.post(reverse('pf_slab_edit', args=[self.inactive_slab.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/pf_slab/_pf_slab_form.html')
        self.assertContains(response, 'PF Employee must be a number with up to 15 digits and 3 decimal places.')
        
        # Ensure data was not updated
        original_slab = PF_Slab.objects.get(pk=self.inactive_slab.pk)
        self.assertEqual(original_slab.pf_employee, self.inactive_slab.pf_employee)

    def test_delete_view_get_htmx(self):
        """Test GET request for delete view (HTMX)."""
        response = self.client.get(reverse('pf_slab_delete', args=[self.inactive_slab.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/pf_slab/_pf_slab_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, str(self.inactive_slab.pf_employee))

    def test_delete_view_post_htmx(self):
        """Test POST request for delete view (HTMX)."""
        slab_to_delete = PF_Slab.objects.create(pf_employee='999.000', pf_company='888.000', active=False)
        response = self.client.post(reverse('pf_slab_delete', args=[slab_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPF_SlabList')
        
        # Verify object was deleted
        self.assertFalse(PF_Slab.objects.filter(pk=slab_to_delete.pk).exists())
```

---

## Step 5: HTMX and Alpine.js Integration

The provided Django templates extensively use HTMX and Alpine.js to deliver a modern, dynamic user experience without requiring full page reloads or complex JavaScript frameworks.

**HTMX Implementation:**
- **List View (`list.html`):** The `pf_slabTable-container` `div` uses `hx-get` to fetch the table content from `{% url 'pf_slab_table' %}` and `hx-trigger="load, refreshPF_SlabList from:body"` to automatically load the table on page load and refresh it whenever the `refreshPF_SlabList` custom event is triggered (e.g., after a successful CRUD operation).
- **CRUD Operations:** "Add", "Edit", and "Delete" buttons use `hx-get` to fetch their respective forms/confirmations into the `modalContent` `div`.
- **Form Submissions:** Forms inside the modal (`_pf_slab_form.html`, `_pf_slab_confirm_delete.html`) use `hx-post` to submit data. `hx-swap="none"` is used, as the view will return a `204 No Content` response with an `HX-Trigger` header to signal the client to refresh the list, and Alpine.js will handle modal closing.
- **`HX-Trigger`:** After successful form submissions (Create, Update, Delete), Django views return an `HX-Trigger` header (`refreshPF_SlabList`), telling HTMX to re-fetch the list table, ensuring the UI is always up-to-date.

**Alpine.js Implementation:**
- **Modal Control:** The main `modal` `div` in `list.html` uses `x-data="{ show: false }"` and `x-show="show"` to manage its visibility.
    - Custom events (`open-modal`, `close-modal`) are dispatched (`$dispatch`) by HTMX elements (e.g., `on click add .is-active to #modal`) and caught by Alpine.js (`x-on:open-modal.window="show = true"`).
    - `x-transition` attributes provide smooth opening/closing animations.
    - `x-on:click.away` allows clicking outside the modal content to close it.
    - Logic for controlling body overflow when modal is open/closed.
    - Logic to keep the modal open if form submission via HTMX returns a 400 (validation error).
- **Dynamic Checkbox (Active field):** In `_pf_slab_form.html`, `x-data="{ activeStatus: ..., activeStatusDisabled: ... }"` is used to bind the `active` checkbox's `checked` state and `disabled` attribute to Alpine.js variables, allowing dynamic UI feedback based on the form's initial data (as determined by the Django form's `__init__` method).

**DataTables Integration:**
- The `_pf_slab_table.html` partial includes a `script` block that initializes DataTables on the `pf_slabTable`.
- It sets `pageLength`, `lengthMenu`, and `responsive` options, providing professional data presentation and interactivity (searching, sorting, pagination).
- It also disables sorting for the "SN" and "Actions" columns, as these are not data fields from the model.
- A conditional initialization ensures DataTables is only applied if there's actual data, preventing errors on empty tables.

**Final Notes:**

- **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names derived from the ASP.NET application (e.g., `PF_Slab`, `pf_slab_list`, `tblHR_PF_Slab`, `hr_masters`).
- **DRY Templates:** Use of `_pf_slab_table.html`, `_pf_slab_form.html`, and `_pf_slab_confirm_delete.html` as partials ensures that common UI components are reusable and maintainable.
- **Business Logic in Models:** The `save()` and `clean()` methods in the `PF_Slab` model encapsulate the critical "at most one active slab" business rule, keeping views lean and focused on request/response handling.
- **Comprehensive Tests:** Robust unit and integration tests are provided to ensure the correctness and reliability of the migrated application, covering both data logic and UI interactions.
- **AI-Assisted Automation:** This detailed plan and generated code provide a blueprint that can be fed into AI-assisted code generation tools to accelerate the actual migration process, focusing on automated conversion of schema, UI elements, and business logic patterns. Human oversight remains crucial for refining business rules and ensuring comprehensive test coverage.