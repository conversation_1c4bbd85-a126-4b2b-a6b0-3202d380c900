## ASP.NET to Django Conversion Script: HR Reports Dashboard

This document outlines a modernization plan for your ASP.NET Dashboard to a modern Django-based solution. The original ASP.NET code for `Dashboard.aspx` appears to be a placeholder or container page, likely designed to integrate various reporting components or display summary data. Since it lacks explicit database interactions or UI elements, we will infer a common dashboard scenario: displaying a list of "HR Reports" with basic management capabilities.

This plan focuses on systematically transforming the application structure, leveraging Django's robust features, and incorporating a dynamic frontend using HTMX and Alpine.js without requiring complex JavaScript.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`hr_reports`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code does not explicitly define a database schema. Based on the page `Module_HR_Reports_Dashboard`, we will infer a conceptual `HR Report Summary` table to demonstrate a typical dashboard data source.

- **Inferred Table Name:** `tblHRReportSummary`
- **Inferred Columns:**
    - `ReportID` (Primary Key, integer)
    - `ReportName` (String, e.g., "Employee Turnover Report")
    - `GeneratedDate` (Datetime)
    - `Status` (String, e.g., "Completed", "Pending", "Failed")

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The original ASP.NET code has an empty `Page_Load` and no other explicit server-side logic or UI controls. However, a dashboard typically involves "Read" functionality (displaying data). For a comprehensive demonstration of a modern Django application, we will implement full CRUD (Create, Read, Update, Delete) operations for the inferred `HR Report Summary` data.

- **Create:** Ability to add new report entries.
- **Read:** Display a list of all HR report summaries.
- **Update:** Ability to edit existing report entries.
- **Delete:** Ability to remove report entries.
- **Validation Logic:** Basic field validation (e.g., `ReportName` is required).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET markup shows only `asp:Content` placeholders, indicating a master page structure. No specific UI controls (like `GridView`, `TextBox`, `Button`) are present. We will infer the necessary Django equivalents to present and manage the `HR Report Summary` data.

- **Data Presentation:** A DataTables-enabled table to display the list of `HR Report Summaries`.
- **User Input:** Modals containing forms (using `TextBox` and `DateTime` picker equivalents) for adding and editing report entries.
- **Actions:** Buttons for triggering "Add New Report", "Edit", and "Delete" actions, integrated with HTMX for dynamic interactions.
- **Frontend Interaction:** HTMX for loading forms into modals and refreshing the DataTables, combined with Alpine.js for modal state management.

---

### Step 4: Generate Django Code

We will create a new Django application named `hr_reports` to encapsulate this module.

#### 4.1 Models (`hr_reports/models.py`)

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The model will map directly to the existing `tblHRReportSummary` table. It will include methods for any business logic related to HR reports, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone # Import timezone for default values if needed

class Report(models.Model):
    """
    Represents a summary entry for HR Reports on the dashboard.
    Maps to the existing tblHRReportSummary table in the database.
    """
    report_id = models.IntegerField(db_column='ReportID', primary_key=True)
    name = models.CharField(db_column='ReportName', max_length=255, verbose_name="Report Name")
    generated_at = models.DateTimeField(db_column='GeneratedDate', verbose_name="Generated Date")
    status = models.CharField(db_column='Status', max_length=50, verbose_name="Status")

    class Meta:
        managed = False  # Important: Tells Django not to manage table creation/deletion
        db_table = 'tblHRReportSummary'
        verbose_name = 'HR Report'
        verbose_name_plural = 'HR Reports'
        ordering = ['-generated_at'] # Default ordering for display

    def __str__(self):
        return self.name

    def is_completed(self):
        """
        Business logic: Checks if the report status is 'Completed'.
        """
        return self.status.lower() == 'completed'

    def update_status(self, new_status):
        """
        Business logic: Updates the report status.
        """
        valid_statuses = ['Completed', 'Pending', 'Failed', 'Processing']
        if new_status not in valid_statuses:
            raise ValueError(f"Invalid status: {new_status}. Must be one of {', '.join(valid_statuses)}")
        self.status = new_status
        self.save()
        return True

    def get_status_badge_color(self):
        """
        Business logic: Returns a Tailwind CSS class for status visualization.
        """
        status_map = {
            'completed': 'bg-green-100 text-green-800',
            'pending': 'bg-yellow-100 text-yellow-800',
            'failed': 'bg-red-100 text-red-800',
            'processing': 'bg-blue-100 text-blue-800',
        }
        return status_map.get(self.status.lower(), 'bg-gray-100 text-gray-800')

```

#### 4.2 Forms (`hr_reports/forms.py`)

**Task:** Define a Django form for user input for `Report` objects.

**Instructions:**
A `ModelForm` will be used for convenience, automatically handling field mapping and basic validation. Widgets will be applied for Tailwind CSS styling.

```python
from django import forms
from .models import Report

class ReportForm(forms.ModelForm):
    """
    Form for creating and updating Report instances.
    """
    generated_at = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        initial=forms.DateTimeInput().initial
    )

    class Meta:
        model = Report
        fields = ['name', 'generated_at', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(
                choices=[('Completed', 'Completed'), ('Pending', 'Pending'), ('Failed', 'Failed'), ('Processing', 'Processing')],
                attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
            ),
        }
        labels = {
            'name': 'Report Name',
            'generated_at': 'Generated Date',
            'status': 'Status',
        }
    
    def clean_name(self):
        """
        Custom validation for the report name.
        """
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise forms.ValidationError("Report Name must be at least 3 characters long.")
        return name
```

#### 4.3 Views (`hr_reports/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), keeping them thin and delegating business logic to models.

**Instructions:**
Views will handle rendering templates and processing form submissions. HTMX responses (`HttpResponse(status=204)`) will be used to trigger client-side events for seamless updates without full page reloads. A dedicated `TablePartialView` will be added to render the DataTables content dynamically.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Report
from .forms import ReportForm

class ReportListView(ListView):
    """
    Displays a list of all HR Reports. This view serves the main dashboard page.
    """
    model = Report
    template_name = 'hr_reports/report/list.html'
    context_object_name = 'reports' # Plural context name

class ReportTablePartialView(ListView):
    """
    Renders only the DataTables portion of the HR Reports list.
    Accessed via HTMX to dynamically update the table.
    """
    model = Report
    template_name = 'hr_reports/report/_report_table.html'
    context_object_name = 'reports' # Plural context name

    def get_queryset(self):
        # Example of applying ordering if needed, but model Meta already handles it.
        return super().get_queryset()

class ReportCreateView(CreateView):
    """
    Handles creating a new HR Report. Renders and processes the form in a modal.
    """
    model = Report
    form_class = ReportForm
    template_name = 'hr_reports/report/_report_form.html' # Partial template for modal
    success_url = reverse_lazy('hr_reports:report_list') # Not directly used for HTMX success

    def form_valid(self, form):
        # Business logic can be called here if it's not model-specific
        # For example, if a specific action needs to happen *before* saving
        response = super().form_valid(form)
        messages.success(self.request, 'HR Report added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshReportList":true, "closeModal":true}'
                }
            )
        return response # Fallback for non-HTMX (shouldn't happen with current setup)

    def form_invalid(self, form):
        # Render the form again within the modal if validation fails
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class ReportUpdateView(UpdateView):
    """
    Handles updating an existing HR Report. Renders and processes the form in a modal.
    """
    model = Report
    form_class = ReportForm
    template_name = 'hr_reports/report/_report_form.html' # Partial template for modal
    success_url = reverse_lazy('hr_reports:report_list') # Not directly used for HTMX success

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'HR Report updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshReportList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class ReportDeleteView(DeleteView):
    """
    Handles deleting an HR Report. Renders a confirmation and processes deletion in a modal.
    """
    model = Report
    template_name = 'hr_reports/report/_report_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('hr_reports:report_list') # Not directly used for HTMX success

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'HR Report deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshReportList":true, "closeModal":true}'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring DRY principles and HTMX/Alpine.js integration.

**Instructions:**
Templates will extend `core/base.html` for consistent layout. The main list view will dynamically load the table content. Modals for CRUD operations will be handled by HTMX and Alpine.js.

**`hr_reports/templates/hr_reports/report/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">HR Reports Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'hr_reports:report_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
            aria-label="Add New Report">
            Add New Report
        </button>
    </div>
    
    <div id="reportTable-container"
         hx-trigger="load, refreshReportList from:body"
         hx-get="{% url 'hr_reports:report_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex flex-col items-center justify-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading reports...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-900 bg-opacity-60 z-50 transition-opacity duration-300 {% if not form %}hidden opacity-0{% endif %} flex items-center justify-center"
         _="on closeModal from body remove .flex from me then remove .opacity-100 from me then remove .scale-100 from #modalContent"
         x-data="{ showModal: false }"
         x-init="$watch('showModal', value => {
            if (value) { $el.classList.remove('hidden', 'opacity-0'); setTimeout(() => $el.classList.add('opacity-100'), 10); } 
            else { $el.classList.remove('opacity-100'); setTimeout(() => $el.classList.add('hidden', 'opacity-0'), 300); }
         })"
         x-show="showModal"
         @click.self="showModal = false">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full transform scale-95 transition-transform duration-300"
             @click.away="showModal = false"
             _="on load set showModal to true">
            <!-- Form content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true; },
            close() { this.isOpen = false; },
        });

        // Listen for the custom "closeModal" event triggered by HTMX
        document.body.addEventListener('closeModal', () => {
            document.getElementById('modal')._x_data_proxy.showModal = false;
        });

        // Listen for the custom "refreshReportList" event triggered by HTMX
        document.body.addEventListener('refreshReportList', () => {
            // HTMX will automatically re-fetch reportTable-container due to hx-trigger="refreshReportList from:body"
            // No explicit JS needed here unless for additional logic like toast messages.
        });
    });

    // Handle messages (Django messages framework)
    document.addEventListener('DOMContentLoaded', function() {
        const messages = document.querySelectorAll('.message');
        messages.forEach(msg => {
            setTimeout(() => {
                msg.classList.add('opacity-0', 'translate-y-4');
                msg.addEventListener('transitionend', () => msg.remove());
            }, 3000);
        });
    });
</script>
{% endblock %}
```

**`hr_reports/templates/hr_reports/report/_report_table.html`**

```html
<div class="overflow-x-auto">
    <table id="hrReportsTable" class="min-w-full bg-white border-collapse">
        <thead>
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Report Name</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Generated Date</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for report in reports %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ report.name }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ report.generated_at|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full {{ report.get_status_badge_color }}">
                        {{ report.status }}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition-colors duration-200"
                        hx-get="{% url 'hr_reports:report_edit' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        aria-label="Edit Report">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition-colors duration-200"
                        hx-get="{% url 'hr_reports:report_delete' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        aria-label="Delete Report">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">No HR reports found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization for the dynamically loaded table
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#hrReportsTable')) {
            $('#hrReportsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
        }
    });
</script>
```

**`hr_reports/templates/hr_reports/report/_report_form.html`**

```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} HR Report</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm mt-2">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg transition-colors duration-200"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition-colors duration-200">
                Save Report
            </button>
        </div>
    </form>
</div>
```

**`hr_reports/templates/hr_reports/report/_report_confirm_delete.html`**

```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">
        Are you sure you want to delete the HR Report: 
        <span class="font-semibold text-red-700">"{{ object.name }}"</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'hr_reports:report_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg transition-colors duration-200"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition-colors duration-200">
                Delete Report
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`hr_reports/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
URLs will be structured to be clear and consistent, reflecting the CRUD operations and partial HTMX endpoints. They will be namespaced under `hr_reports`.

```python
from django.urls import path
from .views import ReportListView, ReportCreateView, ReportUpdateView, ReportDeleteView, ReportTablePartialView

app_name = 'hr_reports' # Namespace for this application's URLs

urlpatterns = [
    path('reports/', ReportListView.as_view(), name='report_list'),
    path('reports/table/', ReportTablePartialView.as_view(), name='report_table'), # HTMX endpoint for table content
    path('reports/add/', ReportCreateView.as_view(), name='report_add'),
    path('reports/edit/<int:pk>/', ReportUpdateView.as_view(), name='report_edit'),
    path('reports/delete/<int:pk>/', ReportDeleteView.as_view(), name='report_delete'),
]
```
*Note: Remember to include this `hr_reports/urls.py` in your project's main `urls.py` by adding `path('hr/', include('hr_reports.urls')),`.*

#### 4.6 Tests (`hr_reports/tests.py`)

**Task:** Write comprehensive tests for the model and views to ensure functionality and coverage.

**Instructions:**
Tests will cover model field attributes, custom methods, and all view behaviors, including standard requests and HTMX-specific interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Report

class ReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        Report.objects.create(
            report_id=1,  # Assuming ReportID is a primary key and needs a value
            name='Test HR Report 1',
            generated_at=timezone.now(),
            status='Completed'
        )
        Report.objects.create(
            report_id=2,
            name='Test HR Report 2',
            generated_at=timezone.now() - timezone.timedelta(days=1),
            status='Pending'
        )
  
    def test_report_creation(self):
        """
        Test that a Report object can be created and its fields are correct.
        """
        report = Report.objects.get(report_id=1)
        self.assertEqual(report.name, 'Test HR Report 1')
        self.assertEqual(report.status, 'Completed')
        self.assertTrue(isinstance(report.generated_at, timezone.datetime))
        self.assertEqual(str(report), 'Test HR Report 1')

    def test_name_label(self):
        """
        Test the verbose name for the 'name' field.
        """
        report = Report.objects.get(report_id=1)
        field_label = report._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'Report Name')

    def test_is_completed_method(self):
        """
        Test the custom 'is_completed' method.
        """
        report1 = Report.objects.get(report_id=1)
        report2 = Report.objects.get(report_id=2)
        self.assertTrue(report1.is_completed())
        self.assertFalse(report2.is_completed())

    def test_update_status_method(self):
        """
        Test the custom 'update_status' method and its validation.
        """
        report = Report.objects.get(report_id=2)
        self.assertEqual(report.status, 'Pending')
        report.update_status('Completed')
        self.assertEqual(report.status, 'Completed')
        with self.assertRaises(ValueError):
            report.update_status('InvalidStatus')

    def test_get_status_badge_color_method(self):
        """
        Test the custom 'get_status_badge_color' method.
        """
        report1 = Report.objects.get(report_id=1) # Completed
        report2 = Report.objects.get(report_id=2) # Pending
        self.assertEqual(report1.get_status_badge_color(), 'bg-green-100 text-green-800')
        self.assertEqual(report2.get_status_badge_color(), 'bg-yellow-100 text-yellow-800')
        
        # Test for an unknown status (should return default)
        report3 = Report.objects.create(
            report_id=3,
            name='Unknown Status Report',
            generated_at=timezone.now(),
            status='Unknown'
        )
        self.assertEqual(report3.get_status_badge_color(), 'bg-gray-100 text-gray-800')

class ReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.report1 = Report.objects.create(
            report_id=1,
            name='Annual HR Review',
            generated_at=timezone.now(),
            status='Completed'
        )
        cls.report2 = Report.objects.create(
            report_id=2,
            name='Monthly Employee Onboarding',
            generated_at=timezone.now(),
            status='Pending'
        )
    
    def setUp(self):
        # Set up a client for each test method
        self.client = Client()
        # Add a dummy response for HTMX success triggers if needed, but HTMX tests handle this.

    def test_list_view_get(self):
        """
        Test that the list view renders correctly and contains reports.
        """
        response = self.client.get(reverse('hr_reports:report_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/report/list.html')
        self.assertIn('reports', response.context)
        self.assertContains(response, self.report1.name)
        self.assertContains(response, self.report2.name)
        
    def test_table_partial_view_get(self):
        """
        Test that the table partial view renders correctly for HTMX.
        """
        response = self.client.get(reverse('hr_reports:report_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/report/_report_table.html')
        self.assertIn('reports', response.context)
        self.assertContains(response, self.report1.name)
        self.assertContains(response, self.report2.name)

    def test_create_view_get(self):
        """
        Test GET request for the create form.
        """
        response = self.client.get(reverse('hr_reports:report_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/report/_report_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add HR Report') # Check for form title

    def test_create_view_post_valid(self):
        """
        Test POST request for creating a new report with valid data.
        """
        new_report_data = {
            'report_id': 3, # Provide unique PK
            'name': 'New Employee Health Report',
            'generated_at': timezone.now().isoformat(),
            'status': 'Pending',
        }
        response = self.client.post(reverse('hr_reports:report_add'), new_report_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(Report.objects.filter(name='New Employee Health Report').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshReportList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """
        Test POST request for creating a new report with invalid data.
        """
        invalid_report_data = {
            'report_id': 4,
            'name': '', # Invalid: too short
            'generated_at': timezone.now().isoformat(),
            'status': 'Completed',
        }
        response = self.client.post(reverse('hr_reports:report_add'), invalid_report_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr_reports/report/_report_form.html')
        self.assertContains(response, 'Report Name must be at least 3 characters long.')
        self.assertFalse(Report.objects.filter(name='').exists()) # Ensure no object created

    def test_update_view_get(self):
        """
        Test GET request for the update form.
        """
        response = self.client.get(reverse('hr_reports:report_edit', args=[self.report1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/report/_report_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, self.report1.name)
        self.assertContains(response, 'Edit HR Report')

    def test_update_view_post_valid(self):
        """
        Test POST request for updating an existing report with valid data.
        """
        updated_data = {
            'report_id': self.report1.pk, # PK must be present for update
            'name': 'Updated Annual HR Review',
            'generated_at': self.report1.generated_at.isoformat(),
            'status': 'Completed',
        }
        response = self.client.post(reverse('hr_reports:report_edit', args=[self.report1.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.report1.refresh_from_db()
        self.assertEqual(self.report1.name, 'Updated Annual HR Review')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshReportList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        """
        Test POST request for updating an existing report with invalid data.
        """
        original_name = self.report1.name
        invalid_updated_data = {
            'report_id': self.report1.pk,
            'name': 'a', # Invalid: too short
            'generated_at': self.report1.generated_at.isoformat(),
            'status': 'Completed',
        }
        response = self.client.post(reverse('hr_reports:report_edit', args=[self.report1.pk]), invalid_updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/report/_report_form.html')
        self.assertContains(response, 'Report Name must be at least 3 characters long.')
        self.report1.refresh_from_db()
        self.assertEqual(self.report1.name, original_name) # Ensure name was not updated

    def test_delete_view_get(self):
        """
        Test GET request for the delete confirmation page.
        """
        response = self.client.get(reverse('hr_reports:report_delete', args=[self.report2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/report/_report_confirm_delete.html')
        self.assertContains(response, f'Are you sure you want to delete the HR Report: "{self.report2.name}"?')

    def test_delete_view_post(self):
        """
        Test POST request for deleting an existing report.
        """
        report_to_delete_pk = self.report2.pk # Use the second report so report1 is still there
        response = self.client.post(reverse('hr_reports:report_delete', args=[report_to_delete_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Report.objects.filter(pk=report_to_delete_pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshReportList', response.headers['HX-Trigger'])
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The integration is vital for a smooth, single-page application feel without heavy JavaScript frameworks.

- **HTMX for Dynamic Content:**
    - The main `list.html` uses `hx-get` to load the `_report_table.html` content initially and on `refreshReportList` events.
    - Buttons for "Add", "Edit", and "Delete" use `hx-get` to fetch the respective form/confirmation partials into the modal (`#modalContent`).
    - Form submissions use `hx-post` to send data and expect a `204 No Content` response with `HX-Trigger` headers (`refreshReportList` and `closeModal`) on success.
    - `hx-swap="outerHTML" hx-target="#modalContent"` is used on forms to replace the form content itself on validation errors, keeping errors visible within the modal.
- **Alpine.js for UI State:**
    - A simple `x-data="{ showModal: false }"` and `x-show="showModal"` is used on the modal `div` to control its visibility.
    - `on click` directives (`_`) are used to manipulate modal classes (`add .flex`, `add .opacity-100`, `add .scale-100`) for opening and closing.
    - HTMX's `HX-Trigger` combined with `_="on closeModal from body"` ensures the Alpine.js `showModal` state is updated when the HTMX form submission is successful.
    - `@click.self` and `@click.away` provide convenient ways to close the modal by clicking outside.
- **DataTables for Lists:**
    - The `_report_table.html` partial contains the `<table id="hrReportsTable">` and its associated `<script>` block for DataTables initialization. This script will run every time the partial is loaded by HTMX, ensuring the table is correctly re-initialized or updated. The `if (!$.fn.DataTable.isDataTable('#hrReportsTable'))` check prevents re-initializing on subsequent loads if the table instance already exists.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Dashboard to a modern Django application. By focusing on automated code generation based on these templates, you can significantly reduce manual effort and ensure consistency. The use of HTMX, Alpine.js, and DataTables delivers a responsive, user-friendly experience that aligns with current web development best practices, emphasizing performance and maintainability. Remember to replace any remaining generic placeholders with your specific application details as you proceed with the automated conversion.