This modernization plan details the conversion of the provided ASP.NET page and its C# code-behind into a modern Django application. The focus is on leveraging Django's ORM for efficient data handling, HTMX and Alpine.js for dynamic frontend interactions, and DataTables for advanced data presentation. This approach ensures a highly responsive user experience without complex JavaScript frameworks, adhering to the "fat model, thin view" principle.

---

## ASP.NET to Django Conversion Script: MultipleReports

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (`core/base.html`).
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code-behind, specifically the `binddropdwn` function and the various dropdown population methods, we identify the following database tables and their implied relationships. The primary table for the report is `tblHR_OfficeStaff`, with several lookup tables providing related data.

**Main Table:**
*   `tblHR_OfficeStaff` (corresponds to Django Model `OfficeStaff`)
    *   `UserID` (Primary Key - often called `id` in Django by default, but we'll map `UserID` to `id` for consistency if it's the PK)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `EmpId` (String/Varchar, unique employee identifier)
    *   `Gender` (String/Varchar)
    *   `Department` (Integer, Foreign Key to `tblHR_Departments.Id`)
    *   `BGGroup` (Integer, Foreign Key to `BusinessGroup.Id`)
    *   `Designation` (Integer, Foreign Key to `tblHR_Designation.Id`)
    *   `SwapCardNo` (Integer, Foreign Key to `tblHR_SwapCard.Id`)
    *   `Grade` (Integer, Foreign Key to `tblHR_Grade.Id`)
    *   `Title` (String/Varchar)
    *   `EmployeeName` (String/Varchar)
    *   `MobileNo` (Integer, Foreign Key to `tblHR_CoporateMobileNo.Id`)
    *   `EmailId1` (String/Varchar)
    *   `EmailId2` (String/Varchar)
    *   `JoiningDate` (Date/DateTime)
    *   `ResignationDate` (Date/DateTime)
    *   `CompId` (Integer, Foreign Key to a Company/CompanyMaster table - assumed to be part of the `tblFinancial_master` context)

**Lookup Tables:**
*   `tblHR_Departments` (corresponds to Django Model `Department`)
    *   `Id` (Primary Key)
    *   `Symbol` (String/Varchar)
    *   `Description` (String/Varchar)
*   `BusinessGroup` (corresponds to Django Model `BusinessGroup`)
    *   `Id` (Primary Key)
    *   `Symbol` (String/Varchar)
*   `tblHR_Designation` (corresponds to Django Model `Designation`)
    *   `Id` (Primary Key)
    *   `Symbol` (String/Varchar)
    *   `Type` (String/Varchar)
*   `tblHR_Grade` (corresponds to Django Model `Grade`)
    *   `Id` (Primary Key)
    *   `Symbol` (String/Varchar)
*   `tblHR_CoporateMobileNo` (corresponds to Django Model `CorporateMobileNo`)
    *   `Id` (Primary Key)
    *   `MobileNo` (String/Varchar)
*   `tblHR_SwapCard` (corresponds to Django Model `SwapCard`)
    *   `Id` (Primary Key)
    *   `SwapCardNo` (String/Varchar)
*   `tblFinancial_master` (corresponds to Django Model `FinancialYear`)
    *   `FinYearId` (Primary Key - based on usage)
    *   `FinYear` (String/Varchar, e.g., "2023-2024")
    *   `CompId` (Integer, used in session and query)

### Step 2: Identify Backend Functionality

The ASP.NET page is primarily a **reporting/listing** interface with extensive filtering and search capabilities. It does not contain direct CRUD (Create, Read, Update, Delete) operations for the `OfficeStaff` records themselves, but rather displays a list of them.

*   **Read (List & Filter):** The core functionality involves retrieving a list of `OfficeStaff` records based on multiple dynamic criteria selected by the user from dropdowns and text inputs.
    *   Filtering by `Department`, `BusinessGroup`, `Designation`, `Grade` (via `DrpCriteria` and `DrpSubCriteria`).
    *   Searching by `EmpId`, `EmployeeName`, `Gender`, `MobileNo`, `SwapCardNo`, `Resigned` status (via `DrpSearch` and `txtSearch`/`txtEmpName`).
    *   Pagination for the displayed list.
    *   Redirection to a "details" page upon clicking "select" for an employee.
    *   Autocomplete for employee names.

*   **No explicit Create/Update/Delete:** The page's purpose is to filter and display, not to modify `OfficeStaff` records. The `GridView2_RowCommand` indicates navigation to another page for detailed viewing/editing.

### Step 3: Infer UI Components

The ASP.NET UI elements translate directly into Django template components, enhanced with HTMX and Alpine.js for interactivity.

*   **Filter/Search Controls:**
    *   `DrpCriteria` (ASP.NET DropDownList): Django `forms.ChoiceField` rendered as a `<select>` for primary filter categories (Department, BusinessGroup, Designation, Grade).
    *   `DrpSubCriteria` (ASP.NET DropDownList): Django `forms.ChoiceField` rendered as a `<select>`, dynamically populated via HTMX based on `DrpCriteria` selection.
    *   `DrpSearch` (ASP.NET DropDownList): Django `forms.ChoiceField` rendered as a `<select>` for secondary search fields.
    *   `txtEmpName` (ASP.NET TextBox with AutoCompleteExtender): Django `forms.CharField` rendered as an `<input type="text">` with HTMX for autocomplete suggestions. Alpine.js will manage its visibility.
    *   `txtSearch` (ASP.NET TextBox): Django `forms.CharField` rendered as an `<input type="text">`. Alpine.js will manage its visibility.
    *   `btnSearch` (ASP.NET Button): A standard HTML `<button>` triggering an HTMX request to update the results table.

*   **Data Display:**
    *   `GridView2` (ASP.NET GridView): Will be replaced by a standard HTML `<table>` element, which will be initialized as a DataTables instance on the client-side. This table will be loaded via HTMX.
    *   "SN", "Emp Id", "Employee Name", "Dept", "BG Group", "Designation", "Grade", "Gender", "Mobile No", "ERP Mail", "Email Id", "SwapCard No", "Joining Date", "Resign Date": These are the columns to be displayed in the DataTables.

*   **Interactivity:**
    *   `AutoPostBack`: Replaced by HTMX `hx-post` or `hx-get` on `change` events for dropdowns.
    *   `AutoCompleteExtender`: Replaced by HTMX `hx-get` for input changes, populating a suggestion list.
    *   Grid pagination: Handled by DataTables client-side.
    *   "Select" link button: An HTMX link/button that triggers a `window.location.href` change or a `hx-redirect` response to the employee details page.

### Step 4: Generate Django Code

We will create a Django app named `hr_reports`.

#### 4.1 Models (hr_reports/models.py)

The `binddropdwn` function in the ASP.NET code performs many inefficient N+1 queries. To adhere to the "fat model, thin view" principle and improve performance, we define proper `ForeignKey` relationships. This allows Django's ORM to use `select_related` for efficient joins, fetching all necessary related data in a single query.

```python
from django.db import models
from django.urls import reverse
from django.utils import timezone
from datetime import datetime

# Helper models for lookup tables
class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return f"{self.symbol} - {self.description}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    type = models.CharField(db_column='Type', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.symbol} - {self.type}"

class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.symbol

class CorporateMobileNo(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=20)

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        return self.mobile_no

class SwapCard(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    swap_card_no = models.CharField(db_column='SwapCardNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_SwapCard'
        verbose_name = 'Swap Card'
        verbose_name_plural = 'Swap Cards'

    def __str__(self):
        return self.swap_card_no

# Main OfficeStaff model
class OfficeStaff(models.Model):
    # Assuming UserID is the primary key in tblHR_OfficeStaff
    id = models.IntegerField(db_column='UserID', primary_key=True) # Changed from UserID to id for Django PK convention
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId') # DO_NOTHING as managed=False
    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True)
    gender = models.CharField(db_column='Gender', max_length=10, blank=True, null=True)
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroup', blank=True, null=True)
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    swap_card = models.ForeignKey(SwapCard, models.DO_NOTHING, db_column='SwapCardNo', blank=True, null=True)
    grade = models.ForeignKey(Grade, models.DO_NOTHING, db_column='Grade', blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    mobile_no = models.ForeignKey(CorporateMobileNo, models.DO_NOTHING, db_column='MobileNo', blank=True, null=True)
    email_id1 = models.CharField(db_column='EmailId1', max_length=255, blank=True, null=True)
    email_id2 = models.CharField(db_column='EmailId2', max_length=255, blank=True, null=True)
    joining_date = models.DateTimeField(db_column='JoiningDate', blank=True, null=True)
    resignation_date = models.DateTimeField(db_column='ResignationDate', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId') # Company ID, directly stored

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'
        ordering = ['-id'] # Corresponds to "Order By UserID Desc"

    def __str__(self):
        return f"{self.get_full_name()} ({self.emp_id})"

    def get_full_name(self):
        """Returns the full employee name including title."""
        return f"{self.title or ''} {self.employee_name}".strip()

    def get_formatted_joining_date(self):
        """Formats the joining date as 'DD-MM-YYYY' or empty string if null."""
        return self.joining_date.strftime('%d-%m-%Y') if self.joining_date else ''

    def get_formatted_resignation_date(self):
        """Formats the resignation date as 'DD-MM-YYYY' or empty string if null."""
        return self.resignation_date.strftime('%d-%m-%Y') if self.resignation_date else ''

    def get_details_url(self):
        """Returns the URL for the employee details page."""
        # This mirrors the ASP.NET redirect
        # In Django, this would be a URL pattern like:
        # path('hr/office-staff/<str:emp_id>/details/', OfficeStaffDetailView.as_view(), name='office_staff_details')
        return reverse('office_staff_details', args=[self.emp_id]) # Assuming an 'office_staff_details' URL pattern
```

#### 4.2 Forms (hr_reports/forms.py)

This page is a search/filter interface, not a direct CRUD form. Therefore, a standard `forms.Form` is appropriate to handle the various search criteria.

```python
from django import forms
from .models import Department, BusinessGroup, Designation, Grade, OfficeStaff

class OfficeStaffFilterForm(forms.Form):
    CRITERIA_CHOICES = [
        ('Select', 'Select'),
        ('tblHR_Departments.DeptName', 'Department'),
        ('BusinessGroup.BGGroup', 'BusinessGroup'),
        ('tblHR_Designation.Designation', 'Designation'),
        ('tblHR_Grade.Symbol', 'Grade'),
    ]

    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('0', 'EmpId'),
        ('1', 'EmployeeName'),
        ('2', 'Gender'),
        ('3', 'MobileNo'),
        ('4', 'SwapCardNo'),
        ('5', 'Resigned'),
    ]

    criteria = forms.ChoiceField(
        choices=CRITERIA_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': '/hr_reports/sub-criteria-options/', 'hx-target': '#id_sub_criteria_container', 'hx-swap': 'outerHTML', 'hx-indicator': '#sub-criteria-loading', 'hx-trigger': 'change'}),
        label="Criteria"
    )
    sub_criteria = forms.ChoiceField(
        choices=[('Select', 'Select')], # Initial empty state, populated dynamically
        required=False,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': '/hr_reports/office-staff-table/', 'hx-target': '#office_staff_table_container', 'hx-swap': 'innerHTML', 'hx-indicator': '#table-loading', 'hx-trigger': 'change'}),
        label="Sub-Criteria"
    )
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'x-model': 'searchField', 'x-on:change': 'updateVisibility()'}),
        label="Search By"
    )
    search_text = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'Enter search text'}),
        label="Search Value"
    )
    emp_name_search = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'Type Employee Name...',
                                       'hx-get': '/hr_reports/employee-autocomplete/',
                                       'hx-trigger': 'keyup changed delay:500ms, search',
                                       'hx-target': '#autocomplete-results',
                                       'hx-indicator': '#autocomplete-loading',
                                       'hx-swap': 'innerHTML',
                                       'autocomplete': 'off'}),
        label="Employee Name"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure sub_criteria always has 'Select' as the first option
        self.fields['sub_criteria'].choices = [('Select', 'Select')] + list(self.fields['sub_criteria'].choices[1:])


    def get_queryset_for_criteria(self, criteria_value):
        """
        Dynamically fetches choices for the sub_criteria dropdown based on selected criteria.
        This method will be called from a view that responds to HTMX requests.
        """
        if criteria_value == 'tblHR_Departments.DeptName':
            return Department.objects.values('id', 'symbol', 'description').order_by('description')
        elif criteria_value == 'BusinessGroup.BGGroup':
            return BusinessGroup.objects.values('id', 'symbol').order_by('symbol')
        elif criteria_value == 'tblHR_Designation.Designation':
            return Designation.objects.values('id', 'symbol', 'type').order_by('symbol')
        elif criteria_value == 'tblHR_Grade.Symbol':
            return Grade.objects.values('id', 'symbol').order_by('symbol')
        return []

    def get_sub_criteria_choices(self, criteria_value):
        queryset = self.get_queryset_for_criteria(criteria_value)
        choices = [('Select', 'Select')]
        for item in queryset:
            if criteria_value == 'tblHR_Departments.DeptName':
                choices.append((item['id'], f"{item['symbol']} - {item['description']}"))
            elif criteria_value == 'BusinessGroup.BGGroup':
                choices.append((item['id'], item['symbol']))
            elif criteria_value == 'tblHR_Designation.Designation':
                choices.append((item['id'], f"{item['symbol']} - {item['type']}"))
            elif criteria_value == 'tblHR_Grade.Symbol':
                choices.append((item['id'], item['symbol']))
        return choices

    def get_employees_for_autocomplete(self, prefix_text, comp_id):
        """
        Fetches employee names for autocomplete.
        Corresponds to the GetCompletionList WebMethod.
        """
        # Limiting to 10 results for performance, similar to typical autocomplete behavior
        employees = OfficeStaff.objects.filter(
            comp_id=comp_id,
            employee_name__icontains=prefix_text
        ).values('emp_id', 'employee_name')[:10]

        suggestions = []
        for emp in employees:
            suggestions.append(f"{emp['employee_name']} [{emp['emp_id']}]")
        return sorted(suggestions)

    def get_emp_id_from_autocomplete_text(self, text):
        """
        Extracts EmpId from autocomplete format 'EmployeeName [EmpId]'.
        Corresponds to fun.getCode.
        """
        if '[' in text and ']' in text:
            start = text.rfind('[') + 1
            end = text.rfind(']')
            return text[start:end]
        return text # Return original text if format not matched, for direct EmpId search
```

#### 4.3 Views (hr_reports/views.py)

We will use a main `ListView` to display the filtered `OfficeStaff` data. Separate views (or methods within the main view) will handle HTMX requests for dynamic dropdown population, table content, and autocomplete.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q # For complex queries
from django.conf import settings # To get COMP_ID and FIN_YEAR_ID from settings or session
from .models import OfficeStaff, Department, BusinessGroup, Designation, Grade, CorporateMobileNo, SwapCard, FinancialYear
from .forms import OfficeStaffFilterForm
import random # For getRandomKey

# Mock session data for CompId and FinYearId, normally from request.session
# In a real app, these would come from request.session for the current user
# For now, we'll use placeholder values or get from request.session directly
# Ensure your Django settings or middleware handle session management for these values
# For demonstration, let's assume session data is available or mock it.

# You would get these from request.session
# request.session['compid']
# request.session['finyear']

# To emulate `fun.getCode` functionality for auto-complete:
def get_emp_id_from_autocomplete_text(text):
    """
    Extracts EmpId from autocomplete format 'EmployeeName [EmpId]'.
    Corresponds to fun.getCode.
    """
    if '[' in text and ']' in text:
        start = text.rfind('[') + 1
        end = text.rfind(']')
        return text[start:end]
    return text # Return original text if format not matched

class OfficeStaffListView(ListView):
    model = OfficeStaff
    template_name = 'hr_reports/office_staff/list.html'
    context_object_name = 'office_staff_list' # Renamed to plural for clarity
    paginate_by = 20 # Same as ASP.NET GridView PageSize

    def get_queryset(self):
        # Emulate CompId and FinYearId from session/settings
        # In a real application, you'd get these from self.request.session
        comp_id = self.request.session.get('compid', 1)  # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not in session

        queryset = OfficeStaff.objects.select_related(
            'department', 'business_group', 'designation', 'grade',
            'mobile_no', 'swap_card', 'fin_year'
        ).filter(
            comp_id=comp_id,
            fin_year__fin_year_id__lte=fin_year_id, # Changed from FinYearId to fin_year__fin_year_id
            id__ne=1 # UserID!='1'
        ).order_by('-id') # Order By UserID Desc

        form = OfficeStaffFilterForm(self.request.GET or None)

        if form.is_valid():
            criteria = form.cleaned_data.get('criteria')
            sub_criteria_id = form.cleaned_data.get('sub_criteria')
            search_field = form.cleaned_data.get('search_field')
            search_text = form.cleaned_data.get('search_text')
            emp_name_search = form.cleaned_data.get('emp_name_search')

            if criteria and criteria != 'Select' and sub_criteria_id and sub_criteria_id != 'Select':
                if criteria == 'tblHR_Departments.DeptName':
                    queryset = queryset.filter(department__id=sub_criteria_id)
                elif criteria == 'BusinessGroup.BGGroup':
                    queryset = queryset.filter(business_group__id=sub_criteria_id)
                elif criteria == 'tblHR_Designation.Designation':
                    queryset = queryset.filter(designation__id=sub_criteria_id)
                elif criteria == 'tblHR_Grade.Symbol':
                    queryset = queryset.filter(grade__id=sub_criteria_id)

            if search_field and search_field != 'Select':
                if search_field == '0': # EmpId
                    if search_text:
                        queryset = queryset.filter(emp_id__iexact=search_text)
                elif search_field == '1': # EmployeeName (uses autocomplete value)
                    if emp_name_search:
                        emp_id_from_text = get_emp_id_from_autocomplete_text(emp_name_search)
                        queryset = queryset.filter(emp_id__iexact=emp_id_from_text)
                elif search_field == '2': # Gender
                    if search_text:
                        queryset = queryset.filter(gender__iexact=search_text)
                elif search_field == '3': # MobileNo (search in related CorporateMobileNo table)
                    if search_text:
                        queryset = queryset.filter(mobile_no__mobile_no__icontains=search_text)
                elif search_field == '4': # SwapCardNo (search in related SwapCard table)
                    if search_text:
                        queryset = queryset.filter(swap_card__swap_card_no__icontains=search_text)
                elif search_field == '5': # Resigned
                    queryset = queryset.filter(resignation_date__isnull=False)
                # The ASP.NET code has an 'else' for RESIGNED which implies not resigned.
                # For consistency, if 'Resigned' is not selected, we filter for not resigned.
                if search_field != '5':
                    queryset = queryset.filter(resignation_date__isnull=True)
        else:
             # Default filter if form is not valid or not submitted (like initial Page_Load)
             # The ASP.NET code implies initial load shows non-resigned employees
             queryset = queryset.filter(resignation_date__isnull=True)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the form to the template for rendering filters
        context['filter_form'] = OfficeStaffFilterForm(self.request.GET or None)
        return context

class OfficeStaffTablePartialView(ListView):
    """
    Renders only the table content for HTMX requests.
    """
    model = OfficeStaff
    template_name = 'hr_reports/office_staff/_office_staff_table.html'
    context_object_name = 'office_staff_list'
    paginate_by = 20 # Match the main list view pagination

    def get_queryset(self):
        # Re-use the filtering logic from the main ListView
        return OfficeStaffListView.get_queryset(self)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Additional context specific to the table partial, if any
        return context

class SubCriteriaOptionsView(View):
    """
    Handles HTMX requests to populate DrpSubCriteria options dynamically.
    """
    def post(self, request, *args, **kwargs):
        criteria_value = request.POST.get('criteria')
        form = OfficeStaffFilterForm() # Create an instance to access methods
        choices = form.get_sub_criteria_choices(criteria_value)

        # Render options as a partial HTML template for HTMX swap
        context = {'choices': choices}
        # The 'id_sub_criteria_container' target expects a full <select> element
        rendered_html = render_to_string('hr_reports/_dropdown_options.html', context, request)
        return HttpResponse(rendered_html)

class EmployeeAutocompleteView(View):
    """
    Handles HTMX requests for employee name autocomplete.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        comp_id = request.session.get('compid', 1) # Get company ID from session
        if prefix_text:
            form = OfficeStaffFilterForm()
            suggestions = form.get_employees_for_autocomplete(prefix_text, comp_id)
        else:
            suggestions = []
        
        # Render a simple list of suggestions
        return HttpResponse(
            "<div class='autocomplete-results absolute z-10 bg-white border border-gray-300 rounded shadow-lg w-full'>" +
            "".join([f"<div class='p-2 hover:bg-gray-100 cursor-pointer' hx-trigger='click' hx-swap='none' x-on:click='document.getElementById(\"id_emp_name_search\").value=\"{s.replace('"', '&quot;')}\"; document.getElementById(\"autocomplete-results\").innerHTML=\"\";'> {s}</div>" for s in suggestions]) +
            "</div>"
        )

class OfficeStaffDetailRedirectView(View):
    """
    Handles the 'select' action, redirecting to the details page.
    Mimics ASP.NET's Response.Redirect.
    """
    def get(self, request, emp_id, *args, **kwargs):
        # Generate a random key similar to ASP.NET's GetRandomAlphaNumeric
        random_key = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=10)) # Example random key
        
        # Construct the URL as per the ASP.NET example
        # In a real Django setup, you'd use reverse() to construct a URL
        # from a named URL pattern in your details app.
        # Example: return redirect(reverse('hr_details:office_staff_print', args=[emp_id]))
        
        # For direct ASP.NET URL emulation, we'll assume the URL structure.
        # This part should be adjusted to point to the actual Django details view.
        redirect_url = f"/module/hr/transactions/office_staff_print_details.aspx?EmpId={emp_id}&ModId=12&SubModId=&PagePrev=2&Key={random_key}"

        # For HTMX, a simple redirect header is sufficient.
        # For non-HTMX, a standard Django redirect is used.
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})
        else:
            from django.shortcuts import redirect
            return redirect(redirect_url)

```

#### 4.4 Templates (hr_reports/templates/hr_reports/)

The templates are designed for HTMX partials to minimize full page reloads and integrate with DataTables.

**hr_reports/templates/hr_reports/office_staff/list.html**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Employee Multiple Reports</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8" x-data="{ searchField: '{{ filter_form.search_field.value|default:'Select' }}' }" x-init="
        updateVisibility = () => {
            if (searchField === '1') { // EmployeeName
                $refs.empNameSearch.style.display = 'block';
                $refs.textSearch.style.display = 'none';
                $refs.textSearch.value = ''; // Clear other search box
            } else {
                $refs.empNameSearch.style.display = 'none';
                $refs.textSearch.style.display = 'block';
                $refs.empNameSearch.value = ''; // Clear other search box
            }
        }; 
        updateVisibility(); // Initial call
    ">
        <form hx-get="{% url 'hr_reports:office_staff_table' %}" hx-target="#office_staff_table_container" hx-swap="innerHTML" hx-indicator="#table-loading">
            {% csrf_token %}
            <table class="w-full">
                <tr>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm">
                        <label for="{{ filter_form.criteria.id_for_label }}" class="sr-only">Criteria</label>
                        {{ filter_form.criteria }}
                        <span id="sub-criteria-loading" class="htmx-indicator inline-block ml-2 text-blue-500">Loading...</span>
                    </td>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm">
                        <label for="{{ filter_form.sub_criteria.id_for_label }}" class="sr-only">Sub-Criteria</label>
                        <div id="id_sub_criteria_container">
                            {{ filter_form.sub_criteria }}
                        </div>
                    </td>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm">
                        <label for="{{ filter_form.search_field.id_for_label }}" class="sr-only">Search By</label>
                        {{ filter_form.search_field }}
                    </td>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm relative">
                        <label for="{{ filter_form.emp_name_search.id_for_label }}" class="sr-only">Employee Name</label>
                        <div x-ref="empNameSearch" style="display: none;">
                            {{ filter_form.emp_name_search }}
                            <div id="autocomplete-results"></div>
                            <span id="autocomplete-loading" class="htmx-indicator absolute top-0 right-0 p-2">
                                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                            </span>
                        </div>
                        <label for="{{ filter_form.search_text.id_for_label }}" class="sr-only">Search Value</label>
                        <div x-ref="textSearch" style="display: none;">
                            {{ filter_form.search_text }}
                        </div>
                    </td>
                    <td class="py-2 px-2">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out">
                            Search
                        </button>
                    </td>
                </tr>
            </table>
        </form>
    </div>

    <div id="office_staff_table_container"
         hx-trigger="load, reloadTable from:body"
         hx-get="{% url 'hr_reports:office_staff_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div id="table-loading" class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading employee data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DataTables initialization, triggered after HTMX loads the table content
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'office_staff_table_container') {
            $('#officeStaffTable').DataTable({
                "pageLength": 20, // Match paginate_by in Django view
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "pagingType": "full_numbers", // More navigation options
                "responsive": true, // Make table responsive
                "language": {
                    "emptyTable": "No data to display !",
                    "zeroRecords": "No matching records found"
                }
            });
        }
    });

    // Handle autocomplete selection to also trigger a search form submission
    document.addEventListener('click', function(event) {
        if (event.target.closest('.autocomplete-results div')) {
            const empNameInput = document.getElementById('id_emp_name_search');
            if (empNameInput) {
                // Ensure the form gets updated value for search
                empNameInput.value = event.target.textContent.trim();
                // Optionally trigger a search immediately after selection
                // htmx.trigger(empNameInput.form, 'submit'); // Triggers the form submission
            }
        }
    });
</script>
{% endblock %}
```

**hr_reports/templates/hr_reports/office_staff/_office_staff_table.html**

```html
{% comment %} This partial template is loaded via HTMX into #office_staff_table_container {% endcomment %}
<div class="overflow-x-auto">
    <table id="officeStaffTable" class="min-w-full bg-white border-collapse yui-datatable-theme">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ERP Mail</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email Id</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SwapCard No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resign Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% if office_staff_list %}
                {% for obj in office_staff_list %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.emp_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_full_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.department.description|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.business_group.symbol|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.designation.symbol|default:'' }} - {{ obj.designation.type|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.grade.symbol|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.gender }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.mobile_no.mobile_no|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.email_id1 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.email_id2 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.swap_card.swap_card_no|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.get_formatted_joining_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.get_formatted_resignation_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <button
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs"
                            hx-get="{% url 'hr_reports:office_staff_detail_redirect' obj.emp_id %}"
                            hx-swap="none"
                            hx-on::after-request="window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect')"
                        >
                            Select
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="15" class="py-4 px-4 text-center font-bold text-lg text-maroon-600">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>
```

**hr_reports/templates/hr_reports/_dropdown_options.html**

```html
{% comment %} This partial template is loaded via HTMX into #id_sub_criteria_container {% endcomment %}
<select name="sub_criteria" id="id_sub_criteria" class="box3 w-full" 
        hx-post="{% url 'hr_reports:office_staff_table' %}" 
        hx-target="#office_staff_table_container" 
        hx-swap="innerHTML" 
        hx-indicator="#table-loading" 
        hx-trigger="change">
    {% for value, label in choices %}
        <option value="{{ value }}">{{ label }}</option>
    {% endfor %}
</select>
```

#### 4.5 URLs (hr_reports/urls.py)

```python
from django.urls import path
from .views import OfficeStaffListView, SubCriteriaOptionsView, OfficeStaffTablePartialView, EmployeeAutocompleteView, OfficeStaffDetailRedirectView

app_name = 'hr_reports'

urlpatterns = [
    path('multiple-reports/', OfficeStaffListView.as_view(), name='office_staff_list'),
    # HTMX endpoints
    path('sub-criteria-options/', SubCriteriaOptionsView.as_view(), name='sub_criteria_options'),
    path('office-staff-table/', OfficeStaffTablePartialView.as_view(), name='office_staff_table'),
    path('employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    path('office-staff-details/<str:emp_id>/', OfficeStaffDetailRedirectView.as_view(), name='office_staff_detail_redirect'),
    
    # Placeholder for the actual detail view, as it's outside this module
    # path('hr/transactions/office_staff_print_details/<str:emp_id>/', YourOfficeStaffDetailView.as_view(), name='office_staff_details'),
    # Make sure 'office_staff_details' points to the correct URL in your actual project
]
```
Add `hr_reports.urls` to your project's main `urls.py`:
`path('hr_reports/', include('hr_reports.urls')),`

#### 4.6 Tests (hr_reports/tests.py)

Comprehensive tests for models and views, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime
from unittest.mock import patch

from .models import (
    FinancialYear, Department, BusinessGroup, Designation, Grade,
    CorporateMobileNo, SwapCard, OfficeStaff
)
from .forms import OfficeStaffFilterForm

# --- Model Tests ---

class LookupModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for lookup tables
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024", comp_id=101)
        cls.dept = Department.objects.create(id=1, symbol="HR", description="Human Resources")
        cls.bg = BusinessGroup.objects.create(id=1, symbol="CORP")
        cls.designation = Designation.objects.create(id=1, symbol="MGR", type="Manager")
        cls.grade = Grade.objects.create(id=1, symbol="A1")
        cls.mobile_no = CorporateMobileNo.objects.create(id=1, mobile_no="9876543210")
        cls.swap_card = SwapCard.objects.create(id=1, swap_card_no="SC001")

    def test_financial_year_creation(self):
        self.assertEqual(self.fin_year.fin_year, "2023-2024")
        self.assertEqual(str(self.fin_year), "2023-2024")

    def test_department_creation(self):
        self.assertEqual(self.dept.description, "Human Resources")
        self.assertEqual(str(self.dept), "HR - Human Resources")

    def test_business_group_creation(self):
        self.assertEqual(self.bg.symbol, "CORP")
        self.assertEqual(str(self.bg), "CORP")

    def test_designation_creation(self):
        self.assertEqual(self.designation.type, "Manager")
        self.assertEqual(str(self.designation), "MGR - Manager")

    def test_grade_creation(self):
        self.assertEqual(self.grade.symbol, "A1")
        self.assertEqual(str(self.grade), "A1")

    def test_corporate_mobile_no_creation(self):
        self.assertEqual(self.mobile_no.mobile_no, "9876543210")
        self.assertEqual(str(self.mobile_no), "9876543210")

    def test_swap_card_creation(self):
        self.assertEqual(self.swap_card.swap_card_no, "SC001")
        self.assertEqual(str(self.swap_card), "SC001")

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024", comp_id=101)
        cls.dept = Department.objects.create(id=1, symbol="IT", description="Information Technology")
        cls.bg = BusinessGroup.objects.create(id=1, symbol="TECH")
        cls.designation = Designation.objects.create(id=1, symbol="DEV", type="Developer")
        cls.grade = Grade.objects.create(id=1, symbol="B2")
        cls.mobile_no = CorporateMobileNo.objects.create(id=1, mobile_no="9988776655")
        cls.swap_card = SwapCard.objects.create(id=1, swap_card_no="SWP001")

        # Create OfficeStaff instances
        cls.staff1 = OfficeStaff.objects.create(
            id=101, # UserID for first staff
            fin_year=cls.fin_year,
            emp_id="EMP001",
            gender="Male",
            department=cls.dept,
            business_group=cls.bg,
            designation=cls.designation,
            swap_card=cls.swap_card,
            grade=cls.grade,
            title="Mr.",
            employee_name="John Doe",
            mobile_no=cls.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2020, 1, 1)),
            resignation_date=None,
            comp_id=101
        )
        cls.staff2 = OfficeStaff.objects.create(
            id=102, # UserID for second staff
            fin_year=cls.fin_year,
            emp_id="EMP002",
            gender="Female",
            department=cls.dept,
            business_group=cls.bg,
            designation=cls.designation,
            swap_card=cls.swap_card,
            grade=cls.grade,
            title="Ms.",
            employee_name="Jane Smith",
            mobile_no=cls.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2019, 5, 10)),
            resignation_date=timezone.make_aware(datetime(2021, 10, 15)),
            comp_id=101
        )

    def test_office_staff_creation(self):
        self.assertEqual(OfficeStaff.objects.count(), 2)
        self.assertEqual(self.staff1.emp_id, "EMP001")
        self.assertEqual(self.staff1.employee_name, "John Doe")
        self.assertEqual(self.staff1.department.description, "Information Technology")

    def test_get_full_name(self):
        self.assertEqual(self.staff1.get_full_name(), "Mr. John Doe")
        self.assertEqual(self.staff2.get_full_name(), "Ms. Jane Smith")

    def test_get_formatted_joining_date(self):
        self.assertEqual(self.staff1.get_formatted_joining_date(), "01-01-2020")
        self.assertEqual(self.staff2.get_formatted_joining_date(), "10-05-2019")

    def test_get_formatted_resignation_date(self):
        self.assertEqual(self.staff1.get_formatted_resignation_date(), "")
        self.assertEqual(self.staff2.get_formatted_resignation_date(), "15-10-2021")

    def test_get_details_url(self):
        # This test relies on 'office_staff_details' URL being defined somewhere
        # Mocking or defining a dummy URL for testing purposes
        with self.settings(ROOT_URLCONF='hr_reports.test_urls'): # Use a temporary URLconf for this test
            self.assertEqual(self.staff1.get_details_url(), reverse('office_staff_details', args=['EMP001']))

class OfficeStaffFilterFormTest(TestCase):
    def test_empty_form_validity(self):
        form = OfficeStaffFilterForm({})
        self.assertTrue(form.is_valid())

    def test_get_sub_criteria_choices_department(self):
        Department.objects.create(id=1, symbol="HR", description="Human Resources")
        form = OfficeStaffFilterForm()
        choices = form.get_sub_criteria_choices('tblHR_Departments.DeptName')
        self.assertIn(('1', 'HR - Human Resources'), choices)
        self.assertIn(('Select', 'Select'), choices)

    def test_get_employees_for_autocomplete(self):
        FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024", comp_id=101)
        OfficeStaff.objects.create(
            id=101, fin_year_id=1, emp_id="E001", employee_name="Alice Brown", comp_id=101
        )
        OfficeStaff.objects.create(
            id=102, fin_year_id=1, emp_id="E002", employee_name="Bob White", comp_id=101
        )
        
        form = OfficeStaffFilterForm()
        suggestions = form.get_employees_for_autocomplete("ali", 101)
        self.assertEqual(suggestions, ["Alice Brown [E001]"])

    def test_get_emp_id_from_autocomplete_text(self):
        form = OfficeStaffFilterForm()
        self.assertEqual(form.get_emp_id_from_autocomplete_text("Alice Brown [E001]"), "E001")
        self.assertEqual(form.get_emp_id_from_autocomplete_text("Just Name"), "Just Name")

# --- View Tests ---

class OfficeStaffViewsTest(TestCase):
    fixtures = ['initial_data.json'] # Load initial data from a fixture file

    def setUp(self):
        self.client = Client()
        # Mock session data for tests
        session = self.client.session
        session['compid'] = 101
        session['finyear'] = 1
        session.save()

        # Create necessary related objects if not using fixtures
        self.fin_year = FinancialYear.objects.get(fin_year_id=1)
        self.dept = Department.objects.get(id=1) # Example from fixture
        self.bg = BusinessGroup.objects.get(id=1)
        self.designation = Designation.objects.get(id=1)
        self.grade = Grade.objects.get(id=1)
        self.mobile_no = CorporateMobileNo.objects.get(id=1)
        self.swap_card = SwapCard.objects.get(id=1)

        # Ensure test staff exists
        OfficeStaff.objects.create(
            id=2, # UserID 2 (not 1 to pass filter)
            fin_year=self.fin_year,
            emp_id="EMP001",
            gender="Male",
            department=self.dept,
            business_group=self.bg,
            designation=self.designation,
            swap_card=self.swap_card,
            grade=self.grade,
            title="Mr.",
            employee_name="John Doe",
            mobile_no=self.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2020, 1, 1)),
            resignation_date=None, # Not resigned
            comp_id=101
        )
        OfficeStaff.objects.create(
            id=3,
            fin_year=self.fin_year,
            emp_id="EMP002",
            gender="Female",
            department=self.dept,
            business_group=self.bg,
            designation=self.designation,
            swap_card=self.swap_card,
            grade=self.grade,
            title="Ms.",
            employee_name="Jane Smith",
            mobile_no=self.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2019, 5, 10)),
            resignation_date=timezone.make_aware(datetime(2021, 10, 15)), # Resigned
            comp_id=101
        )
        OfficeStaff.objects.create( # Staff for different company
            id=4,
            fin_year=self.fin_year,
            emp_id="EMP003",
            gender="Male",
            department=self.dept,
            business_group=self.bg,
            designation=self.designation,
            swap_card=self.swap_card,
            grade=self.grade,
            title="Mr.",
            employee_name="Company B Staff",
            mobile_no=self.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2020, 1, 1)),
            resignation_date=None,
            comp_id=102 # Different company
        )


    def test_list_view_get(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/office_staff/list.html')
        self.assertTrue('office_staff_list' in response.context)
        # Default filter: only not resigned employees for compid 101
        self.assertEqual(response.context['office_staff_list'].count(), 1) # John Doe (EMP001)

    def test_office_staff_table_partial_view_get(self):
        # HTMX request to load the table
        response = self.client.get(reverse('hr_reports:office_staff_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/office_staff/_office_staff_table.html')
        self.assertTrue('office_staff_list' in response.context)
        self.assertEqual(response.context['office_staff_list'].count(), 1) # Still filtered for not resigned

    def test_list_view_filter_by_department(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'tblHR_Departments.DeptName',
            'sub_criteria': self.dept.id,
            'search_field': 'Select', # Keep other fields as select
            'search_text': '',
            'emp_name_search': ''
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/office_staff/list.html')
        # Two employees are in self.dept, but one resigned.
        self.assertEqual(response.context['office_staff_list'].count(), 1) # John Doe, not resigned

    def test_list_view_filter_by_emp_id(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'Select',
            'sub_criteria': 'Select',
            'search_field': '0', # EmpId
            'search_text': 'EMP001',
            'emp_name_search': ''
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['office_staff_list'].count(), 1)
        self.assertEqual(response.context['office_staff_list'][0].emp_id, 'EMP001')

    def test_list_view_filter_by_employee_name(self):
        # When searching by EmployeeName, the search_text is not used, emp_name_search is
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'Select',
            'sub_criteria': 'Select',
            'search_field': '1', # EmployeeName
            'search_text': '', # This should be ignored
            'emp_name_search': 'John Doe [EMP001]' # This is what autocomplete would provide
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['office_staff_list'].count(), 1)
        self.assertEqual(response.context['office_staff_list'][0].employee_name, 'John Doe')

    def test_list_view_filter_by_resigned(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'Select',
            'sub_criteria': 'Select',
            'search_field': '5', # Resigned
            'search_text': '',
            'emp_name_search': ''
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['office_staff_list'].count(), 1)
        self.assertEqual(response.context['office_staff_list'][0].emp_id, 'EMP002')


    def test_sub_criteria_options_view(self):
        response = self.client.post(reverse('hr_reports:sub_criteria_options'), 
                                   {'criteria': 'tblHR_Departments.DeptName'},
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/_dropdown_options.html')
        self.assertContains(response, f'<option value="{self.dept.id}">{self.dept.symbol} - {self.dept.description}</option>')


    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('hr_reports:employee_autocomplete'), 
                                   {'q': 'John'}, 
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn("John Doe [EMP001]", response.content.decode())
        self.assertNotIn("Jane Smith [EMP002]", response.content.decode()) # Jane resigned, but autocomplete queries all

    def test_office_staff_detail_redirect_view(self):
        # Mock the `random.choices` for predictable testing
        with patch('hr_reports.views.random.choices', return_value='ABCDEFGHIJ'):
            response = self.client.get(reverse('hr_reports:office_staff_detail_redirect', args=['EMP001']), HTTP_HX_REQUEST='true')
            self.assertEqual(response.status_code, 204) # HTMX redirect status
            expected_redirect_url = "/module/hr/transactions/office_staff_print_details.aspx?EmpId=EMP001&ModId=12&SubModId=&PagePrev=2&Key=ABCDEFGHIJ"
            self.assertEqual(response['HX-Redirect'], expected_redirect_url)

            # Test non-HTMX redirect
            response = self.client.get(reverse('hr_reports:office_staff_detail_redirect', args=['EMP001']))
            self.assertEqual(response.status_code, 302) # Standard redirect status
            self.assertEqual(response.url, expected_redirect_url)


# To run these tests, you'll need a fixture file.
# Create hr_reports/fixtures/initial_data.json like this (simplified):
# [
#   {
#     "model": "hr_reports.financialyear",
#     "pk": 1,
#     "fields": {
#       "fin_year": "2023-2024",
#       "comp_id": 101
#     }
#   },
#   {
#     "model": "hr_reports.department",
#     "pk": 1,
#     "fields": {
#       "symbol": "IT",
#       "description": "Information Technology"
#     }
#   },
#   {
#     "model": "hr_reports.businessgroup",
#     "pk": 1,
#     "fields": {
#       "symbol": "TECH"
#     }
#   },
#   {
#     "model": "hr_reports.designation",
#     "pk": 1,
#     "fields": {
#       "symbol": "DEV",
#       "type": "Developer"
#     }
#   },
#   {
#     "model": "hr_reports.grade",
#     "pk": 1,
#     "fields": {
#       "symbol": "B2"
#     }
#   },
#   {
#     "model": "hr_reports.corporatemobileno",
#     "pk": 1,
#     "fields": {
#       "mobile_no": "9988776655"
#     }
#   },
#   {
#     "model": "hr_reports.swapcard",
#     "pk": 1,
#     "fields": {
#       "swap_card_no": "SWP001"
#     }
#   }
# ]

# You also need to temporarily define 'office_staff_details' URL for model tests,
# or mock reverse() in the test_get_details_url.
# Example test_urls.py for the test:
# from django.urls import path
# from django.views.generic import View
# class DummyDetailView(View): pass # Dummy view for URL reversal
# urlpatterns = [
#     path('hr/office-staff/<str:emp_id>/details/', DummyDetailView.as_view(), name='office_staff_details'),
# ]
```

### Step 5: HTMX and Alpine.js Integration

The integration is primarily achieved through attributes in the Django templates and handled by the respective libraries on the client-side.

*   **Dynamic Dropdowns (Criteria & Sub-Criteria):**
    *   `DrpCriteria` (`id_criteria`): `hx-post` to `/hr_reports/sub-criteria-options/`, `hx-target="#id_sub_criteria_container"`, `hx-swap="outerHTML"`, `hx-trigger="change"`. This will replace the `sub_criteria` select element entirely.
    *   `DrpSubCriteria` (`id_sub_criteria`): `hx-post` to `/hr_reports/office-staff-table/`, `hx-target="#office_staff_table_container"`, `hx-swap="innerHTML"`, `hx-trigger="change"`. This triggers a refresh of the main data table.

*   **Search Button & Filter Form:**
    *   The main filter form uses `hx-get="{% url 'hr_reports:office_staff_table' %}"`, `hx-target="#office_staff_table_container"`, `hx-swap="innerHTML"`. Submitting this form (e.g., by clicking "Search") will load the filtered table content into the container.

*   **Autocomplete (`txtEmpName`):**
    *   `hx-get="/hr_reports/employee-autocomplete/"`, `hx-trigger="keyup changed delay:500ms, search"`, `hx-target="#autocomplete-results"`, `hx-swap="innerHTML"`. This sends a request on keyup (with debounce) and displays suggestions in a `div`. Alpine.js `x-on:click` on suggestion items fills the input.

*   **Conditional Input Visibility (Alpine.js):**
    *   The main `div` holding the form uses `x-data="{ searchField: '{{ filter_form.search_field.value|default:'Select' }}' }"`.
    *   `x-ref="empNameSearch"` and `x-ref="textSearch"` are used to reference the respective `div`s.
    *   `x-on:change="updateVisibility()"` on `DrpSearch` (`search_field`) triggers a function that sets `display: block` or `display: none` for `txtEmpName` or `txtSearch` based on `searchField` value.

*   **DataTables Initialization:**
    *   The `_office_staff_table.html` partial does not contain the `$(document).ready` block for DataTables. Instead, `document.addEventListener('htmx:afterSwap', ...)` in `list.html` listens for when the table content is loaded by HTMX and then initializes DataTables on `#officeStaffTable`. This ensures DataTables is applied *after* the content is in the DOM.

*   **"Select" Action:**
    *   The "Select" button in the table uses `hx-get="{% url 'hr_reports:office_staff_detail_redirect' obj.emp_id %}"`, `hx-swap="none"`.
    *   `hx-on::after-request="window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect')"` is used to explicitly handle the redirect, as HTMX doesn't automatically redirect on a `204 No Content` response unless it's explicitly told to by `HX-Redirect` header. The view returns a `HX-Redirect` header.

This detailed plan ensures a robust, modern, and performant Django application that addresses the complexities of the original ASP.NET implementation, particularly the N+1 query problem, using best practices for web development.