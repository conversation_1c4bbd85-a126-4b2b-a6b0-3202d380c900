## ASP.NET to Django Conversion Script: Work Order Print Module Modernization

This document outlines a comprehensive plan to migrate the provided ASP.NET `WorkOrder_Print.aspx` module to a modern Django-based solution. Our approach prioritizes automation, leveraging Django's robust ORM, Class-Based Views (CBVs), HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation. The focus is on business benefits: improved maintainability, scalability, user experience, and reduced development costs through automation-assisted migration.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is called `core/base.html`.
- Focus ONLY on component-specific code for the current module (`workorder_app`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several SQL Server tables. The `BindDataCust` method is the primary source for understanding the data model.

*   **Primary Table:** `SD_Cust_WorkOrder_Master` (referred to as `WorkOrder` in Django)
    *   Columns: `Id` (PK), `CustomerId`, `EnqId`, `PONo`, `POId` (hidden), `WONo`, `SysDate` (string, needs conversion), `FinYearId`, `SessionId` (Employee ID), `CompId`, `CloseOpen`.
*   **Related Tables (Lookups/Joins):**
    *   `tblSD_WO_Category` (referred to as `WOCategory` in Django)
        *   Columns: `CId` (PK), `Symbol`, `CName`, `CompId`. Used for `DDLTaskWOType`.
    *   `SD_Cust_Master` (referred to as `Customer` in Django)
        *   Columns: `CustomerId` (PK), `CustomerName`, `CompId`. Used for customer name lookup and autocomplete.
    *   `tblFinancial_master` (referred to as `FinancialYear` in Django)
        *   Columns: `FinYearId` (PK), `FinYear`. Used for financial year name.
    *   `tblHR_OfficeStaff` (referred to as `Employee` in Django)
        *   Columns: `EmpId` (PK), `Title`, `EmployeeName`, `CompId`. Used for generator employee name.

**Assumptions:**
*   A `Company` table exists for `CompId`, although not explicitly shown, to represent company context for `CompId` filtering.
*   `SysDate` in `SD_Cust_WorkOrder_Master` is a string formatted as `MM-DD-YYYY` which needs parsing. Django's `DateField` or `DateTimeField` is appropriate.

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page `WorkOrder_Print.aspx` primarily provides **Read** functionality with extensive filtering capabilities.

*   **Read (Filtering & Listing):**
    *   The `SearchGridView1` displays a list of work orders.
    *   Filtering by `CustomerId`, `EnqId`, `PONo`, `WONo`, and `WO Category` is supported.
    *   The `BindDataCust` method orchestrates complex SQL queries based on user selections. This will be the core logic to migrate to a Django Model Manager.
    *   Pagination and sorting are handled by `GridView` (will be replaced by DataTables).
*   **Navigation (Implicit Read/Detail):**
    *   The `LinkButton` in the `WONo` column (`SearchGridView1_RowCommand`) navigates to `WorkOrder_Print_Details.aspx`. This implies a detail view functionality that will be linked to a `WorkOrderDetailView` in Django, identified by the `Id` (primary key).
*   **Autocomplete:**
    *   The `TxtSearchValue_AutoCompleteExtender` uses a `WebMethod` `sql` for customer name suggestions. This will require a dedicated HTMX endpoint.
*   **No direct Create/Update/Delete operations are explicitly present on *this* page.** However, as per instructions, we will generate placeholder CRUD views and forms for `WorkOrder` for a complete example.

### Step 3: Infer UI Components

**Analysis:**
The page uses standard ASP.NET Web Forms controls.

*   **Data Display:**
    *   `SearchGridView1`: This is the primary data display component. It will be replaced by an HTML `<table>` managed by DataTables.js, with dynamic loading via HTMX.
*   **Input Controls for Filtering:**
    *   `DropDownList1`: `asp:DropDownList` for selecting search criteria (Customer Name, Enquiry No, PO No, WO No). This will be a standard HTML `<select>` with HTMX `hx-post` or `hx-get` to trigger filtering.
    *   `TxtSearchValue`, `txtEnqId`: `asp:TextBox` for entering search values. These will be HTML `<input type="text">` elements. `TxtSearchValue`'s autocomplete functionality will be handled by HTMX.
    *   `DDLTaskWOType`: `asp:DropDownList` for Work Order Category. This will also be an HTML `<select>` with HTMX.
*   **Action Buttons:**
    *   `btnSearch`: `asp:Button` to trigger the search. This will be an HTML `<button>` with `hx-post` to trigger filtering.
    *   `lnkButton` (inside `GridView`): `asp:LinkButton` to navigate to details. This will be an HTML `<a>` tag or `button` redirecting to the Django detail view URL.

### Step 4: Generate Django Code

We will create a Django application named `workorder_app`.

#### 4.1 Models

We will define models for `WorkOrder`, `WOCategory`, `Customer`, `FinancialYear`, `Employee`, and `Company`. The `SysDate` parsing is complex; assuming it's stored as `MM-DD-YYYY` string, we'll map it to a `CharField` and handle conversion in Python, or a `DateField` if the database truly stores it as a date type. For simplicity, we'll assume it's directly convertible to `DateField`. If it's a string, a custom model field or property for conversion might be needed.

**`workorder_app/models.py`**

```python
from django.db import models
from django.db.models import Q
from datetime import datetime

# Assuming these exist in your legacy DB
class Company(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_name = models.CharField(db_column='CompName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.comp_name or f"Company {self.comp_id}"

class WOCategory(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)
    comp = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    comp = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name or f"Customer {self.customer_id}"

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"Financial Year {self.fin_year_id}"

class Employee(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be string
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}. {self.employee_name}" if self.title else self.employee_name or f"Employee {self.emp_id}"


class WorkOrderQuerySet(models.QuerySet):
    def apply_filters(self, search_type, search_value, wo_category_id, fin_year_id, comp_id):
        qs = self.select_related('customer', 'financial_year', 'employee', 'wo_category').filter(
            comp_id=comp_id,
            close_open=False, # CloseOpen=0 means 'Open'
            fin_year_id__lte=fin_year_id # FinYearId<= (not exact match)
        ).order_by('wo_no')

        # Apply specific search filters
        if search_value:
            if search_type == '0': # Customer Name
                # Assumes TxtSearchValue returns "CustomerName [CustomerId]"
                # Need to extract CustomerId. If only name, then search by name.
                # The fun.getCode(TxtSearchValue.Text) suggests parsing a string like "CustomerName [CustomerId]"
                # For this Django ORM, we assume TxtSearchValue directly provides CustomerId or partial name.
                if '[' in search_value and ']' in search_value: # If format is "Name [ID]"
                    customer_id = search_value.split('[')[-1].strip(']')
                    qs = qs.filter(customer_id=customer_id)
                else: # Assume direct name search
                    qs = qs.filter(customer__customer_name__icontains=search_value)
            elif search_type == '1': # Enquiry No
                qs = qs.filter(enq_id__icontains=search_value)
            elif search_type == '2': # PO No
                qs = qs.filter(po_no__icontains=search_value)
            elif search_type == '3': # WO No
                qs = qs.filter(wo_no__icontains=search_value)

        # Apply WO Category filter
        if wo_category_id and wo_category_id != 'WO Category': # 'WO Category' is the default value
            qs = qs.filter(wo_category_id=wo_category_id)

        return qs

class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId', blank=True, null=True)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_id = models.CharField(db_column='POId', max_length=50, blank=True, null=True) # Hidden in ASP.NET
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    # SysDate is stored as MM-DD-YYYY string in SQL, mapping to CharField for direct storage.
    # We will add a property for proper date conversion.
    sys_date = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True) 
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', blank=True, null=True)
    comp = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    close_open = models.BooleanField(db_column='CloseOpen', default=False) # 0=Open, 1=Close

    # Relationship to WO Category, though not directly in SD_Cust_WorkOrder_Master's initial select
    # It's used in the Z filter in BindDataCust. Assuming a CId column.
    # Adding as a nullable foreign key, inferring column name.
    wo_category = models.ForeignKey(WOCategory, models.DO_NOTHING, db_column='CId', blank=True, null=True) # Assuming CId in WO Master

    objects = WorkOrderQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        
    @property
    def generated_date(self):
        try:
            # ASP.NET format: SUBSTRING(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) + 1, 2) + '-' + LEFT(SD_Cust_WorkOrder_Master.SysDate,CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) - 1) + '-' + RIGHT(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', REVERSE(SD_Cust_WorkOrder_Master.SysDate)) - 1)
            # This looks like M-D-YYYY or MM-DD-YYYY. The SQL converts to 103 (DD/MM/YYYY) and then replaces / with -.
            # So the output was DD-MM-YYYY.
            # Assuming sys_date is stored as 'MM-DD-YYYY' or similar.
            # Let's try parsing the common variants. The SQL `REPLACE(CONVERT(varchar, CONVERT(datetime, ...), 103), '/', '-')`
            # implies the final display format was DD-MM-YYYY.
            # The input SysDate format from `SD_Cust_WorkOrder_Master` appears to be 'MM-DD-YYYY'.
            return datetime.strptime(self.sys_date, '%m-%d-%Y').strftime('%d-%m-%Y')
        except (ValueError, TypeError):
            return self.sys_date # Return original if parsing fails

    def __str__(self):
        return self.wo_no or f"Work Order {self.id}"

    # Business logic methods for WorkOrder (Fat Model)
    def get_status_display(self):
        return "Open" if not self.close_open else "Close"

    def get_customer_code(self):
        return self.customer.customer_id if self.customer else ""

    def get_customer_name(self):
        return self.customer.customer_name if self.customer else ""

    def get_fin_year_display(self):
        return self.financial_year.fin_year if self.financial_year else ""

    def get_generated_by_employee_name(self):
        return str(self.employee) if self.employee else ""

```

#### 4.2 Forms

A `WorkOrderForm` is provided for standard CRUD operations, even though this ASP.NET page is primarily for listing/searching. A `SearchForm` will be used for the filtering controls.

**`workorder_app/forms.py`**

```python
from django import forms
from .models import WorkOrder, WOCategory, Customer

# This form is for the actual WorkOrder CRUD (if it were implemented on another page)
class WorkOrderForm(forms.ModelForm):
    class Meta:
        model = WorkOrder
        fields = ['customer', 'enq_id', 'po_no', 'wo_no', 'sys_date', 'financial_year', 'employee', 'close_open', 'wo_category']
        widgets = {
            'customer': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enq_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM-DD-YYYY'}),
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'close_open': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
            'wo_category': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    # Add custom validation methods here if needed
    def clean_sys_date(self):
        sys_date = self.cleaned_data.get('sys_date')
        if sys_date:
            try:
                # Attempt to parse as MM-DD-YYYY or DD-MM-YYYY, store as MM-DD-YYYY
                datetime.strptime(sys_date, '%m-%d-%Y')
            except ValueError:
                try:
                    datetime.strptime(sys_date, '%d-%m-%Y')
                    # If it was DD-MM-YYYY, convert to MM-DD-YYYY for consistency
                    parsed_date = datetime.strptime(sys_date, '%d-%m-%Y')
                    return parsed_date.strftime('%m-%d-%Y')
                except ValueError:
                    raise forms.ValidationError("Date must be in MM-DD-YYYY or DD-MM-YYYY format.")
        return sys_date


# Form for the search/filter controls on the main page
class WorkOrderSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': 'workorder_app/search_controls/', 'hx-target': '#searchControls', 'hx-swap': 'innerHTML'})
    )
    search_value = forms.CharField(
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter value'})
    )
    enq_id = forms.CharField( # This is txtEnqId in ASP.NET
        required=False,
        label="Enquiry/PO/WO No",
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter number'})
    )
    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all(),
        required=False,
        empty_label="WO Category",
        label="WO Category",
        widget=forms.Select(attrs={'class': 'box3'})
    )

    # Custom field to handle autocomplete value if needed for validation
    customer_id_from_autocomplete = forms.CharField(required=False, widget=forms.HiddenInput())

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically set visibility based on initial search_type
        search_type = self.initial.get('search_type') or self.data.get('search_type', '0')
        self.fields['search_value'].widget.attrs['class'] += ' ' + ('hidden' if search_type != '0' else '')
        self.fields['enq_id'].widget.attrs['class'] += ' ' + ('hidden' if search_type == '0' else '')

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        search_value = cleaned_data.get('search_value')
        enq_id = cleaned_data.get('enq_id')

        # Logic to ensure the correct text box is used based on search_type
        if search_type == '0': # Customer Name
            if not search_value:
                # In ASP.NET, if txtSearchValue is empty, it returns all. Replicate that behavior.
                pass 
            # If search_value contains "Name [ID]", extract the ID for filtering
            if search_value and '[' in search_value and ']' in search_value:
                customer_id_from_input = search_value.split('[')[-1].strip(']')
                try:
                    Customer.objects.get(customer_id=customer_id_from_input)
                    cleaned_data['customer_id_from_autocomplete'] = customer_id_from_input
                except Customer.DoesNotExist:
                    self.add_error('search_value', 'Invalid Customer ID found in search value.')
        else: # Enquiry, PO, WO No
            if not enq_id:
                # In ASP.NET, if txtEnqId is empty, it returns all. Replicate that behavior.
                pass 

        return cleaned_data

```

#### 4.3 Views

The core listing view will be `WorkOrderListView`. A separate `WorkOrderTablePartialView` will handle HTMX requests for table refreshes and filtering. Placeholder CRUD views are also included.

**`workorder_app/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from django.db.models import F # For column aliases in ORM if needed, though not strictly required here

from .models import WorkOrder, WOCategory, Customer # Import other models
from .forms import WorkOrderForm, WorkOrderSearchForm

# Helper to get session-like context (replace with actual session/user data)
def get_user_context(request):
    # This should come from actual session management or user profile
    # For demonstration, hardcoding example values as per ASP.NET context.
    # In a real app, this would be tied to Django's authentication and user profiles.
    return {
        'comp_id': request.session.get('compid', 1), # Default to 1
        'fin_year_id': request.session.get('finyear', 1), # Default to 1
        'session_id': request.user.emp_id if request.user.is_authenticated and hasattr(request.user, 'emp_id') else 'dummy_emp_id' # Assuming user has emp_id
    }

class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'workorder_app/workorder/list.html'
    context_object_name = 'workorders'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_context = get_user_context(self.request)
        
        # Initialize search form
        form = WorkOrderSearchForm(
            self.request.GET or {
                'search_type': '0', # Default to Customer Name search
                'wo_category': 'WO Category' # Default empty option
            },
            initial={'search_type': '0'} # Ensures initial value for search_type is set
        )
        context['search_form'] = form
        
        # Populate WO Category dropdown data
        context['wo_categories'] = WOCategory.objects.filter(comp_id=user_context['comp_id']) # Filter by company

        return context

class WorkOrderTablePartialView(ListView):
    model = WorkOrder
    template_name = 'workorder_app/workorder/_workorder_table.html'
    context_object_name = 'workorders'

    def get_queryset(self):
        user_context = get_user_context(self.request)
        search_form = WorkOrderSearchForm(self.request.GET) # Use GET for idempotent search
        
        # Default empty values
        search_type = '0'
        search_value = ''
        enq_id_value = ''
        wo_category_id = ''
        customer_id_for_filter = ''

        if search_form.is_valid():
            cleaned_data = search_form.cleaned_data
            search_type = cleaned_data.get('search_type', '0')
            search_value = cleaned_data.get('search_value', '')
            enq_id_value = cleaned_data.get('enq_id', '')
            wo_category_obj = cleaned_data.get('wo_category')
            wo_category_id = str(wo_category_obj.cid) if wo_category_obj else ''
            
            # If search type is customer name, use the parsed customer_id_from_autocomplete
            if search_type == '0':
                customer_id_for_filter = cleaned_data.get('customer_id_from_autocomplete', '')
                search_value = customer_id_for_filter # Pass the ID to the model manager
                enq_id_value = '' # Ensure enq_id_value is empty for customer name search
            else:
                search_value = enq_id_value # Use enq_id_value for other search types
                customer_id_for_filter = '' # Ensure customer_id_for_filter is empty for other searches

        # Pass filters to the custom manager method
        return WorkOrder.objects.apply_filters(
            search_type=search_type,
            search_value=search_value, # This will be either customer_id_for_filter or enq_id_value
            wo_category_id=wo_category_id,
            fin_year_id=user_context['fin_year_id'],
            comp_id=user_context['comp_id']
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # We might need to pass form instance for initial value display if filtering dynamically
        # For HTMX, this is often less critical as the form fields are managed on the main page.
        context['search_form'] = WorkOrderSearchForm(self.request.GET) # Re-initialize form for rendering if needed
        return context


class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'workorder_app/workorder/_workorder_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        user_context = get_user_context(self.request)
        form.instance.comp_id = user_context['comp_id']
        form.instance.fin_year_id = user_context['fin_year_id']
        form.instance.session_id = user_context['session_id'] # Set employee ID
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshWorkOrderList' # Trigger refresh on client
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This is for form initialization, useful for default values in dropdowns if needed.
        user_context = get_user_context(self.request)
        context['form'].fields['wo_category'].queryset = WOCategory.objects.filter(comp_id=user_context['comp_id'])
        context['form'].fields['customer'].queryset = Customer.objects.filter(comp_id=user_context['comp_id'])
        context['form'].fields['financial_year'].queryset = WorkOrder.objects.filter(comp_id=user_context['comp_id']).values_list('financial_year', flat=True).distinct() # Example of unique years for company
        context['form'].fields['employee'].queryset = Employee.objects.filter(comp_id=user_context['comp_id'])
        return context

class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'workorder_app/workorder/_workorder_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_context = get_user_context(self.request)
        context['form'].fields['wo_category'].queryset = WOCategory.objects.filter(comp_id=user_context['comp_id'])
        context['form'].fields['customer'].queryset = Customer.objects.filter(comp_id=user_context['comp_id'])
        context['form'].fields['financial_year'].queryset = WorkOrder.objects.filter(comp_id=user_context['comp_id']).values_list('financial_year', flat=True).distinct()
        context['form'].fields['employee'].queryset = Employee.objects.filter(comp_id=user_context['comp_id'])
        return context

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'workorder_app/workorder/_workorder_confirm_delete.html' # Use partial for HTMX modal
    success_url = reverse_lazy('workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

# View for autocomplete suggestions (Customer Name)
class CustomerAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        user_context = get_user_context(request)
        customers = Customer.objects.filter(
            comp_id=user_context['comp_id'],
            customer_name__icontains=query
        )[:10] # Limit results
        
        results = []
        for customer in customers:
            results.append({
                'id': customer.customer_id,
                'text': f"{customer.customer_name} [{customer.customer_id}]"
            })
        return JsonResponse(results, safe=False)


# View to render partial search controls based on dropdown selection
class SearchControlsPartialView(View):
    def get(self, request, *args, **kwargs):
        search_type = request.GET.get('search_type', '0')
        form = WorkOrderSearchForm(initial={'search_type': search_type})
        
        # Dynamically control visibility in the rendered partial
        if search_type == '0': # Customer Name
            form.fields['search_value'].widget.attrs['class'] = form.fields['search_value'].widget.attrs['class'].replace(' hidden', '')
            form.fields['enq_id'].widget.attrs['class'] += ' hidden'
        else: # Enquiry No, PO No, WO No
            form.fields['enq_id'].widget.attrs['class'] = form.fields['enq_id'].widget.attrs['class'].replace(' hidden', '')
            form.fields['search_value'].widget.attrs['class'] += ' hidden'

        context = {'search_form': form}
        return render(request, 'workorder_app/workorder/_search_controls.html', context)


# WorkOrder detail view placeholder
class WorkOrderDetailView(View):
    def get(self, request, pk):
        work_order = get_object_or_404(WorkOrder, pk=pk)
        # This would render a detail template or redirect as appropriate
        # For simplicity, returning a JSON response. In reality, it would render
        # 'workorder_app/workorder/detail.html'
        return JsonResponse({
            'id': work_order.id,
            'wo_no': work_order.wo_no,
            'customer_name': work_order.get_customer_name(),
            'enq_id': work_order.enq_id,
            'po_no': work_order.po_no,
            'generated_date': work_order.generated_date,
            'status': work_order.get_status_display()
        })
```

#### 4.4 Templates

All templates will extend `core/base.html` and use HTMX for dynamic content updates.

**`workorder_app/templates/workorder_app/workorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Work Order - Print{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Work Order - Print</h2>
        
        <form id="searchForm" hx-get="{% url 'workorder_table' %}" hx-target="#workorderTable-container" hx-swap="innerHTML" hx-indicator="#table-loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end mb-4">
                <div>
                    <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_type }}
                </div>
                <div id="searchControls" class="col-span-1 md:col-span-2 flex items-end gap-4">
                    {% include 'workorder_app/workorder/_search_controls.html' with search_form=search_form %}
                </div>
                <div>
                    <label for="{{ search_form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700">WO Category</label>
                    {{ search_form.wo_category }}
                </div>
                <div>
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-gray-800">Work Orders List</h3>
        <button 
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>
    
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div id="table-loading-indicator" class="text-center htmx-indicator">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-auto my-auto max-h-screen overflow-y-auto">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup if needed for complex UI states
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderPage', () => ({
            openModal: false,
            // You can add more state management here for the page
        }));
    });

    // Event listener for HTMX triggering a refresh
    document.body.addEventListener('refreshWorkOrderList', function() {
        console.log('refreshWorkOrderList triggered, reloading table...');
        // HTMX automatically re-triggers the hx-get on #workorderTable-container
        // due to 'refreshWorkOrderList from:body' trigger.
        // No explicit JS needed here, just for logging/debugging.
    });

    // Close modal on HTMX 204 response (successful form submission)
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Specific JS for DataTables initialization after HTMX swap
    document.body.addEventListener('htmx:afterSettle', function(evt) {
        if (evt.target.id === 'workorderTable-container') {
            const table = $('#workorderTable');
            if (table.length && !$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 20, // As per ASP.NET GridView PageSize
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "destroy": true, // Allow reinitialization
                    "responsive": true,
                    "language": {
                        "emptyTable": "{{ search_form.is_valid and workorders.count == 0 | yesno:'No matching records found!,No data to display !' }}"
                    }
                });
            }
        }
    });

    // Initialize the search controls on page load to set correct visibility
    document.addEventListener('DOMContentLoaded', function() {
        const searchTypeSelect = document.getElementById('id_search_type');
        const searchControlsDiv = document.getElementById('searchControls');
        
        const updateSearchControls = () => {
            const searchType = searchTypeSelect.value;
            const searchInput = searchControlsDiv.querySelector('#id_search_value');
            const enqIdInput = searchControlsDiv.querySelector('#id_enq_id');

            if (searchInput && enqIdInput) {
                if (searchType === '0') {
                    searchInput.classList.remove('hidden');
                    enqIdInput.classList.add('hidden');
                } else {
                    searchInput.classList.add('hidden');
                    enqIdInput.classList.remove('hidden');
                }
            }
        };

        // Event listener for search type change
        searchTypeSelect.addEventListener('change', updateSearchControls);

        // Initial call to set visibility based on loaded state (e.g., from GET params)
        updateSearchControls();

        // Setup Autocomplete for Customer Name field
        const customerSearchInput = document.getElementById('id_search_value');
        if (customerSearchInput) {
            customerSearchInput.setAttribute('hx-get', '{% url "customer_autocomplete" %}');
            customerSearchInput.setAttribute('hx-trigger', 'keyup changed delay:500ms');
            customerSearchInput.setAttribute('hx-target', '#customer-suggestions');
            customerSearchInput.setAttribute('hx-swap', 'outerHTML');
            customerSearchInput.setAttribute('name', 'search_value'); // Ensure correct name for form submission
            
            // Add a datalist for suggestions (basic autocomplete, can be enhanced with Alpine.js)
            customerSearchInput.setAttribute('list', 'customer-suggestions-list');
            const dataList = document.createElement('datalist');
            dataList.id = 'customer-suggestions-list';
            customerSearchInput.parentNode.appendChild(dataList);

            // HTMX response for autocomplete will swap this datalist
            document.body.addEventListener('htmx:afterSwap', function(evt) {
                if (evt.detail.target.id === 'customer-suggestions') {
                    const newDatalist = document.getElementById('customer-suggestions-list');
                    if (newDatalist) {
                        dataList.innerHTML = newDatalist.innerHTML; // Update the real datalist
                        newDatalist.remove(); // Remove the temporary one from swap
                    }
                }
            });
        }
    });

</script>
{% endblock %}
```

**`workorder_app/templates/workorder_app/workorder/_workorder_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="workorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No.</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PONo</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for wo in workorders %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-center">{{ wo.get_fin_year_display }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-left">{{ wo.get_customer_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-center">{{ wo.get_customer_code }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-center">{{ wo.enq_id }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-left">{{ wo.po_no }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-center">
                    <a href="{% url 'workorder_detail' wo.pk %}" class="text-blue-600 hover:text-blue-800 hover:underline">
                        {{ wo.wo_no }}
                    </a>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-center">{{ wo.generated_date }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-center">{{ wo.get_status_display }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-left">{{ wo.get_generated_by_employee_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-center">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                        hx-get="{% url 'workorder_edit' wo.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'workorder_delete' wo.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <!-- DataTable's empty message will handle this, but for non-JS scenarios: -->
            <tr>
                <td colspan="11" class="py-3 px-6 text-center text-gray-500">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Autocomplete suggestions container -->
<div id="customer-suggestions"></div>
```

**`workorder_app/templates/workorder_app/workorder/_search_controls.html`**

```html
{% comment %}
    This template is dynamically swapped by HTMX based on search_type selection.
    It renders the correct input field (txtEnqId or TxtSearchValue)
{% endcomment %}
<div>
    <label for="{{ search_form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">Search Value</label>
    {{ search_form.search_value }}
</div>
<div>
    <label for="{{ search_form.enq_id.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">Enquiry/PO/WO No</label>
    {{ search_form.enq_id }}
</div>

<!-- DataList for customer autocomplete suggestions -->
<datalist id="customer-suggestions-list">
    {% for result in autocomplete_results %}
        <option value="{{ result.text }}">{{ result.text }}</option>
    {% endfor %}
</datalist>
```

**`workorder_app/templates/workorder_app/workorder/_workorder_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Save
            </button>
        </div>
    </form>
</div>
```

**`workorder_app/templates/workorder_app/workorder/_workorder_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-5 text-gray-700">Are you sure you want to delete Work Order <strong>{{ workorder.wo_no }}</strong>?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

Define URL patterns for all views, including HTMX-specific endpoints.

**`workorder_app/urls.py`**

```python
from django.urls import path
from .views import (
    WorkOrderListView, WorkOrderTablePartialView,
    WorkOrderCreateView, WorkOrderUpdateView, WorkOrderDeleteView,
    CustomerAutocompleteView, SearchControlsPartialView, WorkOrderDetailView
)

urlpatterns = [
    # Main list page
    path('workorders/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX partials
    path('workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    path('workorders/search_controls/', SearchControlsPartialView.as_view(), name='workorder_search_controls'),
    path('workorders/customer_autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # CRUD operations (for modal/HTMX)
    path('workorders/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorders/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorders/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),

    # Detail View (for "WONo" link)
    path('workorders/<int:pk>/detail/', WorkOrderDetailView.as_view(), name='workorder_detail'), # Simplified detail URL
]
```

#### 4.6 Tests

Comprehensive tests for models (unit) and views (integration).

**`workorder_app/tests.py`**

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import WorkOrder, Company, WOCategory, Customer, FinancialYear, Employee
from datetime import datetime

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required related data for all tests
        cls.company = Company.objects.create(comp_id=1, comp_name="Test Company")
        cls.wo_category = WOCategory.objects.create(cid=101, symbol="GEN", cname="General WO", comp=cls.company)
        cls.customer = Customer.objects.create(customer_id="CUST001", customer_name="Test Customer Inc.", comp=cls.company)
        cls.financial_year = FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024")
        cls.employee = Employee.objects.create(emp_id="EMP001", title="Mr", employee_name="Test Employee", comp=cls.company)

        # Create test WorkOrder instances
        WorkOrder.objects.create(
            id=1,
            customer=cls.customer,
            enq_id='ENQ/2023/001',
            po_no='PO/2023/001',
            po_id='POID001',
            wo_no='WO/2023/001',
            sys_date='01-15-2023', # MM-DD-YYYY
            financial_year=cls.financial_year,
            employee=cls.employee,
            comp=cls.company,
            close_open=False,
            wo_category=cls.wo_category
        )
        WorkOrder.objects.create(
            id=2,
            customer=cls.customer,
            enq_id='ENQ/2023/002',
            po_no='PO/2023/002',
            po_id='POID002',
            wo_no='WO/2023/002',
            sys_date='02-20-2023', # MM-DD-YYYY
            financial_year=cls.financial_year,
            employee=cls.employee,
            comp=cls.company,
            close_open=True, # Closed WO
            wo_category=cls.wo_category
        )
        WorkOrder.objects.create(
            id=3,
            customer=Customer.objects.create(customer_id="CUST002", customer_name="Another Customer", comp=cls.company),
            enq_id='ENQ/2023/003',
            po_no='PO/2023/003',
            po_id='POID003',
            wo_no='WO/2023/003',
            sys_date='03-25-2023', # MM-DD-YYYY
            financial_year=cls.financial_year,
            employee=cls.employee,
            comp=cls.company,
            close_open=False,
            wo_category=cls.wo_category
        )

    def test_workorder_creation(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.wo_no, 'WO/2023/001')
        self.assertEqual(wo.customer.customer_name, 'Test Customer Inc.')
        self.assertFalse(wo.close_open)
        
    def test_generated_date_property(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.generated_date, '15-01-2023') # DD-MM-YYYY format expected

    def test_get_status_display(self):
        wo_open = WorkOrder.objects.get(id=1)
        wo_closed = WorkOrder.objects.get(id=2)
        self.assertEqual(wo_open.get_status_display(), 'Open')
        self.assertEqual(wo_closed.get_status_display(), 'Close')

    def test_get_customer_code(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.get_customer_code(), 'CUST001')

    def test_get_customer_name(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.get_customer_name(), 'Test Customer Inc.')

    def test_get_fin_year_display(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.get_fin_year_display(), '2023-2024')

    def test_get_generated_by_employee_name(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.get_generated_by_employee_name(), 'Mr. Test Employee')

    # Test WorkOrderQuerySet methods (fat model)
    def test_apply_filters_no_filters(self):
        # Default user context values from views.py
        qs = WorkOrder.objects.apply_filters(
            search_type='0', search_value='', wo_category_id='', fin_year_id=1, comp_id=1
        )
        self.assertEqual(qs.count(), 2) # Should include WO1 and WO3 (open, same comp/fin year)

    def test_apply_filters_customer_name(self):
        qs = WorkOrder.objects.apply_filters(
            search_type='0', search_value='Test Customer Inc. [CUST001]', wo_category_id='', fin_year_id=1, comp_id=1
        )
        self.assertEqual(qs.count(), 1) # Only WO1 (WO2 is closed)
        self.assertEqual(qs.first().wo_no, 'WO/2023/001')

    def test_apply_filters_enq_id(self):
        qs = WorkOrder.objects.apply_filters(
            search_type='1', search_value='ENQ/2023/003', wo_category_id='', fin_year_id=1, comp_id=1
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().wo_no, 'WO/2023/003')

    def test_apply_filters_wo_no(self):
        qs = WorkOrder.objects.apply_filters(
            search_type='3', search_value='WO/2023/001', wo_category_id='', fin_year_id=1, comp_id=1
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().wo_no, 'WO/2023/001')

    def test_apply_filters_wo_category(self):
        # Create another category for testing
        WOCategory.objects.create(cid=102, symbol="FAB", cname="Fabrication", comp=self.company)
        WorkOrder.objects.create(
            id=4,
            customer=self.customer,
            enq_id='ENQ/2023/004',
            po_no='PO/2023/004',
            po_id='POID004',
            wo_no='WO/2023/004',
            sys_date='04-01-2023', # MM-DD-YYYY
            financial_year=self.financial_year,
            employee=self.employee,
            comp=self.company,
            close_open=False,
            wo_category=WOCategory.objects.get(cid=102)
        )
        qs = WorkOrder.objects.apply_filters(
            search_type='0', search_value='', wo_category_id='102', fin_year_id=1, comp_id=1
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().wo_no, 'WO/2023/004')


class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required related data for all tests
        cls.company = Company.objects.create(comp_id=1, comp_name="Test Company")
        cls.wo_category = WOCategory.objects.create(cid=101, symbol="GEN", cname="General WO", comp=cls.company)
        cls.customer = Customer.objects.create(customer_id="CUST001", customer_name="Test Customer Inc.", comp=cls.company)
        cls.financial_year = FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024")
        cls.employee = Employee.objects.create(emp_id="EMP001", title="Mr", employee_name="Test Employee", comp=cls.company)

        # Create test WorkOrder instances
        WorkOrder.objects.create(
            id=1,
            customer=cls.customer,
            enq_id='ENQ/2023/001',
            po_no='PO/2023/001',
            po_id='POID001',
            wo_no='WO/2023/001',
            sys_date='01-15-2023', # MM-DD-YYYY
            financial_year=cls.financial_year,
            employee=cls.employee,
            comp=cls.company,
            close_open=False,
            wo_category=cls.wo_category
        )
        WorkOrder.objects.create(
            id=2,
            customer=Customer.objects.create(customer_id="CUST002", customer_name="Another Customer", comp=cls.company),
            enq_id='ENQ/2023/002',
            po_no='PO/2023/002',
            po_id='POID002',
            wo_no='WO/2023/002',
            sys_date='02-20-2023', # MM-DD-YYYY
            financial_year=cls.financial_year,
            employee=cls.employee,
            comp=cls.company,
            close_open=False,
            wo_category=cls.wo_category
        )

    def setUp(self):
        self.client = Client()
        # Set session variables to mock ASP.NET environment
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 1
        session['username'] = 'testuser' # Assuming this maps to an employee ID for session_id
        session.save()
    
    def test_list_view(self):
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_app/workorder/list.html')
        self.assertIn('search_form', response.context)
        self.assertIn('wo_categories', response.context)

    def test_table_partial_view_no_filters(self):
        response = self.client.get(reverse('workorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_app/workorder/_workorder_table.html')
        self.assertIn('workorders', response.context)
        self.assertEqual(len(response.context['workorders']), 2) # Both WOs are open and match default filters

    def test_table_partial_view_customer_name_filter(self):
        response = self.client.get(reverse('workorder_table'), {'search_type': '0', 'search_value': 'Test Customer Inc. [CUST001]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['workorders']), 1)
        self.assertEqual(response.context['workorders'].first().wo_no, 'WO/2023/001')

    def test_table_partial_view_enq_id_filter(self):
        response = self.client.get(reverse('workorder_table'), {'search_type': '1', 'enq_id': 'ENQ/2023/002'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['workorders']), 1)
        self.assertEqual(response.context['workorders'].first().wo_no, 'WO/2023/002')

    def test_table_partial_view_wo_category_filter(self):
        new_category = WOCategory.objects.create(cid=102, symbol="SPE", cname="Special WO", comp=self.company)
        WorkOrder.objects.create(
            id=3, customer=self.customer, enq_id='ENQ/2023/003', wo_no='WO/2023/003', sys_date='03-01-2023',
            financial_year=self.financial_year, employee=self.employee, comp=self.company, close_open=False, wo_category=new_category
        )
        response = self.client.get(reverse('workorder_table'), {'wo_category': new_category.cid})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['workorders']), 1)
        self.assertEqual(response.context['workorders'].first().wo_no, 'WO/2023/003')

    def test_create_view_get(self):
        response = self.client.get(reverse('workorder_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_app/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_htmx(self):
        data = {
            'customer': self.customer.customer_id,
            'enq_id': 'NEW_ENQ/2024/001',
            'po_no': 'NEW_PO/2024/001',
            'wo_no': 'NEW_WO/2024/001',
            'sys_date': '04-01-2024',
            'financial_year': self.financial_year.fin_year_id,
            'employee': self.employee.emp_id,
            'close_open': 'off', # Maps to False for CheckboxInput
            'wo_category': self.wo_category.cid,
        }
        response = self.client.post(reverse('workorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshWorkOrderList')
        self.assertTrue(WorkOrder.objects.filter(wo_no='NEW_WO/2024/001').exists())
        
    def test_update_view_get(self):
        wo = WorkOrder.objects.get(id=1)
        response = self.client.get(reverse('workorder_edit', args=[wo.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_app/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, wo)
        
    def test_update_view_post_htmx(self):
        wo = WorkOrder.objects.get(id=1)
        data = {
            'customer': wo.customer.customer_id,
            'enq_id': wo.enq_id,
            'po_no': wo.po_no,
            'wo_no': 'UPDATED_WO/2023/001', # Change WO No
            'sys_date': wo.sys_date,
            'financial_year': wo.financial_year.fin_year_id,
            'employee': wo.employee.emp_id,
            'close_open': 'on', # Mark as closed
            'wo_category': wo.wo_category.cid,
        }
        response = self.client.post(reverse('workorder_edit', args=[wo.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshWorkOrderList')
        wo.refresh_from_db()
        self.assertEqual(wo.wo_no, 'UPDATED_WO/2023/001')
        self.assertTrue(wo.close_open)

    def test_delete_view_get(self):
        wo = WorkOrder.objects.get(id=1)
        response = self.client.get(reverse('workorder_delete', args=[wo.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_app/workorder/_workorder_confirm_delete.html')
        self.assertIn('workorder', response.context)
        self.assertEqual(response.context['workorder'], wo)
        
    def test_delete_view_post_htmx(self):
        wo_to_delete = WorkOrder.objects.get(id=1)
        response = self.client.post(reverse('workorder_delete', args=[wo_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshWorkOrderList')
        self.assertFalse(WorkOrder.objects.filter(id=wo_to_delete.id).exists())

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete'), {'q': 'test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['text'], 'Test Customer Inc. [CUST001]')

    def test_search_controls_partial_view(self):
        response = self.client.get(reverse('workorder_search_controls'), {'search_type': '1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_app/workorder/_search_controls.html')
        self.assertContains(response, 'id_enq_id') # Should show enq_id input
        self.assertNotContains(response, 'id_search_value') # Should hide search_value input (due to 'hidden' class)


    def test_workorder_detail_view(self):
        wo = WorkOrder.objects.get(id=1)
        response = self.client.get(reverse('workorder_detail', args=[wo.id]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json') # Assuming JSON for simplicity in test
        data = response.json()
        self.assertEqual(data['id'], wo.id)
        self.assertEqual(data['wo_no'], wo.wo_no)

```

### Step 5: HTMX and Alpine.js Integration

The provided Django templates and views already demonstrate strong integration of HTMX and Alpine.js principles:

*   **HTMX for dynamic updates:**
    *   The `workorderTable-container` div uses `hx-trigger="load, refreshWorkOrderList from:body"` and `hx-get="{% url 'workorder_table' %}"` to load the table content dynamically on page load and whenever a `refreshWorkOrderList` custom event is triggered (e.g., after successful CRUD operations).
    *   The search form uses `hx-get` to reload the table partial based on filter changes.
    *   Add/Edit/Delete buttons use `hx-get` to fetch form/confirm templates into a modal, and `hx-post` on form submission with `hx-swap="none"` and `HX-Trigger` headers for status updates and client-side refreshes.
    *   Customer autocomplete uses `hx-get` on `keyup changed delay:500ms` to fetch suggestions.
    *   Search type dropdown uses `hx-get` to fetch the `_search_controls.html` partial, dynamically swapping the input field based on selected search type.
*   **Alpine.js for UI state management:**
    *   The modal visibility is controlled with Alpine.js (e.g., `x-data="{ openModal: false }"`, `x-show="openModal"`), or `_` (Hyperscript) for simple toggles on HTMX.
    *   The example uses `_` (Hyperscript) for basic modal toggling `on click add .is-active to #modal`. This is a lightweight alternative to full Alpine.js for simple UI interactions.
*   **DataTables for list views:**
    *   The `_workorder_table.html` partial contains the HTML table structure.
    *   JavaScript in `list.html` listens for the `htmx:afterSettle` event on the `workorderTable-container` to initialize `$('#workorderTable').DataTable({...})` after the table content has been loaded via HTMX. This ensures DataTables is applied to the newly loaded content.
*   **No custom JavaScript requirements:** Beyond the initial DataTables setup and basic Alpine.js/Hyperscript for modal control, the application relies on HTMX for all dynamic interactions, minimizing custom JS.
*   **DRY template inheritance:** `list.html` extends `core/base.html`, ensuring all CDN links for DataTables, HTMX, Alpine.js, and Tailwind CSS are managed centrally in `base.html`.

### Final Notes

*   **Placeholders:** `get_user_context` in `views.py` mocks session data (`compid`, `finyear`, `username`). In a real application, these would be retrieved from Django's authentication system and potentially a `UserProfile` model.
*   **Error Handling:** Basic error handling for form validation is present. Robust error handling (e.g., displaying messages for failed HTMX requests) would be added as needed.
*   **Performance:** The Django ORM with `select_related` significantly improves performance compared to the original N+1 query pattern in ASP.NET's `BindDataCust`.
*   **Security:** Django's built-in CSRF protection is used (`{% csrf_token %}`). All database interactions go through the ORM, preventing SQL injection.
*   **Scalability:** Django's architecture is inherently more scalable, and the use of HTMX reduces server load by offloading UI rendering and interaction logic to the client where appropriate.
*   **Maintainability:** The "Fat Model, Thin View" approach centralizes business logic, making it easier to maintain, test, and reuse.
*   **Automation Focus:** This plan is designed to be executable step-by-step with clear file structures, enabling AI-assisted tools to generate and refactor code, and guiding non-technical stakeholders through the process.