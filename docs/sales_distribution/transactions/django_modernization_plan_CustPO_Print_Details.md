This modernization plan outlines the strategy to transition the ASP.NET Customer PO Print Details functionality to a modern Django application, adhering strictly to the specified guidelines for architecture, technology stack, and communication style.

---

## ASP.NET to Django Conversion Script: Customer PO Details

This document details the conversion of the `CustPO_Print_Details.aspx` ASP.NET page and its C# code-behind into a modern Django application. The focus is on a comprehensive, automation-driven approach using Django's best practices, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code primarily retrieves data for a Customer Purchase Order (PO) and its related details to generate a report. By analyzing the SQL queries (`fun.select` calls), we can infer the necessary database tables and their relationships:

-   **`SD_Cust_PO_Master`**: This is the central table for Customer Purchase Orders.
    -   **Key Columns**: `PONo` (Purchase Order Number), `EnqId` (Enquiry ID), `CompId` (Company Identifier), `POId` (likely the primary key, a unique identifier for the PO), `CustomerId` (links to customer details), `PODate` (PO Date), `POReceivedDate` (Date PO was received), `QuotationNo` (links to quotation details).
-   **`SD_Cust_master`**: Contains general customer information.
    -   **Key Columns**: `CustomerId` (primary key), `CompId`, `RegdCity`, `RegdState`, `RegdCountry` (registered address lookups), `WorkCity`, `WorkState`, `WorkCountry` (work address lookups), `MaterialDelCity`, `MaterialDelState`, `MaterialDelCountry` (material delivery address lookups).
-   **`tblCity`**: A lookup table for city names.
    -   **Key Columns**: `CityId` (primary key), `CityName`.
-   **`tblState`**: A lookup table for state names.
    -   **Key Columns**: `SId` (primary key), `StateName`.
-   **`tblCountry`**: A lookup table for country names.
    -   **Key Columns**: `CId` (primary key), `CountryName`.
-   **`SD_Cust_Quotation_Master`**: Stores details about customer quotations.
    -   **Key Columns**: `Id` (primary key), `QuotationNo`, `CompId`.

### Step 2: Identify Backend Functionality

The original ASP.NET page (`CustPO_Print_Details.aspx`) is designed purely for **displaying (reading) and printing** the detailed information of a specific Customer Purchase Order. It's not involved in creating, updating, or deleting PO records.

-   **Read Operation**: The C# code-behind retrieves a single `CustomerPO` record based on query string parameters (`PONo`, `EnqId`, `POId`) and a session variable (`CompId`). It then performs multiple additional queries to fetch related data such as:
    -   Customer details from `SD_Cust_master`.
    -   City, state, and country names for registered, work, and material delivery addresses from `tblCity`, `tblState`, and `tblCountry`.
    -   The corresponding quotation number from `SD_Cust_Quotation_Master`.
    -   All this data is then assembled and passed to a Crystal Report for rendering.
-   **No CRUD on this page**: There are no explicit Create, Update, or Delete operations shown in this specific ASP.NET file. However, to build a complete Django module, we will implement full CRUD operations for the `CustomerPO` entity, with the "Print Details" functionality becoming a dedicated detail view with PDF export capabilities.

### Step 3: Infer UI Components

The ASP.NET page's user interface is minimalist, centered around displaying a Crystal Report.

-   **`CR:CrystalReportViewer`**: This component displays the generated report. In Django, this will be replaced by a clean HTML template rendering the PO details directly, with an option to generate a PDF for printing or formal records using a library like `WeasyPrint`.
-   **`asp:Panel`**: A container to manage the layout and scrolling for the report viewer. This will translate to a standard HTML `div` element, styled with Tailwind CSS to ensure responsiveness and proper display.
-   **`asp:Button` (ID="Button1", Text="Cancel")**: This button's function is to navigate the user back to a list or previous page. In Django, this will be a simple HTML anchor tag (`<a>`) or a button that navigates to the relevant URL.

The modernized Django application will provide a user-friendly list view for all Customer POs (powered by DataTables), and the detailed view (replacing the print details page) will be accessible from this list, offering comprehensive information and the ability to generate a printable PDF.

### Step 4: Generate Django Code

We will create a Django application named `sales` to manage customer purchase orders and related entities.

#### 4.1 Models (`sales/models.py`)

These models will map directly to your existing database tables using `managed = False` and `db_table`. Foreign keys are used to define relationships between tables, allowing Django's ORM to efficiently retrieve related data, similar to how the ASP.NET code performed multiple `SELECT` queries. The `company_id` is kept as an IntegerField, assuming a `Company` model might exist in a `core` app or is outside the scope of this migration.```markdown
## ASP.NET to Django Conversion Script: Customer PO Details

This document details the conversion of the `CustPO_Print_Details.aspx` ASP.NET page and its C# code-behind into a modern Django application, adhering strictly to the specified guidelines for architecture, technology stack, and communication style.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code primarily retrieves data for a Customer Purchase Order (PO) and its related details to generate a report. By analyzing the SQL queries (`fun.select` calls), we can infer the necessary database tables and their relationships:

-   **`SD_Cust_PO_Master`**: This is the central table for Customer Purchase Orders.
    -   **Key Columns**: `PONo` (Purchase Order Number), `EnqId` (Enquiry ID), `CompId` (Company Identifier), `POId` (likely the primary key, a unique identifier for the PO), `CustomerId` (links to customer details), `PODate` (PO Date), `POReceivedDate` (Date PO was received), `QuotationNo` (links to quotation details).
-   **`SD_Cust_master`**: Contains general customer information.
    -   **Key Columns**: `CustomerId` (primary key), `CompId`, `RegdCity`, `RegdState`, `RegdCountry` (registered address lookups), `WorkCity`, `WorkState`, `WorkCountry` (work address lookups), `MaterialDelCity`, `MaterialDelState`, `MaterialDelCountry` (material delivery address lookups).
-   **`tblCity`**: A lookup table for city names.
    -   **Key Columns**: `CityId` (primary key), `CityName`.
-   **`tblState`**: A lookup table for state names.
    -   **Key Columns**: `SId` (primary key), `StateName`.
-   **`tblCountry`**: A lookup table for country names.
    -   **Key Columns**: `CId` (primary key), `CountryName`.
-   **`SD_Cust_Quotation_Master`**: Stores details about customer quotations.
    -   **Key Columns**: `Id` (primary key), `QuotationNo`, `CompId`.

### Step 2: Identify Backend Functionality

The original ASP.NET page (`CustPO_Print_Details.aspx`) is designed purely for **displaying (reading) and printing** the detailed information of a specific Customer Purchase Order. It's not involved in creating, updating, or deleting PO records.

-   **Read Operation**: The C# code-behind retrieves a single `CustomerPO` record based on query string parameters (`PONo`, `EnqId`, `POId`) and a session variable (`CompId`). It then performs multiple additional queries to fetch related data such as:
    -   Customer details from `SD_Cust_master`.
    -   City, state, and country names for registered, work, and material delivery addresses from `tblCity`, `tblState`, and `tblCountry`.
    -   The corresponding quotation number from `SD_Cust_Quotation_Master`.
    -   All this data is then assembled and passed to a Crystal Report for rendering.
-   **No CRUD on this page**: There are no explicit Create, Update, or Delete operations shown in this specific ASP.NET file. However, to build a complete Django module as per the instructions, we will implement full CRUD operations for the `CustomerPO` entity, with the "Print Details" functionality becoming a dedicated detail view with PDF export capabilities.

### Step 3: Infer UI Components

The ASP.NET page's user interface is minimalist, centered around displaying a Crystal Report.

-   **`CR:CrystalReportViewer`**: This component displays the generated report. In Django, this will be replaced by a clean HTML template rendering the PO details directly, with an option to generate a PDF for printing or formal records using a library like `WeasyPrint`.
-   **`asp:Panel`**: A container to manage the layout and scrolling for the report viewer. This will translate to a standard HTML `div` element, styled with Tailwind CSS to ensure responsiveness and proper display.
-   **`asp:Button` (ID="Button1", Text="Cancel")**: This button's function is to navigate the user back to a list or previous page. In Django, this will be a simple HTML anchor tag (`<a>`) or a button that navigates to the relevant URL.

The modernized Django application will provide a user-friendly list view for all Customer POs (powered by DataTables), and the detailed view (replacing the print details page) will be accessible from this list, offering comprehensive information and the ability to generate a printable PDF.

### Step 4: Generate Django Code

We will create a Django application named `sales` to manage customer purchase orders and related entities.

#### 4.1 Models (`sales/models.py`)

These models will map directly to your existing database tables using `managed = False` and `db_table`. Foreign keys are used to define relationships between tables, allowing Django's ORM to efficiently retrieve related data, similar to how the ASP.NET code performed multiple `SELECT` queries. The `company_id` is kept as an IntegerField, assuming a `Company` model might exist in a `core` app or is outside the scope of this migration.

```python
from django.db import models
from django.urls import reverse
from django.utils import timezone

# Lookup tables for geographical data
class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class State(models.Model):
    state_id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class Country(models.Model):
    country_id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

# Model for SD_Cust_master
class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    company_id = models.IntegerField(db_column='CompId') # Placeholder, ideally ForeignKey to Company
    regd_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='RegdCity', related_name='regd_customers', null=True, blank=True)
    regd_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='RegdState', related_name='regd_customers', null=True, blank=True)
    regd_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='RegdCountry', related_name='regd_customers', null=True, blank=True)
    work_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='WorkCity', related_name='work_customers', null=True, blank=True)
    work_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='WorkState', related_name='work_customers', null=True, blank=True)
    work_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='WorkCountry', related_name='work_customers', null=True, blank=True)
    material_del_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='MaterialDelCity', related_name='del_customers', null=True, blank=True)
    material_del_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='MaterialDelState', related_name='del_customers', null=True, blank=True)
    material_del_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='MaterialDelCountry', related_name='del_customers', null=True, blank=True)
    # Add other customer-specific fields as needed based on actual schema

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"Customer ID: {self.customer_id}" # Assuming customer name is not available

# Model for SD_Cust_Quotation_Master
class CustomerQuotation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the PK
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50)
    company_id = models.IntegerField(db_column='CompId') # Placeholder, ideally ForeignKey to Company
    # Add other quotation-specific fields as needed

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Customer Quotation'
        verbose_name_plural = 'Customer Quotations'

    def __str__(self):
        return self.quotation_no

# Main Model for SD_Cust_PO_Master
class CustomerPO(models.Model):
    # Django will automatically create an 'id' primary key.
    # Assuming POId in ASP.NET code refers to this implicit primary key.
    pono = models.CharField(db_column='PONo', max_length=50, verbose_name="PO Number")
    enquiry_id = models.CharField(db_column='EnqId', max_length=50, verbose_name="Enquiry ID")
    company_id = models.IntegerField(db_column='CompId', verbose_name="Company ID") # Placeholder
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id', verbose_name="Customer")
    po_date = models.DateField(db_column='PODate', verbose_name="PO Date")
    po_received_date = models.DateField(db_column='POReceivedDate', verbose_name="PO Received Date")
    # `QuotationNo` in SD_Cust_PO_Master seems to store the ID of SD_Cust_Quotation_Master.
    quotation = models.ForeignKey(CustomerQuotation, on_delete=models.DO_NOTHING, db_column='QuotationNo', verbose_name="Quotation")
    
    # Inferred additional fields for a comprehensive PO
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="Total Amount")
    status = models.CharField(max_length=20, default='Pending', verbose_name="Status") # e.g., 'Pending', 'Approved', 'Rejected'

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Customer PO'
        verbose_name_plural = 'Customer POs'
        # Define a unique constraint if PONo, EnquiryId, and CompanyId together form a unique identifier
        unique_together = (('pono', 'enquiry_id', 'company_id'),)


    def __str__(self):
        return f"PO No: {self.pono} for Customer: {self.customer.customer_id}"

    # Business logic methods (Fat Model Approach)
    def get_full_address_details(self, address_type='regd'):
        """
        Retrieves formatted address details for a customer based on type.
        address_type can be 'regd', 'work', 'del'.
        """
        customer = self.customer
        city = None
        state = None
        country = None

        if address_type == 'regd':
            city = customer.regd_city
            state = customer.regd_state
            country = customer.regd_country
        elif address_type == 'work':
            city = customer.work_city
            state = customer.work_state
            country = customer.work_country
        elif address_type == 'del':
            city = customer.material_del_city
            state = customer.material_del_state
            country = customer.material_del_country

        return {
            'city_name': city.city_name if city else 'N/A',
            'state_name': state.state_name if state else 'N/A',
            'country_name': country.country_name if country else 'N/A',
        }

    def get_po_details_for_report(self):
        """
        Gathers all necessary data for the PO details report, mimicking the data
        passed to the Crystal Report in the original ASP.NET application.
        """
        data = {
            'pono': self.pono,
            'enquiry_id': self.enquiry_id,
            'po_date': self.po_date.strftime('%d-%m-%Y') if self.po_date else 'N/A',
            'po_received_date': self.po_received_date.strftime('%d-%m-%Y') if self.po_received_date else 'N/A',
            'customer_id': self.customer.customer_id,
            'quotation_no': self.quotation.quotation_no if self.quotation else 'NA',
            'total_amount': self.total_amount,
            'status': self.status,
            'company_id': self.company_id,
            # Placeholder for company info, usually fetched from a Company model
            'company_address': 'Your Company Address Line 1, City, State, Country',
            'company_name': 'Your Company Name (Placeholder)',
        }

        # Retrieve address details via model methods
        regd_addr = self.get_full_address_details(address_type='regd')
        work_addr = self.get_full_address_details(address_type='work')
        del_addr = self.get_full_address_details(address_type='del')

        data.update({
            'regd_city': regd_addr['city_name'],
            'regd_state': regd_addr['state_name'],
            'regd_country': regd_addr['country_name'],
            'work_city': work_addr['city_name'],
            'work_state': work_addr['state_name'],
            'work_country': work_addr['country_name'],
            'del_city': del_addr['city_name'],
            'del_state': del_addr['state_name'],
            'del_country': del_addr['country_name'],
        })

        return data

    def get_absolute_url(self):
        """Returns the URL to access a particular instance of CustomerPO."""
        return reverse('customerpo_detail', kwargs={'pk': self.pk})

```

#### 4.2 Forms (`sales/forms.py`)

A Django `ModelForm` will be used to handle input for `CustomerPO` objects, ensuring data integrity and providing a clean interface for HTMX.

```python
from django import forms
from .models import CustomerPO, Customer, CustomerQuotation

class CustomerPOForm(forms.ModelForm):
    # ModelChoiceField for foreign key relationships
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.all(),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select Customer"
    )
    quotation = forms.ModelChoiceField(
        queryset=CustomerQuotation.objects.all(),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select Quotation"
    )

    class Meta:
        model = CustomerPO
        fields = ['pono', 'enquiry_id', 'company_id', 'customer', 'po_date', 'po_received_date', 'quotation', 'total_amount', 'status']
        widgets = {
            'pono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enquiry_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_received_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'pono': 'PO Number',
            'enquiry_id': 'Enquiry ID',
            'company_id': 'Company ID',
            'po_date': 'PO Date',
            'po_received_date': 'PO Received Date',
            'total_amount': 'Total Amount',
        }
        
    def clean(self):
        cleaned_data = super().clean()
        # Custom validation: PO Received Date cannot be before PO Date
        po_date = cleaned_data.get('po_date')
        po_received_date = cleaned_data.get('po_received_date')

        if po_date and po_received_date and po_received_date < po_date:
            self.add_error('po_received_date', 'PO Received Date cannot be before PO Date.')
        return cleaned_data

```

#### 4.3 Views (`sales/views.py`)

Django Class-Based Views (CBVs) will handle CRUD operations and the specific detail/print view. The views are kept thin, delegating business logic to the models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string # Required for PDF generation
import weasyprint # Install with `pip install WeasyPrint` and system dependencies

from .models import CustomerPO
from .forms import CustomerPOForm

class CustomerPOListView(ListView):
    model = CustomerPO
    template_name = 'sales/customerpo/list.html'
    context_object_name = 'customer_pos' # Renamed for clarity in template

class CustomerPOTablePartialView(ListView):
    """
    Renders the DataTables portion of the list view for HTMX updates.
    """
    model = CustomerPO
    template_name = 'sales/customerpo/_customerpo_table.html'
    context_object_name = 'customer_pos'

class CustomerPOCreateView(CreateView):
    model = CustomerPO
    form_class = CustomerPOForm
    template_name = 'sales/customerpo/form.html'
    success_url = reverse_lazy('customerpo_list') # Redirect to list view after successful creation

    def form_valid(self, form):
        # Additional logic (e.g., setting company_id from session/user) can go here
        # form.instance.company_id = self.request.session.get('compid', 1) # Example
        response = super().form_valid(form)
        messages.success(self.request, 'Customer PO added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content status and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerPOList' # Custom HTMX event for list refresh
                }
            )
        return response

class CustomerPOUpdateView(UpdateView):
    model = CustomerPO
    form_class = CustomerPOForm
    template_name = 'sales/customerpo/form.html'
    success_url = reverse_lazy('customerpo_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer PO updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerPOList'
                }
            )
        return response

class CustomerPODeleteView(DeleteView):
    model = CustomerPO
    template_name = 'sales/customerpo/confirm_delete.html'
    success_url = reverse_lazy('customerpo_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer PO deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerPOList'
                }
            )
        return response

# This replaces the ASP.NET Crystal Report Viewer page functionality
class CustomerPODetailView(DetailView):
    model = CustomerPO
    template_name = 'sales/customerpo/detail.html'
    context_object_name = 'customer_po'

    def get_object(self, queryset=None):
        """
        Overrides to allow retrieving object by primary key (Django default)
        or by legacy ASP.NET query string parameters (PONo, EnqId, POId, CompId).
        """
        # Prioritize Django's standard PK if available in URL
        if 'pk' in self.kwargs:
            return get_object_or_404(CustomerPO, pk=self.kwargs['pk'])
        
        # Fallback to ASP.NET query string parameters for legacy compatibility
        pono = self.request.GET.get('PONo')
        enquiry_id = self.request.GET.get('EnqId')
        po_id_from_qs = self.request.GET.get('POId')
        # Placeholder for company_id, in a real app, it would come from session or user.
        # Assuming a default or retrieving from a user's company context.
        company_id = self.request.GET.get('CompId', 1) 

        if po_id_from_qs:
            return get_object_or_404(CustomerPO, pk=po_id_from_qs)
        elif pono and enquiry_id and company_id:
            # Handle potential composite key lookup if POId is not the primary key
            return get_object_or_404(CustomerPO, pono=pono, enquiry_id=enquiry_id, company_id=company_id)
        else:
            raise Http404("Customer PO not found. Missing required identifiers (PK or query parameters).")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer_po = self.get_object()
        # Use the fat model method to get all report-related data
        context['po_details'] = customer_po.get_po_details_for_report()
        return context

# View to generate PDF (replacing Crystal Reports output)
class CustomerPOPrintPDFView(CustomerPODetailView): # Inherit to reuse object retrieval logic
    template_name = 'sales/customerpo/print_pdf_template.html' # A dedicated HTML template for PDF rendering

    def render_to_response(self, context, **response_kwargs):
        """
        Generates a PDF from an HTML template using WeasyPrint.
        """
        html_content = render_to_string(self.template_name, context)
        # Generate PDF from HTML string
        pdf = weasyprint.HTML(string=html_content).write_pdf()

        response = HttpResponse(pdf, content_type='application/pdf')
        # Set Content-Disposition to 'inline' to display in browser, 'attachment' to download
        response['Content-Disposition'] = f'inline; filename="customer_po_{self.object.pono}.pdf"'
        return response

```

#### 4.4 Templates

**sales/customerpo/list.html**
This template provides the main entry point for managing Customer POs, using DataTables for an interactive list and HTMX for dynamic modal forms.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customer POs</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'customerpo_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Customer PO
        </button>
    </div>
    
    <div id="customerpoTable-container"
         hx-trigger="load, refreshCustomerPOList from:body"
         hx-get="{% url 'customerpo_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Customer POs...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on hx-after-swap add .is-active to #modal if not #modalContent:empty end"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here for more complex UI state.
        // For simple modal toggling, Hyperscript (via _ attribute) is often sufficient.
    });
</script>
{% endblock %}

```

**sales/customerpo/_customerpo_table.html** (Partial for HTMX)
This partial template contains the DataTables structure, designed to be loaded dynamically via HTMX.

```html
<div class="overflow-x-auto rounded-lg shadow">
    <table id="customerpoTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry ID</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer ID</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Date</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in customer_pos %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.pono }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.enquiry_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.customer.customer_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.po_date|date:"d M Y" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.status }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'customerpo_detail' pk=obj.pk %}" 
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded-md mr-2 inline-block text-xs">
                        View
                    </a>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2 text-xs"
                        hx-get="{% url 'customerpo_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'customerpo_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No Customer POs found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded by HTMX
    $(document).ready(function() {
        $('#customerpoTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>
```

**sales/customerpo/form.html** (Partial for HTMX)
This partial template renders the `CustomerPOForm` within the modal, handling both create and update operations.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer PO</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" delegates UI updates to HX-Trigger header #}
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-2 sm:gap-x-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>

```

**sales/customerpo/confirm_delete.html** (Partial for HTMX)
This partial template provides a confirmation dialog for deleting a Customer PO.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete Customer PO <strong>{{ object.pono }}</strong>?</p>
    
    <form hx-post="{% url 'customerpo_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**sales/customerpo/detail.html** (Replaces Crystal Report Viewer display)
This template provides a detailed, human-readable view of a Customer PO, akin to what the Crystal Report displayed, with an option to generate a PDF.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customer PO Details - {{ customer_po.pono }}</h2>
        <div class="space-x-2">
            <a href="{% url 'customerpo_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to List
            </a>
            {# Link to the PDF generation view #}
            <a href="{% url 'customerpo_print_pdf' pk=customer_po.pk %}" target="_blank"
               class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                Generate PDF
            </a>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">PO Information</h3>
        <div class="mt-4 border-t border-gray-200">
            <dl class="divide-y divide-gray-200">
                <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">PO Number</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.pono }}</dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Enquiry ID</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.enquiry_id }}</dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Customer ID</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.customer_id }}</dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">PO Date</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.po_date }}</dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">PO Received Date</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.po_received_date }}</dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Quotation Number</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.quotation_no }}</dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.total_amount|default:'N/A' }}</dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">{{ po_details.status }}</dd>
                </div>
            </dl>
        </div>

        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-8">Customer Addresses</h3>
        <div class="mt-4 border-t border-gray-200">
            <dl class="divide-y divide-gray-200">
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Registered Address</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {{ po_details.regd_city }}, {{ po_details.regd_state }}, {{ po_details.regd_country }}
                    </dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Work Address</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {{ po_details.work_city }}, {{ po_details.work_state }}, {{ po_details.work_country }}
                    </dd>
                </div>
                <div class="px-4 py-3 sm:grid sm-grid-cols-3 sm:gap-4">
                    <dt class="text-sm font-medium text-gray-500">Material Delivery Address</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {{ po_details.del_city }}, {{ po_details.del_state }}, {{ po_details.del_country }}
                    </dd>
                </div>
            </dl>
        </div>
    </div>
</div>
{% endblock %}

```

**sales/customerpo/print_pdf_template.html** (Dedicated template for PDF generation)
This template is specifically for `WeasyPrint` to convert to PDF. It includes minimal inline CSS for proper PDF rendering.

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Customer PO - {{ customer_po.pono }}</title>
    <style>
        /* Basic CSS for PDF readability, avoiding external assets for WeasyPrint */
        body { font-family: 'Helvetica', 'Arial', sans-serif; font-size: 10pt; line-height: 1.4; margin: 2cm; }
        h1, h2, h3 { font-family: 'Helvetica', 'Arial', sans-serif; margin-top: 0; }
        h1 { font-size: 18pt; text-align: center; margin-bottom: 20px; color: #333; }
        h2 { font-size: 14pt; border-bottom: 1px solid #eee; padding-bottom: 5px; margin-top: 30px; color: #555; }
        h3 { font-size: 12pt; margin-top: 20px; color: #777; }
        .section { margin-bottom: 20px; }
        .detail-row { display: flex; margin-bottom: 5px; }
        .detail-label { font-weight: bold; width: 150px; flex-shrink: 0; color: #444; }
        .detail-value { flex-grow: 1; color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f8f8; color: #333; }
        .company-header { text-align: center; margin-bottom: 30px; }
        .company-header h2 { margin-bottom: 5px; border-bottom: none; color: #222; }
        .company-header p { margin: 0; font-size: 9pt; color: #666; }
    </style>
</head>
<body>
    <div class="company-header">
        <h2>{{ po_details.company_name }}</h2>
        <p>{{ po_details.company_address }}</p>
    </div>

    <h1>Customer Purchase Order Details</h1>

    <div class="section">
        <h2>PO Information</h2>
        <div class="detail-row"><span class="detail-label">PO Number:</span> <span class="detail-value">{{ po_details.pono }}</span></div>
        <div class="detail-row"><span class="detail-label">Enquiry ID:</span> <span class="detail-value">{{ po_details.enquiry_id }}</span></div>
        <div class="detail-row"><span class="detail-label">Customer ID:</span> <span class="detail-value">{{ po_details.customer_id }}</span></div>
        <div class="detail-row"><span class="detail-label">PO Date:</span> <span class="detail-value">{{ po_details.po_date }}</span></div>
        <div class="detail-row"><span class="detail-label">PO Received Date:</span> <span class="detail-value">{{ po_details.po_received_date }}</span></div>
        <div class="detail-row"><span class="detail-label">Quotation Number:</span> <span class="detail-value">{{ po_details.quotation_no }}</span></div>
        <div class="detail-row"><span class="detail-label">Total Amount:</span> <span class="detail-value">{{ po_details.total_amount|default:'N/A' }}</span></div>
        <div class="detail-row"><span class="detail-label">Status:</span> <span class="detail-value">{{ po_details.status }}</span></div>
    </div>

    <div class="section">
        <h2>Customer Addresses</h2>
        <h3>Registered Address</h3>
        <div class="detail-row"><span class="detail-label">City:</span> <span class="detail-value">{{ po_details.regd_city }}</span></div>
        <div class="detail-row"><span class="detail-label">State:</span> <span class="detail-value">{{ po_details.regd_state }}</span></div>
        <div class="detail-row"><span class="detail-label">Country:</span> <span class="detail-value">{{ po_details.regd_country }}</span></div>

        <h3>Work Address</h3>
        <div class="detail-row"><span class="detail-label">City:</span> <span class="detail-value">{{ po_details.work_city }}</span></div>
        <div class="detail-row"><span class="detail-label">State:</span> <span class="detail-value">{{ po_details.work_state }}</span></div>
        <div class="detail-row"><span class="detail-label">Country:</span> <span class="detail-value">{{ po_details.work_country }}</span></div>

        <h3>Material Delivery Address</h3>
        <div class="detail-row"><span class="detail-label">City:</span> <span class="detail-value">{{ po_details.del_city }}</span></div>
        <div class="detail-row"><span class="detail-label">State:</span> <span class="detail-value">{{ po_details.del_state }}</span></div>
        <div class="detail-row"><span class="detail-label">Country:</span> <span class="detail-value">{{ po_details.del_country }}</span></div>
    </div>

    <!-- Add other sections of the report as needed -->

</body>
</html>
```

#### 4.5 URLs (`sales/urls.py`)

This file defines the URL patterns for the `sales` application, including standard CRUD operations and the specific detail/print functionalities.

```python
from django.urls import path
from .views import (
    CustomerPOListView, 
    CustomerPOCreateView, 
    CustomerPOUpdateView, 
    CustomerPODeleteView,
    CustomerPOTablePartialView,
    CustomerPODetailView,
    CustomerPOPrintPDFView
)

urlpatterns = [
    # Customer PO List and Table Partial View
    path('customerpo/', CustomerPOListView.as_view(), name='customerpo_list'),
    path('customerpo/table/', CustomerPOTablePartialView.as_view(), name='customerpo_table_partial'),
    
    # Customer PO CRUD Operations (via modals)
    path('customerpo/add/', CustomerPOCreateView.as_view(), name='customerpo_add'),
    path('customerpo/<int:pk>/edit/', CustomerPOUpdateView.as_view(), name='customerpo_edit'),
    path('customerpo/<int:pk>/delete/', CustomerPODeleteView.as_view(), name='customerpo_delete'),
    
    # Customer PO Detail View (replaces original ASP.NET print view)
    path('customerpo/<int:pk>/details/', CustomerPODetailView.as_view(), name='customerpo_detail'),
    # Optional: Legacy URL for direct migration of query string parameters
    path('customerpo/details_legacy/', CustomerPODetailView.as_view(), name='customerpo_detail_legacy'),

    # PDF Generation for Customer PO details
    path('customerpo/<int:pk>/print_pdf/', CustomerPOPrintPDFView.as_view(), name='customerpo_print_pdf'),
    # Optional: Legacy PDF URL to mimic query string behavior
    path('customerpo/print_pdf_legacy/', CustomerPOPrintPDFView.as_view(), name='customerpo_print_pdf_legacy'),
]

```

#### 4.6 Tests (`sales/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure the reliability and correctness of the migrated functionality.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import CustomerPO, Customer, City, State, Country, CustomerQuotation
from datetime import date
from django.utils import timezone
from unittest.mock import patch # For mocking PDF generation

# --- Model Unit Tests ---

class LookupModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for lookup tables
        cls.city1 = City.objects.create(city_id=1, city_name='Test City A')
        cls.state1 = State.objects.create(state_id=1, state_name='Test State A')
        cls.country1 = Country.objects.create(country_id=1, country_name='Test Country A')
        cls.city2 = City.objects.create(city_id=2, city_name='Test City B')

    def test_city_creation(self):
        city = City.objects.get(city_id=1)
        self.assertEqual(city.city_name, 'Test City A')
        self.assertEqual(str(city), 'Test City A')
        self.assertEqual(City._meta.db_table, 'tblCity')
        self.assertFalse(City._meta.managed)

    def test_state_creation(self):
        state = State.objects.get(state_id=1)
        self.assertEqual(state.state_name, 'Test State A')
        self.assertEqual(str(state), 'Test State A')
        self.assertEqual(State._meta.db_table, 'tblState')
        self.assertFalse(State._meta.managed)

    def test_country_creation(self):
        country = Country.objects.get(country_id=1)
        self.assertEqual(country.country_name, 'Test Country A')
        self.assertEqual(str(country), 'Test Country A')
        self.assertEqual(Country._meta.db_table, 'tblCountry')
        self.assertFalse(Country._meta.managed)

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.city1 = City.objects.create(city_id=1, city_name='Test City A')
        cls.state1 = State.objects.create(state_id=1, state_name='Test State A')
        cls.country1 = Country.objects.create(country_id=1, country_name='Test Country A')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            company_id=1,
            regd_city=cls.city1,
            regd_state=cls.state1,
            regd_country=cls.country1,
            work_city=cls.city1,
            work_state=cls.state1,
            work_country=cls.country1,
            material_del_city=cls.city1,
            material_del_state=cls.state1,
            material_del_country=cls.country1,
        )
        cls.customer_no_address = Customer.objects.create(
            customer_id='CUST002',
            company_id=1,
        )


    def test_customer_creation(self):
        customer = Customer.objects.get(customer_id='CUST001')
        self.assertEqual(customer.company_id, 1)
        self.assertEqual(customer.regd_city, self.city1)
        self.assertEqual(str(customer), 'Customer ID: CUST001')
        self.assertEqual(Customer._meta.db_table, 'SD_Cust_master')
        self.assertFalse(Customer._meta.managed)
    
    def test_customer_fields_verbose_name(self):
        customer = Customer.objects.get(customer_id='CUST001')
        field_label = customer._meta.get_field('customer_id').verbose_name
        self.assertEqual(field_label, 'customer id') # Default Django verbose name for primary key
        field_label = customer._meta.get_field('regd_city').verbose_name
        self.assertEqual(field_label, 'regd city')

class CustomerQuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.quotation = CustomerQuotation.objects.create(
            id=101,
            quotation_no='Q001',
            company_id=1
        )

    def test_quotation_creation(self):
        quotation = CustomerQuotation.objects.get(id=101)
        self.assertEqual(quotation.quotation_no, 'Q001')
        self.assertEqual(str(quotation), 'Q001')
        self.assertEqual(CustomerQuotation._meta.db_table, 'SD_Cust_Quotation_Master')
        self.assertFalse(CustomerQuotation._meta.managed)

class CustomerPOModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.city1 = City.objects.create(city_id=1, city_name='Test City A')
        cls.state1 = State.objects.create(state_id=1, state_name='Test State A')
        cls.country1 = Country.objects.create(country_id=1, country_name='Test Country A')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            company_id=1,
            regd_city=cls.city1, regd_state=cls.state1, regd_country=cls.country1,
            work_city=cls.city1, work_state=cls.state1, work_country=cls.country1,
            material_del_city=cls.city1, material_del_state=cls.state1, material_del_country=cls.country1,
        )
        cls.customer_no_address = Customer.objects.create(
            customer_id='CUST002',
            company_id=1,
        )
        cls.quotation = CustomerQuotation.objects.create(id=101, quotation_no='Q001', company_id=1)
        cls.customer_po = CustomerPO.objects.create(
            pono='PO001',
            enquiry_id='ENQ001',
            company_id=1,
            customer=cls.customer,
            po_date='2023-01-01',
            po_received_date='2023-01-05',
            quotation=cls.quotation,
            total_amount=1000.00,
            status='Approved'
        )

    def test_customer_po_creation(self):
        po = CustomerPO.objects.get(pono='PO001')
        self.assertEqual(po.enquiry_id, 'ENQ001')
        self.assertEqual(po.customer.customer_id, 'CUST001')
        self.assertEqual(po.po_date, date(2023, 1, 1))
        self.assertEqual(po.quotation.quotation_no, 'Q001')
        self.assertEqual(po.total_amount, 1000.00)
        self.assertEqual(po.status, 'Approved')
        self.assertEqual(str(po), 'PO No: PO001 for Customer: CUST001')
        self.assertEqual(CustomerPO._meta.db_table, 'SD_Cust_PO_Master')
        self.assertFalse(CustomerPO._meta.managed)

    def test_get_full_address_details(self):
        po = CustomerPO.objects.get(pono='PO001')
        regd_addr = po.get_full_address_details('regd')
        self.assertEqual(regd_addr['city_name'], 'Test City A')
        self.assertEqual(regd_addr['state_name'], 'Test State A')
        self.assertEqual(regd_addr['country_name'], 'Test Country A')

        # Test with a customer that has no address details set
        po.customer = self.customer_no_address
        po.save()
        regd_addr_no_city = po.get_full_address_details('regd')
        self.assertEqual(regd_addr_no_city['city_name'], 'N/A')
        self.assertEqual(regd_addr_no_city['state_name'], 'N/A')
        self.assertEqual(regd_addr_no_city['country_name'], 'N/A')

    def test_get_po_details_for_report(self):
        po = CustomerPO.objects.get(pono='PO001')
        report_details = po.get_po_details_for_report()
        self.assertEqual(report_details['pono'], 'PO001')
        self.assertEqual(report_details['quotation_no'], 'Q001')
        self.assertEqual(report_details['regd_city'], 'Test City A')
        self.assertEqual(report_details['po_date'], '01-01-2023')
        self.assertEqual(report_details['po_received_date'], '05-01-2023')
        self.assertEqual(report_details['company_name'], 'Your Company Name (Placeholder)')
        self.assertIn('city_name', report_details)
        self.assertIn('state_name', report_details)
        self.assertIn('country_name', report_details)

    def test_unique_together_constraint(self):
        # Attempt to create a PO with the same pono, enquiry_id, company_id
        with self.assertRaises(Exception): # Expecting an IntegrityError or similar DB exception
            CustomerPO.objects.create(
                pono='PO001',
                enquiry_id='ENQ001',
                company_id=1,
                customer=self.customer,
                po_date='2023-01-01',
                po_received_date='2023-01-05',
                quotation=self.quotation,
                total_amount=1100.00,
                status='Pending'
            )
    
    def test_get_absolute_url(self):
        po = CustomerPO.objects.get(pono='PO001')
        self.assertEqual(po.get_absolute_url(), reverse('customerpo_detail', kwargs={'pk': po.pk}))


# --- View Integration Tests ---

class CustomerPOViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for views
        cls.city1 = City.objects.create(city_id=1, city_name='Test City A')
        cls.state1 = State.objects.create(state_id=1, state_name='Test State A')
        cls.country1 = Country.objects.create(country_id=1, country_name='Test Country A')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            company_id=1,
            regd_city=cls.city1, regd_state=cls.state1, regd_country=cls.country1,
            work_city=cls.city1, work_state=cls.state1, work_country=cls.country1,
            material_del_city=cls.city1, material_del_state=cls.state1, material_del_country=cls.country1,
        )
        cls.quotation = CustomerQuotation.objects.create(id=101, quotation_no='Q001', company_id=1)
        cls.po1 = CustomerPO.objects.create(
            pono='PO001', enquiry_id='ENQ001', company_id=1, customer=cls.customer,
            po_date='2023-01-01', po_received_date='2023-01-05', quotation=cls.quotation,
            total_amount=1000.00, status='Approved'
        )
        cls.po2 = CustomerPO.objects.create(
            pono='PO002', enquiry_id='ENQ002', company_id=1, customer=cls.customer,
            po_date='2023-02-01', po_received_date='2023-02-05', quotation=cls.quotation,
            total_amount=2000.00, status='Pending'
        )

    def setUp(self):
        self.client = Client()
    
    # --- List View Tests ---
    def test_list_view_get(self):
        response = self.client.get(reverse('customerpo_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/list.html')
        self.assertContains(response, 'Customer POs') # Check for title
        self.assertContains(response, 'Add New Customer PO') # Check for add button

    def test_table_partial_view_get(self):
        # Simulate HTMX request for partial table
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('customerpo_table_partial'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/_customerpo_table.html')
        self.assertTrue('customer_pos' in response.context)
        self.assertEqual(len(response.context['customer_pos']), 2)
        self.assertContains(response, self.po1.pono)
        self.assertContains(response, self.po2.pono)
        self.assertContains(response, '<table id="customerpoTable"') # Ensure DataTables table structure is there

    # --- Create View Tests ---
    def test_create_view_get(self):
        response = self.client.get(reverse('customerpo_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Customer PO') # Check for form title

    def test_create_view_post_success(self):
        data = {
            'pono': 'PO003',
            'enquiry_id': 'ENQ003',
            'company_id': 1,
            'customer': self.customer.customer_id, # Pass PK of customer
            'po_date': '2023-03-01',
            'po_received_date': '2023-03-05',
            'quotation': self.quotation.id, # Pass PK of quotation
            'total_amount': 1500.00,
            'status': 'Pending'
        }
        response = self.client.post(reverse('customerpo_add'), data)
        # Should redirect to list view on success
        self.assertEqual(response.status_code, 302) 
        self.assertTrue(CustomerPO.objects.filter(pono='PO003').exists())
        self.assertRedirects(response, reverse('customerpo_list'))

    def test_create_view_post_htmx_success(self):
        data = {
            'pono': 'PO004',
            'enquiry_id': 'ENQ004',
            'company_id': 1,
            'customer': self.customer.customer_id,
            'po_date': '2023-04-01',
            'po_received_date': '2023-04-05',
            'quotation': self.quotation.id,
            'total_amount': 2500.00,
            'status': 'Approved'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customerpo_add'), data, **headers)
        # HTMX requests should return 204 No Content with HX-Trigger
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshCustomerPOList')
        self.assertTrue(CustomerPO.objects.filter(pono='PO004').exists())

    def test_create_view_post_invalid_data(self):
        data = { # Invalid: po_received_date before po_date
            'pono': 'PO005',
            'enquiry_id': 'ENQ005',
            'company_id': 1,
            'customer': self.customer.customer_id,
            'po_date': '2023-05-05',
            'po_received_date': '2023-05-01', # Invalid date
            'quotation': self.quotation.id,
            'total_amount': 100.00,
            'status': 'Pending'
        }
        response = self.client.post(reverse('customerpo_add'), data)
        # Form should be re-rendered with errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/form.html')
        self.assertFalse(CustomerPO.objects.filter(pono='PO005').exists())
        self.assertContains(response, 'PO Received Date cannot be before PO Date.')

    # --- Update View Tests ---
    def test_update_view_get(self):
        response = self.client.get(reverse('customerpo_edit', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/form.html')
        self.assertContains(response, 'Edit Customer PO') # Check for form title
        self.assertContains(response, self.po1.pono) # Ensure existing data is pre-filled

    def test_update_view_post_success(self):
        data = {
            'pono': 'PO001_updated',
            'enquiry_id': 'ENQ001',
            'company_id': 1,
            'customer': self.customer.customer_id,
            'po_date': '2023-01-01',
            'po_received_date': '2023-01-05',
            'quotation': self.quotation.id,
            'total_amount': 1200.00, # Updated value
            'status': 'Cancelled' # Updated value
        }
        response = self.client.post(reverse('customerpo_edit', args=[self.po1.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.po1.refresh_from_db() # Reload object from DB to get updated values
        self.assertEqual(self.po1.pono, 'PO001_updated')
        self.assertEqual(self.po1.total_amount, 1200.00)
        self.assertEqual(self.po1.status, 'Cancelled')

    def test_update_view_post_htmx_success(self):
        data = {
            'pono': 'PO001',
            'enquiry_id': 'ENQ001_updated',
            'company_id': 1,
            'customer': self.customer.customer_id,
            'po_date': '2023-01-01',
            'po_received_date': '2023-01-05',
            'quotation': self.quotation.id,
            'total_amount': 1000.00,
            'status': 'Approved'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customerpo_edit', args=[self.po1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshCustomerPOList')
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.enquiry_id, 'ENQ001_updated')

    # --- Delete View Tests ---
    def test_delete_view_get(self):
        response = self.client.get(reverse('customerpo_delete', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.po1.pono)

    def test_delete_view_post_success(self):
        po_id_to_delete = self.po1.pk
        response = self.client.post(reverse('customerpo_delete', args=[po_id_to_delete]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(CustomerPO.objects.filter(pk=po_id_to_delete).exists())
        self.assertRedirects(response, reverse('customerpo_list'))

    def test_delete_view_post_htmx_success(self):
        po_id_to_delete = self.po2.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customerpo_delete', args=[po_id_to_delete]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshCustomerPOList')
        self.assertFalse(CustomerPO.objects.filter(pk=po_id_to_delete).exists())

    # --- Detail View Tests (replacing ASP.NET print details) ---
    def test_detail_view_by_pk(self):
        response = self.client.get(reverse('customerpo_detail', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/detail.html')
        self.assertEqual(response.context['customer_po'], self.po1)
        self.assertContains(response, self.po1.pono)
        self.assertContains(response, 'PO Information')
        self.assertContains(response, self.city1.city_name) # Ensure related address details are loaded

    def test_detail_view_legacy_params(self):
        # Test that the view can still handle ASP.NET style query parameters
        response = self.client.get(reverse('customerpo_detail_legacy'), 
                                   {'PONo': self.po1.pono, 'EnqId': self.po1.enquiry_id, 
                                    'POId': self.po1.pk, 'CompId': self.po1.company_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerpo/detail.html')
        self.assertEqual(response.context['customer_po'], self.po1)
        self.assertContains(response, self.po1.pono)

    def test_detail_view_legacy_params_not_found(self):
        response = self.client.get(reverse('customerpo_detail_legacy'), 
                                   {'PONo': 'NONEXIST', 'EnqId': 'NONEXIST', 
                                    'POId': 9999, 'CompId': 1})
        self.assertEqual(response.status_code, 404)

    # --- PDF Generation View Tests ---
    @patch('sales.views.weasyprint.HTML') # Mock WeasyPrint to avoid actual PDF generation
    def test_print_pdf_view(self, mock_html):
        # Configure the mock object to return a dummy PDF content
        mock_html_instance = mock_html.return_value
        mock_html_instance.write_pdf.return_value = b'PDF_CONTENT'

        response = self.client.get(reverse('customerpo_print_pdf', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response.content, b'PDF_CONTENT')
        self.assertIn(f'filename="customer_po_{self.po1.pono}.pdf"', response['Content-Disposition'])
        
        # Verify that WeasyPrint was called with an HTML string
        mock_html.assert_called_once()
        mock_html_instance.write_pdf.assert_called_once()
        # You can add further assertions to check the content of the HTML string passed to WeasyPrint

    @patch('sales.views.weasyprint.HTML')
    def test_print_pdf_view_legacy_params(self, mock_html):
        mock_html_instance = mock_html.return_value
        mock_html_instance.write_pdf.return_value = b'PDF_CONTENT_LEGACY'

        response = self.client.get(reverse('customerpo_print_pdf_legacy'), 
                                   {'PONo': self.po1.pono, 'EnqId': self.po1.enquiry_id, 
                                    'POId': self.po1.pk, 'CompId': self.po1.company_id})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response.content, b'PDF_CONTENT_LEGACY')
        mock_html.assert_called_once()
        mock_html_instance.write_pdf.assert_called_once()

```

### Step 5: HTMX and Alpine.js Integration

The provided templates (`list.html`, `_customerpo_table.html`, `form.html`, `confirm_delete.html`) are designed to seamlessly integrate HTMX for dynamic interactions:

-   **List View (`list.html`)**:
    -   Uses `hx-get` to load the `_customerpo_table.html` initially and on refresh.
    -   `hx-trigger="load, refreshCustomerPOList from:body"` ensures the table reloads when the page loads or when a custom `refreshCustomerPOList` event is dispatched (e.g., after a successful form submission).
    -   "Add New" button uses `hx-get` to fetch the `form.html` into a modal.
    -   Alpine.js-like `_` (Hyperscript) syntax is used for basic modal toggling (`on click add .is-active to #modal`).

-   **Table Partial (`_customerpo_table.html`)**:
    -   Contains "View", "Edit", and "Delete" buttons that use `hx-get` to fetch the respective forms (`form.html` or `confirm_delete.html`) into the modal, leveraging HTMX for partial updates.
    -   Includes the DataTables JavaScript initialization to provide client-side search, sort, and pagination without full page reloads.

-   **Form Partial (`form.html`) and Delete Confirmation (`confirm_delete.html`)**:
    -   Uses `hx-post` for form submissions.
    -   `hx-swap="none"` prevents direct DOM manipulation by HTMX, instead relying on the server to send an `HX-Trigger` header.
    -   On successful form submission (in `CreateView` or `UpdateView`), the server sends `HX-Trigger: 'refreshCustomerPOList'`. This event is caught by the `customerpoTable-container` div in `list.html`, triggering a reload of the table.
    -   The `Cancel` button and modal overlay use Hyperscript (`_`) to close the modal.

**PDF Generation (Replacing Crystal Reports):**
Instead of a Crystal Report Viewer, the `CustomerPODetailView` renders the PO details as a standard HTML page. A "Generate PDF" button links to `CustomerPOPrintPDFView`, which uses `WeasyPrint` to convert a dedicated HTML template (`print_pdf_template.html`) into a PDF. This provides a modern, robust, and open-source alternative to Crystal Reports, ensuring printability and archival capabilities.

### Final Notes

This comprehensive plan addresses the migration of the ASP.NET Customer PO Print Details page to a modern Django application.

-   **Business Value**:
    -   **Reduced Licensing Costs**: Eliminates expensive Crystal Reports licenses by moving to an open-source PDF generation solution.
    -   **Improved Performance**: Django's ORM and optimized database interactions, coupled with HTMX for partial page updates, deliver a faster and more responsive user experience.
    -   **Enhanced Maintainability**: Adherence to Django's "fat model, thin view" architecture and best practices promotes cleaner, more modular, and easier-to-understand code.
    -   **Modern User Experience**: Integration of HTMX, Alpine.js, and DataTables provides interactive and dynamic web interfaces without the complexity of traditional JavaScript frameworks.
    -   **Scalability**: Django provides a robust foundation that can scale to handle increasing data volumes and user traffic.
    -   **Future-Proofing**: Transitioning from a legacy framework to modern Python and Django ensures the application remains relevant and easier to evolve with future business requirements.
    -   **Automated Testing**: Comprehensive unit and integration tests minimize bugs, improve reliability, and accelerate development cycles for future enhancements.

-   **Implementation Guidance**:
    -   Ensure all foreign key relationships in Django models are correctly set up, aligning with the existing database schema (`managed=False`).
    -   The `company_id` field in models is a placeholder; in a production environment, it should be a `ForeignKey` to a proper `Company` model managed within your Django application.
    -   For `weasyprint`, ensure it is installed (`pip install WeasyPrint`) and its system dependencies (like `cairo`, `pango`, `gdk-pixbuf`) are met on the deployment server.
    -   The `get_object` method in `CustomerPODetailView` and `CustomerPOPrintPDFView` has been modified to handle both `pk` and legacy query string parameters, facilitating a smoother transition. Once legacy parameters are phased out, this method can be simplified to rely solely on `pk`.
```