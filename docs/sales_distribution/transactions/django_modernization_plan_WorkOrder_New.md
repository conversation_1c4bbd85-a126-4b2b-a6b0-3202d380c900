This modernization plan outlines the strategy to transition your ASP.NET "Work Order - New" functionality to a robust and modern Django application. We will leverage AI-assisted automation to transform the legacy code into highly efficient and maintainable Django components, focusing on an improved user experience and streamlined backend operations.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the ASP.NET code, the core data for this page is primarily drawn from `SD_Cust_PO_Master`, with lookups and filtering involving `SD_Cust_Master`, `tblFinancial_master`, `tblHR_OfficeStaff`, and `SD_Cust_WorkOrder_Master`. A temporary table, `SD_Cust_WorkOrder_Products_Temp`, is also involved for session-specific cleanup.

*   **Primary Table for Listing:** `SD_Cust_PO_Master`
    *   Columns: `PONo`, `POId`, `EnqId`, `CustomerId`, `SessionId`, `FinYearId`, `PODate`, `SysDate`, `CompId`.
*   **Related Lookup Tables:**
    *   `SD_Cust_Master`: `CustomerId`, `CustomerName`
    *   `tblFinancial_master`: `FinYearId`, `FinYear`
    *   `tblHR_OfficeStaff`: `EmpId` (maps to `SessionId`), `Title`, `EmployeeName`
    *   `SD_Cust_WorkOrder_Master`: `PONo`, `CloseOpen` (for filtering)
    *   `tblCompany`: (Implied by `CompId` session variable)
*   **Temporary Table (for cleanup):** `SD_Cust_WorkOrder_Products_Temp`
    *   Columns: `SessionId`, `CompId`, `FinYearId`

## Step 2: Identify Backend Functionality

The ASP.NET page `WorkOrder_New.aspx` primarily serves as a search and listing interface for Purchase Orders, allowing users to select a PO to proceed with creating a new Work Order.

*   **Read Operation (List & Filter):**
    *   Retrieves Purchase Orders from `SD_Cust_PO_Master`.
    *   Filters records based on user-selected criteria (`Customer Name`, `Enquiry No`, or `PO No`).
    *   Performs lookups to display related `CustomerName`, `FinYear`, and `EmployeeName`.
    *   Applies a crucial filter: only displays POs that are *not* closed (where `SD_Cust_WorkOrder_Master.CloseOpen` is `0`).
    *   Supports pagination and implicitly sorting (via `GridView`).
*   **Search Functionality:**
    *   Dynamic search criteria selection using a dropdown.
    *   Autocomplete for Customer Names.
*   **Temporary Data Cleanup:**
    *   On page load, clears records from `SD_Cust_WorkOrder_Products_Temp` specific to the current user's session, company, and financial year.
*   **Navigation:**
    *   Hyperlink fields direct to `WorkOrder_New_Details.aspx` with various PO-related query parameters, suggesting the next step is to create a new work order from the selected purchase order.
*   **No Direct Create/Update/Delete:** This page does not directly create, update, or delete Purchase Order records. Its role is primarily for searching and initiating a new Work Order creation process.

## Step 3: Infer UI Components

The ASP.NET controls translate to standard HTML forms and table elements, enhanced by HTMX and Alpine.js for dynamic behavior.

*   **Dropdown List (`DropDownList1`):** A `<select>` element to choose the search type (Customer Name, Enquiry No, PO No).
*   **Text Boxes (`txtEnqId`, `TxtSearchValue`):** `<input type="text">` fields for search input.
*   **Autocomplete (`AutoCompleteExtender` for `TxtSearchValue`):** An `<input>` field with HTMX-driven dynamic suggestions.
*   **Button (`btnSearch`):** A `<button>` to trigger the search.
*   **Data Grid (`SearchGridView1`):** A `<table>` element, which will be transformed into a DataTables instance for client-side pagination, sorting, and searching.
*   **Message Label (`Label2`):** A `<div>` or `<p>` to display messages.
*   **Master Page Integration:** Content is placed within `ContentPlaceHolder`s. In Django, this means extending a `base.html` template.
*   **Client-side visibility logic:** `txtEnqId.Visible = false; TxtSearchValue.Visible = true;` based on dropdown selection will be handled by Alpine.js `x-show` directives.

## Step 4: Generate Django Code

We will create a Django application named `sales` to encapsulate this functionality.

### 4.1 Models (`sales/models.py`)

We'll define Django models that map to your existing database tables using `managed = False`. We'll also implement a custom manager for `PurchaseOrder` to encapsulate the complex data retrieval and filtering logic (`BindDataCust`).

```python
from django.db import models
from django.db.models import F, Q, Value
from django.db.models.functions import Concat
from django.utils import timezone # For date handling if needed

# Utility for date conversion (mimicking fun.FromDateDMY)
def from_date_dmy(date_str):
    """Converts 'YYYY-MM-DD HH:MM:SS' to 'DD/MM/YYYY'."""
    try:
        dt_obj = timezone.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        return dt_obj.strftime('%d/%m/%Y')
    except (ValueError, TypeError):
        return "" # Handle invalid date formats gracefully

class Company(models.Model):
    # Assuming primary key for company is CompId
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    compname = models.CharField(db_column='CompName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany' # Assuming this is the company table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.compname or f"Company {self.compid}"

class FinancialYear(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear or f"FinYear {self.finyearid}"

class Customer(models.Model):
    customerid = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) # Use CharField for non-numeric IDs
    customername = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True) # Foreign key
    
    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customername or f"Customer {self.customerid}"

class Employee(models.Model):
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be non-integer
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True) # Foreign key

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}.{self.employeename or ''}".strip('.') or f"Employee {self.empid}"

class WorkOrderMaster(models.Model):
    # This model is primarily for the 'CloseOpen' filter
    poid = models.IntegerField(db_column='POId', primary_key=True) # Assuming this links to SD_Cust_PO_Master.POId
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    closeopen = models.IntegerField(db_column='CloseOpen', blank=True, null=True) # 0 for open, 1 for closed
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.pono or f"Work Order {self.poid}"

class WorkOrderProductTemp(models.Model):
    # This table is for cleanup and might not need full model fields for this view
    # Define primary key if one exists, or combine fields if it's a composite key without one.
    # For now, let's assume it's just a representation for the delete operation.
    sessionid = models.CharField(db_column='SessionId', max_length=50, primary_key=True) # Assuming composite primary key or unique index
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Products_Temp'
        verbose_name = 'Work Order Product Temp'
        verbose_name_plural = 'Work Order Product Temps'
        # If no explicit PK, define unique_together or rely on default Django behavior
        unique_together = (('sessionid', 'compid', 'finyearid'),) # Assuming this combination is unique

    def __str__(self):
        return f"Temp WO Product for {self.sessionid}"

class PurchaseOrderManager(models.Manager):
    def get_search_results(self, comp_id, fin_year_id, search_by='0', search_value='', enquiry_po_value=''):
        """
        Fetches and filters Purchase Orders, joining necessary related data.
        Mimics the BindDataCust logic.
        """
        queryset = self.get_queryset().select_related(
            'customerid', 'finyearid', 'sessionid', 'compid'
        ).filter(
            compid=comp_id,
            finyearid__lte=fin_year_id # Check for FinYearId <= current FinYearId
        ).order_by('-poid') # Order by POId Desc

        # Apply filtering based on search_by and search_value/enquiry_po_value
        if search_by == '1' and enquiry_po_value: # Enquiry No
            queryset = queryset.filter(enqid=enquiry_po_value)
        elif search_by == '2' and enquiry_po_value: # PO No
            queryset = queryset.filter(pono=enquiry_po_value)
        elif search_by == '0' and search_value: # Customer Name
            # The ASP.NET fun.getCode extracts customer ID from "Name [Code]"
            # We will assume search_value is either the name or "Name [Code]"
            customer_code = None
            if '[' in search_value and ']' in search_value:
                try:
                    customer_code = search_value.split('[')[-1].strip(']')
                except IndexError:
                    pass # Handle cases where format is not as expected
            
            if customer_code:
                queryset = queryset.filter(customerid__customerid=customer_code)
            else:
                queryset = queryset.filter(customerid__customername__icontains=search_value)

        # Apply the "CloseOpen" filter from SD_Cust_WorkOrder_Master
        # This is a bit tricky. We need to exclude POs that have a corresponding
        # WorkOrderMaster record with CloseOpen = 1 (closed).
        # We can achieve this by doing a subquery or a left join and checking for null/specific value.
        # Given the original code's multiple queries, we'll mimic the logic:
        # First, get all POs that are *closed*
        closed_po_ids = WorkOrderMaster.objects.filter(
            compid=comp_id,
            finyearid__lte=fin_year_id,
            closeopen=1
        ).values_list('pono', flat=True) # Get only the PONos of closed work orders

        # Exclude those closed POs from the main queryset
        queryset = queryset.exclude(pono__in=closed_po_ids)

        # Now, annotate the queryset with display fields
        # This makes the "fat model" approach where the model method provides all necessary display data
        annotated_queryset = queryset.annotate(
            fin_year_display=F('finyearid__finyear'),
            customer_name_display=F('customerid__customername'),
            employee_name_display=Concat(F('sessionid__title'), Value('.'), F('sessionid__employeename'), output_field=models.CharField())
        ).values(
            'pono', 'poid', 'enqid', 'customerid__customerid', 'customerid__customername',
            'podate', 'sysdate', 'fin_year_display', 'employee_name_display'
        )
        
        # Convert dates to DMY format in Python if direct SQL conversion isn't preferred or possible
        for item in annotated_queryset:
            item['podate_display'] = from_date_dmy(str(item['podate'])) if item['podate'] else ''
            item['sysdate_display'] = from_date_dmy(str(item['sysdate'])) if item['sysdate'] else ''
            
        return list(annotated_queryset) # Return as a list of dicts for simpler template iteration

class PurchaseOrder(models.Model):
    # SD_Cust_PO_Master columns
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    poid = models.IntegerField(db_column='POId', primary_key=True) # Assuming POId is the unique primary key
    enqid = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customerid = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='purchase_orders', blank=True, null=True)
    sessionid = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='generated_pos', blank=True, null=True)
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='purchase_orders', blank=True, null=True)
    podate = models.DateTimeField(db_column='PODate', blank=True, null=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='company_pos', blank=True, null=True)

    objects = PurchaseOrderManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.pono or f"PO {self.poid}"

    # No specific business logic methods beyond the manager's query logic for this page
    # Additional methods would go here if there were calculations or state changes on a single PO.

```

### 4.2 Forms (`sales/forms.py`)

For the search functionality, we will use a regular Django `Form` rather than a `ModelForm`, as it doesn't directly map to a single model instance.

```python
from django import forms

class PurchaseOrderSearchForm(forms.Form):
    SEARCH_BY_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        # 'Select' option from ASP.NET was essentially 'Enquiry No' / 'PO No'
        # For simplicity, we'll map 'Select' to a default of 0 (Customer Name) or remove it.
        # Let's remove 'Select' as it duplicates functionality
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'x-model': 'searchType', 'hx-trigger': 'change', 'hx-get': "{% url 'workorder_new_table' %}", 'hx-target': '#purchaseorderTable-container', 'hx-swap': 'innerHTML'})
    )
    # TxtSearchValue (for Customer Name search and autocomplete)
    search_value = forms.CharField(
        label="Search Value",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'hx-get': "{% url 'sales:customer_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#customer-suggestions',
            'hx-swap': 'innerHTML',
            'x-show': "searchType === '0'", # Alpine.js show/hide
            'placeholder': 'Enter Customer Name or [Code]'
        })
    )
    # txtEnqId (for Enquiry No or PO No search)
    enquiry_po_value = forms.CharField(
        label="Enquiry/PO No",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'x-show': "searchType === '1' || searchType === '2'", # Alpine.js show/hide
            'placeholder': 'Enter Enquiry or PO Number'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply Tailwind classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Select)):
                current_class = field.widget.attrs.get('class', '')
                if 'box3' not in current_class: # Ensure existing classes are not duplicated
                    field.widget.attrs['class'] = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ' + current_class
                if field_name == 'search_by':
                     # Specific styles for dropdown
                     field.widget.attrs['class'] += ' mt-1'


```

### 4.3 Views (`sales/views.py`)

Views will be thin, delegating complex data retrieval and filtering to the `PurchaseOrderManager`. We'll also need a separate view for the HTMX-driven autocomplete.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import connection # For executing raw SQL if absolutely necessary, though ORM preferred

from .models import PurchaseOrder, Customer, WorkOrderProductTemp, Company, FinancialYear
from .forms import PurchaseOrderSearchForm

class WorkOrderNewListView(ListView):
    """
    Displays the main page for searching Purchase Orders.
    This view also handles the session-based cleanup from the ASP.NET Page_Load.
    """
    model = PurchaseOrder
    template_name = 'sales/workorder/list.html'
    context_object_name = 'purchase_orders'
    # Initial queryset will be empty or a default to allow HTMX to load.
    # The actual data comes from the _table partial.

    def get_queryset(self):
        # Initial load, HTMX will handle subsequent loads
        return []

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = PurchaseOrderSearchForm(self.request.GET or None)
        # Pass initial form data for Alpine.js
        context['initial_search_type'] = self.request.GET.get('search_by', '0')
        return context

    def dispatch(self, request, *args, **kwargs):
        """
        Handles the temporary product table cleanup on page load, mimicking ASP.NET.
        This would typically be done in a separate service layer or via signals
        if it's a cross-cutting concern. For direct translation, we put it here.
        """
        user_session_id = request.session.get('username') # 'sId' in ASP.NET
        company_id = request.session.get('compid') # 'CompId' in ASP.NET
        financial_year_id = request.session.get('finyear') # 'FinYearId' in ASP.NET

        if user_session_id and company_id is not None and financial_year_id is not None:
            # Delete records from SD_Cust_WorkOrder_Products_Temp
            WorkOrderProductTemp.objects.filter(
                sessionid=user_session_id,
                compid=company_id,
                finyearid=financial_year_id
            ).delete()
            # print(f"Cleaned up WorkOrderProductTemp for session {user_session_id}, comp {company_id}, finyear {financial_year_id}") # For debugging

        # Add success message from query string if available
        msg = request.GET.get('msg')
        if msg:
            messages.success(request, msg)

        return super().dispatch(request, *args, **kwargs)


class PurchaseOrderTablePartialView(ListView):
    """
    Renders the DataTables partial, used by HTMX to load and refresh the PO list.
    This view contains the main search/filtering logic.
    """
    model = PurchaseOrder
    template_name = 'sales/workorder/_purchaseorder_table.html'
    context_object_name = 'purchase_orders'

    def get_queryset(self):
        user_session_id = self.request.session.get('username')
        company_id = self.request.session.get('compid')
        financial_year_id = self.request.session.get('finyear')

        # Dummy data for demonstration if session values are missing
        # In a real app, you'd likely have authentication and ensure these are present.
        if not all([user_session_id, company_id is not None, financial_year_id is not None]):
            # Log an error or raise an exception if session data is critical
            messages.error(self.request, "Missing session data for filtering. Please log in.")
            return PurchaseOrder.objects.none() # Return empty queryset

        form = PurchaseOrderSearchForm(self.request.GET)
        search_by = form.data.get('search_by', '0') # Default to Customer Name
        search_value = form.data.get('search_value', '')
        enquiry_po_value = form.data.get('enquiry_po_value', '')

        # Use the custom manager method to get the filtered and annotated data
        queryset = PurchaseOrder.objects.get_search_results(
            comp_id=company_id,
            fin_year_id=financial_year_id,
            search_by=search_by,
            search_value=search_value,
            enquiry_po_value=enquiry_po_value
        )
        return queryset

    # This is a thin view, passing most work to the model manager.
    # The `get_queryset` method returns a list of dictionaries, which ListView can iterate over.


class CustomerAutocompleteView(View):
    """
    Provides customer name suggestions for the autocomplete input, via HTMX.
    Mimics the ASP.NET WebMethod `sql`.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        company_id = request.session.get('compid')

        if not company_id:
            return JsonResponse([], safe=False)

        # Assuming 'CustomerName' and 'CustomerId' fields in SD_Cust_Master
        customers = Customer.objects.filter(
            compid=company_id,
            customername__icontains=prefix_text
        ).order_by('customername')[:20] # Limit results as per ASP.NET

        suggestions = [
            f"{customer.customername} [{customer.customerid}]"
            for customer in customers
        ]
        return JsonResponse(suggestions, safe=False)

```

### 4.4 Templates

We'll create the main list template and a partial for the DataTables content. All templates extend `core/base.html`.

**`sales/templates/sales/workorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchType: '{{ initial_search_type }}', search_value: '', enquiry_po_value: '' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Work Order - New (Select Purchase Order)</h2>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Purchase Orders</h3>
        <form id="po-search-form"
              hx-get="{% url 'sales:workorder_new_table' %}"
              hx-target="#purchaseorderTable-container"
              hx-swap="innerHTML"
              hx-indicator="#search-loading-indicator"
              hx-trigger="submit, change from:#id_search_by, keyup delay:500ms from:#id_search_value, keyup delay:500ms from:#id_enquiry_po_value">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_by.label }}
                    </label>
                    {{ form.search_by }}
                </div>
                <div>
                    <label for="{{ form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Search Value
                    </label>
                    <div x-show="searchType === '0'">
                         {{ form.search_value }}
                         <div id="customer-suggestions" class="absolute bg-white border border-gray-300 mt-1 w-full z-10 shadow-lg"></div>
                    </div>
                    <div x-show="searchType === '1' || searchType === '2'">
                         {{ form.enquiry_po_value }}
                    </div>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                        Search
                    </button>
                    <span id="search-loading-indicator" class="htmx-indicator ml-2">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    </span>
                </div>
            </div>
            {% if form.errors %}
                <div class="text-red-500 text-xs mt-2">
                    {% for field in form %}
                        {% for error in field.errors %}
                            <p>{{ field.label }}: {{ error }}</p>
                        {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="purchaseorderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'sales:workorder_new_table' %}?{{ request.GET.urlencode }}" {# Pass initial query params #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Purchase Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderNewPage', () => ({
            searchType: '{{ initial_search_type }}',
            // Ensure these are reactive in Alpine.js if needed for client-side state
            search_value: '',
            enquiry_po_value: '',
            init() {
                // Initialize form values from Django context for Alpine.js
                this.searchType = document.getElementById('id_search_by').value;
                this.search_value = document.getElementById('id_search_value') ? document.getElementById('id_search_value').value : '';
                this.enquiry_po_value = document.getElementById('id_enquiry_po_value') ? document.getElementById('id_enquiry_po_value').value : '';
            }
        }));
    });
</script>
{% endblock %}
```

**`sales/templates/sales/workorder/_purchaseorder_table.html`**

```html
<div class="bg-white p-6 rounded-lg shadow-lg overflow-x-auto">
    {% if purchase_orders %}
    <table id="purchaseOrderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for po in purchase_orders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ po.fin_year_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.customer_name_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ po.customerid__customerid }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ po.enqid }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">
                    <a href="{% url 'sales:workorder_new_details' %}?PONo={{ po.pono }}&CustomerId={{ po.customerid__customerid }}&EnqId={{ po.enqid }}&POId={{ po.poid }}&ModId=2&SubModId=13"
                       class="text-blue-600 hover:underline">
                        {{ po.pono }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ po.podate_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ po.sysdate_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.employee_name_display }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-8">
        <p class="font-bold text-lg text-red-700">No data to display !</p>
    </div>
    {% endif %}
</div>

<script>
$(document).ready(function() {
    // Only initialize DataTable if the table element actually exists.
    // This helps prevent errors if the template renders an empty state.
    if ($.fn.DataTable.isDataTable('#purchaseOrderTable')) {
        $('#purchaseOrderTable').DataTable().destroy(); // Destroy existing instance if any
    }
    $('#purchaseOrderTable').DataTable({
        "pageLength": 17, // As per ASP.NET GridView PageSize
        "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
        "pagingType": "full_numbers", // For full pagination controls
        "responsive": true, // Make table responsive
        "autoWidth": false, // Disable auto-width to allow more control with CSS
        "order": [] // Disable initial sorting as it's handled by server-side query
    });
});
</script>
```

### 4.5 URLs (`sales/urls.py`)

```python
from django.urls import path
from .views import WorkOrderNewListView, PurchaseOrderTablePartialView, CustomerAutocompleteView

app_name = 'sales' # Namespace for URLs

urlpatterns = [
    path('workorder-new/', WorkOrderNewListView.as_view(), name='workorder_new_list'),
    path('workorder-new/table/', PurchaseOrderTablePartialView.as_view(), name='workorder_new_table'),
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    # This URL is for navigation to the "details" page, assuming it's part of the same app
    # You'll need to create WorkOrderNewDetailsView in a separate file (e.g., workorder_details_views.py)
    # or define it here if it's simple enough.
    # For now, it's just a placeholder URL, matching the ASP.NET format.
    path('workorder-new-details/', WorkOrderNewListView.as_view(), name='workorder_new_details'), # Placeholder
]
```

### 4.6 Tests (`sales/tests.py`)

These tests cover the model manager's data retrieval and the views' rendering and logic, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db import connection

from .models import (
    PurchaseOrder, Customer, Employee, FinancialYear, Company,
    WorkOrderMaster, WorkOrderProductTemp
)

class SalesModelTest(TestCase):
    """
    Tests for all sales-related models, ensuring correct mapping and data integrity.
    """
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all model tests
        cls.company = Company.objects.create(compid=1, compname='Test Company')
        cls.fin_year_2023 = FinancialYear.objects.create(finyearid=2023, finyear='2023-2024')
        cls.fin_year_2022 = FinancialYear.objects.create(finyearid=2022, finyear='2022-2023')
        cls.customer1 = Customer.objects.create(customerid='CUST001', customername='Alice Inc.', compid=cls.company)
        cls.customer2 = Customer.objects.create(customerid='CUST002', customername='Bob Ltd.', compid=cls.company)
        cls.employee1 = Employee.objects.create(empid='EMP001', title='Mr', employeename='John Doe', compid=cls.company)
        cls.employee2 = Employee.objects.create(empid='EMP002', title='Ms', employeename='Jane Smith', compid=cls.company)

        # Create PurchaseOrder instances
        cls.po1 = PurchaseOrder.objects.create(
            poid=1, pono='PO-001', enqid='ENQ-001',
            customerid=cls.customer1, sessionid=cls.employee1,
            finyearid=cls.fin_year_2023, podate='2023-01-15 10:00:00',
            sysdate='2023-01-15 10:05:00', compid=cls.company
        )
        cls.po2 = PurchaseOrder.objects.create(
            poid=2, pono='PO-002', enqid='ENQ-002',
            customerid=cls.customer2, sessionid=cls.employee2,
            finyearid=cls.fin_year_2023, podate='2023-02-20 11:00:00',
            sysdate='2023-02-20 11:05:00', compid=cls.company
        )
        cls.po3_closed = PurchaseOrder.objects.create(
            poid=3, pono='PO-003', enqid='ENQ-003',
            customerid=cls.customer1, sessionid=cls.employee1,
            finyearid=cls.fin_year_2023, podate='2023-03-01 12:00:00',
            sysdate='2023-03-01 12:05:00', compid=cls.company
        )
        cls.po4_old_year = PurchaseOrder.objects.create(
            poid=4, pono='PO-004', enqid='ENQ-004',
            customerid=cls.customer1, sessionid=cls.employee1,
            finyearid=cls.fin_year_2022, podate='2022-12-01 12:00:00',
            sysdate='2022-12-01 12:05:00', compid=cls.company
        )

        # Create a closed work order entry for PO-003
        WorkOrderMaster.objects.create(
            poid=cls.po3_closed.poid, pono=cls.po3_closed.pono, closeopen=1,
            finyearid=cls.fin_year_2023, compid=cls.company
        )

        # Create a temp product entry
        WorkOrderProductTemp.objects.create(
            sessionid='test_user', compid=cls.company, finyearid=cls.fin_year_2023
        )

    def test_purchaseorder_creation(self):
        po = PurchaseOrder.objects.get(poid=1)
        self.assertEqual(po.pono, 'PO-001')
        self.assertEqual(po.customerid.customername, 'Alice Inc.')
        self.assertEqual(po.sessionid.employeename, 'John Doe')
        self.assertEqual(po.finyearid.finyear, '2023-2024')

    def test_purchaseorder_manager_get_search_results(self):
        # Test basic retrieval
        results = PurchaseOrder.objects.get_search_results(
            self.company.compid, self.fin_year_2023.finyearid
        )
        self.assertEqual(len(results), 3) # po1, po2, po4_old_year (as finyearid <= 2023), po3_closed should be excluded
        self.assertIn('PO-001', [r['pono'] for r in results])
        self.assertIn('PO-002', [r['pono'] for r in results])
        self.assertIn('PO-004', [r['pono'] for r in results]) # Old year is included if <= current finyear

        # Test filter by Customer Name (exact match from code)
        results = PurchaseOrder.objects.get_search_results(
            self.company.compid, self.fin_year_2023.finyearid,
            search_by='0', search_value='Alice Inc. [CUST001]'
        )
        self.assertEqual(len(results), 2) # po1, po4_old_year
        self.assertIn('PO-001', [r['pono'] for r in results])

        # Test filter by Customer Name (partial match)
        results = PurchaseOrder.objects.get_search_results(
            self.company.compid, self.fin_year_2023.finyearid,
            search_by='0', search_value='Alice'
        )
        self.assertEqual(len(results), 2) # po1, po4_old_year

        # Test filter by Enquiry No
        results = PurchaseOrder.objects.get_search_results(
            self.company.compid, self.fin_year_2023.finyearid,
            search_by='1', enquiry_po_value='ENQ-002'
        )
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['pono'], 'PO-002')

        # Test filter by PO No
        results = PurchaseOrder.objects.get_search_results(
            self.company.compid, self.fin_year_2023.finyearid,
            search_by='2', enquiry_po_value='PO-001'
        )
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['pono'], 'PO-001')

        # Test closed PO exclusion
        results = PurchaseOrder.objects.get_search_results(
            self.company.compid, self.fin_year_2023.finyearid,
        )
        self.assertNotIn('PO-003', [r['pono'] for r in results]) # PO-003 should be excluded

    def test_temp_product_delete(self):
        # Ensure initial temp data exists
        self.assertTrue(WorkOrderProductTemp.objects.filter(sessionid='test_user').exists())
        
        # Call dispatch to trigger cleanup
        c = Client()
        session = c.session
        session['username'] = 'test_user'
        session['compid'] = self.company.compid
        session['finyear'] = self.fin_year_2023.finyearid
        session.save()
        
        response = c.get(reverse('sales:workorder_new_list'))
        self.assertEqual(response.status_code, 200)

        # Verify temp data is deleted
        self.assertFalse(WorkOrderProductTemp.objects.filter(sessionid='test_user').exists())

class SalesViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all view tests
        cls.company = Company.objects.create(compid=1, compname='Test Company')
        cls.fin_year_2023 = FinancialYear.objects.create(finyearid=2023, finyear='2023-2024')
        cls.customer1 = Customer.objects.create(customerid='CUST001', customername='Alice Inc.', compid=cls.company)
        cls.employee1 = Employee.objects.create(empid='EMP001', title='Mr', employeename='John Doe', compid=cls.company)
        cls.po1 = PurchaseOrder.objects.create(
            poid=1, pono='PO-001', enqid='ENQ-001',
            customerid=cls.customer1, sessionid=cls.employee1,
            finyearid=cls.fin_year_2023, podate='2023-01-15 10:00:00',
            sysdate='2023-01-15 10:05:00', compid=cls.company
        )

    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['username'] = 'test_user'
        session['compid'] = self.company.compid
        session['finyear'] = self.fin_year_2023.finyearid
        session.save() # Must save the session after modifying it

    def test_workorder_new_list_view(self):
        response = self.client.get(reverse('sales:workorder_new_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder/list.html')
        self.assertIn('form', response.context)
        self.assertIn('initial_search_type', response.context)

        # Test message from query string
        response_with_msg = self.client.get(reverse('sales:workorder_new_list') + '?msg=TestMessage')
        messages = list(get_messages(response_with_msg.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'TestMessage')

    def test_purchaseorder_table_partial_view_no_search(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:workorder_new_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder/_purchaseorder_table.html')
        self.assertIn('purchase_orders', response.context)
        self.assertTrue(len(response.context['purchase_orders']) > 0) # Should have POs from setUpTestData
        self.assertContains(response, 'PO-001') # Check if data is rendered

    def test_purchaseorder_table_partial_view_with_search(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Search by Enquiry No
        response = self.client.get(reverse('sales:workorder_new_table'), {
            'search_by': '1', 'enquiry_po_value': 'ENQ-001'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder/_purchaseorder_table.html')
        self.assertEqual(len(response.context['purchase_orders']), 1)
        self.assertEqual(response.context['purchase_orders'][0]['pono'], 'PO-001')

        # Search by Customer Name with code
        response = self.client.get(reverse('sales:workorder_new_table'), {
            'search_by': '0', 'search_value': 'Alice Inc. [CUST001]'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['purchase_orders']), 1) # Depends on other test data, adjust if needed
        self.assertEqual(response.context['purchase_orders'][0]['customerid__customerid'], 'CUST001')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('sales:customer_autocomplete'), {'prefixText': 'Ali'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Alice Inc. [CUST001]', data)
        self.assertEqual(len(data), 1) # Only one customer matching 'Ali'

        response_no_match = self.client.get(reverse('sales:customer_autocomplete'), {'prefixText': 'XYZ'})
        self.assertEqual(response_no_match.status_code, 200)
        self.assertEqual(len(response_no_match.json()), 0)

        # Test with missing company_id in session
        del self.client.session['compid']
        self.client.session.save()
        response_no_compid = self.client.get(reverse('sales:customer_autocomplete'), {'prefixText': 'Ali'})
        self.assertEqual(response_no_compid.status_code, 200)
        self.assertEqual(len(response_no_compid.json()), 0)


```

## Step 5: HTMX and Alpine.js Integration

*   **HTMX for Search & Table Refresh:**
    *   The `PurchaseOrderSearchForm` uses `hx-get` to trigger a request to `{% url 'sales:workorder_new_table' %}`.
    *   `hx-target="#purchaseorderTable-container"` ensures only the table div is updated, not the entire page.
    *   `hx-trigger="submit, change from:#id_search_by, keyup delay:500ms from:#id_search_value, keyup delay:500ms from:#id_enquiry_po_value"` allows the search to be triggered by form submission, dropdown changes, or a slight delay after typing in the search boxes.
    *   `hx-indicator="#search-loading-indicator"` provides visual feedback during AJAX requests.
*   **HTMX for Autocomplete:**
    *   The `search_value` input in `PurchaseOrderSearchForm` has `hx-get` to `{% url 'sales:customer_autocomplete' %}` and `hx-target="#customer-suggestions"` to populate a dropdown list of suggestions.
    *   `hx-trigger="keyup changed delay:500ms"` ensures suggestions appear as the user types with a small delay.
*   **Alpine.js for UI State (Show/Hide Inputs):**
    *   The main `list.html` uses `x-data` to initialize `searchType`.
    *   `x-show="searchType === '0'` (for `search_value`) and `x-show="searchType === '1' || searchType === '2'"` (for `enquiry_po_value`) dynamically show/hide the correct input field based on the selected `search_by` option in the dropdown (`x-model="searchType"`).
*   **DataTables for List Views:**
    *   The `_purchaseorder_table.html` partial initializes a DataTables instance on the `purchaseOrderTable` element using `$(document).ready()`. This ensures DataTables features like client-side pagination, sorting, and search are active.
    *   The `pageLength` and `lengthMenu` are configured to match the original ASP.NET GridView's behavior.
    *   `$.fn.DataTable.isDataTable('#purchaseOrderTable').destroy()` is added to prevent re-initialization issues when HTMX reloads the partial.

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values from the prompt have been replaced with concrete names derived from the ASP.NET code analysis.
*   **DRY Templates:** `_purchaseorder_table.html` is a partial, demonstrating reusability. The main `list.html` extends `core/base.html` (which is assumed to contain all CDN links for DataTables, HTMX, Alpine.js, and Tailwind CSS).
*   **Fat Model, Thin View:** The `PurchaseOrderManager` in `sales/models.py` encapsulates the complex data filtering and joining logic, keeping the `PurchaseOrderTablePartialView` clean and focused on rendering.
*   **Comprehensive Tests:** Unit tests for models and integration tests for views ensure the correctness and maintainability of the migrated code. Test coverage aims for at least 80%.
*   **Automation Focus:** This plan provides a clear, structured approach for automated conversion. The defined models, forms, views, URLs, and templates are designed to be generated by an AI, with logical groupings and adherence to modern Django patterns. The HTMX and Alpine.js integration patterns are also systematic, simplifying frontend automation.
*   **Business Value:** This modernization will result in a faster, more responsive user experience due to HTMX's partial page updates. The clear separation of concerns (models for logic, views for coordination, templates for presentation) leads to a highly maintainable and scalable application. The use of standard, modern frameworks (Django, HTMX, Alpine.js, DataTables, Tailwind CSS) significantly reduces technical debt and improves developer productivity.