## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The provided ASP.NET code for `Dispatch_Gunrail_Dashbord.aspx` and its code-behind `Dispatch_Gunrail_Dashbord.aspx.cs` is a foundational page with no explicit UI elements or business logic. This indicates it serves as a placeholder or a master page content holder for a module. For a dashboard, we can infer the need to display and manage a list of "Dispatch Gunrail" entries.

Given the absence of specific details, we will proceed with a common pattern for dashboard pages: displaying a list of records and providing functionality to add, edit, or delete those records. This approach demonstrates the full modernization process using the specified technologies.

---

## Step 1: Extract Database Schema

**Analysis:** The provided `.aspx` and `.aspx.cs` files do not contain any explicit database connection strings, `SqlDataSource` controls, or SQL queries. This means we must infer the underlying database table and its structure based on the page's name: `Dispatch_Gunrail_Dashbord`.

**Inferred Schema:**
-   **[TABLE_NAME]**: `tbl_DispatchGunrail` (Common naming convention in legacy ASP.NET systems).
-   **Inferred Columns**:
    -   `GunrailId` (Primary Key, integer)
    -   `DispatchNumber` (String, unique identifier for a dispatch)
    -   `GunrailNumber` (String, identifier for the gunrail item)
    -   `DispatchDate` (Date, when the dispatch occurred)
    -   `OriginLocation` (String, where the dispatch originated)
    -   `DestinationLocation` (String, where the dispatch is headed)
    -   `Status` (String, e.g., 'Pending', 'Dispatched', 'Delivered')
    -   `Remarks` (Text, any additional notes)

---

## Step 2: Identify Backend Functionality

**Analysis:** With an empty `Page_Load` method and no UI controls, there's no explicit backend functionality defined in the provided C# code.

**Inferred Functionality (Typical for a Dashboard):**
-   **Read (R)**: Display a list of all "Dispatch Gunrail" entries. This would typically be populated into a `GridView` in ASP.NET.
-   **Create (C)**: Ability to add a new "Dispatch Gunrail" entry. This would typically involve a form with input fields and a submit button.
-   **Update (U)**: Ability to modify an existing "Dispatch Gunrail" entry. This would typically involve editing a record in a `GridView` or opening a form pre-filled with data.
-   **Delete (D)**: Ability to remove a "Dispatch Gunrail" entry. This would typically involve a delete button next to each record in a `GridView`.
-   **Validation**: Input validation for fields during Create/Update operations.

---

## Step 3: Infer UI Components

**Analysis:** The `.aspx` file is blank, providing no UI component information.

**Inferred UI Components (Typical for a Dashboard):**
-   **Data List**: A `GridView` (ASP.NET) equivalent, which will be migrated to a Django template using a `<table>` and enhanced with `DataTables.js` for client-side features.
-   **Input Forms**: `TextBox` controls for text, `DropDownList` for selections (like `Status`), and `Button` controls for actions (Add, Save, Edit, Delete). These will become Django Forms with corresponding HTML input types and `hx-post` actions.
-   **Modals**: For forms (add/edit) and delete confirmations, enabling a seamless user experience without full page reloads, implemented using HTMX and Alpine.js.

---

## Step 4: Generate Django Code

We will create a new Django application, perhaps named `sales_distribution` to reflect the ASP.NET module path `Module_SalesDistribution_Transactions`.

### 4.1 Models (`sales_distribution/models.py`)

**Task:** Create a Django model based on the inferred database schema for `tbl_DispatchGunrail`.

```python
from django.db import models

class DispatchGunrail(models.Model):
    # This ID field is typically auto-generated by Django, but if the legacy table has its own PK,
    # Django will manage it automatically. Assuming it's `GunrailId` from legacy.
    gunrail_id = models.AutoField(db_column='GunrailId', primary_key=True)
    dispatch_number = models.CharField(db_column='DispatchNumber', max_length=100, unique=True, verbose_name="Dispatch Number")
    gunrail_number = models.CharField(db_column='GunrailNumber', max_length=100, verbose_name="Gunrail Number")
    dispatch_date = models.DateField(db_column='DispatchDate', verbose_name="Dispatch Date")
    origin_location = models.CharField(db_column='OriginLocation', max_length=255, verbose_name="Origin Location")
    destination_location = models.CharField(db_column='DestinationLocation', max_length=255, verbose_name="Destination Location")
    status = models.CharField(db_column='Status', max_length=50, verbose_name="Status")
    remarks = models.TextField(db_column='Remarks', blank=True, null=True, verbose_name="Remarks")

    class Meta:
        managed = False  # Important: tells Django not to manage table creation/alteration
        db_table = 'tbl_DispatchGunrail'
        verbose_name = 'Dispatch Gunrail Entry'
        verbose_name_plural = 'Dispatch Gunrail Entries'
        ordering = ['-dispatch_date', 'dispatch_number'] # Sensible default ordering

    def __str__(self):
        return f"{self.dispatch_number} - {self.gunrail_number}"
        
    def get_display_status(self):
        """
        Business logic example: Returns a more user-friendly status.
        """
        status_map = {
            'P': 'Pending Dispatch',
            'D': 'Dispatched',
            'L': 'Delivered',
            'C': 'Cancelled',
        }
        return status_map.get(self.status[0].upper(), self.status) # Assuming status is a single char or full word
    
    def is_editable(self):
        """
        Business logic example: Determines if an entry can be edited based on its status.
        """
        return self.status not in ['L', 'C'] # Cannot edit if delivered or cancelled

```

### 4.2 Forms (`sales_distribution/forms.py`)

**Task:** Define a Django form for `DispatchGunrail` model, including appropriate widgets for Tailwind CSS styling.

```python
from django import forms
from .models import DispatchGunrail

class DispatchGunrailForm(forms.ModelForm):
    class Meta:
        model = DispatchGunrail
        fields = ['dispatch_number', 'gunrail_number', 'dispatch_date', 
                  'origin_location', 'destination_location', 'status', 'remarks']
        widgets = {
            'dispatch_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'gunrail_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'dispatch_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'origin_location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'destination_location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[
                ('P', 'Pending'), 
                ('D', 'Dispatched'), 
                ('L', 'Delivered'), 
                ('C', 'Cancelled')
            ], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        
    def clean_dispatch_number(self):
        """
        Example custom validation: Ensure dispatch number format.
        """
        dispatch_number = self.cleaned_data['dispatch_number']
        if not dispatch_number.startswith('D-'):
            raise forms.ValidationError("Dispatch number must start with 'D-'.")
        return dispatch_number

```

### 4.3 Views (`sales_distribution/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views, keeping them thin by delegating logic to the model. Add a partial view for the DataTables table.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DispatchGunrail
from .forms import DispatchGunrailForm

class DispatchGunrailListView(ListView):
    model = DispatchGunrail
    template_name = 'sales_distribution/dispatchgunrail/list.html'
    context_object_name = 'dispatchgunrails'

class DispatchGunrailTablePartialView(TemplateView):
    """
    Renders just the table content for HTMX partial updates.
    """
    template_name = 'sales_distribution/dispatchgunrail/_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['dispatchgunrails'] = DispatchGunrail.objects.all()
        return context

class DispatchGunrailCreateView(CreateView):
    model = DispatchGunrail
    form_class = DispatchGunrailForm
    template_name = 'sales_distribution/dispatchgunrail/_form.html' # This is a partial template
    success_url = reverse_lazy('dispatchgunrail_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dispatch Gunrail entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshDispatchGunrailList'}
            )
        return response

class DispatchGunrailUpdateView(UpdateView):
    model = DispatchGunrail
    form_class = DispatchGunrailForm
    template_name = 'sales_distribution/dispatchgunrail/_form.html' # This is a partial template
    success_url = reverse_lazy('dispatchgunrail_list')

    def form_valid(self, form):
        # Business logic check in model:
        if not form.instance.is_editable():
            messages.error(self.request, 'This dispatch entry cannot be updated due to its status.')
            return self.form_invalid(form) # Render form with error

        response = super().form_valid(form)
        messages.success(self.request, 'Dispatch Gunrail entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshDispatchGunrailList'}
            )
        return response

class DispatchGunrailDeleteView(DeleteView):
    model = DispatchGunrail
    template_name = 'sales_distribution/dispatchgunrail/_confirm_delete.html' # This is a partial template
    success_url = reverse_lazy('dispatchgunrail_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dispatch Gunrail entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshDispatchGunrailList'}
            )
        return response

```

### 4.4 Templates (`sales_distribution/templates/sales_distribution/dispatchgunrail/`)

**Task:** Create templates for each view, leveraging HTMX for dynamic interactions and DataTables for list presentation.

#### `list.html`
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Dispatch Gunrail Entries</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'dispatchgunrail_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Entry
        </button>
    </div>
    
    <div id="dispatchgunrailTable-container"
         hx-trigger="load, refreshDispatchGunrailList from:body"
         hx-get="{% url 'dispatchgunrail_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Dispatch Gunrail Entries...</p>
        </div>
    </div>
    
    <!-- Modal for forms (add/edit) and delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // Example: x-data="{ showModal: false }" on parent modal div
    });

    // Handle HTMX events to manage modal visibility based on content load
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active'); // Ensure modal is visible
        }
    });

    // Close modal after form submission (triggered by HX-Trigger: refreshDispatchGunrailList from server)
    document.body.addEventListener('refreshDispatchGunrailList', function() {
        document.getElementById('modal').classList.remove('is-active');
    });
</script>
{% endblock %}
```

#### `_table.html` (Partial for DataTables)
```html
<table id="dispatchgunrailTable" class="min-w-full bg-white divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dispatch #</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gunrail #</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Origin</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for obj in dispatchgunrails %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.dispatch_number }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.gunrail_number }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.dispatch_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.origin_location }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.destination_location }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.get_display_status }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">
                {% if obj.is_editable %}
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-xs"
                    hx-get="{% url 'dispatchgunrail_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                {% else %}
                <span class="text-gray-500 text-xs py-1 px-2 rounded mr-2">Not Editable</span>
                {% endif %}
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'dispatchgunrail_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
// This script assumes jQuery and DataTables are loaded via base.html
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#dispatchgunrailTable')) {
        $('#dispatchgunrailTable').DataTable().destroy();
    }
    $('#dispatchgunrailTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] } // Disable sorting for SN and Actions column
        ]
    });
});
</script>
```

#### `_form.html` (Partial for Add/Edit Form)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Dispatch Gunrail Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

#### `_confirm_delete.html` (Partial for Delete Confirmation)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete the Dispatch Gunrail entry: <strong>{{ object }}</strong>?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

### 4.5 URLs (`sales_distribution/urls.py`)

**Task:** Define URL patterns for all views and partials.

```python
from django.urls import path
from .views import (
    DispatchGunrailListView, 
    DispatchGunrailCreateView, 
    DispatchGunrailUpdateView, 
    DispatchGunrailDeleteView,
    DispatchGunrailTablePartialView, # Add this for HTMX table loading
)

urlpatterns = [
    path('dispatchgunrail/', DispatchGunrailListView.as_view(), name='dispatchgunrail_list'),
    path('dispatchgunrail/table/', DispatchGunrailTablePartialView.as_view(), name='dispatchgunrail_table'), # HTMX partial
    path('dispatchgunrail/add/', DispatchGunrailCreateView.as_view(), name='dispatchgunrail_add'),
    path('dispatchgunrail/edit/<int:pk>/', DispatchGunrailUpdateView.as_view(), name='dispatchgunrail_edit'),
    path('dispatchgunrail/delete/<int:pk>/', DispatchGunrailDeleteView.as_view(), name='dispatchgunrail_delete'),
]
```

### 4.6 Tests (`sales_distribution/tests.py`)

**Task:** Write comprehensive unit tests for the model and integration tests for views.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import DispatchGunrail
from .forms import DispatchGunrailForm
from datetime import date

class DispatchGunrailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.dispatch_gunrail = DispatchGunrail.objects.create(
            dispatch_number='D-2023-001',
            gunrail_number='GR-XYZ-123',
            dispatch_date='2023-01-01',
            origin_location='Warehouse A',
            destination_location='Client Site B',
            status='P', # Pending
            remarks='First dispatch entry'
        )
  
    def test_dispatch_gunrail_creation(self):
        obj = DispatchGunrail.objects.get(gunrail_id=self.dispatch_gunrail.gunrail_id)
        self.assertEqual(obj.dispatch_number, 'D-2023-001')
        self.assertEqual(obj.gunrail_number, 'GR-XYZ-123')
        self.assertEqual(obj.dispatch_date, date(2023, 1, 1))
        self.assertEqual(obj.status, 'P')
        self.assertEqual(str(obj), 'D-2023-001 - GR-XYZ-123')
        
    def test_field_verbose_names(self):
        obj = DispatchGunrail.objects.get(gunrail_id=self.dispatch_gunrail.gunrail_id)
        field_label = obj._meta.get_field('dispatch_number').verbose_name
        self.assertEqual(field_label, 'Dispatch Number')
        field_label = obj._meta.get_field('gunrail_number').verbose_name
        self.assertEqual(field_label, 'Gunrail Number')
        
    def test_get_display_status_method(self):
        obj_pending = DispatchGunrail.objects.get(gunrail_id=self.dispatch_gunrail.gunrail_id)
        self.assertEqual(obj_pending.get_display_status(), 'Pending Dispatch')

        obj_delivered = DispatchGunrail.objects.create(
            dispatch_number='D-2023-002', gunrail_number='GR-ABC-456', 
            dispatch_date='2023-01-02', origin_location='A', destination_location='B', status='L'
        )
        self.assertEqual(obj_delivered.get_display_status(), 'Delivered')

    def test_is_editable_method(self):
        obj_pending = DispatchGunrail.objects.get(gunrail_id=self.dispatch_gunrail.gunrail_id)
        self.assertTrue(obj_pending.is_editable())

        obj_delivered = DispatchGunrail.objects.create(
            dispatch_number='D-2023-003', gunrail_number='GR-DEF-789', 
            dispatch_date='2023-01-03', origin_location='A', destination_location='B', status='L'
        )
        self.assertFalse(obj_delivered.is_editable())
        
        obj_cancelled = DispatchGunrail.objects.create(
            dispatch_number='D-2023-004', gunrail_number='GR-GHI-012', 
            dispatch_date='2023-01-04', origin_location='A', destination_location='B', status='C'
        )
        self.assertFalse(obj_cancelled.is_editable())

class DispatchGunrailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.dispatch_gunrail1 = DispatchGunrail.objects.create(
            dispatch_number='D-TEST-001', gunrail_number='GR-TEST-A', 
            dispatch_date='2023-01-01', origin_location='Test Origin 1', destination_location='Test Dest 1', status='P'
        )
        cls.dispatch_gunrail2 = DispatchGunrail.objects.create(
            dispatch_number='D-TEST-002', gunrail_number='GR-TEST-B', 
            dispatch_date='2023-01-02', origin_location='Test Origin 2', destination_location='Test Dest 2', status='D'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('dispatchgunrail_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/dispatchgunrail/list.html')
        self.assertTrue('dispatchgunrails' in response.context)
        self.assertEqual(len(response.context['dispatchgunrails']), 2) # Should see two objects
        
    def test_table_partial_view(self):
        response = self.client.get(reverse('dispatchgunrail_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/dispatchgunrail/_table.html')
        self.assertTrue('dispatchgunrails' in response.context)
        self.assertContains(response, 'GR-TEST-A') # Check if data is rendered

    def test_create_view_get(self):
        response = self.client.get(reverse('dispatchgunrail_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/dispatchgunrail/_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], DispatchGunrailForm)
        
    def test_create_view_post_success(self):
        data = {
            'dispatch_number': 'D-NEW-001',
            'gunrail_number': 'GR-NEW-C',
            'dispatch_date': '2023-03-01',
            'origin_location': 'New Origin',
            'destination_location': 'New Dest',
            'status': 'P',
            'remarks': 'New entry for testing'
        }
        # Simulate HTMX request
        response = self.client.post(reverse('dispatchgunrail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDispatchGunrailList')
        self.assertTrue(DispatchGunrail.objects.filter(dispatch_number='D-NEW-001').exists())
        
    def test_create_view_post_invalid(self):
        data = { # Missing required fields or invalid format
            'dispatch_number': 'INVALID', # Fails custom validation
            'gunrail_number': 'GR-NEW-D',
            'dispatch_date': '2023-03-02',
            'status': 'P',
        }
        response = self.client.post(reverse('dispatchgunrail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_distribution/dispatchgunrail/_form.html')
        self.assertContains(response, "Dispatch number must start with &#x27;D-&#x27;.")
        self.assertFalse(DispatchGunrail.objects.filter(dispatch_number='INVALID').exists())

    def test_update_view_get(self):
        obj = self.dispatch_gunrail1
        response = self.client.get(reverse('dispatchgunrail_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/dispatchgunrail/_form.html')
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        obj = self.dispatch_gunrail1
        data = {
            'dispatch_number': 'D-UPDATED-001', # Change this
            'gunrail_number': obj.gunrail_number,
            'dispatch_date': obj.dispatch_date.isoformat(),
            'origin_location': obj.origin_location,
            'destination_location': obj.destination_location,
            'status': 'D', # Change status
            'remarks': 'Updated entry'
        }
        response = self.client.post(reverse('dispatchgunrail_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDispatchGunrailList')
        obj.refresh_from_db()
        self.assertEqual(obj.dispatch_number, 'D-UPDATED-001')
        self.assertEqual(obj.status, 'D')

    def test_update_view_post_uneditable(self):
        # Create an entry with status 'L' (Delivered) which is not editable
        uneditable_obj = DispatchGunrail.objects.create(
            dispatch_number='D-UNEDITABLE-001', gunrail_number='GR-UNEDITABLE', 
            dispatch_date='2023-04-01', origin_location='A', destination_location='B', status='L'
        )
        data = {
            'dispatch_number': uneditable_obj.dispatch_number,
            'gunrail_number': uneditable_obj.gunrail_number,
            'dispatch_date': uneditable_obj.dispatch_date.isoformat(),
            'origin_location': uneditable_obj.origin_location,
            'destination_location': uneditable_obj.destination_location,
            'status': 'P', # Try to change status
            'remarks': uneditable_obj.remarks
        }
        response = self.client.post(reverse('dispatchgunrail_edit', args=[uneditable_obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with error
        self.assertTemplateUsed(response, 'sales_distribution/dispatchgunrail/_form.html')
        self.assertContains(response, "This dispatch entry cannot be updated due to its status.")
        uneditable_obj.refresh_from_db()
        self.assertEqual(uneditable_obj.status, 'L') # Status should not have changed
        
    def test_delete_view_get(self):
        obj = self.dispatch_gunrail1
        response = self.client.get(reverse('dispatchgunrail_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/dispatchgunrail/_confirm_delete.html')
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_success(self):
        obj_to_delete = DispatchGunrail.objects.create(
            dispatch_number='D-DEL-001', gunrail_number='GR-DEL-X', 
            dispatch_date='2023-05-01', origin_location='A', destination_location='B', status='P'
        )
        self.assertTrue(DispatchGunrail.objects.filter(pk=obj_to_delete.pk).exists())
        response = self.client.post(reverse('dispatchgunrail_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDispatchGunrailList')
        self.assertFalse(DispatchGunrail.objects.filter(pk=obj_to_delete.pk).exists())
```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

1.  **HTMX for Dynamic Interactions**:
    *   **Dashboard Loading**: The `list.html` uses `hx-get="{% url 'dispatchgunrail_table' %}"` with `hx-trigger="load, refreshDispatchGunrailList from:body"` to dynamically load and refresh the DataTables powered list. This ensures the table can be updated without a full page reload after any CRUD operation.
    *   **Modals for Forms**: "Add New Entry", "Edit", and "Delete" buttons use `hx-get` to fetch the respective partial templates (`_form.html` or `_confirm_delete.html`) into the `#modalContent` div.
    *   **Form Submission**: All forms (`_form.html`, `_confirm_delete.html`) use `hx-post="{{ request.path }}" hx-swap="none"`. The `hx-swap="none"` prevents HTMX from replacing content on a successful submission, relying on the server to send an `HX-Trigger` header.
    *   **Success Messages and Refresh**: Upon successful creation, update, or deletion, the Django views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshDispatchGunrailList'})`. This `HX-Trigger` causes the `dispatchgunrailTable-container` in `list.html` to re-fetch its content, refreshing the DataTables list.
    *   **Loading Indicators**: `hx-indicator` attributes on buttons and forms (`#form-spinner`, `#delete-spinner`) provide visual feedback (spinners) during HTMX requests.

2.  **Alpine.js for UI State Management**:
    *   The main `list.html` includes `_ = "on click add .is-active to #modal"` on the add/edit/delete buttons and `_ = "on click if event.target.id == 'modal' remove .is-active from me"` on the modal overlay. This uses Alpine.js's micro-expressions (`_` shorthand for `x-data`, etc.) to control the visibility of the modal based on clicks.
    *   The `htmx:afterSwap` and `refreshDispatchGunrailList` event listeners in `list.html`'s `extra_js` block ensure the modal is properly shown or hidden after HTMX operations.

3.  **DataTables for List Views**:
    *   The `_table.html` partial directly contains the `<table>` element with `id="dispatchgunrailTable"`.
    *   A JavaScript block within `_table.html` (wrapped in `$(document).ready(function() { ... });`) initializes DataTables on this table. It includes logic to destroy any existing DataTables instance before re-initialization, which is crucial when loading the table dynamically via HTMX.
    *   Configuration for `pageLength`, `lengthMenu`, and `columnDefs` (to disable sorting on specific columns like SN and Actions) are included.

4.  **No Full Page Reloads**: The entire CRUD workflow is designed to be interactive and dynamic, using HTMX to fetch and submit forms, update table content, and display confirmations without the need for full page refreshes.

---

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with inferred or example values (`dispatch_number`, `tbl_DispatchGunrail`, `sales_distribution`, etc.) to create a complete, runnable example.
*   **DRY Templates:** `_form.html` and `_confirm_delete.html` are partials, designed to be loaded dynamically into a modal, adhering to DRY principles.
*   **Fat Model, Thin View:** Business logic, such as `get_display_status` and `is_editable`, resides in the `DispatchGunrail` model. Views are concise, primarily handling HTTP requests, form validation triggering, and response generation, adhering to the 15-line limit for their main logic.
*   **Comprehensive Tests:** Unit tests for model methods and integration tests for all view interactions (GET, POST, HTMX headers) are provided to ensure functionality and maintainability.
*   **Automation Focus:** This entire structure is designed for automated generation. By inferring the basic structure (a dashboard displaying items with CRUD), the conversion process translates these high-level requirements into structured Django components with built-in best practices for modern web development, significantly reducing manual coding.