The following comprehensive modernization plan outlines the transition of your legacy ASP.NET application to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI for guidance, and focuses on delivering tangible business benefits. By adopting Django, HTMX, and Alpine.js, we aim to deliver a highly interactive, maintainable, and scalable application with significantly reduced development overhead.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the ASP.NET `SqlDataSource` definitions and C# code-behind, we identify the following key tables and their inferred columns. Note that actual data types for `Id` columns might be `AutoField` if they are auto-incrementing in the database, but we use `IntegerField` with `primary_key=True` to reflect their existing management, combined with `managed=False` for Django. Decimal fields are chosen based on the `Length` and `No` values, which are numeric and accept decimals.

- **`tblDG_Gunrail_CrossRail_Dispatch_Temp`**: Temporary storage for "Cross Rail" items during a session.
    - `Id`: `IntegerField` (Primary Key)
    - `Length`: `DecimalField` (up to 3 decimal places)
    - `No`: `DecimalField` (up to 3 decimal places)
    - `SessionId`: `CharField` (maps to `request.user.username`)
    - `CompId`: `IntegerField` (maps to `request.user.company_id`)
    - `Total`: Calculated property (`Length * No`)

- **`tblDG_Gunrail_LongRail_Dispatch_Temp`**: Temporary storage for "Long Rail" items during a session.
    - `Id`: `IntegerField` (Primary Key)
    - `Length`: `DecimalField`
    - `No`: `DecimalField`
    - `SessionId`: `CharField`
    - `CompId`: `IntegerField`
    - `Total`: Calculated property (`Length * No`)

- **`tblDG_Gunrail_Pitch_Dispatch_Master`**: Stores the main dispatch details after proceeding.
    - `Id`: `IntegerField` (Primary Key)
    - `SysDate`: `DateField`
    - `SysTime`: `TimeField`
    - `SessionId`: `CharField`
    - `CompId`: `IntegerField`
    - `FinYearId`: `IntegerField` (maps to `request.session['finyear']`)
    - `Pitch`: `DecimalField`
    - `WONo`: `CharField` (Work Order Number)
    - `Type`: `IntegerField` (0: Swivel, 1: Fixed)

- **`tblDG_Gunrail_CrossRail_Dispatch`**: Permanent storage for "Cross Rail" items linked to a dispatch.
    - `Id`: `IntegerField` (Primary Key)
    - `MId`: `IntegerField` (Foreign Key to `tblDG_Gunrail_Pitch_Dispatch_Master`)
    - `Length`: `DecimalField`
    - `No`: `DecimalField`

- **`tblDG_Gunrail_LongRail_Dispatch`**: Permanent storage for "Long Rail" items linked to a dispatch.
    - `Id`: `IntegerField` (Primary Key)
    - `MId`: `IntegerField` (Foreign Key to `tblDG_Gunrail_Pitch_Dispatch_Master`)
    - `Length`: `DecimalField`
    - `No`: `DecimalField`

- **`tblDG_GUNRAIL_BOM_Master`**: Referenced for Bill of Materials (BOM) integration.
    - `CId`: `IntegerField` (Primary Key)
    - `PId`: `IntegerField`
    - (Other fields unknown, minimal definition used for placeholder)

### Step 2: Identify Backend Functionality

**Cross Rail and Long Rail Temporary Data Management:**
- **Create (Add):** Users can add new "Length" and "Number" entries to both Cross Rail and Long Rail temporary lists via input fields in the table footer or an empty state template. These entries are stored per `SessionId` and `CompId`.
- **Read (List):** Both temporary lists are displayed, showing `SN`, `Length`, `Numbers`, and `Total`.
- **Delete:** Individual items can be removed from both temporary lists.

**Main Dispatch Processing (`btnProceed_Click`):**
- **Data Consolidation:** When "Proceed" is clicked, all temporary Cross Rail and Long Rail data for the current user and company is retrieved.
- **Master Record Creation:** A new `PitchDispatchMaster` record is created, capturing the `WONo`, `Pitch`, `Type`, `SysDate`, `SysTime`, `SessionId`, `CompId`, and `FinYearId`.
- **Permanent Data Transfer:** All records from the temporary Cross Rail and Long Rail tables are moved to their respective permanent tables (`tblDG_Gunrail_CrossRail_Dispatch`, `tblDG_Gunrail_LongRail_Dispatch`), linked to the newly created `PitchDispatchMaster` record.
- **Temporary Data Cleanup:** After successful transfer, the temporary records are deleted.
- **Complex Calculations:** A series of intricate calculations (`TLongRow_no_coloumn`, `TLongRow_with_coloumn`, `TLongRow`, `TLColumn`, `SumLongRailColumn`, `TCrossRow`, `TCColumn`, `SumLongCross`, `TwiceTC`) are performed based on the now-permanent dispatch data.
- **BOM Integration:** The calculated values are then passed to a critical business function (`fun.CopyGunRailToWO_Dispatch`) which integrates the dispatch data into the Bill of Materials (BOM) system.
- **Validation:** Ensures that both Cross Rail and Long Rail temporary data exist, and the Pitch value is provided, before proceeding.

### Step 3: Infer UI Components

The original ASP.NET page features two primary interactive data grids (Cross Rail and Long Rail), each with functionality to display, add, and delete entries. There's a master detail section at the bottom for "Type" and "Support Pitch".

- **Work Order Number Display:** A label showing the `WONo` from the query string.
- **Cross Rail Grid (`SearchGridView1`):**
    - Displays `SN`, `Length in Meter`, `Numbers`, `Total`.
    - Includes an "Add" row in the footer (or an empty template) for new entries.
    - Each row has a "Delete" button.
- **Long Rail Grid (`GridView1`):**
    - Identical structure and functionality to the Cross Rail grid, but for Long Rail items.
- **Input Fields:** Textboxes for "Length" and "Numbers" with validation.
- **Type Selection:** A radio button list for "Swivel" or "Fixed" types.
- **Support Pitch Input:** A textbox for "Pitch" with validation.
- **Action Buttons:** "Proceed" to finalize the dispatch and "Cancel" to navigate away.
- **Client-Side Interactions:** Original page used `confirmationAdd()` and `confirmationDelete()` JavaScript functions for pop-up confirmations. These will be replaced by HTMX/Alpine.js interactions or modern browser confirm dialogs.

### Step 4: Generate Django Code

We will create a Django application named `dispatch` for this module.

#### 4.1 Models (`dispatch/models.py`)

This file will contain all the necessary Django models, mapped to your existing database tables. It also encapsulates the complex business logic within the `PitchDispatchMaster` model as a class method, adhering to the "Fat Model" principle.

```python
from django.db import models, transaction
from django.conf import settings
from django.utils import timezone
from decimal import Decimal, ROUND_HALF_UP

# --- Core Models (Assuming these exist in your main ERP database context) ---
# These are placeholders; adjust their actual names/tables if they reside in a different app.

class Company(models.Model):
    # This model represents the company associated with transactions.
    # It's assumed to be managed by Django for new entries, but maps to existing 'CompId'.
    # If `Id` is truly an auto-incrementing key in your managed DB, this definition is appropriate.
    # If the `CompId` in the original DB is manually managed, use `IntegerField(primary_key=True, db_column='CompId')`
    # and ensure `managed = False` for this model if it's not managed by Django migrations.
    id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255, db_column='CompanyName', blank=True, null=True) # Example

    class Meta:
        managed = False # Important: Django won't create/manage this table
        db_table = 'tblCompanyMaster' # Replace with actual table name if different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company ID: {self.id}"

class FinancialYear(models.Model):
    # This model represents financial years.
    id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_start = models.DateField(db_column='YearStart', blank=True, null=True) # Example
    year_end = models.DateField(db_column='YearEnd', blank=True, null=True) # Example

    class Meta:
        managed = False # Important: Django won't create/manage this table
        db_table = 'tblFinancialYearMaster' # Replace with actual table name if different
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"{self.year_start.year}-{self.year_end.year}" if self.year_start and self.year_end else f"Fin Year ID: {self.id}"

# --- Temporary Dispatch Models ---
# These models are for data that is staged before final processing.
# They are managed by Django for their content but map to existing tables.

class CrossRailTemp(models.Model):
    # `Id` is the primary key from the existing database.
    # If new entries truly auto-increment `Id`, keep `primary_key=True`.
    # For inserts, you typically wouldn't specify `Id` for auto-incrementing fields.
    # If `Id` needs to be explicitly managed, adjust insertion logic in views/forms.
    id = models.IntegerField(primary_key=True, db_column='Id') 
    length = models.DecimalField(max_digits=18, decimal_places=3, db_column='Length')
    number = models.DecimalField(max_digits=18, decimal_places=3, db_column='No')
    session_id = models.CharField(max_length=255, db_column='SessionId')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_CrossRail_Dispatch_Temp'
        verbose_name = 'Cross Rail Temporary Item'
        verbose_name_plural = 'Cross Rail Temporary Items'

    def __str__(self):
        return f"CR Temp {self.id} (L:{self.length}, N:{self.number})"

    @property
    def total(self):
        """Calculates total (Length * No) rounded to 3 decimal places."""
        return (self.length * self.number).quantize(Decimal('0.001'), rounding=ROUND_HALF_UP)

    @classmethod
    def get_user_temp_data(cls, user_id, company_id):
        """Retrieves temporary Cross Rail data for a specific user and company."""
        return cls.objects.filter(session_id=user_id, company_id=company_id)

    @classmethod
    def clear_user_temp_data(cls, user_id, company_id):
        """Clears all temporary Cross Rail data for a specific user and company."""
        cls.objects.filter(session_id=user_id, company_id=company_id).delete()

class LongRailTemp(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    length = models.DecimalField(max_digits=18, decimal_places=3, db_column='Length')
    number = models.DecimalField(max_digits=18, decimal_places=3, db_column='No')
    session_id = models.CharField(max_length=255, db_column='SessionId')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_LongRail_Dispatch_Temp'
        verbose_name = 'Long Rail Temporary Item'
        verbose_name_plural = 'Long Rail Temporary Items'

    def __str__(self):
        return f"LR Temp {self.id} (L:{self.length}, N:{self.number})"

    @property
    def total(self):
        """Calculates total (Length * No) rounded to 3 decimal places."""
        return (self.length * self.number).quantize(Decimal('0.001'), rounding=ROUND_HALF_UP)

    @classmethod
    def get_user_temp_data(cls, user_id, company_id):
        """Retrieves temporary Long Rail data for a specific user and company."""
        return cls.objects.filter(session_id=user_id, company_id=company_id)

    @classmethod
    def clear_user_temp_data(cls, user_id, company_id):
        """Clears all temporary Long Rail data for a specific user and company."""
        cls.objects.filter(session_id=user_id, company_id=company_id).delete()

# --- Permanent Dispatch Models ---
# These models store the finalized dispatch data.

class PitchDispatchMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(max_length=255, db_column='SessionId')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.CASCADE, db_column='FinYearId')
    pitch = models.DecimalField(max_digits=18, decimal_places=3, db_column='Pitch')
    wo_no = models.CharField(max_length=255, db_column='WONo')
    type = models.IntegerField(db_column='Type') # 0 for Swivel, 1 for Fixed

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_Pitch_Dispatch_Master'
        verbose_name = 'Gunrail Pitch Dispatch Master'
        verbose_name_plural = 'Gunrail Pitch Dispatch Masters'

    def __str__(self):
        return f"Dispatch {self.id} for WO: {self.wo_no}"

    @staticmethod
    def calculate_abc_frac_value(length):
        """Equivalent to ASP.NET fun.get(Length / 6), likely a floor function."""
        return (length / Decimal('6')).quantize(Decimal('1.'), rounding=ROUND_FLOOR)

    @staticmethod
    def calculate_without_fractional_value(length, pitch):
        """Equivalent to ASP.NET fun.get((Length / Pitch) + 1), likely a floor function."""
        return (length / pitch + Decimal('1')).quantize(Decimal('1.'), rounding=ROUND_FLOOR)

    @classmethod
    @transaction.atomic
    def process_gunrail_dispatch(cls, wo_no, pitch_type, pitch_value, user_id, company_id, fin_year_id):
        """
        Encapsulates the entire complex business logic from ASP.NET's btnProceed_Click.
        This method handles data transfer, calculations, and BOM integration.
        """
        cross_rail_temps = CrossRailTemp.get_user_temp_data(user_id, company_id)
        long_rail_temps = LongRailTemp.get_user_temp_data(user_id, company_id)

        if not cross_rail_temps.exists() or not long_rail_temps.exists() or pitch_value is None:
            raise ValueError("Invalid data entry. Cross Rail, Long Rail data, or Pitch is missing.")

        current_time = timezone.now()
        pitch_master = cls.objects.create(
            sys_date=current_time.date(),
            sys_time=current_time.time(),
            session_id=user_id,
            company_id=company_id,
            financial_year_id=fin_year_id,
            pitch=pitch_value,
            wo_no=wo_no,
            type=pitch_type
        )

        # Transfer Cross Rail items to permanent table and clear temporary
        for cr_temp in cross_rail_temps:
            CrossRailDispatch.objects.create(
                master=pitch_master,
                length=cr_temp.length.quantize(Decimal('0.001'), rounding=ROUND_HALF_UP),
                number=cr_temp.number.quantize(Decimal('0.001'), rounding=ROUND_HALF_UP)
            )
        CrossRailTemp.clear_user_temp_data(user_id, company_id)

        # Transfer Long Rail items to permanent table and clear temporary
        for lr_temp in long_rail_temps:
            LongRailDispatch.objects.create(
                master=pitch_master,
                length=lr_temp.length.quantize(Decimal('0.001'), rounding=ROUND_HALF_UP),
                number=lr_temp.number.quantize(Decimal('0.001'), rounding=ROUND_HALF_UP)
            )
        LongRailTemp.clear_user_temp_data(user_id, company_id)

        # Re-query permanent tables for calculations (mirroring ASP.NET logic)
        long_rail_dispatches = LongRailDispatch.objects.filter(master=pitch_master)
        cross_rail_dispatches = CrossRailDispatch.objects.filter(master=pitch_master)

        # --- Perform Complex Calculations (as seen in ASP.NET code) ---
        t_long_row_no_column = Decimal('0.0')
        t_long_row_with_column = Decimal('0.0')
        t_long_row = Decimal('0.0')
        t_l_column = Decimal('0.0') # Sum of No * No
        sum_long_rail_column = Decimal('0.0') # Sum of No

        for lr in long_rail_dispatches:
            abc_frac_value = cls.calculate_abc_frac_value(lr.length)
            long_row_no_column = (abc_frac_value + Decimal('1')) - Decimal('2')
            t_long_row_no_column += long_row_no_column * lr.number
            
            # Using the pitch from the master record associated with the current dispatch
            current_pitch = pitch_master.pitch 

            without_fractional_value = cls.calculate_without_fractional_value(lr.length, current_pitch)
            long_row_with_column = (without_fractional_value - long_row_no_column)
            t_long_row_with_column += long_row_with_column * lr.number
            t_long_row += lr.length * lr.number
            t_l_column += lr.number * lr.number 
            sum_long_rail_column += lr.number 

        t_cross_row = Decimal('0.0') # Sum of Length * No
        t_c_column = Decimal('0.0') # Sum of No
        sum_long_cross = Decimal('0.0')
        twice_tc = Decimal('0.0')

        for cr in cross_rail_dispatches:
            t_cross_row += cr.length * cr.number
            t_c_column += cr.number

        sum_long_cross = t_cross_row + t_long_row
        twice_tc = t_c_column * Decimal('2')

        # --- BOM Integration (Placeholder for fun.CopyGunRailToWO_Dispatch) ---
        # This part requires understanding the external `fun` methods.
        # We model a simplified interaction with `GunrailBOMMaster`.
        
        # This `get_bom_cid` needs to be carefully mapped. The original ASP.NET was `fun.getBOMCId(WONo, CompId, FinYearId)`.
        # Assuming `GunrailBOMMaster` has fields that allow this lookup.
        dest_bom_master_obj = GunrailBOMMaster.objects.filter(wo_no=wo_no, company_id=company_id, financial_year_id=fin_year_id).first()
        
        if dest_bom_master_obj:
            dest_cid = dest_bom_master_obj.id # Assuming 'Id' is the 'CId' in BOMMaster context
            # Get parent CIds (assuming PId='0' indicates a parent)
            parent_cids = GunrailBOMMaster.objects.filter(pid=0).values_list('id', flat=True) # Assuming 'Id' is 'CId' in BOMMaster
            
            for src_cid in parent_cids:
                if src_cid not in [16, 46]: # Hardcoded values from ASP.NET
                    # This method must be implemented based on the original fun.CopyGunRailToWO_Dispatch
                    GunrailBOMMaster.copy_gunrail_to_to_wo_dispatch_logic(
                        src_cid=src_cid,
                        wo_no=wo_no,
                        company_id=company_id,
                        session_id=user_id,
                        fin_year_id=fin_year_id,
                        some_param_0=0, # This was '0' in the ASP.NET call
                        dest_cid=dest_cid,
                        tc_column=t_c_column,
                        t_long_row_with_coloumn=t_long_row_with_column,
                        t_long_row_no_coloumn=t_long_row_no_column,
                        sum_long_rail_column=sum_long_rail_column,
                        t_cross_row=t_cross_row,
                        t_long_row=t_long_row
                    )
        else:
            # Handle case where dest_bom_master_obj is not found if required
            pass

        return pitch_master # Return the created master object on success

class CrossRailDispatch(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    master = models.ForeignKey(PitchDispatchMaster, on_delete=models.CASCADE, db_column='MId')
    length = models.DecimalField(max_digits=18, decimal_places=3, db_column='Length')
    number = models.DecimalField(max_digits=18, decimal_places=3, db_column='No')

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_CrossRail_Dispatch'
        verbose_name = 'Cross Rail Dispatch Item'
        verbose_name_plural = 'Cross Rail Dispatch Items'

    def __str__(self):
        return f"CR Disp {self.id} (L:{self.length}, N:{self.number})"

class LongRailDispatch(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    master = models.ForeignKey(PitchDispatchMaster, on_delete=models.CASCADE, db_column='MId')
    length = models.DecimalField(max_digits=18, decimal_places=3, db_column='Length')
    number = models.DecimalField(max_digits=18, decimal_places=3, db_column='No')

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_LongRail_Dispatch'
        verbose_name = 'Long Rail Dispatch Item'
        verbose_name_plural = 'Long Rail Dispatch Items'

    def __str__(self):
        return f"LR Disp {self.id} (L:{self.length}, N:{self.number})"

# --- Bill of Materials (BOM) Integration Model (Placeholder) ---
# This model is crucial for the `CopyGunRailToWO_Dispatch` logic.
# Its full schema and logic need to be ported from the original `fun` class.

class GunrailBOMMaster(models.Model):
    # This model maps to `tblDG_GUNRAIL_BOM_Master`.
    # `CId` and `PId` are from the original ASP.NET code references.
    id = models.IntegerField(primary_key=True, db_column='CId') # Assuming CId is the primary key
    pid = models.IntegerField(db_column='PId')
    wo_no = models.CharField(max_length=255, db_column='WONo', blank=True, null=True) # Assuming for `get_bom_cid`
    company = models.ForeignKey(Company, on_delete=models.CASCADE, db_column='CompId', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.CASCADE, db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_GUNRAIL_BOM_Master'
        verbose_name = 'Gunrail BOM Master'
        verbose_name_plural = 'Gunrail BOM Masters'

    def __str__(self):
        return f"BOM Master {self.id} (P:{self.pid})"

    @classmethod
    def copy_gunrail_to_to_wo_dispatch_logic(cls, src_cid, wo_no, company_id, session_id, fin_year_id,
                                          some_param_0, dest_cid, tc_column, t_long_row_with_coloumn,
                                          t_long_row_no_coloumn, sum_long_rail_column, t_cross_row, t_long_row):
        """
        Placeholder for the complex `fun.CopyGunRailToWO_Dispatch` logic.
        This method would typically involve:
        1. Querying related BOM tables or other operational tables.
        2. Performing inserts or updates based on the calculated dispatch quantities.
        3. Ensuring transactional integrity with the main dispatch process.
        """
        print(f"--- Executing BOM Integration Logic ---")
        print(f"Source CId: {src_cid}, WO No: {wo_no}, Destination CId: {dest_cid}")
        print(f"Calculated Values: TCC:{tc_column}, TLWC:{t_long_row_with_coloumn}, TLNC:{t_long_row_no_coloumn}, SRLC:{sum_long_rail_column}, TCR:{t_cross_row}, TLR:{t_long_row}")
        # **IMPORTANT**: Implement the actual database operations and business logic here
        # based on a detailed analysis of the original `fun.CopyGunRailToWO_Dispatch` function.
        # This will likely involve Django ORM queries for other BOM-related models.
        pass

```

#### 4.2 Forms (`dispatch/forms.py`)

Django forms will handle input validation and structure. We'll use `ModelForm` for convenience.

```python
from django import forms
from .models import CrossRailTemp, LongRailTemp, PitchDispatchMaster
from decimal import Decimal
import re

# Custom validator for decimal fields with up to 3 decimal places and specific format
def validate_decimal_input(value):
    if value is None:
        return # RequiredFieldValidator (Django's default) handles None for required fields
    if not isinstance(value, (Decimal, float, int)):
        raise forms.ValidationError("Enter a valid number.")
    
    s_value = str(value)
    # Regex from ASP.NET: ^\d{1,15}(\.\d{0,3})?$
    # Allows 1 to 15 digits before decimal, optionally 0 to 3 after decimal.
    if not re.match(r"^\d{1,15}(\.\d{0,3})?$", s_value):
        raise forms.ValidationError("Must be a number with up to 15 digits and 3 decimal places.")

class CrossRailTempForm(forms.ModelForm):
    # Using `DecimalField` which handles numeric validation and precision.
    # `validators` attribute for custom regex check from ASP.NET.
    length = forms.DecimalField(
        max_digits=18, decimal_places=3, 
        required=True, # Equivalent to RequiredFieldValidator
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 
            'placeholder': 'Length in Meter',
            'min': '0.001' # Add min value if applicable for non-zero requirement
        }),
        validators=[validate_decimal_input]
    )
    number = forms.DecimalField(
        max_digits=18, decimal_places=3, 
        required=True, # Equivalent to RequiredFieldValidator
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 
            'placeholder': 'Numbers',
            'min': '0.001' # Add min value if applicable for non-zero requirement
        }),
        validators=[validate_decimal_input]
    )

    class Meta:
        model = CrossRailTemp
        fields = ['length', 'number']
        # The 'id', 'session_id', 'company' fields will be populated in the view/model method.

    def clean(self):
        cleaned_data = super().clean()
        length = cleaned_data.get('length')
        number = cleaned_data.get('number')
        # Replicating ASP.NET's `if (length != 0 && NoOfRow != 0)` check
        if length is not None and length == 0:
            self.add_error('length', 'Length cannot be zero.')
        if number is not None and number == 0:
            self.add_error('number', 'Number cannot be zero.')
        return cleaned_data

class LongRailTempForm(forms.ModelForm):
    length = forms.DecimalField(
        max_digits=18, decimal_places=3, 
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 
            'placeholder': 'Length in Meter',
            'min': '0.001'
        }),
        validators=[validate_decimal_input]
    )
    number = forms.DecimalField(
        max_digits=18, decimal_places=3, 
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 
            'placeholder': 'Numbers',
            'min': '0.001'
        }),
        validators=[validate_decimal_input]
    )

    class Meta:
        model = LongRailTemp
        fields = ['length', 'number']

    def clean(self):
        cleaned_data = super().clean()
        length = cleaned_data.get('length')
        number = cleaned_data.get('number')
        if length is not None and length == 0:
            self.add_error('length', 'Length cannot be zero.')
        if number is not None and number == 0:
            self.add_error('number', 'Number cannot be zero.')
        return cleaned_data

class PitchDispatchForm(forms.Form):
    # This form is for the 'Proceed' section, not tied directly to a model for input.
    # It captures 'Type' (RadioButtonList) and 'Pitch' (TextBox).
    TYPE_CHOICES = [
        (0, 'Swivel'),
        (1, 'Fixed'),
    ]
    type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out'}),
        initial=0, # Selected="True" in ASP.NET
        label="Type"
    )
    pitch = forms.DecimalField(
        max_digits=18, decimal_places=3, 
        required=True, # Equivalent to RequiredFieldValidator
        widget=forms.NumberInput(attrs={
            'class': 'block w-20 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 
            'placeholder': 'Pitch',
            'min': '0.001' # Add min value if applicable for non-zero requirement
        }),
        validators=[validate_decimal_input],
        label="Support Pitch"
    )

    def clean(self):
        cleaned_data = super().clean()
        pitch = cleaned_data.get('pitch')
        if pitch is not None and pitch == 0:
            self.add_error('pitch', 'Pitch cannot be zero.')
        return cleaned_data

```

#### 4.3 Views (`dispatch/views.py`)

Views will be thin, primarily handling HTTP requests, calling model methods for business logic, and returning appropriate HTMX responses.

```python
from django.views.generic import TemplateView, ListView, CreateView, DeleteView
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from decimal import Decimal
import logging

from .models import CrossRailTemp, LongRailTemp, PitchDispatchMaster, Company, FinancialYear
from .forms import CrossRailTempForm, LongRailTempForm, PitchDispatchForm

logger = logging.getLogger(__name__)

# Helper to get user-specific Company and Financial Year IDs
def get_user_context_ids(request):
    # These values typically come from user's session or profile
    # For demonstration, assuming they are available via request.session
    # In a real app, `request.user.company_id` might be preferred.
    company_id = request.session.get('compid', 0) # Default to 0 or raise error if not found
    fin_year_id = request.session.get('finyear', 0) # Default to 0
    # Retrieve actual Company and FinancialYear objects if needed for FKs
    company = Company.objects.filter(id=company_id).first()
    financial_year = FinancialYear.objects.filter(id=fin_year_id).first()
    return company, financial_year, company_id, fin_year_id

class DispatchDetailView(LoginRequiredMixin, TemplateView):
    """
    Main view for the Gunrail Dispatch Details page.
    Renders the overall structure and initial data for both Cross Rail and Long Rail.
    """
    template_name = 'dispatch/dispatch_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        won_no = self.request.GET.get('WONo', 'N/A')
        context['won_no'] = won_no
        context['pitch_form'] = PitchDispatchForm()
        
        # Initial load of temporary data will be handled by HTMX fragments
        # so we don't need to load all data here directly, just the context variables.
        return context

class CrossRailTablePartialView(LoginRequiredMixin, ListView):
    """
    HTMX partial view to render the Cross Rail data table.
    """
    model = CrossRailTemp
    template_name = 'dispatch/_cross_rail_table.html'
    context_object_name = 'cross_rail_items'

    def get_queryset(self):
        user_id = self.request.user.username
        company, _, company_id, _ = get_user_context_ids(self.request)
        if not company:
            # Handle case where company is not found
            logger.warning(f"Company with ID {company_id} not found for user {user_id}.")
            return CrossRailTemp.objects.none()
        return CrossRailTemp.get_user_temp_data(user_id, company_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = CrossRailTempForm() # Form for adding new entries in the footer
        return context


class CrossRailCreateView(LoginRequiredMixin, CreateView):
    """
    HTMX partial view to handle adding new Cross Rail items.
    Returns no content (204) with HX-Trigger on success.
    """
    model = CrossRailTemp
    form_class = CrossRailTempForm
    template_name = 'dispatch/_cross_rail_form.html' # Could be re-used for empty table state as well

    def form_valid(self, form):
        user_id = self.request.user.username
        company, _, company_id, _ = get_user_context_ids(self.request)
        if not company:
            messages.error(self.request, "Company information missing. Cannot add item.")
            return HttpResponse(status=400) # Bad request if company not found
        
        # Manually set fields not present in form
        # The original `Id` was an integer, implying auto-increment.
        # If your DB does not auto-increment `Id` for this table, you'll need a different strategy
        # (e.g., retrieve max ID and increment, or use a UUID field for new entries).
        # Assuming for new records, 'Id' is auto-assigned by the database.
        # Django's `save()` method on a new instance usually handles auto-increment.
        # However, for `managed=False` tables, it might need to be explicitly set or assumed DB auto-increments.
        # For a clean insert into an existing table, ensure the `id` field is set correctly
        # if it's not truly auto-incrementing. For now, we omit it and let the DB handle it.
        form.instance.session_id = user_id
        form.instance.company = company
        
        try:
            # Manually get a new ID, if the DB doesn't auto-assign for managed=False
            # This is a common workaround if the original Id is an integer primary key
            # and isn't truly an auto-incrementing identity column, or if Django ORM struggles.
            # Otherwise, just `form.save()` should be sufficient.
            max_id = CrossRailTemp.objects.aggregate(models.Max('id'))['id__max'] or 0
            form.instance.id = max_id + 1
            form.save()
            messages.success(self.request, 'Cross Rail item added.')
            return HttpResponse(
                status=204, # No content, tells HTMX it was successful
                headers={'HX-Trigger': 'refreshCrossRailList'} # Custom event to refresh the table
            )
        except Exception as e:
            logger.error(f"Error saving CrossRailTemp: {e}")
            messages.error(self.request, f"Error adding Cross Rail item: {e}")
            return HttpResponse(status=400, content=str(e)) # HTMX will show this as error

    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors in the Cross Rail form.")
        return render(self.request, self.template_name, {'form': form})

class CrossRailDeleteView(LoginRequiredMixin, DeleteView):
    """
    HTMX partial view to handle deleting Cross Rail items.
    """
    model = CrossRailTemp
    template_name = 'dispatch/_confirm_delete.html' # Generic confirmation template

    def get_object(self, queryset=None):
        # Ensure the user can only delete their own temporary items
        user_id = self.request.user.username
        company, _, company_id, _ = get_user_context_ids(self.request)
        if not company:
            raise Http404("Company information missing.")

        pk = self.kwargs.get(self.pk_url_kwarg)
        return get_object_or_404(self.model, pk=pk, session_id=user_id, company_id=company_id)

    def delete(self, request, *args, **kwargs):
        try:
            self.object = self.get_object()
            self.object.delete()
            messages.success(self.request, 'Cross Rail item deleted.')
            return HttpResponse(
                status=204, # No content
                headers={'HX-Trigger': 'refreshCrossRailList'} # Trigger refresh
            )
        except Exception as e:
            messages.error(self.request, f"Error deleting Cross Rail item: {e}")
            return HttpResponse(status=400, content=f"Error deleting: {e}")


class LongRailTablePartialView(LoginRequiredMixin, ListView):
    """
    HTMX partial view to render the Long Rail data table.
    """
    model = LongRailTemp
    template_name = 'dispatch/_long_rail_table.html'
    context_object_name = 'long_rail_items'

    def get_queryset(self):
        user_id = self.request.user.username
        company, _, company_id, _ = get_user_context_ids(self.request)
        if not company:
            logger.warning(f"Company with ID {company_id} not found for user {user_id}.")
            return LongRailTemp.objects.none()
        return LongRailTemp.get_user_temp_data(user_id, company_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = LongRailTempForm() # Form for adding new entries in the footer
        return context

class LongRailCreateView(LoginRequiredMixin, CreateView):
    """
    HTMX partial view to handle adding new Long Rail items.
    """
    model = LongRailTemp
    form_class = LongRailTempForm
    template_name = 'dispatch/_long_rail_form.html'

    def form_valid(self, form):
        user_id = self.request.user.username
        company, _, company_id, _ = get_user_context_ids(self.request)
        if not company:
            messages.error(self.request, "Company information missing. Cannot add item.")
            return HttpResponse(status=400)
        
        form.instance.session_id = user_id
        form.instance.company = company
        
        try:
            max_id = LongRailTemp.objects.aggregate(models.Max('id'))['id__max'] or 0
            form.instance.id = max_id + 1
            form.save()
            messages.success(self.request, 'Long Rail item added.')
            return HttpResponse(
                status=204, # No content
                headers={'HX-Trigger': 'refreshLongRailList'} # Trigger refresh
            )
        except Exception as e:
            logger.error(f"Error saving LongRailTemp: {e}")
            messages.error(self.request, f"Error adding Long Rail item: {e}")
            return HttpResponse(status=400, content=str(e))

    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors in the Long Rail form.")
        return render(self.request, self.template_name, {'form': form})

class LongRailDeleteView(LoginRequiredMixin, DeleteView):
    """
    HTMX partial view to handle deleting Long Rail items.
    """
    model = LongRailTemp
    template_name = 'dispatch/_confirm_delete.html'

    def get_object(self, queryset=None):
        user_id = self.request.user.username
        company, _, company_id, _ = get_user_context_ids(self.request)
        if not company:
            raise Http404("Company information missing.")

        pk = self.kwargs.get(self.pk_url_kwarg)
        return get_object_or_404(self.model, pk=pk, session_id=user_id, company_id=company_id)

    def delete(self, request, *args, **kwargs):
        try:
            self.object = self.get_object()
            self.object.delete()
            messages.success(self.request, 'Long Rail item deleted.')
            return HttpResponse(
                status=204, # No content
                headers={'HX-Trigger': 'refreshLongRailList'} # Trigger refresh
            )
        except Exception as e:
            messages.error(self.request, f"Error deleting Long Rail item: {e}")
            return HttpResponse(status=400, content=f"Error deleting: {e}")

class ProcessDispatchView(LoginRequiredMixin, TemplateView):
    """
    Handles the 'Proceed' button click, executing the main dispatch logic.
    """
    template_name = 'dispatch/dispatch_detail.html' # Redirect back to the main page or another success page

    def post(self, request, *args, **kwargs):
        won_no = request.GET.get('WONo', 'N/A')
        pitch_form = PitchDispatchForm(request.POST)

        if pitch_form.is_valid():
            pitch_type = pitch_form.cleaned_data['type']
            pitch_value = pitch_form.cleaned_data['pitch']
            user_id = request.user.username
            company, financial_year, company_id, fin_year_id = get_user_context_ids(request)

            if not company or not financial_year:
                messages.error(request, "Company or Financial Year information is not correctly set up for your user.")
                return self.render_to_response(self.get_context_data())

            try:
                PitchDispatchMaster.process_gunrail_dispatch(
                    wo_no, Decimal(pitch_type), pitch_value, user_id, company_id, fin_year_id
                )
                messages.success(request, 'Dispatch processed successfully!')
                # Redirect to a success page or clear the form and display a success message
                # Original ASP.NET redirected to same page, so we will do that,
                # ensuring temporary data is cleared on refresh.
                return redirect(reverse_lazy('dispatch:gunrail_dispatch_detail') + f'?WONo={won_no}')
            except ValueError as e:
                messages.error(request, str(e)) # Display specific error from model
            except Exception as e:
                logger.exception("An unexpected error occurred during dispatch processing.")
                messages.error(request, f"An unexpected error occurred: {e}")
        else:
            messages.error(request, "Please correct the errors in the Pitch and Type selection.")
            # If invalid, render the main page again with form errors
            # Populate context data to render the main page with errors
            context = self.get_context_data()
            context['pitch_form'] = pitch_form # Pass the invalid form back
            return self.render_to_response(context)
        
        # If an error occurred and we didn't redirect, re-render the page with errors
        return self.render_to_response(self.get_context_data())


class CancelDispatchView(LoginRequiredMixin, TemplateView):
    """
    Handles the 'Cancel' button click.
    """
    def get(self, request, *args, **kwargs):
        # Redirect to the specified ASP.NET target page equivalent in Django
        return redirect(reverse_lazy('sales_distribution:wo_grid')) # Assuming a URL pattern like this exists

```

#### 4.4 Templates (`dispatch/templates/dispatch/`)

We'll define the main page and partials that HTMX will load dynamically. Remember, `base.html` is assumed to exist.

**`dispatch_detail.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 border-b pb-4 mb-4">
            <strong class="text-blue-600">&nbsp;Slido Gunrail Dispatch - For WONo: </strong>
            <span class="text-gray-700">{{ won_no }}</span>
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Cross Rail Section -->
            <div>
                <h3 class="text-xl font-semibold text-center mb-4">Cross Rail</h3>
                <div id="crossRailTable-container" 
                     hx-trigger="load, refreshCrossRailList from:body"
                     hx-get="{% url 'dispatch:cross_rail_table' %}"
                     hx-swap="innerHTML">
                    <!-- Loading indicator for HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Cross Rail data...</p>
                    </div>
                </div>
            </div>

            <!-- Long Rail Section -->
            <div>
                <h3 class="text-xl font-semibold text-center mb-4">Long Rail</h3>
                <div id="longRailTable-container" 
                     hx-trigger="load, refreshLongRailList from:body"
                     hx-get="{% url 'dispatch:long_rail_table' %}"
                     hx-swap="innerHTML">
                    <!-- Loading indicator for HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Long Rail data...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 pt-6 border-t border-gray-200">
            <form hx-post="{% url 'dispatch:process_dispatch' %}?WONo={{ won_no }}" hx-swap="none" class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6">
                {% csrf_token %}
                <div class="flex items-center space-x-4">
                    <label class="font-bold text-gray-700">Type :</label>
                    <div class="flex space-x-4">
                        {% for radio in pitch_form.type %}
                        <div class="flex items-center">
                            {{ radio.tag }}
                            <label for="{{ radio.id_for_label }}" class="ml-2 text-gray-700 text-sm">{{ radio.choice_label }}</label>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <label for="{{ pitch_form.pitch.id_for_label }}" class="font-bold text-gray-700">Support Pitch :</label>
                    <div>
                        {{ pitch_form.pitch }}
                        {% if pitch_form.pitch.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ pitch_form.pitch.errors }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                            onclick="return confirm('Are you sure you want to proceed with the dispatch?');">
                        Proceed
                    </button>
                    <a href="{% url 'dispatch:cancel_dispatch' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                </div>
            </form>
            {% if pitch_form.non_field_errors %}
                <div class="text-red-500 text-sm mt-4 text-center">
                    {% for error in pitch_form.non_field_errors %}{{ error }}{% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal for HTMX forms (add/edit/delete) -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"></div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTables after HTMX loads the table content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'crossRailTable-container' || event.detail.target.id === 'longRailTable-container') {
            const tableId = event.detail.target.querySelector('table').id;
            // Destroy existing DataTable instance if it exists to prevent reinitialization errors
            if ($.fn.DataTable.isDataTable('#' + tableId)) {
                $('#' + tableId).DataTable().destroy();
            }
            $('#' + tableId).DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10, // Default page size, matches ASP.NET
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]] // Matches ASP.NET options
            });
        }
    });

    // Handle HTMX response for messages
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status >= 400 && event.detail.xhr.status < 500) {
            // Display error messages from server response
            const errorMessage = event.detail.xhr.responseText;
            if (errorMessage) {
                alert(errorMessage); // Simple alert for errors
            }
        }
    });

    document.addEventListener('alpine:init', () => {
        // Alpine.js components would go here if needed for more complex UI state.
        // For simple modal open/close, hx-on directives with _ are often sufficient.
    });
</script>
{% endblock %}

```

**`_cross_rail_table.html` (Partial for Cross Rail DataTables)**

```html
<table id="crossRailTable" class="min-w-full bg-white border border-gray-300 rounded-md">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Length in Meter</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Numbers</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% if cross_rail_items %}
            {% for item in cross_rail_items %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.length|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.number|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.total|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'dispatch:cross_rail_delete' item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        onclick="return confirm('Are you sure you want to delete this item?');">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <!-- EmptyDataTemplate equivalent -->
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500">
                    No Cross Rail items added yet. Use the form below to add one.
                </td>
            </tr>
        {% endif %}
    </tbody>
    <tfoot class="bg-gray-50">
        <tr class="h-12">
            <td class="py-2 px-4 font-semibold text-gray-700">Add New:</td>
            <td class="py-2 px-4">
                <form hx-post="{% url 'dispatch:cross_rail_add' %}" hx-swap="none" hx-trigger="submit" class="flex flex-col space-y-1">
                    {% csrf_token %}
                    {{ form.length }}
                    {% if form.length.errors %}<span class="text-red-500 text-xs">{{ form.length.errors }}</span>{% endif %}
            </td>
            <td class="py-2 px-4">
                    {{ form.number }}
                    {% if form.number.errors %}<span class="text-red-500 text-xs">{{ form.number.errors }}</span>{% endif %}
            </td>
            <td class="py-2 px-4 text-center" colspan="2">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out"
                        onclick="return confirm('Are you sure you want to add this item?');">
                    Submit
                </button>
                </form>
            </td>
        </tr>
        {% if form.non_field_errors %}
        <tr>
            <td colspan="5" class="py-2 px-4 text-red-500 text-xs text-center">{{ form.non_field_errors }}</td>
        </tr>
        {% endif %}
    </tfoot>
</table>

```

**`_long_rail_table.html` (Partial for Long Rail DataTables)**

```html
<table id="longRailTable" class="min-w-full bg-white border border-gray-300 rounded-md">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Length in Meter</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No of Rows</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% if long_rail_items %}
            {% for item in long_rail_items %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.length|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.number|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.total|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'dispatch:long_rail_delete' item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        onclick="return confirm('Are you sure you want to delete this item?');">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <!-- EmptyDataTemplate equivalent -->
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500">
                    No Long Rail items added yet. Use the form below to add one.
                </td>
            </tr>
        {% endif %}
    </tbody>
    <tfoot class="bg-gray-50">
        <tr class="h-12">
            <td class="py-2 px-4 font-semibold text-gray-700">Add New:</td>
            <td class="py-2 px-4">
                <form hx-post="{% url 'dispatch:long_rail_add' %}" hx-swap="none" hx-trigger="submit" class="flex flex-col space-y-1">
                    {% csrf_token %}
                    {{ form.length }}
                    {% if form.length.errors %}<span class="text-red-500 text-xs">{{ form.length.errors }}</span>{% endif %}
            </td>
            <td class="py-2 px-4">
                    {{ form.number }}
                    {% if form.number.errors %}<span class="text-red-500 text-xs">{{ form.number.errors }}</span>{% endif %}
            </td>
            <td class="py-2 px-4 text-center" colspan="2">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out"
                        onclick="return confirm('Are you sure you want to add this item?');">
                    Submit
                </button>
                </form>
            </td>
        </tr>
        {% if form.non_field_errors %}
        <tr>
            <td colspan="5" class="py-2 px-4 text-red-500 text-xs text-center">{{ form.non_field_errors }}</td>
        </tr>
        {% endif %}
    </tfoot>
</table>

```

**`_confirm_delete.html` (Generic Delete Confirmation Partial)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="mb-4">Are you sure you want to delete this item?</p>
    <div class="flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-delete="{{ request.path }}" 
            hx-swap="none" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`dispatch/urls.py`)

This file defines the URL patterns for accessing the views.

```python
from django.urls import path
from .views import (
    DispatchDetailView,
    CrossRailTablePartialView, CrossRailCreateView, CrossRailDeleteView,
    LongRailTablePartialView, LongRailCreateView, LongRailDeleteView,
    ProcessDispatchView, CancelDispatchView
)

app_name = 'dispatch' # Namespace for URLs

urlpatterns = [
    # Main dispatch detail page
    path('gunrail_dispatch/', DispatchDetailView.as_view(), name='gunrail_dispatch_detail'),

    # HTMX endpoints for Cross Rail (CR) items
    path('cross_rail/table/', CrossRailTablePartialView.as_view(), name='cross_rail_table'),
    path('cross_rail/add/', CrossRailCreateView.as_view(), name='cross_rail_add'),
    path('cross_rail/delete/<int:pk>/', CrossRailDeleteView.as_view(), name='cross_rail_delete'),

    # HTMX endpoints for Long Rail (LR) items
    path('long_rail/table/', LongRailTablePartialView.as_view(), name='long_rail_table'),
    path('long_rail/add/', LongRailCreateView.as_view(), name='long_rail_add'),
    path('long_rail/delete/<int:pk>/', LongRailDeleteView.as_view(), name='long_rail_delete'),

    # Main action buttons
    path('process_dispatch/', ProcessDispatchView.as_view(), name='process_dispatch'),
    path('cancel_dispatch/', CancelDispatchView.as_view(), name='cancel_dispatch'),
]

```

#### 4.6 Tests (`dispatch/tests.py`)

Comprehensive tests ensure the models function correctly and views handle requests as expected, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from decimal import Decimal
import json

from .models import CrossRailTemp, LongRailTemp, PitchDispatchMaster, CrossRailDispatch, LongRailDispatch, Company, FinancialYear, GunrailBOMMaster
from .forms import CrossRailTempForm, LongRailTempForm, PitchDispatchForm

# Mocking Company and FinancialYear for testing purposes as they are foreign keys
# In a real scenario, you'd ensure these exist or mock them from another app.
class SetupFixtures(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user
        cls.user = User.objects.create_user(username='testuser', password='testpassword')
        cls.company, created_comp = Company.objects.get_or_create(id=1, defaults={'name': 'Test Company'})
        cls.fin_year, created_fin = FinancialYear.objects.get_or_create(id=1, defaults={'year_start': '2023-01-01', 'year_end': '2023-12-31'})

        # Add temporary data for testing
        CrossRailTemp.objects.create(id=1, length=Decimal('10.500'), number=Decimal('2.000'), session_id='testuser', company=cls.company)
        CrossRailTemp.objects.create(id=2, length=Decimal('5.200'), number=Decimal('3.000'), session_id='testuser', company=cls.company)
        LongRailTemp.objects.create(id=1, length=Decimal('20.000'), number=Decimal('1.500'), session_id='testuser', company=cls.company)

        # Create a mock BOMMaster for the process_dispatch logic
        GunrailBOMMaster.objects.get_or_create(id=100, pid=0, wo_no='WO12345', company=cls.company, financial_year=cls.fin_year) # Parent BOM
        GunrailBOMMaster.objects.get_or_create(id=101, pid=100, wo_no='WO12345', company=cls.company, financial_year=cls.fin_year) # Child BOM (dest_cid)

class ModelTests(SetupFixtures):
    def test_cross_rail_temp_creation(self):
        item = CrossRailTemp.objects.get(id=1)
        self.assertEqual(item.length, Decimal('10.500'))
        self.assertEqual(item.number, Decimal('2.000'))
        self.assertEqual(item.session_id, 'testuser')
        self.assertEqual(item.company.id, self.company.id)
        self.assertEqual(item.total, Decimal('21.000'))

    def test_long_rail_temp_creation(self):
        item = LongRailTemp.objects.get(id=1)
        self.assertEqual(item.length, Decimal('20.000'))
        self.assertEqual(item.number, Decimal('1.500'))
        self.assertEqual(item.session_id, 'testuser')
        self.assertEqual(item.company.id, self.company.id)
        self.assertEqual(item.total, Decimal('30.000'))

    def test_pitch_dispatch_master_process_dispatch_success(self):
        # Clear existing temporary data for a clean test
        CrossRailTemp.objects.filter(session_id='testuser', company=self.company).delete()
        LongRailTemp.objects.filter(session_id='testuser', company=self.company).delete()
        
        # Re-add some temp data for processing
        CrossRailTemp.objects.create(id=3, length=Decimal('1.0'), number=Decimal('1.0'), session_id='testuser', company=self.company)
        LongRailTemp.objects.create(id=2, length=Decimal('1.0'), number=Decimal('1.0'), session_id='testuser', company=self.company)

        initial_master_count = PitchDispatchMaster.objects.count()
        initial_cr_dispatch_count = CrossRailDispatch.objects.count()
        initial_lr_dispatch_count = LongRailDispatch.objects.count()

        PitchDispatchMaster.process_gunrail_dispatch(
            wo_no='WO12345',
            pitch_type=0, # Swivel
            pitch_value=Decimal('5.0'),
            user_id='testuser',
            company_id=self.company.id,
            fin_year_id=self.fin_year.id
        )

        self.assertEqual(PitchDispatchMaster.objects.count(), initial_master_count + 1)
        self.assertEqual(CrossRailDispatch.objects.count(), initial_cr_dispatch_count + 1)
        self.assertEqual(LongRailDispatch.objects.count(), initial_lr_dispatch_count + 1)
        self.assertFalse(CrossRailTemp.objects.filter(session_id='testuser', company=self.company).exists())
        self.assertFalse(LongRailTemp.objects.filter(session_id='testuser', company=self.company).exists())

    def test_pitch_dispatch_master_process_dispatch_invalid_data(self):
        # Test case where temp data is missing
        CrossRailTemp.objects.filter(session_id='testuser', company=self.company).delete() # Make CR temp empty
        with self.assertRaisesMessage(ValueError, "Invalid data entry. Cross Rail, Long Rail data, or Pitch is missing."):
            PitchDispatchMaster.process_gunrail_dispatch(
                wo_no='WO12345',
                pitch_type=0,
                pitch_value=Decimal('5.0'),
                user_id='testuser',
                company_id=self.company.id,
                fin_year_id=self.fin_year.id
            )
        
        # Restore temp data for next test, clear LR temp
        CrossRailTemp.objects.create(id=4, length=Decimal('1.0'), number=Decimal('1.0'), session_id='testuser', company=self.company)
        LongRailTemp.objects.filter(session_id='testuser', company=self.company).delete() # Make LR temp empty
        with self.assertRaisesMessage(ValueError, "Invalid data entry. Cross Rail, Long Rail data, or Pitch is missing."):
            PitchDispatchMaster.process_gunrail_dispatch(
                wo_no='WO12345',
                pitch_type=0,
                pitch_value=Decimal('5.0'),
                user_id='testuser',
                company_id=self.company.id,
                fin_year_id=self.fin_year.id
            )

        # Restore all temp data, then test missing pitch
        LongRailTemp.objects.create(id=3, length=Decimal('1.0'), number=Decimal('1.0'), session_id='testuser', company=self.company)
        with self.assertRaisesMessage(ValueError, "Invalid data entry. Cross Rail, Long Rail data, or Pitch is missing."):
            PitchDispatchMaster.process_gunrail_dispatch(
                wo_no='WO12345',
                pitch_type=0,
                pitch_value=None, # Missing pitch
                user_id='testuser',
                company_id=self.company.id,
                fin_year_id=self.fin_year.id
            )

class ViewTests(SetupFixtures):
    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='testpassword')
        # Set session variables as they would be in a real request
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year.id
        session.save()

    def test_dispatch_detail_view_get(self):
        response = self.client.get(reverse('dispatch:gunrail_dispatch_detail') + '?WONo=WO12345')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dispatch/dispatch_detail.html')
        self.assertContains(response, 'WO12345')
        self.assertIsInstance(response.context['pitch_form'], PitchDispatchForm)

    def test_cross_rail_table_partial_view(self):
        response = self.client.get(reverse('dispatch:cross_rail_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dispatch/_cross_rail_table.html')
        self.assertContains(response, '10.500') # Check for existing data
        self.assertContains(response, 'class="dataTables_wrapper"') # Check for DataTables div

    def test_cross_rail_add_view_post_success(self):
        data = {'length': '7.777', 'number': '4.444'}
        response = self.client.post(reverse('dispatch:cross_rail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success status
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCrossRailList', response.headers['HX-Trigger'])
        self.assertTrue(CrossRailTemp.objects.filter(length=Decimal('7.777'), number=Decimal('4.444'), session_id='testuser').exists())

    def test_cross_rail_add_view_post_invalid(self):
        data = {'length': '0.000', 'number': '5'} # Invalid length
        response = self.client.post(reverse('dispatch:cross_rail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered
        self.assertTemplateUsed(response, 'dispatch/_cross_rail_form.html')
        self.assertContains(response, 'Length cannot be zero.')
        self.assertFalse(CrossRailTemp.objects.filter(length=Decimal('0.000')).exists())

    def test_cross_rail_delete_view_post_success(self):
        item_to_delete = CrossRailTemp.objects.create(id=99, length=Decimal('1.0'), number=Decimal('1.0'), session_id='testuser', company=self.company)
        response = self.client.delete(reverse('dispatch:cross_rail_delete', args=[item_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCrossRailList', response.headers['HX-Trigger'])
        self.assertFalse(CrossRailTemp.objects.filter(id=item_to_delete.id).exists())

    def test_cross_rail_delete_view_post_unauthorized_delete(self):
        # Create an item for another user
        other_company = Company.objects.create(id=2, name='Other Company')
        other_item = CrossRailTemp.objects.create(id=100, length=Decimal('1.0'), number=Decimal('1.0'), session_id='otheruser', company=other_company)
        response = self.client.delete(reverse('dispatch:cross_rail_delete', args=[other_item.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Should return 404 if not found for current user
        self.assertTrue(CrossRailTemp.objects.filter(id=other_item.id).exists()) # Ensure it wasn't deleted

    def test_long_rail_table_partial_view(self):
        response = self.client.get(reverse('dispatch:long_rail_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dispatch/_long_rail_table.html')
        self.assertContains(response, '20.000') # Check for existing data

    def test_long_rail_add_view_post_success(self):
        data = {'length': '15.5', 'number': '2.5'}
        response = self.client.post(reverse('dispatch:long_rail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshLongRailList', response.headers['HX-Trigger'])
        self.assertTrue(LongRailTemp.objects.filter(length=Decimal('15.500'), number=Decimal('2.500'), session_id='testuser').exists())

    def test_process_dispatch_view_post_success(self):
        # Ensure there's temp data before processing
        CrossRailTemp.objects.filter(session_id='testuser', company=self.company).delete()
        LongRailTemp.objects.filter(session_id='testuser', company=self.company).delete()
        CrossRailTemp.objects.create(id=5, length=Decimal('1.0'), number=Decimal('1.0'), session_id='testuser', company=self.company)
        LongRailTemp.objects.create(id=4, length=Decimal('1.0'), number=Decimal('1.0'), session_id='testuser', company=self.company)

        initial_master_count = PitchDispatchMaster.objects.count()
        initial_cr_dispatch_count = CrossRailDispatch.objects.count()
        initial_lr_dispatch_count = LongRailDispatch.objects.count()

        data = {
            'type': '0', # Swivel
            'pitch': '5.0',
        }
        response = self.client.post(reverse('dispatch:process_dispatch') + '?WONo=WO12345', data)
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertEqual(response.url, reverse('dispatch:gunrail_dispatch_detail') + '?WONo=WO12345')

        self.assertEqual(PitchDispatchMaster.objects.count(), initial_master_count + 1)
        self.assertEqual(CrossRailDispatch.objects.count(), initial_cr_dispatch_count + 1)
        self.assertEqual(LongRailDispatch.objects.count(), initial_lr_dispatch_count + 1)
        self.assertFalse(CrossRailTemp.objects.filter(session_id='testuser').exists())
        self.assertFalse(LongRailTemp.objects.filter(session_id='testuser').exists())

    def test_process_dispatch_view_post_validation_failure(self):
        # No temp data, should trigger ValueError from model
        CrossRailTemp.objects.filter(session_id='testuser', company=self.company).delete()
        LongRailTemp.objects.filter(session_id='testuser', company=self.company).delete()

        data = {
            'type': '0',
            'pitch': '5.0',
        }
        response = self.client.post(reverse('dispatch:process_dispatch') + '?WONo=WO12345', data)
        self.assertEqual(response.status_code, 200) # Renders page with error
        self.assertContains(response, 'Invalid data entry. Cross Rail, Long Rail data, or Pitch is missing.')
        self.assertTemplateUsed(response, 'dispatch/dispatch_detail.html')

    def test_process_dispatch_view_post_form_invalid(self):
        # Missing pitch
        data = {
            'type': '0',
            # 'pitch': '', # Missing or invalid pitch
        }
        response = self.client.post(reverse('dispatch:process_dispatch') + '?WONo=WO12345', data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'This field is required.')
        self.assertTemplateUsed(response, 'dispatch/dispatch_detail.html')

    def test_cancel_dispatch_view(self):
        response = self.client.get(reverse('dispatch:cancel_dispatch'))
        self.assertEqual(response.status_code, 302)
        # Assuming 'sales_distribution:wo_grid' is the correct redirect target
        self.assertEqual(response.url, reverse('sales_distribution:wo_grid'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Updates:**
    - The main `dispatch_detail.html` page uses `hx-get` on `crossRailTable-container` and `longRailTable-container` with `hx-trigger="load, refreshCrossRailList from:body"` and `hx-trigger="load, refreshLongRailList from:body"`. This means the tables will load initially and refresh whenever a `refreshCrossRailList` or `refreshLongRailList` custom event is triggered on the `<body>`.
    - `CrossRailCreateView` and `LongRailCreateView` (add new items) and `CrossRailDeleteView` and `LongRailDeleteView` (delete items) return `HttpResponse` with `status=204` (No Content) and `HX-Trigger` headers. This tells HTMX to trigger the custom refresh events without full page reload.
    - Add/Delete buttons in the tables use `hx-get` to load forms (`_confirm_delete.html`) into a modal (`#modalContent`) and `hx-swap="innerHTML"`.
    - Form submissions for adding items (in the table footers) use `hx-post` and `hx-swap="none"`, relying on the `HX-Trigger` from the backend to refresh the list.
- **Alpine.js for UI State Management:**
    - The modal visibility (`#modal`) is managed by `_` (hyperscript, which is closely integrated with HTMX and Alpine.js philosophies for simple DOM scripting). It adds/removes the `is-active` class to show/hide the modal.
    - Basic `confirm` dialogs are used for add/delete operations, as per the ASP.NET original behavior, which is simple and doesn't require complex Alpine.js.
- **DataTables for List Views:**
    - Each table partial (`_cross_rail_table.html`, `_long_rail_table.html`) includes a `<script>` block that initializes `jQuery(document).ready(function() { $('#tableName').DataTable(); });`.
    - The main template's `htmx:afterSwap` event listener ensures that `DataTable` is initialized (or re-initialized/destroyed) *after* HTMX injects the table HTML, preventing re-initialization errors and ensuring dynamic DataTables functionality.
    - DataTables provides client-side searching, sorting, and pagination without additional backend logic required for these common features.
- **Full Page Reload Prevention:** All CRUD operations on temporary items are handled by HTMX, eliminating full page postbacks for list updates. The `ProcessDispatchView` currently performs a full redirect, consistent with the original ASP.NET `Response.Redirect` for the final submission. This can be adapted to HTMX if further dynamic updates on the main page are desired after final processing.

### Final Notes

- **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names based on the ASP.NET analysis.
- **DRY Templates:** `_confirm_delete.html` serves as a reusable partial. The table structures are similar but distinct enough to warrant separate `_cross_rail_table.html` and `_long_rail_table.html` due to specific field labels and potentially future divergent logic.
- **Business Logic in Models:** The `process_gunrail_dispatch` class method in `PitchDispatchMaster` fully encapsulates the complex multi-step transaction and calculations, keeping views concise and focused on HTTP concerns.
- **Comprehensive Tests:** Both model unit tests and view integration tests are provided, aiming for high coverage and ensuring correct behavior of the migrated logic and HTMX interactions.
- **Tailwind CSS:** All form widgets and general HTML structures are given Tailwind CSS classes for modern, responsive styling, replacing the legacy `yui-datatable.css` and `styles.css`.
- **Database `Id` Management:** The `id = models.IntegerField(primary_key=True, db_column='Id')` with `managed=False` implies that `Id` is managed by the existing database. For new record inserts, Django's ORM typically assumes `id` will be auto-assigned by the database. If your database does not auto-increment this column for these tables, you would need to implement explicit ID generation (e.g., finding the maximum existing ID and incrementing it) within your `save` methods or `CreateView` `form_valid` methods. This has been added as a common workaround in the `CreateView` examples.