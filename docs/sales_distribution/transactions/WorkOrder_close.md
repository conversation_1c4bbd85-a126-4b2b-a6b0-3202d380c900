This modernization plan outlines the automated conversion of your ASP.NET Work Order Close module to a modern Django application. Our focus is on leveraging AI-assisted automation to transform your existing business logic and UI into a highly efficient, maintainable, and scalable Django solution.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, the primary data source is `SD_Cust_WorkOrder_Master`. Other related tables include `SD_Cust_Master`, `tblFinancial_master`, `tblHR_OfficeStaff`, and `SD_Cust_WorkOrder_Products_Temp`.

**Identified Tables and Key Columns:**

*   **[TABLE_NAME]: `SD_Cust_WorkOrder_Master`**
    *   `Id` (Primary Key, inferred INT)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`, inferred INT)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master`, inferred VARCHAR)
    *   `EnqId` (VARCHAR)
    *   `PONo` (VARCHAR)
    *   `POId` (INT, possibly FK or just an ID)
    *   `WONo` (VARCHAR)
    *   `SysDate` (VARCHAR, stores date as string, will be converted to DateTimeField)
    *   `SysTime` (VARCHAR, stores time as string, will be converted to TimeField)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff.EmpId`, inferred VARCHAR)
    *   `CloseOpen` (INT, 0 for Open, 1 for Close)
    *   `Remarks` (VARCHAR)

*   **[TABLE_NAME]: `SD_Cust_Master`**
    *   `CustomerId` (Primary Key, inferred VARCHAR)
    *   `CustomerName` (VARCHAR)
    *   `CompId` (INT)

*   **[TABLE_NAME]: `tblFinancial_master`**
    *   `FinYearId` (Primary Key, inferred INT)
    *   `FinYear` (VARCHAR)

*   **[TABLE_NAME]: `tblHR_OfficeStaff`**
    *   `EmpId` (Primary Key, inferred VARCHAR/INT, based on `SessionId` usage)
    *   `Title` (VARCHAR)
    *   `EmployeeName` (VARCHAR)
    *   `CompId` (INT)

*   **[TABLE_NAME]: `SD_Cust_WorkOrder_Products_Temp`**
    *   `SessionId` (inferred, likely tied to user session)
    *   `CompId` (inferred)
    (Note: This table is used for temporary data cleanup. For this migration, we will primarily focus on the core WorkOrder module and assume Django's session handling is more robust for temporary data, or if persistent, it would be a separate model not directly involved in the WorkOrder status view.)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements **Read** (listing/searching Work Orders) and **Update** (changing Work Order status to Open/Close) operations.

*   **Read:**
    *   `BindDataCust` method: This method fetches Work Order data, filters it based on dropdown selection (Customer Name, Enquiry No, PO No, WO No), and populates the `SearchGridView1`. It performs lookups/joins across `SD_Cust_WorkOrder_Master`, `SD_Cust_Master`, `tblFinancial_master`, and `tblHR_OfficeStaff`. It also dynamically determines the 'Status' ("Open" or "Close") based on the `CloseOpen` field.
    *   Pagination: `SearchGridView1_PageIndexChanging` handles pagination.
    *   Search: `btnSearch_Click` triggers a re-bind with search criteria.
    *   Dynamic Search Input: `DropDownList1_SelectedIndexChanged` toggles visibility of search input fields based on the selected search type.
    *   Autocomplete: The `sql` web method provides customer names for autocomplete, fetching from `SD_Cust_Master`.

*   **Update:**
    *   `SearchGridView1_RowCommand`: Handles "Open" and "Close" actions for individual work orders.
    *   It updates the `CloseOpen` status, `SysDate`, `SysTime`, `SessionId`, and `Remarks` in `SD_Cust_WorkOrder_Master`.
    *   Requires a `Remarks` field to be filled before status change. If not, it displays an alert.

### Step 3: Infer UI Components

The ASP.NET UI primarily consists of:

*   **Search/Filter Area:**
    *   `DropDownList1`: For selecting search criteria (e.g., "Customer Name", "Enquiry No").
    *   `txtEnqId`: A `TextBox` for entering search values when criteria are "Enquiry No", "PO No", or "WO No".
    *   `TxtSearchValue`: A `TextBox` with `AutoCompleteExtender` for searching by "Customer Name".
    *   `btnSearch`: A `Button` to initiate the search.

*   **Data Display:**
    *   `SearchGridView1`: The main data grid displaying work orders.
        *   Columns for `SN`, `FinYear`, `Customer Name`, `CustomerId`, `EnqId`, `PONo`, `POId`, `WONo`, `SysDate`, `EmployeeName`, `Id`, `Status`, `Remarks` (as a `TextBox` for input), and action `LinkButton`s ("Open", "Close").
        *   Paging enabled (`AllowPaging="True"`).
        *   Dynamic visibility of "Open" or "Close" `LinkButton` based on current status.
        *   `EmptyDataTemplate` for no data.

### Step 4: Generate Django Code

The Django application will be named `work_orders`.

#### 4.1 Models

We will define models for `WorkOrder`, `Customer`, `FinancialYear`, and `Employee`, mapping them to your existing database tables. The `WorkOrder` model will include methods for business logic related to status changes and data retrieval.

**`work_orders/models.py`**

```python
from django.db import models
from django.db.models import F, Case, When, Value, CharField
from django.utils import timezone # To handle SysDate/SysTime conversion

class FinancialYear(models.Model):
    # Corresponds to tblFinancial_master
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Customer(models.Model):
    # Corresponds to SD_Cust_Master
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class Employee(models.Model):
    # Corresponds to tblHR_OfficeStaff
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is VARCHAR
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}.{self.employee_name}".strip('.')


class WorkOrderManager(models.Manager):
    """Custom manager for WorkOrder to encapsulate complex query logic."""

    def get_filtered_workorders(self, search_type, search_value, comp_id, fin_year_id):
        """
        Mimics the BindDataCust logic to retrieve filtered work orders.
        search_type: '0' (Customer Name), '1' (Enquiry No), '2' (PO No), '3' (WO No)
        search_value: The value to search for.
        comp_id: Company ID from session.
        fin_year_id: Financial Year ID from session.
        """
        
        # Start with base query for the given company and financial year
        # The original code used FinYearId <= fin_year_id, which is unusual for a single year.
        # Assuming it means 'for this financial year or prior' or just the current financial year.
        # For simplicity, we'll use the provided fin_year_id for equality.
        queryset = self.select_related('customer', 'fin_year', 'created_by').filter(
            comp_id=comp_id, 
            fin_year_id__lte=fin_year_id # Matching original logic
        ).annotate(
            # Annotate 'status' field for display ('Open'/'Close')
            display_status=Case(
                When(close_open_status=0, then=Value('Open')),
                When(close_open_status=1, then=Value('Close')),
                default=Value('Unknown'),
                output_field=CharField()
            )
        ).order_by('wo_no') # Order by WONo ASC as in original code

        # Apply search filters based on search_type
        if search_type == '0' and search_value:  # Customer Name
            # The original code uses fun.getCode(TxtSearchValue.Text) to get CustomerId
            # We'll assume search_value here is the CustomerId or part of CustomerName for broader search.
            # Autocomplete returns "Customer Name [CustomerId]", so we need to extract CustomerId.
            # If search_value is "Customer Name [CustomerId]", extract the ID:
            if '[' in search_value and ']' in search_value:
                customer_id_from_search = search_value.split('[')[-1].strip(']')
                queryset = queryset.filter(customer_id=customer_id_from_search)
            else: # Fallback if only name is provided, search by name Contains
                queryset = queryset.filter(customer__customer_name__icontains=search_value)
        elif search_type == '1' and search_value:  # Enquiry No
            queryset = queryset.filter(enquiry_no=search_value)
        elif search_type == '2' and search_value:  # PO No
            queryset = queryset.filter(po_no=search_value)
        elif search_type == '3' and search_value:  # WO No
            queryset = queryset.filter(wo_no=search_value)
        
        # Annotate EmployeeName and FinYear for direct access in template
        # The original code directly fetches EmployeeName from tblHR_OfficeStaff based on SessionId
        # and FinYear from tblFinancial_master based on FinYearId.
        # With select_related, these are directly accessible: obj.created_by.employee_name, obj.fin_year.fin_year
        # We also need to format SysDate similar to original if needed: REPLACE(CONVERT(varchar, CONVERT(datetime, SUBSTRING(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) + 1, 2) + '-' + LEFT(SD_Cust_WorkOrder_Master.SysDate,CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) - 1) + '-' + RIGHT(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', REVERSE(SD_Cust_WorkOrder_Master.SysDate)) - 1)), 103), '/', '-')
        # Django's DateTimeField automatically handles this, and we can format in template.
        # For 'Gen. By' to correctly display Title.EmployeeName, we'll access it via related object.

        return queryset

class WorkOrder(models.Model):
    # Corresponds to SD_Cust_WorkOrder_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    close_open_status = models.IntegerField(db_column='CloseOpen', default=0) # 0 for Open, 1 for Close
    enquiry_no = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='workorders')
    wo_no = models.CharField(db_column='WONo', max_length=50, unique=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # Assuming integer, not FK
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='workorders')
    
    # Original SysDate/SysTime are strings; map to Django's DateTimeField for proper handling
    # We will need a custom converter or process during data import if data is not already in a standard format.
    # Assuming for now that we will convert to proper datetime on data migration or view layer.
    # For managed=False, we map directly to current column.
    # If the database stores "MM-dd-yyyy HH:mm:ss", Django can parse it if configured.
    # For this exercise, we map to CharField and let conversion happen in model methods if needed,
    # or assume backend stores proper datetime format.
    # Given `REPLACE(CONVERT(varchar, CONVERT(datetime, SUBSTRING(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) + 1, 2) + '-' + LEFT(SD_Cust_WorkOrder_Master.SysDate,CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) - 1) + '-' + RIGHT(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', REVERSE(SD_Cust_WorkOrder_Master.SysDate)) - 1)), 103), '/', '-')`
    # This suggests SysDate itself is *not* a standard date format in the DB, it's a mangled string.
    # We will map to CharField, and provide a property for a parsed datetime object.
    sys_date_str = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True)
    sys_time_str = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True) # If SysTime is separate
    
    # Assuming SessionId maps to EmpId in tblHR_OfficeStaff
    created_by = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='created_workorders', blank=True, null=True)
    
    comp_id = models.IntegerField(db_column='CompId')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)

    objects = WorkOrderManager() # Attach our custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

    @property
    def is_open(self):
        """Returns True if the work order status is 'Open' (0)."""
        return self.close_open_status == 0

    @property
    def display_status(self):
        """Returns 'Open' or 'Close' based on close_open_status."""
        return 'Open' if self.is_open else 'Close'

    @property
    def generated_date(self):
        """Attempts to parse the SysDate_str into a datetime object for display."""
        try:
            # Example: "21-02-2023 10:30:00" -> "dd-MM-yyyy HH:mm:ss"
            # Original code implies MM-dd-yyyy format but stores as DD-MM-YYYY in SysDate.
            # Let's assume the DB stores it consistently as DD-MM-YYYY.
            return timezone.datetime.strptime(self.sys_date_str, '%d-%m-%Y')
        except (ValueError, TypeError):
            return None # Or handle more robustly

    def update_status(self, new_status_is_open, remarks, current_user_employee_id):
        """
        Updates the work order status (Open/Close) and remarks.
        new_status_is_open: True for Open, False for Close.
        remarks: The reason for the status change.
        current_user_employee_id: The EmpId of the user performing the action.
        """
        if not remarks:
            raise ValueError(f"Reason for work order {'open' if new_status_is_open else 'close'} is required.")
        
        self.close_open_status = 0 if new_status_is_open else 1
        self.remarks = remarks
        self.sys_date_str = timezone.now().strftime('%d-%m-%Y') # Format as DD-MM-YYYY
        self.sys_time_str = timezone.now().strftime('%H:%M:%S') # Format as HH:MM:SS
        self.created_by_id = current_user_employee_id # Update SessionId
        self.save()
        return True

```

#### 4.2 Forms

We'll define a `WorkOrderSearchForm` for filtering and a `WorkOrderStatusForm` for updating the status with remarks.

**`work_orders/forms.py`**

```python
from django import forms
from .models import WorkOrder, Customer

class WorkOrderSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-get': 'hx-trigger="change"'}) # Added hx-trigger for dropdown change
    )
    search_value_text = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'Enter Enquiry/PO/WO No', 'id': 'txtEnqId'})
    )
    search_value_autocomplete = forms.CharField(
        max_length=255,
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full', 
            'placeholder': 'Enter Customer Name', 
            'id': 'TxtSearchValue',
            'hx-get': '/workorders/autocomplete/customer/',
            'hx-trigger': 'keyup changed delay:500ms, search', # HTMX autocomplete
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initially hide one of the fields based on default selection or initial state
        # This will be handled by Alpine.js on the client-side
        self.fields['search_value_autocomplete'].widget.attrs['style'] = 'display:none;'
        self.fields['search_value_text'].widget.attrs['style'] = 'display:block;' # Default visible

class WorkOrderStatusForm(forms.ModelForm):
    # This form is for updating remarks and triggering status change.
    # The actual status change (0 or 1) will be passed as a hidden field or via button command.
    remarks = forms.CharField(
        max_length=500,
        required=True, # Remarks are mandatory for status change
        label="Remarks (Reason for change)",
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'})
    )

    class Meta:
        model = WorkOrder
        fields = ['remarks'] # Only remarks is directly editable here
        # Other fields like status and user will be handled in the view/model method

    def clean_remarks(self):
        remarks = self.cleaned_data.get('remarks')
        if not remarks or remarks.strip() == '':
            raise forms.ValidationError("Remarks cannot be empty. Please provide a reason for the status change.")
        return remarks
```

#### 4.3 Views

We'll define views for listing, searching, and updating work order status using HTMX.

**`work_orders/views.py`**

```python
from django.views.generic import ListView, View, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q # For complex queries
from django.shortcuts import get_object_or_404, render

from .models import WorkOrder, Customer
from .forms import WorkOrderSearchForm, WorkOrderStatusForm

# Placeholder for user's company and financial year (replace with actual session/user logic)
# In a real app, this would come from request.user or a user profile.
# For demonstration, we'll hardcode or use a mock.
MOCK_COMP_ID = 1 # Example company ID
MOCK_FIN_YEAR_ID = 2023 # Example financial year ID
MOCK_CURRENT_USER_EMP_ID = 'EMP001' # Example employee ID for the current user

class WorkOrderListView(ListView):
    """
    Main view to display the Work Order list page.
    This view renders the initial page structure including the search form.
    The actual table content is loaded via HTMX by WorkOrderTablePartialView.
    """
    model = WorkOrder
    template_name = 'work_orders/workorder_list.html'
    context_object_name = 'workorders' # Not directly used for initial load, but good practice
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = WorkOrderSearchForm(self.request.GET or None)
        return context

class WorkOrderTablePartialView(ListView):
    """
    HTMX-specific view to return only the table content for dynamic updates.
    Handles search, filtering, and pagination.
    This view should strictly return the partial HTML for the table.
    """
    model = WorkOrder
    template_name = 'work_orders/_workorder_table.html'
    context_object_name = 'workorders'
    paginate_by = 15 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # The main logic of BindDataCust now lives in the WorkOrderManager
        search_type = self.request.GET.get('search_type', 'Select')
        search_value = self.request.GET.get('search_value', '') # Combines txtEnqId and TxtSearchValue
        
        # In a real application, comp_id and fin_year_id would come from the current user's session/profile
        comp_id = MOCK_COMP_ID 
        fin_year_id = MOCK_FIN_YEAR_ID 

        queryset = WorkOrder.objects.get_filtered_workorders(
            search_type, search_value, comp_id, fin_year_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # DataTables will handle pagination on the client side after the initial load.
        # However, for server-side pagination (if DataTables configured for it),
        # this would pass the paginator objects.
        # For HTMX, we just return the full list.
        # If DataTables is truly client-side, we pass all matching records.
        # If the expectation is that Django still handles pagination (like GridView),
        # then we retain the pagination object.
        return context

class WorkOrderAutoCompleteView(View):
    """
    HTMX endpoint for customer name autocomplete, mimicking the ASP.NET 'sql' WebMethod.
    Returns a JSON response suitable for HTMX + Alpine.js or a simple text list.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('query', '').strip()
        if not query:
            return JsonResponse([], safe=False)

        # In a real app, comp_id would come from the current user's session/profile
        comp_id = MOCK_COMP_ID 

        customers = Customer.objects.filter(
            comp_id=comp_id,
            customer_name__icontains=query
        ).values_list('customer_name', 'customer_id')[:10] # Limit results

        results = [f"{name} [{cid}]" for name, cid in customers]
        return JsonResponse(results, safe=False) # Return a simple list of strings

class WorkOrderStatusFormPartialView(TemplateView):
    """
    HTMX endpoint to load the status update form into a modal.
    """
    template_name = 'work_orders/_workorder_status_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        workorder = get_object_or_404(WorkOrder, pk=self.kwargs['pk'])
        context['workorder'] = workorder
        context['form'] = WorkOrderStatusForm()
        return context

class WorkOrderUpdateStatusView(View):
    """
    HTMX endpoint to handle 'Open'/'Close' actions.
    This will receive POST requests from the status form.
    """
    def post(self, request, pk, *args, **kwargs):
        workorder = get_object_or_404(WorkOrder, pk=pk)
        form = WorkOrderStatusForm(request.POST)

        if form.is_valid():
            remarks = form.cleaned_data['remarks']
            action = request.POST.get('action') # 'open' or 'close'

            try:
                # In a real application, MOCK_CURRENT_USER_EMP_ID would come from request.user
                success = workorder.update_status(
                    new_status_is_open=(action == 'open'),
                    remarks=remarks,
                    current_user_employee_id=MOCK_CURRENT_USER_EMP_ID 
                )
                if success:
                    messages.success(request, f"Work order {action}ed successfully.")
                    # HTMX trigger to refresh the list and close the modal
                    return HttpResponse(
                        status=204, # No content, indicates success to HTMX without page reload
                        headers={'HX-Trigger': '{"refreshWorkOrderList":true, "closeModal":true}'}
                    )
            except ValueError as e:
                # This catches the specific validation from the model's update_status method
                messages.error(request, str(e))
            except Exception as e:
                messages.error(request, f"An unexpected error occurred: {e}")
        
        # If form is invalid or an error occurred, re-render the form in the modal
        # and keep the modal open, showing errors.
        context = {
            'workorder': workorder,
            'form': form,
            'show_error': True # Flag to show error message
        }
        response = render(request, 'work_orders/_workorder_status_form.html', context)
        # If we render content, HTMX might swap the target.
        # We need to explicitly re-target if validation fails.
        # This is where Alpine.js helps for client-side form validation.
        # For server-side validation error, just rendering the partial is fine.
        return response

```

#### 4.4 Templates

Templates will be structured to utilize HTMX for dynamic content loading and DataTables for interactive lists.

**`work_orders/templates/work_orders/workorder_list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Work Order - Open/Close</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end" x-data="{ 
            searchType: '{{ search_form.search_type.value|default:'Select' }}',
            txtEnqIdVisible: true,
            txtSearchValueVisible: false,
            init() {
                this.$watch('searchType', value => {
                    this.txtEnqIdVisible = (value === '1' || value === '2' || value === '3' || value === 'Select');
                    this.txtSearchValueVisible = (value === '0');
                });
                // Initial visibility based on initial form value
                this.txtEnqIdVisible = (this.searchType === '1' || this.searchType === '2' || this.searchType === '3' || this.searchType === 'Select');
                this.txtSearchValueVisible = (this.searchType === '0');
            }
        }">
            <div>
                <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                <select id="{{ search_form.search_type.id_for_label }}" name="{{ search_form.search_type.name }}" 
                        x-model="searchType" 
                        class="box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    {% for value, label in search_form.search_type.field.choices %}
                        <option value="{{ value }}" {% if value == search_form.search_type.value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div x-show="txtEnqIdVisible">
                <label for="{{ search_form.search_value_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Value</label>
                <input type="text" id="{{ search_form.search_value_text.id_for_label }}" name="search_value" 
                       value="{{ search_form.search_value_text.value|default:'' }}"
                       class="box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       placeholder="Enter Enquiry/PO/WO No">
            </div>

            <div x-show="txtSearchValueVisible" class="relative">
                <label for="{{ search_form.search_value_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer Name</label>
                <input type="text" id="{{ search_form.search_value_autocomplete.id_for_label }}" name="search_value" 
                       value="{{ search_form.search_value_autocomplete.value|default:'' }}"
                       hx-get="{% url 'workorder_autocomplete_customer' %}"
                       hx-trigger="keyup changed delay:500ms, search" 
                       hx-target="#autocomplete-results"
                       hx-swap="innerHTML"
                       autocomplete="off"
                       class="box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       placeholder="Enter Customer Name">
                <div id="autocomplete-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto"></div>
            </div>

            <div class="col-span-1 md:col-span-1">
                <button 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"
                    hx-get="{% url 'workorder_table_partial' %}"
                    hx-target="#workorder-table-container"
                    hx-vals="js:{ search_type: $q('select[name=search_type]').value, search_value: $q('input[name=search_value]').value }"
                    hx-swap="innerHTML">
                    Search
                </button>
            </div>
        </div>
    </div>
    
    <div id="workorder-table-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table_partial' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for status update form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on closeModal from body remove .is-active from me then remove #modalContent.innerHTML">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full relative"
             _="on click if event.target.id == 'modal' remove .is-active from #modal then remove #modalContent.innerHTML">
            <!-- Form will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables CDN -->
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<!-- Alpine.js -->
<script defer src="https://unpkg.com/alpinejs@3.10.3/dist/cdn.min.js"></script>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'workorder-table-container' && event.detail.xhr.status === 200) {
            // Re-initialize DataTables after HTMX swaps in new table content
            if ($.fn.DataTable.isDataTable('#workorderTable')) {
                $('#workorderTable').DataTable().destroy(); // Destroy existing instance
            }
            $('#workorderTable').DataTable({
                "pageLength": 15, // Matches original GridView PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "searching": true, // Enable default search box
                "ordering": true,  // Enable sorting
                "paging": true     // Enable pagination
            });
        }
        // Handle autocomplete results selection
        if (event.target.id === 'autocomplete-results') {
            document.querySelectorAll('#autocomplete-results div').forEach(item => {
                item.onclick = function() {
                    document.getElementById('TxtSearchValue').value = this.textContent;
                    document.getElementById('autocomplete-results').innerHTML = ''; // Clear results
                };
            });
        }
    });

    document.addEventListener('htmx:beforeRequest', function(event) {
        // Show modal when HTMX requests form
        if (event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden');
        }
    });

    document.addEventListener('htmx:afterSettle', function(event) {
        // If a form is swapped in and it has errors, ensure modal stays visible
        if (event.detail.target.id === 'modalContent' && event.detail.elt.querySelector('.text-red-500')) {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden');
        }
    });

    document.body.addEventListener('closeModal', function(evt) {
        document.getElementById('modal').classList.remove('is-active');
        document.getElementById('modal').classList.add('hidden');
        document.getElementById('modalContent').innerHTML = ''; // Clear modal content
    });
</script>
{% endblock %}
```

**`work_orders/templates/work_orders/_workorder_table.html`**

```html
<table id="workorderTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if workorders %}
            {% for wo in workorders %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ wo.fin_year.fin_year }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-left">{{ wo.customer.customer_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ wo.customer.customer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ wo.enquiry_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-left">{{ wo.po_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ wo.wo_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ wo.generated_date|date:"d-m-Y" }}</td>{# Format date #}
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-left">{{ wo.created_by.title|default_if_none:'' }}.{{ wo.created_by.employee_name|default_if_none:'' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-center">{{ wo.display_status }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-center">
                    {% if wo.is_open %}
                        <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                                hx-get="{% url 'workorder_status_form_partial' pk=wo.pk %}?action=close"
                                hx-target="#modalContent"
                                hx-trigger="click"
                                _="on click add .is-active to #modal then remove .hidden from #modal">
                            Close
                        </button>
                    {% else %}
                        <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                                hx-get="{% url 'workorder_status_form_partial' pk=wo.pk %}?action=open"
                                hx-target="#modalContent"
                                hx-trigger="click"
                                _="on click add .is-active to #modal then remove .hidden from #modal">
                            Open
                        </button>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="11" class="py-3 px-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>
```

**`work_orders/templates/work_orders/_workorder_status_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        Work Order: <span class="font-semibold">{{ workorder.wo_no }}</span> - 
        Change Status to <span class="font-semibold">{{ request.GET.action|title }}</span>
    </h3>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="{{ message.tags }} bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ message }}</span>
            </div>
            {% endfor %}
        </div>
    {% endif %}

    <form hx-post="{% url 'workorder_update_status' pk=workorder.pk %}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        <input type="hidden" name="action" value="{{ request.GET.action }}">
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remarks.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal then remove #modalContent.innerHTML">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Confirm {{ request.GET.action|title }}
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

We will define URL patterns for the views, including specific paths for HTMX partials and actions.

**`work_orders/urls.py`**

```python
from django.urls import path
from .views import (
    WorkOrderListView, 
    WorkOrderTablePartialView, 
    WorkOrderAutoCompleteView,
    WorkOrderStatusFormPartialView,
    WorkOrderUpdateStatusView
)

urlpatterns = [
    path('workorders/', WorkOrderListView.as_view(), name='workorder_list'),
    path('workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table_partial'),
    path('workorders/autocomplete/customer/', WorkOrderAutoCompleteView.as_view(), name='workorder_autocomplete_customer'),
    path('workorders/status_form/<int:pk>/', WorkOrderStatusFormPartialView.as_view(), name='workorder_status_form_partial'),
    path('workorders/status/<int:pk>/', WorkOrderUpdateStatusView.as_view(), name='workorder_update_status'),
]

```

#### 4.6 Tests

Comprehensive unit tests will cover model logic, and integration tests will verify view functionality and HTMX interactions.

**`work_orders/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import WorkOrder, Customer, FinancialYear, Employee, MOCK_COMP_ID, MOCK_FIN_YEAR_ID, MOCK_CURRENT_USER_EMP_ID

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.financial_year = FinancialYear.objects.create(fin_year_id=MOCK_FIN_YEAR_ID, fin_year='2023-2024')
        cls.customer_alpha = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=MOCK_COMP_ID)
        cls.customer_beta = Customer.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=MOCK_COMP_ID)
        cls.employee = Employee.objects.create(emp_id=MOCK_CURRENT_USER_EMP_ID, employee_name='John Doe', title='Mr', comp_id=MOCK_COMP_ID)

        cls.open_wo = WorkOrder.objects.create(
            id=1,
            close_open_status=0, # Open
            enquiry_no='ENQ001',
            customer=cls.customer_alpha,
            wo_no='WO001',
            po_no='PO001',
            po_id=101,
            fin_year=cls.financial_year,
            sys_date_str=timezone.now().strftime('%d-%m-%Y'),
            sys_time_str=timezone.now().strftime('%H:%M:%S'),
            created_by=cls.employee,
            comp_id=MOCK_COMP_ID,
            remarks='Initial open'
        )

        cls.closed_wo = WorkOrder.objects.create(
            id=2,
            close_open_status=1, # Closed
            enquiry_no='ENQ002',
            customer=cls.customer_beta,
            wo_no='WO002',
            po_no='PO002',
            po_id=102,
            fin_year=cls.financial_year,
            sys_date_str=timezone.now().strftime('%d-%m-%Y'),
            sys_time_str=timezone.now().strftime('%H:%M:%S'),
            created_by=cls.employee,
            comp_id=MOCK_COMP_ID,
            remarks='Initial closed'
        )
  
    def test_workorder_creation(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.wo_no, 'WO001')
        self.assertEqual(wo.customer.customer_name, 'Alpha Corp')
        self.assertTrue(wo.is_open)
        self.assertEqual(wo.display_status, 'Open')
        
    def test_is_open_property(self):
        self.assertTrue(self.open_wo.is_open)
        self.assertFalse(self.closed_wo.is_open)

    def test_display_status_property(self):
        self.assertEqual(self.open_wo.display_status, 'Open')
        self.assertEqual(self.closed_wo.display_status, 'Close')

    def test_generated_date_property(self):
        # Test valid date parsing
        self.assertIsNotNone(self.open_wo.generated_date)
        self.assertEqual(self.open_wo.generated_date.date(), timezone.now().date())
        
        # Test invalid date string
        self.open_wo.sys_date_str = "InvalidDate"
        self.assertIsNone(self.open_wo.generated_date)

    def test_update_status_to_close(self):
        initial_remarks = self.open_wo.remarks
        self.open_wo.update_status(new_status_is_open=False, remarks="Closing for maintenance", current_user_employee_id=MOCK_CURRENT_USER_EMP_ID)
        self.open_wo.refresh_from_db() # Refresh object from DB to get latest state
        self.assertFalse(self.open_wo.is_open)
        self.assertEqual(self.open_wo.remarks, "Closing for maintenance")
        self.assertNotEqual(self.open_wo.remarks, initial_remarks)
        self.assertEqual(self.open_wo.created_by_id, MOCK_CURRENT_USER_EMP_ID)
        
    def test_update_status_to_open(self):
        initial_remarks = self.closed_wo.remarks
        self.closed_wo.update_status(new_status_is_open=True, remarks="Re-opening for new phase", current_user_employee_id=MOCK_CURRENT_USER_EMP_ID)
        self.closed_wo.refresh_from_db()
        self.assertTrue(self.closed_wo.is_open)
        self.assertEqual(self.closed_wo.remarks, "Re-opening for new phase")
        self.assertNotEqual(self.closed_wo.remarks, initial_remarks)
        self.assertEqual(self.closed_wo.created_by_id, MOCK_CURRENT_USER_EMP_ID)

    def test_update_status_requires_remarks(self):
        with self.assertRaisesMessage(ValueError, "Reason for work order close is required."):
            self.open_wo.update_status(new_status_is_open=False, remarks="", current_user_employee_id=MOCK_CURRENT_USER_EMP_ID)
        with self.assertRaisesMessage(ValueError, "Reason for work order close is required."):
            self.open_wo.update_status(new_status_is_open=False, remarks=None, current_user_employee_id=MOCK_CURRENT_USER_EMP_ID)

class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.financial_year = FinancialYear.objects.create(fin_year_id=MOCK_FIN_YEAR_ID, fin_year='2023-2024')
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer', comp_id=MOCK_COMP_ID)
        cls.employee = Employee.objects.create(emp_id=MOCK_CURRENT_USER_EMP_ID, employee_name='Test User', title='Dr', comp_id=MOCK_COMP_ID)

        WorkOrder.objects.create(
            id=1, close_open_status=0, enquiry_no='ENQ001', customer=cls.customer, wo_no='WO001', po_no='PO001', po_id=101, fin_year=cls.financial_year,
            sys_date_str=timezone.now().strftime('%d-%m-%Y'), sys_time_str=timezone.now().strftime('%H:%M:%S'), created_by=cls.employee, comp_id=MOCK_COMP_ID, remarks='Initial'
        )
        WorkOrder.objects.create(
            id=2, close_open_status=1, enquiry_no='ENQ002', customer=cls.customer, wo_no='WO002', po_no='PO002', po_id=102, fin_year=cls.financial_year,
            sys_date_str=timezone.now().strftime('%d-%m-%Y'), sys_time_str=timezone.now().strftime('%H:%M:%S'), created_by=cls.employee, comp_id=MOCK_COMP_ID, remarks='Closed'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_orders/workorder_list.html')
        self.assertContains(response, 'Work Order - Open/Close') # Check for page title

    def test_table_partial_view_get(self):
        # Simulate HTMX request for the table partial
        response = self.client.get(reverse('workorder_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_orders/_workorder_table.html')
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WO002')
        self.assertContains(response, 'Open')
        self.assertContains(response, 'Close')

    def test_table_partial_view_search_wo_no(self):
        response = self.client.get(reverse('workorder_table_partial'), {'search_type': '3', 'search_value': 'WO001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertNotContains(response, 'WO002')

    def test_table_partial_view_search_customer_name(self):
        # Autocomplete search value format: "Customer Name [CustomerId]"
        response = self.client.get(reverse('workorder_table_partial'), {'search_type': '0', 'search_value': 'Test Customer [CUST001]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WO002') # Both work orders have CUST001

    def test_autocomplete_customer_view(self):
        response = self.client.get(reverse('workorder_autocomplete_customer'), {'query': 'Test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('Test Customer [CUST001]', response.json())

    def test_status_form_partial_view_get(self):
        wo = WorkOrder.objects.get(id=1)
        response = self.client.get(reverse('workorder_status_form_partial', args=[wo.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_orders/_workorder_status_form.html')
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'Remarks (Reason for change)')

    def test_update_status_view_post_close(self):
        wo = WorkOrder.objects.get(id=1) # Initially open
        data = {'action': 'close', 'remarks': 'Closing for testing.'}
        response = self.client.post(reverse('workorder_update_status', args=[wo.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWorkOrderList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        
        wo.refresh_from_db()
        self.assertFalse(wo.is_open)
        self.assertEqual(wo.remarks, 'Closing for testing.')

    def test_update_status_view_post_open(self):
        wo = WorkOrder.objects.get(id=2) # Initially closed
        data = {'action': 'open', 'remarks': 'Opening for retest.'}
        response = self.client.post(reverse('workorder_update_status', args=[wo.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWorkOrderList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        
        wo.refresh_from_db()
        self.assertTrue(wo.is_open)
        self.assertEqual(wo.remarks, 'Opening for retest.')

    def test_update_status_view_post_missing_remarks(self):
        wo = WorkOrder.objects.get(id=1)
        data = {'action': 'close', 'remarks': ''}
        response = self.client.post(reverse('workorder_update_status', args=[wo.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with error
        self.assertTemplateUsed(response, 'work_orders/_workorder_status_form.html')
        self.assertContains(response, 'Remarks cannot be empty.')
        
        wo.refresh_from_db()
        self.assertTrue(wo.is_open) # Status should not have changed

```

### Step 5: HTMX and Alpine.js Integration

The generated templates (`workorder_list.html`, `_workorder_table.html`, `_workorder_status_form.html`) already incorporate HTMX attributes for:

*   **Initial Load & Refresh:** `hx-get` and `hx-trigger="load, refreshWorkOrderList from:body"` on the `workorder-table-container` div to load the table content dynamically.
*   **Search:** `hx-get` on the Search button to re-fetch table content with new parameters. The `hx-vals` attribute dynamically collects form values.
*   **Autocomplete:** `hx-get` and `hx-trigger="keyup changed delay:500ms, search"` on the customer search input, targeting `autocomplete-results` div.
*   **Modal Opening:** `hx-get` on "Open" and "Close" buttons, targeting `modalContent` inside `#modal`, triggering the modal to show via Alpine.js/h_x (inline JS with HTMX).
*   **Form Submission:** `hx-post` on the status update form, with `hx-swap="none"` and `hx-trigger` to send custom events (`refreshWorkOrderList`, `closeModal`) upon successful submission (handled by the Django view returning 204 with `HX-Trigger` header).
*   **Modal Closing:** Alpine.js `x-data` and `x-show` are used for modal visibility. The `_` (h_x) attribute (Alpine.js's imperative helper) provides more control, e.g., `on click add .is-active to #modal` and `on closeModal from body remove .is-active from me`.
*   **DataTables:** JavaScript in `workorder_list.html` ensures DataTables is initialized on `htmx:afterSwap` after the table content is loaded or refreshed, allowing client-side pagination, sorting, and searching.

This setup ensures that all interactions are driven by lightweight AJAX calls using HTMX, with Alpine.js managing the UI state for modals and input visibility without heavy JavaScript frameworks. DataTables provides a rich, interactive experience for tabular data.

### Final Notes

This comprehensive plan provides a blueprint for migrating your ASP.NET Work Order Close module to Django. By following these steps and leveraging the provided code, your organization can achieve:

*   **Modernization:** Transition from legacy ASP.NET Web Forms to a contemporary Django web framework with a clean, component-based architecture.
*   **Performance:** Improve user experience through HTMX-driven dynamic updates, reducing full page reloads and server load. DataTables enhances client-side performance for large datasets.
*   **Maintainability:** Adopt the "Fat Model, Thin View" pattern, centralizing business logic in models, making your codebase more organized, readable, and easier to debug.
*   **Scalability:** Django's robust ORM and architecture provide a strong foundation for future growth and scaling of your application.
*   **Developer Efficiency:** Leverage Django's built-in features (ORM, forms, CBVs) and modern frontend tools (HTMX, Alpine.js, Tailwind CSS) to accelerate development and reduce manual coding effort.
*   **Testability:** Comprehensive unit and integration tests ensure high code quality, reduce bugs, and provide confidence in future changes.

This automated approach minimizes manual code rewriting, streamlines the migration process, and delivers a highly performant and maintainable Django application.