## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code for `CostSheet.aspx` and its code-behind is a minimal placeholder, indicating a page exists but providing no specific logic, UI components, or database interactions. Therefore, this modernization plan will proceed by assuming a standard "Cost Sheet" management functionality, including typical data fields and CRUD operations, to demonstrate the full migration process as requested. This approach ensures a comprehensive example, even with limited initial input.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is a placeholder, we're inferring the database schema based on the page name "CostSheet". We'll assume a common table naming convention and a few relevant columns for a cost sheet.

**Inferred Schema:**

*   **Table Name:** `tblCostSheets`
*   **Columns:**
    *   `SheetID` (Primary Key, integer)
    *   `SheetName` (String, e.g., "Project Alpha Cost")
    *   `Amount` (Decimal, e.g., 1234.56)
    *   `DateCreated` (Date)
    *   `IsActive` (Boolean, for soft deletion)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the placeholder nature of the ASP.NET page, we'll assume standard CRUD (Create, Read, Update, Delete) functionality for managing cost sheets.

*   **Create:** Ability to add new cost sheets via a form.
*   **Read:** Display a list of all cost sheets, typically with filtering and sorting.
*   **Update:** Edit details of an existing cost sheet via a form.
*   **Delete:** Remove a cost sheet from the system (potentially a soft delete).

We will also assume basic validation for required fields, which will be replicated in Django forms.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Based on a typical ASP.NET "Cost Sheet" page, we infer the following UI components and their Django equivalents:

*   **Data Display:**
    *   **ASP.NET:** `GridView`
    *   **Django Equivalent:** An HTML `<table>` rendered by Django's template engine, enhanced with DataTables for client-side features.
*   **Input Fields:**
    *   **ASP.NET:** `TextBox` (for `SheetName`, `Amount`), `CalendarExtender` or similar for `DateCreated`, `CheckBox` for `IsActive`.
    *   **Django Equivalent:** Django `forms.TextInput` for text and numbers, `forms.DateInput` for dates, `forms.CheckboxInput` for booleans.
*   **Action Buttons:**
    *   **ASP.NET:** `Button` (e.g., "Add New", "Save", "Edit", "Delete").
    *   **Django Equivalent:** HTML `<button>` elements, with `hx-get`, `hx-post`, `hx-target`, `hx-trigger` attributes for HTMX-driven interactions.
*   **Page Structure:**
    *   **ASP.NET:** `MasterPageFile` content placeholders.
    *   **Django Equivalent:** Template inheritance using `{% extends 'core/base.html' %}`.

## Step 4: Generate Django Code

We will create a new Django application named `sales_distribution` to house the `CostSheet` module, aligning with the ASP.NET namespace `Module_SalesDistribution_Transactions`.

### 4.1 Models (`sales_distribution/models.py`)

Task: Create a Django model based on the inferred database schema.

## Instructions:

The `CostSheet` model maps directly to the `tblCostSheets` table. We define fields that reflect the database columns and include a `__str__` method for better object representation. Business logic, if identified, would reside in model methods.

```python
from django.db import models
from decimal import Decimal

class CostSheet(models.Model):
    sheet_id = models.AutoField(db_column='SheetID', primary_key=True) # Assuming SheetID is PK
    sheet_name = models.CharField(db_column='SheetName', max_length=255, verbose_name='Sheet Name')
    amount = models.DecimalField(db_column='Amount', max_digits=10, decimal_places=2, verbose_name='Amount')
    date_created = models.DateField(db_column='DateCreated', verbose_name='Date Created')
    is_active = models.BooleanField(db_column='IsActive', default=True, verbose_name='Is Active')

    class Meta:
        managed = False  # Important: Django will not manage this table's schema (it exists externally)
        db_table = 'tblCostSheets'
        verbose_name = 'Cost Sheet'
        verbose_name_plural = 'Cost Sheets'
        ordering = ['-date_created', 'sheet_name'] # Default ordering

    def __str__(self):
        return f"{self.sheet_name} ({self.amount})"
        
    def get_display_amount(self):
        """
        Example of a business logic method in the model.
        Formats the amount for display.
        """
        return f"${self.amount:,.2f}"

    def can_be_deleted(self):
        """
        Example business logic: Determines if a cost sheet can be deleted.
        Perhaps based on its age or linked transactions.
        """
        # Placeholder for actual business logic
        # For instance, if self.date_created < some_threshold: return False
        return True
```

### 4.2 Forms (`sales_distribution/forms.py`)

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` for `CostSheet` will handle input and basic validation. Widgets are used to apply Tailwind CSS classes directly, ensuring a consistent look and feel across the application.

```python
from django import forms
from .models import CostSheet

class CostSheetForm(forms.ModelForm):
    class Meta:
        model = CostSheet
        fields = ['sheet_name', 'amount', 'date_created', 'is_active']
        widgets = {
            'sheet_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'date_created': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        
    def clean_amount(self):
        """
        Example of custom form validation. Ensures amount is positive.
        """
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive value.")
        return amount

    # Additional custom validation methods would go here based on ASP.NET validation rules.
```

### 4.3 Views (`sales_distribution/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are kept thin, adhering to the 15-line limit for method bodies. Business logic is delegated to the model or handled by Django's CBV structure. HTMX-specific headers are used to trigger frontend updates after successful form submissions or deletions. A `TablePartialView` is added to support HTMX reloading of the DataTables content.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import CostSheet
from .forms import CostSheetForm

# Helper mixin for HTMX responses
class HxTriggerMixin:
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f"{self.model._meta.verbose_name} saved successfully.")
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCostSheetList'})
        return response

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        if not obj.can_be_deleted(): # Example of calling model business logic
            messages.error(self.request, f"Cannot delete {self.model._meta.verbose_name} '{obj}'.")
            return HttpResponse(status=400, content="Deletion not allowed.") # Or render form with error

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f"{self.model._meta.verbose_name} deleted successfully.")
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCostSheetList'})
        return response

class CostSheetListView(ListView):
    model = CostSheet
    template_name = 'sales_distribution/costsheet/list.html'
    context_object_name = 'costsheets'

class CostSheetTablePartialView(ListView):
    model = CostSheet
    template_name = 'sales_distribution/costsheet/_costsheet_table.html'
    context_object_name = 'costsheets'

class CostSheetCreateView(HxTriggerMixin, CreateView):
    model = CostSheet
    form_class = CostSheetForm
    template_name = 'sales_distribution/costsheet/_costsheet_form.html' # Use partial for modal
    success_url = reverse_lazy('costsheet_list') # Fallback if not HTMX

class CostSheetUpdateView(HxTriggerMixin, UpdateView):
    model = CostSheet
    form_class = CostSheetForm
    template_name = 'sales_distribution/costsheet/_costsheet_form.html' # Use partial for modal
    success_url = reverse_lazy('costsheet_list') # Fallback if not HTMX

class CostSheetDeleteView(HxTriggerMixin, DeleteView):
    model = CostSheet
    template_name = 'sales_distribution/costsheet/_costsheet_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('costsheet_list') # Fallback if not HTMX
```

### 4.4 Templates (`sales_distribution/templates/sales_distribution/costsheet/`)

Task: Create templates for each view.

## Instructions:

Templates are structured for reusability and HTMX-driven partial updates. `list.html` includes the overall page structure and modal placeholders. `_costsheet_table.html` provides the DataTables content, loaded via HTMX. `_costsheet_form.html` and `_costsheet_confirm_delete.html` are partials for modal interactions.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Cost Sheets</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'costsheet_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
            hx-on::after-request="if(event.detail.successful) { console.log('Form loaded successfully'); } else { alert('Failed to load form.'); }">
            Add New Cost Sheet
        </button>
    </div>
    
    <div id="costsheetTable-container"
         hx-trigger="load, refreshCostSheetList from:body"
         hx-get="{% url 'costsheet_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- DataTables content will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading cost sheets...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden opacity-0 transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then remove .flex from me then remove .scale-100 from #modalContent">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full transform scale-95 transition-transform duration-300">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally or on specific elements using x-data.
    // No specific Alpine.js script needed here unless for custom UI state.
    // For modal close, we use Alpine.js/HTMX interop via _ attribute.
</script>
{% endblock %}
```

**`_costsheet_table.html`** (Partial template for DataTables)

```html
<table id="costsheetTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Sheet Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date Created</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in costsheets %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.sheet_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.get_display_amount }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.date_created|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {{ obj.is_active|yesno:"Active,Inactive" }}
                </span>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'costsheet_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'costsheet_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only after the table is loaded
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#costsheetTable')) {
            $('#costsheetTable').DataTable().destroy(); // Destroy existing instance if any
        }
        $('#costsheetTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`_costsheet_form.html`** (Partial template for Add/Edit forms)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Cost Sheet</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::htmx:afterRequest="if(event.detail.successful) { document.getElementById('modal').classList.remove('flex'); document.getElementById('modal').classList.remove('opacity-100'); document.getElementById('modalContent').classList.remove('scale-100'); }"
          >
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            <div>
                <label for="{{ form.sheet_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.sheet_name.label }}
                </label>
                {{ form.sheet_name }}
                {% if form.sheet_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.sheet_name.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.amount.label }}
                </label>
                {{ form.amount }}
                {% if form.amount.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.date_created.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.date_created.label }}
                </label>
                {{ form.date_created }}
                {% if form.date_created.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.date_created.errors }}</p>
                {% endif %}
            </div>
            
            <div class="flex items-center pt-2">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
                    {{ form.is_active.label }}
                </label>
                {% if form.is_active.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.is_active.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modal then remove .flex from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_costsheet_confirm_delete.html`** (Partial template for delete confirmation)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the cost sheet "<span class="font-medium text-indigo-700">{{ object.sheet_name }}</span>"? This action cannot be undone.</p>
    
    <form hx-post="{% url 'costsheet_delete' object.pk %}" hx-swap="none"
          hx-on::htmx:afterRequest="if(event.detail.successful) { document.getElementById('modal').classList.remove('flex'); document.getElementById('modal').classList.remove('opacity-100'); document.getElementById('modalContent').classList.remove('scale-100'); }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modal then remove .flex from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`sales_distribution/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

This file defines the routes for the `CostSheet` functionality, including endpoints for the main list view and the HTMX-loaded partials for forms and tables.

```python
from django.urls import path
from .views import CostSheetListView, CostSheetCreateView, CostSheetUpdateView, CostSheetDeleteView, CostSheetTablePartialView

urlpatterns = [
    # Main list view for Cost Sheets
    path('costsheets/', CostSheetListView.as_view(), name='costsheet_list'),
    
    # HTMX partial endpoint for the DataTables content
    path('costsheets/table/', CostSheetTablePartialView.as_view(), name='costsheet_table'),

    # HTMX partial endpoint for adding a new Cost Sheet (modal form)
    path('costsheets/add/', CostSheetCreateView.as_view(), name='costsheet_add'),
    
    # HTMX partial endpoint for editing an existing Cost Sheet (modal form)
    path('costsheets/edit/<int:pk>/', CostSheetUpdateView.as_view(), name='costsheet_edit'),
    
    # HTMX partial endpoint for confirming and deleting a Cost Sheet (modal form)
    path('costsheets/delete/<int:pk>/', CostSheetDeleteView.as_view(), name='costsheet_delete'),
]
```

### 4.6 Tests (`sales_distribution/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Comprehensive tests are crucial for verifying the migration. These tests cover model integrity, business logic within the model, and full CRUD functionality of the views, including specific assertions for HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import CostSheet
from datetime import date
from decimal import Decimal

class CostSheetModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        CostSheet.objects.create(
            sheet_name='Initial Project Budget',
            amount=Decimal('5000.00'),
            date_created=date(2023, 1, 15),
            is_active=True
        )
        CostSheet.objects.create(
            sheet_name='Marketing Campaign Costs',
            amount=Decimal('1250.75'),
            date_created=date(2023, 2, 20),
            is_active=False
        )
  
    def test_cost_sheet_creation(self):
        # Verify that the object was created correctly
        obj = CostSheet.objects.get(pk=1) # Using pk as it's the primary_key
        self.assertEqual(obj.sheet_name, 'Initial Project Budget')
        self.assertEqual(obj.amount, Decimal('5000.00'))
        self.assertEqual(obj.date_created, date(2023, 1, 15))
        self.assertTrue(obj.is_active)
        
    def test_sheet_name_label(self):
        obj = CostSheet.objects.get(pk=1)
        field_label = obj._meta.get_field('sheet_name').verbose_name
        self.assertEqual(field_label, 'Sheet Name')
        
    def test_amount_decimal_places(self):
        obj = CostSheet.objects.get(pk=1)
        # Check that decimal places are preserved
        self.assertEqual(obj.amount.as_tuple().exponent, -2)

    def test_get_display_amount_method(self):
        obj = CostSheet.objects.get(sheet_name='Initial Project Budget')
        self.assertEqual(obj.get_display_amount(), '$5,000.00')
        obj2 = CostSheet.objects.get(sheet_name='Marketing Campaign Costs')
        self.assertEqual(obj2.get_display_amount(), '$1,250.75')

    def test_can_be_deleted_method(self):
        obj = CostSheet.objects.get(pk=1)
        # Assuming our placeholder can_be_deleted always returns True
        self.assertTrue(obj.can_be_deleted())
        # Add more complex tests if actual business logic is involved

    def test_object_str_representation(self):
        obj = CostSheet.objects.get(pk=1)
        self.assertEqual(str(obj), 'Initial Project Budget (5000.00)')

class CostSheetViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        CostSheet.objects.create(
            sheet_name='Test Sheet 1',
            amount=Decimal('100.00'),
            date_created=date(2023, 3, 1),
            is_active=True
        )
        CostSheet.objects.create(
            sheet_name='Test Sheet 2',
            amount=Decimal('200.00'),
            date_created=date(2023, 3, 2),
            is_active=False
        )
    
    def setUp(self):
        # Set up a new client for each test method to ensure clean state
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('costsheet_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/costsheet/list.html')
        self.assertIn('costsheets', response.context)
        self.assertEqual(len(response.context['costsheets']), 2) # Check number of objects
        
    def test_table_partial_view(self):
        # This view is typically called via HTMX
        response = self.client.get(reverse('costsheet_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/costsheet/_costsheet_table.html')
        self.assertIn('costsheets', response.context)
        self.assertContains(response, 'Test Sheet 1')
        self.assertContains(response, 'Test Sheet 2')

    def test_create_view_get(self):
        # Testing HTMX GET request for the modal form
        response = self.client.get(reverse('costsheet_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/costsheet/_costsheet_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_successful(self):
        # Testing HTMX POST request for form submission
        data = {
            'sheet_name': 'New Cost Sheet A',
            'amount': '350.50',
            'date_created': '2023-04-01',
            'is_active': 'on',
        }
        response = self.client.post(reverse('costsheet_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for successful swap="none"
        self.assertTrue(CostSheet.objects.filter(sheet_name='New Cost Sheet A').exists())
        self.assertIn('HX-Trigger', response.headers) # Verify HTMX trigger for refresh
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCostSheetList')

    def test_create_view_post_invalid(self):
        # Test with invalid data (e.g., negative amount)
        data = {
            'sheet_name': 'Invalid Sheet',
            'amount': '-10.00', # Invalid amount
            'date_created': '2023-04-01',
            'is_active': 'on',
        }
        response = self.client.post(reverse('costsheet_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX expects 200 to swap in new form
        self.assertFalse(CostSheet.objects.filter(sheet_name='Invalid Sheet').exists())
        self.assertIn('form', response.context)
        self.assertIn('Amount must be a positive value.', str(response.content)) # Check for error message
        
    def test_update_view_get(self):
        obj = CostSheet.objects.get(sheet_name='Test Sheet 1')
        response = self.client.get(reverse('costsheet_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/costsheet/_costsheet_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_successful(self):
        obj = CostSheet.objects.get(sheet_name='Test Sheet 1')
        data = {
            'sheet_name': 'Updated Sheet 1',
            'amount': '150.00',
            'date_created': '2023-03-01',
            'is_active': 'on',
        }
        response = self.client.post(reverse('costsheet_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.sheet_name, 'Updated Sheet 1')
        self.assertEqual(obj.amount, Decimal('150.00'))
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCostSheetList')

    def test_delete_view_get(self):
        obj = CostSheet.objects.get(sheet_name='Test Sheet 1')
        response = self.client.get(reverse('costsheet_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/costsheet/_costsheet_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_successful(self):
        obj_to_delete = CostSheet.objects.get(sheet_name='Test Sheet 1')
        response = self.client.post(reverse('costsheet_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(CostSheet.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCostSheetList')

    def test_delete_view_post_not_allowed(self):
        # Create a special object that can't be deleted (by overriding can_be_deleted)
        class MockCostSheet(CostSheet):
            def can_be_deleted(self):
                return False
            class Meta:
                proxy = True # So it doesn't try to create a new db table

        undeletable_obj = MockCostSheet.objects.create(
            sheet_name='Undeletable Sheet',
            amount=Decimal('1.00'),
            date_created=date(2023, 1, 1),
            is_active=True
        )

        # Patch the actual CostSheet model's can_be_deleted for this test
        original_can_be_deleted = CostSheet.can_be_deleted
        CostSheet.can_be_deleted = lambda self: False

        try:
            response = self.client.post(reverse('costsheet_delete', args=[undeletable_obj.pk]), HTTP_HX_REQUEST='true')
            self.assertEqual(response.status_code, 400) # Expecting an error status
            self.assertTrue(CostSheet.objects.filter(pk=undeletable_obj.pk).exists()) # Should not be deleted
            self.assertFalse('HX-Trigger' in response.headers) # No trigger on failure
            self.assertIn(b"Deletion not allowed.", response.content) # Check for custom error message
        finally:
            # Restore original method
            CostSheet.can_be_deleted = original_can_be_deleted

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The entire user experience for `CostSheet` management is designed around HTMX and Alpine.js to provide a fast, dynamic, and modern interface without full page reloads.

*   **List View (`costsheet_list`):**
    *   The main list page uses `hx-get` to load the actual table content (`costsheet_table`) on page load and whenever a `refreshCostSheetList` event is triggered. This means the table can update dynamically after any CRUD operation.
    *   The "Add New Cost Sheet" button uses `hx-get` to fetch the form (`costsheet_add`) into a modal, triggered by a click.
*   **Table Partial (`_costsheet_table.html`):**
    *   Each "Edit" and "Delete" button inside the table uses `hx-get` to fetch the respective `_costsheet_form.html` or `_costsheet_confirm_delete.html` into the modal, also triggered by a click.
    *   DataTables JavaScript is initialized on this partial, ensuring correct functionality when the table content is reloaded via HTMX. The `$(document).ready` combined with a `destroy()` check handles re-initialization gracefully.
*   **Form Partial (`_costsheet_form.html`):**
    *   The form uses `hx-post` to submit data back to the same URL (`request.path`).
    *   `hx-swap="none"` is crucial here: it tells HTMX not to replace the content of the modal. Instead, the server sends a `HX-Trigger` header (`refreshCostSheetList`) upon successful submission.
    *   The `hx-on::htmx:afterRequest` listener on the form ensures the modal closes (by removing Tailwind classes) *after* the HTMX request is complete and successful.
*   **Delete Confirmation Partial (`_costsheet_confirm_delete.html`):**
    *   Similar to the form, it uses `hx-post` and `hx-swap="none"`.
    *   The server sends a `HX-Trigger` header upon successful deletion to refresh the list.
    *   An `hx-on::htmx:afterRequest` listener closes the modal.
*   **Alpine.js:**
    *   Integrated via the `_` (hyperscript) attribute to manage modal visibility (adding/removing `hidden`, `flex`, `opacity-100`, `scale-100` classes) for a smoother transition effect. This keeps JavaScript concerns minimal and localized directly in the HTML.
*   **No Additional JavaScript:** All dynamic interactions are handled by HTMX, Alpine.js, or DataTables (which is a jQuery plugin, but its integration is managed within the HTMX partial). There is no need for custom JavaScript files.

## Final Notes

This comprehensive modernization plan provides a clear, step-by-step guide to transform the ASP.NET Cost Sheet module into a modern Django application. Key takeaways for business stakeholders include:

*   **Improved User Experience:** The use of HTMX and Alpine.js delivers a highly responsive, single-page application-like feel without the complexity of traditional JavaScript frameworks. Users will experience instant updates and interactions without full page reloads.
*   **Maintainability and Scalability:** Django's structured approach with clear separation of concerns (Fat Models, Thin Views) makes the codebase easier to understand, maintain, and extend in the long run.
*   **Database Integration:** The `managed = False` setting for models ensures seamless integration with the existing database, minimizing disruption to current data.
*   **Automated Testing:** The focus on comprehensive unit and integration tests provides a safety net, ensuring that new features or changes do not break existing functionality. This reduces risks and speeds up development cycles.
*   **Modern Practices:** Adopting Django 5.0+, Tailwind CSS, HTMX, and Alpine.js aligns the application with current web development best practices, ensuring it is robust, secure, and performant.

This plan is designed to be executable through an AI-assisted automation process, generating the bulk of the code and requiring human oversight for business logic translation and final review. It prioritizes efficiency, quality, and a modern outcome.