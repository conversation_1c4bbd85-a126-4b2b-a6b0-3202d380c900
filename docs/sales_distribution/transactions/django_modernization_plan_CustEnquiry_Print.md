This document outlines a strategic plan for migrating your existing ASP.NET Customer Enquiry application to a modern Django-based solution. Our approach prioritizes automation and leverages best-in-class tools to ensure a smooth transition, reducing manual effort and potential errors.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns involved in the ASP.NET code.

Instructions:

Based on the ASP.NET code-behind, specifically the `BindDataCust` method calling `Sp_Enquiry_Grid` and the `sql` WebMethod querying `SD_Cust_master`, we infer the following entities:

*   **[TABLE_NAME] for Customer Information:** `SD_Cust_master`
    *   **Columns:**
        *   `CustomerId` (likely primary key, `CharField` in Django)
        *   `CustomerName` (`CharField`)
        *   `CompId` (Company ID, `IntegerField`, used for filtering in the autocomplete)

*   **[TABLE_NAME] for Enquiry Information:** `T_ENQUIRY_HDR` (Inferred from common ERP patterns, as `Sp_Enquiry_Grid` suggests an enquiry header table. The GridView columns represent its data.)
    *   **Columns:**
        *   `EnqId` (Enquiry ID, primary key, `CharField` in Django, as `txtEnqId` implies string input)
        *   `FinYear` (Financial Year, `IntegerField`)
        *   `CustomerId` (Foreign Key to `SD_Cust_master`, `CharField` in DB, mapped to `Customer` model)
        *   `SysDate` (System Date, `DateTimeField`)
        *   `EmployeeName` (Generated By Employee Name, `CharField`)
        *   `CompId` (Company ID, `IntegerField`, used for filtering in the stored procedure)

### Step 2: Identify Backend Functionality

Task: Determine the operations performed by the ASP.NET code.

Instructions:

The ASP.NET page primarily handles **Read** operations with advanced **Filtering/Searching** and **Pagination**. There are no direct Create, Update, or Delete operations on the enquiry record itself on *this specific page*. The `LinkButton` in the GridView suggests navigation to a separate detail/print page for a specific enquiry, where further actions might occur.

*   **Read:** Displaying a list of customer enquiries in a grid format.
*   **Search/Filter:** Users can search enquiries by:
    *   Customer Name (with an autocomplete feature).
    *   Enquiry Number.
*   **Pagination:** The `GridView` has built-in paging, which will be migrated to client-side DataTables.
*   **Dynamic UI:** The search input fields (`TxtSearchValue`, `txtEnqId`) dynamically show/hide based on the `DropDownList` selection. This will be handled with HTMX.
*   **Autocomplete:** For "Customer Name" search, fetching suggestions from `SD_Cust_master`.
*   **Navigation:** Redirecting to a "CustEnquiry_Print_Details.aspx" page when a customer name is clicked in the grid, passing `CustomerId` and `EnqId`.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles to map them to Django/HTMX/Tailwind.

Instructions:

*   **Search Controls:**
    *   `DropDownList1` (Search By): A Django `ChoiceField` rendered as an HTML `<select>`. Its `onselectedindexchanged` `AutoPostBack` will be replaced by an HTMX `hx-get` to dynamically update the search input fields.
    *   `TxtSearchValue` (Customer Name Search): A Django `CharField` rendered as an HTML `<input type="text">`. The `AutoCompleteExtender` will be replaced by HTMX `hx-get` to an autocomplete endpoint, showing suggestions in a dropdown.
    *   `txtEnqId` (Enquiry Number Search): A Django `CharField` rendered as an HTML `<input type="text">`. Its visibility will be toggled by HTMX based on the dropdown selection.
    *   `btnSearch` (Search Button): An HTML `<button type="submit">`. Its `onclick` event will trigger an HTMX form submission to refresh the enquiry list table.
*   **Data Display:**
    *   `SearchGridView1`: This will be replaced by an HTML `<table>` element, styled with Tailwind CSS, and enhanced with the DataTables.js library for client-side features like sorting, searching, and pagination.
    *   `LinkButton` for `CustomerName`: This will be an HTML `<button>` or `<a>` tag with HTMX attributes to trigger a client-side redirect to the detail page.
    *   `EmptyDataTemplate`: Replicated by Django's template `{% empty %}` tag.

### Step 4: Generate Django Code

We will structure the Django application under a new app, e.g., `enquiries`, to manage customer enquiries.

#### 4.1 Models

We will define two models: `Customer` and `Enquiry`, mapping them to the identified database tables.

```python
# enquiries/models.py
from django.db import models

class Customer(models.Model):
    """
    Represents customer master data.
    Maps to the existing SD_Cust_master table.
    """
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True, verbose_name="Customer Code")
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name="Customer Name")
    # Assuming CompId is a column in SD_Cust_master based on ASP.NET autocomplete query.
    company_id = models.IntegerField(db_column='CompId', default=1, verbose_name="Company ID") 

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'SD_Cust_master'  # The actual table name in your database
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

    # Business logic for customer-related operations can be added here
    # For example, validation, default values, or complex lookups.

class Enquiry(models.Model):
    """
    Represents customer enquiry header data.
    Maps to the existing T_ENQUIRY_HDR table.
    """
    # EnqId is likely the primary key based on DataKeyNames in GridView
    enq_id = models.CharField(db_column='EnqId', max_length=50, primary_key=True, verbose_name="Enquiry No.")
    financial_year = models.IntegerField(db_column='FinYear', verbose_name="Financial Year")
    # CustomerId in the DB is a FK to SD_Cust_master.
    customer = models.ForeignKey(
        Customer,
        on_delete=models.DO_NOTHING,  # Or models.PROTECT depending on desired DB behavior
        db_column='CustomerId',       # The actual column name in T_ENQUIRY_HDR
        related_name='enquiries',
        verbose_name="Customer"
    )
    system_date = models.DateTimeField(db_column='SysDate', verbose_name="Generated Date")
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, verbose_name="Generated By")
    # Assuming CompId is a column in T_ENQUIRY_HDR based on Sp_Enquiry_Grid parameters.
    company_id = models.IntegerField(db_column='CompId', default=1, verbose_name="Company ID") 

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'T_ENQUIRY_HDR'  # The actual table name in your database
        verbose_name = 'Customer Enquiry'
        verbose_name_plural = 'Customer Enquiries'

    def __str__(self):
        return f"Enquiry {self.enq_id} for {self.customer.customer_name}"

    # Fat Model: Business logic methods
    @classmethod
    def search_enquiries(cls, comp_id, fin_id, search_type, search_value):
        """
        Encapsulates the search logic previously handled by Sp_Enquiry_Grid.
        Filters enquiries based on company, financial year, and search criteria.
        """
        queryset = cls.objects.filter(company_id=comp_id, financial_year=fin_id)
        
        if search_type == '0':  # Customer Name search
            if search_value:
                # The autocomplete returns "CustomerName [CustomerId]", parse it to get ID
                if ' [' in search_value and search_value.endswith(']'):
                    parts = search_value.rsplit(' [', 1)
                    customer_id_part = parts[1][:-1] # Remove ']'
                    queryset = queryset.filter(customer__customer_id=customer_id_part)
                else:
                    # Fallback for partial name match if autocomplete value is not strictly followed
                    queryset = queryset.filter(customer__customer_name__icontains=search_value)
        elif search_type == '1':  # Enquiry No search
            if search_value:
                queryset = queryset.filter(enq_id=search_value)
        
        # Optimize query by selecting related customer data to avoid N+1 issues
        return queryset.select_related('customer').order_by('-system_date') # Order by date descending by default
```

#### 4.2 Forms

We will define a form specifically for the search functionality, as this page does not directly involve CRUD for enquiries. A placeholder form for general `Enquiry` CRUD is provided as per the prompt's template.

```python
# enquiries/forms.py
from django import forms
from .models import Enquiry, Customer

class EnquirySearchForm(forms.Form):
    """
    Form for handling customer enquiry search parameters.
    Mimics the ASP.NET DropDownList and TextBoxes.
    """
    SEARCH_CHOICES = [
        ('select', 'Select'), # Mimics the initial 'Select' option in ASP.NET
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
    ]
    
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-52 p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': "{% url 'enquiries:search_form_fields' %}",  # HTMX to update input fields
            'hx-target': '#search-inputs-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change' # Trigger on dropdown change
        }),
        initial='select',
        label="Search By"
    )
    
    customer_search_value = forms.CharField(
        max_length=350, 
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-96 p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 
            'placeholder': 'Search Customer Name',
            'autocomplete': 'off', # Disable browser autocomplete
            'hx-get': "{% url 'enquiries:customer_autocomplete' %}", # HTMX for autocomplete suggestions
            'hx-target': '#customer_search_value_autocomplete_results',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'keyup changed delay:300ms', # Fetch suggestions on keyup with debounce
            'hx-indicator': '#autocomplete-spinner' # Show loading indicator
        }),
        label="Customer Name"
    )
    
    enquiry_id_value = forms.CharField(
        max_length=150, 
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-72 p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 
            'placeholder': 'Search Enquiry No.'
        }),
        label="Enquiry No."
    )

    # Note: Initial visibility logic is handled by HTMX rendering of partial templates,
    # not directly within the form's __init__ for a cleaner separation.


# --- Placeholder for general Enquiry CRUD Form as requested by prompt template ---
class EnquiryForm(forms.ModelForm):
    """
    A ModelForm for creating/editing Enquiry objects.
    This would be used in a separate Enquiry management page, not directly on the print page.
    """
    class Meta:
        model = Enquiry
        # Exclude primary key 'enq_id' if it's auto-generated by the database/SP.
        # If it's manually entered, include it. Assuming it's often user-defined.
        fields = ['enq_id', 'financial_year', 'customer', 'system_date', 'employee_name', 'company_id']
        widgets = {
            'enq_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'system_date': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'enq_id': 'Enquiry No.',
            'financial_year': 'Fin. Year',
            'customer': 'Customer Name',
            'system_date': 'Generated Date',
            'employee_name': 'Generated By',
            'company_id': 'Company ID',
        }
    
    # Add custom validation methods here if needed
```

#### 4.3 Views

Views will be thin, primarily handling HTTP requests, delegating business logic to models, and rendering templates. We'll include both the specific list/search views and the general CRUD views as per the prompt's requirements for a complete migration plan.

```python
# enquiries/views.py
from django.views.generic import ListView, View
from django.views.generic.edit import CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect
from django.conf import settings # To get default COMP_ID, FIN_ID

from .models import Enquiry, Customer
from .forms import EnquirySearchForm, EnquiryForm # Import the general EnquiryForm

# Helper function to get user context (Company ID, Financial Year ID)
# In a real application, this would integrate with user authentication,
# session management, or a specific ERP context.
def get_user_context(request):
    """
    Retrieves the current user's company ID and financial year ID.
    Placeholder for actual session/user profile integration.
    """
    # For demonstration, using default values from settings or hardcoded.
    # Replace with logic to get from request.user, request.session, or similar.
    comp_id = getattr(settings, 'DEFAULT_COMPANY_ID', 1) 
    fin_id = getattr(settings, 'DEFAULT_FINANCIAL_YEAR_ID', 2023)
    
    # Example: If user profile has these fields
    # if request.user.is_authenticated and hasattr(request.user, 'profile'):
    #     comp_id = request.user.profile.company_id
    #     fin_id = request.user.profile.financial_year_id

    return {'comp_id': comp_id, 'fin_id': fin_id}

class EnquiryListView(ListView):
    """
    Main view for displaying the customer enquiry search page.
    Initializes the search form and sets up the container for the HTMX-loaded table.
    """
    model = Enquiry
    template_name = 'enquiries/enquiry/list.html'
    context_object_name = 'enquiries' # Not directly used for initial load, but consistent

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Initialize the search form with any existing GET parameters or default 'select'
        initial_search_by = self.request.GET.get('search_by', 'select')
        initial_customer_search_value = self.request.GET.get('customer_search_value', '')
        initial_enquiry_id_value = self.request.GET.get('enquiry_id_value', '')
        
        context['form'] = EnquirySearchForm(initial={
            'search_by': initial_search_by,
            'customer_search_value': initial_customer_search_value,
            'enquiry_id_value': initial_enquiry_id_value
        })
        
        # DataTables will load data via an HTMX request to EnquiryTablePartialView
        # So, the initial 'enquiries' context will be empty or a default limited set.
        context['initial_enquiries'] = [] # Data is loaded dynamically via HTMX
        return context

class EnquiryTablePartialView(View):
    """
    HTMX endpoint to render the customer enquiry table.
    This view is triggered by the search form submission or page load/refresh events.
    """
    def get(self, request, *args, **kwargs):
        user_context = get_user_context(request)
        comp_id = user_context['comp_id']
        fin_id = user_context['fin_id']

        search_by = request.GET.get('search_by', 'select')
        customer_search_value = request.GET.get('customer_search_value', '')
        enquiry_id_value = request.GET.get('enquiry_id_value', '')
        
        # Determine which search value to pass to the model's search method
        actual_search_value = customer_search_value if search_by == '0' else enquiry_id_value

        # Use the Fat Model to perform the search logic
        enquiries = Enquiry.search_enquiries(
            comp_id, fin_id, search_by, actual_search_value
        )
        
        context = {
            'enquiries': enquiries,
        }
        return render(request, 'enquiries/enquiry/_enquiry_table.html', context)

class SearchFormFieldsPartialView(View):
    """
    HTMX endpoint to dynamically update search input fields based on dropdown selection.
    This mimics the ASP.NET visibility toggling for txtEnqId and TxtSearchValue.
    """
    def get(self, request, *args, **kwargs):
        search_by = request.GET.get('search_by', 'select')
        form = EnquirySearchForm(initial={'search_by': search_by})
        # Render only the relevant input fields based on the selected choice
        return render(request, 'enquiries/enquiry/_search_inputs.html', {'form': form})

class CustomerAutocompleteView(View):
    """
    HTMX endpoint for providing customer name autocomplete suggestions.
    Mimics the AjaxControlToolkit AutoCompleteExtender.
    Returns HTML content for the suggestions list.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('customer_search_value', '')
        user_context = get_user_context(request)
        comp_id = user_context['comp_id']
        
        if not query:
            return HttpResponse("") # Return empty string if no query

        # Filter customers based on prefix and company ID (from session context)
        suggestions = Customer.objects.filter(
            customer_name__icontains=query,
            company_id=comp_id # Assuming Customer model has a company_id field for filtering
        ).values_list('customer_name', 'customer_id')[:10] # Limit suggestions to 10 as per ASP.NET

        # Format results as "CustomerName [CustomerId]" as in ASP.NET
        formatted_results = [f"{name} [{cid}]" for name, cid in suggestions]
        
        context = {'results': formatted_results}
        return render(request, 'enquiries/enquiry/_customer_autocomplete_results.html', context)

class EnquiryDetailRedirectView(View):
    """
    Handles the redirect to the customer enquiry detail page.
    This simulates the Response.Redirect logic from the ASP.NET GridView LinkButton.
    """
    def get(self, request, customer_id, enq_id, *args, **kwargs):
        messages.info(request, f"Navigating to details for Customer ID: {customer_id}, Enquiry ID: {enq_id}")
        # Replace 'enquiries:enquiry_detail_page' with the actual URL name for your
        # Django-based enquiry detail/print page, passing necessary parameters.
        return redirect(reverse_lazy('enquiries:enquiry_detail_page', kwargs={
            'customer_id': customer_id, 
            'enq_id': enq_id
            # You might pass other parameters like ModId, SubModId if they are still relevant in Django
        }))


# --- Placeholder CRUD Views for Enquiry as requested by prompt template ---
# (These would typically be on a separate management page, not this print page)

class EnquiryCreateView(CreateView):
    """
    View for adding a new customer enquiry.
    """
    model = Enquiry
    form_class = EnquiryForm
    template_name = 'enquiries/enquiry/form.html' # Generic form template
    success_url = reverse_lazy('enquiries:enquiry_list') # Redirect to list after success

    def form_valid(self, form):
        user_context = get_user_context(self.request)
        form.instance.company_id = user_context['comp_id'] # Set company_id from context
        # Additional logic if enq_id is generated or requires specific format
        response = super().form_valid(form)
        messages.success(self.request, 'Enquiry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success for HTMX
                headers={
                    'HX-Trigger': 'refreshEnquiryList' # Custom HTMX event to refresh the list
                }
            )
        return response

class EnquiryUpdateView(UpdateView):
    """
    View for editing an existing customer enquiry.
    """
    model = Enquiry
    form_class = EnquiryForm
    template_name = 'enquiries/enquiry/form.html'
    success_url = reverse_lazy('enquiries:enquiry_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Enquiry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEnquiryList'
                }
            )
        return response

class EnquiryDeleteView(DeleteView):
    """
    View for deleting a customer enquiry.
    """
    model = Enquiry
    template_name = 'enquiries/enquiry/confirm_delete.html'
    success_url = reverse_lazy('enquiries:enquiry_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Enquiry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEnquiryList'
                }
            )
        return response

# You would need to define 'enquiry_detail_page' view and template separately
# Example:
# class EnquiryDetailView(DetailView):
#     model = Enquiry
#     template_name = 'enquiries/enquiry/detail.html'
#     context_object_name = 'enquiry'
#     slug_field = 'enq_id' # If using enq_id as slug
#     slug_url_kwarg = 'enq_id'
```

#### 4.4 Templates

Templates will be designed for HTMX integration, using partials for dynamic updates and leveraging DataTables for the list view. They will extend a base template (assumed to be `core/base.html`).

**`enquiries/enquiry/list.html`** (Main page, extends base.html)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Enquiry - Print</h2>
        {# Add New button as per prompt's template, though not directly from source page #}
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'enquiries:enquiry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Enquiry
        </button>
    </div>

    {# Search Form Section #}
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Enquiries</h3>
        <form id="enquirySearchForm" 
              hx-get="{% url 'enquiries:enquiry_table' %}" 
              hx-target="#enquiryTable-container" 
              hx-swap="innerHTML" 
              hx-trigger="submit, change from:#id_search_by"> {# Trigger search on submit or dropdown change #}
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <label for="{{ form.search_by.id_for_label }}" class="font-medium text-gray-700 sr-only">Search By:</label>
                    {{ form.search_by }}
                </div>
                {# This container will be swapped by HTMX to show relevant input fields #}
                <div id="search-inputs-container">
                    {# Initial render of search inputs #}
                    {% include 'enquiries/enquiry/_search_inputs.html' with form=form %}
                </div>
                
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded shadow-md">
                    Search
                </button>
            </div>
        </form>
    </div>
    
    {# Enquiry Table Section #}
    <div id="enquiryTable-container"
         hx-trigger="load, refreshEnquiryList from:body" {# Loads on initial page load and refreshes on custom event #}
         hx-get="{% url 'enquiries:enquiry_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading customer enquiries...</p>
        </div>
    </div>
    
    {# Modal for CRUD operations (Add/Edit/Delete forms) #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0">
            <!-- Modal content loaded via HTMX here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here for client-side UI state management
        // For instance, managing modal open/close states, or complex form interactions not handled by HTMX.
        // Example: 
        // Alpine.data('modalHandler', () => ({
        //     isOpen: false,
        //     openModal() { this.isOpen = true },
        //     closeModal() { this.isOpen = false }
        // }));
    });
</script>
{# Include DataTables and JQuery #}
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
{% endblock %}
```

**`enquiries/enquiry/_search_inputs.html`** (HTMX partial for search fields)

```html
<div id="search-inputs-container" class="flex items-center space-x-4">
    {# Render the appropriate input field based on the selected search type #}
    {% if form.search_by.value == '0' %} {# Customer Name #}
        <div class="relative">
            {{ form.customer_search_value }}
            <div id="customer_search_value_autocomplete_results" 
                 class="absolute mt-1 w-full max-h-60 overflow-y-auto z-10">
                {# Autocomplete suggestions will be loaded here by HTMX #}
            </div>
            {# Optional: Autocomplete loading indicator #}
            <span id="autocomplete-spinner" class="htmx-indicator ml-2 absolute right-2 top-1/2 -translate-y-1/2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
            </span>
        </div>
    {% elif form.search_by.value == '1' %} {# Enquiry No #}
        {{ form.enquiry_id_value }}
    {% else %} {# 'Select' or default #}
        {# As per ASP.NET, txtEnqId is visible for 'Select' initially #}
        {{ form.enquiry_id_value }}
    {% endif %}
</div>
```

**`enquiries/enquiry/_customer_autocomplete_results.html`** (HTMX partial for autocomplete suggestions)

```html
{% if results %}
    <div class="border border-gray-300 bg-white shadow-lg rounded-md">
        {% for result in results %}
            <div class="px-4 py-2 hover:bg-blue-100 cursor-pointer text-gray-800 text-sm"
                 hx-on--click="document.getElementById('id_customer_search_value').value = '{{ result }}'; this.closest('#customer_search_value_autocomplete_results').innerHTML = '';">
                {{ result }}
            </div>
        {% endfor %}
    </div>
{% endif %}
```

**`enquiries/enquiry/_enquiry_table.html`** (HTMX partial for the DataTables content)

```html
<table id="enquiryTable" class="min-w-full bg-white yui-datatable-theme display compact stripe">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No.</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for enquiry in enquiries %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.financial_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">
                <button class="text-blue-600 hover:text-blue-800 underline font-medium"
                    hx-get="{% url 'enquiries:enquiry_detail_redirect' customer_id=enquiry.customer.customer_id enq_id=enquiry.enq_id %}"
                    hx-swap="none"
                    hx-indicator="#loading-spinner"> {# Assuming a global loading spinner #}
                    {{ enquiry.customer.customer_name }}
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ enquiry.customer.customer_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ enquiry.enq_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ enquiry.system_date|date:"d-M-Y H:i" }}</td> {# Format date #}
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ enquiry.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                {# Edit and Delete buttons for general CRUD functionality #}
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 text-sm rounded mr-2"
                    hx-get="{% url 'enquiries:enquiry_edit' enquiry.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 text-sm rounded"
                    hx-get="{% url 'enquiries:enquiry_delete' enquiry.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 border-b border-gray-200 text-center text-red-500 font-semibold text-lg">
                No data to display !
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Destroy any existing DataTable instance before re-initializing to prevent errors
    if ($.fn.DataTable.isDataTable('#enquiryTable')) {
        $('#enquiryTable').DataTable().destroy();
    }
    $('#enquiryTable').DataTable({
        "pageLength": 20, // Default page size, as per ASP.NET GridView
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]], // Options for items per page
        "ordering": true,  // Enable sorting
        "searching": true, // Enable client-side search box provided by DataTables
        "responsive": true // Make table responsive
    });
});
</script>
```

**`enquiries/enquiry/form.html`** (Partial template for Add/Edit forms, loaded into modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Enquiry</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" {# We don't swap content directly, rely on HX-Trigger #}
          hx-on--after-request="if(event.detail.successful) { removeElement('#modal'); sendHTMXRequest('body', 'refreshEnquiryList'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md">
                Save
            </button>
        </div>
    </form>
</div>
```

**`enquiries/enquiry/confirm_delete.html`** (Partial template for Delete confirmation, loaded into modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the enquiry with ID <strong>{{ object.enq_id }}</strong>?</p>
    
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-on--after-request="if(event.detail.successful) { removeElement('#modal'); sendHTMXRequest('body', 'refreshEnquiryList'); }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

URL patterns for accessing the views.

```python
# enquiries/urls.py
from django.urls import path
from .views import (
    EnquiryListView, EnquiryTablePartialView, SearchFormFieldsPartialView,
    CustomerAutocompleteView, EnquiryDetailRedirectView,
    EnquiryCreateView, EnquiryUpdateView, EnquiryDeleteView
)

app_name = 'enquiries' # Namespace for the app

urlpatterns = [
    # Main page for customer enquiry list and search
    path('customer-enquiry-print/', EnquiryListView.as_view(), name='enquiry_list'),
    
    # HTMX endpoints for dynamic updates
    path('customer-enquiry-print/table/', EnquiryTablePartialView.as_view(), name='enquiry_table'),
    path('customer-enquiry-print/search-fields/', SearchFormFieldsPartialView.as_view(), name='search_form_fields'),
    path('customer-enquiry-print/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    
    # Endpoint for redirecting to detail page (from LinkButton in GridView)
    # The actual detail page URL would need to be defined elsewhere (e.g., a separate 'enquiry_detail_page' view)
    path('customer-enquiry-print/details/<str:customer_id>/<str:enq_id>/', 
         EnquiryDetailRedirectView.as_view(), name='enquiry_detail_redirect'),

    # --- Placeholder CRUD Routes for Enquiry (as requested by prompt template) ---
    # These would typically be on a separate enquiry management page.
    path('enquiry/add/', EnquiryCreateView.as_view(), name='enquiry_add'),
    path('enquiry/edit/<str:pk>/', EnquiryUpdateView.as_view(), name='enquiry_edit'), # PK is string (EnqId)
    path('enquiry/delete/<str:pk>/', EnquiryDeleteView.as_view(), name='enquiry_delete'),
    
    # Placeholder for the actual detail page the redirect goes to
    # path('enquiry/detail/<str:customer_id>/<str:enq_id>/', EnquiryDetailView.as_view(), name='enquiry_detail_page'),
]
```

#### 4.6 Tests

Comprehensive tests for models and views ensure functionality and maintainability.

```python
# enquiries/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import Customer, Enquiry
from unittest.mock import patch # For mocking session/settings in views

# Define default context for tests, mimicking session data
DEFAULT_COMP_ID = 1
DEFAULT_FIN_ID = 2023

# Mock the get_user_context to return consistent values for tests
# This ensures tests are isolated from actual session/user logic
mock_user_context = {'comp_id': DEFAULT_COMP_ID, 'fin_id': DEFAULT_FIN_ID}

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test customer data
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Alpha Customer',
            company_id=DEFAULT_COMP_ID
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002',
            customer_name='Beta Corp',
            company_id=DEFAULT_COMP_ID + 1 # Different company
        )
        cls.customer3 = Customer.objects.create(
            customer_id='CUST003',
            customer_name='Gamma Industries',
            company_id=DEFAULT_COMP_ID
        )

    def test_customer_creation(self):
        """Test that a customer can be created and properties are correct."""
        self.assertEqual(self.customer1.customer_id, 'CUST001')
        self.assertEqual(self.customer1.customer_name, 'Alpha Customer')
        self.assertEqual(self.customer1.company_id, DEFAULT_COMP_ID)
        self.assertEqual(str(self.customer1), 'Alpha Customer [CUST001]')

    def test_customer_verbose_names(self):
        """Test verbose names for model and fields."""
        self.assertEqual(Customer._meta.verbose_name, 'Customer')
        self.assertEqual(Customer._meta.verbose_name_plural, 'Customers')
        self.assertEqual(Customer._meta.get_field('customer_id').verbose_name, 'Customer Code')
        self.assertEqual(Customer._meta.get_field('customer_name').verbose_name, 'Customer Name')

class EnquiryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test customer and enquiry data
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Test Customer One',
            company_id=DEFAULT_COMP_ID
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002',
            customer_name='Another Customer',
            company_id=DEFAULT_COMP_ID
        )
        cls.enquiry1 = Enquiry.objects.create(
            enq_id='ENQ001',
            financial_year=DEFAULT_FIN_ID,
            customer=cls.customer1,
            system_date='2023-01-15T10:00:00',
            employee_name='John Doe',
            company_id=DEFAULT_COMP_ID
        )
        cls.enquiry2 = Enquiry.objects.create(
            enq_id='ENQ002',
            financial_year=DEFAULT_FIN_ID,
            customer=cls.customer2,
            system_date='2023-02-20T11:30:00',
            employee_name='Jane Smith',
            company_id=DEFAULT_COMP_ID
        )
        cls.enquiry3 = Enquiry.objects.create(
            enq_id='ENQ003',
            financial_year=DEFAULT_FIN_ID + 1, # Different financial year
            customer=cls.customer1,
            system_date='2024-03-01T14:00:00',
            employee_name='John Doe',
            company_id=DEFAULT_COMP_ID
        )
        cls.enquiry4 = Enquiry.objects.create(
            enq_id='ENQ004',
            financial_year=DEFAULT_FIN_ID,
            customer=cls.customer1,
            system_date='2023-01-16T10:00:00', # Different date
            employee_name='John Doe',
            company_id=DEFAULT_COMP_ID + 1 # Different company
        )


    def test_enquiry_creation(self):
        """Test that an enquiry can be created and properties are correct."""
        self.assertEqual(self.enquiry1.enq_id, 'ENQ001')
        self.assertEqual(self.enquiry1.customer.customer_name, 'Test Customer One')
        self.assertEqual(self.enquiry1.employee_name, 'John Doe')
        self.assertEqual(self.enquiry1.company_id, DEFAULT_COMP_ID)
        self.assertEqual(str(self.enquiry1), 'Enquiry ENQ001 for Test Customer One')

    def test_enquiry_verbose_names(self):
        """Test verbose names for model and fields."""
        self.assertEqual(Enquiry._meta.verbose_name, 'Customer Enquiry')
        self.assertEqual(Enquiry._meta.verbose_name_plural, 'Customer Enquiries')
        self.assertEqual(Enquiry._meta.get_field('enq_id').verbose_name, 'Enquiry No.')
        self.assertEqual(Enquiry._meta.get_field('customer').verbose_name, 'Customer')

    def test_enquiry_search_by_customer_id_from_autocomplete_format(self):
        """Test searching by customer name using the 'Name [ID]' format from autocomplete."""
        with patch('enquiries.views.get_user_context', return_value=mock_user_context):
            results = Enquiry.search_enquiries(
                DEFAULT_COMP_ID, DEFAULT_FIN_ID, '0', 'Test Customer One [CUST001]'
            )
            self.assertEqual(results.count(), 1)
            self.assertEqual(results.first().enq_id, 'ENQ001')

    def test_enquiry_search_by_partial_customer_name(self):
        """Test searching by partial customer name (fallback if ID not found)."""
        with patch('enquiries.views.get_user_context', return_value=mock_user_context):
            results = Enquiry.search_enquiries(
                DEFAULT_COMP_ID, DEFAULT_FIN_ID, '0', 'Another'
            )
            self.assertEqual(results.count(), 1)
            self.assertEqual(results.first().enq_id, 'ENQ002')

    def test_enquiry_search_by_enquiry_id(self):
        """Test searching by enquiry ID."""
        with patch('enquiries.views.get_user_context', return_value=mock_user_context):
            results = Enquiry.search_enquiries(
                DEFAULT_COMP_ID, DEFAULT_FIN_ID, '1', 'ENQ002'
            )
            self.assertEqual(results.count(), 1)
            self.assertEqual(results.first().enq_id, 'ENQ002')

    def test_enquiry_search_no_match(self):
        """Test search when no matching enquiries are found."""
        with patch('enquiries.views.get_user_context', return_value=mock_user_context):
            results = Enquiry.search_enquiries(
                DEFAULT_COMP_ID, DEFAULT_FIN_ID, '1', 'NONEXISTENT'
            )
            self.assertEqual(results.count(), 0)

    def test_enquiry_search_different_financial_year(self):
        """Test search with a different financial year context."""
        with patch('enquiries.views.get_user_context', return_value={'comp_id': DEFAULT_COMP_ID, 'fin_id': DEFAULT_FIN_ID + 1}):
            results = Enquiry.search_enquiries(
                DEFAULT_COMP_ID, DEFAULT_FIN_ID + 1, '0', 'Test Customer One [CUST001]'
            )
            self.assertEqual(results.count(), 1)
            self.assertEqual(results.first().enq_id, 'ENQ003')

    def test_enquiry_search_different_company_id(self):
        """Test search with a different company ID context."""
        # Enquiry 4 belongs to DEFAULT_COMP_ID + 1
        results = Enquiry.search_enquiries(
            DEFAULT_COMP_ID + 1, DEFAULT_FIN_ID, '0', 'Test Customer One [CUST001]'
        )
        self.assertEqual(results.count(), 0) # Should not find ENQ001, which is for DEFAULT_COMP_ID
        
        # Test finding ENQ004 specifically with its company ID
        with patch('enquiries.views.get_user_context', return_value={'comp_id': DEFAULT_COMP_ID + 1, 'fin_id': DEFAULT_FIN_ID}):
            results_for_comp2 = Enquiry.search_enquiries(
                DEFAULT_COMP_ID + 1, DEFAULT_FIN_ID, '0', 'Test Customer One [CUST001]'
            )
            self.assertEqual(results_for_comp2.count(), 1)
            self.assertEqual(results_for_comp2.first().enq_id, 'ENQ004')


class EnquiryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.customer = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Example Customer',
            company_id=DEFAULT_COMP_ID
        )
        self.enquiry = Enquiry.objects.create(
            enq_id='ENQ001',
            financial_year=DEFAULT_FIN_ID,
            customer=self.customer,
            system_date='2023-01-15T10:00:00',
            employee_name='Test User',
            company_id=DEFAULT_COMP_ID
        )
        # Mock get_user_context for all view tests
        self.get_user_context_patcher = patch('enquiries.views.get_user_context', return_value=mock_user_context)
        self.mock_get_user_context = self.get_user_context_patcher.start()

    def tearDown(self):
        self.get_user_context_patcher.stop()

    def test_enquiry_list_view_get(self):
        """Test the main enquiry list page loads correctly."""
        response = self.client.get(reverse('enquiries:enquiry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/list.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Customer Enquiry - Print')

    def test_enquiry_table_partial_view_get_initial(self):
        """Test the HTMX endpoint for the table content on initial load."""
        response = self.client.get(reverse('enquiries:enquiry_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/_enquiry_table.html')
        self.assertContains(response, self.enquiry.customer.customer_name)
        self.assertContains(response, self.enquiry.enq_id)

    def test_enquiry_table_partial_view_search_customer_name(self):
        """Test searching by customer name via HTMX request."""
        response = self.client.get(reverse('enquiries:enquiry_table'), {
            'search_by': '0',
            'customer_search_value': 'Example Customer [CUST001]'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/_enquiry_table.html')
        self.assertContains(response, self.enquiry.customer.customer_name)
        self.assertContains(response, self.enquiry.enq_id)
        self.assertContains(response, 'data-dt-idx="1"') # Check DataTables init

    def test_enquiry_table_partial_view_search_enquiry_id(self):
        """Test searching by enquiry ID via HTMX request."""
        response = self.client.get(reverse('enquiries:enquiry_table'), {
            'search_by': '1',
            'enquiry_id_value': 'ENQ001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/_enquiry_table.html')
        self.assertContains(response, self.enquiry.customer.customer_name)
        self.assertContains(response, self.enquiry.enq_id)

    def test_search_form_fields_partial_view_customer_name(self):
        """Test dynamic update of search fields for customer name."""
        response = self.client.get(reverse('enquiries:search_form_fields'), {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/_search_inputs.html')
        self.assertContains(response, 'id="id_customer_search_value"')
        self.assertNotContains(response, 'id="id_enquiry_id_value"')

    def test_search_form_fields_partial_view_enquiry_id(self):
        """Test dynamic update of search fields for enquiry ID."""
        response = self.client.get(reverse('enquiries:search_form_fields'), {'search_by': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/_search_inputs.html')
        self.assertNotContains(response, 'id="id_customer_search_value"')
        self.assertContains(response, 'id="id_enquiry_id_value"')

    def test_customer_autocomplete_view_with_query(self):
        """Test autocomplete suggestions with a matching query."""
        response = self.client.get(reverse('enquiries:customer_autocomplete'), {'customer_search_value': 'Example'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/_customer_autocomplete_results.html')
        self.assertContains(response, 'Example Customer [CUST001]')

    def test_customer_autocomplete_view_no_query(self):
        """Test autocomplete with no query string."""
        response = self.client.get(reverse('enquiries:customer_autocomplete'), {'customer_search_value': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "") # Should return empty string

    def test_customer_autocomplete_view_no_results(self):
        """Test autocomplete when no matches are found."""
        response = self.client.get(reverse('enquiries:customer_autocomplete'), {'customer_search_value': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/_customer_autocomplete_results.html')
        self.assertNotContains(response, 'div class="px-4 py-2"') # Check if no suggestion divs are rendered

    def test_enquiry_detail_redirect_view(self):
        """Test the redirection logic for enquiry details."""
        response = self.client.get(reverse('enquiries:enquiry_detail_redirect', 
                                            kwargs={'customer_id': self.customer.customer_id, 'enq_id': self.enquiry.enq_id}))
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertIn('/enquiry/detail/', response.url) # Check if redirecting to the placeholder URL
        self.assertTrue('messages' in self.client.session) # Check if message was set in session

    # --- Integration Tests for Dummy CRUD Views (as per prompt template) ---

    def test_enquiry_create_view_get(self):
        """Test GET request for the Create Enquiry form."""
        response = self.client.get(reverse('enquiries:enquiry_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/form.html')
        self.assertContains(response, 'Add Customer Enquiry')

    def test_enquiry_create_view_post_htmx_success(self):
        """Test successful HTMX POST request for creating an enquiry."""
        new_customer = Customer.objects.create(customer_id='CUST003', customer_name='New Customer', company_id=DEFAULT_COMP_ID)
        data = {
            'enq_id': 'NEWENQ', # Providing PK as it's not auto-generated in model
            'financial_year': DEFAULT_FIN_ID,
            'customer': new_customer.customer_id, # Pass FK value
            'system_date': '2023-04-01T10:00:00',
            'employee_name': 'New Employee',
            'company_id': DEFAULT_COMP_ID,
        }
        response = self.client.post(reverse('enquiries:enquiry_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success for non-swap
        self.assertTrue(Enquiry.objects.filter(enq_id='NEWENQ').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertRegex(response.headers['HX-Trigger'], r'refreshEnquiryList')
        self.assertFalse(response.content) # No content for 204

    def test_enquiry_update_view_get(self):
        """Test GET request for the Update Enquiry form."""
        response = self.client.get(reverse('enquiries:enquiry_edit', args=[self.enquiry.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/form.html')
        self.assertContains(response, 'Edit Customer Enquiry')
        self.assertContains(response, self.enquiry.employee_name) # Check if form pre-fills

    def test_enquiry_update_view_post_htmx_success(self):
        """Test successful HTMX POST request for updating an enquiry."""
        updated_employee_name = 'Updated Employee Name'
        data = {
            'enq_id': self.enquiry.enq_id,
            'financial_year': self.enquiry.financial_year,
            'customer': self.enquiry.customer.customer_id,
            'system_date': self.enquiry.system_date.isoformat(), # Convert datetime to ISO string
            'employee_name': updated_employee_name,
            'company_id': self.enquiry.company_id,
        }
        response = self.client.post(reverse('enquiries:enquiry_edit', args=[self.enquiry.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.enquiry.refresh_from_db() # Reload instance from DB
        self.assertEqual(self.enquiry.employee_name, updated_employee_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertRegex(response.headers['HX-Trigger'], r'refreshEnquiryList')

    def test_enquiry_delete_view_get(self):
        """Test GET request for the Delete Enquiry confirmation."""
        response = self.client.get(reverse('enquiries:enquiry_delete', args=[self.enquiry.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'enquiries/enquiry/confirm_delete.html')
        self.assertContains(response, f'delete the enquiry with ID <strong>{self.enquiry.enq_id}</strong>?')

    def test_enquiry_delete_view_post_htmx_success(self):
        """Test successful HTMX POST request for deleting an enquiry."""
        response = self.client.post(reverse('enquiries:enquiry_delete', args=[self.enquiry.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Enquiry.objects.filter(pk=self.enquiry.pk).exists()) # Verify deletion
        self.assertIn('HX-Trigger', response.headers)
        self.assertRegex(response.headers['HX-Trigger'], r'refreshEnquiryList')
```

### Step 5: HTMX and Alpine.js Integration

The provided code demonstrates the core principles:

*   **HTMX for Dynamic Updates:**
    *   `hx-get` and `hx-target` are used for loading the initial table, refreshing it after search, and updating search input fields when the dropdown changes.
    *   `hx-trigger="keyup changed delay:300ms"` is used for the autocomplete input, triggering requests after a delay to reduce server load.
    *   Modal forms (Add/Edit/Delete) are loaded via `hx-get` into `#modalContent`.
    *   Form submissions (`hx-post`) on modal forms use `hx-swap="none"` and `hx-on--after-request` to close the modal and trigger a `refreshEnquiryList` event on the body. This event is then caught by the main table container to reload itself.
    *   The "Customer Name" link in the table uses `hx-get` to trigger a redirect to the detail page.
    *   `hx-indicator` is used to show loading spinners for dynamic content.

*   **Alpine.js for UI State Management:**
    *   A generic `alpine:init` block is included in `base.html` for future Alpine.js components. For this migration, HTMX handles most of the dynamic UI, but Alpine.js would be ideal for more complex client-side state, like managing modal open/close states, form validation feedback, or tabbed interfaces if needed.

*   **DataTables for List Views:**
    *   The `_enquiry_table.html` partial includes the necessary `script` tag to initialize DataTables on the `enquiryTable` ID after the HTMX swap.
    *   This provides client-side searching, sorting, and pagination without requiring server-side pagination logic.

*   **DRY Template Inheritance:**
    *   All templates explicitly extend `core/base.html` (`{% extends 'core/base.html' %}`).
    *   CDN links for jQuery, DataTables, and Tailwind CSS (which should be configured in `base.html` or through a build process) are assumed to be present in `base.html` or dynamically included for DataTables.

## Final Notes

This comprehensive plan transforms your ASP.NET Customer Enquiry application into a modern, maintainable Django solution. By focusing on automated migration strategies, leveraging HTMX for dynamic interactions, and adhering to Django best practices, we aim to deliver a robust and scalable system that meets your business needs effectively. Remember to configure your Django `settings.py` for database connection, `DEFAULT_COMPANY_ID`, and `DEFAULT_FINANCIAL_YEAR_ID`. The `urls.py` of your main Django project will also need to include `path('enquiries/', include('enquiries.urls'))`.