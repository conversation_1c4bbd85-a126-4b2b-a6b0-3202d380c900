## ASP.NET to Django Conversion Script: Quotation Print Details

This modernization plan outlines the conversion of your ASP.NET Quotation Print Details page to a modern Django application. Our focus is on delivering a robust, maintainable, and highly performant solution using Django's best practices, emphasizing automation and a clear separation of concerns.

The original ASP.NET page served primarily as a display mechanism for a Crystal Report, providing a "print view" of a quotation. Our Django equivalent will render this report directly as an HTML page, optimized for printing, and leverage Django's powerful ORM for data retrieval and business logic within the models.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (Note: For this specific report view, DataTables is not applicable as it's a single detailed report, not a list of items).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns accessed by the ASP.NET code.

**Instructions:**
The ASP.NET code utilizes several SQL queries to fetch data for the report. Based on these queries, the following tables and relevant columns have been identified:

-   **`SD_Cust_Quotation_Master`**: This is the primary table for quotation details.
    -   `Id` (used as a unique identifier for the quotation record)
    -   `QuotationNo`
    -   `EnqId`
    -   `CompId`
    -   `CustomerId` (Foreign Key to `SD_Cust_master`)
    -   `SysDate` (System Date, likely the quotation date)
    -   `DueDate`
    -   `CheckedBy` (Foreign Key to `tblHR_OfficeStaff`)
    -   `ApprovedBy` (Foreign Key to `tblHR_OfficeStaff`)
    -   `AuthorizedBy` (Foreign Key to `tblHR_OfficeStaff`)

-   **`SD_Cust_master`**: Contains customer registration details.
    -   `CustomerId` (Primary Key / Unique identifier for customer)
    -   `CompId`
    -   `RegdAddress`
    -   `RegdPinNo`
    -   `RegdCity` (Foreign Key to `tblCity`)
    -   `RegdState` (Foreign Key to `tblState`)
    -   `RegdCountry` (Foreign Key to `tblCountry`)

-   **`tblCity`**: Stores city names.
    -   `CityId`
    -   `CityName`

-   **`tblState`**: Stores state names.
    -   `SId`
    -   `StateName`

-   **`tblCountry`**: Stores country names.
    -   `CId`
    -   `CountryName`

-   **`tblHR_OfficeStaff`**: Contains employee details for "checked by," "approved by," and "authorized by" fields.
    -   `EmpId`
    -   `CompId`
    -   `Title` (e.g., Mr., Ms., Dr.)
    -   `EmployeeName`

-   **Inferred `tblCompany`**: The `fun.getCompany(CompId)` and `fun.CompAdd(CompId)` functions suggest a `tblCompany` table exists to retrieve company name and address based on `CompId`.
    -   `CompId`
    -   `CompanyName`
    -   `Address`

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations of the ASP.NET page.

**Instructions:**
The `Quotation_Print_Details.aspx` page primarily performs a "Read" operation.

-   **Read:** The page fetches detailed quotation information, customer details, and employee names from multiple related tables. It then aggregates and formats this data for display as a report. There are no direct "Create," "Update," or "Delete" operations performed *on this page*.
-   **Navigation:** The "Cancel" button redirects the user to different pages (`Quotation_Check.aspx`, `Quotation_Print.aspx`, etc.) based on a `parentpage` query string parameter. This indicates a return-to-workflow functionality.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
-   **`CR:CrystalReportViewer`**: This control is used to render the actual Crystal Report. In Django, this will be replaced by a standard HTML template that dynamically renders the fetched data in a structured, print-friendly format using Tailwind CSS.
-   **`asp:Button ID="Button1" Text="Cancel"`**: This button triggers a server-side event (`Button1_Click`) for navigation. In Django, this will be a simple HTML `<a>` tag or `<button>` that leverages either client-side `window.history.back()` or a Django URL redirect based on the `parentpage` parameter.

### Step 4: Generate Django Code

#### 4.1 Models

**Task:** Create Django models mapping to the identified database schema. These models will encapsulate the business logic required to retrieve and format the report data (Fat Model approach).

**File:** `Module_SalesDistribution/models.py`

```python
from django.db import models
from django.utils import timezone # Used for date/time fields if needed for defaults or validation

# Assuming a Company table for 'fun.getCompany' and 'fun.CompAdd'
class Company(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblCompany' # Name of the existing database table
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name or f"Company {self.comp_id}"

    @classmethod
    def get_company_details(cls, comp_id):
        """Fetches company name and address by CompId for report headers."""
        try:
            company = cls.objects.get(comp_id=comp_id)
            return company.company_name, company.address
        except cls.DoesNotExist:
            return "N/A Company", "N/A Address"

class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class State(models.Model):
    state_id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class Country(models.Model):
    country_id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    regd_address = models.TextField(db_column='RegdAddress', blank=True, null=True)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20, blank=True, null=True)
    regd_city = models.IntegerField(db_column='RegdCity', blank=True, null=True)
    regd_state = models.IntegerField(db_column='RegdState', blank=True, null=True)
    regd_country = models.IntegerField(db_column='RegdCountry', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_id

    def get_full_address(self):
        """Constructs the full registered address by combining address, city, state, country, and pin."""
        city = City.objects.filter(city_id=self.regd_city).first()
        state = State.objects.filter(state_id=self.regd_state).first()
        country = Country.objects.filter(country_id=self.regd_country).first()

        parts = [self.regd_address]
        if city and city.city_name:
            parts.append(city.city_name)
        if state and state.state_name:
            parts.append(state.state_name)
        if country and country.country_name:
            parts.append(country.country_name)
        if self.regd_pin_no:
            parts.append(self.regd_pin_no)

        return ",\n".join(filter(None, parts)) + "\n" # Adding newline at end as in original ASP.NET

class Employee(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        # Replicates "Title+'. '+EmployeeName As EmpName" from ASP.NET
        full_name = self.employee_name if self.employee_name else ""
        return f"{self.title}. {full_name}".strip() if self.title else full_name.strip()

class Quotation(models.Model):
    # 'Id' from Request.QueryString["Id"] is the primary key in the database
    id = models.IntegerField(db_column='Id', primary_key=True)
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True) # Matches string type from strenqId
    comp_id = models.IntegerField(db_column='CompId')
    customer_id = models.CharField(db_column='CustomerId', max_length=50)
    sys_date = models.DateTimeField(db_column='SysDate')
    due_date = models.DateTimeField(db_column='DueDate')
    checked_by = models.IntegerField(db_column='CheckedBy', blank=True, null=True)
    approved_by = models.IntegerField(db_column='ApprovedBy', blank=True, null=True)
    authorized_by = models.IntegerField(db_column='AuthorizedBy', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'
        # Composite unique constraint reflecting the primary lookup in ASP.NET code
        unique_together = (('quotation_no', 'enq_id', 'comp_id', 'id'),)

    def __str__(self):
        return f"Quotation {self.quotation_no} (ID: {self.id})"

    def get_customer_obj(self):
        """Returns the related Customer object."""
        return Customer.objects.filter(customer_id=self.customer_id, comp_id=self.comp_id).first()

    def get_employee_by_id(self, emp_id):
        """Helper to get an employee by ID and company ID."""
        if emp_id:
            return Employee.objects.filter(emp_id=emp_id, comp_id=self.comp_id).first()
        return None

    def get_report_data(self):
        """
        Aggregates all necessary data for the quotation report,
        mimicking the data preparation done in the ASP.NET code-behind.
        This method embodies the 'Fat Model' principle.
        """
        customer = self.get_customer_obj()
        checked_by_emp = self.get_employee_by_id(self.checked_by)
        approved_by_emp = self.get_employee_by_id(self.approved_by)
        authorized_by_emp = self.get_employee_by_id(self.authorized_by)

        company_name, company_address = Company.get_company_details(self.comp_id)

        return {
            'quotation_no': self.quotation_no,
            'quotation_date': self.sys_date.strftime("%d-%m-%Y") if self.sys_date else "",
            'due_date': self.due_date.strftime("%d-%m-%Y") if self.due_date else "",
            'customer_id': self.customer_id,
            'customer_address': customer.get_full_address() if customer else "N/A Address",
            'checked_by': str(checked_by_emp) if checked_by_emp else "",
            'approved_by': str(approved_by_emp) if approved_by_emp else "",
            'authorized_by': str(authorized_by_emp) if authorized_by_emp else "",
            'company_name': company_name,
            'company_address': company_address,
            # Add any other fields from quotation master or its related tables needed for report
        }

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
Since this page is a report display and does not involve user input for creating or updating records, a `forms.py` file is not strictly necessary for this specific functionality. If this were a general CRUD module for quotations, `QuotationForm` would be defined here. For this print view, we skip the `forms.py` file.

#### 4.3 Views

**Task:** Implement the report display using a Class-Based View.

**Instructions:**
-   A `TemplateView` is suitable here as it primarily displays information and does not interact with forms.
-   The view will fetch necessary parameters from the URL query string, just like the ASP.NET page.
-   It will call the `get_report_data` method on the `Quotation` model to retrieve all aggregated data, adhering to the "thin view" principle.

**File:** `Module_SalesDistribution/views.py`

```python
from django.views.generic import TemplateView
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.contrib import messages
# Import all models used in the context of the view for clear dependency
from .models import Quotation, Company, Customer, City, State, Country, Employee


class QuotationPrintView(TemplateView):
    model = Quotation # Although TemplateView, good practice to define the main model if focused
    template_name = 'sales_distribution/quotation/quotation_print_details.html'

    def get_context_data(self, **kwargs):
        """
        Prepares the context data for the template, fetching and processing
        the quotation details for the print report.
        """
        context = super().get_context_data(**kwargs)

        # Extract parameters from query string, mirroring ASP.NET Request.QueryString
        quotation_no = self.request.GET.get('QuotationNo')
        enq_id = self.request.GET.get('EnqId')
        quotation_id_str = self.request.GET.get('Id') # This is the 'Id' column from DB
        parent_page = self.request.GET.get('parentpage')

        # Inferred CompId from ASP.NET Session. In a real Django app, this would be from
        # authenticated user's profile or a specific URL parameter.
        comp_id = self.request.session.get('compid', 1) # Defaulting to 1 for demonstration

        try:
            # Convert Id to integer as per the database type
            quotation_id = int(quotation_id_str) if quotation_id_str else None
            
            # Fetch the Quotation object using all parameters for robustness, mirroring original query
            quotation = get_object_or_404(
                Quotation,
                quotation_no=quotation_no,
                enq_id=enq_id,
                id=quotation_id,
                comp_id=comp_id
            )
            
            # Call the model's method to get all report data (Fat Model)
            context['report_data'] = quotation.get_report_data()
            context['quotation'] = quotation # Optionally pass the object itself for direct access
        except (ValueError, TypeError, Quotation.DoesNotExist) as e:
            # Handle cases where parameters are missing/invalid or quotation not found
            messages.error(self.request, f"Could not load quotation details. Error: {e}")
            context['report_data'] = None # Indicate that data is not available
            # Log the error for debugging: logger.error(f"Error in QuotationPrintView: {e}")

        context['parent_page'] = parent_page # Used for "Cancel" button redirection logic
        return context

    # No POST method is needed for this view as it's purely for display and printing.
    # The "Cancel" button's redirection is handled client-side or via a simple GET link.

```

#### 4.4 Templates

**Task:** Create a template for the report display, replacing the Crystal Report Viewer.

**Instructions:**
-   The template will extend `core/base.html`.
-   It will present the quotation details in a structured, print-friendly layout using Tailwind CSS.
-   It includes a "Print Report" button and a "Cancel" button. The cancel button will attempt to go back to the previous page or a default home page, mimicking the original `Response.Redirect` behavior based on context.

**File:** `Module_SalesDistribution/templates/sales_distribution/quotation/quotation_print_details.html`

```html
{% extends 'core/base.html' %}

{% block title %}Quotation Print Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-8 print:p-0 print:shadow-none print:rounded-none">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-4 print:hidden">Quotation - Print Preview</h2>
        
        {% if report_data %}
        <div class="quotation-report-header mb-8 text-center">
            <h1 class="text-3xl font-extrabold text-blue-700">{{ report_data.company_name }}</h1>
            <p class="text-gray-600 text-sm">{{ report_data.company_address|linebreaksbr }}</p>
            <h2 class="text-2xl font-semibold mt-4 text-blue-600">SALES QUOTATION</h2>
        </div>

        <div class="grid grid-cols-2 gap-4 text-sm mb-8">
            <div>
                <p><strong class="text-gray-700">Quotation No:</strong> {{ report_data.quotation_no }}</p>
                <p><strong class="text-gray-700">Quotation Date:</strong> {{ report_data.quotation_date }}</p>
                <p><strong class="text-gray-700">Due Date:</strong> {{ report_data.due_date }}</p>
            </div>
            <div class="text-right">
                <p><strong class="text-gray-700">Customer ID:</strong> {{ report_data.customer_id }}</p>
                <p class="mt-2"><strong class="text-gray-700">Address:</strong></p>
                <p class="whitespace-pre-line">{{ report_data.customer_address }}</p>
            </div>
        </div>

        <div class="quotation-details border-t border-b py-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">Quotation Items (Example Placeholder for Line Items)</h3>
            <!-- In a full implementation, you would query an SD_Cust_Quotation_Detail table here -->
            <!-- and loop through its items, similar to how the master data is handled. -->
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No.</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Example hardcoded rows - replace with loop over actual line items -->
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Product A - Feature 1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$50.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$250.00</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Service B - Phase 2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$750.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$750.00</td>
                    </tr>
                    <!-- End example -->
                </tbody>
            </table>
            <div class="text-right mt-4 font-bold text-lg text-gray-800">
                Total: $1000.00 <!-- This would be dynamically calculated from line items -->
            </div>
        </div>

        <div class="grid grid-cols-3 gap-4 text-sm text-gray-700 mb-8">
            <div>
                <p><strong class="font-semibold">Checked By:</strong> {{ report_data.checked_by }}</p>
            </div>
            <div>
                <p><strong class="font-semibold">Approved By:</strong> {{ report_data.approved_by }}</p>
            </div>
            <div>
                <p><strong class="font-semibold">Authorized By:</strong> {{ report_data.authorized_by }}</p>
            </div>
        </div>

        <div class="mt-8 flex justify-center space-x-4 print:hidden">
            <button onclick="window.print()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Print Report
            </button>
            <!-- Mimics ASP.NET's Response.Redirect for Cancel. Using JS history back for simplicity, or a specific URL -->
            <a href="{{ request.META.HTTP_REFERER|default:'/' }}" 
               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Cancel
            </a>
            <!-- If HTMX-driven redirection is needed for specific parent pages, a hidden input with parent_page
                 and an hx-post or hx-get to a dedicated view for redirection could be used:
            <button hx-get="{% url 'sales_distribution:quotation_return_to_parent' %}?parent_page={{ parent_page }}"
                    hx-swap="outerHTML" hx-target="body" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Cancel (HTMX)
            </button>
            -->
        </div>
        {% else %}
        <div class="text-center text-red-600 font-semibold text-lg">
            <p>Quotation details could not be loaded. Please ensure all required parameters are provided and valid.</p>
            <p class="mt-4">
                <a href="{{ request.META.HTTP_REFERER|default:'/' }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Go Back
                </a>
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // No specific Alpine.js or HTMX interactions are strictly needed for a static print view,
    // but this block is available for any future client-side enhancements.
    document.addEventListener('alpine:init', () => {
        // Alpine.js components would be defined here if any interactive elements were added
        // beyond basic print/cancel.
    });
</script>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the quotation print view.

**Instructions:**
-   A single URL path is needed for the `QuotationPrintView`.
-   The parameters `QuotationNo`, `EnqId`, `Id`, and `parentpage` will be passed as query string parameters, mimicking the original ASP.NET behavior.

**File:** `Module_SalesDistribution/urls.py`

```python
from django.urls import path
from .views import QuotationPrintView

app_name = 'sales_distribution' # Define app_name for namespacing URLs within the project

urlpatterns = [
    # URL for displaying the quotation print details.
    # Parameters for QuotationNo, EnqId, Id are expected as query parameters (e.g., /sales/quotation_print_details/?QuotationNo=Q001&EnqId=E001&Id=1).
    path('quotation_print_details/', QuotationPrintView.as_view(), name='quotation_print_details'),

    # If a dedicated view for handling the 'Cancel' button's complex redirection logic were needed,
    # it would be defined here. For simple navigation, the template's 'a' tag is sufficient.
    # Example (if using HTMX for redirection based on parent_page):
    # path('quotation_return_to_parent/', views.quotation_return_to_parent_view, name='quotation_return_to_parent'),
]

# Note: Remember to include this app's URLs in your project's main urls.py:
# from django.urls import include, path
# urlpatterns = [
#     path('sales/', include('Module_SalesDistribution.urls', namespace='sales_distribution')),
#     # ... other project URLs
# ]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
-   Model tests will verify the correctness of data retrieval methods and data aggregation (`get_report_data`, `get_full_address`).
-   View tests will ensure the `QuotationPrintView` loads correctly with valid parameters and handles missing/invalid parameters gracefully (e.g., returning 404).

**File:** `Module_SalesDistribution/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Quotation, Customer, City, State, Country, Employee, Company

class QuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal necessary data for related models to support Quotation model tests
        cls.company = Company.objects.create(comp_id=1, company_name="Test Company Inc.", address="123 Corporate Ave")
        cls.city = City.objects.create(city_id=101, city_name="Metropolis")
        cls.state = State.objects.create(state_id=201, state_name="State of Hope")
        cls.country = Country.objects.create(country_id=301, country_name="Imaginationland")

        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            comp_id=1,
            regd_address='456 Customer Lane',
            regd_pin_no='54321',
            regd_city=cls.city.city_id,
            regd_state=cls.state.state_id,
            regd_country=cls.country.country_id
        )

        cls.employee_checked = Employee.objects.create(emp_id=1001, comp_id=1, title="Mr", employee_name="Checked By")
        cls.employee_approved = Employee.objects.create(emp_id=1002, comp_id=1, title="Ms", employee_name="Approved By")
        cls.employee_authorized = Employee.objects.create(emp_id=1003, comp_id=1, title="Dr", employee_name="Authorized By")

        # Create a sample Quotation instance
        cls.quotation = Quotation.objects.create(
            id=1,
            quotation_no='QTEST001',
            enq_id='EQTEST001',
            comp_id=1,
            customer_id=cls.customer.customer_id,
            sys_date=timezone.now(),
            due_date=timezone.now() + timezone.timedelta(days=30),
            checked_by=cls.employee_checked.emp_id,
            approved_by=cls.employee_approved.emp_id,
            authorized_by=cls.employee_authorized.emp_id
        )

    def test_quotation_creation(self):
        """Verify basic creation and field assignments."""
        quotation = Quotation.objects.get(id=1)
        self.assertEqual(quotation.quotation_no, 'QTEST001')
        self.assertEqual(quotation.customer_id, 'CUST001')
        self.assertEqual(quotation.comp_id, 1)

    def test_customer_full_address_method(self):
        """Test the get_full_address method in Customer model."""
        expected_address = (
            f"{self.customer.regd_address},\n"
            f"{self.city.city_name},{self.state.state_name},\n"
            f"{self.country.country_name}.\n"
            f"{self.customer.regd_pin_no}\n"
        )
        self.assertEqual(self.customer.get_full_address(), expected_address)
    
    def test_customer_full_address_missing_parts(self):
        """Test get_full_address when some address components are null."""
        customer_no_city = Customer.objects.create(
            customer_id='CUST002', comp_id=1, regd_address='No City St', regd_pin_no='11111',
            regd_city=None, regd_state=self.state.state_id, regd_country=self.country.country_id
        )
        expected_address = f"No City St,\n{self.state.state_name},\n{self.country.country_name}.\n11111\n"
        self.assertEqual(customer_no_city.get_full_address(), expected_address)


    def test_employee_str_method(self):
        """Test the __str__ representation of Employee."""
        self.assertEqual(str(self.employee_checked), 'Mr. Checked By')
        employee_no_title = Employee.objects.create(emp_id=1004, comp_id=1, title=None, employee_name="No Title")
        self.assertEqual(str(employee_no_title), 'No Title')

    def test_get_report_data_method(self):
        """Verify the comprehensive report data aggregation."""
        report_data = self.quotation.get_report_data()

        self.assertIsInstance(report_data, dict)
        self.assertEqual(report_data['quotation_no'], 'QTEST001')
        self.assertEqual(report_data['customer_id'], 'CUST001')
        self.assertEqual(report_data['customer_address'], self.customer.get_full_address())
        self.assertEqual(report_data['checked_by'], str(self.employee_checked))
        self.assertEqual(report_data['approved_by'], str(self.employee_approved))
        self.assertEqual(report_data['authorized_by'], str(self.employee_authorized))
        self.assertEqual(report_data['company_name'], self.company.company_name)
        self.assertEqual(report_data['company_address'], self.company.address)
        self.assertIn(self.quotation.sys_date.strftime("%d-%m-%Y"), report_data['quotation_date'])
        self.assertIn(self.quotation.due_date.strftime("%d-%m-%Y"), report_data['due_date'])

    def test_get_report_data_missing_employees(self):
        """Test report data when some employee IDs are null."""
        quotation_incomplete = Quotation.objects.create(
            id=2, quotation_no='QTEST002', enq_id='EQTEST002', comp_id=1,
            customer_id=self.customer.customer_id, sys_date=timezone.now(), due_date=timezone.now(),
            checked_by=None, approved_by=None, authorized_by=None
        )
        report_data = quotation_incomplete.get_report_data()
        self.assertEqual(report_data['checked_by'], "")
        self.assertEqual(report_data['approved_by'], "")
        self.assertEqual(report_data['authorized_by'], "")


class QuotationPrintViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup comprehensive test data needed for view interactions
        cls.company = Company.objects.create(comp_id=1, company_name="Test Company Inc.", address="123 Corporate Ave")
        cls.city = City.objects.create(city_id=101, city_name="Metropolis")
        cls.state = State.objects.create(state_id=201, state_name="State of Hope")
        cls.country = Country.objects.create(country_id=301, country_name="Imaginationland")

        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            comp_id=1,
            regd_address='456 Customer Lane',
            regd_pin_no='54321',
            regd_city=cls.city.city_id,
            regd_state=cls.state.state_id,
            regd_country=cls.country.country_id
        )

        cls.employee_checked = Employee.objects.create(emp_id=1001, comp_id=1, title="Mr", employee_name="Checked By")
        cls.employee_approved = Employee.objects.create(emp_id=1002, comp_id=1, title="Ms", employee_name="Approved By")
        cls.employee_authorized = Employee.objects.create(emp_id=1003, comp_id=1, title="Dr", employee_name="Authorized By")

        cls.quotation = Quotation.objects.create(
            id=1,
            quotation_no='QTEST001',
            enq_id='EQTEST001',
            comp_id=1,
            customer_id=cls.customer.customer_id,
            sys_date=timezone.now(),
            due_date=timezone.now() + timezone.timedelta(days=30),
            checked_by=cls.employee_checked.emp_id,
            approved_by=cls.employee_approved.emp_id,
            authorized_by=cls.employee_authorized.emp_id
        )

    def setUp(self):
        self.client = Client()
        # Simulate session variables expected by the view
        session = self.client.session
        session['compid'] = 1
        session.save()

    def _get_url(self, quotation_instance=None, parent_page='1'):
        """Helper to construct the URL with query parameters."""
        if quotation_instance:
            return (
                reverse('sales_distribution:quotation_print_details') +
                f"?QuotationNo={quotation_instance.quotation_no}&EnqId={quotation_instance.enq_id}&Id={quotation_instance.id}&parentpage={parent_page}"
            )
        return reverse('sales_distribution:quotation_print_details') # For testing missing params

    def test_quotation_print_view_success(self):
        """Test that the view loads successfully with valid parameters."""
        url = self._get_url(self.quotation)
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/quotation/quotation_print_details.html')
        self.assertIn('report_data', response.context)
        self.assertIsNotNone(response.context['report_data'])

        # Verify key data points are rendered in the HTML
        self.assertContains(response, self.quotation.quotation_no)
        self.assertContains(response, self.company.company_name)
        self.assertContains(response, str(self.employee_checked)) # Employee name rendered
        self.assertContains(response, self.customer.get_full_address().replace('\n', '<br/>')) # Check address rendering
        self.assertContains(response, 'Quotation - Print Preview') # Check page title/header

    def test_quotation_print_view_not_found(self):
        """Test behavior when quotation parameters do not match any record (404)."""
        url = (
            reverse('sales_distribution:quotation_print_details') +
            "?QuotationNo=NONEXISTENT&EnqId=FAKE&Id=999999&parentpage=1"
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)
        # Verify error message is present
        self.assertContains(response, "Not Found") 

    def test_quotation_print_view_missing_required_params(self):
        """Test behavior when crucial query parameters are missing."""
        # Missing QuotationNo, EnqId, Id
        url = reverse('sales_distribution:quotation_print_details') + f"?parentpage=1"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # get_object_or_404 will raise Http404

    def test_quotation_print_view_invalid_id_param(self):
        """Test behavior with a non-integer 'Id' parameter."""
        url = (
            reverse('sales_distribution:quotation_print_details') +
            f"?QuotationNo={self.quotation.quotation_no}&EnqId={self.quotation.enq_id}&Id=invalid_id&parentpage=1"
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Due to int conversion error in get_object_or_404 arguments

    def test_quotation_print_view_cancel_button_url(self):
        """Verify the cancel button points to the correct previous page or default."""
        url = self._get_url(self.quotation, parent_page='2') # Simulate coming from 'parentpage=2'
        response = self.client.get(url, HTTP_REFERER='/previous/page/path/')
        self.assertContains(response, '<a href="/previous/page/path/"') # Check if HTTP_REFERER is used
        
        response = self.client.get(url) # No HTTP_REFERER
        self.assertContains(response, '<a href="/"') # Should default to '/'

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
For this specific "Quotation Print Details" page, the primary goal is to display a static report suitable for printing, rather than dynamic user interaction with data entry or lists. Therefore, the immediate application of HTMX and Alpine.js is minimal.

-   **HTMX:** While HTMX is typically used for dynamic content loading, form submissions, and partial updates, it's not the main focus for a pre-rendered print view. The "Cancel" button's navigation can be handled by standard `<a>` tags or JavaScript's `window.history.back()`, which is simpler and equally effective for this scenario than an HTMX request. If there were interactive elements *within* the report (e.g., collapsible sections for details), HTMX would be used for those.
-   **Alpine.js:** Similarly, Alpine.js's role for local UI state management is limited on a static print view. It would become highly relevant if complex client-side features like dynamic filtering on sub-sections of the report or interactive chart toggles were added.
-   **DataTables:** As noted previously, DataTables is designed for interactive list presentation (searching, sorting, pagination). This page displays a single, detailed report, not a list of records. Thus, DataTables is not applicable for this particular conversion.

**Summary for this page:** The provided Django code for models, views, and templates focuses on rendering the report data directly in HTML. This adheres to the "fat model, thin view" principle and prepares the application for future enhancements that might incorporate HTMX/Alpine.js for interactive elements, but they are not the core solution for replacing a static Crystal Report output. The emphasis for this page is on data presentation and print-friendliness.

## Final Notes

-   **Placeholders:** All `[PLACEHOLDERS]` have been replaced with inferred values from your ASP.NET code or suitable Django conventions.
-   **DRY Principles:** Templates extend a `base.html` (not included, as per instructions) and model methods (`get_report_data`, `get_full_address`) encapsulate reusable business logic, adhering to DRY.
-   **Separation of Concerns:** Business logic for data aggregation and formatting is moved into the `Quotation` model (`get_report_data` method), keeping the `QuotationPrintView` concise and focused on orchestrating data retrieval and template rendering.
-   **Test Coverage:** Comprehensive unit tests for models and integration tests for the view have been provided to ensure reliability and maintainability of the migrated code.
-   **Scalability:** This Django architecture is designed to be highly scalable and maintainable, offering a modern foundation for your application. Future features or integrations will be significantly easier to implement.