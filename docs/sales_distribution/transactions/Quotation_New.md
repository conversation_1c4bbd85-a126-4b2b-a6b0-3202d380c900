## ASP.NET to Django Conversion Script: Customer Quotation - New

This modernization plan outlines the strategy to transition your ASP.NET "Customer Quotation - New" module to a robust Django 5.0+ application, leveraging modern web patterns like HTMX, Alpine.js, and DataTables for a highly interactive user experience. Our focus is on automated, systematic conversion, ensuring a clear separation of concerns, and delivering a solution that is easy to maintain and scale.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code primarily interacts with `SD_Cust_Enquiry_Master` to display customer enquiries. It performs lookups against `tblFinancial_master` (for financial year), `tblHR_OfficeStaff` (for employee name), and `SD_Cust_Quotation_Master` (to count existing quotations for an enquiry). The `AutoCompleteExtender` uses `SD_Cust_master` for customer search.

**Identified Tables and Key Columns:**

*   **`SD_Cust_Enquiry_Master` (Main Entity: `CustomerEnquiry`)**
    *   `EnqId` (String, Primary Key for lookup)
    *   `Flag` (Boolean/Bit)
    *   `FinYearId` (Integer, Foreign Key to `tblFinancial_master`)
    *   `CustomerName` (String)
    *   `CustomerId` (String, Foreign Key to `SD_Cust_master`)
    *   `SessionId` (String, maps to `EmpId` in `tblHR_OfficeStaff`)
    *   `SysDate` (String, needs date conversion logic)
*   **`tblFinancial_master` (`FinancialYear`)**
    *   `FinYearId` (Integer, Primary Key)
    *   `FinYear` (String)
*   **`tblHR_OfficeStaff` (`OfficeStaff`)**
    *   `EmpId` (String, Primary Key)
    *   `EmployeeName` (String)
    *   `Title` (String)
    *   `CompId` (Integer)
*   **`SD_Cust_master` (`Customer`)**
    *   `CustomerId` (String, Primary Key)
    *   `CustomerName` (String)
    *   `CompId` (Integer)
*   **`SD_Cust_Quotation_Master` (`Quotation`)**
    *   `QuotationNo` (String, Primary Key)
    *   `EnqId` (String, Foreign Key to `SD_Cust_Enquiry_Master`)
*   **`SD_Cust_Quotation_Details_Temp` (Temporary Data)**
    *   This table is cleared on page load in ASP.NET. In Django, this typically points to a temporary session-bound data structure or a different architectural approach for draft data. For this migration, we will acknowledge it but not directly implement its cleanup in the `CustomerEnquiry` module's core logic, as it falls outside the main `CustomerEnquiry` CRUD.

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET code defines an "Enquiry Selection" page rather than direct "Quotation Creation".

*   **Read (List & Filter):** The primary functionality is to display a paginated, sortable list of customer enquiries (`SearchGridView1`). The list is filterable by "Customer Name" or "Enquiry No" using a dropdown (`DropDownList1`) and text boxes (`TxtSearchValue`, `txtEnqId`).
*   **Autocomplete:** The `TxtSearchValue` textbox has an `AutoCompleteExtender` for `CustomerName` based on `SD_Cust_master`.
*   **Selection & Redirection:** Users can "Select" an enquiry from the list, which redirects them to `Quotation_New_Details.aspx` with `CustomerId` and `EnqId` parameters. This implies the next step is to create a quotation *from* the selected enquiry.
*   **Session Management:** The ASP.NET code heavily relies on `Session["username"]`, `Session["compid"]`, `Session["finyear"]` for filtering and context. In Django, `request.user` and potentially a `UserProfile` model or a custom context processor will manage these.

### Step 3: Infer UI Components

**Analysis:** The UI consists of a search area and a data grid.

*   **Search Controls:**
    *   `DropDownList1`: A dropdown (`<select>`) for choosing search criteria (Customer Name or Enquiry No).
    *   `TxtSearchValue`: A text input (`<input type="text">`) for customer name search, with autocomplete.
    *   `txtEnqId`: A text input (`<input type="text">`) for enquiry ID search.
    *   `btnSearch`: A button (`<button>`) to trigger the search.
*   **Data Grid:**
    *   `SearchGridView1`: A `GridView` (`<table>`) displaying enquiry details. This will be converted to a standard HTML table managed by DataTables for client-side functionality (pagination, sorting, search).
*   **Dynamic Visibility:** `txtEnqId` and `TxtSearchValue` visibility changes based on `DropDownList1` selection. This will be handled by Alpine.js.
*   **Messages:** `Label2` displays messages, which will be handled by Django's messages framework and potentially displayed with HTMX.

### Step 4: Generate Django Code

We will create a Django application named `sales`.

#### 4.1 Models (`sales/models.py`)

This file defines the Django models that map to your existing database tables. We use `managed = False` because Django will not manage the table schema; it only uses them for data access. We also define a custom manager for `CustomerEnquiry` to encapsulate the complex data retrieval and enrichment logic.

```python
import re
from datetime import datetime
from django.db import models
from django.db.models import F, OuterRef, Subquery, Value, Case, When, IntegerField
from django.db.models.functions import Concat
from django.db.models import Count

# Helper function to extract code from customer name (mimics fun.getCode)
def extract_code_from_customer_name(customer_name_with_code):
    """
    Extracts the code from a string like "Customer Name [Code]".
    Returns the code if found, otherwise None.
    """
    match = re.search(r'\[(.*?)\]$', customer_name_with_code)
    if match:
        return match.group(1)
    return None

# Custom Manager for CustomerEnquiry to encapsulate complex query logic
class CustomerEnquiryManager(models.Manager):
    def get_enquiry_list_data(self, company_id, financial_year_id, search_type=None, search_value=None, enquiry_id_value=None):
        queryset = self.get_queryset()
        
        # Initial filter based on session context (CompId, FinYearId)
        queryset = queryset.filter(
            customer_id__isnull=False, # Equivalent to CustomerId!=''
            financial_year_id__lte=financial_year_id,
            company_id=company_id
        )

        # Apply search filters based on dropdown selection
        if search_type == '0' and search_value: # Customer Name
            customer_code = extract_code_from_customer_name(search_value)
            if customer_code:
                queryset = queryset.filter(customer_id=customer_code)
        elif search_type == '1' and enquiry_id_value: # Enquiry No
            queryset = queryset.filter(enquiry_id=enquiry_id_value)

        # Annotate with Financial Year from tblFinancial_master
        financial_year_subquery = FinancialYear.objects.filter(
            financial_year_id=OuterRef('financial_year_id')
        ).values('financial_year')[:1]
        
        queryset = queryset.annotate(
            fin_year_display=Subquery(financial_year_subquery)
        )

        # Annotate with Employee Name from tblHR_OfficeStaff
        employee_name_subquery = OfficeStaff.objects.filter(
            company_id=OuterRef('company_id'), # Assuming company_id exists on OfficeStaff
            employee_id=OuterRef('session_id') # ASP.NET SessionId maps to EmpId
        ).annotate(
            full_employee_name=Concat(F('title'), Value('.'), F('employee_name'))
        ).values('full_employee_name')[:1]

        queryset = queryset.annotate(
            employee_name_display=Subquery(employee_name_subquery)
        )

        # Annotate with Quotation Count from SD_Cust_Quotation_Master
        quotation_count_subquery = Quotation.objects.filter(
            enquiry_id=OuterRef('enquiry_id')
        ).values('enquiry_id').annotate(
            count=Count('quotation_no')
        ).values('count')
        
        queryset = queryset.annotate(
            quotation_count=Subquery(quotation_count_subquery, output_field=IntegerField())
        )
        
        # Ensure quotation_count is 0 if no quotations exist (similar to ASP.NET logic)
        queryset = queryset.annotate(
            quotation_count_final=Case(
                When(quotation_count__isnull=True, then=Value(0)),
                default=F('quotation_count'),
                output_field=IntegerField()
            )
        )

        # Order by EnquiryId Desc as in ASP.NET
        queryset = queryset.order_by('-enquiry_id')
        
        return queryset

# Define other models with managed=False and db_table mapping

class FinancialYear(models.Model):
    financial_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    financial_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.financial_year

class OfficeStaff(models.Model):
    employee_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=100)
    title = models.CharField(db_column='Title', max_length=10)
    company_id = models.IntegerField(db_column='CompId') # Added as per usage in C#

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}.{self.employee_name}"

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.IntegerField(db_column='CompId') # Added as per usage in C#

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class Quotation(models.Model):
    # This model is only used for counting, so minimal fields
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50, primary_key=True)
    enquiry_id = models.CharField(db_column='EnqId', max_length=50) # Assuming this is a FK to CustomerEnquiry

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'

    def __str__(self):
        return self.quotation_no

class CustomerEnquiry(models.Model):
    # Using EnqId as primary key based on ASP.NET DataKeyNames
    enquiry_id = models.CharField(db_column='EnqId', max_length=50, primary_key=True)
    flag = models.BooleanField(db_column='Flag', default=False) # Assuming Flag is a boolean/bit
    financial_year_id = models.IntegerField(db_column='FinYearId')
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    customer_id = models.CharField(db_column='CustomerId', max_length=50)
    session_id = models.CharField(db_column='SessionId', max_length=50) # Maps to EmpId in tblHR_OfficeStaff
    sys_date = models.CharField(db_column='SysDate', max_length=20) # Stored as string, will format via property
    company_id = models.IntegerField(db_column='CompId', default=1) # Assuming CompId exists on Enquiry Master as well

    # Custom manager for complex queries
    objects = CustomerEnquiryManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_Enquiry_Master'
        verbose_name = 'Customer Enquiry'
        verbose_name_plural = 'Customer Enquiries'

    def __str__(self):
        return f"{self.customer_name} ({self.enquiry_id})"

    @property
    def formatted_sys_date(self):
        """
        Emulates the ASP.NET date format conversion (e.g., 'MM-DD-YYYY' to 'DD-MM-YYYY').
        The original SQL converted to dd/mm/yyyy then replaced '/' with '-'.
        """
        try:
            # Assuming sys_date is like 'MM-DD-YYYY'
            month, day, year = self.sys_date.split('-')
            # The CONVERT(datetime, 'DD-MM-YYYY', 103) part in SQL means it expects DD-MM-YYYY for parsing.
            # So, we first re-arrange MM-DD-YYYY to DD-MM-YYYY for robust parsing, then format back.
            dt_obj = datetime.strptime(f"{day}-{month}-{year}", "%d-%m-%Y")
            return dt_obj.strftime("%d-%m-%Y") # Output format dd-mm-yyyy
        except (ValueError, AttributeError):
            return self.sys_date # Return raw if parsing fails
```

#### 4.2 Forms (`sales/forms.py`)

We create a simple form for the search criteria. This is not a `ModelForm` as it doesn't directly map to a model instance for creation/update, but rather handles search inputs.

```python
from django import forms
from .models import Customer

class EnquirySearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'sales:enquiry_search_form_partial' %}",
            'hx-target': '#search-inputs-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
            'hx-include': '#id_search_by', # Pass the selected value
        }),
        initial='Select'
    )
    
    txt_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-96 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name',
            'x-show': "searchBy === '0'", # Alpine.js for visibility
            'hx-get': "{% url 'sales:customer_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms from:this",
            'hx-target': "#autocomplete-suggestions",
            'hx-indicator': "#autocomplete-loading",
            'hx-swap': "innerHTML",
            'autocomplete': "off"
        })
    )

    txt_enquiry_id = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Enquiry ID',
            'x-show': "searchBy === '1'", # Alpine.js for visibility
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        txt_search_value = cleaned_data.get('txt_search_value')
        txt_enquiry_id = cleaned_data.get('txt_enquiry_id')

        if search_by == '0' and not txt_search_value:
            # We don't enforce 'required' for text fields in the form to allow empty search
            # The C# code allows empty search values, so we follow that.
            pass
        elif search_by == '1' and not txt_enquiry_id:
            pass
        
        return cleaned_data

class CustomerAutoCompleteForm(forms.Form):
    # This form is just for validation of the autocomplete input
    q = forms.CharField(label='Search customers', required=True)

```

#### 4.3 Views (`sales/views.py`)

Views are kept thin, delegating complex data retrieval to the model manager. HTMX handles partial updates for search and table rendering.

```python
from django.views.generic import ListView, FormView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import render
from django.conf import settings # For mock session data

from .models import CustomerEnquiry, Customer, FinancialYear, OfficeStaff
from .forms import EnquirySearchForm, CustomerAutoCompleteForm
import re

# Mock session data for demonstration. In a real app, these would come from request.user profile.
MOCK_COMPANY_ID = 1
MOCK_FINANCIAL_YEAR_ID = 1 # Assuming a default financial year ID
MOCK_USERNAME = 'user123' # Not directly used in queries here, but in original code

class CustomerEnquiryListView(ListView):
    model = CustomerEnquiry
    template_name = 'sales/customerenquiry/list.html'
    context_object_name = 'customerenquiries'
    
    def get_queryset(self):
        # Initial queryset to pass to the template, the actual data will be loaded via HTMX
        return CustomerEnquiry.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        search_by_initial = self.request.GET.get('search_by', 'Select')
        context['search_form'] = EnquirySearchForm(
            initial={
                'search_by': search_by_initial,
                'txt_search_value': self.request.GET.get('txt_search_value', ''),
                'txt_enquiry_id': self.request.GET.get('txt_enquiry_id', ''),
            }
        )
        # Initial state for Alpine.js
        context['initial_search_by'] = search_by_initial
        
        # Display messages from the ASP.NET QueryString 'msg' equivalent
        if 'msg' in self.request.GET:
            messages.info(self.request, self.request.GET['msg'])
            
        # The ASP.NET code clears 'SD_Cust_Quotation_Details_Temp'. 
        # This is typically handled differently in Django (e.g., dedicated cleanup task, session data).
        # We acknowledge it here but don't implement explicit DB deletion for this unrelated temp table
        # within this view, adhering to separation of concerns.
        
        return context

class CustomerEnquiryTablePartialView(ListView):
    model = CustomerEnquiry
    template_name = 'sales/customerenquiry/_table.html'
    context_object_name = 'customerenquiries'

    def get_queryset(self):
        # The query parameters are passed via GET from the search form
        search_by = self.request.GET.get('search_by')
        txt_search_value = self.request.GET.get('txt_search_value')
        txt_enquiry_id = self.request.GET.get('txt_enquiry_id')

        # Get session/company specific IDs (mocked for now)
        company_id = MOCK_COMPANY_ID
        financial_year_id = MOCK_FINANCIAL_YEAR_ID

        # Call the custom manager method to get the enriched data
        enquiries = CustomerEnquiry.objects.get_enquiry_list_data(
            company_id=company_id,
            financial_year_id=financial_year_id,
            search_type=search_by,
            search_value=txt_search_value,
            enquiry_id_value=txt_enquiry_id
        )
        
        return enquiries

class EnquirySearchFormPartialView(FormView):
    form_class = EnquirySearchForm
    template_name = 'sales/customerenquiry/_search_form.html' # Only renders the form section

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pre-populate form based on current selection from HTMX
        kwargs['initial'] = {
            'search_by': self.request.GET.get('search_by', 'Select'),
            'txt_search_value': self.request.GET.get('txt_search_value', ''),
            'txt_enquiry_id': self.request.GET.get('txt_enquiry_id', ''),
        }
        return kwargs
    
    def get(self, request, *args, **kwargs):
        # Re-render only the form with the correct Alpine.js state
        form = self.get_form()
        initial_search_by = request.GET.get('search_by', 'Select')
        context = {
            'search_form': form,
            'initial_search_by': initial_search_by
        }
        return render(request, self.template_name, context)

class CustomerSearchAutoCompleteView(ListView):
    model = Customer
    context_object_name = 'customers'
    
    def get_queryset(self):
        query = self.request.GET.get('q', '')
        company_id = MOCK_COMPANY_ID # Use the session's company ID
        
        if query:
            # Filter customers by name or ID, case-insensitive
            return Customer.objects.filter(
                company_id=company_id,
                customer_name__icontains=query
            ).values('customer_id', 'customer_name')[:10] # Limit suggestions
        return Customer.objects.none()

    def render_to_response(self, context, **response_kwargs):
        # Return a list of suggestions as HTML for HTMX to swap
        # Each suggestion should include both name and code for selection
        suggestions = []
        for customer in context['customers']:
            suggestions.append(
                f"<div class='p-2 hover:bg-gray-200 cursor-pointer' "
                f"hx-on:click=\"document.getElementById('id_txt_search_value').value='{customer['customer_name']} [{customer['customer_id']}]'; document.getElementById('autocomplete-suggestions').innerHTML='';\""
                f">"
                f"{customer['customer_name']} [{customer['customer_id']}]"
                f"</div>"
            )
        return HttpResponse("".join(suggestions))

```

#### 4.4 Templates (`sales/templates/sales/customerenquiry/`)

These templates incorporate HTMX for dynamic content and Alpine.js for UI state, like showing/hiding search inputs. DataTables is used for interactive grid functionality.

**`list.html`** (Main page for customer enquiries)

```html
{% extends 'core/base.html' %}

{% block title %}Customer Quotation - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Quotation - New</h2>
    </div>

    {% if messages %}
        <div id="messages" class="mb-4">
            {% for message in messages %}
                <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{ searchBy: '{{ initial_search_by }}' }">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Enquiries</h3>
        <form hx-get="{% url 'sales:enquiry_table_partial' %}" hx-target="#customerenquiry-table-container" hx-swap="innerHTML" hx-indicator="#loading-spinner">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div id="search-by-dropdown-container">
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">Search By</label>
                    {{ search_form.search_by }}
                </div>

                <div id="search-inputs-container" x-data="{ currentSearchBy: searchBy }"
                     hx-swap="outerHTML" hx-target="this"> {# HTMX swaps this div to update inputs visibility #}
                    {% include 'sales/customerenquiry/_search_form.html' with search_form=search_form initial_search_by=initial_search_by %}
                </div>
                
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                    Search
                </button>
            </div>
            <div id="autocomplete-loading" class="htmx-indicator ml-4 text-sm text-gray-500">Searching...</div>
            <div id="autocomplete-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-96 max-h-60 overflow-y-auto"></div>
        </form>
    </div>

    <div id="customerenquiry-table-container"
         hx-trigger="load, refreshCustomerEnquiryList from:body"
         hx-get="{% url 'sales:enquiry_table_partial' %}"
         hx-target="this"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div id="loading-spinner" class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 htmx-indicator"></div>
            <p class="mt-2 text-gray-600">Loading enquiries...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init will automatically pick up x-data attributes.
    // The select element hx-get will re-render the search inputs container.
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'search-inputs-container') {
            // Re-initialize Alpine.js on the swapped content
            Alpine.initTree(evt.detail.target);
        }
    });

    // Handle clicks outside autocomplete suggestions to hide them
    document.addEventListener('click', function(event) {
        const autocompleteDiv = document.getElementById('autocomplete-suggestions');
        const searchInput = document.getElementById('id_txt_search_value');
        if (autocompleteDiv && !autocompleteDiv.contains(event.target) && !searchInput.contains(event.target)) {
            autocompleteDiv.innerHTML = '';
        }
    });

    // Set initial state for Alpine.js on page load if search_by is already set in query params
    document.addEventListener('alpine:init', () => {
        Alpine.data('searchFormState', () => ({
            searchBy: '{{ initial_search_by }}', // This is set from initial_search_by in context
            init() {
                // Ensure initial visibility is correct
                this.$nextTick(() => {
                    const searchSelect = this.$refs.searchSelect;
                    if (searchSelect) {
                        this.searchBy = searchSelect.value;
                    }
                });
            },
            updateSearchBy(event) {
                this.searchBy = event.target.value;
            }
        }));
    });
</script>
{% endblock %}
```

**`_search_form.html`** (Partial for dynamic search input display)

```html
<div id="search-inputs-container" x-data="{ currentSearchBy: '{{ initial_search_by }}' }"
     hx-swap="outerHTML" hx-target="this">
    <div class="flex items-center space-x-4" x-init="currentSearchBy = document.getElementById('id_search_by').value"
         x-on:change="currentSearchBy = $event.target.value">
        {% if search_form %}
            <div x-show="currentSearchBy === '0'">
                <label for="{{ search_form.txt_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">Customer Name</label>
                {{ search_form.txt_search_value }}
            </div>
            <div x-show="currentSearchBy === '1'">
                <label for="{{ search_form.txt_enquiry_id.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">Enquiry No</label>
                {{ search_form.txt_enquiry_id }}
            </div>
        {% endif %}
    </div>
</div>
```

**`_table.html`** (Partial for the DataTables content)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="customerEnquiryTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No. of Quo.</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if customerenquiries %}
                {% for enquiry in customerenquiries %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ enquiry.fin_year_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-right">{{ enquiry.enquiry_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-left">{{ enquiry.customer_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ enquiry.customer_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ enquiry.quotation_count_final }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ enquiry.formatted_sys_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-left">{{ enquiry.employee_name_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">
                        <a href="/module/sales/transactions/quotation_new_details/?CustomerId={{ enquiry.customer_id }}&EnqId={{ enquiry.enquiry_id }}&ModId=2&SubModId=63"
                           class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs"
                           hx-boost="false" {# Use hx-boost="false" to allow standard navigation for this specific link #}
                           >Select</a>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-4 text-center font-bold text-red-700 text-lg">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Only initialize DataTable if it hasn't been initialized
    if (!$.fn.DataTable.isDataTable('#customerEnquiryTable')) {
        $('#customerEnquiryTable').DataTable({
            "pageLength": 20, // Matching ASP.NET PageSize
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "pagingType": "full_numbers",
            "order": [[2, "desc"]] // Default order by Enquiry No descending
        });
    }
});
</script>
```

#### 4.5 URLs (`sales/urls.py`)

This file defines the URL patterns for our views, organizing them within the `sales` application namespace.

```python
from django.urls import path
from .views import CustomerEnquiryListView, CustomerEnquiryTablePartialView, EnquirySearchFormPartialView, CustomerSearchAutoCompleteView

app_name = 'sales' # Namespace for the sales application

urlpatterns = [
    path('quotation-new/', CustomerEnquiryListView.as_view(), name='enquiry_list'),
    path('quotation-new/table/', CustomerEnquiryTablePartialView.as_view(), name='enquiry_table_partial'),
    path('quotation-new/search-form-partial/', EnquirySearchFormPartialView.as_view(), name='enquiry_search_form_partial'),
    path('quotation-new/autocomplete-customer/', CustomerSearchAutoCompleteView.as_view(), name='customer_autocomplete'),
    # The 'Select' action redirects to an external/new page,
    # so no specific Django URL is needed for the 'select' button itself,
    # it uses a standard <a> tag with hx-boost="false"
]
```

#### 4.6 Tests (`sales/tests.py`)

Comprehensive tests cover the models' behavior and the views' interactions, ensuring the migration maintains functional parity and robustness.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection # For raw SQL to verify managed=False models
from unittest.mock import patch, MagicMock

# Import all models
from .models import CustomerEnquiry, FinancialYear, OfficeStaff, Customer, Quotation
from .models import MOCK_COMPANY_ID, MOCK_FINANCIAL_YEAR_ID # Access mock data from models file

class SalesModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Establish mock database entries for managed=False models
        # In a real setup, you'd use a test database that already contains these tables.
        # For demonstration, we'll create some minimal data using raw SQL
        # if using sqlite in memory for tests. For proper DB, ensure test DB is populated.

        # Ensure tables exist for managed=False models if running against a fresh test DB
        # This is a workaround for SQLite in-memory, assumes a real DB setup for production
        with connection.cursor() as cursor:
            # Create mock tables if they don't exist (for in-memory SQLite testing)
            # In a real scenario with an existing DB, these tables would already be there.
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tblFinancial_master (FinYearId INTEGER PRIMARY KEY, FinYear TEXT);
                CREATE TABLE IF NOT EXISTS tblHR_OfficeStaff (EmpId TEXT PRIMARY KEY, EmployeeName TEXT, Title TEXT, CompId INTEGER);
                CREATE TABLE IF NOT EXISTS SD_Cust_master (CustomerId TEXT PRIMARY KEY, CustomerName TEXT, CompId INTEGER);
                CREATE TABLE IF NOT EXISTS SD_Cust_Quotation_Master (QuotationNo TEXT PRIMARY KEY, EnqId TEXT);
                CREATE TABLE IF NOT EXISTS SD_Cust_Enquiry_Master (
                    EnqId TEXT PRIMARY KEY, Flag INTEGER, FinYearId INTEGER, CustomerName TEXT, CustomerId TEXT, SessionId TEXT, SysDate TEXT, CompId INTEGER
                );
            """)
            # Insert test data
            cursor.execute("INSERT OR REPLACE INTO tblFinancial_master (FinYearId, FinYear) VALUES (?, ?)", (MOCK_FINANCIAL_YEAR_ID, '2023-2024'))
            cursor.execute("INSERT OR REPLACE INTO tblHR_OfficeStaff (EmpId, EmployeeName, Title, CompId) VALUES (?, ?, ?, ?)", ('EMP001', 'John Doe', 'Mr', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_master (CustomerId, CustomerName, CompId) VALUES (?, ?, ?)", ('CUST001', 'Acme Corp', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_master (CustomerId, CustomerName, CompId) VALUES (?, ?, ?)", ('CUST002', 'Beta Solutions', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Enquiry_Master (EnqId, Flag, FinYearId, CustomerName, CustomerId, SessionId, SysDate, CompId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                            ('ENQ001', 0, MOCK_FINANCIAL_YEAR_ID, 'Acme Corp', 'CUST001', 'EMP001', '01-15-2023', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Enquiry_Master (EnqId, Flag, FinYearId, CustomerName, CustomerId, SessionId, SysDate, CompId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                            ('ENQ002', 0, MOCK_FINANCIAL_YEAR_ID, 'Beta Solutions', 'CUST002', 'EMP001', '02-20-2023', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Enquiry_Master (EnqId, Flag, FinYearId, CustomerName, CustomerId, SessionId, SysDate, CompId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                            ('ENQ003', 0, MOCK_FINANCIAL_YEAR_ID, 'Acme Corp', 'CUST001', 'EMP001', '03-10-2023', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Quotation_Master (QuotationNo, EnqId) VALUES (?, ?)", ('QUO001', 'ENQ001'))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Quotation_Master (QuotationNo, EnqId) VALUES (?, ?)", ('QUO002', 'ENQ001'))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Quotation_Master (QuotationNo, EnqId) VALUES (?, ?)", ('QUO003', 'ENQ002'))

    def test_customer_enquiry_model_exists(self):
        self.assertEqual(CustomerEnquiry.objects.count(), 3)
        enquiry = CustomerEnquiry.objects.get(enquiry_id='ENQ001')
        self.assertEqual(enquiry.customer_name, 'Acme Corp')
        self.assertEqual(enquiry.customer_id, 'CUST001')
        self.assertEqual(enquiry.sys_date, '01-15-2023')
        self.assertEqual(enquiry.formatted_sys_date, '15-01-2023') # Test date formatting

    def test_financial_year_model_exists(self):
        fy = FinancialYear.objects.get(financial_year_id=MOCK_FINANCIAL_YEAR_ID)
        self.assertEqual(fy.financial_year, '2023-2024')

    def test_office_staff_model_exists(self):
        staff = OfficeStaff.objects.get(employee_id='EMP001')
        self.assertEqual(staff.employee_name, 'John Doe')

    def test_customer_model_exists(self):
        customer = Customer.objects.get(customer_id='CUST001')
        self.assertEqual(customer.customer_name, 'Acme Corp')

    def test_quotation_model_exists(self):
        quotation = Quotation.objects.get(quotation_no='QUO001')
        self.assertEqual(quotation.enquiry_id, 'ENQ001')

    def test_customer_enquiry_manager_get_enquiry_list_data_no_filter(self):
        enquiries = CustomerEnquiry.objects.get_enquiry_list_data(
            company_id=MOCK_COMPANY_ID,
            financial_year_id=MOCK_FINANCIAL_YEAR_ID
        )
        self.assertEqual(len(enquiries), 3)
        
        # Test annotations
        enq1 = enquiries.get(enquiry_id='ENQ001')
        self.assertEqual(enq1.fin_year_display, '2023-2024')
        self.assertEqual(enq1.employee_name_display, 'Mr.John Doe')
        self.assertEqual(enq1.quotation_count_final, 2) # ENQ001 has two quotations
        
        enq2 = enquiries.get(enquiry_id='ENQ002')
        self.assertEqual(enq2.quotation_count_final, 1) # ENQ002 has one quotation

        enq3 = enquiries.get(enquiry_id='ENQ003')
        self.assertEqual(enq3.quotation_count_final, 0) # ENQ003 has no quotations

    def test_customer_enquiry_manager_get_enquiry_list_data_customer_name_filter(self):
        enquiries = CustomerEnquiry.objects.get_enquiry_list_data(
            company_id=MOCK_COMPANY_ID,
            financial_year_id=MOCK_FINANCIAL_YEAR_ID,
            search_type='0',
            search_value='Acme Corp [CUST001]'
        )
        self.assertEqual(len(enquiries), 2)
        self.assertTrue(all(e.customer_id == 'CUST001' for e in enquiries))

    def test_customer_enquiry_manager_get_enquiry_list_data_enquiry_id_filter(self):
        enquiries = CustomerEnquiry.objects.get_enquiry_list_data(
            company_id=MOCK_COMPANY_ID,
            financial_year_id=MOCK_FINANCIAL_YEAR_ID,
            search_type='1',
            enquiry_id_value='ENQ002'
        )
        self.assertEqual(len(enquiries), 1)
        self.assertEqual(enquiries[0].enquiry_id, 'ENQ002')

    def test_customer_enquiry_manager_get_enquiry_list_data_invalid_filter(self):
        # Test a filter that should yield no results
        enquiries = CustomerEnquiry.objects.get_enquiry_list_data(
            company_id=MOCK_COMPANY_ID,
            financial_year_id=MOCK_FINANCIAL_YEAR_ID,
            search_type='1',
            enquiry_id_value='NONEXISTENT'
        )
        self.assertEqual(len(enquiries), 0)

class SalesViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Same setup as for models, ensure data is available for views
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tblFinancial_master (FinYearId INTEGER PRIMARY KEY, FinYear TEXT);
                CREATE TABLE IF NOT EXISTS tblHR_OfficeStaff (EmpId TEXT PRIMARY KEY, EmployeeName TEXT, Title TEXT, CompId INTEGER);
                CREATE TABLE IF NOT EXISTS SD_Cust_master (CustomerId TEXT PRIMARY KEY, CustomerName TEXT, CompId INTEGER);
                CREATE TABLE IF NOT EXISTS SD_Cust_Quotation_Master (QuotationNo TEXT PRIMARY KEY, EnqId TEXT);
                CREATE TABLE IF NOT EXISTS SD_Cust_Enquiry_Master (
                    EnqId TEXT PRIMARY KEY, Flag INTEGER, FinYearId INTEGER, CustomerName TEXT, CustomerId TEXT, SessionId TEXT, SysDate TEXT, CompId INTEGER
                );
            """)
            cursor.execute("INSERT OR REPLACE INTO tblFinancial_master (FinYearId, FinYear) VALUES (?, ?)", (MOCK_FINANCIAL_YEAR_ID, '2023-2024'))
            cursor.execute("INSERT OR REPLACE INTO tblHR_OfficeStaff (EmpId, EmployeeName, Title, CompId) VALUES (?, ?, ?, ?)", ('EMP001', 'John Doe', 'Mr', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_master (CustomerId, CustomerName, CompId) VALUES (?, ?, ?)", ('CUST001', 'Acme Corp', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_master (CustomerId, CustomerName, CompId) VALUES (?, ?, ?)", ('CUST002', 'Beta Solutions', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Enquiry_Master (EnqId, Flag, FinYearId, CustomerName, CustomerId, SessionId, SysDate, CompId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                            ('ENQ001', 0, MOCK_FINANCIAL_YEAR_ID, 'Acme Corp', 'CUST001', 'EMP001', '01-15-2023', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Enquiry_Master (EnqId, Flag, FinYearId, CustomerName, CustomerId, SessionId, SysDate, CompId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                            ('ENQ002', 0, MOCK_FINANCIAL_YEAR_ID, 'Beta Solutions', 'CUST002', 'EMP001', '02-20-2023', MOCK_COMPANY_ID))
            cursor.execute("INSERT OR REPLACE INTO SD_Cust_Quotation_Master (QuotationNo, EnqId) VALUES (?, ?)", ('QUO001', 'ENQ001'))


    def setUp(self):
        self.client = Client()

    def test_enquiry_list_view_get(self):
        response = self.client.get(reverse('sales:enquiry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/list.html')
        self.assertIn('search_form', response.context)
        self.assertIn('initial_search_by', response.context)
        # Initial table container should be empty, loaded by HTMX
        self.assertContains(response, '<div id="customerenquiry-table-container"')
        self.assertContains(response, 'Loading enquiries...') # Checks for initial loading state

    def test_enquiry_list_view_with_msg_query_string(self):
        response = self.client.get(reverse('sales:enquiry_list') + '?msg=TestMessage')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TestMessage')
        self.assertContains(response, 'bg-blue-100 text-blue-800') # Default info message style

    def test_enquiry_table_partial_view_get_no_filter(self):
        response = self.client.get(reverse('sales:enquiry_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_table.html')
        self.assertIn('customerenquiries', response.context)
        self.assertEqual(len(response.context['customerenquiries']), 2) # Only ENQ001, ENQ002 are fully valid for test data
        self.assertContains(response, 'Acme Corp')
        self.assertContains(response, 'Beta Solutions')
        self.assertContains(response, '15-01-2023') # Check formatted date

    def test_enquiry_table_partial_view_get_customer_name_filter(self):
        response = self.client.get(reverse('sales:enquiry_table_partial'), {
            'search_by': '0',
            'txt_search_value': 'Acme Corp [CUST001]'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_table.html')
        self.assertIn('customerenquiries', response.context)
        self.assertEqual(len(response.context['customerenquiries']), 1) # ENQ001
        self.assertContains(response, 'Acme Corp')
        self.assertNotContains(response, 'Beta Solutions')

    def test_enquiry_table_partial_view_get_enquiry_id_filter(self):
        response = self.client.get(reverse('sales:enquiry_table_partial'), {
            'search_by': '1',
            'txt_enquiry_id': 'ENQ002'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_table.html')
        self.assertIn('customerenquiries', response.context)
        self.assertEqual(len(response.context['customerenquiries']), 1)
        self.assertContains(response, 'ENQ002')
        self.assertNotContains(response, 'ENQ001')
        
    def test_enquiry_table_partial_view_get_no_results(self):
        response = self.client.get(reverse('sales:enquiry_table_partial'), {
            'search_by': '1',
            'txt_enquiry_id': 'NONEXISTENT'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_table.html')
        self.assertIn('customerenquiries', response.context)
        self.assertEqual(len(response.context['customerenquiries']), 0)
        self.assertContains(response, 'No data to display !')

    def test_enquiry_search_form_partial_view_get(self):
        response = self.client.get(reverse('sales:enquiry_search_form_partial'), {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_search_form.html')
        self.assertIn('search_form', response.context)
        self.assertIn('initial_search_by', response.context)
        self.assertContains(response, 'x-show="currentSearchBy === \'0\'"') # Check Alpine.js attribute
        self.assertContains(response, 'id_txt_search_value') # Textbox for customer name
        self.assertNotContains(response, 'id_txt_enquiry_id') # Textbox for enquiry ID should be hidden

        response = self.client.get(reverse('sales:enquiry_search_form_partial'), {'search_by': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'id_txt_enquiry_id') # Textbox for enquiry ID
        self.assertNotContains(response, 'id_txt_search_value') # Textbox for customer name should be hidden
        
    def test_customer_autocomplete_view_get(self):
        response = self.client.get(reverse('sales:customer_autocomplete'), {'q': 'acme'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/html; charset=utf-8') # HTMX expects HTML fragment
        self.assertContains(response, 'Acme Corp [CUST001]')
        self.assertNotContains(response, 'Beta Solutions')

    def test_customer_autocomplete_view_get_no_query(self):
        response = self.client.get(reverse('sales:customer_autocomplete'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), '') # Empty string if no query

    def test_customer_autocomplete_view_get_no_match(self):
        response = self.client.get(reverse('sales:customer_autocomplete'), {'q': 'nomatch'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), '') # Empty string if no match

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Implementation:**

*   **Page Load:** The `customerenquiry-table-container` on `list.html` uses `hx-trigger="load"` to automatically fetch and render the initial table data from `{% url 'sales:enquiry_table_partial' %}`.
*   **Search Form:**
    *   The `search_by` dropdown uses `hx-get="{% url 'sales:enquiry_search_form_partial' %}" hx-target="#search-inputs-container" hx-swap="outerHTML" hx-trigger="change"` to dynamically reload the correct input field (customer name or enquiry ID) based on selection.
    *   The entire search form is wrapped in an `hx-get` that targets the table container, so clicking the "Search" button (which is type="submit") reloads only the table content.
*   **Dynamic Input Visibility:** Alpine.js `x-data`, `x-show`, and `x-on:change` attributes on the `_search_form.html` partial handle the conditional display of `txt_search_value` and `txt_enquiry_id` based on the `search_by` dropdown's value.
*   **Autocomplete:**
    *   `txt_search_value` has `hx-get="{% url 'sales:customer_autocomplete' %}" hx-trigger="keyup changed delay:500ms from:this" hx-target="#autocomplete-suggestions"`.
    *   The `CustomerSearchAutoCompleteView` returns an HTML fragment with `div` elements, each having `hx-on:click` to populate the input field and clear suggestions.
*   **DataTables:** The `_table.html` partial includes a `$(document).ready` script that initializes DataTables on the `customerEnquiryTable`. This ensures client-side pagination, sorting, and search capabilities.
*   **Selection:** The "Select" `LinkButton` is converted to a standard `<a>` tag with `hx-boost="false"` to ensure a full page navigation to the "Quotation New Details" page, preserving the original ASP.NET behavior.
*   **Messages:** Django's `messages` framework is used, and displayed directly in `list.html`. If dynamic message updates were needed (e.g., from an HTMX form submission on another page), HTMX `HX-Trigger` headers could be used to swap messages.

### Final Notes

*   **Placeholders:** `[APP_NAME]` is `sales`, `[MODEL_NAME]` is `CustomerEnquiry`, `[MODEL_NAME_LOWER]` is `customerenquiry`, and so on, as used throughout the generated code.
*   **DRY Templates:** `_search_form.html` and `_table.html` are partials for reusability and HTMX-driven updates.
*   **Fat Model, Thin View:** The complex data fetching and manipulation logic from `BindDataCust` is fully encapsulated within the `CustomerEnquiryManager.get_enquiry_list_data` method, keeping the Django views concise and focused on request/response handling.
*   **Temporary Data Cleanup:** The ASP.NET logic to clear `SD_Cust_Quotation_Details_Temp` on page load is noted. For Django, this would typically involve a separate application service or a different pattern for handling temporary data (e.g., in-session, database-backed caches with expiry, or explicit user actions), which falls outside the scope of this particular module's direct functional migration.
*   **Database Connection:** This plan assumes your Django project is correctly configured to connect to your existing SQL Server database using a backend like `django-pyodbc` or `django-mssql-backend`, and that the `managed=False` setting correctly maps to your existing table schemas.
*   **Routing:** Ensure your project's `urls.py` includes `path('sales/', include('sales.urls'))` to make these URLs accessible.