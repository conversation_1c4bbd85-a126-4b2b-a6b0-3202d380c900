## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET `.aspx` and `.aspx.cs` files for `Quotation_Dashboard` are extremely minimal. They primarily define content placeholders and an empty `Page_Load` method, without revealing any specific UI components, data sources, or business logic.

Therefore, for this modernization plan, we will **infer** a common structure and functionality for a "Quotation Dashboard" module, assuming it manages business quotations. This approach demonstrates the systematic conversion process even when explicit details are missing, focusing on best practices for a typical enterprise application.

We will proceed with the following assumptions:
*   **Module Name**: `SalesDistribution` (from the ASP.NET namespace `Module_SalesDistribution`).
*   **Entity**: `Quotation`.
*   **Expected Functionality**: Displaying a list of quotations with options to add, edit, and delete them.

---

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code contains no `SqlDataSource`, `GridView` bindings, or explicit SQL commands, we cannot directly extract the database schema.

**Inference:** Based on the page name `Quotation_Dashboard` and the common requirements for business applications, we will infer the existence of a database table named `tbl_quotation` (a common ASP.NET naming convention). We will also define a set of likely columns for a Quotation entity.

*   **[TABLE_NAME]**: `tbl_quotation`
*   **Inferred Columns**:
    *   `quotation_id` (Primary Key)
    *   `quotation_number` (Unique Identifier, e.g., 'Q-2023-001')
    *   `customer_name` (Name of the customer)
    *   `quotation_date` (Date the quotation was issued)
    *   `total_amount` (Monetary value of the quotation)
    *   `status` (e.g., 'Draft', 'Pending', 'Approved', 'Rejected')
    *   `created_by` (User who created the quotation)
    *   `created_on` (Timestamp of creation)

---

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the minimal ASP.NET code, no explicit CRUD (Create, Read, Update, Delete) operations are visible.

**Inference:** A "Dashboard" typically implies reading and displaying data. To make it a complete and useful module, we assume standard CRUD capabilities are intended:
*   **Read**: Displaying a list of `Quotation` records (implied by "Dashboard").
*   **Create**: Adding new `Quotation` records.
*   **Update**: Modifying existing `Quotation` records.
*   **Delete**: Removing `Quotation` records.
*   **Validation Logic**: Standard data type validation, uniqueness checks (for `quotation_number`), and required field checks will be implemented in the Django form.

---

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The provided `.aspx` file only contains content placeholders, so no specific ASP.NET UI controls (`GridView`, `TextBox`, `Button`, etc.) are present for direct analysis.

**Inference:** For a "Quotation Dashboard" with inferred CRUD functionality, we anticipate the following UI components will be needed in the Django version:
*   **Data Table**: A grid-like display for listing multiple quotations, suitable for migration to **DataTables**.
*   **Input Fields**: Textboxes for `quotation_number`, `customer_name`, `total_amount`; date pickers for `quotation_date`; dropdowns for `status`. These will be handled by Django forms.
*   **Action Buttons**: Buttons for "Add New Quotation," "Edit," and "Delete" for each row. These will trigger dynamic interactions using **HTMX**.
*   **Modals**: For displaying "Add," "Edit," and "Delete" forms without full page reloads, using **HTMX** for content loading and **Alpine.js** for modal visibility.

---

## Step 4: Generate Django Code

We will create a new Django application, for instance, named `sales`, to house the Quotation module.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `Quotation` model is designed to map to the inferred `tbl_quotation` table. It includes methods for common business logic, demonstrating the "fat model" approach.

```python
# sales/models.py
from django.db import models
from django.utils import timezone
from decimal import Decimal

class Quotation(models.Model):
    # Using 'pk' as the Django primary key convention, assuming it maps to 'quotation_id'
    # db_column is used if the database column name differs from the Django field name
    quotation_number = models.CharField(
        db_column='quotation_number', 
        max_length=50, 
        unique=True,
        verbose_name='Quotation No.'
    )
    customer_name = models.CharField(
        db_column='customer_name', 
        max_length=255,
        verbose_name='Customer Name'
    )
    quotation_date = models.DateField(
        db_column='quotation_date',
        verbose_name='Quotation Date',
        default=timezone.now
    )
    total_amount = models.DecimalField(
        db_column='total_amount', 
        max_digits=10, 
        decimal_places=2,
        verbose_name='Total Amount'
    )
    # Status choices for a better user experience and data integrity
    STATUS_CHOICES = [
        ('Draft', 'Draft'),
        ('Pending', 'Pending Approval'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
        ('Closed', 'Closed'),
    ]
    status = models.CharField(
        db_column='status', 
        max_length=50, 
        choices=STATUS_CHOICES, 
        default='Draft',
        verbose_name='Status'
    )
    created_by = models.CharField( # This could be a ForeignKey to a User model in a real app
        db_column='created_by', 
        max_length=100, 
        blank=True, 
        null=True,
        verbose_name='Created By'
    )
    created_on = models.DateTimeField(
        db_column='created_on', 
        auto_now_add=True,
        verbose_name='Created On'
    )

    class Meta:
        managed = False # Django will not manage this table's schema (assumes it exists)
        db_table = 'tbl_quotation' # Explicitly map to the inferred table name
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'
        ordering = ['-quotation_date', '-quotation_number'] # Default ordering

    def __str__(self):
        return f"{self.quotation_number} - {self.customer_name}"
        
    # Business logic methods go here (Fat Model approach)
    
    def approve_quotation(self):
        """
        Marks the quotation as 'Approved'.
        """
        if self.status == 'Pending':
            self.status = 'Approved'
            self.save()
            return True
        return False

    def reject_quotation(self):
        """
        Marks the quotation as 'Rejected'.
        """
        if self.status == 'Pending':
            self.status = 'Rejected'
            self.save()
            return True
        return False

    def is_editable(self):
        """
        Determines if the quotation can be edited based on its status.
        """
        return self.status in ['Draft', 'Pending']

    def calculate_tax(self, tax_rate_percentage):
        """
        Calculates the tax amount for the quotation.
        """
        if not isinstance(tax_rate_percentage, (int, float)) or tax_rate_percentage < 0:
            raise ValueError("Tax rate must be a non-negative number.")
        tax_amount = self.total_amount * (Decimal(tax_rate_percentage) / 100)
        return tax_amount.quantize(Decimal('0.01'))

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` is used to directly interact with the `Quotation` model. Widgets are styled with Tailwind CSS classes for a modern look.

```python
# sales/forms.py
from django import forms
from .models import Quotation

class QuotationForm(forms.ModelForm):
    class Meta:
        model = Quotation
        fields = ['quotation_number', 'customer_name', 'quotation_date', 'total_amount', 'status']
        widgets = {
            'quotation_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., Q-2023-001'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., ABC Company'}),
            'quotation_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'type': 'date'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01', 'placeholder': 'e.g., 1234.56'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_quotation_number(self):
        quotation_number = self.cleaned_data['quotation_number']
        # Example custom validation: ensure quotation number is in a specific format
        if not quotation_number.startswith('Q-'):
            raise forms.ValidationError("Quotation number must start with 'Q-'.")
        return quotation_number

    # Add custom validation methods here as per ASP.NET logic
    def clean(self):
        cleaned_data = super().clean()
        total_amount = cleaned_data.get('total_amount')
        if total_amount and total_amount <= 0:
            self.add_error('total_amount', "Total amount must be greater than zero.")
        return cleaned_data
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

The views are kept thin, delegating business logic to the model. HTMX headers are used to trigger client-side updates (e.g., refreshing the DataTables list) without full page reloads. A `TablePartialView` is added to support HTMX loading of the DataTable.

```python
# sales/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Quotation
from .forms import QuotationForm

class QuotationListView(ListView):
    model = Quotation
    template_name = 'sales/quotation/list.html'
    context_object_name = 'quotations' # Matches MODEL_NAME_PLURAL_LOWER

class QuotationTablePartialView(ListView):
    model = Quotation
    template_name = 'sales/quotation/_quotation_table.html'
    context_object_name = 'quotations' # Matches MODEL_NAME_PLURAL_LOWER

class QuotationCreateView(CreateView):
    model = Quotation
    form_class = QuotationForm
    template_name = 'sales/quotation/form.html'
    success_url = reverse_lazy('quotation_list') # Redirect on non-HTMX request

    def form_valid(self, form):
        # Example of model business logic before saving, if any
        # form.instance.created_by = self.request.user.username # Assuming user is logged in
        response = super().form_valid(form)
        messages.success(self.request, 'Quotation added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response with HX-Trigger header for HTMX
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList' # Trigger refresh on the client side
                }
            )
        return response

class QuotationUpdateView(UpdateView):
    model = Quotation
    form_class = QuotationForm
    template_name = 'sales/quotation/form.html'
    success_url = reverse_lazy('quotation_list') # Redirect on non-HTMX request

    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # Example of applying business logic from model: check if editable
        if not obj.is_editable():
            messages.error(self.request, "This quotation cannot be edited in its current status.")
            raise PermissionDenied("Quotation not editable.") # Or redirect
        return obj

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Quotation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return response

class QuotationDeleteView(DeleteView):
    model = Quotation
    template_name = 'sales/quotation/confirm_delete.html'
    success_url = reverse_lazy('quotation_list') # Redirect on non-HTMX request

    def delete(self, request, *args, **kwargs):
        # Example of applying business logic from model: check if deletable
        self.object = self.get_object()
        if not self.object.is_editable(): # Assuming same rule for delete
            messages.error(self.request, "This quotation cannot be deleted in its current status.")
            return HttpResponse(status=403) # Forbidden
            
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Quotation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates utilize Django's template inheritance, HTMX for dynamic content, Alpine.js for UI state (like modal visibility), and DataTables for interactive lists.

**`sales/quotation/list.html`**
This is the main dashboard page, extending `core/base.html`.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Quotation Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'quotation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Quotation
        </button>
    </div>
    
    <!-- Container for the DataTable, updated via HTMX -->
    <div id="quotationTable-container"
         hx-trigger="load, refreshQuotationList from:body"
         hx-get="{% url 'quotation_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Quotations...</p>
        </div>
    </div>
    
    <!-- Modal for form display (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-3xl w-full mx-4 my-8 transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed.
        // No specific components needed here as HTMX drives interactions.
    });
</script>
{% endblock %}
```

**`sales/quotation/_quotation_table.html`**
This partial template is loaded dynamically by HTMX to display the DataTables.

```html
<!-- sales/quotation/_quotation_table.html -->
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <table id="quotationTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No.</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in quotations %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.quotation_number }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.customer_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.quotation_date|date:"Y-m-d" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">${{ obj.total_amount }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.status == 'Approved' %}bg-green-100 text-green-800
                        {% elif obj.status == 'Pending' %}bg-yellow-100 text-yellow-800
                        {% elif obj.status == 'Rejected' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ obj.status }}
                    </span>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-4"
                        hx-get="{% url 'quotation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'quotation_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No quotations found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#quotationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true
        });
    });
</script>
```

**`sales/quotation/form.html`**
This partial template renders the form within the modal.

```html
<!-- sales/quotation/form.html -->
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Quotation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {% for field in form %}
            <div class="col-span-1">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-2 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Quotation
            </button>
        </div>
    </form>
</div>
```

**`sales/quotation/confirm_delete.html`**
This partial template provides the delete confirmation within the modal.

```html
<!-- sales/quotation/confirm_delete.html -->
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-lg text-gray-700 mb-8">
        Are you sure you want to delete quotation **"{{ object.quotation_number }} - {{ object.customer_name }}"**?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'quotation_delete' object.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns are set up within the `sales` application's `urls.py` file, enabling access to all CRUD views and the HTMX partial for the DataTables.

```python
# sales/urls.py
from django.urls import path
from .views import (
    QuotationListView, 
    QuotationCreateView, 
    QuotationUpdateView, 
    QuotationDeleteView,
    QuotationTablePartialView
)

urlpatterns = [
    # Main list view (dashboard)
    path('quotations/', QuotationListView.as_view(), name='quotation_list'),
    
    # HTMX partial for refreshing the table
    path('quotations/table/', QuotationTablePartialView.as_view(), name='quotation_table'),
    
    # CRUD operations loaded into modal via HTMX
    path('quotations/add/', QuotationCreateView.as_view(), name='quotation_add'),
    path('quotations/edit/<int:pk>/', QuotationUpdateView.as_view(), name='quotation_edit'),
    path('quotations/delete/<int:pk>/', QuotationDeleteView.as_view(), name='quotation_delete'),
]

```
Remember to include these `sales/urls.py` patterns in your project's main `urls.py`:
```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('sales/', include('sales.urls')), # Include the sales app URLs
    # ... other project-level URLs
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests cover model methods and properties, while integration tests validate all view functionalities, including HTMX responses and success messages.

```python       
# sales/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from .models import Quotation
from django.core.exceptions import PermissionDenied

class QuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a single test quotation for all tests
        cls.quotation1 = Quotation.objects.create(
            quotation_number='Q-TEST-001',
            customer_name='Test Customer A',
            quotation_date=timezone.now().date(),
            total_amount=Decimal('100.00'),
            status='Pending',
            created_by='test_user'
        )
        cls.quotation2_draft = Quotation.objects.create(
            quotation_number='Q-TEST-002',
            customer_name='Test Customer B',
            quotation_date=timezone.now().date(),
            total_amount=Decimal('250.50'),
            status='Draft',
            created_by='another_user'
        )
        cls.quotation3_approved = Quotation.objects.create(
            quotation_number='Q-TEST-003',
            customer_name='Test Customer C',
            quotation_date=timezone.now().date(),
            total_amount=Decimal('500.00'),
            status='Approved',
            created_by='admin_user'
        )
  
    def test_quotation_creation(self):
        obj = Quotation.objects.get(pk=self.quotation1.pk)
        self.assertEqual(obj.quotation_number, 'Q-TEST-001')
        self.assertEqual(obj.customer_name, 'Test Customer A')
        self.assertEqual(obj.total_amount, Decimal('100.00'))
        self.assertEqual(obj.status, 'Pending')
        self.assertIsNotNone(obj.created_on)
        
    def test_quotation_number_label(self):
        obj = Quotation.objects.get(pk=self.quotation1.pk)
        field_label = obj._meta.get_field('quotation_number').verbose_name
        self.assertEqual(field_label, 'Quotation No.')
        
    def test_customer_name_max_length(self):
        obj = Quotation.objects.get(pk=self.quotation1.pk)
        max_length = obj._meta.get_field('customer_name').max_length
        self.assertEqual(max_length, 255)

    def test_str_method(self):
        obj = Quotation.objects.get(pk=self.quotation1.pk)
        self.assertEqual(str(obj), 'Q-TEST-001 - Test Customer A')

    # Test business logic methods
    def test_approve_quotation(self):
        obj = Quotation.objects.get(pk=self.quotation1.pk) # Status 'Pending'
        self.assertTrue(obj.approve_quotation())
        self.assertEqual(obj.status, 'Approved')
        
        # Should not approve if already approved
        self.assertFalse(obj.approve_quotation()) 
        
        obj_draft = Quotation.objects.get(pk=self.quotation2_draft.pk) # Status 'Draft'
        self.assertFalse(obj_draft.approve_quotation()) # Cannot approve directly from Draft
        
    def test_reject_quotation(self):
        obj = Quotation.objects.get(pk=self.quotation1.pk) # Status 'Pending'
        self.assertTrue(obj.reject_quotation())
        self.assertEqual(obj.status, 'Rejected')

        # Should not reject if already rejected
        self.assertFalse(obj.reject_quotation())
        
        obj_draft = Quotation.objects.get(pk=self.quotation2_draft.pk) # Status 'Draft'
        self.assertFalse(obj_draft.reject_quotation()) # Cannot reject directly from Draft

    def test_is_editable(self):
        self.assertTrue(self.quotation1.is_editable()) # Pending
        self.assertTrue(self.quotation2_draft.is_editable()) # Draft
        self.assertFalse(self.quotation3_approved.is_editable()) # Approved
        
    def test_calculate_tax(self):
        obj = Quotation.objects.get(pk=self.quotation1.pk)
        tax_rate = 10
        expected_tax = Decimal('10.00') # 10% of 100.00
        self.assertEqual(obj.calculate_tax(tax_rate), expected_tax)

        tax_rate_float = 7.5
        expected_tax_float = Decimal('7.50')
        self.assertEqual(obj.calculate_tax(tax_rate_float), expected_tax_float)

        with self.assertRaises(ValueError):
            obj.calculate_tax(-5)
        with self.assertRaises(ValueError):
            obj.calculate_tax('abc')

class QuotationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.quotation = Quotation.objects.create(
            quotation_number='Q-VIEW-001',
            customer_name='View Test Customer',
            quotation_date=timezone.now().date(),
            total_amount=Decimal('99.99'),
            status='Draft',
            created_by='view_test_user'
        )
        cls.quotation_non_editable = Quotation.objects.create(
            quotation_number='Q-VIEW-002',
            customer_name='Non-Editable Customer',
            quotation_date=timezone.now().date(),
            total_amount=Decimal('150.00'),
            status='Approved',
            created_by='view_test_user'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('quotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/list.html')
        self.assertIn('quotations', response.context)
        self.assertContains(response, 'Quotation Dashboard') # Check for specific content
        self.assertContains(response, self.quotation.customer_name) # Check if object is displayed

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('quotation_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/_quotation_table.html')
        self.assertIn('quotations', response.context)
        self.assertContains(response, self.quotation.customer_name) # Ensure data is present
        self.assertContains(response, '<table id="quotationTable"') # Ensure DataTable structure

    def test_create_view_get(self):
        response = self.client.get(reverse('quotation_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Quotation')
        
    def test_create_view_post_success(self):
        initial_count = Quotation.objects.count()
        data = {
            'quotation_number': 'Q-NEW-001',
            'customer_name': 'New Customer Inc.',
            'quotation_date': '2023-11-01',
            'total_amount': '500.00',
            'status': 'Draft',
        }
        response = self.client.post(reverse('quotation_add'), data, follow=True)
        # Check for redirect after successful creation (non-HTMX)
        self.assertEqual(response.status_code, 200) # Follows redirect to list view
        self.assertContains(response, 'Quotation added successfully.')
        self.assertEqual(Quotation.objects.count(), initial_count + 1)
        self.assertTrue(Quotation.objects.filter(quotation_number='Q-NEW-001').exists())

    def test_create_view_post_htmx_success(self):
        initial_count = Quotation.objects.count()
        data = {
            'quotation_number': 'Q-HX-001',
            'customer_name': 'HTMX Customer Corp.',
            'quotation_date': '2023-11-02',
            'total_amount': '750.50',
            'status': 'Pending',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        self.assertEqual(Quotation.objects.count(), initial_count + 1)
        self.assertTrue(Quotation.objects.filter(quotation_number='Q-HX-001').exists())

    def test_create_view_post_invalid(self):
        initial_count = Quotation.objects.count()
        data = {
            'quotation_number': 'INVALID-FORMAT', # Missing 'Q-'
            'customer_name': '', # Missing required field
            'quotation_date': 'invalid-date',
            'total_amount': '0.00', # Invalid amount
            'status': 'Draft',
        }
        response = self.client.post(reverse('quotation_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales/quotation/form.html')
        self.assertContains(response, 'Quotation number must start with &#x27;Q-&#x27;.')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid date.')
        self.assertContains(response, 'Total amount must be greater than zero.')
        self.assertEqual(Quotation.objects.count(), initial_count) # No new object created
        
    def test_update_view_get(self):
        response = self.client.get(reverse('quotation_edit', args=[self.quotation.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.quotation)
        self.assertContains(response, 'Edit Quotation')

    def test_update_view_get_non_editable(self):
        response = self.client.get(reverse('quotation_edit', args=[self.quotation_non_editable.pk]))
        # Assuming PermissionDenied raises 403 or redirects
        self.assertEqual(response.status_code, 403) # Or 302 if redirect implemented
        self.assertContains(response, "This quotation cannot be edited in its current status.") # Message should be displayed

    def test_update_view_post_success(self):
        data = {
            'quotation_number': self.quotation.quotation_number, # Keep same unique number
            'customer_name': 'Updated Customer Name',
            'quotation_date': str(self.quotation.quotation_date),
            'total_amount': '123.45',
            'status': 'Pending',
        }
        response = self.client.post(reverse('quotation_edit', args=[self.quotation.pk]), data, follow=True)
        self.assertEqual(response.status_code, 200) # Follows redirect
        self.assertContains(response, 'Quotation updated successfully.')
        self.quotation.refresh_from_db()
        self.assertEqual(self.quotation.customer_name, 'Updated Customer Name')
        self.assertEqual(self.quotation.total_amount, Decimal('123.45'))

    def test_update_view_post_htmx_success(self):
        data = {
            'quotation_number': self.quotation.quotation_number,
            'customer_name': 'HTMX Updated Customer',
            'quotation_date': str(self.quotation.quotation_date),
            'total_amount': '200.00',
            'status': 'Approved',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_edit', args=[self.quotation.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        self.quotation.refresh_from_db()
        self.assertEqual(self.quotation.customer_name, 'HTMX Updated Customer')
        self.assertEqual(self.quotation.status, 'Approved')
        
    def test_delete_view_get(self):
        response = self.client.get(reverse('quotation_delete', args=[self.quotation.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.quotation)
        self.assertContains(response, 'Confirm Deletion')
        
    def test_delete_view_post_success(self):
        quotation_to_delete = Quotation.objects.create(
            quotation_number='Q-DEL-001',
            customer_name='Customer for Delete',
            quotation_date=timezone.now().date(),
            total_amount=Decimal('10.00'),
            status='Draft',
            created_by='del_user'
        )
        initial_count = Quotation.objects.count()
        response = self.client.post(reverse('quotation_delete', args=[quotation_to_delete.pk]), follow=True)
        self.assertEqual(response.status_code, 200) # Follows redirect
        self.assertContains(response, 'Quotation deleted successfully.')
        self.assertEqual(Quotation.objects.count(), initial_count - 1)
        self.assertFalse(Quotation.objects.filter(pk=quotation_to_delete.pk).exists())

    def test_delete_view_post_htmx_success(self):
        quotation_to_delete = Quotation.objects.create(
            quotation_number='Q-DEL-HX',
            customer_name='HTMX Delete Customer',
            quotation_date=timezone.now().date(),
            total_amount=Decimal('20.00'),
            status='Draft',
            created_by='del_user_hx'
        )
        initial_count = Quotation.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_delete', args=[quotation_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        self.assertEqual(Quotation.objects.count(), initial_count - 1)
        self.assertFalse(Quotation.objects.filter(pk=quotation_to_delete.pk).exists())

    def test_delete_view_post_non_editable(self):
        initial_count = Quotation.objects.count()
        response = self.client.post(reverse('quotation_delete', args=[self.quotation_non_editable.pk]))
        self.assertEqual(response.status_code, 403) # Forbidden
        self.assertContains(response, "This quotation cannot be deleted in its current status.")
        self.assertEqual(Quotation.objects.count(), initial_count) # Object not deleted
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided templates demonstrate core HTMX and Alpine.js integration patterns:
*   **HTMX for Dynamic Content**:
    *   The `quotationTable-container` in `list.html` uses `hx-get="{% url 'quotation_table' %}"` and `hx-trigger="load, refreshQuotationList from:body"` to load the table dynamically on page load and whenever `refreshQuotationList` event is triggered.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to load forms or confirmation dialogs into the `#modalContent` div.
    *   Form submissions (`hx-post`) in `form.html` and `confirm_delete.html` swap `none` (meaning HTMX doesn't update the target, as the server sends a 204 No Content response) and then trigger the `refreshQuotationList` event and hide the modal via `hx-on::after-request` or `_ = on click`.
*   **Alpine.js for UI State**:
    *   The modal (`#modal`) in `list.html` uses `x-data="{ showModal: false }"` (implicitly handled by `_ = on click add/remove .is-active to #modal`) to manage its visibility. The `hidden` class is toggled to show/hide the modal.
    *   `_ = on click if event.target.id == 'modal' remove .is-active from me` on the modal backdrop allows clicking outside to close it.
*   **DataTables**:
    *   The `_quotation_table.html` partial explicitly includes the `<table>` with `id="quotationTable"`.
    *   A JavaScript block within this partial uses `$(document).ready(function() { $('#quotationTable').DataTable({...}); });` to initialize DataTables after the table HTML is loaded into the DOM by HTMX. This assumes jQuery and DataTables JavaScript are loaded in the `core/base.html` template.

This setup ensures that all primary user interactions (adding, editing, deleting, and refreshing the list) occur without full page reloads, providing a smooth and modern user experience. The separation of concerns is maintained, with HTMX managing network requests and partial updates, Alpine.js handling simple UI state, and Django serving dynamic content and processing forms.

## Final Notes

*   **Placeholders**: All bracketed placeholders like `[MODEL_NAME]`, `[APP_NAME]`, `[TABLE_NAME]`, `[FIELD1]`, etc., have been replaced with concrete values inferred from the `Quotation_Dashboard` context (e.g., `Quotation`, `sales`, `tbl_quotation`, `quotation_number`, `customer_name`, etc.).
*   **DRY Templates**: The use of `_quotation_table.html`, `form.html`, and `confirm_delete.html` as partials loaded via HTMX ensures components are reusable and avoids code duplication. The main `list.html` focuses on the page structure and entry points.
*   **Fat Model, Thin View**: Business logic examples (like `is_editable`, `approve_quotation`, `calculate_tax`) have been added to the `Quotation` model. The views primarily handle HTTP requests, form validation, and delegating to model methods, keeping their code concise.
*   **Comprehensive Tests**: Unit tests for model methods and integration tests for all CRUD views, including specific HTMX request/response handling, ensure high code quality and maintainability.
*   **HTMX/Alpine.js**: The entire UI interaction flow for CRUD operations is designed around HTMX for efficient partial page updates and Alpine.js for client-side interactivity, minimizing traditional JavaScript needs. The DataTables integration provides powerful client-side data management.