This comprehensive modernization plan outlines the strategy for transitioning the ASP.NET `WorkOrder_Print_Details` functionality to a modern Django application. The core challenge here is replacing the proprietary Crystal Reports viewer with a web-native, HMTX-driven HTML report, ensuring all data transformations and lookups from the original C# code are handled efficiently within Django's "fat model" paradigm.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Business Value of Django Modernization:

Migrating from legacy ASP.NET with Crystal Reports to Django with HTMX and Alpine.js delivers significant business benefits:

1.  **Reduced Licensing Costs:** Eliminate expensive Crystal Reports licenses by moving to open-source, web-native reporting.
2.  **Enhanced User Experience:** Provide interactive, fast-loading reports directly in the browser without reliance on plug-ins, improving user satisfaction and productivity.
3.  **Future-Proof Architecture:** Transition to a modern, maintainable, and scalable Python/Django stack that is easier to develop, secure, and adapt to future business needs.
4.  **Simplified Deployment & Maintenance:** A unified web stack reduces complexities associated with disparate reporting tools, streamlining deployment and ongoing support.
5.  **Improved Development Agility:** Leverage Django's rapid development capabilities and a vibrant open-source ecosystem to accelerate future feature delivery and iterations.
6.  **Better Data Integration:** Native Python data handling makes it easier to integrate with various data sources and perform complex data transformations directly within the application logic.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The ASP.NET code primarily fetches data from `SD_Cust_WorkOrder_Master` and `SD_Cust_WorkOrder_Products_Details`. It also performs lookups against `tblSD_WO_Category`, `tblSD_WO_SubCategory`, `tblMM_Buyer_Master`, and `tblHR_OfficeStaff`, and uses utility functions (`fun.getCity`, `fun.getState`, `fun.getCountry`, `fun.CompAdd`, `fun.getCompany`) which imply underlying tables for geographical data and company information.

**Inferred Tables and Key Columns:**

1.  **`SD_Cust_WorkOrder_Master` (Main Work Order Details)**
    *   `Id` (Primary Key, used in `Request.QueryString["Id"]`)
    *   `POId`, `PONo`, `EnqId`, `CustomerId` (used in `WHERE` clause)
    *   `CompId` (Company ID, used in `Session["compid"]`)
    *   `ShippingCity`, `ShippingState`, `ShippingCountry` (IDs for lookups)
    *   `TaskWorkOrderDate`, `TaskTargetDAP_FDate`, `TaskTargetDAP_TDate`, `TaskDesignFinalization_FDate`, `TaskDesignFinalization_TDate`, `TaskTargetManufg_FDate`, `TaskTargetManufg_TDate`, `TaskTargetTryOut_FDate`, `TaskTargetTryOut_TDate`, `TaskTargetDespach_FDate`, `TaskTargetDespach_TDate`, `TaskTargetInstalation_FDate`, `TaskTargetInstalation_TDate`, `TaskTargetAssembly_FDate`, `TaskTargetAssembly_TDate`, `TaskCustInspection_FDate`, `TaskCustInspection_TDate`, `ManufMaterialDate`, `BoughtoutMaterialDate` (Date fields, require formatting)
    *   `InstractionPrimerPainting`, `InstractionPainting`, `InstractionSelfCertRept` (Boolean/integer flags for instructions)
    *   `InstractionOther` (Text field for additional instructions)
    *   `CId` (Category ID for `tblSD_WO_Category`)
    *   `SCId` (SubCategory ID for `tblSD_WO_SubCategory`)
    *   `Buyer` (Buyer ID for `tblMM_Buyer_Master`)
    *   Many other fields (implied by `SELECT *`) will be included in the model.

2.  **`SD_Cust_WorkOrder_Products_Details` (Work Order Products)**
    *   `MId` (Foreign key to `SD_Cust_WorkOrder_Master.Id`)
    *   `CompId` (Company ID)
    *   `ItemCode`
    *   `Description`
    *   `Qty`

3.  **Lookup Tables (Inferred, will be mapped to simple models for data access)**
    *   `tblCity` (for `ShippingCity` lookup)
    *   `tblState` (for `ShippingState` lookup)
    *   `tblCountry` (for `ShippingCountry` lookup)
    *   `tblCompany` (for `CompId` lookup)
    *   `tblSD_WO_Category` (`CId`, `Symbol`, `CName`)
    *   `tblSD_WO_SubCategory` (`SCId`, `Symbol`, `SCName`)
    *   `tblMM_Buyer_Master` (`Id`, `Category`, `Nos`, `EmpId`)
    *   `tblHR_OfficeStaff` (`EmpId`, `EmployeeName`)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
This ASP.NET page (`WorkOrder_Print_Details.aspx`) serves as a **reporting/display-only** page for a specific "Work Order." It is NOT a CRUD (Create, Read, Update, Delete) page for `SD_Cust_WorkOrder_Master`.

*   **Read Operation:** The primary function is to read and present a comprehensive report of a single Work Order based on multiple query parameters (`Id`, `POId`, `PONo`, `EnqId`, `CustomerId`).
*   **Data Aggregation & Transformation:** It fetches data from `SD_Cust_WorkOrder_Master`, `SD_Cust_WorkOrder_Products_Details`, and various lookup tables. It performs significant data transformation, such as:
    *   Converting IDs to names (city, state, country, category, subcategory, buyer, company).
    *   Formatting dates from database strings to display format.
    *   Concatenating instruction flags into a single string.
*   **Navigation:** A "Cancel" button redirects the user to a list of Work Orders.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
*   **`CR:CrystalReportViewer`:** This is the main display component. In Django, this will be replaced by a custom-rendered HTML template that formats the data for viewing and printing.
*   **`asp:Button ID="btnCl" Text="Cancel"`:** A simple button for navigation. In Django, this will become an HTMX-powered button (`hx-get`) that navigates back to the work order list view.
*   **`loadingNotifier.js`:** This indicates a need for a loading indicator during data retrieval, which HTMX handles elegantly with its `hx-indicator` attribute.
*   The `Panel1` with `ScrollBars="Auto"` implies the report content can be long and should be scrollable.

### Step 4: Generate Django Code

Given the analysis, we will create Django models for the main entities and lookup tables, and a single Django `DetailView` to handle the report generation. The "fat model" approach will be crucial here, moving all data lookups and transformations into model methods and properties.

**Assumed Django App Name:** `workorders`

---

### 4.1 Models (`workorders/models.py`)

This section defines the Django models that map to your existing database tables. We'll include methods on the `WorkOrder` model to encapsulate the complex lookups and data transformations currently found in your C# code-behind, adhering to the "fat model" principle.

```python
from django.db import models
from django.conf import settings # Assuming company_id is available in settings or a user profile

# --- Lookup Models (Simplified for demonstration, extend as needed) ---
# These models map to your lookup tables for dynamic data retrieval.
# They are marked as managed=False as they point to existing tables.

class City(models.Model):
    # Inferring columns based on usage (e.g., fun.getCity(id, 1))
    id = models.IntegerField(db_column='CityId', primary_key=True) # Example: Primary key column in City table
    name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity' # Adjust if your city table has a different name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='StateId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState' # Adjust if your state table has a different name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class Country(models.Model):
    id = models.IntegerField(db_column='CountryId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry' # Adjust if your country table has a different name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255)
    address = models.CharField(db_column='CompanyAddress', max_length=500) # Inferred from fun.CompAdd

    class Meta:
        managed = False
        db_table = 'tblCompany' # Adjust if your company table has a different name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class WOCategory(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'Work Order Category'
        verbose_name_plural = 'Work Order Categories'

    def __str__(self):
        return f"{self.symbol} - {self.name}"

class WOSubCategory(models.Model):
    id = models.IntegerField(db_column='SCId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='SCName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_SubCategory'
        verbose_name = 'Work Order SubCategory'
        verbose_name_plural = 'Work Order SubCategories'

    def __str__(self):
        return f"{self.symbol} - {self.name}"

class OfficeStaff(models.Model):
    id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is char/varchar
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class Buyer(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=100)
    nos = models.IntegerField(db_column='Nos')
    emp_id = models.CharField(db_column='EmpId', max_length=50) # References OfficeStaff

    class Meta:
        managed = False
        db_table = 'tblMM_Buyer_Master'
        verbose_name = 'Buyer'
        verbose_name_plural = 'Buyers'

    def __str__(self):
        try:
            staff = OfficeStaff.objects.get(id=self.emp_id)
            return f"{self.category}{self.nos} - {staff.employee_name} [{self.emp_id}]"
        except OfficeStaff.DoesNotExist:
            return f"{self.category}{self.nos} - NA [{self.emp_id}]"

# --- Main Work Order Models ---

class WorkOrder(models.Model):
    # Primary key from the ASP.NET code
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_id = models.CharField(db_column='POId', max_length=50)
    po_no = models.CharField(db_column='PONo', max_length=50)
    enq_id = models.CharField(db_column='EnqId', max_length=50)
    customer_id = models.CharField(db_column='CustomerId', max_length=50)
    company_id = models.IntegerField(db_column='CompId')

    # Shipping Address related fields
    shipping_city_id = models.IntegerField(db_column='ShippingCity', null=True, blank=True)
    shipping_state_id = models.IntegerField(db_column='ShippingState', null=True, blank=True)
    shipping_country_id = models.IntegerField(db_column='ShippingCountry', null=True, blank=True)

    # Date fields (assuming they are stored as strings or specific date types in DB)
    # Using CharField to match potential string storage in legacy DB, convert to date objects in properties
    task_work_order_date = models.CharField(db_column='TaskWorkOrderDate', max_length=50, null=True, blank=True)
    task_target_dap_fdate = models.CharField(db_column='TaskTargetDAP_FDate', max_length=50, null=True, blank=True)
    task_target_dap_tdate = models.CharField(db_column='TaskTargetDAP_TDate', max_length=50, null=True, blank=True)
    task_design_finalization_fdate = models.CharField(db_column='TaskDesignFinalization_FDate', max_length=50, null=True, blank=True)
    task_design_finalization_tdate = models.CharField(db_column='TaskDesignFinalization_TDate', max_length=50, null=True, blank=True)
    task_target_manufg_fdate = models.CharField(db_column='TaskTargetManufg_FDate', max_length=50, null=True, blank=True)
    task_target_manufg_tdate = models.CharField(db_column='TaskTargetManufg_TDate', max_length=50, null=True, blank=True)
    task_target_try_out_fdate = models.CharField(db_column='TaskTargetTryOut_FDate', max_length=50, null=True, blank=True)
    task_target_try_out_tdate = models.CharField(db_column='TaskTargetTryOut_TDate', max_length=50, null=True, blank=True)
    task_target_despach_fdate = models.CharField(db_column='TaskTargetDespach_FDate', max_length=50, null=True, blank=True)
    task_target_despach_tdate = models.CharField(db_column='TaskTargetDespach_TDate', max_length=50, null=True, blank=True)
    task_target_instalation_fdate = models.CharField(db_column='TaskTargetInstalation_FDate', max_length=50, null=True, blank=True)
    task_target_instalation_tdate = models.CharField(db_column='TaskTargetInstalation_TDate', max_length=50, null=True, blank=True)
    task_target_assembly_fdate = models.CharField(db_column='TaskTargetAssembly_FDate', max_length=50, null=True, blank=True)
    task_target_assembly_tdate = models.CharField(db_column='TaskTargetAssembly_TDate', max_length=50, null=True, blank=True)
    task_cust_inspection_fdate = models.CharField(db_column='TaskCustInspection_FDate', max_length=50, null=True, blank=True)
    task_cust_inspection_tdate = models.CharField(db_column='TaskCustInspection_TDate', max_length=50, null=True, blank=True)
    manuf_material_date = models.CharField(db_column='ManufMaterialDate', max_length=50, null=True, blank=True)
    boughtout_material_date = models.CharField(db_column='BoughtoutMaterialDate', max_length=50, null=True, blank=True)

    # Instruction fields (assuming 0 or 1 for boolean flags)
    instraction_primer_painting = models.IntegerField(db_column='InstractionPrimerPainting', default=0)
    instraction_painting = models.IntegerField(db_column='InstractionPainting', default=0)
    instraction_self_cert_rept = models.IntegerField(db_column='InstractionSelfCertRept', default=0)
    instraction_other = models.CharField(db_column='InstractionOther', max_length=500, null=True, blank=True)

    # Category and Buyer fields
    category_id = models.IntegerField(db_column='CId', null=True, blank=True)
    sub_category_id = models.IntegerField(db_column='SCId', null=True, blank=True)
    buyer_id = models.IntegerField(db_column='Buyer', null=True, blank=True)

    # Add other fields identified by SELECT * as needed
    # For instance:
    # other_field_1 = models.CharField(db_column='OtherField1', max_length=255, null=True, blank=True)
    # other_field_2 = models.IntegerField(db_column='OtherField2', null=True, blank=True)

    class Meta:
        managed = False # Important: This tells Django not to manage table creation/modification
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"Work Order {self.po_no} ({self.id})"

    # --- Business Logic / Helper Methods (Fat Model) ---
    # These methods encapsulate the logic previously in clsFunctions and C# Page_Init

    def _format_date_dmy(self, date_str, to_date=False):
        """Helper to format dates from YYYY-MM-DD or other formats to DD-MM-YYYY."""
        if not date_str:
            return ""
        try:
            # Attempt to parse common date formats (e.g., YYYY-MM-DD, M/D/YYYY)
            if ' ' in date_str: # Handle dates with time component
                dt_obj = models.DateField().to_python(date_str.split(' ')[0])
            else:
                dt_obj = models.DateField().to_python(date_str)
            return dt_obj.strftime("%d-%m-%Y")
        except (ValueError, TypeError):
            # Fallback for unexpected formats or if conversion fails
            return date_str # Return original string if cannot be parsed
            
    @property
    def formatted_task_work_order_date(self):
        return self._format_date_dmy(self.task_work_order_date)

    @property
    def formatted_task_target_dap_fdate(self):
        return self._format_date_dmy(self.task_target_dap_fdate)

    @property
    def formatted_task_target_dap_tdate(self):
        return self._format_date_dmy(self.task_target_dap_tdate, to_date=True)

    @property
    def formatted_task_design_finalization_fdate(self):
        return self._format_date_dmy(self.task_design_finalization_fdate)

    @property
    def formatted_task_design_finalization_tdate(self):
        return self._format_date_dmy(self.task_design_finalization_tdate, to_date=True)

    @property
    def formatted_task_target_manufg_fdate(self):
        return self._format_date_dmy(self.task_target_manufg_fdate)

    @property
    def formatted_task_target_manufg_tdate(self):
        return self._format_date_dmy(self.task_target_manufg_tdate, to_date=True)
    
    @property
    def formatted_task_target_try_out_fdate(self):
        return self._format_date_dmy(self.task_target_try_out_fdate)

    @property
    def formatted_task_target_try_out_tdate(self):
        return self._format_date_dmy(self.task_target_try_out_tdate, to_date=True)

    @property
    def formatted_task_target_despach_fdate(self):
        return self._format_date_dmy(self.task_target_despach_fdate)

    @property
    def formatted_task_target_despach_tdate(self):
        return self._format_date_dmy(self.task_target_despach_tdate, to_date=True)

    @property
    def formatted_task_target_instalation_fdate(self):
        return self._format_date_dmy(self.task_target_instalation_fdate)

    @property
    def formatted_task_target_instalation_tdate(self):
        return self._format_date_dmy(self.task_target_instalation_tdate, to_date=True)

    @property
    def formatted_task_target_assembly_fdate(self):
        return self._format_date_dmy(self.task_target_assembly_fdate)

    @property
    def formatted_task_target_assembly_tdate(self):
        return self._format_date_dmy(self.task_target_assembly_tdate, to_date=True)

    @property
    def formatted_task_cust_inspection_fdate(self):
        return self._format_date_dmy(self.task_cust_inspection_fdate)

    @property
    def formatted_task_cust_inspection_tdate(self):
        return self._format_date_dmy(self.task_cust_inspection_tdate, to_date=True)

    @property
    def formatted_manuf_material_date(self):
        return self._format_date_dmy(self.manuf_material_date, to_date=True)

    @property
    def formatted_boughtout_material_date(self):
        return self._format_date_dmy(self.boughtout_material_date, to_date=True)


    @property
    def shipping_city_name(self):
        try:
            return City.objects.get(id=self.shipping_city_id).name
        except City.DoesNotExist:
            return "N/A"

    @property
    def shipping_state_name(self):
        try:
            return State.objects.get(id=self.shipping_state_id).name
        except State.DoesNotExist:
            return "N/A"

    @property
    def shipping_country_name(self):
        try:
            return Country.objects.get(id=self.shipping_country_id).name
        except Country.DoesNotExist:
            return "N/A"

    @property
    def company_name(self):
        try:
            return Company.objects.get(id=self.company_id).name
        except Company.DoesNotExist:
            return "N/A"

    @property
    def company_address(self):
        try:
            return Company.objects.get(id=self.company_id).address
        except Company.DoesNotExist:
            return "N/A"

    @property
    def category_display(self):
        try:
            return WOCategory.objects.get(id=self.category_id).__str__()
        except WOCategory.DoesNotExist:
            return "NA"

    @property
    def sub_category_display(self):
        try:
            return WOSubCategory.objects.get(id=self.sub_category_id).__str__()
        except WOSubCategory.DoesNotExist:
            return "NA"

    @property
    def buyer_display(self):
        try:
            return Buyer.objects.get(id=self.buyer_id).__str__()
        except Buyer.DoesNotExist:
            return "NA"

    @property
    def instruction_summary(self):
        instructions = []
        if self.instraction_primer_painting == 1:
            instructions.append("Primer Painting")
        if self.instraction_painting == 1:
            instructions.append("Painting")
        if self.instraction_self_cert_rept == 1:
            instructions.append("Self Certification Report")
        if self.instraction_other:
            instructions.append(self.instraction_other)
        return ", ".join(instructions) if instructions else "No special instructions."


class WorkOrderProduct(models.Model):
    # This model represents the SD_Cust_WorkOrder_Products_Details table
    # Assuming MId is the foreign key to WorkOrder.Id
    id = models.AutoField(db_column='PId', primary_key=True) # Assuming products have their own PK
    work_order = models.ForeignKey(
        WorkOrder, 
        on_delete=models.DO_NOTHING, # Or models.CASCADE, depending on DB behavior
        db_column='MId', 
        related_name='products'
    )
    company_id = models.IntegerField(db_column='CompId') # Redundant if using WorkOrder.company_id, but keeping for direct mapping
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    description = models.CharField(db_column='Description', max_length=500)
    quantity = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=2) # Adjust precision/scale as needed

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Products_Details'
        verbose_name = 'Work Order Product'
        verbose_name_plural = 'Work Order Products'

    def __str__(self):
        return f"{self.item_code} - {self.description}"

```

---

### 4.2 Forms (`workorders/forms.py`)

**Task:** Define a Django form for user input.

**Analysis:**
The ASP.NET `WorkOrder_Print_Details.aspx` page is a report display page, not an input form for `WorkOrder` creation or modification. Therefore, **no Django form is required** for this specific conversion. If there were other pages in the original application for editing `WorkOrder` details, a `WorkOrderForm` would be created there.

```python
# No forms.py needed for this specific report viewing functionality.
# This file would be created if there were input/edit capabilities.
```

---

### 4.3 Views (`workorders/views.py`)

**Task:** Implement the report display logic using a Django Class-Based View.

**Analysis:**
We will use a `DetailView` to fetch the specific `WorkOrder` object and pass it, along with its related products and derived report parameters (from model methods), to the template. The view will remain thin, delegating complex data processing to the `WorkOrder` model.

```python
from django.views.generic import DetailView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.http import HttpResponseRedirect
from django.contrib import messages # For potential future notifications

from .models import WorkOrder, WorkOrderProduct, Company # Import Company for context if needed

class WorkOrderReportView(DetailView):
    """
    Displays a detailed report for a specific Work Order, replicating the functionality
    of the ASP.NET Crystal Report viewer by rendering data in HTML.
    """
    model = WorkOrder
    template_name = 'workorders/workorder_report.html'
    context_object_name = 'work_order'

    def get_object(self, queryset=None):
        """
        Retrieves the WorkOrder object based on the 'Id' from the URL.
        Adds additional query string parameters for contextual filtering, if applicable,
        mimicking the original ASP.NET's complex WHERE clause.
        """
        if queryset is None:
            queryset = self.get_queryset()

        # The ASP.NET code used multiple query parameters for lookup.
        # Here, 'pk' directly maps to 'Id'.
        # We can optionally use other query parameters for stricter validation or logging.
        pk = self.kwargs.get(self.pk_url_kwarg)
        po_id = self.request.GET.get('POId')
        po_no = self.request.GET.get('PONo')
        enq_id = self.request.GET.get('EnqId')
        customer_id = self.request.GET.get('CustomerId')
        
        # Assuming company_id from session is managed by Django authentication or settings
        # For this example, let's assume it's part of the request or a default.
        # In a real app, this would come from request.user.company_id or similar.
        # For now, we'll hardcode or make it optional for testing.
        comp_id = self.request.session.get('compid') # Replicate original session logic
        if not comp_id:
            # Fallback or raise error if company ID is mandatory
            # For demonstration, let's assume a default or fetch from settings
            comp_id = getattr(settings, 'DEFAULT_COMPANY_ID', 1) 

        # Perform the lookup, potentially adding other filters from query parameters
        work_order_filters = {'id': pk}
        if po_id:
            work_order_filters['po_id'] = po_id
        if po_no:
            work_order_filters['po_no'] = po_no
        if enq_id:
            work_order_filters['enq_id'] = enq_id
        if customer_id:
            work_order_filters['customer_id'] = customer_id
        if comp_id is not None:
             work_order_filters['company_id'] = comp_id

        obj = get_object_or_404(queryset, **work_order_filters)
        return obj

    def get_context_data(self, **kwargs):
        """
        Adds related data (products) and derived report parameters to the context.
        All data transformation is handled by model properties/methods.
        """
        context = super().get_context_data(**kwargs)
        work_order = context['work_order']

        # Fetch related products
        context['work_order_products'] = work_order.products.all()

        # All other report parameters are now properties/methods on the work_order object itself
        # Examples: work_order.shipping_city_name, work_order.formatted_task_work_order_date, etc.
        # These are directly accessible in the template.

        return context
    
    def post(self, request, *args, **kwargs):
        """
        Handles the 'Cancel' button action, redirecting to the work order list.
        """
        # The original button was a simple redirect, so we'll just redirect.
        # If it were an HTMX request that triggered a backend action before redirect,
        # we'd return a 204 or HX-Redirect header.
        messages.info(request, "Returning to Work Order list.")
        return HttpResponseRedirect(reverse_lazy('workorder_list')) # Redirect to the list view


# Assuming there's a list view for Work Orders to go back to.
# This would be implemented in another section of the migration.
# For demonstration purposes, a dummy list view.
from django.views.generic import ListView
class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'workorders/workorder_list.html' # This template would list all work orders
    context_object_name = 'work_orders'
    # This view would typically have pagination, search, etc.
    # It's included here just as a target for the "Cancel" button.
```

---

### 4.4 Templates (`workorders/templates/workorders/`)

**Task:** Create templates for the report view and the product list.

**Analysis:**
The report template (`workorder_report.html`) will dynamically display all the data extracted from the `WorkOrder` model and its related `WorkOrderProduct`s, leveraging the "fat model" properties for formatted output. The product list will be a partial for DataTables.

#### `workorders/templates/workorders/workorder_report.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl print-area">
    <div class="flex justify-between items-center mb-6 no-print">
        <h2 class="text-2xl font-bold text-gray-800">Work Order Report: WO#{{ work_order.po_no }}</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'workorder_list' %}"
            hx-swap="outerHTML"
            hx-target="#main-content"
            hx-indicator="#page-loader"
            _="on click history.pushState({}, '', hx-get.getAttribute('href'))">
            Back to Work Order List
        </button>
    </div>

    <div id="page-loader" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading...</p>
    </div>

    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <div class="text-center mb-6">
            <h1 class="text-3xl font-extrabold text-gray-900">{{ work_order.company_name }}</h1>
            <p class="text-gray-600">{{ work_order.company_address }}</p>
            <hr class="my-4 border-gray-300">
            <h2 class="text-2xl font-semibold text-blue-700">Work Order Details</h2>
            <p class="text-gray-700"><strong>Work Order No:</strong> {{ work_order.po_no }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-6">
            <div>
                <p><strong>Customer ID:</strong> {{ work_order.customer_id }}</p>
                <p><strong>Enquiry ID:</strong> {{ work_order.enq_id }}</p>
                <p><strong>PO ID:</strong> {{ work_order.po_id }}</p>
                <p><strong>Category:</strong> {{ work_order.category_display }}</p>
                <p><strong>Sub Category:</strong> {{ work_order.sub_category_display }}</p>
                <p><strong>Buyer:</strong> {{ work_order.buyer_display }}</p>
            </div>
            <div>
                <p><strong>Shipping City:</strong> {{ work_order.shipping_city_name }}</p>
                <p><strong>Shipping State:</strong> {{ work_order.shipping_state_name }}</p>
                <p><strong>Shipping Country:</strong> {{ work_order.shipping_country_name }}</p>
                <p><strong>Work Order Date:</strong> {{ work_order.formatted_task_work_order_date }}</p>
                <p><strong>Manufactured Material Date:</strong> {{ work_order.formatted_manuf_material_date }}</p>
                <p><strong>Boughtout Material Date:</strong> {{ work_order.formatted_boughtout_material_date }}</p>
            </div>
        </div>

        <h3 class="text-xl font-semibold text-gray-800 mb-4">Task Milestones:</h3>
        <div class="space-y-3 text-sm mb-6">
            <p><strong>Target DAP:</strong> {{ work_order.formatted_task_target_dap_fdate }} - {{ work_order.formatted_task_target_dap_tdate }}</p>
            <p><strong>Design Finalization:</strong> {{ work_order.formatted_task_design_finalization_fdate }} - {{ work_order.formatted_task_design_finalization_tdate }}</p>
            <p><strong>Target Manufacturing:</strong> {{ work_order.formatted_task_target_manufg_fdate }} - {{ work_order.formatted_task_target_manufg_tdate }}</p>
            <p><strong>Target Try Out:</strong> {{ work_order.formatted_task_target_try_out_fdate }} - {{ work_order.formatted_task_target_try_out_tdate }}</p>
            <p><strong>Target Despatch:</strong> {{ work_order.formatted_task_target_despach_fdate }} - {{ work_order.formatted_task_target_despach_tdate }}</p>
            <p><strong>Target Installation:</strong> {{ work_order.formatted_task_target_instalation_fdate }} - {{ work_order.formatted_task_target_instalation_tdate }}</p>
            <p><strong>Target Assembly:</strong> {{ work_order.formatted_task_target_assembly_fdate }} - {{ work_order.formatted_task_target_assembly_tdate }}</p>
            <p><strong>Customer Inspection:</strong> {{ work_order.formatted_task_cust_inspection_fdate }} - {{ work_order.formatted_task_cust_inspection_tdate }}</p>
        </div>

        <h3 class="text-xl font-semibold text-gray-800 mb-4">Instructions:</h3>
        <p class="text-sm mb-6">{{ work_order.instruction_summary }}</p>
        
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Products:</h3>
        <div id="product-table-container">
            {% include 'workorders/_workorder_product_table.html' with products=work_order_products %}
        </div>
    </div>
    
    <div class="text-center mt-6 no-print">
        <button onclick="window.print()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Print Report
        </button>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        body {
            margin: 0;
            padding: 0;
            background-color: #fff;
        }
        .print-area {
            box-shadow: none !important;
            border-radius: 0 !important;
            padding: 0 !important;
            margin: 0 !important;
            max-width: 100% !important;
        }
        h1, h2, h3, p {
            color: #000 !important; /* Ensure text is black for printing */
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is not strictly required for this read-only report page,
    // but can be used for minor UI enhancements if needed.
    document.addEventListener('alpine:init', () => {
        // e.g., for simple modal if report loaded in modal
    });
    
    // DataTables initialization for the product table is in the partial template.
</script>
{% endblock %}
```

#### `workorders/templates/workorders/_workorder_product_table.html` (Partial)

```html
<table id="workOrderProductsTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
        </tr>
    </thead>
    <tbody>
        {% for product in products %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ product.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ product.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-800">{{ product.quantity }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 text-center text-gray-500">No products found for this Work Order.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Only initialize if there are actual rows to avoid DataTables error on empty tables
    if ($('#workOrderProductsTable tbody tr').length > 0 && $('#workOrderProductsTable tbody tr:first-child').find('td').length > 1) {
        $('#workOrderProductsTable').DataTable({
            "paging": false,       // No pagination for a report table
            "searching": false,    // No search box
            "info": false,         // No "Showing X of Y entries" info
            "ordering": false      // No sorting
            // Consider "dom": 't' if you want only the table to be rendered by DataTables
        });
    }
});
</script>
```

---

### 4.5 URLs (`workorders/urls.py`)

**Task:** Define URL patterns for the views.

**Analysis:**
We'll define a URL for the `WorkOrderReportView` that accepts the `Id` as a primary key. We also include a placeholder for the `workorder_list` view which the "Cancel" button targets.

```python
from django.urls import path
from .views import WorkOrderReportView, WorkOrderListView # Import WorkOrderListView

urlpatterns = [
    # URL for the Work Order report, mapping 'Id' from ASP.NET query string to Django's 'pk'
    path('workorders/report/<int:pk>/', WorkOrderReportView.as_view(), name='workorder_report'),
    
    # Placeholder for the Work Order list view that the 'Cancel' button redirects to.
    # This view would typically be defined in another part of the 'sales' module.
    path('workorders/', WorkOrderListView.as_view(), name='workorder_list'), 
]

```

---

### 4.6 Tests (`workorders/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Analysis:**
Tests will cover the `WorkOrder` model's calculated properties and the `WorkOrderReportView`'s ability to fetch the correct data and render the template. Mocking database interactions for `managed=False` models is crucial to isolate unit tests from actual database dependencies.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import datetime
from .models import (
    WorkOrder, WorkOrderProduct, City, State, Country, Company, 
    WOCategory, WOSubCategory, Buyer, OfficeStaff
)

class WorkOrderModelTest(TestCase):
    # Setup mock data for unmanaged models since we can't create them directly via ORM
    # in setUpTestData when managed=False. We'll use patches or explicit object creation
    # for these lookup tables if needed for specific tests.

    # For tests that rely on related lookups, we'll mock the .objects.get calls.

    def setUp(self):
        # Create a sample WorkOrder instance for testing
        self.work_order_data = {
            'id': 1,
            'po_id': 'PO-001',
            'po_no': 'WO-2023-001',
            'enq_id': 'ENQ-001',
            'customer_id': 'CUST-001',
            'company_id': 101,
            'shipping_city_id': 1,
            'shipping_state_id': 1,
            'shipping_country_id': 1,
            'task_work_order_date': '2023-01-15',
            'task_target_dap_fdate': '2023-01-20',
            'task_target_dap_tdate': '2023-01-25',
            'task_design_finalization_fdate': '2023-02-01',
            'task_design_finalization_tdate': '2023-02-05',
            'task_target_manufg_fdate': '2023-02-10',
            'task_target_manufg_tdate': '2023-02-15',
            'task_target_try_out_fdate': '2023-02-16',
            'task_target_try_out_tdate': '2023-02-18',
            'task_target_despach_fdate': '2023-02-20',
            'task_target_despach_tdate': '2023-02-25',
            'task_target_instalation_fdate': '2023-03-01',
            'task_target_instalation_tdate': '2023-03-05',
            'task_target_assembly_fdate': '2023-03-06',
            'task_target_assembly_tdate': '2023-03-08',
            'task_cust_inspection_fdate': '2023-03-09',
            'task_cust_inspection_tdate': '2023-03-10',
            'manuf_material_date': '2023-01-10',
            'boughtout_material_date': '2023-01-12',
            'instraction_primer_painting': 1,
            'instraction_painting': 0,
            'instraction_self_cert_rept': 1,
            'instraction_other': 'Handle with care.',
            'category_id': 1,
            'sub_category_id': 1,
            'buyer_id': 1,
        }
        self.work_order = WorkOrder(**self.work_order_data)

    def test_work_order_creation(self):
        self.assertEqual(self.work_order.id, 1)
        self.assertEqual(self.work_order.po_no, 'WO-2023-001')

    # Test date formatting properties
    def test_formatted_date_properties(self):
        self.assertEqual(self.work_order.formatted_task_work_order_date, '15-01-2023')
        self.assertEqual(self.work_order.formatted_task_target_dap_fdate, '20-01-2023')
        self.assertEqual(self.work_order.formatted_task_target_dap_tdate, '25-01-2023')
        self.assertEqual(self.work_order.formatted_manuf_material_date, '10-01-2023')
        self.assertEqual(self.work_order.formatted_boughtout_material_date, '12-01-2023')
        
        # Test with invalid date format
        self.work_order.task_work_order_date = 'not-a-date'
        self.assertEqual(self.work_order.formatted_task_work_order_date, 'not-a-date')
        self.work_order.task_work_order_date = None
        self.assertEqual(self.work_order.formatted_task_work_order_date, '')


    # Test lookup properties using mocks
    @patch('workorders.models.City.objects.get')
    def test_shipping_city_name(self, mock_get_city):
        mock_city = MagicMock(spec=City)
        mock_city.name = "Mumbai"
        mock_get_city.return_value = mock_city
        self.assertEqual(self.work_order.shipping_city_name, "Mumbai")
        mock_get_city.assert_called_with(id=1)

    @patch('workorders.models.State.objects.get')
    def test_shipping_state_name(self, mock_get_state):
        mock_state = MagicMock(spec=State)
        mock_state.name = "Maharashtra"
        mock_get_state.return_value = mock_state
        self.assertEqual(self.work_order.shipping_state_name, "Maharashtra")
        mock_get_state.assert_called_with(id=1)

    @patch('workorders.models.Country.objects.get')
    def test_shipping_country_name(self, mock_get_country):
        mock_country = MagicMock(spec=Country)
        mock_country.name = "India"
        mock_get_country.return_value = mock_country
        self.assertEqual(self.work_order.shipping_country_name, "India")
        mock_get_country.assert_called_with(id=1)

    @patch('workorders.models.Company.objects.get')
    def test_company_name(self, mock_get_company):
        mock_company = MagicMock(spec=Company)
        mock_company.name = "Acme Corp"
        mock_get_company.return_value = mock_company
        self.assertEqual(self.work_order.company_name, "Acme Corp")
        mock_get_company.assert_called_with(id=101)

    @patch('workorders.models.Company.objects.get')
    def test_company_address(self, mock_get_company):
        mock_company = MagicMock(spec=Company)
        mock_company.address = "123 Ind. Area, City"
        mock_get_company.return_value = mock_company
        self.assertEqual(self.work_order.company_address, "123 Ind. Area, City")
        mock_get_company.assert_called_with(id=101)

    def test_instruction_summary(self):
        # Test all instructions
        self.assertEqual(self.work_order.instruction_summary, 
                         "Primer Painting, Self Certification Report, Handle with care.")
        
        # Test only one instruction
        self.work_order.instraction_primer_painting = 0
        self.work_order.instraction_self_cert_rept = 0
        self.work_order.instraction_other = ""
        self.work_order.instraction_painting = 1 # Set only one to true
        self.assertEqual(self.work_order.instruction_summary, "Painting")

        # Test no instructions
        self.work_order.instraction_primer_painting = 0
        self.work_order.instraction_painting = 0
        self.work_order.instraction_self_cert_rept = 0
        self.work_order.instraction_other = ""
        self.assertEqual(self.work_order.instruction_summary, "No special instructions.")
        
    @patch('workorders.models.WOCategory.objects.get')
    def test_category_display(self, mock_get):
        mock_category = MagicMock(spec=WOCategory)
        mock_category.__str__.return_value = "SYM - Category Name"
        mock_get.return_value = mock_category
        self.assertEqual(self.work_order.category_display, "SYM - Category Name")
        mock_get.assert_called_with(id=1)

    @patch('workorders.models.WOSubCategory.objects.get')
    def test_sub_category_display(self, mock_get):
        mock_subcategory = MagicMock(spec=WOSubCategory)
        mock_subcategory.__str__.return_value = "SC-SYM - SubCategory Name"
        mock_get.return_value = mock_subcategory
        self.assertEqual(self.work_order.sub_category_display, "SC-SYM - SubCategory Name")
        mock_get.assert_called_with(id=1)
        
    @patch('workorders.models.Buyer.objects.get')
    def test_buyer_display(self, mock_get_buyer):
        mock_buyer = MagicMock(spec=Buyer)
        mock_buyer.__str__.return_value = "CAT1 - Employee Name [EMP001]"
        mock_get_buyer.return_value = mock_buyer
        self.assertEqual(self.work_order.buyer_display, "CAT1 - Employee Name [EMP001]")
        mock_get_buyer.assert_called_with(id=1)


class WorkOrderProductModelTest(TestCase):
    def setUp(self):
        # Create a dummy WorkOrder instance for FK reference
        self.work_order = WorkOrder(id=100, po_no='WO-TEST', company_id=1)
        self.product = WorkOrderProduct(
            id=1,
            work_order=self.work_order,
            company_id=1,
            item_code='ITEM-001',
            description='Test Product',
            quantity=10.5
        )

    def test_work_order_product_creation(self):
        self.assertEqual(self.product.item_code, 'ITEM-001')
        self.assertEqual(self.product.quantity, 10.5)
        self.assertEqual(self.product.work_order.id, 100)
        self.assertEqual(str(self.product), "ITEM-001 - Test Product")


class WorkOrderReportViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create dummy data for unmanaged models for view tests (since mocks might be complex for get_object)
        # In a real scenario, you'd use a test database or a more robust mocking strategy.
        # For simplicity here, we'll create directly in the test if possible,
        # or use patch on .objects.get within get_object for views.
        
        # We need to ensure that WorkOrder.objects.get returns a WorkOrder object.
        # Since WorkOrder is managed=False, we can't save it directly in the test DB.
        # We'll patch the manager's get method.

        self.work_order_data = {
            'id': 1,
            'po_id': 'PO-VIEW',
            'po_no': 'WO-VIEW-001',
            'enq_id': 'ENQ-VIEW',
            'customer_id': 'CUST-VIEW',
            'company_id': 101,
            'shipping_city_id': 1,
            'shipping_state_id': 1,
            'shipping_country_id': 1,
            'task_work_order_date': '2023-01-15',
            'task_target_dap_fdate': '2023-01-20',
            'task_target_dap_tdate': '2023-01-25',
            'task_design_finalization_fdate': '2023-02-01',
            'task_design_finalization_tdate': '2023-02-05',
            'task_target_manufg_fdate': '2023-02-10',
            'task_target_manufg_tdate': '2023-02-15',
            'task_target_try_out_fdate': '2023-02-16',
            'task_target_try_out_tdate': '2023-02-18',
            'task_target_despach_fdate': '2023-02-20',
            'task_target_despach_tdate': '2023-02-25',
            'task_target_instalation_fdate': '2023-03-01',
            'task_target_instalation_tdate': '2023-03-05',
            'task_target_assembly_fdate': '2023-03-06',
            'task_target_assembly_tdate': '2023-03-08',
            'task_cust_inspection_fdate': '2023-03-09',
            'task_cust_inspection_tdate': '2023-03-10',
            'manuf_material_date': '2023-01-10',
            'boughtout_material_date': '2023-01-12',
            'instraction_primer_painting': 1,
            'instraction_painting': 1,
            'instraction_self_cert_rept': 1,
            'instraction_other': 'View specific instruction.',
            'category_id': 1,
            'sub_category_id': 1,
            'buyer_id': 1,
        }
        self.mock_work_order = WorkOrder(**self.work_order_data)

        # Mock related products
        self.mock_products = [
            WorkOrderProduct(work_order=self.mock_work_order, item_code='P1', description='Product 1', quantity=5.0),
            WorkOrderProduct(work_order=self.mock_work_order, item_code='P2', description='Product 2', quantity=10.0),
        ]
        # Mock the related manager
        self.mock_work_order.products = MagicMock()
        self.mock_work_order.products.all.return_value = self.mock_products

    @patch('workorders.models.WorkOrder.objects.get')
    @patch('workorders.models.City.objects.get')
    @patch('workorders.models.State.objects.get')
    @patch('workorders.models.Country.objects.get')
    @patch('workorders.models.Company.objects.get')
    @patch('workorders.models.WOCategory.objects.get')
    @patch('workorders.models.WOSubCategory.objects.get')
    @patch('workorders.models.Buyer.objects.get')
    def test_report_view_success(self, 
                                 mock_get_buyer, mock_get_subcategory, mock_get_category, 
                                 mock_get_company, mock_get_country, mock_get_state, 
                                 mock_get_city, mock_get_work_order):
        
        # Configure mocks for lookup models
        mock_get_city.return_value = MagicMock(name="Mumbai")
        mock_get_state.return_value = MagicMock(name="Maharashtra")
        mock_get_country.return_value = MagicMock(name="India")
        mock_get_company.return_value = MagicMock(name="Test Company", address="123 Test St.")
        mock_get_category.return_value = MagicMock(__str__=lambda: "CAT - Test Category")
        mock_get_subcategory.return_value = MagicMock(__str__=lambda: "SC-CAT - Test SubCategory")
        mock_get_buyer.return_value = MagicMock(__str__=lambda: "BUYER - Test Buyer [EMP-001]")

        # Mock WorkOrder.objects.get to return our mock instance
        mock_get_work_order.return_value = self.mock_work_order

        response = self.client.get(reverse('workorder_report', args=[self.mock_work_order.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder_report.html')
        self.assertIn('work_order', response.context)
        self.assertEqual(response.context['work_order'].po_no, 'WO-VIEW-001')
        self.assertIn('Product 1', response.content.decode())
        self.assertIn('Product 2', response.content.decode())
        self.assertIn('Work Order Report: WO#WO-VIEW-001', response.content.decode())
        self.assertIn('Primer Painting, Painting, Self Certification Report, View specific instruction.', response.content.decode())

    @patch('workorders.models.WorkOrder.objects.get')
    def test_report_view_not_found(self, mock_get_work_order):
        mock_get_work_order.side_effect = WorkOrder.DoesNotExist # Simulate object not found
        response = self.client.get(reverse('workorder_report', args=[999])) # Non-existent ID
        self.assertEqual(response.status_code, 404) # Should return 404 Not Found

    def test_cancel_button_redirect(self):
        # The target of the cancel button (workorder_list) needs to exist for the redirect to work.
        # For this test, we don't need a real WorkOrder instance as we're testing the POST redirect.
        response = self.client.post(reverse('workorder_report', args=[1])) # PK doesn't matter for POST to this URL
        self.assertEqual(response.status_code, 302) # Expect a redirect
        self.assertRedirects(response, reverse('workorder_list'))

    # HTMX related tests (if applicable)
    # For this report view, HTMX mostly for the "Back to List" button.
    def test_back_to_list_button_htmx(self):
        # Simulate an HTMX request for the list view from the report page
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Assuming workorder_list returns a partial or just the main content.
        # For simplicity, we just check the target URL for hx-get.
        # A more thorough test would involve testing the list view itself.
        response = self.client.get(reverse('workorder_list'), **headers)
        self.assertEqual(response.status_code, 200) 
        # Check if the response content is what you'd expect from the list view (e.g., its title)
        # self.assertIn('Work Orders List', response.content.decode()) # Requires workorder_list to render this.
```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

For the `WorkOrder_Print_Details` page, HTMX and Alpine.js integration is straightforward as it's primarily a display page:

*   **"Back to Work Order List" Button:**
    *   The button uses `hx-get="{% url 'workorder_list' %}"` to fetch the content of the Work Order list page.
    *   `hx-swap="outerHTML"` replaces the entire `div` where the report is displayed with the new list content, giving a seamless page transition without a full reload.
    *   `hx-target="#main-content"` ensures the new content replaces the main content area of your `base.html`. Adjust `#main-content` to your actual content div ID.
    *   `hx-indicator="#page-loader"` shows a loading spinner during the fetch.
    *   `_ = "on click history.pushState({}, '', hx-get.getAttribute('href'))"` for pushing the new URL to browser history, enabling proper back/forward button behavior.
*   **DataTables for Products:**
    *   The `_workorder_product_table.html` partial uses DataTables for client-side functionality.
    *   The `$(document).ready(function() { $('#workOrderProductsTable').DataTable({...}); });` script initializes DataTables as soon as the partial is loaded.
    *   For a simple report, DataTables can be configured with `paging: false`, `searching: false`, `info: false`, `ordering: false` as shown in the template to avoid unnecessary interactive features on a static report.
*   **Alpine.js:**
    *   While not strictly necessary for a read-only report display, Alpine.js could be used for subtle UI enhancements like a print confirmation dialog (`x-data="{ showPrintModal: false }"` with an `x-show="showPrintModal"` modal and `@click="showPrintModal = true"` on the print button). For this specific conversion, the provided template includes a simple `window.print()` call, which is sufficient.
*   **Loading Indicators:**
    *   The `hx-indicator` attribute on the "Back to List" button leverages HTMX's built-in loading mechanism, providing visual feedback during asynchronous content loading.

This approach ensures a modern, responsive, and user-friendly experience without the overhead of full page reloads or complex JavaScript frameworks, aligning perfectly with the HTMX + Alpine.js philosophy.