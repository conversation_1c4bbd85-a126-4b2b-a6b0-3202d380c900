This comprehensive Django modernization plan outlines the strategic transition from your legacy ASP.NET application to a modern Django-based solution. Our approach emphasizes AI-assisted automation, "fat model/thin view" architecture, and dynamic frontend interactions using HTMX and Alpine.js, ensuring a robust, scalable, and maintainable system. We focus on clear, actionable steps that can be guided by conversational AI, minimizing manual development effort.

## ASP.NET to Django Conversion Plan: Work Order Management

This plan focuses on migrating the `WorkOrder_New_Details.aspx` page, which handles the creation and management of new work orders, including associated products and shipping details.

### Business Value & Outcomes

Migrating to Django delivers significant business benefits:

*   **Reduced Operational Costs:** Modern infrastructure requires less specialized maintenance and is easier to update, leading to lower ongoing costs.
*   **Improved User Experience:** HTMX and Alpine.js create a highly responsive, app-like feel without complex JavaScript, boosting user satisfaction and productivity.
*   **Enhanced Scalability:** Django's robust framework efficiently handles increased user loads and data volumes, ensuring your application grows with your business needs.
*   **Accelerated Development:** Leveraging Django's "batteries-included" philosophy and a clear architectural pattern speeds up future feature development and reduces time-to-market for new functionalities.
*   **Increased Security:** Django's built-in security features and active community provide a stronger defense against modern cyber threats.
*   **Future-Proofing:** Adopting a popular, open-source framework like Django ensures long-term viability and access to a vast ecosystem of tools and talent.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to map them to Django models.

**Instructions:**
Our AI will parse the C# code-behind and ASPX file to identify SQL commands and UI control bindings.

*   **Main Work Order Details:** `SD_Cust_WorkOrder_Master`
    *   Columns: `Id` (Primary Key, inferred), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `CustomerId`, `EnqId`, `PONo`, `WONo`, `TaskWorkOrderDate`, `TaskProjectTitle`, `TaskProjectLeader`, `CId` (Category ID), `SCId` (Subcategory ID), `TaskBusinessGroup` (Business Group ID), `TaskTargetDAP_FDate`, `TaskTargetDAP_TDate`, `TaskDesignFinalization_FDate`, `TaskDesignFinalization_TDate`, `TaskTargetManufg_FDate`, `TaskTargetManufg_TDate`, `TaskTargetTryOut_FDate`, `TaskTargetTryOut_TDate`, `TaskTargetDespach_FDate`, `TaskTargetDespach_TDate`, `TaskTargetAssembly_FDate`, `TaskTargetAssembly_TDate`, `TaskTargetInstalation_FDate`, `TaskTargetInstalation_TDate`, `TaskCustInspection_FDate`, `TaskCustInspection_TDate`, `ShippingAdd`, `ShippingCountry` (Country ID), `ShippingState` (State ID), `ShippingCity` (City ID), `ShippingContactPerson1`, `ShippingContactNo1`, `ShippingEmail1`, `ShippingContactPerson2`, `ShippingContactNo2`, `ShippingEmail2`, `ShippingFaxNo`, `ShippingEccNo`, `ShippingTinCstNo`, `ShippingTinVatNo`, `InstractionPrimerPainting` (boolean), `InstractionPainting` (boolean), `InstractionSelfCertRept` (boolean), `InstractionOther`, `InstractionExportCaseMark`, `InstractionAttachAnnexure`, `POId`, `ManufMaterialDate`, `BoughtoutMaterialDate`, `Buyer` (Buyer ID).
*   **Work Order Products (Temporary/Detail):** `SD_Cust_WorkOrder_Products_Temp` and `SD_Cust_WorkOrder_Products_Details`
    *   Columns (common to both): `Id` (Primary Key, for temp table), `MId` (Foreign Key to `SD_Cust_WorkOrder_Master.Id` for details table), `SessionId`, `CompId`, `FinYearId`, `ItemCode`, `Description`, `Qty`.
*   **Lookup Tables (inferred from dropdowns and data binding):**
    *   `SD_Cust_master` (`CustomerId`, `CustomerName`)
    *   `tblSD_WO_Category` (`CId`, `Symbol`, `CName`, `HasSubCat`)
    *   `tblSD_WO_SubCategory` (`CId`, `SCId`, `Symbol`, `SCName`)
    *   `tblCountry` (or similar) (`CountryId`, `CountryName`)
    *   `tblState` (or similar) (`StateId`, `StateName`, `CountryId`)
    *   `tblCity` (or similar) (`CityId`, `CityName`, `StateId`)
    *   `tblBuyer` (or similar) (`BuyerId`, `BuyerName`)
    *   `tblBusinessGroup` (or similar) (`BGId`, `BGName`)

### Step 2: Identify Backend Functionality

**Task:** Determine the core Create, Read, Update, and Delete (CRUD) operations and associated business logic within the ASP.NET code.

**Instructions:**
Our AI will identify the purpose of each C# method and ASP.NET control event.

*   **Create (Work Order):** Triggered by `btnSubmit_Click`.
    *   Generates a unique `WONo` based on Category and Subcategory.
    *   Inserts main work order details into `SD_Cust_WorkOrder_Master`.
    *   Transfers all associated product entries from `SD_Cust_WorkOrder_Products_Temp` to `SD_Cust_WorkOrder_Products_Details`.
    *   Clears the temporary product entries from `SD_Cust_WorkOrder_Products_Temp` for the current session.
*   **Create (Product):** Triggered by `btnProductSubmit_Click`.
    *   Inserts new product details into `SD_Cust_WorkOrder_Products_Temp` for the current session.
*   **Read (Work Order Details):** `Page_Load` logic.
    *   Loads `CustomerId`, `PONo`, `EnqId` from query string.
    *   Retrieves `CustomerName` based on `CustomerId`.
    *   Populates initial dropdowns (`DDLBuyer`, `DDLBusinessGroup`, `DDLShippingCountry`, `DDLTaskWOType`).
*   **Read (Product List):** `FillGrid` method.
    *   Retrieves products from `SD_Cust_WorkOrder_Products_Temp` filtered by `SessionId`, `CompId`, `FinYearId` to display in `GridView1`.
*   **Update (Product):** Triggered by `GridView1_RowUpdating`.
    *   Updates `ItemCode`, `Description`, `Qty` for a specific product in `SD_Cust_WorkOrder_Products_Temp`.
*   **Delete (Product):** Triggered by `GridView1_RowDeleting`.
    *   Deletes a specific product entry from `SD_Cust_WorkOrder_Products_Temp`.
*   **Cascading Dropdowns:**
    *   `DDLTaskWOType_SelectedIndexChanged`: Dynamically loads Subcategories based on selected Category.
    *   `DDLShippingCountry_SelectedIndexChanged`: Dynamically loads States based on selected Country.
    *   `DDLShippingState_SelectedIndexChanged`: Dynamically loads Cities based on selected State.
*   **Validation:** Extensive server-side (and implied client-side) validation on all fields for required status, date format, email format, and quantity format (e.g., `fun.NumberValidationQty`, `fun.EmailValidation`, `fun.DateValidation`).

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their visual and interactive roles to inform the Django template and HTMX/Alpine.js structure.

**Instructions:**
Our AI will map ASP.NET controls to their modern Django/frontend equivalents.

*   **Tabbed Interface:** `cc1:TabContainer` with `TabPanel`s for "Task Execution", "Shipping", "Products", and "Instructions." This will be re-implemented using HTMX to swap content for each tab, providing a smooth user experience.
*   **Form Inputs:**
    *   `asp:Label`: For displaying static text or data (`lblCustomerName`, `lblPONo`).
    *   `asp:TextBox`: For text input (`txtWorkOrderDate`, `txtProjectTitle`, `txtShippingAdd`, `txtItemCode`, `txtDescOfItem`, `txtQty`, etc.). Date inputs with `cc1:CalendarExtender` will become HTML5 `type="date"` inputs. Multi-line textboxes will become `textarea`s.
    *   `asp:DropDownList`: For selection (`DDLTaskWOType`, `DDLSubcategory`, `DDLBusinessGroup`, `DDLShippingCountry`, `DDLShippingState`, `DDLShippingCity`, `DDLBuyer`). These will be powered by Django Forms and HTMX for dynamic loading.
    *   `asp:CheckBox`: For boolean choices (`CKInstractionPrimerPainting`, etc.).
*   **Buttons:** `asp:Button` elements (`btnTaskNext`, `btnShippingNext`, `btnProductSubmit`, `btnProductNext`, `btnCancel`, `btnSubmit`).
    *   "Next" buttons will use HTMX to switch tabs client-side.
    *   "Submit" buttons will perform form submissions via HTMX, triggering `HX-Trigger` to refresh relevant sections or redirect.
*   **Data Display Grid:** `asp:GridView` (`GridView1`) for product listings. This will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side functionality (searching, sorting, pagination) and HTMX for dynamic content loading (e.g., after add/edit/delete).
*   **Validation Display:** `asp:RequiredFieldValidator`, `asp:RegularExpressionValidator` will be handled by Django's form validation and displayed inline with the form fields.

---

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create Django models that map to the identified database tables, incorporating the "fat model" principle for business logic.

**Instructions:**
Our AI will generate the following model definitions. Note that `CompId`, `FinYearId`, and `SessionId` are often managed implicitly by the application context (e.g., user session, multi-tenancy setup) and might not be directly exposed in user-facing forms. For this migration, we'll include them as fields if present in the database schema, but exclude from forms if they are system-managed.

```python
# workorders/models.py
from django.db import models
from django.db.models import F, Max
from django.core.validators import MinValueValidator
from django.utils import timezone
import datetime
import re

# Placeholder/Lookup Models (assuming these tables exist in the DB)
# These models are for foreign key relationships and dropdown population
class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class WOCategory(models.Model):
    category_id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)
    category_name = models.CharField(db_column='CName', max_length=255)
    has_subcat = models.BooleanField(db_column='HasSubCat', default=False)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.category_name}"

class WOSubCategory(models.Model):
    subcategory_id = models.IntegerField(db_column='SCId', primary_key=True)
    category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', related_name='subcategories')
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)
    subcategory_name = models.CharField(db_column='SCName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_SubCategory'
        verbose_name = 'WO Subcategory'
        verbose_name_plural = 'WO Subcategories'

    def __str__(self):
        return f"{self.symbol} - {self.subcategory_name}"

class Country(models.Model):
    country_id = models.IntegerField(db_column='CountryId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCountry' # Assuming this table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class State(models.Model):
    state_id = models.IntegerField(db_column='StateId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=255)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CountryId', related_name='states')

    class Meta:
        managed = False
        db_table = 'tblState' # Assuming this table name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=255)
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='StateId', related_name='cities')

    class Meta:
        managed = False
        db_table = 'tblCity' # Assuming this table name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class Buyer(models.Model):
    buyer_id = models.IntegerField(db_column='BuyerId', primary_key=True)
    buyer_name = models.CharField(db_column='BuyerName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblBuyer' # Assuming this table name
        verbose_name = 'Buyer'
        verbose_name_plural = 'Buyers'

    def __str__(self):
        return self.buyer_name

class BusinessGroup(models.Model):
    bg_id = models.IntegerField(db_column='BGId', primary_key=True)
    bg_name = models.CharField(db_column='BGName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblBusinessGroup' # Assuming this table name
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.bg_name


# Main Work Order Models
class WorkOrderMaster(models.Model):
    # Core system fields (often populated automatically or from session)
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    # Task Execution Tab
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id', related_name='work_orders')
    enquiry_id = models.IntegerField(db_column='EnqId')
    po_number = models.CharField(db_column='PONo', max_length=50)
    po_id = models.CharField(db_column='POId', max_length=50, blank=True, null=True)
    work_order_no = models.CharField(db_column='WONo', max_length=50, unique=True)
    work_order_date = models.DateField(db_column='TaskWorkOrderDate')
    project_title = models.CharField(db_column='TaskProjectTitle', max_length=500)
    project_leader = models.CharField(db_column='TaskProjectLeader', max_length=255)
    category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', to_field='category_id', related_name='work_orders')
    subcategory = models.ForeignKey(WOSubCategory, on_delete=models.DO_NOTHING, db_column='SCId', to_field='subcategory_id', blank=True, null=True, related_name='work_orders')
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='TaskBusinessGroup', to_field='bg_id', related_name='work_orders')

    target_dap_fdate = models.DateField(db_column='TaskTargetDAP_FDate', blank=True, null=True)
    target_dap_tdate = models.DateField(db_column='TaskTargetDAP_TDate', blank=True, null=True)
    design_finalization_fdate = models.DateField(db_column='TaskDesignFinalization_FDate', blank=True, null=True)
    design_finalization_tdate = models.DateField(db_column='TaskDesignFinalization_TDate', blank=True, null=True)
    target_manufg_fdate = models.DateField(db_column='TaskTargetManufg_FDate', blank=True, null=True)
    target_manufg_tdate = models.DateField(db_column='TaskTargetManufg_TDate', blank=True, null=True)
    target_try_out_fdate = models.DateField(db_column='TaskTargetTryOut_FDate', blank=True, null=True)
    target_try_out_tdate = models.DateField(db_column='TaskTargetTryOut_TDate', blank=True, null=True)
    target_despatch_fdate = models.DateField(db_column='TaskTargetDespach_FDate', blank=True, null=True)
    target_despatch_tdate = models.DateField(db_column='TaskTargetDespach_TDate', blank=True, null=True)
    target_assembly_fdate = models.DateField(db_column='TaskTargetAssembly_FDate', blank=True, null=True)
    target_assembly_tdate = models.DateField(db_column='TaskTargetAssembly_TDate', blank=True, null=True)
    target_installation_fdate = models.DateField(db_column='TaskTargetInstalation_FDate', blank=True, null=True)
    target_installation_tdate = models.DateField(db_column='TaskTargetInstalation_TDate', blank=True, null=True)
    cust_inspection_fdate = models.DateField(db_column='TaskCustInspection_FDate', blank=True, null=True)
    cust_inspection_tdate = models.DateField(db_column='TaskCustInspection_TDate', blank=True, null=True)
    manuf_material_date = models.DateField(db_column='ManufMaterialDate', blank=True, null=True)
    boughtout_material_date = models.DateField(db_column='BoughtoutMaterialDate', blank=True, null=True)
    buyer = models.ForeignKey(Buyer, on_delete=models.DO_NOTHING, db_column='Buyer', to_field='buyer_id', related_name='work_orders')


    # Shipping Tab
    shipping_address = models.TextField(db_column='ShippingAdd')
    shipping_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='ShippingCountry', to_field='country_id', related_name='shipping_work_orders')
    shipping_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='ShippingState', to_field='state_id', related_name='shipping_work_orders')
    shipping_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='ShippingCity', to_field='city_id', related_name='shipping_work_orders')
    shipping_contact_person1 = models.CharField(db_column='ShippingContactPerson1', max_length=255)
    shipping_contact_no1 = models.CharField(db_column='ShippingContactNo1', max_length=20)
    shipping_email1 = models.CharField(db_column='ShippingEmail1', max_length=255)
    shipping_contact_person2 = models.CharField(db_column='ShippingContactPerson2', max_length=255, blank=True, null=True)
    shipping_contact_no2 = models.CharField(db_column='ShippingContactNo2', max_length=20, blank=True, null=True)
    shipping_email2 = models.CharField(db_column='ShippingEmail2', max_length=255, blank=True, null=True)
    shipping_fax_no = models.CharField(db_column='ShippingFaxNo', max_length=50, blank=True, null=True)
    shipping_ecc_no = models.CharField(db_column='ShippingEccNo', max_length=50, blank=True, null=True)
    shipping_tin_cst_no = models.CharField(db_column='ShippingTinCstNo', max_length=50, blank=True, null=True)
    shipping_tin_vat_no = models.CharField(db_column='ShippingTinVatNo', max_length=50, blank=True, null=True)

    # Instructions Tab
    instruction_primer_painting = models.BooleanField(db_column='InstractionPrimerPainting', default=False)
    instruction_painting = models.BooleanField(db_column='InstractionPainting', default=False)
    instruction_self_cert_rept = models.BooleanField(db_column='InstractionSelfCertRept', default=False)
    instruction_other = models.TextField(db_column='InstractionOther', blank=True, null=True)
    instruction_export_case_mark = models.CharField(db_column='InstractionExportCaseMark', max_length=255, blank=True, null=True)
    instruction_attach_annexure = models.CharField(db_column='InstractionAttachAnnexure', max_length=255, blank=True, null=True) # Placeholder for file upload

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.work_order_no or f"Work Order {self.pk}"

    @classmethod
    def generate_work_order_number(cls, category_id, subcategory_id=None, company_id=None):
        """
        Generates a new Work Order Number based on category/subcategory and sequence.
        This logic directly mirrors the ASP.NET getWOChar and WONo generation.
        """
        category_symbol_map = {
            # Example mapping, expand as per actual 'tblSD_WO_Category' 'Symbol' column logic
            1: 'WO-CAT1-',
            2: 'WO-CAT2-',
            # ... and so on
        }
        subcategory_symbol_map = {
            # Example mapping, expand as per actual 'tblSD_WO_SubCategory' 'Symbol' column logic
            1: 'SUB1-',
            2: 'SUB2-',
            # ... and so on
        }

        category_prefix = category_symbol_map.get(category_id, 'WO-UNKNOWN-')
        
        # Check if category requires subcategory
        category_obj = WOCategory.objects.filter(category_id=category_id).first()
        if category_obj and category_obj.has_subcat and not subcategory_id:
            raise ValueError("Subcategory is required for this WO type.")

        subcategory_prefix = subcategory_symbol_map.get(subcategory_id, '') if subcategory_id else ''

        # The original code simplifies 'cat + subcat + won.ToString("D4")' to 'cat + won.ToString("D4")'
        # if the subcategory logic is complex or not always used. Let's stick to the observed behavior
        # where it generates a sequence after just the category, or if subcategory is 'Select' it defaults.
        # The line `WorkOrderNo = cat + won.ToString("D4");` was present in the C# code.
        # We'll use the category symbol for prefixing.

        # Find the max WO number for this category (and subcategory if applicable)
        # Note: Original code seems to generate WONo based on CId and SCId.
        # However, the line `WorkOrderNo = cat + won.ToString("D4");` implies a simpler sequential numbering.
        # Let's try to infer a more robust one, likely based on the `Symbol` column.
        
        # Inferred logic: The WO number generation uses a symbol from the category and then a sequence.
        # The C# code uses `fun.getWOChar(DDLTaskWOType.SelectedItem.Text)` and `fun.getWOChar(DDLSubcategory.SelectedItem.Text)`
        # `getWOChar` probably returns a character based on the name/symbol.
        # `getWO` extracts the numeric part from the last WO number.

        # For this migration, we'll assume `category_prefix` from `WOCategory.symbol`
        # and subcategory doesn't directly influence the prefix but only the sequence filtering.
        
        prefix = category_obj.symbol if category_obj else 'WO-' # Use the symbol from the category model

        # Filter by category and subcategory to get the highest existing WO number
        # Assuming WONo pattern is like 'SYMBOL####'
        query = cls.objects.filter(work_order_no__startswith=prefix)
        if subcategory_id:
            query = query.filter(subcategory_id=subcategory_id)
        if company_id: # Important for multi-company setups
             query = query.filter(company_id=company_id)

        # Get the highest numeric part of existing work order numbers
        last_wo = query.annotate(
            num_part=models.functions.Cast(
                models.functions.Substr('work_order_no', len(prefix) + 1),
                models.IntegerField()
            )
        ).aggregate(Max('num_part'))

        next_sequence = (last_wo['num_part__max'] or 0) + 1
        return f"{prefix}{next_sequence:04d}"

    def save_work_order_and_products(self, products_data, user_session_id):
        """
        Business logic to save the Work Order and its associated products.
        This mimics the btnSubmit_Click logic.
        """
        # Ensure WO number is generated if not already
        if not self.work_order_no:
            self.work_order_no = self.generate_work_order_number(
                self.category.category_id,
                self.subcategory.subcategory_id if self.subcategory else None,
                self.company_id # Assuming company_id is set
            )
        
        # Set system fields if not already set by auto_now_add
        if not self.sys_date:
            self.sys_date = timezone.localdate()
        if not self.sys_time:
            self.sys_time = timezone.localtime().time()
        if not self.session_id:
            self.session_id = user_session_id # Pass session ID from view

        # Save the main work order
        self.save()

        # Transfer products from temp data to permanent storage
        # In Django, we typically use an inline formset or manage it directly
        # For simplicity, we'll assume products_data is a list of dictionaries
        # matching WorkOrderProduct structure (excluding MId, CompId, FinYearId which are added here)
        for product_item_data in products_data:
            WorkOrderProduct.objects.create(
                master_work_order=self, # Link to the newly created WO
                session_id=user_session_id, # Can be dropped if not needed permanently
                company_id=self.company_id,
                financial_year_id=self.financial_year_id,
                item_code=product_item_data['item_code'],
                description=product_item_data['description'],
                quantity=product_item_data['quantity']
            )

        # Clear temp products for this session (simulated, as we don't have a physical _Temp table here)
        # This part depends on how 'temporary' products are handled.
        # If products are stored in a session variable, clearing it is automatic upon session expiration.
        # If a temp DB table is truly used, then WorkOrderProductTemp would need a delete method.
        # For this example, we assume `products_data` is the "temp" data passed from the view.

        return self.work_order_no


class WorkOrderProduct(models.Model):
    # This model will represent both SD_Cust_WorkOrder_Products_Temp and SD_Cust_WorkOrder_Products_Details
    # The 'temp' aspect is managed by session data / specific query filters.
    # For a real migration, decide if Temp table is truly needed or if session storage is enough.
    # Here, we'll map to the _Details table and imply temp state via session logic in views/forms.
    
    # Primary key from the original table will be 'Id'
    # For _Details table, it might not have an independent 'Id' or MId is part of a composite key.
    # Assuming 'Id' is the PK for _Details as well, or Django's default auto-PK.
    
    master_work_order = models.ForeignKey(WorkOrderMaster, on_delete=models.CASCADE, db_column='MId', related_name='products', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Used for temporary storage during creation
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    description = models.TextField(db_column='Description')
    quantity = models.DecimalField(db_column='Qty', max_digits=15, decimal_places=3, validators=[MinValueValidator(0)])

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Products_Details' # This model represents the final products
        verbose_name = 'Work Order Product'
        verbose_name_plural = 'Work Order Products'

    def __str__(self):
        return f"{self.item_code} - {self.quantity}"

    # Business logic for products can go here (e.g., calculate total value, validation beyond form)
    # The original logic primarily involved CRUD on a temporary store.
```

### 4.2 Forms

**Task:** Define Django forms for user input, including validation and appropriate widgets.

**Instructions:**
Our AI will generate two main forms: one for the `WorkOrderMaster` and one for `WorkOrderProduct`. Date fields will use HTML5 `type="date"` for modern browser pickers.

```python
# workorders/forms.py
from django import forms
from .models import WorkOrderMaster, WorkOrderProduct, WOCategory, WOSubCategory, Country, State, City, Buyer, BusinessGroup
import re

# Custom form field for date formatting (dd-MM-yyyy)
# In a real scenario, consider setting DATE_INPUT_FORMATS in settings.py
# and using a standard DateInput widget. For direct migration, we can match.
class DMYDateField(forms.DateField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('input_formats', ['%d-%m-%Y'])
        kwargs.setdefault('widget', forms.DateInput(attrs={'type': 'date', 'class': 'box3'}))
        super().__init__(*args, **kwargs)

class WorkOrderMasterForm(forms.ModelForm):
    # Dynamically populate dropdowns from database
    category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('category_name'),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/workorders/get-subcategories/', 'hx-target': '#id_subcategory_container', 'hx-swap': 'outerHTML'}),
        label="Category"
    )
    subcategory = forms.ModelChoiceField(
        queryset=WOSubCategory.objects.none(), # Will be populated via HTMX
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Subcategory"
    )
    
    shipping_country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by('country_name'),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/workorders/get-states/', 'hx-target': '#id_shipping_state_container', 'hx-swap': 'outerHTML'}),
        label="Country"
    )
    shipping_state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Populated via HTMX
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/workorders/get-cities/', 'hx-target': '#id_shipping_city_container', 'hx-swap': 'outerHTML'}),
        label="State"
    )
    shipping_city = forms.ModelChoiceField(
        queryset=City.objects.none(), # Populated via HTMX
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'}),
        label="City"
    )
    buyer = forms.ModelChoiceField(
        queryset=Buyer.objects.all().order_by('buyer_name'),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Buyer"
    )
    business_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('bg_name'),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Business Group"
    )

    # Date fields with custom widget for type="date" and format adherence
    work_order_date = DMYDateField(label="Date of WO")
    target_dap_fdate = DMYDateField(label="Target DAP Date From", required=False)
    target_dap_tdate = DMYDateField(label="Target DAP Date To", required=False)
    design_finalization_fdate = DMYDateField(label="Design Finalization Date From", required=False)
    design_finalization_tdate = DMYDateField(label="Design Finalization Date To", required=False)
    target_manufg_fdate = DMYDateField(label="Target Manufg. Date From", required=False)
    target_manufg_tdate = DMYDateField(label="Target Manufg. Date To", required=False)
    target_try_out_fdate = DMYDateField(label="Target Try-out Date From", required=False)
    target_try_out_tdate = DMYDateField(label="Target Try-out Date To", required=False)
    target_despatch_fdate = DMYDateField(label="Target Despatch Date From", required=False)
    target_despatch_tdate = DMYDateField(label="Target Despatch Date To", required=False)
    target_assembly_fdate = DMYDateField(label="Target Assembly Date From", required=False)
    target_assembly_tdate = DMYDateField(label="Target Assembly Date To", required=False)
    target_installation_fdate = DMYDateField(label="Target Installation Date From", required=False)
    target_installation_tdate = DMYDateField(label="Target Installation Date To", required=False)
    cust_inspection_fdate = DMYDateField(label="Cust. Inspection Date From", required=False)
    cust_inspection_tdate = DMYDateField(label="Cust. Inspection Date To", required=False)
    manuf_material_date = DMYDateField(label="Manufacturing Material Date", required=False)
    boughtout_material_date = DMYDateField(label="Boughtout Material Date", required=False)

    class Meta:
        model = WorkOrderMaster
        fields = [
            'customer', 'enquiry_id', 'po_number', 'po_id', 'work_order_date',
            'project_title', 'project_leader', 'category', 'subcategory', 'business_group',
            'target_dap_fdate', 'target_dap_tdate', 'design_finalization_fdate', 'design_finalization_tdate',
            'target_manufg_fdate', 'target_manufg_tdate', 'target_try_out_fdate', 'target_try_out_tdate',
            'target_despatch_fdate', 'target_despatch_tdate', 'target_assembly_fdate', 'target_assembly_tdate',
            'target_installation_fdate', 'target_installation_tdate', 'cust_inspection_fdate', 'cust_inspection_tdate',
            'manuf_material_date', 'boughtout_material_date', 'buyer',
            'shipping_address', 'shipping_country', 'shipping_state', 'shipping_city',
            'shipping_contact_person1', 'shipping_contact_no1', 'shipping_email1',
            'shipping_contact_person2', 'shipping_contact_no2', 'shipping_email2',
            'shipping_fax_no', 'shipping_ecc_no', 'shipping_tin_cst_no', 'shipping_tin_vat_no',
            'instruction_primer_painting', 'instruction_painting', 'instruction_self_cert_rept',
            'instruction_other', 'instruction_export_case_mark', 'instruction_attach_annexure'
        ]
        widgets = {
            'customer': forms.HiddenInput(), # Customer ID from query string
            'enquiry_id': forms.HiddenInput(), # Enquiry ID from query string
            'po_number': forms.HiddenInput(), # PO Number from query string
            'po_id': forms.HiddenInput(), # PO ID from query string

            'project_title': forms.TextInput(attrs={'class': 'box3', 'width': '400px'}),
            'project_leader': forms.TextInput(attrs={'class': 'box3', 'width': '300px'}),
            'shipping_address': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'cols': 40}),
            'shipping_contact_person1': forms.TextInput(attrs={'class': 'box3'}),
            'shipping_contact_no1': forms.TextInput(attrs={'class': 'box3'}),
            'shipping_email1': forms.EmailInput(attrs={'class': 'box3'}),
            'shipping_contact_person2': forms.TextInput(attrs={'class': 'box3'}),
            'shipping_contact_no2': forms.TextInput(attrs={'class': 'box3'}),
            'shipping_email2': forms.EmailInput(attrs={'class': 'box3'}),
            'shipping_fax_no': forms.TextInput(attrs={'class': 'box3'}),
            'shipping_ecc_no': forms.TextInput(attrs={'class': 'box3'}),
            'shipping_tin_cst_no': forms.TextInput(attrs={'class': 'box3'}),
            'shipping_tin_vat_no': forms.TextInput(attrs={'class': 'box3'}),
            'instruction_other': forms.TextInput(attrs={'class': 'box3'}),
            'instruction_export_case_mark': forms.TextInput(attrs={'class': 'box3'}),
            'instruction_attach_annexure': forms.FileInput(attrs={'class': 'box3'}), # Or custom widget for multi-file upload
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply Tailwind CSS classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Textarea, forms.Select, forms.EmailInput, forms.FileInput)):
                current_classes = field.widget.attrs.get('class', '')
                field.widget.attrs['class'] = f'{current_classes} block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'.strip()
            elif isinstance(field.widget, forms.CheckboxInput):
                current_classes = field.widget.attrs.get('class', '')
                field.widget.attrs['class'] = f'{current_classes} h-4 w-4 text-indigo-600 border-gray-300 rounded'.strip()

        # Initial queryset for subcategory, state, city if instance is being edited
        if self.instance and self.instance.pk:
            if self.instance.category:
                self.fields['subcategory'].queryset = self.instance.category.subcategories.all().order_by('subcategory_name')
            if self.instance.shipping_country:
                self.fields['shipping_state'].queryset = self.instance.shipping_country.states.all().order_by('state_name')
            if self.instance.shipping_state:
                self.fields['shipping_city'].queryset = self.instance.shipping_state.cities.all().order_by('city_name')

    def clean(self):
        cleaned_data = super().clean()

        # Replicate ASP.NET's `ReqWoType0.Visible = true/false` logic
        category = cleaned_data.get('category')
        subcategory = cleaned_data.get('subcategory')
        if category and category.has_subcat and not subcategory:
            self.add_error('subcategory', "Subcategory is required for this WO type.")
            
        # Comprehensive date range validation (if applicable)
        date_fields = [
            ('target_dap_fdate', 'target_dap_tdate', 'Target DAP Date'),
            ('design_finalization_fdate', 'design_finalization_tdate', 'Design Finalization Date'),
            ('target_manufg_fdate', 'target_manufg_tdate', 'Target Manufacturing Date'),
            ('target_try_out_fdate', 'target_try_out_tdate', 'Target Try-out Date'),
            ('target_despatch_fdate', 'target_despatch_tdate', 'Target Despatch Date'),
            ('target_assembly_fdate', 'target_assembly_tdate', 'Target Assembly Date'),
            ('target_installation_fdate', 'target_installation_tdate', 'Target Installation Date'),
            ('cust_inspection_fdate', 'cust_inspection_tdate', 'Customer Inspection Date'),
        ]

        for fdate_name, tdate_name, label in date_fields:
            fdate = cleaned_data.get(fdate_name)
            tdate = cleaned_data.get(tdate_name)

            if fdate and not tdate:
                self.add_error(tdate_name, f"{label} 'To' date is required if 'From' date is provided.")
            if tdate and not fdate:
                self.add_error(fdate_name, f"{label} 'From' date is required if 'To' date is provided.")
            if fdate and tdate and fdate > tdate:
                self.add_error(fdate_name, f"{label} 'From' date cannot be after 'To' date.")
                self.add_error(tdate_name, f"{label} 'To' date cannot be before 'From' date.")
        
        # Email validations (RegEmail1, RegEmail2) are handled by forms.EmailInput
        # Phone numbers, fax, TIN/CST etc. if they need specific formats, add regex validators here.
        # Example for contact number (if strict numeric)
        if cleaned_data.get('shipping_contact_no1') and not re.fullmatch(r'^\d+$', cleaned_data['shipping_contact_no1']):
            self.add_error('shipping_contact_no1', "Contact No 1 must contain only digits.")

        return cleaned_data


class WorkOrderProductForm(forms.ModelForm):
    class Meta:
        model = WorkOrderProduct
        fields = ['item_code', 'description', 'quantity']
        widgets = {
            'item_code': forms.TextInput(attrs={'class': 'box3', 'width': '180px'}),
            'description': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'cols': 40}),
            'quantity': forms.NumberInput(attrs={'class': 'box3'}),
        }
        labels = {
            'item_code': 'Item Code',
            'description': 'Desc. of Item',
            'quantity': 'Qty',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            current_classes = field.widget.attrs.get('class', '')
            field.widget.attrs['class'] = f'{current_classes} block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'.strip()

    def clean_quantity(self):
        # Replicate ASP.NET's `^\d{1,15}(\.\d{0,3})?$` regex for quantity
        quantity = self.cleaned_data['quantity']
        if quantity is not None:
            # Convert to string for regex check
            qty_str = str(quantity)
            if not re.fullmatch(r'^\d{1,15}(\.\d{0,3})?$', qty_str):
                raise forms.ValidationError("Quantity must be a number with up to 15 digits before and 3 digits after the decimal point.")
        return quantity

```

### 4.3 Views

**Task:** Implement CRUD operations and dynamic content loading using Django Class-Based Views (CBVs), keeping views thin by delegating logic to models.

**Instructions:**
Our AI will generate views for the main Work Order creation/update flow and for the dynamic product list management. We'll use specific HTMX triggers to update parts of the page.

```python
# workorders/views.py
from django.views.generic import FormView, ListView, View
from django.views.generic.edit import UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.conf import settings # For SESSION_COOKIE_NAME if needed

from .models import WorkOrderMaster, WorkOrderProduct, WOCategory, WOSubCategory, Country, State, City
from .forms import WorkOrderMasterForm, WorkOrderProductForm

import uuid # For session ID in temp products

# --- Main Work Order Views ---

class WorkOrderCreateView(FormView):
    template_name = 'workorders/workorder_new_details.html'
    form_class = WorkOrderMasterForm
    success_url = reverse_lazy('workorder_list') # Redirect to a list view after successful creation

    def get_initial(self):
        initial = super().get_initial()
        # Extract initial data from query parameters, mirroring ASP.NET's Request.QueryString
        initial['customer'] = self.request.GET.get('CustomerId')
        initial['po_number'] = self.request.GET.get('PONo')
        initial['po_id'] = self.request.GET.get('PoId')
        initial['enquiry_id'] = self.request.GET.get('EnqId')

        # Populate customer name for display
        if initial['customer']:
            customer_obj = Customer.objects.filter(customer_id=initial['customer']).first()
            if customer_obj:
                self.request.session['customer_name'] = customer_obj.customer_name
        
        # Initialize an empty list for products in session if not present
        if 'work_order_products' not in self.request.session:
            self.request.session['work_order_products'] = []
        
        # Manage active tab state in session
        if 'active_tab_index' not in self.request.session:
            self.request.session['active_tab_index'] = 0 # Default to first tab
        
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['customer_name'] = self.request.session.get('customer_name', 'N/A')
        context['po_number_display'] = self.request.GET.get('PONo', 'N/A')
        context['active_tab_index'] = self.request.session.get('active_tab_index', 0)
        context['product_form'] = WorkOrderProductForm()
        # Products for the grid come from session
        context['work_order_products'] = self.request.session.get('work_order_products', [])
        return context

    def form_valid(self, form):
        # All validation passes. Now, save the work order and its products.
        # These values (CompId, FinYearId, SessionId) typically come from user session/context.
        # For this example, we'll use dummy values or assume they are set globally.
        company_id = self.request.session.get('compid', 1) # Default company ID
        financial_year_id = self.request.session.get('finyear', 1) # Default financial year
        current_session_id = str(self.request.session.session_key or uuid.uuid4()) # Unique ID for temp products

        work_order = form.save(commit=False)
        work_order.company_id = company_id
        work_order.financial_year_id = financial_year_id
        work_order.session_id = current_session_id # Associate main WO with session ID for temp products

        try:
            # Call fat model method to handle complex saving logic
            products_to_save = self.request.session.get('work_order_products', [])
            work_order.save_work_order_and_products(products_to_save, current_session_id)

            # Clear temporary products from session after successful submission
            del self.request.session['work_order_products']
            del self.request.session['active_tab_index']
            
            messages.success(self.request, f"Work order '{work_order.work_order_no}' generated successfully.")
            return redirect(self.get_success_url()) # Redirect to WorkOrder_New.aspx list page
        except ValueError as e:
            messages.error(self.request, f"Error generating work order: {e}")
            return self.form_invalid(form)
        except Exception as e:
            messages.error(self.request, f"An unexpected error occurred: {e}")
            return self.form_invalid(form)

    def form_invalid(self, form):
        # Render the form with errors. HTMX will swap the entire form content.
        messages.error(self.request, "Please correct the errors below.")
        response = render(self.request, self.template_name, self.get_context_data(form=form))
        if self.request.headers.get('HX-Request'):
             # If HTMX request, we might want to return only the form part or trigger a full refresh
             # For a full multi-tab form, best to re-render the whole page or trigger a full refresh on error.
             # Or, pass the errors via `HX-Retarget` and `HX-Reswap`.
             # For simplicity, returning the full page will work, but could be optimized.
             return response
        return response

    def post(self, request, *args, **kwargs):
        # Handle 'Next' buttons or the final submit button.
        # Since this is a single form, 'Next' buttons just switch active tab client-side.
        # The final 'Submit' button triggers this `post` method.
        # If the 'Next' buttons were submitting parts of the form, it would be different.
        
        # Check if the "Cancel" button was clicked
        if 'btnCancel' in request.POST:
            del self.request.session['work_order_products']
            del self.request.session['active_tab_index']
            # Redirect to WorkOrder_New.aspx (Django equivalent)
            return redirect(reverse_lazy('workorder_list')) # Assuming 'workorder_list' is the equivalent

        return super().post(request, *args, **kwargs)

# --- HTMX Endpoints for Dynamic Content ---

class SetActiveTab(View):
    def post(self, request):
        tab_index = request.POST.get('tab_index')
        if tab_index is not None:
            request.session['active_tab_index'] = int(tab_index)
            request.session.modified = True
            return HttpResponse(status=204) # No content to send back, just update session
        return HttpResponse(status=400)


class GetSubcategoriesView(View):
    def get(self, request):
        category_id = request.GET.get('category_id')
        subcategories = WOSubCategory.objects.none()
        if category_id:
            try:
                category_obj = WOCategory.objects.get(category_id=category_id)
                subcategories = category_obj.subcategories.all().order_by('subcategory_name')
                
                # Check if subcategory is required
                if category_obj.has_subcat:
                    # In ASP.NET, ReqWoType0.Visible was set. Here, we can add 'required' attr to select
                    # or manage it via Alpine.js on the client. For simple HTMX, return a partial for the dropdown.
                    # This would ideally be handled by rendering a partial `_subcategory_dropdown.html`
                    # that includes the 'required' attribute if needed.
                    is_required = True
                else:
                    is_required = False
            except WOCategory.DoesNotExist:
                pass
        
        # Render just the select element (and its wrapping div if needed)
        context = {'subcategories': subcategories, 'is_required': is_required}
        return render(request, 'workorders/_subcategory_dropdown.html', context)

class GetStatesView(View):
    def get(self, request):
        country_id = request.GET.get('country_id')
        states = State.objects.none()
        if country_id:
            try:
                country_obj = Country.objects.get(country_id=country_id)
                states = country_obj.states.all().order_by('state_name')
            except Country.DoesNotExist:
                pass
        return render(request, 'workorders/_state_dropdown.html', {'states': states})

class GetCitiesView(View):
    def get(self, request):
        state_id = request.GET.get('state_id')
        cities = City.objects.none()
        if state_id:
            try:
                state_obj = State.objects.get(state_id=state_id)
                cities = state_obj.cities.all().order_by('city_name')
            except State.DoesNotExist:
                pass
        return render(request, 'workorders/_city_dropdown.html', {'cities': cities})

# --- Work Order Product CRUD (Session-based for 'Temp' table) ---

class WorkOrderProductAddView(View):
    def post(self, request):
        form = WorkOrderProductForm(request.POST)
        if form.is_valid():
            product_data = form.cleaned_data
            # Add a unique ID for editing/deleting from session list
            product_data['session_product_id'] = str(uuid.uuid4())
            
            if 'work_order_products' not in request.session:
                request.session['work_order_products'] = []
            
            request.session['work_order_products'].append(product_data)
            request.session.modified = True # Important to save session changes for mutable objects

            messages.success(request, 'Product added successfully.')
            # Clear form inputs after successful add
            response = render(request, 'workorders/_product_form.html', {'product_form': WorkOrderProductForm()})
            # Trigger refresh of the product list table
            response['HX-Trigger'] = 'refreshProductList'
            return response
        else:
            messages.error(request, 'Please correct product form errors.')
            return render(request, 'workorders/_product_form.html', {'product_form': form})

class WorkOrderProductListView(View):
    def get(self, request):
        # Retrieve products from session to display in DataTables
        products = request.session.get('work_order_products', [])
        return render(request, 'workorders/_product_table.html', {'products': products})

class WorkOrderProductEditView(View):
    def get(self, request, pk):
        products = request.session.get('work_order_products', [])
        product_to_edit = next((p for p in products if p['session_product_id'] == pk), None)
        if product_to_edit:
            form = WorkOrderProductForm(initial=product_to_edit)
            return render(request, 'workorders/_product_form_modal.html', {'product_form': form, 'session_product_id': pk})
        return HttpResponse(status=404, content="Product not found in session.")

    def post(self, request, pk):
        products = request.session.get('work_order_products', [])
        product_index = next((i for i, p in enumerate(products) if p['session_product_id'] == pk), None)

        if product_index is not None:
            form = WorkOrderProductForm(request.POST)
            if form.is_valid():
                updated_data = form.cleaned_data
                updated_data['session_product_id'] = pk # Keep the same ID
                products[product_index] = updated_data
                request.session['work_order_products'] = products
                request.session.modified = True
                messages.success(request, 'Product updated successfully.')
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshProductList'
                    }
                ) # No content, close modal, trigger refresh
            else:
                messages.error(request, 'Please correct product form errors.')
                return render(request, 'workorders/_product_form_modal.html', {'product_form': form, 'session_product_id': pk})
        return HttpResponse(status=404, content="Product not found in session.")

class WorkOrderProductDeleteView(View):
    def get(self, request, pk):
        # Just return a confirmation modal for deletion
        products = request.session.get('work_order_products', [])
        product_to_delete = next((p for p in products if p['session_product_id'] == pk), None)
        if product_to_delete:
            return render(request, 'workorders/_product_confirm_delete.html', {'product': product_to_delete, 'session_product_id': pk})
        return HttpResponse(status=404, content="Product not found in session.")

    def post(self, request, pk):
        products = request.session.get('work_order_products', [])
        products = [p for p in products if p['session_product_id'] != pk]
        request.session['work_order_products'] = products
        request.session.modified = True
        messages.success(request, 'Product deleted successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshProductList'
            }
        ) # No content, close modal, trigger refresh

# --- Simple List View for redirection after submission ---
class WorkOrderListView(ListView):
    model = WorkOrderMaster
    template_name = 'workorders/list.html' # This would be WorkOrder_New.aspx's list view
    context_object_name = 'work_orders'
    paginate_by = 10 # Example pagination

```

### 4.4 Templates

**Task:** Create HTML templates for each view, incorporating HTMX for dynamic interactions, Alpine.js for local UI state, and DataTables for list presentation.

**Instructions:**
Our AI will generate the main multi-tab form, partials for dropdowns, product forms, and the product list table. All templates will extend `core/base.html`.

```html
<!-- workorders/workorder_new_details.html -->
{% extends 'core/base.html' %}
{% load static %}

{% block title %}Work Order - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <h2 class="text-2xl font-bold">Work Order - New</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="mb-4">
            <p class="font-semibold">Customer Name: <span class="font-normal" id="lblCustomerName">{{ customer_name }}</span></p>
            <p class="font-semibold">PO No. : <span class="font-bold" id="lblPONo">{{ po_number_display }}</span></p>
        </div>

        <div x-data="{ activeTab: {{ active_tab_index }} }" class="space-y-4">
            <!-- Tab Headers -->
            <div class="flex border-b border-gray-200">
                <button @click="activeTab = 0; $dispatch('set-active-tab', { tab_index: 0 })"
                        :class="{ 'border-b-2 border-indigo-500 text-indigo-600': activeTab === 0, 'text-gray-500': activeTab !== 0 }"
                        class="px-4 py-2 font-medium text-sm focus:outline-none transition-colors duration-200"
                        hx-post="{% url 'set_active_tab' %}" hx-vals='js:{tab_index: 0}' hx-swap="none" hx-trigger="click">
                    Task Execution
                </button>
                <button @click="activeTab = 1; $dispatch('set-active-tab', { tab_index: 1 })"
                        :class="{ 'border-b-2 border-indigo-500 text-indigo-600': activeTab === 1, 'text-gray-500': activeTab !== 1 }"
                        class="px-4 py-2 font-medium text-sm focus:outline-none transition-colors duration-200"
                        hx-post="{% url 'set_active_tab' %}" hx-vals='js:{tab_index: 1}' hx-swap="none" hx-trigger="click">
                    Shipping
                </button>
                <button @click="activeTab = 2; $dispatch('set-active-tab', { tab_index: 2 })"
                        :class="{ 'border-b-2 border-indigo-500 text-indigo-600': activeTab === 2, 'text-gray-500': activeTab !== 2 }"
                        class="px-4 py-2 font-medium text-sm focus:outline-none transition-colors duration-200"
                        hx-post="{% url 'set_active_tab' %}" hx-vals='js:{tab_index: 2}' hx-swap="none" hx-trigger="click">
                    Products
                </button>
                <button @click="activeTab = 3; $dispatch('set-active-tab', { tab_index: 3 })"
                        :class="{ 'border-b-2 border-indigo-500 text-indigo-600': activeTab === 3, 'text-gray-500': activeTab !== 3 }"
                        class="px-4 py-2 font-medium text-sm focus:outline-none transition-colors duration-200"
                        hx-post="{% url 'set_active_tab' %}" hx-vals='js:{tab_index: 3}' hx-swap="none" hx-trigger="click">
                    Instructions
                </button>
            </div>

            <!-- Tab Content -->
            <form method="post" class="space-y-6" hx-post="." hx-swap="outerHTML" hx-target="#form-container">
                {% csrf_token %}
                {{ form.customer }}
                {{ form.po_number }}
                {{ form.po_id }}
                {{ form.enquiry_id }}
                {{ form.company_id }}
                {{ form.financial_year_id }}

                <div x-show="activeTab === 0" class="tab-content space-y-4" x-transition>
                    <h3 class="text-xl font-semibold mb-4">Task Execution Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.category.label }}</label>
                            {{ form.category }}
                            {% if form.category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>{% endif %}
                        </div>
                        <div id="id_subcategory_container">
                            <!-- Subcategory dropdown will be loaded here via HTMX -->
                            {% include 'workorders/_subcategory_dropdown.html' with subcategories=form.subcategory.field.queryset is_required=form.fields.subcategory.required %}
                        </div>
                        <div>
                            <label for="{{ form.work_order_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.work_order_date.label }}</label>
                            {{ form.work_order_date }}
                            {% if form.work_order_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_order_date.errors }}</p>{% endif %}
                        </div>
                        <div class="md:col-span-2 lg:col-span-3">
                            <label for="{{ form.project_title.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.project_title.label }}</label>
                            {{ form.project_title }}
                            {% if form.project_title.errors %}<p class="text-red-500 text-xs mt-1">{{ form.project_title.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.project_leader.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.project_leader.label }}</label>
                            {{ form.project_leader }}
                            {% if form.project_leader.errors %}<p class="text-red-500 text-xs mt-1">{{ form.project_leader.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.business_group.label }}</label>
                            {{ form.business_group }}
                            {% if form.business_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.business_group.errors }}</p>{% endif %}
                        </div>
                    </div>

                    <h4 class="text-lg font-medium mt-6 mb-4">Target Dates</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for f_field, t_field, label in form.date_fields_pairs %}
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ label }} From</label>
                                {{ f_field }}
                                {% if f_field.errors %}<p class="text-red-500 text-xs mt-1">{{ f_field.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ label }} To</label>
                                {{ t_field }}
                                {% if t_field.errors %}<p class="text-red-500 text-xs mt-1">{{ t_field.errors }}</p>{% endif %}
                            </div>
                        {% endfor %}
                    </div>
                    <h4 class="text-lg font-medium mt-6 mb-4">Material Procurement</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="{{ form.manuf_material_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.manuf_material_date.label }}</label>
                            {{ form.manuf_material_date }}
                            {% if form.manuf_material_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.manuf_material_date.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.buyer.label }}</label>
                            {{ form.buyer }}
                            {% if form.buyer.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.boughtout_material_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.boughtout_material_date.label }}</label>
                            {{ form.boughtout_material_date }}
                            {% if form.boughtout_material_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.boughtout_material_date.errors }}</p>{% endif %}
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="button" @click="activeTab = 1; $dispatch('set-active-tab', { tab_index: 1 })"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Next
                        </button>
                    </div>
                </div>

                <div x-show="activeTab === 1" class="tab-content space-y-4" x-transition>
                    <h3 class="text-xl font-semibold mb-4">Shipping Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="{{ form.shipping_address.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_address.label }}</label>
                            {{ form.shipping_address }}
                            {% if form.shipping_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_address.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_country.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_country.label }}</label>
                            {{ form.shipping_country }}
                            {% if form.shipping_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_country.errors }}</p>{% endif %}
                        </div>
                        <div id="id_shipping_state_container">
                            <!-- State dropdown will be loaded here via HTMX -->
                            {% include 'workorders/_state_dropdown.html' with states=form.shipping_state.field.queryset %}
                        </div>
                        <div id="id_shipping_city_container">
                            <!-- City dropdown will be loaded here via HTMX -->
                            {% include 'workorders/_city_dropdown.html' with cities=form.shipping_city.field.queryset %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_contact_person1.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_contact_person1.label }}</label>
                            {{ form.shipping_contact_person1 }}
                            {% if form.shipping_contact_person1.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_person1.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_contact_no1.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_contact_no1.label }}</label>
                            {{ form.shipping_contact_no1 }}
                            {% if form.shipping_contact_no1.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_no1.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_email1.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_email1.label }}</label>
                            {{ form.shipping_email1 }}
                            {% if form.shipping_email1.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_email1.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_contact_person2.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_contact_person2.label }}</label>
                            {{ form.shipping_contact_person2 }}
                            {% if form.shipping_contact_person2.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_person2.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_contact_no2.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_contact_no2.label }}</label>
                            {{ form.shipping_contact_no2 }}
                            {% if form.shipping_contact_no2.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_no2.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_email2.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_email2.label }}</label>
                            {{ form.shipping_email2 }}
                            {% if form.shipping_email2.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_email2.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_fax_no.label }}</label>
                            {{ form.shipping_fax_no }}
                            {% if form.shipping_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_fax_no.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_ecc_no.label }}</label>
                            {{ form.shipping_ecc_no }}
                            {% if form.shipping_ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_ecc_no.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_tin_cst_no.label }}</label>
                            {{ form.shipping_tin_cst_no }}
                            {% if form.shipping_tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_tin_cst_no.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.shipping_tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.shipping_tin_vat_no.label }}</label>
                            {{ form.shipping_tin_vat_no }}
                            {% if form.shipping_tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_tin_vat_no.errors }}</p>{% endif %}
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="button" @click="activeTab = 2; $dispatch('set-active-tab', { tab_index: 2 })"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Next
                        </button>
                    </div>
                </div>

                <div x-show="activeTab === 2" class="tab-content space-y-4" x-transition>
                    <h3 class="text-xl font-semibold mb-4">Products</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Product Add/Edit Form -->
                        <div id="product-form-container" class="lg:col-span-1 bg-gray-50 p-4 rounded-lg shadow-inner">
                            {% include 'workorders/_product_form.html' with product_form=product_form %}
                        </div>

                        <!-- Product List Table -->
                        <div id="product-list-container" class="lg:col-span-2 overflow-x-auto"
                             hx-trigger="load, refreshProductList from:body"
                             hx-get="{% url 'workorder_product_list' %}"
                             hx-swap="innerHTML">
                            <!-- Products table will be loaded here via HTMX -->
                            <div class="text-center py-8">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                <p class="mt-2 text-gray-600">Loading products...</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="button" @click="activeTab = 3; $dispatch('set-active-tab', { tab_index: 3 })"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Next
                        </button>
                    </div>
                </div>

                <div x-show="activeTab === 3" class="tab-content space-y-4" x-transition>
                    <h3 class="text-xl font-semibold mb-4">Instructions</h3>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            {{ form.instruction_primer_painting }}
                            <label for="{{ form.instruction_primer_painting.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Primer Painting to be done.</label>
                        </div>
                        <div class="flex items-center">
                            {{ form.instruction_painting }}
                            <label for="{{ form.instruction_painting.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Painting to be done.</label>
                        </div>
                        <div class="flex items-center">
                            {{ form.instruction_self_cert_rept }}
                            <label for="{{ form.instruction_self_cert_rept.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">Self Certification Report to be submitted.</label>
                        </div>
                        <div>
                            <label for="{{ form.instruction_other.id_for_label }}" class="block text-sm font-medium text-gray-700">Others</label>
                            {{ form.instruction_other }}
                            {% if form.instruction_other.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_other.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.instruction_export_case_mark.id_for_label }}" class="block text-sm font-medium text-gray-700">Export Case Mark</label>
                            {{ form.instruction_export_case_mark }}
                            {% if form.instruction_export_case_mark.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_export_case_mark.errors }}</p>{% endif %}
                        </div>
                        <!-- Annexure field -->
                        <div>
                            <label for="{{ form.instruction_attach_annexure.id_for_label }}" class="block text-sm font-medium text-gray-700">Attach Annexure</label>
                            {{ form.instruction_attach_annexure }}
                            {% if form.instruction_attach_annexure.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_attach_annexure.errors }}</p>{% endif %}
                        </div>
                        <p class="text-sm font-bold text-gray-600">*Packing Instructions : Export Seaworthy / Wooden / Corrugated 7 day before desp.</p>
                    </div>
                </div>
                
                <div class="mt-8 flex justify-center space-x-4">
                    <button type="submit" name="btnSubmit" value="Submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                        Submit
                    </button>
                    <button type="submit" name="btnCancel" value="Cancel" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for product forms -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
     _="on click if event.target.id == 'modal' remove .hidden from #modal">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full"></div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderTabs', () => ({
            activeTab: 0,
            init() {
                // Initialize activeTab from session (passed from Django context)
                this.activeTab = {{ active_tab_index }};
                
                // Listen for custom event to update active tab (from HTMX calls)
                this.$root.addEventListener('set-active-tab', (event) => {
                    this.activeTab = event.detail.tab_index;
                });
            }
        }));
    });

    document.addEventListener('htmx:afterRequest', function(evt) {
        // Handle HTMX response for modals
        if (evt.detail.target.id === 'modalContent' && (evt.detail.xhr.status === 200 || evt.detail.xhr.status === 204)) {
            // Add 'is-active' or remove 'hidden' class to show the modal
            document.getElementById('modal').classList.remove('hidden');
        }
    });

    document.addEventListener('htmx:afterSwap', function(evt) {
        // If the product list table was swapped, reinitialize DataTables
        if (evt.detail.target.id === 'product-list-container' || evt.detail.elt.id === 'product-list-container') {
             // Destroy existing DataTable instance if it exists
            if ($.fn.DataTable.isDataTable('#productTable')) {
                $('#productTable').DataTable().destroy();
            }
            $('#productTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });
</script>
{% endblock %}
```

```html
<!-- workorders/_subcategory_dropdown.html -->
<label for="id_subcategory" class="block text-sm font-medium text-gray-700">Subcategory</label>
<select name="subcategory" id="id_subcategory" 
        class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        {% if is_required %}required{% endif %}>
    <option value="">Select</option>
    {% for subcategory in subcategories %}
        <option value="{{ subcategory.subcategory_id }}" {% if subcategory.subcategory_id == form.subcategory.value %}selected{% endif %}>{{ subcategory.subcategory_name }}</option>
    {% endfor %}
</select>
{% if form.subcategory.errors %}<p class="text-red-500 text-xs mt-1">{{ form.subcategory.errors }}</p>{% endif %}

```

```html
<!-- workorders/_state_dropdown.html -->
<label for="id_shipping_state" class="block text-sm font-medium text-gray-700">State</label>
<select name="shipping_state" id="id_shipping_state"
        class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        hx-get="{% url 'get_cities' %}" hx-target="#id_shipping_city_container" hx-swap="outerHTML">
    <option value="">Select</option>
    {% for state in states %}
        <option value="{{ state.state_id }}" {% if state.state_id == form.shipping_state.value %}selected{% endif %}>{{ state.state_name }}</option>
    {% endfor %}
</select>
{% if form.shipping_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_state.errors }}</p>{% endif %}

```

```html
<!-- workorders/_city_dropdown.html -->
<label for="id_shipping_city" class="block text-sm font-medium text-gray-700">City</label>
<select name="shipping_city" id="id_shipping_city"
        class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    <option value="">Select</option>
    {% for city in cities %}
        <option value="{{ city.city_id }}" {% if city.city_id == form.shipping_city.value %}selected{% endif %}>{{ city.city_name }}</option>
    {% endfor %}
</select>
{% if form.shipping_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_city.errors }}</p>{% endif %}

```

```html
<!-- workorders/_product_form.html -->
<h4 class="text-lg font-semibold mb-4">{{ product_form.instance.pk|yesno:'Edit,Add' }} Product</h4>
<form hx-post="{% if session_product_id %}{% url 'workorder_product_edit' session_product_id %}{% else %}{% url 'workorder_product_add' %}{% endif %}" 
      hx-swap="outerHTML" 
      hx-target="#product-form-container"
      _="on htmx:afterRequest if event.detail.xhr.status == 204 remove .hidden from #modal">
    {% csrf_token %}
    <div class="space-y-4">
        <div>
            <label for="{{ product_form.item_code.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ product_form.item_code.label }}</label>
            {{ product_form.item_code }}
            {% if product_form.item_code.errors %}<p class="text-red-500 text-xs mt-1">{{ product_form.item_code.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ product_form.description.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ product_form.description.label }}</label>
            {{ product_form.description }}
            {% if product_form.description.errors %}<p class="text-red-500 text-xs mt-1">{{ product_form.description.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ product_form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ product_form.quantity.label }}</label>
            {{ product_form.quantity }}
            {% if product_form.quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ product_form.quantity.errors }}</p>{% endif %}
        </div>
    </div>
    <div class="mt-6 flex items-center justify-end space-x-4">
        {% if session_product_id %}
            <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                    _="on click add .hidden to #modal">
                Cancel
            </button>
            <button type="submit" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                Update Product
            </button>
        {% else %}
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    onclick="return confirmationAdd()">
                Add Product
            </button>
        {% endif %}
    </div>
</form>

<script>
    // Confirmation message for adding product (from ASP.NET OnClientClick)
    function confirmationAdd() {
        return confirm("Do you really want to add this product?");
    }
</script>

```

```html
<!-- workorders/_product_form_modal.html -->
<!-- This partial is used when editing a product from the list, loaded into the modal -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Product</h3>
    {% include 'workorders/_product_form.html' with product_form=product_form session_product_id=session_product_id %}
</div>
```

```html
<!-- workorders/_product_confirm_delete.html -->
<!-- This partial is used for delete confirmation, loaded into the modal -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p>Are you sure you want to delete item code: <strong>{{ product.item_code }}</strong>?</p>
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click add .hidden to #modal">
            Cancel
        </button>
        <button hx-post="{% url 'workorder_product_delete' session_product_id %}" 
                hx-swap="none"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                _="on click add .hidden to #modal">
            Delete
        </button>
    </div>
</div>
```

```html
<!-- workorders/_product_table.html -->
<!-- This partial is loaded into product-list-container for DataTables -->
<table id="productTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for product in products %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ product.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ product.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ product.quantity }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'workorder_product_edit' product.session_product_id %}"
                    hx-target="#modalContent"
                    _="on click remove .hidden from #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'workorder_product_delete' product.session_product_id %}"
                    hx-target="#modalContent"
                    _="on click remove .hidden from #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No products added yet.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization is handled in workorder_new_details.html's extra_js block
    // to ensure it runs after the content is swapped.
    // However, for DataTables to work on the swapped content, it needs to be re-initialized.
    // The main template's htmx:afterSwap event listener handles this.
</script>

```

```html
<!-- workorders/list.html (Simplified placeholder for redirection target) -->
{% extends 'core/base.html' %}

{% block title %}Work Orders List{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Work Orders List</h2>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">{{ message }}</span>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white shadow-md rounded-lg p-6">
        <table id="workOrdersTable" class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No.</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO Date</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for wo in work_orders %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200">{{ wo.work_order_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ wo.customer.customer_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ wo.project_title }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ wo.work_order_date|date:"d-m-Y" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        <!-- Example actions -->
                        <a href="#" class="text-blue-600 hover:text-blue-900 mr-2">View</a>
                        <a href="#" class="text-yellow-600 hover:text-yellow-900 mr-2">Edit</a>
                        <a href="#" class="text-red-600 hover:text-red-900">Delete</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="py-4 px-4 text-center text-gray-500">No work orders found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% if is_paginated %}
        <nav class="flex justify-center mt-4">
            <ul class="flex items-center space-x-2">
                {% if page_obj.has_previous %}
                    <li><a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-1 rounded-md bg-gray-200 hover:bg-gray-300">&laquo; Previous</a></li>
                {% endif %}
                <li><span class="px-3 py-1 text-gray-700">Page {{ page_obj.number }} of {{ page_obj.num_pages }}</span></li>
                {% if page_obj.has_next %}
                    <li><a href="?page={{ page_obj.next_page_number }}" class="px-3 py-1 rounded-md bg-gray-200 hover:bg-gray-300">Next &raquo;</a></li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<script>
    $(document).ready(function() {
        $('#workOrdersTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true
        });
    });
</script>
{% endblock %}
```

### 4.5 URLs

**Task:** Define URL patterns for all views, including HTMX-specific endpoints.

**Instructions:**
Our AI will generate `urls.py` entries.

```python
# workorders/urls.py
from django.urls import path
from .views import (
    WorkOrderCreateView, WorkOrderListView,
    WorkOrderProductAddView, WorkOrderProductListView,
    WorkOrderProductEditView, WorkOrderProductDeleteView,
    GetSubcategoriesView, GetStatesView, GetCitiesView, SetActiveTab
)

urlpatterns = [
    # Main Work Order Creation/Update
    path('new/', WorkOrderCreateView.as_view(), name='workorder_create'),
    path('', WorkOrderListView.as_view(), name='workorder_list'), # For the "WorkOrder_New.aspx" equivalent list

    # HTMX Endpoints for Product Management (within the creation form)
    path('products/add/', WorkOrderProductAddView.as_view(), name='workorder_product_add'),
    path('products/list/', WorkOrderProductListView.as_view(), name='workorder_product_list'),
    path('products/edit/<str:pk>/', WorkOrderProductEditView.as_view(), name='workorder_product_edit'),
    path('products/delete/<str:pk>/', WorkOrderProductDeleteView.as_view(), name='workorder_product_delete'),

    # HTMX Endpoints for Cascading Dropdowns
    path('get-subcategories/', GetSubcategoriesView.as_view(), name='get_subcategories'),
    path('get-states/', GetStatesView.as_view(), name='get_states'),
    path('get-cities/', GetCitiesView.as_view(), name='get_cities'),
    
    # HTMX Endpoint for setting active tab (client-side state management)
    path('set-active-tab/', SetActiveTab.as_view(), name='set_active_tab'),
]

# In your project's main urls.py, you would include this:
# from django.urls import include, path
# urlpatterns = [
#     path('workorders/', include('workorders.urls')),
#     # ... other paths
# ]
```

### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views, aiming for at least 80% test coverage.

**Instructions:**
Our AI will generate test cases covering model logic, form validation, and view functionality, including HTMX interactions.

```python
# workorders/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.contrib.messages import get_messages
from unittest.mock import patch, MagicMock

from .models import WorkOrderMaster, WorkOrderProduct, Customer, WOCategory, WOSubCategory, Country, State, City, Buyer, BusinessGroup
from .forms import WorkOrderMasterForm, WorkOrderProductForm

import datetime
import json

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required lookup data
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer')
        cls.category_no_sub = WOCategory.objects.create(category_id=1, symbol='NOCAT', category_name='No Subcategory', has_subcat=False)
        cls.category_with_sub = WOCategory.objects.create(category_id=2, symbol='SUBCAT', category_name='With Subcategory', has_subcat=True)
        cls.subcategory1 = WOSubCategory.objects.create(subcategory_id=101, category=cls.category_with_sub, symbol='SUB1', subcategory_name='Sub Cat 1')
        cls.country = Country.objects.create(country_id=1, country_name='Country A')
        cls.state = State.objects.create(state_id=1, state_name='State A', country=cls.country)
        cls.city = City.objects.create(city_id=1, city_name='City A', state=cls.state)
        cls.buyer = Buyer.objects.create(buyer_id=1, buyer_name='Test Buyer')
        cls.business_group = BusinessGroup.objects.create(bg_id=1, bg_name='Test BG')

    def create_sample_work_order(self, wo_no="WO-TEST-0001", customer=None, category=None, subcategory=None):
        return WorkOrderMaster.objects.create(
            customer=customer or self.customer,
            enquiry_id=123,
            po_number='PO-001',
            po_id='POID-001',
            work_order_no=wo_no,
            work_order_date='2023-01-01',
            project_title='Test Project',
            project_leader='John Doe',
            category=category or self.category_no_sub,
            subcategory=subcategory,
            business_group=self.business_group,
            shipping_address='123 Main St',
            shipping_country=self.country,
            shipping_state=self.state,
            shipping_city=self.city,
            shipping_contact_person1='Jane Doe',
            shipping_contact_no1='1234567890',
            shipping_email1='<EMAIL>',
            company_id=1,
            financial_year_id=1,
            session_id='test_session_id'
        )

    def test_work_order_creation(self):
        wo = self.create_sample_work_order()
        self.assertEqual(wo.customer.customer_name, 'Test Customer')
        self.assertEqual(wo.work_order_no, 'WO-TEST-0001')
        self.assertFalse(wo.instruction_primer_painting)

    def test_generate_work_order_number(self):
        # Test generation for a category without existing WOs
        wo_no1 = WorkOrderMaster.generate_work_order_number(self.category_no_sub.category_id, company_id=1)
        self.assertEqual(wo_no1, f"{self.category_no_sub.symbol}0001")

        # Create one WO and test next number
        self.create_sample_work_order(wo_no=f"{self.category_no_sub.symbol}0001", category=self.category_no_sub)
        wo_no2 = WorkOrderMaster.generate_work_order_number(self.category_no_sub.category_id, company_id=1)
        self.assertEqual(wo_no2, f"{self.category_no_sub.symbol}0002")

        # Test with subcategory
        self.create_sample_work_order(wo_no=f"{self.category_with_sub.symbol}0001", category=self.category_with_sub, subcategory=self.subcategory1)
        wo_no3 = WorkOrderMaster.generate_work_order_number(self.category_with_sub.category_id, self.subcategory1.subcategory_id, company_id=1)
        self.assertEqual(wo_no3, f"{self.category_with_sub.symbol}0002") # Assumes sequence is only based on category symbol

        # Test subcategory required logic
        with self.assertRaises(ValueError):
            WorkOrderMaster.generate_work_order_number(self.category_with_sub.category_id, subcategory_id=None, company_id=1)

    def test_save_work_order_and_products(self):
        wo = WorkOrderMaster(
            customer=self.customer,
            enquiry_id=124,
            po_number='PO-002',
            po_id='POID-002',
            work_order_date='2023-01-02',
            project_title='Project with Products',
            project_leader='Jane Doe',
            category=self.category_no_sub,
            business_group=self.business_group,
            shipping_address='456 Elm St',
            shipping_country=self.country,
            shipping_state=self.state,
            shipping_city=self.city,
            shipping_contact_person1='Contact 1',
            shipping_contact_no1='9876543210',
            shipping_email1='<EMAIL>',
            company_id=1,
            financial_year_id=1
        )
        products_data = [
            {'item_code': 'ITEM1', 'description': 'Product A', 'quantity': 10.5},
            {'item_code': 'ITEM2', 'description': 'Product B', 'quantity': 5.0}
        ]
        
        # Mock the WO number generation to avoid conflicts during testing multiple saves
        with patch('workorders.models.WorkOrderMaster.generate_work_order_number', return_value='WO-AUTOGEN-0001'):
            wo.save_work_order_and_products(products_data, 'test_session_xyz')

        self.assertIsNotNone(wo.pk)
        self.assertEqual(wo.work_order_no, 'WO-AUTOGEN-0001')
        self.assertEqual(wo.products.count(), 2)
        self.assertTrue(wo.products.filter(item_code='ITEM1').exists())
        self.assertEqual(wo.products.get(item_code='ITEM1').quantity, 10.5)


class WorkOrderFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required lookup data
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer')
        cls.category_no_sub = WOCategory.objects.create(category_id=1, symbol='NOCAT', category_name='No Subcategory', has_subcat=False)
        cls.category_with_sub = WOCategory.objects.create(category_id=2, symbol='SUBCAT', category_name='With Subcategory', has_subcat=True)
        cls.subcategory1 = WOSubCategory.objects.create(subcategory_id=101, category=cls.category_with_sub, symbol='SUB1', subcategory_name='Sub Cat 1')
        cls.country = Country.objects.create(country_id=1, country_name='Country A')
        cls.state = State.objects.create(state_id=1, state_name='State A', country=cls.country)
        cls.city = City.objects.create(city_id=1, city_name='City A', state=cls.state)
        cls.buyer = Buyer.objects.create(buyer_id=1, buyer_name='Test Buyer')
        cls.business_group = BusinessGroup.objects.create(bg_id=1, bg_name='Test BG')

    def get_valid_data(self):
        return {
            'customer': self.customer.customer_id,
            'enquiry_id': 123,
            'po_number': 'PO-001',
            'po_id': 'POID-001',
            'work_order_date': '01-01-2023', # dd-MM-yyyy format
            'project_title': 'Valid Project Title',
            'project_leader': 'Valid Leader',
            'category': self.category_no_sub.category_id,
            'subcategory': '', # No subcategory for category_no_sub
            'business_group': self.business_group.bg_id,
            'buyer': self.buyer.buyer_id,
            'shipping_address': '123 Test St',
            'shipping_country': self.country.country_id,
            'shipping_state': self.state.state_id,
            'shipping_city': self.city.city_id,
            'shipping_contact_person1': 'Contact One',
            'shipping_contact_no1': '1234567890',
            'shipping_email1': '<EMAIL>',
            'shipping_contact_person2': '',
            'shipping_contact_no2': '',
            'shipping_email2': '',
            'shipping_fax_no': '',
            'shipping_ecc_no': '',
            'shipping_tin_cst_no': '',
            'shipping_tin_vat_no': '',
            'instruction_primer_painting': True,
            'instruction_painting': False,
            'instruction_self_cert_rept': True,
            'instruction_other': 'Some other instruction',
            'instruction_export_case_mark': 'EXPORT MARK',
            'instruction_attach_annexure': None, # File field, will be handled by FileField validation
            # All date range fields are optional and can be left blank initially
            'target_dap_fdate': '', 'target_dap_tdate': '',
            'design_finalization_fdate': '', 'design_finalization_tdate': '',
            'target_manufg_fdate': '', 'target_manufg_tdate': '',
            'target_try_out_fdate': '', 'target_try_out_tdate': '',
            'target_despatch_fdate': '', 'target_despatch_tdate': '',
            'target_assembly_fdate': '', 'target_assembly_tdate': '',
            'target_installation_fdate': '', 'target_installation_tdate': '',
            'cust_inspection_fdate': '', 'cust_inspection_tdate': '',
            'manuf_material_date': '', 'boughtout_material_date': '',
        }

    def test_work_order_master_form_valid(self):
        form = WorkOrderMasterForm(data=self.get_valid_data())
        self.assertTrue(form.is_valid(), form.errors.as_data())

    def test_work_order_master_form_subcategory_required(self):
        data = self.get_valid_data()
        data['category'] = self.category_with_sub.category_id
        data['subcategory'] = '' # Make it missing for category that requires it
        form = WorkOrderMasterForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('subcategory', form.errors)
        self.assertIn('Subcategory is required for this WO type.', form.errors['subcategory'][0])

    def test_work_order_master_form_date_range_validation(self):
        data = self.get_valid_data()
        data['target_dap_fdate'] = '05-01-2023'
        data['target_dap_tdate'] = '01-01-2023' # To date before From date
        form = WorkOrderMasterForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('target_dap_fdate', form.errors)
        self.assertIn('target_dap_tdate', form.errors)
        self.assertIn('Target DAP Date \'From\' date cannot be after \'To\' date.', form.errors['target_dap_fdate'][0])

    def test_work_order_master_form_date_missing_pair_validation(self):
        data = self.get_valid_data()
        data['target_dap_fdate'] = '01-01-2023'
        data['target_dap_tdate'] = '' # Missing To date
        form = WorkOrderMasterForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('target_dap_tdate', form.errors)
        self.assertIn('Target DAP Date \'To\' date is required if \'From\' date is provided.', form.errors['target_dap_tdate'][0])

    def test_work_order_product_form_valid(self):
        form = WorkOrderProductForm(data={'item_code': 'XYZ', 'description': 'Test', 'quantity': 10.234})
        self.assertTrue(form.is_valid())

    def test_work_order_product_form_invalid_qty_format(self):
        form = WorkOrderProductForm(data={'item_code': 'XYZ', 'description': 'Test', 'quantity': 'abc'})
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)

        form = WorkOrderProductForm(data={'item_code': 'XYZ', 'description': 'Test', 'quantity': '123.4567'})
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)
        self.assertIn('Quantity must be a number with up to 15 digits before and 3 digits after the decimal point.', form.errors['quantity'][0])


class WorkOrderViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create minimal required lookup data for views
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer')
        cls.category_no_sub = WOCategory.objects.create(category_id=1, symbol='NOCAT', category_name='No Subcategory', has_subcat=False)
        cls.category_with_sub = WOCategory.objects.create(category_id=2, symbol='SUBCAT', category_name='With Subcategory', has_subcat=True)
        cls.subcategory1 = WOSubCategory.objects.create(subcategory_id=101, category=cls.category_with_sub, symbol='SUB1', subcategory_name='Sub Cat 1')
        cls.country = Country.objects.create(country_id=1, country_name='Country A')
        cls.state = State.objects.create(state_id=1, state_name='State A', country=cls.country)
        cls.city = City.objects.create(city_id=1, city_name='City A', state=cls.state)
        cls.buyer = Buyer.objects.create(buyer_id=1, buyer_name='Test Buyer')
        cls.business_group = BusinessGroup.objects.create(bg_id=1, bg_name='Test BG')

        # Dummy work order for list view
        WorkOrderMaster.objects.create(
            customer=cls.customer, enquiry_id=1, po_number='PO-LIST-001', po_id='L1',
            work_order_no='WO-LIST-0001', work_order_date='2024-01-01',
            project_title='List Item 1', project_leader='Admin',
            category=cls.category_no_sub, business_group=cls.business_group,
            shipping_address='List Address', shipping_country=cls.country,
            shipping_state=cls.state, shipping_city=cls.city,
            shipping_contact_person1='List C1', shipping_contact_no1='123', shipping_email1='<EMAIL>',
            company_id=1, financial_year_id=1
        )

    def get_valid_form_data(self):
        return {
            'customer': self.customer.customer_id,
            'enquiry_id': 123,
            'po_number': 'PO-001',
            'po_id': 'POID-001',
            'work_order_date': '01-01-2023',
            'project_title': 'New Project Title',
            'project_leader': 'New Leader',
            'category': self.category_no_sub.category_id,
            'subcategory': '',
            'business_group': self.business_group.bg_id,
            'buyer': self.buyer.buyer_id,
            'shipping_address': 'New Address',
            'shipping_country': self.country.country_id,
            'shipping_state': self.state.state_id,
            'shipping_city': self.city.city_id,
            'shipping_contact_person1': 'New Contact1',
            'shipping_contact_no1': '1234567890',
            'shipping_email1': '<EMAIL>',
            'instruction_primer_painting': True,
            'instruction_painting': False,
            'instruction_self_cert_rept': False,
            'instruction_other': 'Other notes',
            'instruction_export_case_mark': 'Case Mark',
            'manuf_material_date': '', 'boughtout_material_date': '', # Optional dates
            # All other date range fields
            'target_dap_fdate': '', 'target_dap_tdate': '', 'design_finalization_fdate': '', 'design_finalization_tdate': '',
            'target_manufg_fdate': '', 'target_manufg_tdate': '', 'target_try_out_fdate': '', 'target_try_out_tdate': '',
            'target_despatch_fdate': '', 'target_despatch_tdate': '', 'target_assembly_fdate': '', 'target_assembly_tdate': '',
            'target_installation_fdate': '', 'target_installation_tdate': '', 'cust_inspection_fdate': '', 'cust_inspection_tdate': '',
        }

    # --- WorkOrderCreateView Tests ---
    def test_work_order_create_view_get(self):
        url = reverse('workorder_create') + f'?CustomerId={self.customer.customer_id}&PONo=PO-XYZ&PoId=XYZ&EnqId=1'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder_new_details.html')
        self.assertContains(response, 'Test Customer')
        self.assertContains(response, 'PO-XYZ')
        self.assertIn('work_order_products', self.client.session)
        self.assertEqual(self.client.session['active_tab_index'], 0)

    @patch('workorders.models.WorkOrderMaster.save_work_order_and_products')
    def test_work_order_create_view_post_valid(self, mock_save_method):
        mock_save_method.return_value = 'WO-GEN-0001' # Simulate WO number generation
        
        url = reverse('workorder_create') + f'?CustomerId={self.customer.customer_id}&PONo=PO-XYZ&PoId=XYZ&EnqId=1'
        data = self.get_valid_form_data()
        
        # Add a product to session first
        self.client.post(reverse('workorder_product_add'), {
            'item_code': 'P1', 'description': 'Prod 1', 'quantity': 5.0
        })
        
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertRedirects(response, reverse('workorder_list'))
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Work order 'WO-GEN-0001' generated successfully.")
        
        mock_save_method.assert_called_once()
        self.assertNotIn('work_order_products', self.client.session) # Should be cleared
        self.assertNotIn('active_tab_index', self.client.session) # Should be cleared

    def test_work_order_create_view_post_invalid(self):
        url = reverse('workorder_create') + f'?CustomerId={self.customer.customer_id}&PONo=PO-XYZ&PoId=XYZ&EnqId=1'
        data = self.get_valid_form_data()
        data['project_title'] = '' # Make it invalid
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200) # Rerenders form with errors
        self.assertTemplateUsed(response, 'workorders/workorder_new_details.html')
        self.assertContains(response, 'This field is required.')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Please correct the errors below.")

    def test_work_order_create_view_cancel_button(self):
        url = reverse('workorder_create') + f'?CustomerId={self.customer.customer_id}&PONo=PO-XYZ&PoId=XYZ&EnqId=1'
        response = self.client.post(url, {'btnCancel': 'Cancel'})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('workorder_list'))
        # Ensure session data is cleared
        self.assertNotIn('work_order_products', self.client.session)
        self.assertNotIn('active_tab_index', self.client.session)

    # --- HTMX Endpoint Tests ---
    def test_set_active_tab_view(self):
        response = self.client.post(reverse('set_active_tab'), {'tab_index': 1})
        self.assertEqual(response.status_code, 204)
        self.assertEqual(self.client.session['active_tab_index'], 1)

    def test_get_subcategories_view(self):
        response = self.client.get(reverse('get_subcategories'), {'category_id': self.category_with_sub.category_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_subcategory_dropdown.html')
        self.assertContains(response, 'Sub Cat 1') # Should contain the subcategory

        response = self.client.get(reverse('get_subcategories'), {'category_id': self.category_no_sub.category_id})
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'Sub Cat') # Should not contain any subcategory for 'no_sub' category

    def test_get_states_view(self):
        response = self.client.get(reverse('get_states'), {'country_id': self.country.country_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_state_dropdown.html')
        self.assertContains(response, 'State A')

    def test_get_cities_view(self):
        response = self.client.get(reverse('get_cities'), {'state_id': self.state.state_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_city_dropdown.html')
        self.assertContains(response, 'City A')

    # --- WorkOrderProduct Views Tests (Session-based) ---
    def test_work_order_product_add_view_post_valid(self):
        url = reverse('workorder_product_add')
        data = {'item_code': 'PROD1', 'description': 'Description 1', 'quantity': 10.0}
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_product_form.html') # Renders empty form for next add
        self.assertIn('work_order_products', self.client.session)
        self.assertEqual(len(self.client.session['work_order_products']), 1)
        self.assertEqual(self.client.session['work_order_products'][0]['item_code'], 'PROD1')
        self.assertEqual(response['HX-Trigger'], 'refreshProductList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Product added successfully.')

    def test_work_order_product_add_view_post_invalid(self):
        url = reverse('workorder_product_add')
        data = {'item_code': '', 'description': '', 'quantity': 'abc'} # Invalid data
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_product_form.html')
        self.assertNotContains(response, 'Product added successfully.')
        self.assertIn('item_code', response.content.decode()) # Check for errors in rendered form
        self.assertIn('work_order_products', self.client.session)
        self.assertEqual(len(self.client.session['work_order_products']), 0) # No product added
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Please correct product form errors.')

    def test_work_order_product_list_view(self):
        # Add some products to session
        self.client.post(reverse('workorder_product_add'), {'item_code': 'A', 'description': 'D1', 'quantity': 1}, HTTP_HX_REQUEST='true')
        self.client.post(reverse('workorder_product_add'), {'item_code': 'B', 'description': 'D2', 'quantity': 2}, HTTP_HX_REQUEST='true')

        response = self.client.get(reverse('workorder_product_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_product_table.html')
        self.assertContains(response, 'PROD1') # Should contain the previously added product if session persists
        self.assertContains(response, 'Product A') # Assuming this was the first added product
        self.assertContains(response, 'Product B')

    def test_work_order_product_edit_view_get(self):
        # Add a product to session to get its session_product_id
        add_response = self.client.post(reverse('workorder_product_add'), {'item_code': 'EDIT1', 'description': 'Desc Edit', 'quantity': 15.0}, HTTP_HX_REQUEST='true')
        session_product_id = self.client.session['work_order_products'][0]['session_product_id']

        response = self.client.get(reverse('workorder_product_edit', args=[session_product_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_product_form_modal.html')
        self.assertContains(response, 'EDIT1')
        self.assertContains(response, 'Desc Edit')

    def test_work_order_product_edit_view_post_valid(self):
        add_response = self.client.post(reverse('workorder_product_add'), {'item_code': 'OLD', 'description': 'Old Desc', 'quantity': 10.0}, HTTP_HX_REQUEST='true')
        session_product_id = self.client.session['work_order_products'][0]['session_product_id']

        data = {'item_code': 'NEW', 'description': 'New Desc', 'quantity': 20.0}
        response = self.client.post(reverse('workorder_product_edit', args=[session_product_id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No content, indicates success for HTMX
        self.assertEqual(response['HX-Trigger'], 'refreshProductList')
        
        updated_product = self.client.session['work_order_products'][0]
        self.assertEqual(updated_product['item_code'], 'NEW')
        self.assertEqual(updated_product['quantity'], 20.0)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Product updated successfully.')

    def test_work_order_product_delete_view_post(self):
        add_response = self.client.post(reverse('workorder_product_add'), {'item_code': 'TO_DELETE', 'description': 'Delete Me', 'quantity': 1.0}, HTTP_HX_REQUEST='true')
        session_product_id = self.client.session['work_order_products'][0]['session_product_id']
        
        self.assertEqual(len(self.client.session['work_order_products']), 1)

        response = self.client.post(reverse('workorder_product_delete', args=[session_product_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshProductList')
        self.assertEqual(len(self.client.session['work_order_products']), 0)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Product deleted successfully.')

    # --- WorkOrderListView Tests ---
    def test_work_order_list_view(self):
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/list.html')
        self.assertContains(response, 'WO-LIST-0001')
        self.assertContains(response, 'List Item 1')
        self.assertTrue('work_orders' in response.context)
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views fully implement HTMX and Alpine.js patterns.

*   **HTMX for dynamic updates:**
    *   **Tab switching:** Buttons for tabs (`@click` and `hx-post` to `set_active_tab`) update the `activeTab` in Alpine.js and persist the state in the session via HTMX, allowing the UI to remember the last active tab.
    *   **Cascading Dropdowns:** `hx-get` on Country and State dropdowns target `_state_dropdown.html` and `_city_dropdown.html` respectively, swapping the inner HTML of their containers.
    *   **Product Grid CRUD:**
        *   Product add form (`_product_form.html`) is submitted via `hx-post`. Upon success (HTTP 204), it triggers `refreshProductList`.
        *   Product list (`_product_table.html`) is loaded via `hx-get` with `hx-trigger="load, refreshProductList from:body"`, ensuring it's always up-to-date after any product operation.
        *   Edit/Delete buttons on product rows trigger `hx-get` requests to load partial forms (`_product_form_modal.html`) or confirmation messages (`_product_confirm_delete.html`) into a modal.
        *   Submitting edit/delete forms from modals uses `hx-post` with `hx-swap="none"` and `hx-trigger="refreshProductList"` to close the modal and refresh the list without requiring a full page reload.
    *   **Main Form Submission:** The entire `WorkOrderMasterForm` is submitted via `hx-post="."` which targets its own `outerHTML` for a full form re-render on error, or a redirect on success.
*   **Alpine.js for UI state management:**
    *   `x-data="{ activeTab: ... }"` in `workorder_new_details.html` manages the visibility of tab content based on the `activeTab` variable.
    *   `x-show="activeTab === N"` directives control which tab's content is displayed.
    *   `@click` directives on tab buttons update `activeTab` and dispatch custom events.
*   **DataTables for list views:**
    *   `_product_table.html` uses `id="productTable"` and the associated JavaScript snippet to initialize DataTables on the dynamically loaded product list. The `htmx:afterSwap` listener ensures re-initialization when the table content changes.
    *   `list.html` also includes DataTables for the main work order list.

### Final Notes

*   **Placeholders:** Replace `tblCountry`, `tblState`, `tblCity`, `tblBuyer`, `tblBusinessGroup` with your actual database table names for lookup data.
*   **Company/Financial Year ID:** These are hardcoded as `1` in views and models. In a real application, they would be derived from the authenticated user's session or a multi-tenancy configuration.
*   **`session_id` in `WorkOrderProduct`:** The original ASP.NET code copied `SessionId` from the temporary table to the permanent `_Details` table. This is unusual for a permanent record and might indicate a specific business purpose or be an artifact of their temp storage logic. Review if this field is truly needed in `SD_Cust_WorkOrder_Products_Details`.
*   **File Upload (`InstractionAttachAnnexure`):** Django's `forms.FileInput` is used. For actual file storage, configure `MEDIA_ROOT` and `MEDIA_URL` in `settings.py`.
*   **Error Handling:** While `messages.error` is used, a full production application would need robust error logging and user feedback mechanisms.
*   **CSS:** The conversion assumes Tailwind CSS is configured and applied. The `box3` class in ASP.NET is mapped to generic Tailwind classes in the forms.
*   **`confirmationAdd()`:** This JavaScript function is preserved as a client-side `onclick` attribute for `Add Product` button, mirroring the original ASP.NET behavior.
*   **Test Coverage:** The provided tests offer a strong foundation. Continue to add more edge cases and thoroughly test all model methods and view logic to achieve high test coverage.