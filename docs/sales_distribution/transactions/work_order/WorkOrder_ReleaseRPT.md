## ASP.NET to Django Conversion Script: Work Order Release Report

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we can infer the following database tables and their approximate column structures, based on `SELECT` and `INSERT` statements, and `GridView` bindings. Data types are inferred from C# usage (e.g., `decimal.Parse`, `Convert.ToInt32`, `ToString()`) and common SQL patterns.

**Inferred Database Tables and Columns:**

1.  **`SD_Cust_WorkOrder_Products_Details`**
    *   `Id` (INT, Primary Key, Auto-incremented)
    *   `MId` (INT, Foreign Key to `SD_Cust_WorkOrder_Master.Id`)
    *   `ItemCode` (VARCHAR/NVARCHAR, e.g., `255`)
    *   `Description` (VARCHAR/NVARCHAR, e.g., `255`)
    *   `Qty` (DECIMAL/NUMERIC, e.g., `18,3`)

2.  **`SD_Cust_WorkOrder_Release`**
    *   `WRNo` (VARCHAR/NVARCHAR, e.g., `50`, generated transaction number)
    *   `WONo` (VARCHAR/NVARCHAR, e.g., `50`, Work Order Number)
    *   `ItemId` (INT, Foreign Key to `SD_Cust_WorkOrder_Products_Details.Id`)
    *   `IssuedQty` (DECIMAL/NUMERIC, e.g., `18,3`)
    *   `SysDate` (DATE)
    *   `SysTime` (TIME)
    *   `SessionId` (VARCHAR/NVARCHAR, e.g., `100`, user session ID)
    *   `CompId` (INT, Company ID)
    *   `FinYearId` (INT, Financial Year ID)

3.  **`tblHR_OfficeStaff`**
    *   `EmpId` (INT, Primary Key, Auto-incremented)
    *   `EmployeeName` (VARCHAR/NVARCHAR, e.g., `255`)
    *   `EmailId1` (VARCHAR/NVARCHAR, e.g., `255`)
    *   `WR` (INT) - Filter condition `WHERE WR = 1`
    *   `ResignationDate` (VARCHAR/NVARCHAR, e.g., `50`, filter `WHERE ResignationDate = ''`)
    *   `CompId` (INT) - Filter condition
    *   `UserID` (VARCHAR/NVARCHAR, e.g., `50`) - Filter condition `WHERE UserID != '1'`

4.  **`SD_Cust_WorkOrder_Master`**
    *   `Id` (INT, Primary Key, Auto-incremented)
    *   `WONo` (VARCHAR/NVARCHAR, e.g., `50`)
    *   `POId` (INT, Foreign Key to `SD_Cust_PO_Master.POId`)
    *   `TaskWorkOrderDate` (DATE)
    *   `CId` (INT, Foreign Key to `tblSD_WO_Category.CId`)
    *   `SCId` (INT, Foreign Key to `tblSD_WO_SubCategory.SCId`)
    *   `InstractionPrimerPainting` (INT, `0` or `1`)
    *   `InstractionPainting` (INT, `0` or `1`)
    *   `InstractionSelfCertRept` (INT, `0` or `1`)
    *   `InstractionOther` (VARCHAR/NVARCHAR, e.g., `500`)
    *   `TaskDesignFinalization_FDate`, `_TDate`, `TaskTargetDAP_FDate`, `_TDate`, `TaskTargetManufg_FDate`, `_TDate`, `TaskTargetTryOut_FDate`, `_TDate`, `TaskTargetDespach_FDate`, `_TDate`, `TaskTargetAssembly_FDate`, `_TDate`, `TaskTargetInstalation_FDate`, `_TDate`, `TaskCustInspection_FDate`, `_TDate` (DATE)

5.  **`SD_Cust_PO_Master`**
    *   `POId` (INT, Primary Key, Auto-incremented)
    *   `PODate` (DATE)
    *   `PONo` (VARCHAR/NVARCHAR, e.g., `50`)

6.  **`tblSD_WO_Category`**
    *   `CId` (INT, Primary Key, Auto-incremented)
    *   `Symbol` (VARCHAR/NVARCHAR, e.g., `50`)
    *   `CName` (VARCHAR/NVARCHAR, e.g., `255`)

7.  **`tblSD_WO_SubCategory`**
    *   `SCId` (INT, Primary Key, Auto-incremented)
    *   `SCName` (VARCHAR/NVARCHAR, e.g., `255`)

8.  **`tblCompany_master`**
    *   `CompId` (INT, Primary Key, Auto-incremented)
    *   `MailServerIp` (VARCHAR/NVARCHAR, e.g., `100`)
    *   `ErpSysmail` (VARCHAR/NVARCHAR, e.g., `255`)

## Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET page `WorkOrder_ReleaseRPT.aspx` is primarily a **transactional processing page** rather than a standard CRUD interface for a single entity. It allows users to "release" quantities of work order products and, upon submission, generates a report and sends emails.

*   **Read (Display Data):**
    *   **Work Order Product Details:** Reads data from `SD_Cust_WorkOrder_Products_Details` based on `MId` (passed as `Id` in query string) and `CompId`. It calculates `ReleasedQty` by summing `IssuedQty` from `SD_Cust_WorkOrder_Release` for each item and then computes `RemainQty`. This data populates `GridView1`.
    *   **Employee List:** Reads data from `tblHR_OfficeStaff` filtered by `WR=1`, `ResignationDate=''`, `CompId`, and `UserID!='1'`. This data populates `GridView2`.
    *   **Report Parameters:** Fetches extensive data from `SD_Cust_WorkOrder_Master`, `SD_Cust_PO_Master`, `tblSD_WO_Category`, `tblSD_WO_SubCategory`, and `tblCompany_master` to populate a Crystal Report.

*   **Create (Transaction):**
    *   **Work Order Release Entry:** Upon `Submit_Click`, it inserts new records into `SD_Cust_WorkOrder_Release` for each checked item in `GridView1` with a valid "To Release Qty".
        *   Validation ensures: `CheckBox1` is checked, `TextBox1` has a value, quantity is numeric, and `To Release Qty` is `> 0` and `_<=_` `RemainQty`.
        *   It also validates that at least one product and one employee are selected.

*   **Other Operations:**
    *   **Transaction Number Generation:** `fun.TranNo` is used to generate `WRNo` for `SD_Cust_WorkOrder_Release`.
    *   **Report Generation:** Generates a Crystal Report (`WOReleaseMail.rpt`), populates it with data from various tables, and exports it as a PDF.
    *   **Email Sending:** Sends the generated PDF report as an attachment via email to selected employees from `GridView2`.
    *   **File Management:** Deletes the generated XML and PDF files after email submission.
    *   **Client-Side Validation:** `RequiredFieldValidator` and `RegularExpressionValidator` on the quantity input.
    *   **Redirection:** Redirects to another page upon successful submission or cancellation.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting client-side interactions.

**Inferred UI Components and Their Django/HTMX/Alpine.js Equivalents:**

*   **`MasterPageFile="~/MasterPage.master"`**: Represents the base layout. In Django, this maps to `core/base.html` that all templates extend.
*   **`asp:UpdateProgress` and `cc1:ModalPopupExtender`**: Used for showing "processing" indicators during asynchronous postbacks. In Django, these are replaced by:
    *   HTMX's `hx-indicator` for loading states.
    *   Alpine.js for managing a modal display (e.g., loading spinner modal, or general form/confirmation modals).
*   **`asp:UpdatePanel`**: Enables partial page updates, avoiding full page reloads. This is the core functionality to be replaced by **HTMX**. All form submissions and dynamic content loading will use HTMX.
*   **`asp:Label ID="Label2"` (Work Order No.)**: Displays the work order number. In Django, this will be a simple template variable.
*   **`asp:Panel ID="Panel1"` (Scrollable Container for `GridView1`)**: A container for the first grid. This will be a standard `div` in Django templates.
*   **`asp:GridView ID="GridView1"` (Work Order Product Details)**:
    *   Displays items with `Id`, `ItemCode`, `Description`, `Qty`, `ReleasedQty`, `RemainQty`.
    *   Includes `asp:CheckBox ID="CheckBox1"` for selection.
    *   Includes `asp:TextBox ID="TextBox1"` for "To Release Qty" with `OnTextChanged` event.
    *   Includes `asp:RequiredFieldValidator` and `asp:RegularExpressionValidator` for `TextBox1`.
    *   **Django Equivalent**: A **DataTables-powered HTML table**. Each row will represent a work order product detail. Checkboxes and textboxes will be standard HTML inputs with `hx-on` or Alpine.js for dynamic behavior. Validation handled by Django forms on submit.
*   **`asp:Panel ID="Panel2"` (Scrollable Container for `GridView2`)**: Another container.
*   **`asp:GridView ID="GridView2"` (Name of Employee)**:
    *   Displays `EmployeeName`, `EmpId`, `EmailId`.
    *   Includes `asp:CheckBox ID="CheckBox2"` for selection.
    *   **Django Equivalent**: Another **DataTables-powered HTML table**. Checkboxes for selection.
*   **`asp:SqlDataSource ID="SqlDataSource1"`**: Fetches employee data. In Django, this is handled directly by the ORM in the view.
*   **`asp:Button ID="Submit"`**: Triggers the release process. `ValidationGroup="A"`, `OnClientClick="return confDyna('...')"`, `onclick="Submit_Click"`.
    *   **Django Equivalent**: A standard HTML `button` with `hx-post` attributes to submit the form data to a Django view, potentially with `hx-confirm` for the client-side confirmation.
*   **`asp:Button ID="Button2"` (Cancel)**: Redirects to another page.
    *   **Django Equivalent**: A standard HTML `button` or `a` tag linking to the cancel URL.
*   **Client-Side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`, `Sys.WebForms.PageRequestManager`, `confDyna`)**: These scripts manage UI feedback and confirmations.
    *   **Django Equivalent**: Mostly replaced by HTMX (loading indicators, partial swaps) and Alpine.js (modals, client-side state). `confDyna` replaced by `hx-confirm` or a simple Alpine.js modal for confirmation.

## Step 4: Generate Django Code

### Application Name: `work_order_release`

### 4.1 Models (`work_order_release/models.py`)

**Task:** Create Django models based on the database schema, including methods for business logic (fat model).

**Instructions:**
We define models for each identified database table. Crucially, `managed = False` is set in the `Meta` class, meaning Django will use the existing tables without attempting to create or modify their schema. Relationships are defined using `ForeignKey`. Business logic for calculating `ReleasedQty` and `RemainQty` is added as properties to `WorkOrderProductDetail`. `get_next_wr_no` is a placeholder for `fun.TranNo` logic.

```python
from django.db import models
from django.db.models import Sum
from django.utils import timezone
from django.core.exceptions import ValidationError

# Utility function for generating transaction numbers (mocked for now)
# In a real system, this would typically involve sequence management in the database
# or a robust transaction number generation service.
def get_next_wr_no(company_id):
    """
    Mocks the behavior of fun.TranNo("SD_Cust_WorkOrder_Release", "WRNo", SessionCompId).
    A robust implementation would consider uniqueness, format, and concurrency.
    """
    # This is a simplified example. A real implementation might query the DB
    # for the max existing WRNo and increment, or use a database sequence.
    # For now, it generates a timestamp-based unique number.
    timestamp = timezone.now().strftime('%Y%m%d%H%M%S%f')
    return f"WR-{company_id}-{timestamp}"

class CompanyMaster(models.Model):
    CompId = models.IntegerField(db_column='CompId', primary_key=True)
    MailServerIp = models.CharField(db_column='MailServerIp', max_length=100, blank=True, null=True)
    ErpSysmail = models.CharField(db_column='ErpSysmail', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

    def __str__(self):
        return f"Company {self.CompId}"

class WOCategory(models.Model):
    CId = models.IntegerField(db_column='CId', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    CName = models.CharField(db_column='CName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.Symbol} - {self.CName}"

class WOSubCategory(models.Model):
    SCId = models.IntegerField(db_column='SCId', primary_key=True)
    SCName = models.CharField(db_column='SCName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_SubCategory'
        verbose_name = 'WO Sub Category'
        verbose_name_plural = 'WO Sub Categories'

    def __str__(self):
        return self.SCName

class PurchaseOrderMaster(models.Model):
    POId = models.IntegerField(db_column='POId', primary_key=True)
    PONo = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    PODate = models.DateField(db_column='PODate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'

    def __str__(self):
        return self.PONo

class WorkOrderMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    WONo = models.CharField(db_column='WONo', max_length=50)
    POId = models.ForeignKey(PurchaseOrderMaster, on_delete=models.DO_NOTHING, db_column='POId', blank=True, null=True)
    TaskWorkOrderDate = models.DateField(db_column='TaskWorkOrderDate', blank=True, null=True)
    CId = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', blank=True, null=True)
    SCId = models.ForeignKey(WOSubCategory, on_delete=models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    InstractionPrimerPainting = models.BooleanField(db_column='InstractionPrimerPainting', default=False)
    InstractionPainting = models.BooleanField(db_column='InstractionPainting', default=False)
    InstractionSelfCertRept = models.BooleanField(db_column='InstractionSelfCertRept', default=False)
    InstractionOther = models.CharField(db_column='InstractionOther', max_length=500, blank=True, null=True)
    TaskDesignFinalization_FDate = models.DateField(db_column='TaskDesignFinalization_FDate', blank=True, null=True)
    TaskDesignFinalization_TDate = models.DateField(db_column='TaskDesignFinalization_TDate', blank=True, null=True)
    TaskTargetDAP_FDate = models.DateField(db_column='TaskTargetDAP_FDate', blank=True, null=True)
    TaskTargetDAP_TDate = models.DateField(db_column='TaskTargetDAP_TDate', blank=True, null=True)
    TaskTargetManufg_FDate = models.DateField(db_column='TaskTargetManufg_FDate', blank=True, null=True)
    TaskTargetManufg_TDate = models.DateField(db_column='TaskTargetManufg_TDate', blank=True, null=True)
    TaskTargetTryOut_FDate = models.DateField(db_column='TaskTargetTryOut_FDate', blank=True, null=True)
    TaskTargetTryOut_TDate = models.DateField(db_column='TaskTargetTryOut_TDate', blank=True, null=True)
    TaskTargetDespach_FDate = models.DateField(db_column='TaskTargetDespach_FDate', blank=True, null=True)
    TaskTargetDespach_TDate = models.DateField(db_column='TaskTargetDespach_TDate', blank=True, null=True)
    TaskTargetAssembly_FDate = models.DateField(db_column='TaskTargetAssembly_FDate', blank=True, null=True)
    TaskTargetAssembly_TDate = models.DateField(db_column='TaskTargetAssembly_TDate', blank=True, null=True)
    TaskTargetInstalation_FDate = models.DateField(db_column='TaskTargetInstalation_FDate', blank=True, null=True)
    TaskTargetInstalation_TDate = models.DateField(db_column='TaskTargetInstalation_TDate', blank=True, null=True)
    TaskCustInspection_FDate = models.DateField(db_column='TaskCustInspection_FDate', blank=True, null=True)
    TaskCustInspection_TDate = models.DateField(db_column='TaskCustInspection_TDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.WONo

    @property
    def instructions_summary(self):
        """Combines all instruction fields into a single string."""
        instructions = []
        if self.InstractionPrimerPainting:
            instructions.append("Primer Painting")
        if self.InstractionPainting:
            instructions.append("Painting")
        if self.InstractionSelfCertRept:
            instructions.append("Self Certification Report")
        if self.InstractionOther:
            instructions.append(self.InstractionOther)
        return ", ".join(instructions) if instructions else "N/A"

class WorkOrderProductDetail(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MId = models.ForeignKey(WorkOrderMaster, on_delete=models.DO_NOTHING, db_column='MId')
    ItemCode = models.CharField(db_column='ItemCode', max_length=255)
    Description = models.CharField(db_column='Description', max_length=255)
    Qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Products_Details'
        verbose_name = 'Work Order Product Detail'
        verbose_name_plural = 'Work Order Product Details'

    def __str__(self):
        return f"{self.ItemCode} - {self.Description}"

    @property
    def released_qty(self):
        """Calculates the sum of issued quantities for this item."""
        return self.workorderrelease_set.aggregate(total_issued=Sum('IssuedQty'))['total_issued'] or 0

    @property
    def remain_qty(self):
        """Calculates the remaining quantity for this item."""
        return self.Qty - self.released_qty

    def is_fully_released(self):
        """Checks if the item is fully released."""
        return self.remain_qty <= 0

    def validate_release_quantity(self, release_qty):
        """Validates if the proposed release quantity is valid."""
        if not isinstance(release_qty, (int, float, models.Decimal)):
            raise ValidationError("Release quantity must be a number.")
        if release_qty <= 0:
            raise ValidationError("Release quantity must be greater than zero.")
        if release_qty > self.remain_qty:
            raise ValidationError(f"Release quantity ({release_qty}) exceeds remaining quantity ({self.remain_qty}).")
        return True

class WorkOrderRelease(models.Model):
    # WRNo is not typically a primary key if generated as a transaction number
    # but could be unique. Assuming it's part of a composite key or a unique identifier.
    # In Django, we'll let `id` be the PK and make WRNo unique or index it.
    id = models.AutoField(db_column='id', primary_key=True) # Assuming a Django-managed PK or similar DB PK
    WRNo = models.CharField(db_column='WRNo', max_length=50) # Assuming this is unique
    WONo = models.CharField(db_column='WONo', max_length=50)
    ItemId = models.ForeignKey(WorkOrderProductDetail, on_delete=models.DO_NOTHING, db_column='ItemId')
    IssuedQty = models.DecimalField(db_column='IssuedQty', max_digits=18, decimal_places=3)
    SysDate = models.DateField(db_column='SysDate')
    SysTime = models.TimeField(db_column='SysTime')
    SessionId = models.CharField(db_column='SessionId', max_length=100)
    CompId = models.IntegerField(db_column='CompId')
    FinYearId = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Release'
        verbose_name = 'Work Order Release'
        verbose_name_plural = 'Work Order Releases'
        unique_together = ('WRNo', 'ItemId') # Example, assuming combination is unique

    def __str__(self):
        return f"Release {self.WRNo} for Item {self.ItemId.ItemCode}"

class HROfficeStaff(models.Model):
    EmpId = models.IntegerField(db_column='EmpId', primary_key=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255)
    EmailId1 = models.CharField(db_column='EmailId1', max_length=255, blank=True, null=True)
    WR = models.IntegerField(db_column='WR', default=0) # Assuming 0 for not WR, 1 for WR
    ResignationDate = models.CharField(db_column='ResignationDate', max_length=50, blank=True, null=True) # Stored as empty string ''
    CompId = models.IntegerField(db_column='CompId')
    UserID = models.CharField(db_column='UserID', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return self.EmployeeName

    def is_eligible_for_wr(self):
        """Checks if the staff member is eligible for Work Order Release communication."""
        return self.WR == 1 and (self.ResignationDate is None or self.ResignationDate == '') and self.UserID != '1'

```

### 4.2 Forms (`work_order_release/forms.py`)

**Task:** Define a Django form for processing the dynamic input from the work order product details and employee selections.

**Instructions:**
Given the input isn't a simple single model form but rather a collection of items from a grid and a list of selected employees, a custom `Form` (not `ModelForm`) is most appropriate. This form will receive lists of item IDs and quantities, and selected employee IDs. Validation logic from the ASP.NET code will be replicated here.

```python
from django import forms
from django.core.exceptions import ValidationError
from django.db import transaction
from .models import WorkOrderProductDetail, HROfficeStaff, WorkOrderRelease, get_next_wr_no

class WorkOrderReleaseForm(forms.Form):
    # This form will receive lists of item IDs and quantities from the client
    # and selected employee IDs.
    # It doesn't use ModelForm as it processes multiple records from multiple models.

    released_items_data = forms.JSONField(
        required=False,
        help_text="JSON string of {item_id: quantity} for items to release."
    )
    selected_employee_ids = forms.JSONField(
        required=False,
        help_text="JSON string of [employee_id] for selected employees."
    )
    work_order_id = forms.IntegerField(
        required=True,
        widget=forms.HiddenInput() # Hidden field to pass the main work order ID
    )
    work_order_no = forms.CharField(
        required=True,
        widget=forms.HiddenInput() # Hidden field to pass the main work order number
    )


    def clean(self):
        cleaned_data = super().clean()
        released_items_data = cleaned_data.get('released_items_data', {})
        selected_employee_ids = cleaned_data.get('selected_employee_ids', [])
        work_order_id = cleaned_data.get('work_order_id')
        work_order_no = cleaned_data.get('work_order_no')

        self.cleaned_released_items = [] # To store validated item data with actual objects
        self.cleaned_selected_employees = [] # To store validated employee objects

        if not released_items_data:
            self.add_error(None, "No items selected for release or no quantities entered.")

        if not selected_employee_ids:
            self.add_error(None, "No employees selected to notify.")

        # Validate each item for release
        valid_items_count = 0
        all_product_details = {
            p.Id: p for p in WorkOrderProductDetail.objects.filter(MId=work_order_id)
        }

        for item_id_str, qty_str in released_items_data.items():
            try:
                item_id = int(item_id_str)
                release_qty = forms.DecimalField(max_digits=18, decimal_places=3).clean(qty_str)
            except (ValueError, forms.ValidationError):
                self.add_error(None, f"Invalid quantity or item ID for item: {item_id_str}.")
                continue

            product_detail = all_product_details.get(item_id)
            if not product_detail:
                self.add_error(None, f"Work order product detail not found for ID: {item_id}.")
                continue

            try:
                product_detail.validate_release_quantity(release_qty)
                self.cleaned_released_items.append({
                    'product_detail': product_detail,
                    'release_qty': release_qty
                })
                valid_items_count += 1
            except ValidationError as e:
                self.add_error(None, f"Validation error for item {product_detail.ItemCode}: {e.message}")

        if valid_items_count == 0 and released_items_data: # If items were submitted but none passed validation
             self.add_error(None, "All submitted items had validation errors. Please check quantities.")

        # Validate selected employees
        all_eligible_staff = {
            s.EmpId: s for s in HROfficeStaff.objects.filter(
                WR=1,
                CompId=self.initial.get('comp_id'), # Access from initial data set in view
                ResignationDate__in=['', None] # Handle empty string or NULL
            ).exclude(UserID='1')
        }
        for emp_id_str in selected_employee_ids:
            try:
                emp_id = int(emp_id_str)
            except ValueError:
                self.add_error(None, f"Invalid employee ID: {emp_id_str}.")
                continue

            employee = all_eligible_staff.get(emp_id)
            if not employee:
                self.add_error(None, f"Selected employee with ID {emp_id} is not valid or eligible.")
                continue
            self.cleaned_selected_employees.append(employee)

        return cleaned_data

    def save_release(self, request):
        """
        Processes the validated form data to create WorkOrderRelease records.
        Assumes cleaned_data and cleaned_released_items/employees are available.
        """
        if not self.is_valid():
            raise ValueError("Form is not valid, cannot save release.")

        comp_id = request.session.get('compid')
        fin_year_id = request.session.get('finyear')
        session_id = request.session.get('username')

        if not all([comp_id, fin_year_id, session_id]):
            raise ValidationError("Session data (CompId, FinYearId, Username) missing.")

        wr_no = get_next_wr_no(comp_id) # Generate WRNo once for this batch

        new_release_objects = []
        for item_data in self.cleaned_released_items:
            product_detail = item_data['product_detail']
            release_qty = item_data['release_qty']

            new_release_objects.append(
                WorkOrderRelease(
                    WRNo=wr_no,
                    WONo=self.cleaned_data['work_order_no'],
                    ItemId=product_detail,
                    IssuedQty=release_qty,
                    SysDate=timezone.localdate(),
                    SysTime=timezone.localtime().time(),
                    SessionId=session_id,
                    CompId=comp_id,
                    FinYearId=fin_year_id
                )
            )

        with transaction.atomic():
            WorkOrderRelease.objects.bulk_create(new_release_objects)
        
        return wr_no

```

### 4.3 Services (`work_order_release/services.py`)

**Task:** Create a dedicated service to handle the complex report generation and email sending logic, keeping views thin.

**Instructions:**
This service replaces the Crystal Reports and `System.Web.Mail` logic. We will use `WeasyPrint` for PDF generation from Django templates and Django's built-in email functionality.

```python
import os
from django.template.loader import render_to_string
from django.core.mail import EmailMessage
from django.conf import settings
from django.db.models import Sum
from weasyprint import HTML, CSS
from .models import WorkOrderMaster, WorkOrderProductDetail, WorkOrderRelease, CompanyMaster, PurchaseOrderMaster
from django.urls import reverse
import logging
from django.http import HttpRequest

logger = logging.getLogger(__name__)

class WorkOrderReleaseService:
    def __init__(self, request: HttpRequest):
        self.request = request
        self.comp_id = request.session.get('compid')
        # Ensure 'temp_media' directory exists for temporary files
        self.temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp_docs')
        os.makedirs(self.temp_dir, exist_ok=True)

    def generate_wr_report_pdf(self, wo_id, wr_no, selected_item_ids):
        """
        Generates a PDF report for the Work Order Release.
        Mimics Crystal Reports data fetching and parameter setting.
        """
        try:
            work_order = WorkOrderMaster.objects.select_related(
                'POId', 'CId', 'SCId'
            ).get(Id=wo_id)
            
            company = CompanyMaster.objects.get(CompId=self.comp_id)

            # Gather data for report items (mimics Crystal Reports data table)
            report_items = []
            for item_id in selected_item_ids:
                product_detail = WorkOrderProductDetail.objects.get(Id=item_id)
                issued_qty = WorkOrderRelease.objects.filter(
                    WRNo=wr_no, ItemId=product_detail, CompId=self.comp_id
                ).aggregate(total_issued=Sum('IssuedQty'))['total_issued'] or 0
                
                report_items.append({
                    'Id': product_detail.Id,
                    'ItemCode': product_detail.ItemCode,
                    'Description': product_detail.Description,
                    'Qty': product_detail.Qty,
                    'ReleasedQty': issued_qty, # This is the *current* released quantity for this WRNo, not cumulative
                    'WONo': work_order.WONo,
                    'WRNo': wr_no,
                })
            
            context = {
                'work_order': work_order,
                'company': company,
                'report_items': report_items,
                'wr_no': wr_no,
                'current_date': timezone.localdate(),
                'address': "Your Company Address Here (from fun.CompAdd)", # Placeholder for Company Address
            }

            # Render HTML content for the report
            html_string = render_to_string('work_order_release/reports/wo_release_report.html', context, request=self.request)
            
            # Convert HTML to PDF using WeasyPrint
            pdf_filename = f"WorkOrderRelease_{work_order.WONo}_{wr_no}.pdf"
            pdf_path = os.path.join(self.temp_dir, pdf_filename)
            
            HTML(string=html_string, base_url=self.request.build_absolute_uri('/')).write_pdf(pdf_path, presentational_hints=True)
            
            return pdf_path

        except WorkOrderMaster.DoesNotExist:
            logger.error(f"Work Order Master with ID {wo_id} not found for report generation.")
            raise
        except CompanyMaster.DoesNotExist:
            logger.error(f"Company Master with ID {self.comp_id} not found for report generation.")
            raise
        except Exception as e:
            logger.error(f"Error generating PDF report for WO ID {wo_id}, WRNo {wr_no}: {e}")
            raise

    def send_release_emails(self, wo_no, wr_no, pdf_path, selected_employees):
        """
        Sends the generated PDF report via email to selected employees.
        """
        try:
            company = CompanyMaster.objects.get(CompId=self.comp_id)
            sender_email = company.ErpSysmail
            smtp_server = company.MailServerIp # In Django, SMTP server is configured in settings.py

            if not sender_email:
                logger.warning(f"ERP System Email not configured for Company {self.comp_id}. Cannot send email.")
                return False

            # Collect recipient emails
            recipient_emails = [emp.EmailId1 for emp in selected_employees if emp.EmailId1]
            if not recipient_emails:
                logger.warning("No valid recipient emails found among selected employees. Sending to ERP system email.")
                recipient_emails = [sender_email] # Fallback to ERP system email if no recipients

            subject = f"Work Order Release WONo:{wo_no} WRNo:{wr_no}"
            body = "Dear Sir, This is Auto generated mail by ERP system, please do not reply.<br><br> Thank you."

            email = EmailMessage(
                subject,
                body,
                sender_email,
                recipient_emails,
                # headers={'Reply-To': sender_email} # Optional: set reply-to header
            )
            email.content_subtype = "html" # Main content is HTML

            # Attach the generated PDF
            if os.path.exists(pdf_path):
                email.attach_file(pdf_path)
            else:
                logger.error(f"PDF file not found for attachment: {pdf_path}")
                return False

            email.send(fail_silently=False)
            logger.info(f"Email sent successfully for WRNo {wr_no} to {recipient_emails}")
            return True

        except CompanyMaster.DoesNotExist:
            logger.error(f"Company Master with ID {self.comp_id} not found for email sending.")
            return False
        except Exception as e:
            logger.error(f"Error sending email for WRNo {wr_no}: {e}")
            raise
        finally:
            # Clean up temporary PDF file after sending
            if os.path.exists(pdf_path):
                os.remove(pdf_path)
                logger.debug(f"Removed temporary PDF: {pdf_path}")


```

### 4.3 Views (`work_order_release/views.py`)

**Task:** Implement the main Work Order Release view and partial views for HTMX interactions, keeping them thin.

**Instructions:**
The main view will be a `TemplateView` to display the initial page. HTMX will then load the product and employee tables via separate endpoints (`PartialView`). The `_process_release` method handles the complex submission, making the main `post` method thin.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from django.db import transaction
from django.core.exceptions import ValidationError
import json
import logging

from .models import WorkOrderProductDetail, HROfficeStaff, WorkOrderMaster
from .forms import WorkOrderReleaseForm
from .services import WorkOrderReleaseService

logger = logging.getLogger(__name__)

class WorkOrderReleaseDetailView(TemplateView):
    template_name = 'work_order_release/release_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Parameters from query string, analogous to Request.QueryString["WONo"], Request.QueryString["Id"]
        work_order_no = self.request.GET.get('WONo')
        work_order_id = self.request.GET.get('Id') # This maps to MId in SD_Cust_WorkOrder_Products_Details

        if not work_order_id:
            messages.error(self.request, "Work Order ID is required.")
            return context # Or raise an error

        try:
            main_work_order = WorkOrderMaster.objects.get(Id=work_order_id)
            context['work_order'] = main_work_order
            context['work_order_no'] = work_order_no if work_order_no else main_work_order.WONo
            context['work_order_id'] = work_order_id

        except WorkOrderMaster.DoesNotExist:
            messages.error(self.request, f"Work Order with ID {work_order_id} not found.")
            context['work_order'] = None # Explicitly set to None
        
        return context

    # This method handles the main form submission for releasing items.
    def post(self, request, *args, **kwargs):
        # The form is structured to receive JSON data from HTMX
        # Ensure we have the necessary session data
        comp_id = request.session.get('compid')
        if not comp_id:
            messages.error(request, "Company ID not found in session.")
            return HttpResponse(status=400, content="Company ID missing.")

        # Parse JSON payload from HTMX if submitted this way
        try:
            payload = json.loads(request.body)
            released_items_data = payload.get('released_items_data', {})
            selected_employee_ids = payload.get('selected_employee_ids', [])
            work_order_id = payload.get('work_order_id')
            work_order_no = payload.get('work_order_no')
        except json.JSONDecodeError:
            messages.error(request, "Invalid JSON payload.")
            return HttpResponse(status=400, content="Invalid JSON data.")

        # Initialize the form with data and initial context
        form = WorkOrderReleaseForm(
            data={
                'released_items_data': released_items_data,
                'selected_employee_ids': selected_employee_ids,
                'work_order_id': work_order_id,
                'work_order_no': work_order_no,
            },
            initial={'comp_id': comp_id} # Pass session data required for form validation
        )

        if form.is_valid():
            try:
                # Use a database transaction to ensure atomicity
                with transaction.atomic():
                    # Save release records
                    wr_no = form.save_release(request)
                    
                    # Generate report and send emails via the service
                    release_service = WorkOrderReleaseService(request)
                    
                    # Prepare selected item IDs for report generation
                    selected_item_ids_for_report = [
                        item['product_detail'].Id for item in form.cleaned_released_items
                    ]
                    
                    pdf_path = release_service.generate_wr_report_pdf(
                        wo_id=work_order_id,
                        wr_no=wr_no,
                        selected_item_ids=selected_item_ids_for_report
                    )
                    
                    release_service.send_release_emails(
                        wo_no=work_order_no,
                        wr_no=wr_no,
                        pdf_path=pdf_path,
                        selected_employees=form.cleaned_selected_employees
                    )
                
                messages.success(request, f"Release of Work Order No. {work_order_no} is completed.")
                # HTMX redirect/refresh
                return HttpResponse(
                    status=204,
                    headers={'HX-Location': reverse_lazy('work_order_release_list')} # Redirect to a list page
                )

            except ValidationError as e:
                messages.error(request, f"Release failed: {e.message}")
                return HttpResponse(status=400, content=f"Release failed: {e.message}")
            except Exception as e:
                logger.exception(f"An unexpected error occurred during work order release for WONo: {work_order_no}")
                messages.error(request, "An unexpected error occurred during release. Please try again.")
                return HttpResponse(status=500, content="An unexpected error occurred.")
        else:
            # If form is not valid, return errors via HTMX headers or partial
            error_messages = []
            for field, errors in form.errors.items():
                error_messages.append(f"{field}: {', '.join(errors)}")
            messages.error(request, "Invalid input details: " + " ".join(error_messages))
            return HttpResponse(status=400, content="Invalid input details.", headers={'HX-Trigger': 'showMessages'})


class WorkOrderProductTablePartialView(View):
    """
    HTMX endpoint to render the work order product details table.
    """
    def get(self, request, *args, **kwargs):
        work_order_id = request.GET.get('Id')
        comp_id = request.session.get('compid')

        if not work_order_id or not comp_id:
            return HttpResponse(status=400, content="Missing Work Order ID or Company ID.")

        products = WorkOrderProductDetail.objects.filter(MId=work_order_id).order_by('Id')
        
        # We need to explicitly calculate released_qty and remain_qty for each item
        # within the queryset for efficiency if possible, or iterate in template.
        # For a fat model, we can leverage the properties for display.
        
        context = {
            'product_details': products,
        }
        return render(request, 'work_order_release/_work_order_product_table.html', context)


class EmployeeTablePartialView(View):
    """
    HTMX endpoint to render the employee selection table.
    """
    def get(self, request, *args, **kwargs):
        comp_id = request.session.get('compid')
        if not comp_id:
            return HttpResponse(status=400, content="Missing Company ID.")

        employees = HROfficeStaff.objects.filter(
            WR=1,
            CompId=comp_id,
            ResignationDate__in=['', None] # Equivalent to ResignationDate=''
        ).exclude(UserID='1').order_by('EmpId')
        
        context = {
            'employees': employees,
        }
        return render(request, 'work_order_release/_employee_table.html', context)

# Helper view for the redirect after successful submission (e.g., to a list of WO Releases)
class WorkOrderReleaseListView(TemplateView):
    template_name = 'work_order_release/work_order_release_list.html' # A placeholder for a general list page

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['work_order_releases'] = WorkOrderRelease.objects.all().order_by('-SysDate', '-SysTime')[:20] # Last 20 releases
        return context

```

### 4.4 Templates (`work_order_release/templates/work_order_release/`)

**Task:** Create templates for the main view and partials, integrating HTMX, Alpine.js, and DataTables.

**Instructions:**
`release_detail.html` will be the main page. `_work_order_product_table.html` and `_employee_table.html` will be partials loaded by HTMX. A modal for confirmation/loading uses Alpine.js.

#### `release_detail.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ showModal: false, modalContent: '' }">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h2 class="text-2xl font-bold text-gray-800">
                Work Order No. {{ work_order_no }} - Release
            </h2>
            {% if work_order %}
                <span class="text-gray-600">ID: {{ work_order_id }}</span>
            {% endif %}
        </div>

        {% if not work_order %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">Work Order details not found or not provided.</span>
            </div>
        {% else %}
            <!-- Work Order Products Table -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Items for Release</h3>
                <div id="product-table-container"
                     hx-get="{% url 'work_order_release_products_table' %}?Id={{ work_order_id }}"
                     hx-trigger="load, refreshProductList from:body"
                     hx-swap="innerHTML"
                     class="min-h-[250px] relative"> {# Min-height to prevent jump during loading #}
                    <!-- Loading indicator for product table -->
                    <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10" hx-indicator="#product-table-container">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading items...</p>
                    </div>
                    <!-- Product table will be loaded here via HTMX -->
                </div>
            </div>

            <!-- Employee Selection Table -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Select Employees for Notification</h3>
                <div id="employee-table-container"
                     hx-get="{% url 'work_order_release_employees_table' %}"
                     hx-trigger="load, refreshEmployeeList from:body"
                     hx-swap="innerHTML"
                     class="min-h-[250px] relative"> {# Min-height to prevent jump during loading #}
                    <!-- Loading indicator for employee table -->
                    <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10" hx-indicator="#employee-table-container">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading employees...</p>
                    </div>
                    <!-- Employee table will be loaded here via HTMX -->
                </div>
            </div>

            <!-- Submit/Cancel Buttons -->
            <div class="flex justify-center space-x-4 mt-8">
                <button
                    type="button"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md transition duration-150 ease-in-out"
                    @click="showModal = true"
                >
                    Submit
                </button>
                <a href="{% url 'work_order_release_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-md transition duration-150 ease-in-out">
                    Cancel
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Confirmation Modal (Alpine.js) -->
    <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50"
         @click.away="showModal = false">
        <div class="relative p-8 bg-white w-full max-w-md mx-auto rounded-lg shadow-xl"
             @click.stop>
            <h3 class="text-xl font-semibold mb-4 text-gray-900">Confirm Work Order Release</h3>
            <p class="text-gray-700 mb-6">Do you really want to release Work Order No. {{ work_order_no }}?</p>
            <div class="flex justify-end space-x-4">
                <button @click="showModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md">
                    No, Cancel
                </button>
                <button
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md"
                    hx-post="{% url 'work_order_release_process' %}"
                    hx-headers='{"Content-Type": "application/json"}'
                    hx-swap="none"
                    hx-target="body"
                    hx-indicator="#global-loading-indicator" {# Assuming a global indicator in base.html #}
                    hx-confirm="You are about to finalize the release. This action is irreversible. Are you sure?"
                    _="on click
                        let releasedItems = {};
                        document.querySelectorAll('#product-table-container tbody tr').forEach(row => {
                            let checkbox = row.querySelector('input[type=\"checkbox\"][name^=\"select_item_\"]');
                            let qtyInput = row.querySelector('input[type=\"text\"][name^=\"release_qty_\"]');
                            let itemId = checkbox ? checkbox.value : null;

                            if (checkbox && checkbox.checked && qtyInput && qtyInput.value.trim() !== '' && itemId) {
                                releasedItems[itemId] = qtyInput.value.trim();
                            }
                        });

                        let selectedEmployees = [];
                        document.querySelectorAll('#employee-table-container tbody tr').forEach(row => {
                            let checkbox = row.querySelector('input[type=\"checkbox\"][name^=\"select_employee_\"]');
                            let empId = checkbox ? checkbox.value : null;
                            if (checkbox && checkbox.checked && empId) {
                                selectedEmployees.push(empId);
                            }
                        });

                        let payload = {
                            released_items_data: releasedItems,
                            selected_employee_ids: selectedEmployees,
                            work_order_id: '{{ work_order_id }}',
                            work_order_no: '{{ work_order_no }}'
                        };
                        
                        this.setAttribute('hx-vals', JSON.stringify(payload));
                        
                        # Trigger the HTMX request
                        htmx.trigger(this, 'click');
                        
                        # Hide the modal immediately after triggering HTMX
                        document.getElementById('modal').classList.remove('is-active');
                        this.closest('[x-data]').showModal = false;"
                >
                    Yes, Release
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is initialized
    });

    // Initialize DataTables after HTMX loads content
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.target.id === 'product-table-container' || evt.target.id === 'employee-table-container') {
            $('#productTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy previous instance if it exists
            });
            $('#employeeTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy previous instance if it exists
            });
        }
    });

    // Client-side validation for release quantity input (simulating ASP.NET validators)
    // using Alpine.js or plain JS
    document.addEventListener('DOMContentLoaded', () => {
        document.body.addEventListener('input', (event) => {
            if (event.target.matches('input[name^="release_qty_"]')) {
                const inputField = event.target;
                const row = inputField.closest('tr');
                const qtyLabel = row.querySelector('label[name^="lblQty_"]');
                const releasedQtyLabel = row.querySelector('label[name^="lblReleasedQty_"]');
                const remainQtyLabel = row.querySelector('label[name^="lblRemainQty_"]');
                const errorSpan = row.querySelector('span[name^="error_release_qty_"]');
                const checkbox = row.querySelector('input[type="checkbox"][name^="select_item_"]');

                let totalQty = parseFloat(qtyLabel.textContent) || 0;
                let currentReleasedQty = parseFloat(releasedQtyLabel.textContent) || 0;
                let remainQty = parseFloat(remainQtyLabel.textContent) || 0;
                let inputValue = parseFloat(inputField.value);

                errorSpan.textContent = ''; // Clear previous error

                if (checkbox) {
                    if (inputField.value.trim() === '') {
                        checkbox.checked = false; // Uncheck if quantity is empty
                        errorSpan.textContent = '*'; // Required field indicator
                    } else if (isNaN(inputValue) || inputValue <= 0) {
                        checkbox.checked = false;
                        errorSpan.textContent = 'Invalid quantity.';
                    } else if (inputValue > remainQty) {
                        checkbox.checked = false;
                        errorSpan.textContent = `Exceeds remaining qty (${remainQty.toFixed(3)}).`;
                    } else {
                        checkbox.checked = true;
                        errorSpan.textContent = ''; // No error
                    }
                }
            }
        });
        
        // Handle checkbox changes for enabling/disabling textboxes
        document.body.addEventListener('change', (event) => {
            if (event.target.matches('input[type="checkbox"][name^="select_item_"]')) {
                const checkbox = event.target;
                const row = checkbox.closest('tr');
                const qtyInput = row.querySelector('input[name^="release_qty_"]');
                const errorSpan = row.querySelector('span[name^="error_release_qty_"]');

                if (qtyInput) {
                    if (checkbox.checked) {
                        qtyInput.disabled = false;
                        if (qtyInput.value.trim() === '') {
                             errorSpan.textContent = '*'; // Show required if checked and empty
                        }
                    } else {
                        qtyInput.disabled = true;
                        errorSpan.textContent = ''; // Hide error if unchecked
                    }
                }
            }
        });
    });
</script>
{% endblock %}
```

#### `_work_order_product_table.html` (Partial for HTMX loading)

```html
<table id="productTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-center">SN</th>
            <th class="py-3 px-6 text-center">CK</th>
            <th class="py-3 px-6 text-center">To Release Qty</th>
            <th class="py-3 px-6 text-left">Item Code</th>
            <th class="py-3 px-6 text-left">Description</th>
            <th class="py-3 px-6 text-right">Qty</th>
            <th class="py-3 px-6 text-right">Released Qty</th>
            <th class="py-3 px-6 text-right">Remain Qty</th>
        </tr>
    </thead>
    <tbody class="text-gray-700 text-sm font-light">
        {% for product in product_details %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-6 text-center whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-6 text-center">
                {% if product.remain_qty > 0 %}
                <input type="checkbox" name="select_item_{{ product.Id }}" value="{{ product.Id }}"
                       class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                {% else %}
                <span class="text-gray-400">N/A</span>
                {% endif %}
            </td>
            <td class="py-3 px-6 text-center">
                {% if product.remain_qty > 0 %}
                <input type="text" name="release_qty_{{ product.Id }}"
                       class="w-24 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-center"
                       pattern="^\d{1,15}(\.\d{0,3})?$" placeholder="0.000" disabled>
                <span name="error_release_qty_{{ product.Id }}" class="text-red-500 text-xs mt-1 block"></span>
                {% else %}
                <span class="text-gray-400">N/A</span>
                {% endif %}
            </td>
            <td class="py-3 px-6 text-left">{{ product.ItemCode }}</td>
            <td class="py-3 px-6 text-left">{{ product.Description }}</td>
            <td class="py-3 px-6 text-right">
                <label name="lblQty_{{ product.Id }}">{{ product.Qty|floatformat:3 }}</label>
            </td>
            <td class="py-3 px-6 text-right">
                <label name="lblReleasedQty_{{ product.Id }}">{{ product.released_qty|floatformat:3 }}</label>
            </td>
            <td class="py-3 px-6 text-right">
                <label name="lblRemainQty_{{ product.Id }}">{{ product.remain_qty|floatformat:3 }}</label>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-6 text-center text-lg text-red-700 font-semibold">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization for the product table.
    // This script block will run when HTMX swaps in the content.
    $(document).ready(function() {
        $('#productTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers", // Optional: for more pagination controls
            "responsive": true // Makes table responsive
        });
    });
</script>
```

#### `_employee_table.html` (Partial for HTMX loading)

```html
<table id="employeeTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-center">SN</th>
            <th class="py-3 px-6 text-center">CK</th>
            <th class="py-3 px-6 text-left">Name of Employee</th>
            <th class="py-3 px-6 text-center">Emp Id</th>
            <th class="py-3 px-6 text-left">Email Id</th>
        </tr>
    </thead>
    <tbody class="text-gray-700 text-sm font-light">
        {% for employee in employees %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-6 text-center whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-6 text-center">
                <input type="checkbox" name="select_employee_{{ employee.EmpId }}" value="{{ employee.EmpId }}"
                       class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
            </td>
            <td class="py-3 px-6 text-left">{{ employee.EmployeeName }}</td>
            <td class="py-3 px-6 text-center">{{ employee.EmpId }}</td>
            <td class="py-3 px-6 text-left">{{ employee.EmailId1 }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-6 text-center text-lg text-red-700 font-semibold">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization for the employee table.
    $(document).ready(function() {
        $('#employeeTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers",
            "responsive": true
        });
    });
</script>
```

#### `reports/wo_release_report.html` (For PDF Generation)

```html
<!DOCTYPE html>
<html>
<head>
    <title>Work Order Release Report</title>
    <style>
        body { font-family: sans-serif; font-size: 10px; margin: 20px; }
        .header { text-align: center; margin-bottom: 20px; }
        .header h1 { font-size: 18px; margin-bottom: 5px; }
        .header p { font-size: 12px; margin: 0;}
        .section-title { font-size: 14px; font-weight: bold; margin-top: 15px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px;}
        table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .text-right { text-align: right; }
        .footer { margin-top: 30px; font-size: 9px; text-align: center; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 5px 20px; margin-bottom: 15px; }
        .info-item { display: flex; justify-content: space-between; }
        .info-item span:first-child { font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ company.name|default:"Your Company" }}</h1>
        <p>{{ address|default:"[Company Address from DB]" }}</p>
        <p>Work Order Release Report</p>
        <h2>Work Order No: {{ work_order.WONo }} - Release No: {{ wr_no }}</h2>
    </div>

    <div class="section-title">Work Order Details</div>
    <div class="info-grid">
        <div class="info-item"><span>WO Date:</span> <span>{{ work_order.TaskWorkOrderDate|date:"d M Y" }}</span></div>
        <div class="info-item"><span>PO No:</span> <span>{{ work_order.POId.PONo|default:"N/A" }}</span></div>
        <div class="info-item"><span>Category:</span> <span>{{ work_order.CId.Symbol|default:"N/A" }} - {{ work_order.CId.CName|default:"N/A" }}</span></div>
        <div class="info-item"><span>Sub Category:</span> <span>{{ work_order.SCId.SCName|default:"N/A" }}</span></div>
        <div class="info-item"><span>PO Date:</span> <span>{{ work_order.POId.PODate|date:"d M Y"|default:"N/A" }}</span></div>
    </div>

    <div class="section-title">Task Deadlines</div>
    <table>
        <thead>
            <tr>
                <th>Task</th>
                <th>From Date</th>
                <th>To Date</th>
            </tr>
        </thead>
        <tbody>
            <tr><td>Design Finalization</td><td>{{ work_order.TaskDesignFinalization_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskDesignFinalization_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
            <tr><td>Target DAP</td><td>{{ work_order.TaskTargetDAP_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskTargetDAP_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
            <tr><td>Target Manufacturing</td><td>{{ work_order.TaskTargetManufg_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskTargetManufg_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
            <tr><td>Target Try Out</td><td>{{ work_order.TaskTargetTryOut_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskTargetTryOut_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
            <tr><td>Target Dispatch</td><td>{{ work_order.TaskTargetDespach_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskTargetDespach_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
            <tr><td>Target Assembly</td><td>{{ work_order.TaskTargetAssembly_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskTargetAssembly_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
            <tr><td>Target Installation</td><td>{{ work_order.TaskTargetInstalation_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskTargetInstalation_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
            <tr><td>Customer Inspection</td><td>{{ work_order.TaskCustInspection_FDate|date:"d M Y"|default:"N/A" }}</td><td>{{ work_order.TaskCustInspection_TDate|date:"d M Y"|default:"N/A" }}</td></tr>
        </tbody>
    </table>

    <div class="section-title">Instructions</div>
    <p>{{ work_order.instructions_summary|default:"No special instructions." }}</p>

    <div class="section-title">Released Items</div>
    <table>
        <thead>
            <tr>
                <th>SN</th>
                <th>Item Code</th>
                <th>Description</th>
                <th class="text-right">Ordered Qty</th>
                <th class="text-right">Released Qty (This Release)</th>
            </tr>
        </thead>
        <tbody>
            {% for item in report_items %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ item.ItemCode }}</td>
                <td>{{ item.Description }}</td>
                <td class="text-right">{{ item.Qty|floatformat:3 }}</td>
                <td class="text-right">{{ item.ReleasedQty|floatformat:3 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="footer">
        <p>Report Generated on: {{ current_date|date:"d M Y" }}</p>
        <p>This is an auto-generated report. Please do not reply.</p>
    </div>
</body>
</html>
```

#### `work_order_release_list.html` (A simple list page to redirect to)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Recent Work Order Releases</h2>
        <a href="{% url 'work_order_release_detail' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Perform New Release
        </a>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6">
        <table id="recentReleasesTable" class="min-w-full bg-white">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WR No.</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No.</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
            </thead>
            <tbody>
                {% for release in work_order_releases %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ release.WRNo }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ release.WONo }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ release.ItemId.ItemCode }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ release.IssuedQty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ release.SysDate|date:"d M Y" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="py-4 px-6 text-center text-lg text-gray-500">No recent releases found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#recentReleasesTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers",
            "responsive": true
        });
    });
</script>
{% endblock %}
```

### 4.5 URLs (`work_order_release/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
We define paths for the main release page, the HTMX partials for the tables, and the POST endpoint for processing the release. A placeholder list view is also added for redirection.

```python
from django.urls import path
from .views import (
    WorkOrderReleaseDetailView,
    WorkOrderProductTablePartialView,
    EmployeeTablePartialView,
    WorkOrderReleaseListView,
)

urlpatterns = [
    # Main page for performing a work order release.
    # It expects WONo and Id (MId) as query parameters, e.g., /release/?WONo=WO123&Id=45
    path('release/', WorkOrderReleaseDetailView.as_view(), name='work_order_release_detail'),

    # HTMX endpoints for loading table partials
    path('release/products_table/', WorkOrderProductTablePartialView.as_view(), name='work_order_release_products_table'),
    path('release/employees_table/', EmployeeTablePartialView.as_view(), name='work_order_release_employees_table'),

    # HTMX POST endpoint for processing the release submission
    path('release/process/', WorkOrderReleaseDetailView.as_view(), name='work_order_release_process'),

    # A placeholder list view to redirect to after successful release or cancellation
    path('releases/list/', WorkOrderReleaseListView.as_view(), name='work_order_release_list'),
]
```

### 4.6 Tests (`work_order_release/tests.py`)

**Task:** Write comprehensive unit tests for models and services, and integration tests for views.

**Instructions:**
Tests cover model properties and methods (e.g., `released_qty`, `remain_qty`, `validate_release_quantity`), form validation, and view interactions (GET requests for rendering, POST requests for submission, HTMX responses). Mocking is used for external dependencies like email sending and PDF generation.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from django.core.exceptions import ValidationError
from unittest.mock import patch, MagicMock
import json
from decimal import Decimal

from .models import (
    CompanyMaster, WOCategory, WOSubCategory, PurchaseOrderMaster,
    WorkOrderMaster, WorkOrderProductDetail, WorkOrderRelease, HROfficeStaff,
    get_next_wr_no # Import for mocking
)
from .forms import WorkOrderReleaseForm
from .services import WorkOrderReleaseService

class WorkOrderReleaseModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for testing
        cls.company = CompanyMaster.objects.create(CompId=1, MailServerIp="smtp.example.com", ErpSysmail="<EMAIL>")
        cls.category = WOCategory.objects.create(CId=1, Symbol="CAT", CName="Category1")
        cls.subcategory = WOSubCategory.objects.create(SCId=1, SCName="SubCategory1")
        cls.po = PurchaseOrderMaster.objects.create(POId=1, PONo="PO-001", PODate=timezone.localdate())
        
        cls.work_order = WorkOrderMaster.objects.create(
            Id=101,
            WONo="WO-2023-001",
            POId=cls.po,
            TaskWorkOrderDate=timezone.localdate(),
            CId=cls.category,
            SCId=cls.subcategory,
            InstractionPrimerPainting=True,
            InstractionOther="Special instructions."
        )

        cls.product1 = WorkOrderProductDetail.objects.create(
            Id=1,
            MId=cls.work_order,
            ItemCode="ITEM001",
            Description="Product A",
            Qty=Decimal('100.000')
        )
        cls.product2 = WorkOrderProductDetail.objects.create(
            Id=2,
            MId=cls.work_order,
            ItemCode="ITEM002",
            Description="Product B",
            Qty=Decimal('50.000')
        )

        cls.employee1 = HROfficeStaff.objects.create(
            EmpId=1, EmployeeName="John Doe", EmailId1="<EMAIL>",
            WR=1, ResignationDate="", CompId=1, UserID="jdoe"
        )
        cls.employee2 = HROfficeStaff.objects.create(
            EmpId=2, EmployeeName="Jane Smith", EmailId1="<EMAIL>",
            WR=1, ResignationDate="", CompId=1, UserID="jsmith"
        )
        # Ineligible employee
        HROfficeStaff.objects.create(
            EmpId=3, EmployeeName="Retired Bob", EmailId1="<EMAIL>",
            WR=0, ResignationDate=timezone.localdate().strftime('%Y-%m-%d'), CompId=1, UserID="rbob"
        )

    def test_work_order_product_detail_creation(self):
        self.assertEqual(self.product1.ItemCode, "ITEM001")
        self.assertEqual(self.product1.Qty, Decimal('100.000'))
        self.assertEqual(self.product1.MId.WONo, "WO-2023-001")

    def test_released_qty_property(self):
        # No releases yet, so should be 0
        self.assertEqual(self.product1.released_qty, 0)
        
        # Add a release
        WorkOrderRelease.objects.create(
            WRNo="WR-001", WONo="WO-2023-001", ItemId=self.product1, IssuedQty=Decimal('25.000'),
            SysDate=timezone.localdate(), SysTime=timezone.localtime().time(),
            SessionId="testuser", CompId=1, FinYearId=2023
        )
        self.assertEqual(self.product1.released_qty, Decimal('25.000'))

    def test_remain_qty_property(self):
        # Initial remain_qty
        self.assertEqual(self.product1.remain_qty, Decimal('100.000'))
        
        # After a release
        WorkOrderRelease.objects.create(
            WRNo="WR-002", WONo="WO-2023-001", ItemId=self.product1, IssuedQty=Decimal('10.000'),
            SysDate=timezone.localdate(), SysTime=timezone.localtime().time(),
            SessionId="testuser", CompId=1, FinYearId=2023
        )
        self.assertEqual(self.product1.remain_qty, Decimal('90.000')) # 100 - (25 already + 10 new)

    def test_is_fully_released(self):
        self.assertFalse(self.product1.is_fully_released())
        
        # Release all quantity
        WorkOrderRelease.objects.create(
            WRNo="WR-003", WONo="WO-2023-001", ItemId=self.product1, IssuedQty=Decimal('100.000'),
            SysDate=timezone.localdate(), SysTime=timezone.localtime().time(),
            SessionId="testuser", CompId=1, FinYearId=2023
        )
        self.assertTrue(self.product1.is_fully_released()) # Quantity now 0

    def test_validate_release_quantity(self):
        # Valid quantity
        self.assertTrue(self.product1.validate_release_quantity(Decimal('50.000')))
        
        # Exceeds remaining
        with self.assertRaisesMessage(ValidationError, "Release quantity (101) exceeds remaining quantity (100)."):
            self.product1.validate_release_quantity(Decimal('101.000'))
        
        # Zero or negative
        with self.assertRaisesMessage(ValidationError, "Release quantity must be greater than zero."):
            self.product1.validate_release_quantity(Decimal('0.000'))
        with self.assertRaisesMessage(ValidationError, "Release quantity must be greater than zero."):
            self.product1.validate_release_quantity(Decimal('-10.000'))
        
        # Invalid type
        with self.assertRaisesMessage(ValidationError, "Release quantity must be a number."):
            self.product1.validate_release_quantity("abc")

    def test_hr_office_staff_eligibility(self):
        self.assertTrue(self.employee1.is_eligible_for_wr())
        self.assertTrue(self.employee2.is_eligible_for_wr())
        
        # Test ineligible employee
        ineligible_employee = HROfficeStaff.objects.get(EmpId=3)
        self.assertFalse(ineligible_employee.is_eligible_for_wr())

    def test_work_order_master_instructions_summary(self):
        self.assertEqual(self.work_order.instructions_summary, "Primer Painting, Special instructions.")
        
        wo_no_instructions = WorkOrderMaster.objects.create(
            Id=102, WONo="WO-2023-002", TaskWorkOrderDate=timezone.localdate(),
            InstractionPrimerPainting=False, InstractionPainting=False,
            InstractionSelfCertRept=False, InstractionOther=""
        )
        self.assertEqual(wo_no_instructions.instructions_summary, "N/A")


class WorkOrderReleaseFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = CompanyMaster.objects.create(CompId=1, MailServerIp="smtp.example.com", ErpSysmail="<EMAIL>")
        cls.work_order = WorkOrderMaster.objects.create(Id=101, WONo="WO-2023-001", TaskWorkOrderDate=timezone.localdate())
        cls.product1 = WorkOrderProductDetail.objects.create(Id=1, MId=cls.work_order, ItemCode="ITEM001", Qty=Decimal('100.000'))
        cls.product2 = WorkOrderProductDetail.objects.create(Id=2, MId=cls.work_order, ItemCode="ITEM002", Qty=Decimal('50.000'))
        cls.employee1 = HROfficeStaff.objects.create(EmpId=1, EmployeeName="John Doe", EmailId1="<EMAIL>", WR=1, ResignationDate="", CompId=1, UserID="jdoe")
        cls.employee2 = HROfficeStaff.objects.create(EmpId=2, EmployeeName="Jane Smith", EmailId1="<EMAIL>", WR=1, ResignationDate="", CompId=1, UserID="jsmith")

    def test_form_valid_data(self):
        data = {
            'released_items_data': json.dumps({"1": "10.000", "2": "5.000"}),
            'selected_employee_ids': json.dumps([1, 2]),
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        form = WorkOrderReleaseForm(data=data, initial={'comp_id': self.company.CompId})
        self.assertTrue(form.is_valid())
        self.assertEqual(len(form.cleaned_released_items), 2)
        self.assertEqual(len(form.cleaned_selected_employees), 2)

    def test_form_no_items_selected(self):
        data = {
            'released_items_data': json.dumps({}),
            'selected_employee_ids': json.dumps([1]),
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        form = WorkOrderReleaseForm(data=data, initial={'comp_id': self.company.CompId})
        self.assertFalse(form.is_valid())
        self.assertIn("No items selected for release or no quantities entered.", form.errors[None][0])

    def test_form_no_employees_selected(self):
        data = {
            'released_items_data': json.dumps({"1": "10.000"}),
            'selected_employee_ids': json.dumps([]),
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        form = WorkOrderReleaseForm(data=data, initial={'comp_id': self.company.CompId})
        self.assertFalse(form.is_valid())
        self.assertIn("No employees selected to notify.", form.errors[None][0])

    def test_form_invalid_release_quantity(self):
        data = {
            'released_items_data': json.dumps({"1": "101.000"}), # Exceeds available qty
            'selected_employee_ids': json.dumps([1]),
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        form = WorkOrderReleaseForm(data=data, initial={'comp_id': self.company.CompId})
        self.assertFalse(form.is_valid())
        self.assertIn("Validation error for item ITEM001: Release quantity (101) exceeds remaining quantity (100).", form.errors[None][0])

    def test_form_save_release(self):
        data = {
            'released_items_data': json.dumps({"1": "10.000", "2": "5.000"}),
            'selected_employee_ids': json.dumps([1]),
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        form = WorkOrderReleaseForm(data=data, initial={'comp_id': self.company.CompId})
        request = MagicMock()
        request.session = {'compid': self.company.CompId, 'finyear': 2023, 'username': 'testuser'}
        
        with patch('work_order_release.models.get_next_wr_no', return_value='WR-TEST-001'):
            if form.is_valid():
                wr_no = form.save_release(request)
                self.assertEqual(wr_no, 'WR-TEST-001')
                self.assertEqual(WorkOrderRelease.objects.count(), 2)
                release1 = WorkOrderRelease.objects.get(ItemId=self.product1)
                self.assertEqual(release1.IssuedQty, Decimal('10.000'))
                self.assertEqual(release1.WRNo, 'WR-TEST-001')
                release2 = WorkOrderRelease.objects.get(ItemId=self.product2)
                self.assertEqual(release2.IssuedQty, Decimal('5.000'))
            else:
                self.fail(f"Form did not validate: {form.errors}")

class WorkOrderReleaseServiceTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = CompanyMaster.objects.create(CompId=1, MailServerIp="127.0.0.1", ErpSysmail="<EMAIL>")
        cls.category = WOCategory.objects.create(CId=1, Symbol="CAT", CName="Category1")
        cls.subcategory = WOSubCategory.objects.create(SCId=1, SCName="SubCategory1")
        cls.po = PurchaseOrderMaster.objects.create(POId=1, PONo="PO-001", PODate=timezone.localdate())
        cls.work_order = WorkOrderMaster.objects.create(
            Id=101, WONo="WO-2023-001", POId=cls.po, TaskWorkOrderDate=timezone.localdate(),
            CId=cls.category, SCId=cls.subcategory, InstractionPrimerPainting=True
        )
        cls.product1 = WorkOrderProductDetail.objects.create(Id=1, MId=cls.work_order, ItemCode="ITEM001", Qty=Decimal('100.000'))
        cls.release_entry = WorkOrderRelease.objects.create(
            WRNo="WR-TEST-001", WONo="WO-2023-001", ItemId=cls.product1, IssuedQty=Decimal('10.000'),
            SysDate=timezone.localdate(), SysTime=timezone.localtime().time(),
            SessionId="testuser", CompId=1, FinYearId=2023
        )
        cls.employee1 = HROfficeStaff.objects.create(EmpId=1, EmployeeName="John Doe", EmailId1="<EMAIL>", WR=1, ResignationDate="", CompId=1, UserID="jdoe")
    
    def setUp(self):
        self.request = MagicMock()
        self.request.session = {'compid': self.company.CompId, 'username': 'testuser'}
        self.request.build_absolute_uri = MagicMock(return_value='http://testserver/')
        self.service = WorkOrderReleaseService(self.request)

    @patch('work_order_release.services.HTML')
    @patch('work_order_release.services.os.makedirs')
    def test_generate_wr_report_pdf(self, mock_makedirs, mock_html):
        mock_instance = MagicMock()
        mock_html.return_value = mock_instance
        
        pdf_path = self.service.generate_wr_report_pdf(
            wo_id=self.work_order.Id,
            wr_no=self.release_entry.WRNo,
            selected_item_ids=[self.product1.Id]
        )
        
        mock_html.assert_called_once()
        mock_instance.write_pdf.assert_called_once()
        self.assertTrue(pdf_path.endswith(f"WorkOrderRelease_{self.work_order.WONo}_{self.release_entry.WRNo}.pdf"))

    @patch('work_order_release.services.EmailMessage')
    @patch('work_order_release.services.os.path.exists', return_value=True) # Mock file existence
    @patch('work_order_release.services.os.remove') # Mock file removal
    def test_send_release_emails(self, mock_remove, mock_exists, mock_email_message):
        mock_email_instance = MagicMock()
        mock_email_message.return_value = mock_email_instance

        # Create a dummy PDF file for the test
        dummy_pdf_path = os.path.join(self.service.temp_dir, "test_report.pdf")
        with open(dummy_pdf_path, 'w') as f:
            f.write("dummy content")

        result = self.service.send_release_emails(
            wo_no=self.work_order.WONo,
            wr_no=self.release_entry.WRNo,
            pdf_path=dummy_pdf_path,
            selected_employees=[self.employee1]
        )
        
        self.assertTrue(result)
        mock_email_message.assert_called_once_with(
            f"Work Order Release WONo:{self.work_order.WONo} WRNo:{self.release_entry.WRNo}",
            "Dear Sir, This is Auto generated mail by ERP system, please do not reply.<br><br> Thank you.",
            self.company.ErpSysmail,
            [self.employee1.EmailId1]
        )
        mock_email_instance.content_subtype = "html"
        mock_email_instance.attach_file.assert_called_once_with(dummy_pdf_path)
        mock_email_instance.send.assert_called_once_with(fail_silently=False)
        mock_remove.assert_called_once_with(dummy_pdf_path) # Verify temporary file cleanup

class WorkOrderReleaseViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = CompanyMaster.objects.create(CompId=1, MailServerIp="smtp.example.com", ErpSysmail="<EMAIL>")
        cls.work_order = WorkOrderMaster.objects.create(Id=101, WONo="WO-2023-001", TaskWorkOrderDate=timezone.localdate())
        cls.product1 = WorkOrderProductDetail.objects.create(Id=1, MId=cls.work_order, ItemCode="ITEM001", Description="Product A", Qty=Decimal('100.000'))
        cls.product2 = WorkOrderProductDetail.objects.create(Id=2, MId=cls.work_order, ItemCode="ITEM002", Description="Product B", Qty=Decimal('50.000'))
        cls.employee1 = HROfficeStaff.objects.create(EmpId=1, EmployeeName="John Doe", EmailId1="<EMAIL>", WR=1, ResignationDate="", CompId=1, UserID="jdoe")
        cls.employee2 = HROfficeStaff.objects.create(EmpId=2, EmployeeName="Jane Smith", EmailId1="<EMAIL>", WR=1, ResignationDate="", CompId=1, UserID="jsmith")
    
    def setUp(self):
        self.client = Client()
        self.session = self.client.session
        self.session['compid'] = self.company.CompId
        self.session['finyear'] = 2023
        self.session['username'] = 'testuser'
        self.session.save()

    def test_detail_view_get(self):
        response = self.client.get(reverse('work_order_release_detail'), {'WONo': self.work_order.WONo, 'Id': self.work_order.Id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order_release/release_detail.html')
        self.assertContains(response, self.work_order.WONo)
        self.assertContains(response, str(self.work_order.Id))

    def test_detail_view_get_missing_id(self):
        response = self.client.get(reverse('work_order_release_detail'), {'WONo': self.work_order.WONo})
        self.assertEqual(response.status_code, 200) # Still 200, but with an error message
        messages_on_response = list(response.context['messages'])
        self.assertIn("Work Order ID is required.", [str(m) for m in messages_on_response])
    
    def test_product_table_partial_view(self):
        response = self.client.get(reverse('work_order_release_products_table'), {'Id': self.work_order.Id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order_release/_work_order_product_table.html')
        self.assertContains(response, self.product1.ItemCode)
        self.assertContains(response, str(self.product2.Qty))

    def test_employee_table_partial_view(self):
        response = self.client.get(reverse('work_order_release_employees_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order_release/_employee_table.html')
        self.assertContains(response, self.employee1.EmployeeName)
        self.assertContains(response, self.employee2.EmailId1)
        self.assertNotContains(response, "Retired Bob") # Ensure ineligible employee is not present

    @patch('work_order_release.services.WorkOrderReleaseService.generate_wr_report_pdf', return_value="/tmp/mock_report.pdf")
    @patch('work_order_release.services.WorkOrderReleaseService.send_release_emails', return_value=True)
    @patch('work_order_release.models.get_next_wr_no', return_value='WR-MOCK-001')
    def test_process_release_post_success(self, mock_get_wr_no, mock_send_emails, mock_generate_pdf):
        post_data = {
            'released_items_data': {"1": "10.000", "2": "5.000"},
            'selected_employee_ids': [self.employee1.EmpId],
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        response = self.client.post(
            reverse('work_order_release_process'), 
            json.dumps(post_data), 
            content_type='application/json',
            HTTP_HX_REQUEST='true' # Indicate an HTMX request
        )
        
        self.assertEqual(response.status_code, 204) # HTMX success code (no content)
        self.assertIn('HX-Location', response.headers)
        self.assertEqual(response.headers['HX-Location'], reverse_lazy('work_order_release_list'))
        
        # Verify database inserts
        self.assertEqual(WorkOrderRelease.objects.count(), 2)
        self.assertTrue(WorkOrderRelease.objects.filter(WRNo='WR-MOCK-001', ItemId=self.product1, IssuedQty=Decimal('10.000')).exists())
        self.assertTrue(WorkOrderRelease.objects.filter(WRNo='WR-MOCK-001', ItemId=self.product2, IssuedQty=Decimal('5.000')).exists())
        
        # Verify service calls
        mock_generate_pdf.assert_called_once()
        mock_send_emails.assert_called_once()

    @patch('work_order_release.services.WorkOrderReleaseService.generate_wr_report_pdf', return_value="/tmp/mock_report.pdf")
    @patch('work_order_release.services.WorkOrderReleaseService.send_release_emails', return_value=True)
    def test_process_release_post_invalid_quantity(self, mock_send_emails, mock_generate_pdf):
        post_data = {
            'released_items_data': {"1": "101.000"}, # Exceeds available qty
            'selected_employee_ids': [self.employee1.EmpId],
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        response = self.client.post(
            reverse('work_order_release_process'), 
            json.dumps(post_data), 
            content_type='application/json',
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertIn('Invalid input details:', response.content.decode())
        self.assertFalse(WorkOrderRelease.objects.exists()) # No records created
        mock_generate_pdf.assert_not_called()
        mock_send_emails.assert_not_called()

    @patch('work_order_release.services.WorkOrderReleaseService.generate_wr_report_pdf', side_effect=Exception("PDF error"))
    @patch('work_order_release.services.WorkOrderReleaseService.send_release_emails')
    def test_process_release_post_service_failure(self, mock_send_emails, mock_generate_pdf):
        post_data = {
            'released_items_data': {"1": "10.000"},
            'selected_employee_ids': [self.employee1.EmpId],
            'work_order_id': self.work_order.Id,
            'work_order_no': self.work_order.WONo
        }
        response = self.client.post(
            reverse('work_order_release_process'), 
            json.dumps(post_data), 
            content_type='application/json',
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 500) # Internal Server Error
        self.assertIn("An unexpected error occurred", response.content.decode())
        self.assertFalse(WorkOrderRelease.objects.exists()) # Transaction should be rolled back
        mock_generate_pdf.assert_called_once()
        mock_send_emails.assert_not_called() # Should not be called if PDF generation failed
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates and views are already designed with HTMX and Alpine.js in mind.

*   **HTMX for Dynamic Updates:**
    *   The main `release_detail.html` loads the product table (`_work_order_product_table.html`) and employee table (`_employee_table.html`) using `hx-get="{% url ... %}" hx-trigger="load"`.
    *   After a successful release POST (handled by `WorkOrderReleaseDetailView.post`), the `HX-Location` header is used to redirect the browser to the `work_order_release_list` page, providing a full page update consistent with the ASP.NET `Response.Redirect`.
    *   Error messages from failed POST requests are sent via the `HX-Trigger` header, which can be picked up by a global message display mechanism (e.g., in `base.html` or Alpine.js).
    *   The `Submit` button's `hx-post` dynamically collects data from the checkboxes and textboxes on the page and sends it as a JSON payload. This replaces the complex `GridView` data collection on the server-side.
    *   Loading indicators are implicitly handled by HTMX's `hx-indicator` attribute on containers or globally.

*   **Alpine.js for UI State Management:**
    *   A simple Alpine.js component (`x-data="{ showModal: false }"`) is used to control the visibility of the confirmation modal.
    *   The `Submit` button `@click="showModal = true"` opens the modal.
    *   Buttons within the modal (`Yes, Release`, `No, Cancel`) update the `showModal` state and/or trigger HTMX requests.
    *   The client-side validation for "To Release Qty" (`TextBox1`) is re-implemented using plain JavaScript (or Alpine.js directives could be used for more reactivity), simulating the `RequiredFieldValidator` and `RegularExpressionValidator` and dynamically checking/unchecking the associated checkbox.

*   **DataTables for List Views:**
    *   Both `GridView1` and `GridView2` are replaced by standard HTML `<table>` elements initialized with DataTables.
    *   The DataTables initialization script is included directly within the HTMX-loaded partial templates (`_work_order_product_table.html`, `_employee_table.html`). This ensures that DataTables is applied to the newly loaded content after HTMX performs a swap.
    *   `"destroy": true` is added to DataTables options to gracefully re-initialize if the content is swapped multiple times.

*   **DRY Template Inheritance:**
    *   All main templates (`release_detail.html`, `work_order_release_list.html`) extend `core/base.html` as instructed.
    *   Partial templates (`_work_order_product_table.html`, `_employee_table.html`) are designed to be included by HTMX without needing to extend `base.html` themselves.

## Final Notes

*   **Placeholders:** Remember to replace `[COMPANY_ADDRESS_FROM_DB]` in `reports/wo_release_report.html` with actual logic to fetch the company address, similar to how `fun.CompAdd` worked in ASP.NET.
*   **Session Management:** The Django code assumes `request.session['compid']`, `request.session['finyear']`, and `request.session['username']` are populated (e.g., by an authentication system or middleware, mimicking ASP.NET Session variables).
*   **Transaction Number Generation:** The `get_next_wr_no` function in `models.py` is a placeholder. A production-ready system should implement a robust, concurrent-safe transaction number generation mechanism, possibly using database sequences or a dedicated service that handles locking.
*   **Error Handling and User Feedback:** Error messages are passed via Django's `messages` framework. For HTMX requests, these messages are often triggered by `HX-Trigger` headers which a global Alpine.js listener can capture and display.
*   **WeasyPrint/PDF Dependencies:** Ensure `WeasyPrint` (and its underlying system dependencies like `Pango`, `Cairo`, `GDK-Pixbuf`) is installed in your Django environment for PDF generation.
*   **Email Configuration:** Django's `settings.py` must be configured for email sending (e.g., `EMAIL_BACKEND`, `EMAIL_HOST`, `EMAIL_PORT`, etc.) for `send_release_emails` to function.
*   **Temporary Files:** The `WorkOrderReleaseService` handles temporary PDF file creation and deletion. Ensure your `settings.MEDIA_ROOT` is correctly configured and has write permissions for the Django application.
*   **Security:** Always sanitize user inputs and use Django's built-in security features (CSRF tokens, ORM for preventing SQL injection). `hx-post` handles CSRF automatically with `{% csrf_token %}` in the form.
*   **Scalability:** For very large tables, consider server-side processing with DataTables instead of client-side, or pagination/infinite scroll with HTMX. This example uses client-side DataTables which is suitable for moderate datasets.