## ASP.NET to Django Conversion Script: WORelease_Dashbord Modernization Plan

This document outlines a strategic plan for migrating your ASP.NET `WORelease_Dashbord.aspx` and its associated C# code-behind to a modern Django application. This approach emphasizes automation, clear separation of concerns, and leveraging cutting-edge web technologies like HTMX and Alpine.js for a highly interactive user experience without complex JavaScript.

**Important Note:** The provided ASP.NET `.aspx` and `.aspx.cs` files are largely empty, containing no explicit UI elements or database interaction logic. This means our AI-assisted analysis tool could not infer concrete details about your application's data structure or functionality from *this specific input*.

Therefore, this plan proceeds with a **generic, yet representative, example** of what a "Work Order Release Dashboard" might entail, using placeholders for typical data elements (e.g., `WorkOrderRelease` model with fields like `work_order_number`, `release_date`, `status`). In a real-world scenario, our AI automation would first analyze your complete ASP.NET codebase and database schema to extract these precise details, then automatically generate the Django components based on those findings.

## AutoERP Guidelines:

Our modernization approach adheres to the following principles for a robust, maintainable, and scalable Django application:

-   **'Fat Model, Thin View'**: Business logic resides exclusively within Django models, keeping views concise (under 15 lines per method).
-   **Database Mapping**: Models will directly map to your existing database tables using `managed = False` and `db_table`, preserving your current data structure.
-   **DataTables Integration**: All list views will feature DataTables for advanced client-side search, sorting, and pagination capabilities.
-   **HTMX for Dynamic Interactions**: We'll use HTMX to create seamless, dynamic user experiences without full page reloads, making the application feel fast and responsive.
-   **Alpine.js for UI State**: Alpine.js will manage simple client-side UI states, such as modal visibility, with minimal JavaScript.
-   **DRY Templates**: Templates will extend a `core/base.html` (not included here) to ensure consistency and reusability across the application.
-   **Comprehensive Testing**: We aim for at least 80% test coverage with dedicated unit tests for models and integration tests for views.
-   **DRY Principle**: We minimize code duplication through careful design and template inheritance.
-   **Tailwind CSS**: For clean, utility-first styling, ensuring a modern and responsive user interface.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions (Automation Perspective):**
Given the empty ASP.NET code, a human or AI would typically:

-   **Scan for Data Access:** Look for `SqlDataSource` controls, ADO.NET code (e.g., `SqlConnection`, `SqlCommand`, `SqlDataReader`), or ORM configurations (e.g., Entity Framework setup if present in other files).
-   **Identify Table Names:** Extract table names from `SELECT`, `INSERT`, `UPDATE`, `DELETE` statements or `Table Name` properties in UI controls.
-   **Map Columns:** Infer column names and types from SQL queries, `DataField` properties in `GridView` columns, or C# data objects (e.g., `DataTable` columns).

**Inferred Example for "Work Order Release Dashboard":**
For demonstration purposes, we assume a database table named `WORelease_tbl` with the following columns:
-   `WOReleaseID` (Primary Key, Integer)
-   `WorkOrderNumber` (Text)
-   `ReleaseDate` (Date)
-   `Status` (Text)
-   `Remarks` (Text, optional)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions (Automation Perspective):**
Without explicit code, an AI would analyze:

-   **Event Handlers:** Look for `OnRowCommand`, `OnItemCommand`, `OnClick` events associated with `GridView`s, `Button`s, or `LinkButton`s that trigger database operations.
-   **Form Submissions:** Identify `PostBack` events or server-side form handling that lead to data creation or updates.
-   **Query Parameters:** Look for ID parameters in URLs that indicate retrieval, update, or deletion of specific records.

**Inferred Example:**
Typically, a dashboard allows:
-   **Read:** Displaying a list of Work Order Releases.
-   **Create:** Adding new Work Order Releases.
-   **Update:** Modifying existing Work Order Releases.
-   **Delete:** Removing Work Order Releases.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions (Automation Perspective):**
Given the empty `.aspx` file, an AI would look for:

-   **Data Grids:** Identify `asp:GridView`, `asp:Repeater`, `asp:ListView` for displaying lists of data.
-   **Input Fields:** Locate `asp:TextBox`, `asp:DropDownList`, `asp:CheckBox`, `asp:Calendar` for user input.
-   **Action Buttons:** Find `asp:Button`, `asp:LinkButton`, `asp:ImageButton` to trigger actions.

**Inferred Example:**
We assume a `GridView` would be used to display a list of work order releases, with `TextBox`es and `DropDownLists` for input forms (create/edit).

---

### Step 4: Generate Django Code

Based on the inferred structure, here's the Django implementation:

#### 4.1 Models (`sales_distribution/models.py`)

This model directly maps to your existing `WORelease_tbl` database table. All business logic related to `WorkOrderRelease` objects would be implemented as methods within this class.

```python
from django.db import models
from datetime import date # Required for default date values or comparisons

class WorkOrderRelease(models.Model):
    # Primary Key - Assuming it's an auto-incrementing integer managed by the DB
    # Django will automatically map 'id' to the primary key unless specified otherwise.
    # If your PK is named differently (e.g., WOReleaseID), you can specify it:
    # w_o_release_id = models.IntegerField(db_column='WOReleaseID', primary_key=True)
    
    work_order_number = models.CharField(db_column='WorkOrderNumber', max_length=100, unique=True, verbose_name='Work Order Number')
    release_date = models.DateField(db_column='ReleaseDate', default=date.today, verbose_name='Release Date')
    status = models.CharField(db_column='Status', max_length=50, default='Pending', verbose_name='Status')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True, verbose_name='Remarks')

    class Meta:
        managed = False  # Set to False because the table already exists in the database
        db_table = 'WORelease_tbl' # The exact name of your database table
        verbose_name = 'Work Order Release'
        verbose_name_plural = 'Work Order Releases'
        ordering = ['-release_date', 'work_order_number'] # Default ordering for list views

    def __str__(self):
        """Returns the string representation of the WorkOrderRelease object."""
        return f"{self.work_order_number} ({self.status})"

    def can_be_edited(self):
        """
        Example business logic: A work order can only be edited if its status is 'Pending'.
        This method keeps the view logic thin.
        """
        return self.status == 'Pending'

    def release_item(self):
        """
        Example business logic: Method to change the status to 'Released'.
        This encapsulates the release process within the model.
        """
        if self.status == 'Pending':
            self.status = 'Released'
            self.save()
            return True
        return False
```

#### 4.2 Forms (`sales_distribution/forms.py`)

This form will handle data input for creating and updating `WorkOrderRelease` objects. It leverages Django's `ModelForm` for easy mapping to the `WorkOrderRelease` model and includes Tailwind CSS classes for consistent styling.

```python
from django import forms
from .models import WorkOrderRelease

class WorkOrderReleaseForm(forms.ModelForm):
    class Meta:
        model = WorkOrderRelease
        fields = ['work_order_number', 'release_date', 'status', 'remarks']
        widgets = {
            'work_order_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., WO-2023-001'}),
            'release_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'status': forms.Select(choices=[('Pending', 'Pending'), ('Released', 'Released'), ('Cancelled', 'Cancelled')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        
    def clean_work_order_number(self):
        """Custom validation for work order number, ensuring it's unique if not editing."""
        work_order_number = self.cleaned_data['work_order_number']
        if not self.instance.pk and WorkOrderRelease.objects.filter(work_order_number=work_order_number).exists():
            raise forms.ValidationError("This work order number already exists.")
        return work_order_number
```

#### 4.3 Views (`sales_distribution/views.py`)

Views are kept intentionally thin, delegating all business logic to the `WorkOrderRelease` model. They handle rendering forms, processing submissions, and sending appropriate HTMX responses.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render, get_object_or_404
from .models import WorkOrderRelease
from .forms import WorkOrderReleaseForm

class WorkOrderReleaseListView(ListView):
    model = WorkOrderRelease
    template_name = 'sales_distribution/workorderrelease/list.html'
    context_object_name = 'workorderreleases'

class WorkOrderReleaseTablePartialView(ListView):
    """
    Renders only the table portion for HTMX updates, improving performance.
    """
    model = WorkOrderRelease
    template_name = 'sales_distribution/workorderrelease/_workorderrelease_table.html'
    context_object_name = 'workorderreleases'

class WorkOrderReleaseCreateView(CreateView):
    model = WorkOrderRelease
    form_class = WorkOrderReleaseForm
    template_name = 'sales_distribution/workorderrelease/_workorderrelease_form.html' # Use partial template
    success_url = reverse_lazy('workorderrelease_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order Release added successfully.')
        # HTMX check: If it was an HTMX request, return a no-content response
        # with a trigger to refresh the list on the client side.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshWorkOrderReleaseList' # Custom HTMX event
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Indicate that this is a create form for template rendering logic
        context['is_create_form'] = True 
        return context

class WorkOrderReleaseUpdateView(UpdateView):
    model = WorkOrderRelease
    form_class = WorkOrderReleaseForm
    template_name = 'sales_distribution/workorderrelease/_workorderrelease_form.html' # Use partial template
    success_url = reverse_lazy('workorderrelease_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order Release updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderReleaseList'
                }
            )
        return response

class WorkOrderReleaseDeleteView(DeleteView):
    model = WorkOrderRelease
    template_name = 'sales_distribution/workorderrelease/_workorderrelease_confirm_delete.html' # Use partial template
    success_url = reverse_lazy('workorderrelease_list')

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        # Example of calling business logic from model before deleting
        # if not obj.can_be_deleted(): # Assuming such a method exists in the model
        #     messages.error(self.request, 'Work Order Release cannot be deleted in its current state.')
        #     return HttpResponse(status=400) # Bad Request
            
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order Release deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderReleaseList'
                }
            )
        return response

# Example of a custom view to trigger a model method via HTMX
class WorkOrderReleaseReleaseView(View):
    def post(self, request, pk):
        work_order_release = get_object_or_404(WorkOrderRelease, pk=pk)
        if work_order_release.release_item(): # Call model business logic
            messages.success(request, f"Work Order {work_order_release.work_order_number} released successfully.")
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderReleaseList'
                }
            )
        else:
            messages.error(request, f"Work Order {work_order_release.work_order_number} could not be released.")
            return HttpResponse(status=400) # Bad request
```

#### 4.4 Templates (`sales_distribution/templates/sales_distribution/workorderrelease/`)

Templates are designed for HTMX interactions, supporting modals for forms and dynamic table updates.

**`list.html`**: The main page displaying the list of work order releases.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Work Order Releases</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'workorderrelease_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Work Order Release
        </button>
    </div>
    
    <div id="workorderreleaseTable-container"
         hx-trigger="load, refreshWorkOrderReleaseList from:body" {# Triggers on page load and custom event #}
         hx-get="{% url 'workorderrelease_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Work Order Releases...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal"
         x-init="$watch('showModal', value => { if (!value) { document.getElementById('modalContent').innerHTML = ''; } })"
         >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto"
             _="on htmx:afterSwap remove .is-active from #modal end">
            <!-- Content loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js components here if needed, beyond the modal toggle -->
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for modal visibility, if not handled by HTMX alone
        // _="on click add .is-active to #modal" implies Alpine.js is not strictly necessary for modal show/hide with HTMX
        // but could be used for more complex UI state management.
    });
</script>
{% endblock %}
```

**`_workorderrelease_table.html`**: Partial template for the DataTables.

```html
<div class="p-6">
    <table id="workorderreleaseTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Release Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in workorderreleases %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-gray-800">{{ obj.work_order_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-gray-800">{{ obj.release_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-gray-800">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.status == 'Released' %}bg-green-100 text-green-800
                        {% elif obj.status == 'Pending' %}bg-yellow-100 text-yellow-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ obj.status }}
                    </span>
                </td>
                <td class="py-3 px-4 text-gray-600 max-w-xs truncate">{{ obj.remarks|default_if_none:"-" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'workorderrelease_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    {% if obj.status == 'Pending' %}
                    <button 
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-post="{% url 'workorderrelease_release' obj.pk %}"
                        hx-confirm="Are you sure you want to release Work Order {{ obj.work_order_number }}?"
                        hx-swap="none"
                        hx-target="body"> {# Target body to receive messages #}
                        Release
                    </button>
                    {% endif %}
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'workorderrelease_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#workorderreleaseTable')) {
            $('#workorderreleaseTable').DataTable().destroy();
        }
        $('#workorderreleaseTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 5] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>
```

**`_workorderrelease_form.html`**: Partial template for create/edit forms within a modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order Release</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="body"> {# Target body to receive messages #}
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {# Iterate over specific fields to control layout if needed, otherwise use form.as_p or form.as_div #}
            {% for field in form %}
            <div class="mb-4 {% if field.name == 'remarks' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4 border-t pt-6">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Work Order
            </button>
        </div>
    </form>
</div>
```

**`_workorderrelease_confirm_delete.html`**: Partial template for delete confirmation within a modal.

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Work Order Release **{{ object.work_order_number }}**?</p>
    <div class="text-red-500 mb-6">
        <p>This action cannot be undone.</p>
        {% if object.status != 'Pending' %}
        <p class="font-bold mt-2">Warning: This Work Order Release is not in 'Pending' status. Deletion may have downstream implications.</p>
        {% endif %}
    </div>

    <form hx-post="{% url 'workorderrelease_delete' object.pk %}" hx-swap="none" hx-target="body"> {# Target body for messages #}
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete Work Order
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`sales_distribution/urls.py`)

Defines the URL structure for all `WorkOrderRelease` related views.

```python
from django.urls import path
from .views import (
    WorkOrderReleaseListView,
    WorkOrderReleaseTablePartialView,
    WorkOrderReleaseCreateView,
    WorkOrderReleaseUpdateView,
    WorkOrderReleaseDeleteView,
    WorkOrderReleaseReleaseView # Custom view for business logic
)

urlpatterns = [
    # Main list page
    path('workorderreleases/', WorkOrderReleaseListView.as_view(), name='workorderrelease_list'),
    
    # HTMX partial for the table content
    path('workorderreleases/table/', WorkOrderReleaseTablePartialView.as_view(), name='workorderrelease_table'),
    
    # CRUD operations via modal (HTMX requests)
    path('workorderreleases/add/', WorkOrderReleaseCreateView.as_view(), name='workorderrelease_add'),
    path('workorderreleases/<int:pk>/edit/', WorkOrderReleaseUpdateView.as_view(), name='workorderrelease_edit'),
    path('workorderreleases/<int:pk>/delete/', WorkOrderReleaseDeleteView.as_view(), name='workorderrelease_delete'),
    
    # Custom action: release work order
    path('workorderreleases/<int:pk>/release/', WorkOrderReleaseReleaseView.as_view(), name='workorderrelease_release'),
]
```

#### 4.6 Tests (`sales_distribution/tests.py`)

Comprehensive tests for the model and views to ensure functionality and data integrity.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import WorkOrderRelease
from datetime import date
from django.contrib.messages import get_messages

class WorkOrderReleaseModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.wo_pending = WorkOrderRelease.objects.create(
            work_order_number='WO-PENDING-001',
            release_date=date(2023, 1, 15),
            status='Pending',
            remarks='Test remarks for pending WO.'
        )
        cls.wo_released = WorkOrderRelease.objects.create(
            work_order_number='WO-RELEASED-002',
            release_date=date(2023, 1, 10),
            status='Released',
            remarks='Test remarks for released WO.'
        )
  
    def test_workorderrelease_creation(self):
        obj = WorkOrderRelease.objects.get(work_order_number='WO-PENDING-001')
        self.assertEqual(obj.release_date, date(2023, 1, 15))
        self.assertEqual(obj.status, 'Pending')
        self.assertEqual(obj.remarks, 'Test remarks for pending WO.')
        
    def test_work_order_number_label(self):
        obj = WorkOrderRelease.objects.get(work_order_number='WO-PENDING-001')
        field_label = obj._meta.get_field('work_order_number').verbose_name
        self.assertEqual(field_label, 'Work Order Number')
        
    def test_str_representation(self):
        obj = WorkOrderRelease.objects.get(work_order_number='WO-PENDING-001')
        self.assertEqual(str(obj), 'WO-PENDING-001 (Pending)')

    def test_can_be_edited_method(self):
        self.assertTrue(self.wo_pending.can_be_edited())
        self.assertFalse(self.wo_released.can_be_edited())
        
    def test_release_item_method(self):
        # Test releasing a pending item
        self.assertTrue(self.wo_pending.release_item())
        self.assertEqual(self.wo_pending.status, 'Released')
        
        # Test trying to release an already released item
        self.assertFalse(self.wo_released.release_item())
        self.assertEqual(self.wo_released.status, 'Released') # Should remain 'Released'

class WorkOrderReleaseViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.wo_1 = WorkOrderRelease.objects.create(
            work_order_number='WO-TEST-001',
            release_date=date(2023, 2, 1),
            status='Pending'
        )
        cls.wo_2 = WorkOrderRelease.objects.create(
            work_order_number='WO-TEST-002',
            release_date=date(2023, 2, 2),
            status='Released'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('workorderrelease_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderrelease/list.html')
        self.assertContains(response, 'Work Order Releases') # Check for page title
        
    def test_table_partial_view_get(self):
        response = self.client.get(reverse('workorderrelease_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderrelease/_workorderrelease_table.html')
        self.assertContains(response, self.wo_1.work_order_number)
        self.assertContains(response, self.wo_2.work_order_number)

    def test_create_view_get(self):
        response = self.client.get(reverse('workorderrelease_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderrelease/_workorderrelease_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Work Order Release')
        
    def test_create_view_post_success(self):
        data = {
            'work_order_number': 'WO-NEW-003',
            'release_date': '2023-03-01',
            'status': 'Pending',
            'remarks': 'New item for testing.'
        }
        response = self.client.post(reverse('workorderrelease_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful creation
        self.assertTrue(WorkOrderRelease.objects.filter(work_order_number='WO-NEW-003').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order Release added successfully.')
        
    def test_create_view_post_htmx_success(self):
        data = {
            'work_order_number': 'WO-HTMX-004',
            'release_date': '2023-03-05',
            'status': 'Pending',
            'remarks': 'HTMX test item.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorderrelease_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(WorkOrderRelease.objects.filter(work_order_number='WO-HTMX-004').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderReleaseList')
        
    def test_create_view_post_invalid(self):
        data = {
            'work_order_number': 'WO-TEST-001', # Duplicate
            'release_date': '2023-03-01',
            'status': 'Pending'
        }
        response = self.client.post(reverse('workorderrelease_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertFalse(WorkOrderRelease.objects.filter(work_order_number='WO-TEST-001').count() > 1)
        self.assertContains(response, 'This work order number already exists.')

    def test_update_view_get(self):
        response = self.client.get(reverse('workorderrelease_edit', args=[self.wo_1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderrelease/_workorderrelease_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Work Order Release')
        self.assertContains(response, self.wo_1.work_order_number)
        
    def test_update_view_post_success(self):
        data = {
            'work_order_number': self.wo_1.work_order_number, # Keep same number or change to unique
            'release_date': '2023-02-01',
            'status': 'Released', # Change status
            'remarks': 'Updated remarks.'
        }
        response = self.client.post(reverse('workorderrelease_edit', args=[self.wo_1.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.wo_1.refresh_from_db()
        self.assertEqual(self.wo_1.status, 'Released')
        
    def test_update_view_post_htmx_success(self):
        data = {
            'work_order_number': self.wo_2.work_order_number,
            'release_date': '2023-02-02',
            'status': 'Pending', # Change status
            'remarks': 'Updated remarks via HTMX.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorderrelease_edit', args=[self.wo_2.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.wo_2.refresh_from_db()
        self.assertEqual(self.wo_2.status, 'Pending')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderReleaseList')

    def test_delete_view_get(self):
        response = self.client.get(reverse('workorderrelease_delete', args=[self.wo_1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderrelease/_workorderrelease_confirm_delete.html')
        self.assertContains(response, f'Are you sure you want to delete Work Order Release **{self.wo_1.work_order_number}**?')
        
    def test_delete_view_post_success(self):
        pk_to_delete = self.wo_1.pk # Use the existing object's PK
        response = self.client.post(reverse('workorderrelease_delete', args=[pk_to_delete]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(WorkOrderRelease.objects.filter(pk=pk_to_delete).exists())
        
    def test_delete_view_post_htmx_success(self):
        # Create a new object to delete specifically for this HTMX test
        temp_wo = WorkOrderRelease.objects.create(work_order_number='WO-TO-DELETE', release_date=date(2024, 1, 1), status='Pending')
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorderrelease_delete', args=[temp_wo.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(WorkOrderRelease.objects.filter(pk=temp_wo.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderReleaseList')

    def test_release_view_post_success(self):
        # Ensure the item is pending before attempting to release
        self.wo_1.status = 'Pending'
        self.wo_1.save()

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorderrelease_release', args=[self.wo_1.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.wo_1.refresh_from_db()
        self.assertEqual(self.wo_1.status, 'Released')
        messages = list(get_messages(response.wsgi_request)) # Access messages from the request
        self.assertEqual(str(messages[0]), f"Work Order {self.wo_1.work_order_number} released successfully.")
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderReleaseList')

    def test_release_view_post_failure_already_released(self):
        # The wo_2 is already 'Released'
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorderrelease_release', args=[self.wo_2.pk]), **headers)
        self.assertEqual(response.status_code, 400) # Bad Request because release_item returns False
        self.wo_2.refresh_from_db()
        self.assertEqual(self.wo_2.status, 'Released') # Status should not change
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), f"Work Order {self.wo_2.work_order_number} could not be released.")

```

### Step 5: HTMX and Alpine.js Integration

This plan fully embraces HTMX for all dynamic interactions and form submissions. Alpine.js is used for simple UI toggles like modal visibility.

-   **Dynamic Table Refresh**: The `_workorderrelease_table.html` partial is loaded via `hx-get` and refreshes upon `load` or a custom `refreshWorkOrderReleaseList` event triggered after successful CRUD operations (`HX-Trigger` header from views).
-   **Modal Forms**: Create, Update, and Delete forms are loaded into a modal (`#modalContent`) using `hx-get`. Form submissions use `hx-post` with `hx-swap="none"` and `hx-target="body"` to allow Django to handle messages and HTMX to trigger a table refresh without replacing any UI elements.
-   **Inline Actions**: Buttons like "Edit", "Delete", and "Release" have `hx-get` or `hx-post` attributes to trigger actions and load corresponding content into the modal or trigger background processes.
-   **UI Feedback**: Messages (success/error) are handled by Django's messages framework and are rendered at the top of the `base.html` template. HTMX triggers ensure these messages appear and the table updates.
-   **DataTables**: Integrated directly within the `_workorderrelease_table.html` partial, ensuring client-side sorting, searching, and pagination. The JavaScript to initialize DataTables runs every time the partial is swapped in.

### Final Notes

This comprehensive plan provides a blueprint for migrating your ASP.NET Work Order Release Dashboard to Django. While the initial ASP.NET code was minimal, this example demonstrates the principles and specific code patterns our AI-assisted automation would generate once concrete details are extracted from your full application.

The benefits of this Django modernization include:

-   **Enhanced User Experience**: HTMX and Alpine.js provide a smooth, app-like feel without heavy JavaScript frameworks.
-   **Improved Maintainability**: Clear separation of concerns (Model-View-Template) makes the codebase easier to understand and manage.
-   **Increased Productivity**: Leveraging Django's robust features, forms, and ORM speeds up development.
-   **Future-Proof Architecture**: A modern, test-driven approach ensures the application is scalable and adaptable to future requirements.
-   **Reduced Manual Effort**: This structured approach is ideal for automation tools, significantly reducing the manual coding required for migration.