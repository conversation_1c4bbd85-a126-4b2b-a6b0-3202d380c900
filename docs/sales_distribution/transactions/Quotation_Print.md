## ASP.NET to Django Conversion Script: Customer Quotation - Print

This document outlines a comprehensive plan to modernize the provided ASP.NET "Customer Quotation - Print" module to a robust Django application. Our approach focuses on AI-assisted automation, leveraging Django's "Fat Model, Thin View" paradigm, HTMX, Alpine.js, and DataTables for a highly efficient, responsive, and maintainable solution.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The ASP.NET code primarily interacts with data through a stored procedure `Sp_Quatation_Grid` for the main grid display and directly queries `SD_Cust_master` for customer autocomplete.

*   **Main Grid Data (from `Sp_Quatation_Grid` output and `GridView` columns):**
    *   **Inferred Table:** `TblQuotationHeader` (This is an assumed placeholder name as the exact table isn't specified in the C# `SqlDataAdapter` call, but common for "header" level data in ERPs).
    *   **Columns:**
        *   `Id`: Primary Key, Integer
        *   `FinYear`: Financial Year, Varchar/Integer
        *   `CustomerName`: Customer Name, Varchar
        *   `CustomerId`: Customer ID, Varchar/Integer (Implied Foreign Key to `SD_Cust_master`)
        *   `QuotationNo`: Quotation Number, Varchar
        *   `EnqId`: Enquiry ID, Varchar
        *   `SysDate`: System Date / Generation Date, DateTime
        *   `EmployeeName`: Employee Name / Generated By, Varchar

*   **Customer Master Data (from `sql` web method):**
    *   **Table:** `SD_Cust_master`
    *   **Columns:**
        *   `CustomerId`: Primary Key/Unique ID, Varchar/Integer
        *   `CustomerName`: Customer Name, Varchar

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**

*   **Read Operation (List View):**
    *   Displays a list of customer quotations based on search criteria.
    *   Data is retrieved via `Sp_Quatation_Grid` stored procedure.
    *   Supports pagination via `SearchGridView1_PageIndexChanging`.
*   **Search/Filtering:**
    *   Allows searching by `Customer Name`, `Enquiry No`, or `Quotation No` using a dropdown and a text input.
    *   `DropDownList1_SelectedIndexChanged` dynamically changes visibility of search textboxes.
    *   `btnSearch_Click` triggers data re-binding with applied filters.
*   **Autocomplete:**
    *   `AutoCompleteExtender` provides autocomplete functionality for `Customer Name` using `SD_Cust_master`.
*   **Navigation/Redirection:**
    *   Clicking on a `Quotation No` in the grid (`SearchGridView1_RowCommand`) redirects to `Quotation_Print_Details.aspx` with various query parameters (`CustomerId`, `EnqId`, `QuotationNo`, `Id`, `Key`, `ModId`, `SubModId`, `parentpage`).
*   **Temporary Data Handling (Not Migrated for this module):**
    *   The `fun.delete("SD_Cust_Quotation_Details_Temp", ...)` on `Page_Load` is for clearing session-specific temporary data. This functionality is likely related to the *creation* or *editing* of quotations and is not directly part of the "print/view" page's core functionality. It will not be migrated as part of this read-only module.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, inferring Django equivalents.

**Analysis:**

*   **Search Controls:**
    *   `DropDownList1` (ASP.NET DropDownList): Django `forms.ChoiceField` or `forms.Select`.
    *   `txtEnqId` (ASP.NET TextBox): Django `forms.TextInput` (for Enquiry No or Quotation No).
    *   `TxtSearchValue` (ASP.NET TextBox + `AutoCompleteExtender`): Django `forms.TextInput` combined with an HTMX/JavaScript (Alpine.js) driven autocomplete.
    *   `btnSearch` (ASP.NET Button): Standard HTML button triggering HTMX.
*   **Data Display:**
    *   `SearchGridView1` (ASP.NET GridView): HTML `<table>` with DataTables. Columns correspond to Django model fields.
    *   `lnkButton` (ASP.NET LinkButton in GridView): Standard HTML `<a>` tag or a button for navigation.
*   **Messages:**
    *   `Label2`: Django `messages` framework.
*   **Styling:**
    *   `CssClass` attributes (e.g., `box3`, `redbox`, `yui-datatable-theme`): Will be replaced by Tailwind CSS classes.

---

### Step 4: Generate Django Code

We will create a new Django app named `sales` for this module.

#### 4.1 Models (sales/models.py)

We will define two models: `Quotation` to represent the main data displayed in the grid, and `Customer` for the autocomplete feature. We'll add a custom manager to the `Quotation` model to encapsulate the search logic from `BindDataCust`.

```python
from django.db import models
from django.db.models import F, Q

class QuotationManager(models.Manager):
    """
    Custom manager for the Quotation model to encapsulate search logic.
    """
    def search_quotations(self, company_id, financial_year_id, search_type, search_value_enq_quot, search_value_customer_name, customer_code_mapper=None):
        """
        Replicates the logic from the ASP.NET BindDataCust method.
        
        Args:
            company_id (int): Current Company ID from session.
            financial_year_id (int): Current Financial Year ID from session.
            search_type (str): '0' for Customer Name, '1' for Enquiry No, '2' for Quotation No.
            search_value_enq_quot (str): Value for Enquiry No or Quotation No search.
            search_value_customer_name (str): Value for Customer Name search (e.g., 'Customer Name [Code]').
            customer_code_mapper (callable): A function to extract customer code from customer name string.
                                            Defaults to extracting code from '[Code]' format.
        """
        # Apply fixed filters from ASP.NET
        # Note: In a real system, CompId and FinYearId would likely be linked
        # to a user's context or organization. For managed=False, we pass them directly.
        # This assumes the underlying stored procedure would filter by these.
        # Since we're replacing the SP, we'll apply them as ORM filters.
        
        # Start with a base queryset (mimicking the SP output fields)
        # Note: Since the SP filters by CompId and FinYearId, we apply these here.
        # If these were fields in the model's db_table, they'd be direct filters.
        # For a truly managed=False setup, these parameters would be passed to the SP.
        # Here, we assume the model *could* have these and filter, or this logic
        # would be pushed into a database view.
        
        # For simplicity, let's assume the underlying table has these and the SP filters them
        # For this model, we're just providing a search interface.
        queryset = self.all() 
        
        # Dynamic search conditions
        if search_type == '1' and search_value_enq_quot: # Enquiry No
            queryset = queryset.filter(enq_id=search_value_enq_quot)
        elif search_type == '2' and search_value_enq_quot: # Quotation No
            queryset = queryset.filter(quotation_no=search_value_enq_quot)
        elif search_type == '0' and search_value_customer_name: # Customer Name
            # Replicate fun.getCode(TxtSearchValue.Text)
            # This implies TxtSearchValue contains "Customer Name [CustomerId]"
            # We need to extract CustomerId.
            if customer_code_mapper is None:
                def default_customer_code_mapper(customer_name_string):
                    if '[' in customer_name_string and ']' in customer_name_string:
                        return customer_name_string.split('[')[-1].replace(']', '').strip()
                    return customer_name_string # Fallback if no code format
                customer_code_mapper = default_customer_code_mapper

            customer_id_from_search = customer_code_mapper(search_value_customer_name)
            if customer_id_from_search:
                queryset = queryset.filter(customer_id=customer_id_from_search)
        
        # Order by a relevant field for consistent display, e.g., SysDate descending
        return queryset.order_by('-sys_date')

class Quotation(models.Model):
    # Mapping fields from the Sp_Quatation_Grid output / GridView columns
    # Assumed table `TblQuotationHeader`
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK
    fin_year = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    customer_id = models.CharField(db_column='CustomerId', max_length=50, blank=True, null=True) # Store as CharField matching source
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50, blank=True, null=True)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    objects = QuotationManager() # Assign custom manager

    class Meta:
        managed = False # Django will not manage this table's creation/alteration
        db_table = 'TblQuotationHeader' # Assumed table name for the quotation data
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'

    def __str__(self):
        return f"Quotation No: {self.quotation_no} for {self.customer_name}"

    # Business logic methods can go here, e.g., for calculating totals, statuses etc.
    # No specific complex logic identified from the ASP.NET code for this model itself.

class Customer(models.Model):
    # Mapping fields from SD_Cust_master
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    # Add other fields from SD_Cust_master as needed

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name
    
    # Method to format for autocomplete
    def get_autocomplete_display(self):
        return f"{self.customer_name} [{self.customer_id}]"

```

#### 4.2 Forms (sales/forms.py)

We'll define a simple form for the search criteria. This is not a ModelForm as it's purely for filtering.

```python
from django import forms

class QuotationSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('Select', 'Select'), # Mimics "Select" option in ASP.NET
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'Quotation No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-full sm:w-auto p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'})
    )
    enquiry_quotation_id = forms.CharField(
        required=False,
        label="", # Label managed by context
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full sm:w-auto p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'placeholder': 'Enter Enquiry/Quotation No',
            'x-show': "selectedType === '1' || selectedType === '2'" # Alpine.js visibility
        })
    )
    customer_search_value = forms.CharField(
        required=False,
        label="", # Label managed by context
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full sm:w-auto p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'placeholder': 'Enter Customer Name',
            'hx-get': '/sales/autocomplete-customer/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'x-show': "selectedType === '0'", # Alpine.js visibility
            'autocomplete': 'off', # Disable browser autocomplete
            'aria-expanded': 'false',
            'aria-autocomplete': 'list',
            'aria-controls': 'autocomplete-results',
        })
    )

    # Note: No direct "Search" button field in the form as it will be an HTML button
    # triggering HTMX with the form's data.

```

#### 4.3 Views (sales/views.py)

We'll define views for the main list page, the partial table rendering, dynamic search field rendering, and customer autocomplete.

```python
from django.views.generic import TemplateView, ListView, View
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming user authentication
from django.db.models import Q # For complex queries, if needed
import re # For extracting customer code from autocomplete string

from .models import Quotation, Customer
from .forms import QuotationSearchForm

# Helper to extract customer ID from "Customer Name [CustomerId]" format
def extract_customer_id(customer_name_string):
    match = re.search(r'\[(.*?)\]', customer_name_string)
    return match.group(1).strip() if match else customer_name_string

class QuotationListView(LoginRequiredMixin, TemplateView):
    """
    Main view for displaying the Quotation list page.
    Renders the search form and a container for the HTMX-loaded table.
    """
    template_name = 'sales/quotation/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        form = QuotationSearchForm(self.request.GET or None)
        context['form'] = form
        # Pass an initial message if present in query string (mimicking ASP.NET Label2)
        initial_message = self.request.GET.get('msg', '')
        if initial_message:
            context['initial_message'] = initial_message
        return context

class QuotationTablePartialView(LoginRequiredMixin, ListView):
    """
    HTMX endpoint to render the quotation list table.
    Handles search and pagination.
    """
    model = Quotation
    template_name = 'sales/quotation/_quotation_table.html'
    context_object_name = 'quotations'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        # Retrieve session context (assuming these are available, e.g., from user profile)
        # For simplicity, hardcoding for now, but in a real app, these would come from
        # request.user.profile or similar, or a lookup based on logged-in user.
        # In a managed=False scenario with an SP, these would be passed to the SP.
        # Here, we're applying them as filters if they were model fields.
        # Placeholder values:
        company_id = 1 # Example: self.request.user.company.id if applicable
        financial_year_id = 1 # Example: self.request.session.get('finyear_id')

        # Get search parameters from GET request (HTMX will send form data here)
        search_type = self.request.GET.get('search_type', 'Select')
        enquiry_quotation_id = self.request.GET.get('enquiry_quotation_id', '').strip()
        customer_search_value = self.request.GET.get('customer_search_value', '').strip()

        # Use the custom manager method to apply search logic
        queryset = Quotation.objects.search_quotations(
            company_id,
            financial_year_id,
            search_type,
            enquiry_quotation_id,
            customer_search_value,
            customer_code_mapper=extract_customer_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # DataTables doesn't need Django pagination in its JS, but for server-side, it does.
        # Since we're using client-side DataTables, just provide the full queryset.
        # The `paginate_by` is useful if DataTables were using server-side processing.
        # For simple client-side DataTables, the entire queryset might be loaded.
        # If the dataset is large, consider server-side DataTables processing via Django-Datatables-View.
        # For this exercise, we assume client-side DataTables will handle the pagination/sorting.
        context['quotations'] = self.get_queryset() # Pass all data for client-side DataTable
        return context

class SearchFilterPartialView(LoginRequiredMixin, View):
    """
    HTMX endpoint to dynamically render the search input fields
    based on the selected dropdown value.
    """
    def get(self, request, *args, **kwargs):
        selected_type = request.GET.get('search_type', 'Select')
        # We don't need to re-render the whole form, just the relevant input fields.
        # This view's primary purpose is to trigger an Alpine.js state change.
        # However, to explicitly return a partial HTML, we'd render the inputs.
        # For a clean Alpine.js solution, this view might not be necessary if Alpine
        # directly handles the `x-show` logic based on `search_type` from the form.
        # Let's provide a minimal response if needed for complex partial updates.
        
        # In our case, Alpine.js handles the visibility client-side.
        # We will just return an empty 200 OK or similar, as the form fields are already present.
        # Or, we can re-render the specific inputs if logic required it.
        # For strict partial rendering:
        form = QuotationSearchForm(request.GET) # Re-init form with current GET data
        return render(request, 'sales/quotation/_search_inputs.html', {'form': form, 'selected_type': selected_type})

class CustomerAutocompleteView(LoginRequiredMixin, View):
    """
    HTMX endpoint for customer name autocomplete.
    Mimics the ASP.NET `sql` web method.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('customer_search_value', '').strip()
        
        # Assuming company_id is relevant for customer filtering
        # company_id = request.user.company.id # Example
        # For `managed=False`, we assume the database has a way to filter customers.
        # For the original ASP.NET, it was hardcoded: CompId='" + CompId + "'
        
        # Filter customers by name starting with prefix_text, case-insensitive
        # Order by customer_name for consistent display
        customers = Customer.objects.filter(customer_name__istartswith=prefix_text).order_by('customer_name')[:10] # Limit to 10 results
        
        # Format results as "CustomerName [CustomerId]"
        results = [customer.get_autocomplete_display() for customer in customers]
        
        # HTMX will swap this into the #autocomplete-results div
        # For simple text display:
        html_results = ""
        if results:
            html_results += '<ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto mt-1">'
            for item in results:
                # When an item is clicked, populate the input field and hide results
                html_results += f'<li class="p-2 cursor-pointer hover:bg-gray-100" hx-on:click="this.closest(\'div\').querySelector(\'input[name=customer_search_value]\').value=\'{item.replace("&#39;", "\\\'")}\'; this.closest(\'div\').querySelector(\'#autocomplete-results\').innerHTML=\'\';">{{item}}</li>'
            html_results += '</ul>'
        
        return HttpResponse(html_results)

# Placeholder for the detail view that the LinkButton navigates to
class QuotationDetailView(LoginRequiredMixin, View):
    """
    This view is a placeholder for the page that ASP.NET redirected to.
    It would display the details of a specific quotation.
    """
    def get(self, request, pk, *args, **kwargs):
        # Retrieve the quotation based on primary key
        quotation = Quotation.objects.get(pk=pk) # Or get_object_or_404
        
        # Mimic the query parameters if needed for the detail page.
        # In Django, typically you'd pass the PK and the detail page
        # fetches its own data.
        
        # Instead of redirecting to a new URL with many query params,
        # Django's approach would be to render a detail template.
        
        # For this example, we'll just demonstrate fetching the object
        # and a simple render. In a real app, this would be a full detail page.
        
        context = {
            'quotation': quotation,
            'message': request.GET.get('msg', ''), # Pass message if present
            'customer_id_param': request.GET.get('CustomerId', ''),
            'enq_id_param': request.GET.get('EnqId', ''),
            'quotation_no_param': request.GET.get('QuotationNo', ''),
            # etc. for other params like Key, ModId, SubModId, parentpage
        }
        return render(request, 'sales/quotation/details.html', context)

```

#### 4.4 Templates

**Directory Structure:** `templates/sales/quotation/`

1.  **`sales/quotation/list.html`** (Main page)

    ```html
    {% extends 'core/base.html' %}

    {% block content %}
    <div class="container mx-auto px-4 py-8">
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg mb-4">
            <h2 class="text-xl font-bold">Customer Quotation - Print</h2>
        </div>

        {% if initial_message %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ initial_message }}</span>
            </div>
        {% endif %}

        <div x-data="{ selectedType: '{{ form.search_type.value|default:'Select' }}' }" class="bg-white p-6 rounded-b-lg shadow-md mb-6">
            <form hx-get="{% url 'sales:quotation_table_partial' %}" hx-target="#quotationTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:#id_search_type">
                {% csrf_token %}
                <div class="flex flex-wrap items-center gap-4 mb-4">
                    <label for="{{ form.search_type.id_for_label }}" class="font-medium text-gray-700">Search By:</label>
                    {{ form.search_type }}
                    <div class="flex-grow">
                        {{ form.enquiry_quotation_id }}
                        <div class="relative">
                            {{ form.customer_search_value }}
                            <div id="autocomplete-results" class="absolute z-10 w-full"></div>
                        </div>
                    </div>
                    <button type="submit" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Search</button>
                </div>
            </form>
        </div>

        <div id="quotationTable-container"
             hx-trigger="load, searchSuccess from:body" {# Load on page load, and after successful search/update #}
             hx-get="{% url 'sales:quotation_table_partial' %}"
             hx-swap="innerHTML">
            <!-- DataTables table will be loaded here via HTMX -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
                <p class="mt-4 text-lg text-gray-600">Loading Quotations...</p>
            </div>
        </div>
    </div>
    {% endblock %}

    {% block extra_js %}
    <script src="https://cdn.datatables.net/2.0.0/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/2.0.0/js/dataTables.tailwindcss.min.js"></script>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('quotationSearch', () => ({
                selectedType: '{{ form.search_type.value|default:'Select' }}',
                init() {
                    this.$watch('selectedType', value => {
                        // HTMX handles re-fetching the table, Alpine handles local UI state.
                        // If you have fields that need to be cleared when type changes:
                        if (value === '0') {
                            this.$refs.enqQuoId.value = '';
                        } else {
                            this.$refs.custSearchVal.value = '';
                        }
                    });
                }
            }));
        });

        // Event listener for HTMX after a swap, to re-initialize DataTables
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.target.id === 'quotationTable-container') {
                $('#quotationTable').DataTable({
                    "pageLength": 20, // Matches ASP.NET PageSize
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "ordering": true, // Allow sorting
                    "searching": true // Enable client-side search
                });
            }
        });

        // Trigger a 'searchSuccess' event from body after HTMX updates the table
        // This is a pattern for broader events if needed, but for this specific setup
        // 'load' on the hx-target container handles initial load, and 'submit' on the form
        // will handle subsequent searches.
    </script>
    {% endblock %}
    ```

2.  **`sales/quotation/_quotation_table.html`** (Partial for DataTables)

    ```html
    <div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
        <table id="quotationTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No.</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Gen Date</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if quotations %}
                    {% for obj in quotations %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ obj.fin_year|default_if_none:'' }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customer_name|default_if_none:'' }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.customer_id|default_if_none:'' }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">
                            <a href="{% url 'sales:quotation_details' pk=obj.id %}?CustomerId={{ obj.customer_id }}&EnqId={{ obj.enq_id }}&QuotationNo={{ obj.quotation_no }}&Id={{ obj.id }}&Key=some_key&ModId=2&SubModId=63&parentpage=2" 
                               class="text-blue-600 hover:text-blue-800 hover:underline">
                                {{ obj.quotation_no|default_if_none:'' }}
                            </a>
                        </td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.enq_id|default_if_none:'' }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sys_date|date:"d M Y"|default_if_none:'' }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.employee_name|default_if_none:'' }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="8" class="py-4 text-center text-lg text-maroon-600 font-semibold">
                            No data to display !
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <script>
        // DataTables initialization is handled by the htmx:afterSwap event listener in list.html
        // This ensures it runs after the partial is loaded.
    </script>
    ```

3.  **`sales/quotation/_search_inputs.html`** (Partial for dynamic search fields - can be used if Alpine.js isn't the sole method for visibility)

    This partial is a more explicit HTMX way to handle the dynamic input fields, although Alpine.js can handle `x-show` directly. For demonstration, we'll show how it *could* be used. However, the `list.html` Alpine.js approach is cleaner for this specific dynamic UI.

    ```html
    {# This partial is mainly for conceptual understanding if Alpine.js wasn't handling x-show directly #}
    {# The Alpine.js approach in list.html is more direct for this UI state #}

    {% comment %}
    <div x-data="{ selectedType: '{{ selected_type }}' }" class="flex-grow">
        <div x-show="selectedType === '1' || selectedType === '2'">
            {{ form.enquiry_quotation_id }}
        </div>
        <div x-show="selectedType === '0'" class="relative">
            {{ form.customer_search_value }}
            <div id="autocomplete-results" class="absolute z-10 w-full"></div>
        </div>
    </div>
    {% endcomment %}

    {# In our current setup, the full form is rendered once, and Alpine.js handles `x-show`. #}
    {# This partial would only be strictly necessary if the form structure itself changed #}
    {# on dropdown selection, which it doesn't in this case. #}
    ```

4.  **`sales/quotation/details.html`** (Placeholder for Quotation Details page)

    ```html
    {% extends 'core/base.html' %}

    {% block content %}
    <div class="container mx-auto px-4 py-8">
        <h2 class="text-3xl font-bold text-gray-800 mb-6">Quotation Details: {{ quotation.quotation_no }}</h2>

        {% if message %}
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ message }}</span>
            </div>
        {% endif %}

        <div class="bg-white shadow-md rounded-lg p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-gray-600"><strong>Customer Name:</strong> {{ quotation.customer_name }} ({{ quotation.customer_id }})</p>
                    <p class="text-gray-600"><strong>Enquiry No:</strong> {{ quotation.enq_id }}</p>
                    <p class="text-gray-600"><strong>Quotation No:</strong> {{ quotation.quotation_no }}</p>
                </div>
                <div>
                    <p class="text-gray-600"><strong>Financial Year:</strong> {{ quotation.fin_year }}</p>
                    <p class="text-gray-600"><strong>Generation Date:</strong> {{ quotation.sys_date|date:"d M Y h:i A" }}</p>
                    <p class="text-gray-600"><strong>Generated By:</strong> {{ quotation.employee_name }}</p>
                </div>
            </div>
        </div>

        {# Add sections for quotation items, terms, etc. if available in a real application #}
        <div class="mt-8">
            <a href="{% url 'sales:quotation_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                Back to Quotation List
            </a>
        </div>
    </div>
    {% endblock %}
    ```

#### 4.5 URLs (sales/urls.py)

```python
from django.urls import path
from .views import QuotationListView, QuotationTablePartialView, SearchFilterPartialView, CustomerAutocompleteView, QuotationDetailView

app_name = 'sales' # Namespace for the app

urlpatterns = [
    path('quotation/', QuotationListView.as_view(), name='quotation_list'),
    path('quotation/table/', QuotationTablePartialView.as_view(), name='quotation_table_partial'),
    # This path is conceptually for dynamic search inputs, but Alpine.js handles it more elegantly client-side
    # path('quotation/search-inputs/', SearchFilterPartialView.as_view(), name='quotation_search_inputs'),
    path('autocomplete-customer/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    path('quotation/details/<int:pk>/', QuotationDetailView.as_view(), name='quotation_details'),
]

```

#### 4.6 Tests (sales/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch # For mocking external dependencies like session/user context

from .models import Quotation, Customer, QuotationManager
from .forms import QuotationSearchForm
from .views import extract_customer_id

class SalesModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for both models, directly bypassing default manager if needed
        # For managed=False models, we simulate data existing in the DB.
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Customer')
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Beta Industries')

        cls.quotation1 = Quotation.objects.create(
            id=101,
            fin_year='2023-24',
            customer_name=cls.customer1.customer_name,
            customer_id=cls.customer1.customer_id,
            quotation_no='QOUT/23-24/001',
            enq_id='ENQ/23-24/001',
            sys_date=timezone.now() - timezone.timedelta(days=10),
            employee_name='John Doe'
        )
        cls.quotation2 = Quotation.objects.create(
            id=102,
            fin_year='2023-24',
            customer_name=cls.customer2.customer_name,
            customer_id=cls.customer2.customer_id,
            quotation_no='QOUT/23-24/002',
            enq_id='ENQ/23-24/002',
            sys_date=timezone.now() - timezone.timedelta(days=5),
            employee_name='Jane Smith'
        )
        cls.quotation3 = Quotation.objects.create(
            id=103,
            fin_year='2023-24',
            customer_name=cls.customer1.customer_name,
            customer_id=cls.customer1.customer_id,
            quotation_no='QOUT/23-24/003',
            enq_id='ENQ/23-24/003',
            sys_date=timezone.now() - timezone.timedelta(days=1),
            employee_name='John Doe'
        )

    def test_quotation_creation(self):
        self.assertEqual(self.quotation1.customer_name, 'Alpha Customer')
        self.assertEqual(self.quotation2.quotation_no, 'QOUT/23-24/002')

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customer_name, 'Alpha Customer')
        self.assertEqual(self.customer1.customer_id, 'CUST001')

    def test_customer_autocomplete_display(self):
        self.assertEqual(self.customer1.get_autocomplete_display(), 'Alpha Customer [CUST001]')

    def test_quotation_manager_search_by_enquiry_no(self):
        qs = Quotation.objects.search_quotations(
            company_id=1, financial_year_id=1, search_type='1',
            search_value_enq_quot='ENQ/23-24/001', search_value_customer_name=''
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().quotation_no, 'QOUT/23-24/001')

    def test_quotation_manager_search_by_quotation_no(self):
        qs = Quotation.objects.search_quotations(
            company_id=1, financial_year_id=1, search_type='2',
            search_value_enq_quot='QOUT/23-24/002', search_value_customer_name=''
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().enq_id, 'ENQ/23-24/002')

    def test_quotation_manager_search_by_customer_name(self):
        qs = Quotation.objects.search_quotations(
            company_id=1, financial_year_id=1, search_type='0',
            search_value_enq_quot='', search_value_customer_name='Alpha Customer [CUST001]'
        )
        self.assertEqual(qs.count(), 2) # Both quotation1 and quotation3 are for Alpha Customer
        self.assertIn(self.quotation1, qs)
        self.assertIn(self.quotation3, qs)

    def test_quotation_manager_no_search_parameters(self):
        qs = Quotation.objects.search_quotations(
            company_id=1, financial_year_id=1, search_type='Select',
            search_value_enq_quot='', search_value_customer_name=''
        )
        self.assertEqual(qs.count(), 3) # All quotations
    
    def test_extract_customer_id_utility(self):
        self.assertEqual(extract_customer_id('Customer Name [CODE123]'), 'CODE123')
        self.assertEqual(extract_customer_id('No Code Here'), 'No Code Here')
        self.assertEqual(extract_customer_id('Another [ XYZ ] Test'), 'XYZ')


class SalesViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Customer')
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Beta Industries')

        cls.quotation1 = Quotation.objects.create(
            id=101,
            fin_year='2023-24',
            customer_name=cls.customer1.customer_name,
            customer_id=cls.customer1.customer_id,
            quotation_no='QOUT/23-24/001',
            enq_id='ENQ/23-24/001',
            sys_date=timezone.now() - timezone.timedelta(days=10),
            employee_name='John Doe'
        )
        cls.quotation2 = Quotation.objects.create(
            id=102,
            fin_year='2023-24',
            customer_name=cls.customer2.customer_name,
            customer_id=cls.customer2.customer_id,
            quotation_no='QOUT/23-24/002',
            enq_id='ENQ/23-24/002',
            sys_date=timezone.now() - timezone.timedelta(days=5),
            employee_name='Jane Smith'
        )

    def setUp(self):
        self.client = Client()
        # Mocking user login for LoginRequiredMixin
        self.user = self.client.login(username='testuser', password='password') # Assume a user is created
        # A simple way to mock login:
        # from django.contrib.auth.models import User
        # self.user = User.objects.create_user(username='testuser', password='password')
        # self.client.force_login(self.user)

    def test_quotation_list_view_get(self):
        url = reverse('sales:quotation_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/list.html')
        self.assertIsInstance(response.context['form'], QuotationSearchForm)
        self.assertContains(response, 'Customer Quotation - Print')
        # Check initial message handling
        response_with_msg = self.client.get(f"{url}?msg=Test+Message")
        self.assertContains(response_with_msg, 'Test Message')


    def test_quotation_table_partial_view_get(self):
        url = reverse('sales:quotation_table_partial')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/_quotation_table.html')
        self.assertIn('quotations', response.context)
        self.assertEqual(response.context['quotations'].count(), 2) # Both test quotations

    def test_quotation_table_partial_view_search_enquiry_no(self):
        url = reverse('sales:quotation_table_partial')
        response = self.client.get(f"{url}?search_type=1&enquiry_quotation_id={self.quotation1.enq_id}")
        self.assertEqual(response.status_code, 200)
        self.assertIn('quotations', response.context)
        self.assertEqual(response.context['quotations'].count(), 1)
        self.assertEqual(response.context['quotations'].first().id, self.quotation1.id)
        self.assertContains(response, self.quotation1.enq_id)
        self.assertNotContains(response, self.quotation2.enq_id) # Should not contain second quotation's enquiry ID

    def test_quotation_table_partial_view_search_customer_name(self):
        url = reverse('sales:quotation_table_partial')
        response = self.client.get(f"{url}?search_type=0&customer_search_value={self.customer1.get_autocomplete_display()}")
        self.assertEqual(response.status_code, 200)
        self.assertIn('quotations', response.context)
        self.assertEqual(response.context['quotations'].count(), 1) # only q1
        self.assertEqual(response.context['quotations'].first().id, self.quotation1.id)
        self.assertContains(response, self.customer1.customer_name)
        self.assertNotContains(response, self.customer2.customer_name) # Should not contain second customer's name


    def test_customer_autocomplete_view(self):
        url = reverse('sales:customer_autocomplete')
        response = self.client.get(f"{url}?customer_search_value=Alpha")
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Customer [CUST001]')
        self.assertNotContains(response, 'Beta Industries [CUST002]')

        response_all = self.client.get(f"{url}?customer_search_value=")
        self.assertEqual(response_all.status_code, 200)
        self.assertContains(response_all, 'Alpha Customer [CUST001]')
        self.assertContains(response_all, 'Beta Industries [CUST002]')

    def test_quotation_detail_view(self):
        url = reverse('sales:quotation_details', args=[self.quotation1.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/details.html')
        self.assertContains(response, self.quotation1.quotation_no)
        self.assertContains(response, self.quotation1.customer_name)
        
        # Test with query parameters (though Django typically prefers clean URLs)
        url_with_params = f"{url}?CustomerId={self.quotation1.customer_id}&EnqId={self.quotation1.enq_id}&msg=Detail%20Page"
        response_params = self.client.get(url_with_params)
        self.assertEqual(response_params.status_code, 200)
        self.assertContains(response_params, 'Detail Page')
        self.assertEqual(response_params.context['customer_id_param'], self.quotation1.customer_id)
        self.assertEqual(response_params.context['enq_id_param'], self.quotation1.enq_id)
```

---

### Step 5: HTMX and Alpine.js Integration

The templates and views are designed for seamless HTMX and Alpine.js integration:

*   **HTMX for dynamic content:**
    *   The `quotationTable-container` div in `list.html` uses `hx-get` to fetch `{% url 'sales:quotation_table_partial' %}` on `load` and `searchSuccess`. This ensures the table is populated dynamically without a full page reload.
    *   The search `form` uses `hx-get` to trigger a re-fetch of the table, sending its data as query parameters. `hx-trigger="submit, change from:#id_search_type"` ensures a new table is loaded when the form is submitted (e.g., via the Search button) or when the dropdown selection changes.
    *   The `customer_search_value` input uses `hx-get` to `{% url 'sales:customer_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms, search"` to provide real-time autocomplete suggestions, which are swapped into the `autocomplete-results` div. Clicks on autocomplete suggestions update the input value and clear the suggestions via `hx-on:click` in the HTML.
*   **Alpine.js for UI state management:**
    *   The `div` wrapping the search form in `list.html` uses `x-data="{ selectedType: '...' }"` to manage the state of the `search_type` dropdown.
    *   `x-show` directives on `enquiry_quotation_id` and `customer_search_value` fields control their visibility based on `selectedType`, mimicking the ASP.NET `Visible` property.
*   **DataTables for list views:**
    *   The `_quotation_table.html` partial contains a `<table>` with `id="quotationTable"`.
    *   A JavaScript block in `list.html` listens for the `htmx:afterSwap` event on the `quotationTable-container`. Once the table partial is loaded, it initializes DataTables on `#quotationTable`, providing client-side searching, sorting, and pagination.
*   **No custom JavaScript requirements:** Apart from CDN links for HTMX, Alpine.js, jQuery, and DataTables, no custom JavaScript files are needed for the core functionality. All dynamic interactions are declarative using HTMX attributes or simple Alpine.js directives.
*   **CRUD Operations:** While this module is primarily "Read" and "Search", the pattern established (HTMX partials, modals for forms) can be extended for Create, Update, and Delete operations using `hx-post` and `hx-target` to load forms into modals and trigger list refreshes. (As per system instructions for general CRUD examples, these would be in the model's module, but this specific ASP.NET page is just a print view).

### Final Notes

*   **Placeholders:** `CompId`, `FinYearId`, and `Session` variables from ASP.NET would typically be handled by Django's authentication system and potentially a `request.user.profile` or global context in a real application. For `managed=False` models interacting with existing database tables, these would be passed to the stored procedures or applied as ORM filters if the underlying table has these columns.
*   **DRY Principles:** Templates are kept DRY by using partials (e.g., `_quotation_table.html`). The base template `core/base.html` (not included here) handles all common elements like CDN links for HTMX, Alpine.js, jQuery, and Tailwind CSS.
*   **Business Logic:** The `QuotationManager` class now encapsulates the complex search logic from `BindDataCust`, adhering to the "Fat Model, Thin View" principle. Views are kept minimal, primarily orchestrating data retrieval and rendering.
*   **Test Coverage:** Comprehensive unit tests for models and integration tests for views ensure high quality and prevent regressions during ongoing development.

This modernization plan provides a clear, automated path to transform the legacy ASP.NET "Customer Quotation - Print" module into a modern, efficient, and maintainable Django application.