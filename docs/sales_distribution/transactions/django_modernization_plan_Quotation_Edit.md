## ASP.NET to Django Conversion Script:

This modernization plan outlines the strategic transition of your existing ASP.NET application, specifically the "Customer Quotation - Edit" functionality, to a robust and modern Django 5.0+ solution. We prioritize automation-driven approaches, ensuring that the process is streamlined, efficient, and delivers significant business value by leveraging Django's powerful features alongside modern frontend technologies like HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`quotations`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code primarily interacts with a stored procedure `Sp_Quatation_Grid` for displaying quotation data and directly queries `SD_Cust_master` for customer autocomplete. Based on the `GridView` columns and method parameters, we infer the following tables:

**1. `SD_Cust_Quotation` (Inferred Main Quotation Table)**
This table is the likely source for the `Sp_Quatation_Grid` stored procedure's output columns.
- `Id` (Primary Key, Integer)
- `CompId` (Company ID, Integer - inferred from `@CompId` parameter to SP)
- `FinYear` (Financial Year, Integer - inferred from `@FinId` parameter to SP and `FinYear` column)
- `CustomerId` (Customer ID, String/Varchar - used for linking to customer master, and passed as parameter)
- `CustomerName` (Customer Name, String/Varchar - likely denormalized or joined in SP)
- `QuotationNo` (Quotation Number, String/Varchar)
- `EnqId` (Enquiry ID, String/Varchar - nullable)
- `SysDate` (System Date/Generation Date, Date)
- `EmployeeName` (Employee Name who generated, String/Varchar)

**2. `SD_Cust_master` (Customer Master Table)**
This table is explicitly queried for customer autocomplete functionality.
- `CustomerId` (Primary Key, String/Varchar)
- `CustomerName` (Customer Name, String/Varchar)
- `CompId` (Company ID, Integer - inferred from `CompId` filter in `sql` method)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations (CRUD, search, etc.) handled by the ASP.NET code.

**Analysis:**
The provided ASP.NET page `Quotation_Edit.aspx` is primarily a **read (list/search)** page. It allows users to:

- **Search/Filter Quotations:** Based on "Customer Name", "Enquiry No", or "Quotation No".
    - "Customer Name" search uses an autocomplete feature and requires deriving `CustomerId` from the full customer name string.
    - "Enquiry No" and "Quotation No" searches use a direct text input.
- **Display Quotation List:** Shows a paginated and sortable grid (`GridView`) of quotations.
- **Navigate to Details:** Each `QuotationNo` in the grid is a hyperlink leading to `Quotation_Edit_Details.aspx`, implying a separate "edit/details" page for a specific quotation.
- **Autocomplete Service:** Provides real-time suggestions for customer names.
- **Session Management:** The `Page_Load` event includes a `DELETE` operation on `SD_Cust_Quotation_Details_Temp` based on `SessionId` and `CompId`. This suggests temporary data handling, likely for a multi-step form or session-specific details. For this modernization, we will focus on the main listing/search functionality and note that such temporary data management would be redesigned using Django's session/cache or proper form workflows.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in the user interface.

**Analysis:**

- **Search Filters:**
    - `asp:DropDownList ID="DropDownList1"`: Selector for search criteria (Customer Name, Enquiry No, Quotation No).
    - `asp:TextBox ID="txtEnqId"`: Text input for Enquiry No or Quotation No. Its visibility is toggled by the dropdown.
    - `asp:TextBox ID="TxtSearchValue"`: Text input for Customer Name search. This is integrated with an `AjaxControlToolkit:AutoCompleteExtender`. Its visibility is toggled by the dropdown.
    - `asp:Button ID="btnSearch"`: Triggers the search operation.
- **Data Display:**
    - `asp:GridView ID="SearchGridView1"`: The main component displaying the list of quotations. It supports pagination, sorting, and includes specific columns for `FinYear`, `CustomerName`, `CustomerId`, `QuotationNo` (hyperlink), `EnqId`, `SysDate`, `EmployeeName`.
- **Messages:**
    - `asp:Label ID="Label2"`: Used to display informational or error messages, often populated from query string parameters.

### Step 4: Generate Django Code

We will structure the Django application under a new app named `quotations`.

#### 4.1 Models

We will create two models: `Customer` for the master customer data and `CustomerQuotation` for the main quotation details, mapping them to the inferred database tables.

**File: `quotations/models.py`**

```python
from django.db import models

class Customer(models.Model):
    """
    Maps to the SD_Cust_master table, used for customer lookup and autocomplete.
    """
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True) # Assuming CompId exists in SD_Cust_master

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

    @classmethod
    def get_customer_id_from_autocomplete_text(cls, customer_name_with_id_text):
        """
        Extracts CustomerId from autocomplete text like "CustomerName [CustomerId]".
        This mimics the fun.getCode() behavior.
        """
        if '[' in customer_name_with_id_text and ']' in customer_name_with_id_text:
            try:
                start_index = customer_name_with_id_text.rfind('[') + 1
                end_index = customer_name_with_id_text.rfind(']')
                return customer_name_with_id_text[start_index:end_index]
            except ValueError:
                pass # Fall through if parsing fails
        return None # Return None if not in expected format or parsing fails


class CustomerQuotation(models.Model):
    """
    Maps to the SD_Cust_Quotation table (inferred), representing customer quotations.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True) # Added based on SP parameter @CompId
    fin_year = models.IntegerField(db_column='FinYear')
    customer_id = models.CharField(db_column='CustomerId', max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50)
    enquiry_id = models.CharField(db_column='EnqId', max_length=50, null=True, blank=True)
    sys_date = models.DateField(db_column='SysDate') # Assuming DateField for 'Gen Date'
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'SD_Cust_Quotation'
        verbose_name = 'Customer Quotation'
        verbose_name_plural = 'Customer Quotations'

    def __str__(self):
        return self.quotation_no

    @classmethod
    def get_filtered_quotations(cls, user_comp_id, user_fin_year_id, search_type, search_value, enquiry_id_value):
        """
        Filters CustomerQuotations based on provided search parameters and user context.
        This method encapsulates the search logic from the ASP.NET BindDataCust.
        """
        queryset = cls.objects.filter(comp_id=user_comp_id, fin_year=user_fin_year_id)

        if search_type == '0':  # Customer Name search
            if search_value:
                customer_id_from_name = Customer.get_customer_id_from_autocomplete_text(search_value)
                if customer_id_from_name:
                    queryset = queryset.filter(customer_id=customer_id_from_name)
                else:
                    queryset = queryset.none()  # No match found if customer ID cannot be derived
        elif search_type == '1':  # Enquiry No search
            if enquiry_id_value:
                queryset = queryset.filter(enquiry_id__icontains=enquiry_id_value) # Using icontains for flexible search
        elif search_type == '2':  # Quotation No search
            if enquiry_id_value:
                queryset = queryset.filter(quotation_no__icontains=enquiry_id_value) # Using icontains

        return queryset.order_by('-sys_date', 'quotation_no') # Order by newest date, then quotation number

    def get_absolute_url(self):
        """
        Generates the URL for the quotation details page, mimicking the HyperLinkField.
        This would link to the Django equivalent of Quotation_Edit_Details.aspx.
        """
        from django.urls import reverse
        # Adjust 'quotations:customerquotation_detail' based on your actual detail view name and parameters
        return reverse('quotations:customerquotation_detail', kwargs={
            'pk': self.id,
            'customer_id': self.customer_id,
            'quotation_no': self.quotation_no,
            'enq_id': self.enquiry_id or 'N/A' # Handle potential null values for URL safety
        })

```

#### 4.2 Forms

This specific ASP.NET page is for listing/searching, not direct editing. However, the requirement asks for a form for `[MODEL_NAME]`. We'll provide a placeholder form for `CustomerQuotation` that would typically be used on the `Quotation_Edit_Details` page. The search functionality does not require a Django form, as HTMX will directly submit raw input values.

**File: `quotations/forms.py`**

```python
from django import forms
from .models import CustomerQuotation

class CustomerQuotationForm(forms.ModelForm):
    """
    A ModelForm for CustomerQuotation, intended for use in an 'Add/Edit' view.
    This form is illustrative as the current ASP.NET page is for listing/searching.
    """
    class Meta:
        model = CustomerQuotation
        # List all fields that would typically be editable on a detail page
        fields = [
            'fin_year', 'customer_id', 'customer_name',
            'quotation_no', 'enquiry_id', 'sys_date', 'employee_name'
        ]
        widgets = {
            'fin_year': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quotation_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enquiry_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    def clean_quotation_no(self):
        # Example custom validation: ensure quotation number is unique (if required)
        quotation_no = self.cleaned_data['quotation_no']
        if self.instance.pk is None and CustomerQuotation.objects.filter(quotation_no=quotation_no).exists():
            raise forms.ValidationError("This quotation number already exists.")
        return quotation_no
```

#### 4.3 Views

We will implement a `ListView` for the main page, a partial view for the DataTables content (which will be dynamically loaded via HTMX), and a view for the autocomplete functionality. Standard CRUD views are also provided as per the prompt's template.

**File: `quotations/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q # For flexible search if needed

from .models import CustomerQuotation, Customer
from .forms import CustomerQuotationForm

# --- Main Customer Quotation List/Search View ---
class CustomerQuotationListView(ListView):
    """
    Displays the main Customer Quotation search and list page.
    This view renders the initial HTML structure.
    """
    model = CustomerQuotation
    template_name = 'quotations/customerquotation/list.html'
    context_object_name = 'customerquotations' # Will be used by the _table.html partial

    def get_queryset(self):
        # The initial page load will not have search parameters.
        # HTMX will handle subsequent searches to the partial table view.
        # Provide an empty queryset or a default set on initial load.
        # For simplicity, returning an empty queryset initially, HTMX will load data.
        return self.model.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass any initial messages from the ASP.NET query string
        msg = self.request.GET.get('msg')
        if msg:
            messages.info(self.request, msg)
        return context


class CustomerQuotationTablePartialView(ListView):
    """
    Renders only the table data for Customer Quotations.
    This view is designed to be loaded via HTMX.
    It encapsulates the search logic from ASP.NET's BindDataCust.
    """
    model = CustomerQuotation
    template_name = 'quotations/customerquotation/_table.html'
    context_object_name = 'customerquotations'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Access user context (CompId, FinYearId) from session or request if available
        # In a real system, this would come from an authenticated user session
        user_comp_id = self.request.session.get('compid', 1) # Default to 1 if not found
        user_fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not found

        search_type = self.request.GET.get('search_type', 'Select') # DropDownList1.SelectedValue
        txt_search_value = self.request.GET.get('txt_search_value', '') # TxtSearchValue.Text
        txt_enq_id = self.request.GET.get('txt_enq_id', '') # txtEnqId.Text

        queryset = self.model.get_filtered_quotations(
            user_comp_id=user_comp_id,
            user_fin_year_id=user_fin_year_id,
            search_type=search_type,
            search_value=txt_search_value,
            enquiry_id_value=txt_enq_id
        )
        return queryset


class CustomerAutocompleteView(View):
    """
    Provides autocomplete suggestions for customer names, mimicking the WebMethod 'sql'.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '') # 'term' is a common parameter for autocomplete
        # Access user context for filtering, e.g., company ID
        user_comp_id = request.session.get('compid', 1) # Default to 1 if not found

        # Filter customers based on prefix_text and user_comp_id
        customers = Customer.objects.filter(
            Q(customer_name__icontains=prefix_text) | Q(customer_id__icontains=prefix_text),
            comp_id=user_comp_id
        ).order_by('customer_name')[:10] # Limit to 10 results, as per common autocomplete practice

        results = []
        for customer in customers:
            results.append(f"{customer.customer_name} [{customer.customer_id}]")

        return JsonResponse(results, safe=False)


# --- Generic CRUD Views (as per template, for future use with details page) ---
class CustomerQuotationCreateView(CreateView):
    model = CustomerQuotation
    form_class = CustomerQuotationForm
    template_name = 'quotations/customerquotation/form.html'
    success_url = reverse_lazy('quotations:customerquotation_list')

    def form_valid(self, form):
        # In a real scenario, default values like comp_id, fin_year, sys_date, employee_name
        # would be set here, e.g., from session or request.user.
        # For demonstration, setting dummy values
        # Assume these come from context or logged-in user
        form.instance.comp_id = self.request.session.get('compid', 1)
        form.instance.fin_year = self.request.session.get('finyear', 1)
        form.instance.sys_date = form.instance.sys_date or models.DateField().auto_now_add # ensure date is set if not provided by form
        form.instance.employee_name = self.request.session.get('username', 'System User') # Or from request.user

        response = super().form_valid(form)
        messages.success(self.request, 'Customer Quotation added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX without reloading
                headers={'HX-Trigger': 'refreshCustomerQuotationList'} # Custom event to trigger list refresh
            )
        return response

class CustomerQuotationUpdateView(UpdateView):
    model = CustomerQuotation
    form_class = CustomerQuotationForm
    template_name = 'quotations/customerquotation/form.html'
    success_url = reverse_lazy('quotations:customerquotation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Quotation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshCustomerQuotationList'}
            )
        return response

class CustomerQuotationDeleteView(DeleteView):
    model = CustomerQuotation
    template_name = 'quotations/customerquotation/confirm_delete.html'
    success_url = reverse_lazy('quotations:customerquotation_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Quotation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshCustomerQuotationList'}
            )
        return response

# A placeholder for the Quotation_Edit_Details.aspx equivalent.
# This would be a more complex view for managing the actual quotation details.
class CustomerQuotationDetailView(View):
    def get(self, request, pk, customer_id, quotation_no, enq_id):
        # In a real application, fetch the CustomerQuotation object and its related details
        # and render a complex template for editing.
        # For now, it's just a placeholder to satisfy the URL link from the list.
        quotation = CustomerQuotation.objects.filter(id=pk).first()
        return HttpResponse(f"<h1>Quotation Details for {quotation_no} (ID: {pk})</h1><p>Customer: {customer_id}, Enquiry: {enq_id}</p><p>This page would allow detailed editing of the quotation.</p>")

```

#### 4.4 Templates

We will create the main list template, a partial template for the dynamic table content, and partials for the CRUD forms (add/edit/delete modals).

**File: `quotations/templates/quotations/customerquotation/list.html`**

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Customer Quotation - Edit</h2>

    <!-- Search/Filter Section -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <div class="flex flex-wrap items-center space-x-4 mb-4" x-data="{ searchType: 'Select', enqIdValue: '', searchValue: '' }">
            <label for="id_search_type" class="sr-only">Search By:</label>
            <select id="id_search_type" name="search_type"
                    x-model="searchType"
                    @change="
                        if (searchType === '0') {
                            $refs.txtSearchValue.style.display = 'block';
                            $refs.txtEnqId.style.display = 'none';
                            searchValue = '';
                        } else {
                            $refs.txtSearchValue.style.display = 'none';
                            $refs.txtEnqId.style.display = 'block';
                            enqIdValue = '';
                        }
                    "
                    class="box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm w-48">
                <option value="Select">Select</option>
                <option value="0">Customer Name</option>
                <option value="1">Enquiry No</option>
                <option value="2">Quotation No</option>
            </select>

            <input type="text" id="id_txt_enq_id" name="txt_enq_id" x-model="enqIdValue"
                   class="box3 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm w-40"
                   x-ref="txtEnqId" x-show="searchType === '1' || searchType === '2' || searchType === 'Select'" placeholder="Enquiry / Quotation No." />

            <input type="text" id="id_txt_search_value" name="txt_search_value" x-model="searchValue"
                   class="box3 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm w-80"
                   x-ref="txtSearchValue" x-show="searchType === '0'" placeholder="Customer Name (start typing for suggestions)"
                   hx-get="{% url 'quotations:customer_autocomplete' %}"
                   hx-trigger="keyup changed delay:500ms, search"
                   hx-target="#autocomplete-results"
                   hx-swap="innerHTML"
                   autocomplete="off" />
            <div id="autocomplete-results" class="relative w-80"></div>
            
            <button type="button"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox"
                    hx-get="{% url 'quotations:customerquotation_table' %}"
                    hx-target="#customerQuotationTable-container"
                    hx-swap="innerHTML"
                    hx-indicator="#loading-indicator"
                    hx-include="#id_search_type, #id_txt_enq_id, #id_txt_search_value">
                Search
            </button>
        </div>
        <div id="loading-indicator" class="htmx-indicator text-center py-2 hidden">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-1 text-sm text-gray-600">Loading data...</p>
        </div>
        <!-- Display messages from Django messages framework -->
        {% if messages %}
            {% for message in messages %}
                <div class="p-3 mb-4 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Data Table Section -->
    <div id="customerQuotationTable-container"
         hx-trigger="load, refreshCustomerQuotationList from:body"
         hx-get="{% url 'quotations:customerquotation_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#loading-indicator">
        <!-- Initial loading state -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading customer quotations...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         x-data="{ isOpen: false }" x-show="isOpen" @modal-open.window="isOpen = true" @modal-close.window="isOpen = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @click.away="$dispatch('modal-close')" x-show="isOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('customerQuotationList', () => ({
            // No specific Alpine state needed for this list page beyond x-data on searchType etc.
            // All interactions are HTMX driven.
        }));
    });

    // Helper to initialize DataTable after HTMX swap
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'customerQuotationTable-container') {
            $('#customerQuotationTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "ordering": true,
                "paging": true,
                "searching": true,
                "info": true
            });
        }
        // Handle autocomplete results selection
        if (event.target.id === 'autocomplete-results') {
            document.querySelectorAll('#autocomplete-results div').forEach(item => {
                item.onclick = function() {
                    document.getElementById('id_txt_search_value').value = this.textContent;
                    document.getElementById('autocomplete-results').innerHTML = ''; // Clear results
                };
            });
        }
    });

    // Handle modal visibility using Alpine.js and HTMX
    document.addEventListener('htmx:afterOnLoad', function(event) {
        if (event.detail.elt.hasAttribute('hx-target') && event.detail.elt.getAttribute('hx-target') === '#modalContent') {
            document.getElementById('modal').classList.add('is-active');
            window.dispatchEvent(new CustomEvent('modal-open'));
        }
    });

    document.addEventListener('htmx:afterRequest', function(event) {
        // If HTMX returns a 204 (no content) and has HX-Trigger, close modal
        if (event.detail.xhr.status === 204 && event.detail.target.id === 'modalContent') {
            window.dispatchEvent(new CustomEvent('modal-close'));
        }
    });

    // Autocomplete results handling
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'autocomplete-results') {
            // Add click listener to select an autocomplete item
            document.querySelectorAll('#autocomplete-results > div').forEach(item => {
                item.addEventListener('click', function() {
                    document.getElementById('id_txt_search_value').value = this.textContent;
                    document.getElementById('autocomplete-results').innerHTML = ''; // Clear results after selection
                });
            });
        }
    });
</script>
{% endblock %}
```

**File: `quotations/templates/quotations/customerquotation/_table.html`**

```html
<table id="customerQuotationTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if customerquotations %}
            {% for obj in customerquotations %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customer_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.customer_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <a href="{{ obj.get_absolute_url }}" class="text-blue-600 hover:underline">{{ obj.quotation_no }}</a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.enquiry_id|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sys_date|date:"d M Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.employee_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center whitespace-nowrap">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'quotations:customerquotation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click send modal-open to window">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'quotations:customerquotation_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click send modal-open to window">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="9" class="py-4 px-4 text-center text-lg font-semibold text-red-700">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>
```

**File: `quotations/templates/quotations/customerquotation/_autocomplete_results.html`**

```html
{% if results %}
<div class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1">
    {% for result in results %}
    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm" hx-on:click="document.getElementById('id_txt_search_value').value = '{{ result }}'; document.getElementById('autocomplete-results').innerHTML = '';">
        {{ result }}
    </div>
    {% endfor %}
</div>
{% endif %}
```

**File: `quotations/templates/quotations/customerquotation/form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Quotation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) window.dispatchEvent(new CustomEvent('modal-close'));">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click send modal-close to window">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `quotations/templates/quotations/customerquotation/confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete quotation "<strong>{{ object.quotation_no }}</strong>"? This action cannot be undone.</p>

    <form hx-post="{% url 'quotations:customerquotation_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) window.dispatchEvent(new CustomEvent('modal-close'));">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click send modal-close to window">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

URL patterns for the `quotations` application.

**File: `quotations/urls.py`**

```python
from django.urls import path
from .views import (
    CustomerQuotationListView,
    CustomerQuotationTablePartialView,
    CustomerAutocompleteView,
    CustomerQuotationCreateView,
    CustomerQuotationUpdateView,
    CustomerQuotationDeleteView,
    CustomerQuotationDetailView,
)

app_name = 'quotations' # Namespace for the application

urlpatterns = [
    # Main list and search page
    path('customerquotation/', CustomerQuotationListView.as_view(), name='customerquotation_list'),
    # HTMX endpoint for dynamically loading the table content
    path('customerquotation/table/', CustomerQuotationTablePartialView.as_view(), name='customerquotation_table'),
    # HTMX endpoint for customer autocomplete
    path('customer/autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # CRUD operations (for the "details" page, assuming modal usage here)
    path('customerquotation/add/', CustomerQuotationCreateView.as_view(), name='customerquotation_add'),
    path('customerquotation/edit/<int:pk>/', CustomerQuotationUpdateView.as_view(), name='customerquotation_edit'),
    path('customerquotation/delete/<int:pk>/', CustomerQuotationDeleteView.as_view(), name='customerquotation_delete'),

    # Placeholder for the Quotation_Edit_Details.aspx equivalent
    # Example URL structure, adjust as per actual details page needs
    path('customerquotation/details/<int:pk>/<str:customer_id>/<str:quotation_no>/<str:enq_id>/',
         CustomerQuotationDetailView.as_view(),
         name='customerquotation_detail'),
]
```

Remember to include these URLs in your project's main `urls.py`:
`path('sales/', include('quotations.urls')),` or `path('', include('quotations.urls')),` depending on your overall URL structure.

#### 4.6 Tests

Comprehensive unit tests for models and integration tests for views are crucial for ensuring the migrated application's correctness and reliability.

**File: `quotations/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Customer, CustomerQuotation
from datetime import date

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for Customer model
        Customer.objects.create(
            customer_id='CUST001',
            customer_name='Test Customer One',
            comp_id=1
        )
        Customer.objects.create(
            customer_id='CUST002',
            customer_name='Another Customer',
            comp_id=1
        )
        Customer.objects.create(
            customer_id='CUST003',
            customer_name='Third Customer',
            comp_id=2 # Different company
        )

    def test_customer_creation(self):
        customer = Customer.objects.get(customer_id='CUST001')
        self.assertEqual(customer.customer_name, 'Test Customer One')
        self.assertEqual(customer.comp_id, 1)

    def test_str_method(self):
        customer = Customer.objects.get(customer_id='CUST001')
        self.assertEqual(str(customer), 'Test Customer One [CUST001]')

    def test_get_customer_id_from_autocomplete_text(self):
        # Test valid parsing
        self.assertEqual(Customer.get_customer_id_from_autocomplete_text('Test Customer One [CUST001]'), 'CUST001')
        self.assertEqual(Customer.get_customer_id_from_autocomplete_text('Another Customer [CUST002]'), 'CUST002')
        # Test invalid formats
        self.assertIsNone(Customer.get_customer_id_from_autocomplete_text('Test Customer One'))
        self.assertIsNone(Customer.get_customer_id_from_autocomplete_text('Test Customer One [CUST001'))
        self.assertIsNone(Customer.get_customer_id_from_autocomplete_text('Test Customer One CUST001]'))
        self.assertIsNone(Customer.get_customer_id_from_autocomplete_text(''))

class CustomerQuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for CustomerQuotation model
        CustomerQuotation.objects.create(
            id=1, comp_id=1, fin_year=2023, customer_id='CUST001',
            customer_name='Test Customer One', quotation_no='Q001', enquiry_id='E001',
            sys_date=date(2023, 1, 15), employee_name='Employee A'
        )
        CustomerQuotation.objects.create(
            id=2, comp_id=1, fin_year=2023, customer_id='CUST002',
            customer_name='Another Customer', quotation_no='Q002', enquiry_id='E002',
            sys_date=date(2023, 1, 20), employee_name='Employee B'
        )
        CustomerQuotation.objects.create(
            id=3, comp_id=1, fin_year=2024, customer_id='CUST001',
            customer_name='Test Customer One', quotation_no='Q003', enquiry_id='E003',
            sys_date=date(2024, 2, 10), employee_name='Employee A'
        )
        CustomerQuotation.objects.create(
            id=4, comp_id=2, fin_year=2023, customer_id='CUST003', # Different company
            customer_name='Third Customer', quotation_no='Q004', enquiry_id='E004',
            sys_date=date(2023, 3, 1), employee_name='Employee C'
        )

    def test_quotation_creation(self):
        quotation = CustomerQuotation.objects.get(id=1)
        self.assertEqual(quotation.quotation_no, 'Q001')
        self.assertEqual(quotation.customer_id, 'CUST001')
        self.assertEqual(quotation.fin_year, 2023)

    def test_str_method(self):
        quotation = CustomerQuotation.objects.get(id=1)
        self.assertEqual(str(quotation), 'Q001')

    def test_get_filtered_quotations_no_filter(self):
        # Setup session for the test
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023

        quotations = CustomerQuotation.get_filtered_quotations(
            user_comp_id=self.client.session['compid'],
            user_fin_year_id=self.client.session['finyear'],
            search_type='Select', search_value='', enquiry_id_value=''
        )
        self.assertEqual(quotations.count(), 2) # Q001, Q002

    def test_get_filtered_quotations_by_customer_name(self):
        Customer.objects.create(customer_id='CUST001', customer_name='Test Customer One', comp_id=1) # Ensure customer exists
        quotations = CustomerQuotation.get_filtered_quotations(
            user_comp_id=1, user_fin_year_id=2023,
            search_type='0', search_value='Test Customer One [CUST001]', enquiry_id_value=''
        )
        self.assertEqual(quotations.count(), 1)
        self.assertEqual(quotations.first().quotation_no, 'Q001')

    def test_get_filtered_quotations_by_enquiry_id(self):
        quotations = CustomerQuotation.get_filtered_quotations(
            user_comp_id=1, user_fin_year_id=2023,
            search_type='1', search_value='', enquiry_id_value='E002'
        )
        self.assertEqual(quotations.count(), 1)
        self.assertEqual(quotations.first().quotation_no, 'Q002')

    def test_get_filtered_quotations_by_quotation_no(self):
        quotations = CustomerQuotation.get_filtered_quotations(
            user_comp_id=1, user_fin_year_id=2023,
            search_type='2', search_value='', enquiry_id_value='Q001'
        )
        self.assertEqual(quotations.count(), 1)
        self.assertEqual(quotations.first().quotation_no, 'Q001')

    def test_get_filtered_quotations_by_different_fin_year(self):
        quotations = CustomerQuotation.get_filtered_quotations(
            user_comp_id=1, user_fin_year_id=2024,
            search_type='Select', search_value='', enquiry_id_value=''
        )
        self.assertEqual(quotations.count(), 1)
        self.assertEqual(quotations.first().quotation_no, 'Q003')

    def test_get_filtered_quotations_by_different_company(self):
        quotations = CustomerQuotation.get_filtered_quotations(
            user_comp_id=2, user_fin_year_id=2023,
            search_type='Select', search_value='', enquiry_id_value=''
        )
        self.assertEqual(quotations.count(), 1)
        self.assertEqual(quotations.first().quotation_no, 'Q004')

    def test_get_absolute_url(self):
        quotation = CustomerQuotation.objects.get(id=1)
        expected_url = reverse('quotations:customerquotation_detail', kwargs={
            'pk': 1,
            'customer_id': 'CUST001',
            'quotation_no': 'Q001',
            'enq_id': 'E001'
        })
        self.assertEqual(quotation.get_absolute_url(), expected_url)

class CustomerQuotationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Ensure test data exists for views
        Customer.objects.create(customer_id='CUST001', customer_name='Test Customer One', comp_id=1)
        CustomerQuotation.objects.create(
            id=1, comp_id=1, fin_year=2023, customer_id='CUST001',
            customer_name='Test Customer One', quotation_no='Q001', enquiry_id='E001',
            sys_date=date(2023, 1, 15), employee_name='Employee A'
        )
        CustomerQuotation.objects.create(
            id=2, comp_id=1, fin_year=2023, customer_id='CUST002',
            customer_name='Another Customer', quotation_no='Q002', enquiry_id='E002',
            sys_date=date(2023, 1, 20), employee_name='Employee B'
        )

    def setUp(self):
        self.client = Client()
        # Mock session data for company and financial year
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session['username'] = 'testuser'
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('quotations:customerquotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/list.html')
        self.assertContains(response, 'Customer Quotation - Edit')
        # Initial load might not have quotations in context as HTMX loads table
        self.assertNotContains(response, 'Q001') # Should be loaded by HTMX

    def test_table_partial_view_get_initial(self):
        response = self.client.get(reverse('quotations:customerquotation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/_table.html')
        self.assertContains(response, 'Q001')
        self.assertContains(response, 'Q002')
        self.assertContains(response, 'Test Customer One')

    def test_table_partial_view_get_with_customer_search(self):
        response = self.client.get(reverse('quotations:customerquotation_table'), {
            'search_type': '0',
            'txt_search_value': 'Test Customer One [CUST001]'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/_table.html')
        self.assertContains(response, 'Q001')
        self.assertNotContains(response, 'Q002')

    def test_table_partial_view_get_with_enquiry_search(self):
        response = self.client.get(reverse('quotations:customerquotation_table'), {
            'search_type': '1',
            'txt_enq_id': 'E002'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/_table.html')
        self.assertContains(response, 'Q002')
        self.assertNotContains(response, 'Q001')

    def test_table_partial_view_get_with_quotation_search(self):
        response = self.client.get(reverse('quotations:customerquotation_table'), {
            'search_type': '2',
            'txt_enq_id': 'Q001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/_table.html')
        self.assertContains(response, 'Q001')
        self.assertNotContains(response, 'Q002')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('quotations:customer_autocomplete'), {'term': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertIn('Test Customer One [CUST001]', response.json())
        self.assertNotIn('Third Customer [CUST003]', response.json()) # From different company

    def test_create_view_get(self):
        response = self.client.get(reverse('quotations:customerquotation_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/form.html')
        self.assertContains(response, 'Add Customer Quotation')

    def test_create_view_post_success(self):
        data = {
            'fin_year': 2024,
            'customer_id': 'CUST001',
            'customer_name': 'Test Customer One',
            'quotation_no': 'Q005',
            'enquiry_id': 'E005',
            'sys_date': '2024-05-01',
            'employee_name': 'New Employee'
        }
        response = self.client.post(reverse('quotations:customerquotation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(CustomerQuotation.objects.filter(quotation_no='Q005').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerQuotationList')

    def test_create_view_post_invalid(self):
        data = {
            'fin_year': 'invalid', # Invalid data
            'quotation_no': 'Q001' # Existing quotation no if validation added
        }
        response = self.client.post(reverse('quotations:customerquotation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'quotations/customerquotation/form.html')
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        quotation = CustomerQuotation.objects.get(id=1)
        response = self.client.get(reverse('quotations:customerquotation_edit', args=[quotation.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/form.html')
        self.assertContains(response, 'Edit Customer Quotation')
        self.assertContains(response, 'Q001')

    def test_update_view_post_success(self):
        quotation = CustomerQuotation.objects.get(id=1)
        data = {
            'fin_year': 2023,
            'customer_id': 'CUST001',
            'customer_name': 'Test Customer One',
            'quotation_no': 'Q001-Updated', # Change
            'enquiry_id': 'E001',
            'sys_date': '2023-01-15',
            'employee_name': 'Employee A'
        }
        response = self.client.post(reverse('quotations:customerquotation_edit', args=[quotation.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        quotation.refresh_from_db()
        self.assertEqual(quotation.quotation_no, 'Q001-Updated')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerQuotationList')

    def test_delete_view_get(self):
        quotation = CustomerQuotation.objects.get(id=1)
        response = self.client.get(reverse('quotations:customerquotation_delete', args=[quotation.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/customerquotation/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, 'Q001')

    def test_delete_view_post_success(self):
        quotation_to_delete = CustomerQuotation.objects.get(id=1)
        response = self.client.post(reverse('quotations:customerquotation_delete', args=[quotation_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(CustomerQuotation.objects.filter(id=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerQuotationList')

    def test_customer_quotation_detail_view(self):
        quotation = CustomerQuotation.objects.get(id=1)
        url = reverse('quotations:customerquotation_detail', kwargs={
            'pk': quotation.id,
            'customer_id': quotation.customer_id,
            'quotation_no': quotation.quotation_no,
            'enq_id': quotation.enquiry_id
        })
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'Quotation Details for {quotation.quotation_no}')

```

### Step 5: HTMX and Alpine.js Integration

The provided templates and views already integrate HTMX for dynamic content loading, form submissions, and modal interactions. Alpine.js is used for managing UI state like the visibility of search inputs and modal open/close states. DataTables is integrated into the `_table.html` partial for client-side table enhancements.

**Key HTMX/Alpine.js Features:**

- **Dynamic Table Loading:** The entire table (`#customerQuotationTable-container`) is initially empty and then loaded via `hx-get` to `{% url 'quotations:customerquotation_table' %}` on `load` and `refreshCustomerQuotationList` (custom event).
- **Search Functionality:** The "Search" button uses `hx-get` to submit the form data to `customerquotation_table` endpoint, updating only the table. `hx-include` ensures form fields are sent.
- **Autocomplete:** The `TxtSearchValue` input uses `hx-get` to `customer_autocomplete` on `keyup changed delay:500ms`, targeting `#autocomplete-results`. Alpine.js handles the `x-show` for the input visibility based on `searchType`.
- **Modal Interactions:**
    - "Add New", "Edit", and "Delete" buttons use `hx-get` to load forms into `#modalContent`.
    - Alpine.js controls the visibility of the `#modal` element (`x-data`, `x-show`, custom events `modal-open`/`modal-close`).
    - Form submissions (`hx-post`) return `204 No Content` with an `HX-Trigger` header (`refreshCustomerQuotationList`) to refresh the table and close the modal.
- **DataTables:** Initialized using jQuery within `htmx:afterSwap` event listener, ensuring DataTables is applied after the HTMX content is loaded into the DOM.
- **Messages:** Django's messages framework is used, and messages are displayed in the main template, benefiting from HTMX updates if the entire content block were swapped, or could be specifically triggered with an `HX-Trigger` if partial updates are more granular.

This comprehensive plan provides a detailed, automated, and modern approach to migrating your ASP.NET application to Django, focusing on best practices and enhanced user experience through HTMX and Alpine.js.

---