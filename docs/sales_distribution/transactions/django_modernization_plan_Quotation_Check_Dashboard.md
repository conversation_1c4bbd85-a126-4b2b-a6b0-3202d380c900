## ASP.NET to Django Conversion Script: Quotation Check Dashboard

### Project Overview and Business Value

The existing ASP.NET application, `Quotation_Check_Dashboard.aspx`, appears to be a foundational component, likely intended for viewing and managing sales quotations. While the provided code is a minimal placeholder, a robust "Quotation Check Dashboard" in a real-world scenario would enable sales teams or management to monitor, approve, or reject quotation requests, thereby streamlining the sales workflow and improving response times to customers.

**Our proposed Django modernization transforms this foundational component into a highly efficient, maintainable, and scalable solution, delivering significant business value:**

*   **Enhanced User Experience:** By leveraging HTMX and Alpine.js, users will experience a modern, responsive interface with instant feedback and no full-page reloads for common actions like adding, editing, or deleting quotations. This means faster data interaction and improved productivity.
*   **Improved Operational Efficiency:** DataTables integration provides powerful client-side search, sort, and pagination capabilities for quotation lists, allowing users to quickly find and manage critical information.
*   **Reduced Development and Maintenance Costs:** Django's "fat model, thin view" architecture, combined with strict separation of concerns, makes the codebase easier to understand, test, and extend. This reduces the time and cost associated with future enhancements and bug fixes.
*   **Future-Proof Architecture:** Transitioning to Django positions your application on a modern, actively maintained, and widely supported framework, ensuring long-term viability and access to a vast ecosystem of tools and libraries.
*   **Simplified Scalability:** Django's robust design patterns and built-in features are well-suited for applications that need to scale, ensuring your sales operations can grow without being bottlenecked by the application's performance.
*   **Automated Migration Path:** This plan focuses on providing a structured approach that can be largely automated using AI-assisted tools, minimizing manual effort and reducing the risk of human error during the transition.

This modernized Django application will provide a solid, high-performance foundation for all quotation-related activities, making your sales process more agile and data-driven.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Instructions:**
The provided ASP.NET code (`Quotation_Check_Dashboard.aspx` and its empty C# code-behind) does not contain explicit database schema information, SQL commands, or UI bindings that would reveal the underlying database table or its columns. The `Page_Load` method is also empty.

**Inference:**
Given the page name `Quotation_Check_Dashboard`, we infer the primary entity is a "Quotation". We will assume a database table named `tbl_quotation` and infer a plausible set of columns commonly associated with a sales quotation for demonstration purposes.

*   **Inferred Table Name:** `tbl_quotation`
*   **Inferred Columns:**
    *   `QuotationID` (Primary Key, Integer)
    *   `QuotationNo` (String, unique identifier for the quotation)
    *   `CustomerName` (String, name of the customer)
    *   `QuotationAmount` (Decimal, total amount of the quotation)
    *   `Status` (String, e.g., 'Pending', 'Approved', 'Rejected')
    *   `CreatedDate` (DateTime, date of quotation creation)
    *   `ValidUntilDate` (DateTime, expiration date of the quotation)
    *   `PreparedBy` (String, employee who prepared it)

### Step 2: Identify Backend Functionality

**Instructions:**
Based on the minimal ASP.NET code, no specific backend functionality (CRUD operations, validation, business logic) can be identified. The `Page_Load` method is empty, indicating no initial data loading or event handling.

**Inference:**
For a "Quotation Check Dashboard," the primary functionalities would be:
*   **Read:** Displaying a list of all quotations, with filters and search capabilities. This is the core "Check" functionality.
*   **Update:** Changing the status of a quotation (e.g., from 'Pending' to 'Approved' or 'Rejected'), or modifying other details.
*   **Create:** Adding new quotations (even if this dashboard is primarily for "checking," the ability to add new entries is typical for a data management system).
*   **Delete:** Removing obsolete or incorrect quotations.

We will implement standard CRUD operations (Create, Read, Update, Delete) to provide a comprehensive migration example, assuming these are the implied functionalities for a dashboard managing quotations. Any specific business logic for checking or approving quotations would typically reside in the "fat model" methods in Django.

### Step 3: Infer UI Components

**Instructions:**
The `.aspx` file only defines content placeholders and no specific UI controls (like GridView, TextBox, Button).

**Inference:**
Based on the inferred backend functionality for a "Quotation Check Dashboard", we would typically expect:
*   **List View:** A data grid (like a `GridView` in ASP.NET) to display a tabular list of quotations with columns for `QuotationNo`, `CustomerName`, `Amount`, `Status`, `CreatedDate`, etc. This list would need client-side search, sorting, and pagination.
*   **Detail/Form View:** Forms (potentially in a modal dialog) for adding new quotations and editing existing ones. These would likely include text inputs, number inputs, dropdowns for status, and date pickers.
*   **Action Buttons:** Buttons or links for "Add New Quotation", "Edit", "Delete", and potentially status-specific actions (e.g., "Approve", "Reject").

Our Django implementation will reflect these inferred UI components using DataTables for lists and HTMX-driven modals for forms.

---

### Step 4: Generate Django Code

We will create a new Django app, `sales`, within which the `Quotation` module components will reside.

#### 4.1 Models (`sales/models.py`)

**Instructions:**
- Name the model `Quotation`.
- Define fields with appropriate Django field types based on inferred column data types.
- Use `db_column` if field names differ from column names.
- Set `managed = False` and `db_table = 'tbl_quotation'` in the Meta class.
- Include model methods for business logic (fat model approach).

**Template:**

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal

class Quotation(models.Model):
    """
    Represents a sales quotation in the system.
    Maps to the existing 'tbl_quotation' database table.
    """
    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
        ('Cancelled', 'Cancelled'),
    ]

    id = models.IntegerField(db_column='QuotationID', primary_key=True)
    quotation_number = models.CharField(db_column='QuotationNo', max_length=50, unique=True, verbose_name='Quotation Number')
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name='Customer Name')
    quotation_amount = models.DecimalField(db_column='QuotationAmount', max_digits=18, decimal_places=2, verbose_name='Amount')
    status = models.CharField(db_column='Status', max_length=50, choices=STATUS_CHOICES, default='Pending', verbose_name='Status')
    created_date = models.DateTimeField(db_column='CreatedDate', default=timezone.now, verbose_name='Created Date')
    valid_until_date = models.DateTimeField(db_column='ValidUntilDate', null=True, blank=True, verbose_name='Valid Until')
    prepared_by = models.CharField(db_column='PreparedBy', max_length=100, verbose_name='Prepared By')

    class Meta:
        managed = False  # Set to True if Django should manage the table schema
        db_table = 'tbl_quotation'
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'
        ordering = ['-created_date']

    def __str__(self):
        """Returns a string representation of the quotation."""
        return f"Quotation {self.quotation_number} for {self.customer_name}"
        
    def is_pending(self):
        """Checks if the quotation status is 'Pending'."""
        return self.status == 'Pending'

    def approve_quotation(self):
        """Approves the quotation and updates its status."""
        if self.is_pending():
            self.status = 'Approved'
            self.save()
            return True
        return False

    def reject_quotation(self):
        """Rejects the quotation and updates its status."""
        if self.is_pending():
            self.status = 'Rejected'
            self.save()
            return True
        return False
        
    def get_display_status(self):
        """Returns the human-readable status."""
        return dict(self.STATUS_CHOICES).get(self.status, self.status)

```

#### 4.2 Forms (`sales/forms.py`)

**Instructions:**
- Create a `ModelForm` for `Quotation`.
- Include editable fields.
- Add widgets with Tailwind CSS classes for styling.
- Implement form validation logic based on ASP.NET validation (if any, otherwise standard Django validation).

**Template:**

```python
from django import forms
from .models import Quotation

class QuotationForm(forms.ModelForm):
    """
    Form for creating and updating Quotation instances.
    Includes Tailwind CSS classes for styling.
    """
    class Meta:
        model = Quotation
        fields = ['quotation_number', 'customer_name', 'quotation_amount', 'status', 'valid_until_date', 'prepared_by']
        widgets = {
            'quotation_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quotation_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'valid_until_date': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'prepared_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'quotation_number': 'Quotation Number',
            'customer_name': 'Customer Name',
            'quotation_amount': 'Amount',
            'status': 'Current Status',
            'valid_until_date': 'Valid Until',
            'prepared_by': 'Prepared By',
        }
        
    def clean_quotation_amount(self):
        """Ensures the quotation amount is positive."""
        amount = self.cleaned_data['quotation_amount']
        if amount <= 0:
            raise forms.ValidationError("Quotation amount must be a positive value.")
        return amount

    def clean_valid_until_date(self):
        """Ensures valid_until_date is in the future."""
        valid_until_date = self.cleaned_data.get('valid_until_date')
        if valid_until_date and valid_until_date < timezone.now():
            raise forms.ValidationError("Valid Until Date must be in the future.")
        return valid_until_date
```

#### 4.3 Views (`sales/views.py`)

**Instructions:**
- Define `QuotationListView`, `QuotationCreateView`, `QuotationUpdateView`, `QuotationDeleteView`.
- Use `Quotation` as the model, `QuotationForm` for forms, and set appropriate `template_name` and `success_url`.
- Add success messages using `messages.success`.
- Keep views thin (5-15 lines) and move business logic to models.
- Add `QuotationTablePartialView` for HTMX-driven table refreshes.

**Template:**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Quotation
from .forms import QuotationForm

class QuotationListView(ListView):
    """
    Displays a list of all Quotation objects.
    This view serves the main page where the DataTable will be loaded via HTMX.
    """
    model = Quotation
    template_name = 'sales/quotation/list.html'
    context_object_name = 'quotations' # This is used in the main list template for initial render, if not using full HTMX load.
                                       # For full HTMX load of table, the context_object_name is primarily used by QuotationTablePartialView.

class QuotationTablePartialView(ListView):
    """
    Returns only the HTML for the Quotation table, designed for HTMX requests.
    """
    model = Quotation
    template_name = 'sales/quotation/_quotation_table.html'
    context_object_name = 'quotations'

class QuotationCreateView(CreateView):
    """
    Handles the creation of new Quotation objects.
    Renders a form and processes its submission via HTMX.
    """
    model = Quotation
    form_class = QuotationForm
    template_name = 'sales/quotation/_quotation_form.html' # This is a partial template for modal
    success_url = reverse_lazy('quotation_list')

    def form_valid(self, form):
        # Business logic can be called on the model instance before saving if needed.
        # e.g., form.instance.set_default_status()
        response = super().form_valid(form)
        messages.success(self.request, 'Quotation added successfully.')
        # For HTMX requests, return a 204 No Content and trigger a client-side event.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList' # Triggers an HTMX event to refresh the table.
                }
            )
        return response

class QuotationUpdateView(UpdateView):
    """
    Handles the updating of existing Quotation objects.
    Renders a pre-filled form and processes its submission via HTMX.
    """
    model = Quotation
    form_class = QuotationForm
    template_name = 'sales/quotation/_quotation_form.html' # This is a partial template for modal
    success_url = reverse_lazy('quotation_list')

    def form_valid(self, form):
        # Business logic can be called on the model instance before saving if needed.
        # e.g., form.instance.update_audit_fields()
        response = super().form_valid(form)
        messages.success(self.request, 'Quotation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return response

class QuotationDeleteView(DeleteView):
    """
    Handles the deletion of Quotation objects.
    Renders a confirmation prompt and processes deletion via HTMX.
    """
    model = Quotation
    template_name = 'sales/quotation/_quotation_confirm_delete.html' # This is a partial template for modal
    success_url = reverse_lazy('quotation_list')

    def delete(self, request, *args, **kwargs):
        # Business logic before delete, e.g., check dependencies.
        # if not self.get_object().can_be_deleted():
        #    messages.error(self.request, 'Quotation cannot be deleted due to dependencies.')
        #    return HttpResponse(status=400) # Or render error partial
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Quotation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return response

```

#### 4.4 Templates (`sales/templates/sales/quotation/`)

**Instructions:**
- List Template (`list.html`): Extend `core/base.html`, use DataTables, HTMX for dynamic updates and form loading.
- Table Partial Template (`_quotation_table.html`): Rendered by `QuotationTablePartialView` for HTMX.
- Form Partial Template (`_quotation_form.html`): For `CreateView` and `UpdateView`.
- Delete Partial Template (`_quotation_confirm_delete.html`): For `DeleteView`.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Quotation Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200"
            hx-get="{% url 'quotation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Quotation
        </button>
    </div>
    
    <div id="quotationTable-container"
         hx-trigger="load, refreshQuotationList from:body"
         hx-get="{% url 'quotation_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Quotations...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 relative transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
             _="on htmx:afterSwap add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js can be used here for more complex UI state management
        // For example, managing modal visibility based on global state if needed
        // but for simple show/hide, _hyperscript is very efficient.
    });

    // Handle messages from Django
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            // Re-initialize any JS elements inside the modal after content swap
        }
    });

    // Listen for HTMX triggers to hide modal after successful form submissions
    document.body.addEventListener('refreshQuotationList', function(event) {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active'); // Hide the modal
        }
    });
</script>
{% endblock %}
```

**`_quotation_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="quotationTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valid Until</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prepared By</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for quotation in quotations %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ quotation.quotation_number }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ quotation.customer_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ quotation.quotation_amount|floatformat:2 }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if quotation.status == 'Approved' %}bg-green-100 text-green-800
                        {% elif quotation.status == 'Pending' %}bg-yellow-100 text-yellow-800
                        {% elif quotation.status == 'Rejected' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ quotation.get_display_status }}
                    </span>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ quotation.created_date|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{% if quotation.valid_until_date %}{{ quotation.valid_until_date|date:"Y-m-d H:i" }}{% else %}N/A{% endif %}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ quotation.prepared_by }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3"
                        hx-get="{% url 'quotation_edit' quotation.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'quotation_delete' quotation.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 px-6 text-center text-gray-500">No quotations found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Initialize DataTables after the content is loaded via HTMX
$(document).ready(function() {
    $('#quotationTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 8] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

**`_quotation_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Quotation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                <div class="mt-1">
                    {{ field }}
                </div>
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save Quotation
            </button>
        </div>
    </form>
</div>
```

**`_quotation_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete quotation **{{ object.quotation_number }}** by **{{ object.customer_name }}**? This action cannot be undone.</p>
    
    <form hx-post="{% url 'quotation_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <span id="delete-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`sales/urls.py`)

**Instructions:**
- Create paths for list, create, update, delete, and the table partial view.
- Use appropriate naming patterns and consistent URL structure.

**Template:**

```python
from django.urls import path
from .views import QuotationListView, QuotationCreateView, QuotationUpdateView, QuotationDeleteView, QuotationTablePartialView

urlpatterns = [
    # Main dashboard view for quotations
    path('quotations/', QuotationListView.as_view(), name='quotation_list'),
    
    # HTMX endpoint for refreshing the quotation table (partial view)
    path('quotations/table/', QuotationTablePartialView.as_view(), name='quotation_table'),
    
    # HTMX endpoint for displaying and processing the add form
    path('quotations/add/', QuotationCreateView.as_view(), name='quotation_add'),
    
    # HTMX endpoint for displaying and processing the edit form
    path('quotations/edit/<int:pk>/', QuotationUpdateView.as_view(), name='quotation_edit'),
    
    # HTMX endpoint for displaying and processing the delete confirmation
    path('quotations/delete/<int:pk>/', QuotationDeleteView.as_view(), name='quotation_delete'),
]
```

#### 4.6 Tests (`sales/tests.py`)

**Instructions:**
- Include comprehensive unit tests for model methods and properties.
- Add integration tests for all views (list, create, update, delete).
- Ensure at least 80% test coverage of code.

**Template:**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from .models import Quotation
from .forms import QuotationForm

class QuotationModelTest(TestCase):
    """
    Unit tests for the Quotation model.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects for all test methods
        cls.quotation1 = Quotation.objects.create(
            id=1,
            quotation_number='Q-001',
            customer_name='Test Customer A',
            quotation_amount=Decimal('100.00'),
            status='Pending',
            created_date=timezone.now(),
            valid_until_date=timezone.now() + timedelta(days=7),
            prepared_by='John Doe'
        )
        cls.quotation2 = Quotation.objects.create(
            id=2,
            quotation_number='Q-002',
            customer_name='Test Customer B',
            quotation_amount=Decimal('250.50'),
            status='Approved',
            created_date=timezone.now() - timedelta(days=5),
            valid_until_date=timezone.now() + timedelta(days=2),
            prepared_by='Jane Smith'
        )
  
    def test_quotation_creation(self):
        """Verify Quotation object is created correctly."""
        self.assertEqual(self.quotation1.quotation_number, 'Q-001')
        self.assertEqual(self.quotation1.customer_name, 'Test Customer A')
        self.assertEqual(self.quotation1.quotation_amount, Decimal('100.00'))
        self.assertEqual(self.quotation1.status, 'Pending')
        self.assertEqual(self.quotation1.prepared_by, 'John Doe')

    def test_quotation_str_method(self):
        """Test the __str__ method of the Quotation model."""
        expected_str = f"Quotation {self.quotation1.quotation_number} for {self.quotation1.customer_name}"
        self.assertEqual(str(self.quotation1), expected_str)
        
    def test_is_pending_method(self):
        """Test the is_pending method."""
        self.assertTrue(self.quotation1.is_pending())
        self.assertFalse(self.quotation2.is_pending()) # quotation2 is 'Approved'
        
    def test_approve_quotation_method(self):
        """Test the approve_quotation method."""
        # Test pending quotation
        self.assertTrue(self.quotation1.approve_quotation())
        self.assertEqual(self.quotation1.status, 'Approved')
        
        # Test already approved quotation (should not change and return False)
        self.assertFalse(self.quotation2.approve_quotation())
        self.assertEqual(self.quotation2.status, 'Approved')

    def test_reject_quotation_method(self):
        """Test the reject_quotation method."""
        # Create a new pending quotation for rejection test
        quotation_to_reject = Quotation.objects.create(
            id=3,
            quotation_number='Q-003',
            customer_name='Test Customer C',
            quotation_amount=Decimal('500.00'),
            status='Pending',
            created_date=timezone.now(),
            valid_until_date=timezone.now() + timedelta(days=10),
            prepared_by='Alice Brown'
        )
        self.assertTrue(quotation_to_reject.reject_quotation())
        self.assertEqual(quotation_to_reject.status, 'Rejected')
        
        # Test already approved quotation (should not change and return False)
        self.assertFalse(self.quotation2.reject_quotation())
        self.assertEqual(self.quotation2.status, 'Approved')

    def test_get_display_status_method(self):
        """Test the get_display_status method."""
        self.assertEqual(self.quotation1.get_display_status(), 'Pending')
        self.assertEqual(self.quotation2.get_display_status(), 'Approved')
        self.quotation1.status = 'Unknown'; # Test unknown status
        self.assertEqual(self.quotation1.get_display_status(), 'Unknown')


class QuotationViewsTest(TestCase):
    """
    Integration tests for Quotation views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.quotation1 = Quotation.objects.create(
            id=10, # Use a different ID range to avoid clashes if IDs are auto-incremented
            quotation_number='Q-VIEW-001',
            customer_name='View Test Customer A',
            quotation_amount=Decimal('100.00'),
            status='Pending',
            created_date=timezone.now(),
            valid_until_date=timezone.now() + timedelta(days=7),
            prepared_by='Test User'
        )
        cls.quotation2 = Quotation.objects.create(
            id=11,
            quotation_number='Q-VIEW-002',
            customer_name='View Test Customer B',
            quotation_amount=Decimal('200.00'),
            status='Approved',
            created_date=timezone.now() - timedelta(days=1),
            valid_until_date=timezone.now() + timedelta(days=1),
            prepared_by='Test User'
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view(self):
        """Test the Quotation list view (main dashboard page)."""
        response = self.client.get(reverse('quotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/list.html')
        # The main list view template does not load objects directly,
        # it relies on HTMX to load the table partial. So, context objects might not be present.
        # self.assertTrue('quotations' in response.context) # This might fail if using full HTMX table load
        
    def test_table_partial_view(self):
        """Test the HTMX partial view for the quotation table."""
        response = self.client.get(reverse('quotation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/_quotation_table.html')
        self.assertTrue('quotations' in response.context)
        self.assertEqual(len(response.context['quotations']), 2) # Should contain the two test quotations
        self.assertContains(response, 'View Test Customer A')
        self.assertContains(response, 'View Test Customer B')

    def test_create_view_get(self):
        """Test GET request to the create quotation form."""
        response = self.client.get(reverse('quotation_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/_quotation_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], QuotationForm)
        
    def test_create_view_post_success(self):
        """Test successful POST request to create a new quotation."""
        initial_count = Quotation.objects.count()
        data = {
            'quotation_number': 'Q-NEW-003',
            'customer_name': 'New Customer C',
            'quotation_amount': '300.00',
            'status': 'Pending',
            'created_date': timezone.now(),
            'valid_until_date': (timezone.now() + timedelta(days=14)).isoformat(), # ISO format for datetime-local
            'prepared_by': 'New Salesperson',
        }
        # Simulate an HTMX request
        response = self.client.post(reverse('quotation_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX returns 204 No Content
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        
        self.assertEqual(Quotation.objects.count(), initial_count + 1)
        new_quotation = Quotation.objects.get(quotation_number='Q-NEW-003')
        self.assertEqual(new_quotation.customer_name, 'New Customer C')
        
    def test_create_view_post_invalid(self):
        """Test invalid POST request to create a new quotation."""
        initial_count = Quotation.objects.count()
        data = {
            'quotation_number': 'Q-FAIL-004',
            'customer_name': 'Invalid Customer',
            'quotation_amount': '-50.00', # Invalid amount
            'status': 'Pending',
            'created_date': timezone.now(),
            'valid_until_date': (timezone.now() - timedelta(days=1)).isoformat(), # Invalid date
            'prepared_by': 'Invalid User',
        }
        response = self.client.post(reverse('quotation_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # HTMX renders the form again with errors
        self.assertTemplateUsed(response, 'sales/quotation/_quotation_form.html')
        self.assertTrue('form' in response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('Quotation amount must be a positive value.', str(response.content))
        self.assertIn('Valid Until Date must be in the future.', str(response.content))
        self.assertEqual(Quotation.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test GET request to the update quotation form."""
        obj = self.quotation1
        response = self.client.get(reverse('quotation_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/_quotation_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], QuotationForm)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        """Test successful POST request to update an existing quotation."""
        obj = self.quotation1
        updated_amount = Decimal('150.75')
        data = {
            'quotation_number': obj.quotation_number, # Keep original as it's unique
            'customer_name': 'Updated Customer A',
            'quotation_amount': str(updated_amount),
            'status': 'Approved',
            'created_date': obj.created_date.isoformat(),
            'valid_until_date': (timezone.now() + timedelta(days=10)).isoformat(),
            'prepared_by': obj.prepared_by,
        }
        response = self.client.post(reverse('quotation_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        
        obj.refresh_from_db()
        self.assertEqual(obj.customer_name, 'Updated Customer A')
        self.assertEqual(obj.quotation_amount, updated_amount)
        self.assertEqual(obj.status, 'Approved')

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation page."""
        obj = self.quotation1
        response = self.client.get(reverse('quotation_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation/_quotation_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post_success(self):
        """Test successful POST request to delete a quotation."""
        # Create an object specifically for deletion
        quotation_to_delete = Quotation.objects.create(
            id=12,
            quotation_number='Q-DEL-005',
            customer_name='Customer for Deletion',
            quotation_amount=Decimal('99.99'),
            status='Pending',
            created_date=timezone.now(),
            valid_until_date=timezone.now() + timedelta(days=3),
            prepared_by='Delete Test'
        )
        pk_to_delete = quotation_to_delete.pk
        initial_count = Quotation.objects.count()

        response = self.client.post(reverse('quotation_delete', args=[pk_to_delete]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        
        self.assertEqual(Quotation.objects.count(), initial_count - 1)
        self.assertFalse(Quotation.objects.filter(pk=pk_to_delete).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- Use HTMX for all dynamic updates and form submissions.
- Use Alpine.js for client-side reactivity and modals.
- Implement DataTables for all list views with sorting and filtering.
- Make all interactions work without full page reloads.
- Ensure proper `HX-Trigger` responses for list refreshes after CRUD operations.

**Implementation Details:**
*   **HTMX for CRUD Modals:**
    *   The "Add New Quotation", "Edit", and "Delete" buttons use `hx-get` to fetch partial HTML (`_quotation_form.html` or `_quotation_confirm_delete.html`) into the `#modalContent` div.
    *   The `hx-target="#modalContent"` and `hx-trigger="click"` ensure the content is loaded when the button is clicked.
    *   `_hyperscript` (`_="on click add .is-active to #modal"`) is used to show the modal by adding a CSS class (`is-active`). A similar `_` command on the modal itself hides it when clicking outside.
*   **HTMX for Form Submission:**
    *   Forms in `_quotation_form.html` and `_quotation_confirm_delete.html` use `hx-post="{{ request.path }}" hx-swap="none"`. `hx-swap="none"` prevents HTMX from modifying the current DOM, as the view will return a `204 No Content` response.
    *   Upon successful submission, the Django view returns a `204 No Content` response with an `HX-Trigger` header (`{'HX-Trigger': 'refreshQuotationList'}`).
    *   The `list.html` has a div wrapping the table (`#quotationTable-container`) with `hx-trigger="load, refreshQuotationList from:body"`. This makes it listen for the `refreshQuotationList` event (fired from the body) and then re-fetch the table content (`hx-get="{% url 'quotation_table' %}"`). This effectively refreshes the DataTable after any CRUD operation without a full page reload.
    *   The `_hyperscript` on `modalContent` also `adds .is-active to #modal` after an `htmx:afterSwap` event on the modal content, ensuring the modal remains open after partial content is loaded (e.g., if a form submission has errors, the form with errors is reloaded into the modal).
*   **DataTables Integration:**
    *   The `_quotation_table.html` partial contains the HTML `<table>` structure.
    *   A `<script>` tag *within* this partial initializes DataTables (`$('#quotationTable').DataTable({...})`) immediately after the partial is loaded and inserted into the DOM by HTMX. This ensures DataTables is always applied to the newly loaded table content.
*   **Alpine.js:**
    *   `document.addEventListener('alpine:init', ...)` is included in `list.html` as a placeholder for more complex client-side state management using Alpine.js, though for basic modal visibility and form submission, `_hyperscript` is very effective and lightweight. Alpine could be used to manage, for example, search input states or more intricate modal interactions if needed.
*   **Tailwind CSS:** All generated HTML templates and form widgets incorporate Tailwind CSS classes for modern, responsive styling.

### Final Notes

This comprehensive plan provides a clear, step-by-step guide for modernizing your `Quotation_Check_Dashboard` to a Django 5.0+ application. By adhering to the principles of "fat models, thin views," leveraging HTMX for dynamic interactions, and implementing DataTables for efficient data presentation, your organization will benefit from a high-performance, maintainable, and future-proof sales management solution. The focus on AI-assisted automation means that much of this code generation and transformation can be streamlined, significantly reducing the manual effort and accelerating your migration timeline.