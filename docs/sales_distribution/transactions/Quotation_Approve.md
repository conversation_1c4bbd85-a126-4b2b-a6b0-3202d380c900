## ASP.NET to Django Conversion Script: Quotation Approval Module

This modernization plan outlines the conversion of your existing ASP.NET Quotation Approval module to a modern Django application. Our approach prioritizes automation, leveraging AI-assisted tools for code generation and migration, ensuring a smooth, efficient, and reliable transition.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with two primary tables: `SD_Cust_Quotation_Master` for quotation details and `SD_Cust_master` for customer information, used in the autocomplete.

*   **Primary Table:** `SD_Cust_Quotation_Master`
    *   `Id` (PK, int): Mapped to `id` in Django.
    *   `FinYear` (string/int): Financial Year.
    *   `QuotationNo` (string): Unique quotation number.
    *   `SysDate` (datetime): System Date (Quotation Date).
    *   `EmpLoyeeName` (string): Name of the employee who generated the quotation.
    *   `CustomerName` (string): Customer's name.
    *   `CustomerId` (string/int): Customer's unique identifier.
    *   `Checked` (bool/int): Indicates if the quotation has been checked (value `1` in `makegrid`).
    *   `CheckedDate` (datetime): Date when the quotation was checked.
    *   `Approved` (bool/int): Indicates if the quotation is approved (value `1` when approved).
    *   `ApprovedBy` (string/int): User ID who approved the quotation.
    *   `ApproveDate` (datetime): Date when the quotation was approved.
    *   `ApproveTime` (time): Time when the quotation was approved.
    *   `AuthorizedDate` (datetime): Date when the quotation was authorized.
    *   `CompId` (int): Company ID.
    *   `EnqId` (string/int): Enquiry ID, used for viewing/printing details.

*   **Related Table:** `SD_Cust_master`
    *   `CustomerId` (PK, string/int): Customer's unique identifier.
    *   `CustomerName` (string): Customer's name.
    *   `CompId` (int): Company ID.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD and other operations in the ASP.NET code.

**Instructions:**
The ASP.NET code primarily focuses on **Read** and **Update** operations for quotations, along with a **Read** operation for customer autocomplete.

*   **Read (List View):**
    *   Displays a list of quotations.
    *   Filters by `QuotationNo` or `CustomerId`.
    *   Crucially, it *always* filters for quotations where `Checked` is `1` (via `@y` parameter in `Sp_Quatation_Grid`).
    *   Handles pagination (`GridView2_PageIndexChanging`).

*   **Update (Bulk Approval):**
    *   Allows users to select multiple quotations via checkboxes.
    *   A single "Approved" button (in the GridView footer) triggers an update.
    *   Updates `Approved`, `ApprovedBy`, `ApproveDate`, and `ApproveTime` for selected quotations.

*   **Read (Detail View/Print):**
    *   A "View" link for each row redirects to a `Quotation_Print_Details.aspx` page with quotation details. This implies a separate detail view or print functionality.

*   **Read (Autocomplete):**
    *   Provides customer name suggestions for search input (`GetCompletionList` WebMethod).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page uses standard controls to display data and capture user input.

*   **Search/Filter Controls:**
    *   `drpfield` (DropDownList): A dropdown to select the search criteria ("Quotation No" or "Customer"). This will be an HTML `<select>` element controlled by Alpine.js.
    *   `txtPONo` (TextBox): Text input for "Quotation No". Will be an HTML `<input type="text">`.
    *   `txtSupplier` (TextBox) with `AutoCompleteExtender`: Text input for "Customer" with autocomplete functionality. This will be an HTML `<input type="text">` that interacts with an HTMX endpoint for suggestions.
    *   `Button1` (Button): "Search" button to trigger data refresh. This will be an HTML `<button>` with HTMX attributes.

*   **Data Display:**
    *   `GridView2`: The main data table. This will be replaced by an HTML `<table>` element rendered by DataTables, loaded dynamically via HTMX.
    *   Columns include: "SN", "Id" (hidden), "Fin Year", "Quotation No", "Date", "Gen. By", "Customer", "Code", "Checked", "For Approve" (with checkbox), "Authorized".

*   **Actions:**
    *   `lnkbtn` (LinkButton with "View" text): Triggers redirection to a detail page. This will be an HTML `<a>` tag.
    *   `CK` (CheckBox): Individual checkbox for each quotation in the "For Approve" column. These will be HTML `<input type="checkbox">` elements.
    *   `App` (Button with "Approved" text): Triggers the bulk approval action. This will be an HTML `<button>` inside a `<form>` submitting data via HTMX.

### Step 4: Generate Django Code

We will create a Django application named `quotations` to house this module.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
We'll define two models: `Customer` and `Quotation`. Both will be `managed = False` as they map to existing tables. We'll add a `QuotationManager` for encapsulating the `makegrid` logic (filtering and data retrieval) and an `approve_selected` method for bulk approval.

**File: `quotations/models.py`**

```python
from django.db import models
from django.db.models import F
from django.utils import timezone

# Placeholder for Company model, assuming it exists elsewhere or will be created
# If CompId is just an integer in the database, it can remain an IntegerField
class Company(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_name = models.CharField(max_length=255, db_column='CompName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'CompanyMaster' # Adjust table name if different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.comp_name or f"Company {self.comp_id}"


class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', related_name='customers', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

    @classmethod
    def get_customer_code_from_display(cls, display_text):
        """
        Extracts the CustomerId from a display string like "Customer Name [CUST001]".
        Replicates the logic of fun.getCode().
        """
        if '[' in display_text and ']' in display_text:
            return display_text.split('[')[-1].rstrip(']')
        return None # Or raise an error if not found


class QuotationManager(models.Manager):
    def get_quotations_for_approval_list(self, company_id, financial_year, search_field=None, search_value=None):
        """
        Replicates the Sp_Quatation_Grid stored procedure logic.
        Filters quotations that are 'Checked' (status 1) and applies dynamic search.
        """
        queryset = self.select_related('customer').filter(
            company_id=company_id,
            fin_year=financial_year,
            is_checked=True # Equivalent to 'And SD_Cust_Quotation_Master.Checked=1'
        ).order_by('-quotation_no') # Order by quotation number, newest first

        if search_field == 'quotation_no' and search_value:
            queryset = queryset.filter(quotation_no__iexact=search_value)
        elif search_field == 'customer_id' and search_value:
            # Assume search_value is the actual CustomerId, derived from autocomplete
            queryset = queryset.filter(customer_id=search_value)
            
        return queryset.annotate(
            # Replicate fields that might be derived or joined for display
            generated_by_employee_name=F('generated_by_employee_name'), # Assuming direct field for now
            customer_name_display=F('customer__customer_name') # Join from Customer model
        )

    def approve_selected_quotations(self, quotation_ids, approved_by_username, company_id):
        """
        Approves a list of quotations.
        Replicates the 'App' command logic from GridView2_RowCommand.
        """
        now = timezone.now()
        # Filter by company_id for security and only update if not already approved
        updated_count = self.filter(
            id__in=quotation_ids,
            company_id=company_id,
            is_approved=False # Only approve if not already approved
        ).update(
            is_approved=True,
            approved_by_username=approved_by_username,
            approved_date=now.date(),
            approved_time=now.time()
        )
        return updated_count


class Quotation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # PK is Id
    fin_year = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50, blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    # EmpLoyeeName might be a ForeignKey to an Employee table, but kept as CharField based on current info
    generated_by_employee_name = models.CharField(db_column='EmpLoyeeName', max_length=255, blank=True, null=True)
    
    # CustomerName is duplicated in master, CustomerId is the FK
    # For reporting, we might want to keep CustomerName here if that's how it's stored
    customer_id = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId', to_field='customer_id', related_name='quotations', blank=True, null=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)

    is_checked = models.BooleanField(db_column='Checked', default=False)
    checked_date = models.DateField(db_column='CheckedDate', blank=True, null=True)
    
    is_approved = models.BooleanField(db_column='Approve', default=False)
    approved_by_username = models.CharField(db_column='ApprovedBy', max_length=100, blank=True, null=True)
    approved_date = models.DateField(db_column='ApproveDate', blank=True, null=True)
    approved_time = models.TimeField(db_column='ApproveTime', blank=True, null=True)
    
    authorized_date = models.DateField(db_column='AuthorizedDate', blank=True, null=True)
    
    company_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', related_name='quotations', blank=True, null=True)
    enquiry_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)

    objects = QuotationManager() # Attach our custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'

    def __str__(self):
        return f"Quotation No: {self.quotation_no} - {self.customer_name}"

    def get_absolute_url(self):
        # This will be the URL for the detail/print page, similar to ASP.NET's redirect
        return reverse('quotations:quotation_detail', args=[self.id]) # Assuming a detail view exists

    def can_be_approved(self):
        """
        Business logic: A quotation can be approved if it's checked and not yet approved.
        This helps hide the checkbox if already approved, replicating ASP.NET logic.
        """
        return self.is_checked and not self.is_approved

```

#### 4.2 Forms

**Task:** Define Django forms for user input (search and bulk approval).

**Instructions:**
We'll create a `QuotationSearchForm` for the top search bar and a simple `ApproveQuotationsForm` to handle the bulk POST for approvals.

**File: `quotations/forms.py`**

```python
from django import forms
from .models import Customer, Quotation
from django.urls import reverse_lazy

class QuotationSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('quotation_no', 'Quotation No'),
        ('customer_id', 'Customer'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        required=False
    )
    quotation_no_search = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter Quotation No', 'x-show': "searchBy === 'quotation_no'"})
    )
    customer_search = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Search Customer Name',
            'x-show': "searchBy === 'customer_id'",
            'hx-get': reverse_lazy('quotations:customer_autocomplete'),
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-autocomplete-results',
            'hx-indicator': '#customer-autocomplete-indicator',
            'autocomplete': 'off'
        })
    )
    # Hidden field to store the customer ID when selected from autocomplete
    selected_customer_id = forms.CharField(widget=forms.HiddenInput(), required=False)

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        quotation_no = cleaned_data.get('quotation_no_search')
        customer_search = cleaned_data.get('customer_search')
        selected_customer_id = cleaned_data.get('selected_customer_id')

        if search_by == 'quotation_no' and not quotation_no:
            self.add_error('quotation_no_search', 'Quotation No cannot be empty for this search type.')
        elif search_by == 'customer_id':
            if not customer_search:
                self.add_error('customer_search', 'Customer name cannot be empty for this search type.')
            # If customer name is provided, ensure a corresponding ID was selected via autocomplete
            # This logic needs to be more robust for production, perhaps checking if selected_customer_id matches
            # the entered text or if the text is a valid customer.
            # For now, we'll assume a selection implies a valid ID for simplicity.
        return cleaned_data


class ApproveQuotationsForm(forms.Form):
    # This form will handle the bulk submission of quotation IDs
    # It won't be rendered as a typical form, but its clean method can be used for validation
    quotation_ids = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple,
        required=False,
        choices=() # Choices will be dynamically set in the view
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically set choices for quotation_ids
        # This form is typically used for validation of POST data, not rendering input fields.
        # The checkboxes are rendered manually in the template.
        # We ensure the IDs are integers.
        self.fields['quotation_ids'].coerce = int

```

#### 4.3 Views

**Task:** Implement Read (list, table partial, autocomplete) and Update (bulk approve) operations using CBVs and standard Django views.

**Instructions:**
We'll have a main `QuotationListView` for the initial page, a `QuotationTablePartialView` to serve the DataTables content via HTMX, a `QuotationApproveSelectedView` for the bulk approval POST, and a `CustomerAutocompleteView` for the AJAX search.

**File: `quotations/views.py`**

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin # Assume authentication for session variables
from django.db.models import Q # For complex queries

from .models import Quotation, Customer, Company
from .forms import QuotationSearchForm, ApproveQuotationsForm

# Mock session variables (replace with actual session/user data in production)
# In a real application, company_id and fin_year would come from request.user profile or session
def get_user_context(request):
    # Example: Retrieve from session or user profile
    # For demonstration, using dummy values.
    # Replace with: request.session.get('compid'), request.session.get('finyear'), request.user.username
    return {
        'company_id': 1, # Replace with dynamic CompId
        'financial_year': '2023-2024', # Replace with dynamic FinYear
        'username': 'admin_user' # Replace with request.user.username
    }


class QuotationListView(LoginRequiredMixin, View):
    """
    Main view for the Quotation Approval page.
    Renders the search form and the container for the HTMX-loaded table.
    """
    template_name = 'quotations/quotation/list.html'

    def get(self, request, *args, **kwargs):
        form = QuotationSearchForm()
        context = {
            'search_form': form,
        }
        return render(request, self.template_name, context)


class QuotationTablePartialView(LoginRequiredMixin, ListView):
    """
    HTMX-driven view to render the DataTables content.
    Handles search filtering based on form data submitted via HTMX.
    """
    model = Quotation
    template_name = 'quotations/quotation/_table.html'
    context_object_name = 'quotations'
    paginate_by = 20 # ASP.NET GridView had PageSize="20"

    def get_queryset(self):
        user_context = get_user_context(self.request)
        company_id = user_context['company_id']
        financial_year = user_context['financial_year']

        search_by = self.request.GET.get('search_by')
        quotation_no = self.request.GET.get('quotation_no_search')
        customer_name_input = self.request.GET.get('customer_search')
        selected_customer_id = self.request.GET.get('selected_customer_id') # Get the actual ID if selected

        search_field = None
        search_value = None

        if search_by == 'quotation_no':
            search_field = 'quotation_no'
            search_value = quotation_no
        elif search_by == 'customer_id':
            # Use the selected_customer_id if available, otherwise try to derive from name
            if selected_customer_id:
                search_field = 'customer_id'
                search_value = selected_customer_id
            elif customer_name_input:
                # Fallback if ID not explicitly passed, try to look up customer.
                # This should ideally be handled client-side or with robust autocomplete.
                try:
                    customer = Customer.objects.get(
                        Q(customer_name__iexact=customer_name_input) |
                        Q(customer_id__iexact=Customer.get_customer_code_from_display(customer_name_input)),
                        company_id=company_id
                    )
                    search_field = 'customer_id'
                    search_value = customer.customer_id
                except Customer.DoesNotExist:
                    search_field = None # No matching customer found
                    search_value = None

        # Use the custom manager method to fetch data
        queryset = Quotation.objects.get_quotations_for_approval_list(
            company_id=company_id,
            financial_year=financial_year,
            search_field=search_field,
            search_value=search_value
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the form back to re-render it with current search values if needed
        context['search_form'] = QuotationSearchForm(self.request.GET)
        return context


class QuotationApproveSelectedView(LoginRequiredMixin, View):
    """
    Handles the bulk approval of selected quotations.
    This view receives a POST request via HTMX.
    """
    def post(self, request, *args, **kwargs):
        user_context = get_user_context(self.request)
        company_id = user_context['company_id']
        approved_by_username = user_context['username']

        # Get selected quotation IDs from the POST data
        # Checkbox names are 'selected_quotations' + id. Example: selected_quotations_123
        # Or simply, if 'CK' is the name of the checkbox, it would be request.POST.getlist('CK')
        selected_quotation_ids = [
            int(key.split('_')[-1]) for key, value in request.POST.items()
            if key.startswith('selected_quotations_') and value == 'on'
        ]

        if not selected_quotation_ids:
            messages.warning(request, "No quotations selected for approval.")
            # Trigger a refresh of the table to show the message
            return HttpResponse(
                status=200, # OK, but no content. HTMX handles the refresh.
                headers={'HX-Trigger': 'refreshQuotationList'}
            )

        # Use the model manager method to perform the bulk update
        updated_count = Quotation.objects.approve_selected_quotations(
            quotation_ids=selected_quotation_ids,
            approved_by_username=approved_by_username,
            company_id=company_id
        )

        if updated_count > 0:
            messages.success(request, f"{updated_count} quotation(s) approved successfully.")
        else:
            messages.info(request, "No new quotations were approved (they might have been already approved or not found).")

        # HTMX standard for successful POST that causes a refresh of another element
        return HttpResponse(
            status=204, # No Content, successful processing
            headers={'HX-Trigger': 'refreshQuotationList'}
        )


class CustomerAutocompleteView(LoginRequiredMixin, View):
    """
    Provides customer name and ID suggestions for the autocomplete input.
    Replicates the GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        user_context = get_user_context(self.request)
        company_id = user_context['company_id']

        # Filter customers by prefix and company ID
        customers = Customer.objects.filter(
            Q(customer_name__icontains=prefix_text) | Q(customer_id__icontains=prefix_text),
            company_id=company_id
        ).values('customer_id', 'customer_name')[:10] # Limit results similar to AJAX toolkit

        suggestions = []
        for customer in customers:
            suggestions.append({
                'value': f"{customer['customer_name']} [{customer['customer_id']}]",
                'id': customer['customer_id']
            })

        return JsonResponse({'suggestions': suggestions})


class QuotationDetailView(LoginRequiredMixin, View):
    """
    Placeholder for the 'View' link, which redirected to a print page.
    In Django, this would be a detailed view, potentially rendering a PDF.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            quotation = Quotation.objects.get(id=pk)
            # You might need to retrieve EnqId and CustomerId similar to the ASP.NET redirect
            # For now, just rendering a basic page.
            # In production, this would likely render a print-ready template or generate a PDF.
            context = {
                'quotation': quotation
            }
            return render(request, 'quotations/quotation/detail.html', context)
        except Quotation.DoesNotExist:
            messages.error(request, "Quotation not found.")
            return redirect(reverse_lazy('quotations:quotation_list'))

```

#### 4.4 Templates

**Task:** Create templates for the list view, the DataTables partial, the search form partial, and a placeholder for the detail view.

**Instructions:**
All templates will extend `core/base.html`. HTMX and Alpine.js will be used for interactivity.

**File: `quotations/templates/quotations/quotation/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Quotation Approve{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-3">Quotation Approve</h2>

        <div x-data="{ searchBy: 'quotation_no' }" class="mb-6">
            {% include 'quotations/quotation/_search_form.html' with search_form=search_form %}
        </div>
        
        <form id="quotationApprovalForm" hx-post="{% url 'quotations:quotation_approve_selected' %}" hx-swap="none">
            {% csrf_token %}
            <!-- This hidden input will be populated by Alpine.js based on checked checkboxes -->
            <!-- We will capture individual checkbox states directly via hx-post -->

            <div id="quotationTable-container"
                 hx-trigger="load, refreshQuotationList from:body"
                 hx-get="{% url 'quotations:quotation_table' %}"
                 hx-target="this"
                 hx-swap="innerHTML"
                 class="relative min-h-[200px] flex items-center justify-center bg-gray-50 rounded-lg">
                <!-- DataTable will be loaded here via HTMX -->
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading quotations...</p>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end">
                <button 
                    type="submit" 
                    id="approveButton"
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-colors duration-200"
                    onclick="return confirm('Are you sure you want to approve the selected quotations?');">
                    Approved Selected
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal for autocomplete results (if needed, or display inline) -->
<div id="customer-autocomplete-results" class="autocomplete-results">
    <!-- Autocomplete results will be loaded here -->
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('quotationApprovalData', () => ({
            searchBy: 'quotation_no',
            init() {
                // Initialize searchBy based on form data if present
                const searchByParam = new URLSearchParams(window.location.search).get('search_by');
                if (searchByParam) {
                    this.searchBy = searchByParam;
                }
            }
        }));

        Alpine.data('customerAutocomplete', () => ({
            selectedCustomer: null,
            selectCustomer(id, value) {
                document.getElementById('id_selected_customer_id').value = id;
                document.getElementById('id_customer_search').value = value;
                document.getElementById('customer-autocomplete-results').innerHTML = ''; // Clear results
            }
        }));
    });

    // Event listener for HTMX after swap to initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'quotationTable-container') {
            $('#quotationTable').DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "language": {
                    "emptyTable": "No data to display !" // Matches ASP.NET empty template
                }
            });
        }
    });

    // Autocomplete result selection logic (if using custom HTMX for autocomplete results)
    document.body.addEventListener('click', function(event) {
        if (event.target.closest('.autocomplete-suggestion')) {
            const suggestion = event.target.closest('.autocomplete-suggestion');
            const customerId = suggestion.dataset.customerId;
            const customerName = suggestion.dataset.customerName; // Assuming data attributes store name
            const customerDisplay = suggestion.textContent;

            document.getElementById('id_selected_customer_id').value = customerId;
            document.getElementById('id_customer_search').value = customerDisplay;
            document.getElementById('customer-autocomplete-results').innerHTML = '';
        }
    });

    // Clear autocomplete results when input loses focus
    document.getElementById('id_customer_search').addEventListener('blur', function() {
        setTimeout(() => { // Small delay to allow click on results
            document.getElementById('customer-autocomplete-results').innerHTML = '';
        }, 100);
    });

</script>
{% endblock %}
```

**File: `quotations/templates/quotations/quotation/_search_form.html`** (Partial for search)

```html
<div x-data="quotationApprovalData" class="flex flex-col md:flex-row items-center gap-4 mb-4">
    <div class="w-full md:w-auto">
        <label for="{{ search_form.search_by.id_for_label }}" class="sr-only">Search By:</label>
        {{ search_form.search_by }}
    </div>
    
    <div class="w-full md:w-auto flex-grow">
        <label for="{{ search_form.quotation_no_search.id_for_label }}" class="sr-only">Quotation No:</label>
        {{ search_form.quotation_no_search }}
        
        <label for="{{ search_form.customer_search.id_for_label }}" class="sr-only">Customer:</label>
        {{ search_form.customer_search }}
        {{ search_form.selected_customer_id }} {# Hidden field for customer ID #}
        
        <div id="customer-autocomplete-indicator" class="htmx-indicator ml-2">
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </div>
        
        <div id="customer-autocomplete-results" 
             class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full max-w-sm mt-1 overflow-y-auto max-h-60"
             x-show="searchBy === 'customer_id' && document.getElementById('customer-autocomplete-results').children.length > 0">
            <!-- Autocomplete results will be loaded here by HTMX -->
        </div>
    </div>
    
    <div class="w-full md:w-auto">
        <button 
            type="button" 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm w-full md:w-auto"
            hx-get="{% url 'quotations:quotation_table' %}"
            hx-target="#quotationTable-container"
            hx-swap="innerHTML"
            hx-trigger="click"
            hx-include="[name='search_by'], [name='quotation_no_search'], [name='customer_search'], [name='selected_customer_id']">
            Search
        </button>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('quotationApprovalData', () => ({
            searchBy: '{{ search_form.search_by.value|default:"quotation_no" }}', // Initialize from current value
            init() {
                this.$watch('searchBy', value => {
                    // Reset inputs when search type changes
                    if (value === 'quotation_no') {
                        document.getElementById('id_customer_search').value = '';
                        document.getElementById('id_selected_customer_id').value = '';
                    } else {
                        document.getElementById('id_quotation_no_search').value = '';
                    }
                });
            }
        }));
    });
</script>
```

**File: `quotations/templates/quotations/quotation/_table.html`** (Partial for DataTables content)

```html
{% load humanize %} {# Optional: for better date formatting if needed #}

<table id="quotationTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th> {# Renamed from "View" #}
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Checked Date</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For Approve</th>
            <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized Date</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if quotations %}
            {% for quotation in quotations %}
            <tr>
                <td class="py-2 px-4 border-b text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b text-center">{{ quotation.fin_year }}</td>
                <td class="py-2 px-4 border-b text-center">{{ quotation.quotation_no }}</td>
                <td class="py-2 px-4 border-b text-center">
                    <a href="{% url 'quotations:quotation_detail' quotation.id %}" 
                       class="text-blue-600 hover:text-blue-800 hover:underline">View</a>
                </td>
                <td class="py-2 px-4 border-b text-center">{{ quotation.sys_date|date:"d-M-Y" }}</td>
                <td class="py-2 px-4 border-b text-left">{{ quotation.generated_by_employee_name }}</td>
                <td class="py-2 px-4 border-b text-left">{{ quotation.customer_name }}</td>
                <td class="py-2 px-4 border-b text-center">{{ quotation.customer_id.customer_id }}</td> {# Accessing FK object #}
                <td class="py-2 px-4 border-b text-center">{{ quotation.checked_date|date:"d-M-Y"|default_if_none:"" }}</td>
                <td class="py-2 px-4 border-b text-center">
                    {% if quotation.can_be_approved %} {# Using model method to hide checkbox #}
                        <input type="checkbox" name="selected_quotations_{{ quotation.id }}" class="form-checkbox h-4 w-4 text-green-600 rounded">
                    {% else %}
                        <span class="text-gray-500">{{ quotation.approved_date|date:"d-M-Y"|default_if_none:"" }}</span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b text-center">{{ quotation.authorized_date|date:"d-M-Y"|default_if_none:"" }}</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="11" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>
```

**File: `quotations/templates/quotations/quotation/detail.html`** (Placeholder for view link)

```html
{% extends 'core/base.html' %}

{% block title %}Quotation Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-3">Quotation Details: #{{ quotation.quotation_no }}</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            <div><strong>ID:</strong> {{ quotation.id }}</div>
            <div><strong>Fin Year:</strong> {{ quotation.fin_year }}</div>
            <div><strong>Quotation No:</strong> {{ quotation.quotation_no }}</div>
            <div><strong>Date:</strong> {{ quotation.sys_date|date:"d M Y" }}</div>
            <div><strong>Generated By:</strong> {{ quotation.generated_by_employee_name }}</div>
            <div><strong>Customer Name:</strong> {{ quotation.customer_name }}</div>
            <div><strong>Customer ID:</strong> {{ quotation.customer_id.customer_id }}</div>
            <div><strong>Checked:</strong> {% if quotation.is_checked %}Yes{% else %}No{% endif %}</div>
            <div><strong>Checked Date:</strong> {{ quotation.checked_date|date:"d M Y"|default_if_none:"N/A" }}</div>
            <div><strong>Approved:</strong> {% if quotation.is_approved %}Yes{% else %}No{% endif %}</div>
            <div><strong>Approved By:</strong> {{ quotation.approved_by_username|default_if_none:"N/A" }}</div>
            <div><strong>Approved Date:</strong> {{ quotation.approved_date|date:"d M Y"|default_if_none:"N/A" }}</div>
            <div><strong>Approved Time:</strong> {{ quotation.approved_time|date:"H:i"|default_if_none:"N/A" }}</div>
            <div><strong>Authorized Date:</strong> {{ quotation.authorized_date|date:"d M Y"|default_if_none:"N/A" }}</div>
            <div><strong>Company ID:</strong> {{ quotation.company_id.comp_id }}</div>
            <div><strong>Enquiry ID:</strong> {{ quotation.enquiry_id|default_if_none:"N/A" }}</div>
        </div>

        <div class="mt-8">
            <a href="{% url 'quotations:quotation_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm">Back to List</a>
            {# Example: Link to print functionality if available #}
            {# <a href="{% url 'quotations:quotation_print' quotation.id %}" class="ml-4 bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-md shadow-sm">Print Quotation</a> #}
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs will be defined within the `quotations` app, then included in the project's main `urls.py`.

**File: `quotations/urls.py`**

```python
from django.urls import path
from .views import (
    QuotationListView,
    QuotationTablePartialView,
    QuotationApproveSelectedView,
    CustomerAutocompleteView,
    QuotationDetailView
)

app_name = 'quotations' # Namespace for URLs

urlpatterns = [
    path('quotation/', QuotationListView.as_view(), name='quotation_list'),
    path('quotation/table/', QuotationTablePartialView.as_view(), name='quotation_table'),
    path('quotation/approve_selected/', QuotationApproveSelectedView.as_view(), name='quotation_approve_selected'),
    path('customer/autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    path('quotation/<int:pk>/detail/', QuotationDetailView.as_view(), name='quotation_detail'),
]
```

**Project `urls.py` (Example, assuming `quotations` app is created):**

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('sales/', include('quotations.urls')), # Main entry point for sales modules
    # path('', include('core.urls')), # Assuming a core app for base templates/dashboard
]
```

#### 4.6 Tests

**Task:** Write tests for the models and views to ensure functionality and coverage.

**Instructions:**
Include comprehensive unit tests for model methods and properties, and integration tests for all views.

**File: `quotations/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Quotation, Customer, Company
from .views import get_user_context # Import the mock user context

# Mock the user context for tests
def mock_get_user_context(request):
    return {
        'company_id': 1,
        'financial_year': '2023-2024',
        'username': 'test_user'
    }
get_user_context = mock_get_user_context # Override the function for tests

class QuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a mock company for FK
        cls.company = Company.objects.create(comp_id=1, comp_name="Test Company")
        # Create test customer for FK
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer 1', company_id=cls.company)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Another Customer', company_id=cls.company)

        # Create test data for Quotation model
        Quotation.objects.create(
            id=101,
            fin_year='2023-2024',
            quotation_no='Q001',
            sys_date=timezone.now().date(),
            generated_by_employee_name='Employee A',
            customer_id=cls.customer1,
            customer_name=cls.customer1.customer_name, # Denormalized field
            is_checked=True,
            checked_date=timezone.now().date(),
            is_approved=False,
            company_id=cls.company,
            enquiry_id='ENQ001'
        )
        Quotation.objects.create(
            id=102,
            fin_year='2023-2024',
            quotation_no='Q002',
            sys_date=timezone.now().date(),
            generated_by_employee_name='Employee B',
            customer_id=cls.customer2,
            customer_name=cls.customer2.customer_name, # Denormalized field
            is_checked=True,
            checked_date=timezone.now().date(),
            is_approved=True, # Already approved
            approved_date=timezone.now().date(),
            approved_by_username='old_approver',
            company_id=cls.company,
            enquiry_id='ENQ002'
        )
        Quotation.objects.create(
            id=103,
            fin_year='2023-2024',
            quotation_no='Q003',
            sys_date=timezone.now().date(),
            generated_by_employee_name='Employee C',
            customer_id=cls.customer1,
            customer_name=cls.customer1.customer_name,
            is_checked=False, # Not checked
            company_id=cls.company,
            enquiry_id='ENQ003'
        )

    def test_quotation_creation(self):
        quotation = Quotation.objects.get(id=101)
        self.assertEqual(quotation.quotation_no, 'Q001')
        self.assertEqual(quotation.customer_id, self.customer1)
        self.assertFalse(quotation.is_approved)
        self.assertTrue(quotation.is_checked)

    def test_quotation_verbose_name(self):
        self.assertEqual(Quotation._meta.verbose_name, 'Quotation')
        self.assertEqual(Quotation._meta.verbose_name_plural, 'Quotations')

    def test_customer_verbose_name(self):
        self.assertEqual(Customer._meta.verbose_name, 'Customer')
        self.assertEqual(Customer._meta.verbose_name_plural, 'Customers')

    def test_can_be_approved_method(self):
        quotation_unapproved = Quotation.objects.get(id=101)
        quotation_approved = Quotation.objects.get(id=102)
        quotation_unchecked = Quotation.objects.get(id=103)

        self.assertTrue(quotation_unapproved.can_be_approved())
        self.assertFalse(quotation_approved.can_be_approved())
        self.assertFalse(quotation_unchecked.can_be_approved())

    def test_get_quotations_for_approval_list(self):
        # Test filtering by default conditions (checked=1)
        quotations = Quotation.objects.get_quotations_for_approval_list(
            company_id=self.company.comp_id,
            financial_year='2023-2024'
        )
        self.assertEqual(quotations.count(), 2) # Q001, Q002 (Q003 is unchecked)
        self.assertIn(Quotation.objects.get(id=101), quotations)
        self.assertIn(Quotation.objects.get(id=102), quotations)

        # Test filtering by quotation_no
        quotations_q001 = Quotation.objects.get_quotations_for_approval_list(
            company_id=self.company.comp_id,
            financial_year='2023-2024',
            search_field='quotation_no',
            search_value='Q001'
        )
        self.assertEqual(quotations_q001.count(), 1)
        self.assertEqual(quotations_q001.first().quotation_no, 'Q001')

        # Test filtering by customer_id
        quotations_cust001 = Quotation.objects.get_quotations_for_approval_list(
            company_id=self.company.comp_id,
            financial_year='2023-2024',
            search_field='customer_id',
            search_value='CUST001'
        )
        self.assertEqual(quotations_cust001.count(), 1) # Q001 is checked and CUST001
        self.assertEqual(quotations_cust001.first().customer_id, self.customer1)

    def test_approve_selected_quotations(self):
        # Approve Q001
        initial_quotation = Quotation.objects.get(id=101)
        self.assertFalse(initial_quotation.is_approved)

        updated_count = Quotation.objects.approve_selected_quotations(
            quotation_ids=[101],
            approved_by_username='new_approver',
            company_id=self.company.comp_id
        )
        self.assertEqual(updated_count, 1)

        approved_quotation = Quotation.objects.get(id=101)
        self.assertTrue(approved_quotation.is_approved)
        self.assertEqual(approved_quotation.approved_by_username, 'new_approver')
        self.assertEqual(approved_quotation.approved_date, timezone.now().date())

        # Try to approve already approved quotation (Q002) - should return 0 updated
        updated_count_already_approved = Quotation.objects.approve_selected_quotations(
            quotation_ids=[102],
            approved_by_username='another_approver',
            company_id=self.company.comp_id
        )
        self.assertEqual(updated_count_already_approved, 0)
        self.assertTrue(Quotation.objects.get(id=102).is_approved) # Still approved
        self.assertEqual(Quotation.objects.get(id=102).approved_by_username, 'old_approver') # Not changed


class QuotationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup similar test data for views
        cls.company = Company.objects.create(comp_id=1, comp_name="Test Company")
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer 1', company_id=cls.company)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Another Customer', company_id=cls.company)
        cls.quotation1 = Quotation.objects.create(
            id=201, fin_year='2023-2024', quotation_no='Q004', sys_date=timezone.now().date(),
            generated_by_employee_name='Employee D', customer_id=cls.customer1, customer_name=cls.customer1.customer_name,
            is_checked=True, checked_date=timezone.now().date(), is_approved=False, company_id=cls.company, enquiry_id='ENQ004'
        )
        cls.quotation2 = Quotation.objects.create(
            id=202, fin_year='2023-2024', quotation_no='Q005', sys_date=timezone.now().date(),
            generated_by_employee_name='Employee E', customer_id=cls.customer2, customer_name=cls.customer2.customer_name,
            is_checked=True, checked_date=timezone.now().date(), is_approved=False, company_id=cls.company, enquiry_id='ENQ005'
        )
        cls.quotation_approved = Quotation.objects.create(
            id=203, fin_year='2023-2024', quotation_no='Q006', sys_date=timezone.now().date(),
            generated_by_employee_name='Employee F', customer_id=cls.customer1, customer_name=cls.customer1.customer_name,
            is_checked=True, checked_date=timezone.now().date(), is_approved=True, company_id=cls.company, enquiry_id='ENQ006'
        )

    def setUp(self):
        self.client = Client()
        # Mock login for LoginRequiredMixin
        self.client.force_login(self.get_or_create_user())

    def get_or_create_user(self):
        # Create a dummy user for authentication
        from django.contrib.auth.models import User
        user, created = User.objects.get_or_create(username='testuser')
        if created:
            user.set_password('password')
            user.save()
        return user

    def test_quotation_list_view_get(self):
        response = self.client.get(reverse('quotations:quotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/quotation/list.html')
        self.assertContains(response, 'Quotation Approve')
        self.assertContains(response, 'id="quotationTable-container"') # Check for HTMX target div

    def test_quotation_table_partial_view_get(self):
        response = self.client.get(reverse('quotations:quotation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/quotation/_table.html')
        self.assertContains(response, 'Q004') # Check for unapproved quotation
        self.assertContains(response, 'Q005') # Check for unapproved quotation
        self.assertContains(response, 'Q006') # Check for approved quotation
        
        # Ensure checkbox is visible for unapproved, hidden for approved
        self.assertContains(response, f'name="selected_quotations_{self.quotation1.id}"')
        self.assertNotContains(response, f'name="selected_quotations_{self.quotation_approved.id}"')
        self.assertContains(response, self.quotation_approved.approved_date.strftime("%d-%b-%Y"))

    def test_quotation_table_partial_view_get_with_quotation_no_search(self):
        response = self.client.get(reverse('quotations:quotation_table'), {'search_by': 'quotation_no', 'quotation_no_search': 'Q004'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Q004')
        self.assertNotContains(response, 'Q005')

    def test_quotation_table_partial_view_get_with_customer_search(self):
        response = self.client.get(reverse('quotations:quotation_table'), {'search_by': 'customer_id', 'customer_search': 'Test Customer 1 [CUST001]', 'selected_customer_id': 'CUST001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Q004')
        self.assertContains(response, 'Q006')
        self.assertNotContains(response, 'Q005')

    def test_quotation_approve_selected_view_post_success(self):
        initial_approved_status = Quotation.objects.get(id=self.quotation1.id).is_approved
        self.assertFalse(initial_approved_status)

        response = self.client.post(reverse('quotations:quotation_approve_selected'), {
            f'selected_quotations_{self.quotation1.id}': 'on',
            f'selected_quotations_{self.quotation2.id}': 'on',
        }, HTTP_HX_Request='true') # Simulate HTMX request

        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')

        # Verify database update
        self.assertTrue(Quotation.objects.get(id=self.quotation1.id).is_approved)
        self.assertTrue(Quotation.objects.get(id=self.quotation2.id).is_approved)

    def test_quotation_approve_selected_view_post_no_selection(self):
        response = self.client.post(reverse('quotations:quotation_approve_selected'), {}, HTTP_HX_Request='true')
        self.assertEqual(response.status_code, 200) # Returns OK with messages for no selection
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('quotations:customer_autocomplete'), {'q': 'Test'}, HTTP_HX_Request='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertTrue('suggestions' in data)
        self.assertGreater(len(data['suggestions']), 0)
        self.assertEqual(data['suggestions'][0]['value'], 'Test Customer 1 [CUST001]')
        self.assertEqual(data['suggestions'][0]['id'], 'CUST001')

    def test_quotation_detail_view(self):
        response = self.client.get(reverse('quotations:quotation_detail', args=[self.quotation1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotations/quotation/detail.html')
        self.assertContains(response, f'Quotation Details: #{self.quotation1.quotation_no}')
        self.assertContains(response, self.quotation1.customer_name)

    def test_quotation_detail_view_not_found(self):
        response = self.client.get(reverse('quotations:quotation_detail', args=[9999999]))
        self.assertEqual(response.status_code, 302) # Redirects to list
        self.assertRedirects(response, reverse('quotations:quotation_list'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated code heavily relies on HTMX and Alpine.js.

*   **HTMX for dynamic updates:**
    *   The main list page (`list.html`) uses `hx-get` to load the `_table.html` content into `quotationTable-container` on `load` and `refreshQuotationList` events.
    *   The "Search" button uses `hx-get` to re-fetch the table content, including the search form data.
    *   The "Approved Selected" button uses `hx-post` to submit the form data to `quotation_approve_selected` view.
    *   The `quotation_approve_selected` view returns `HX-Trigger: refreshQuotationList` to automatically refresh the table after approval.
    *   Customer autocomplete uses `hx-get` with `keyup changed delay:500ms` to fetch suggestions dynamically.

*   **Alpine.js for UI state management:**
    *   `x-data` on the search form container (`list.html`) to manage `searchBy` variable, which toggles the visibility of `quotation_no_search` and `customer_search` input fields using `x-show`. This directly replaces the ASP.NET `drpfield_SelectedIndexChanged` logic.
    *   Alpine.js can also be used for modal management if adding create/edit forms in modals later.

*   **DataTables for list views:**
    *   The `_table.html` partial includes a `script` block to initialize `$('#quotationTable').DataTable()` after it's loaded into the DOM by HTMX. The `htmx:afterSwap` event listener ensures this.
    *   DataTables provides client-side searching, sorting, and pagination, replacing the `GridView`'s built-in functionality.

*   **No full page reloads:** All search, approval, and table updates are handled asynchronously via HTMX, providing a smooth user experience without full page reloads.

*   **DRY Template Inheritance:** All templates (`list.html`, `_table.html`, `detail.html`) extend `core/base.html` to inherit the basic structure, CSS (Tailwind), and common JavaScript libraries (HTMX, Alpine.js, jQuery, DataTables CDN links in `base.html`'s `extra_js` block).

*   **Separation of Concerns:** Business logic for filtering and approving quotations resides within the `QuotationManager` and `Quotation` model methods. Views remain thin, primarily handling HTTP request/response, form validation, and delegating complex logic to the models.

## Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Quotation Approval module to Django. By following these steps and leveraging the power of AI-assisted automation, your team can achieve a modern, maintainable, and performant application. Remember to replace placeholder values (e.g., `company_id`, `financial_year`, `username` in `get_user_context`) with actual dynamic values from your Django authentication and session management system in a production environment. Additional integrations might be needed for the `Quotation_Print_Details.aspx` redirect, potentially involving a Django view that generates a PDF or routes to an external reporting service.