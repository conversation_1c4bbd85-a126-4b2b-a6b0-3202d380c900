## ASP.NET to Django Conversion Script:

This modernization plan outlines the strategic transition of your legacy ASP.NET application, specifically the `CustPO_Print.aspx` module, to a modern Django-based solution. Our focus is on leveraging AI-assisted automation, adhering to a "fat model, thin view" architecture, and utilizing cutting-edge frontend technologies like HTMX and Alpine.js for highly interactive user experiences without traditional JavaScript frameworks. This approach minimizes manual coding, improves maintainability, and provides significant business value through enhanced performance, scalability, and developer efficiency.

The `CustPO_Print` module, which serves as a customer purchase order search and listing interface with file download capabilities, will be refactored into a dedicated Django application within your overall ERP system.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several database tables to retrieve and display customer purchase order information.

*   **Primary Table:** `SD_Cust_PO_Master` (Customer Purchase Order Master)
    *   **Columns Inferred:**
        *   `PONo`: (String) Purchase Order Number
        *   `POId`: (Integer) Primary key for Customer PO
        *   `EnqId`: (String) Enquiry ID
        *   `CustomerId`: (String) Foreign key to `SD_Cust_Master`
        *   `SessionId`: (Integer) Foreign key to `tblHR_OfficeStaff` (Employee ID who generated)
        *   `FinYearId`: (Integer) Foreign key to `tblFinancial_master`
        *   `PODate`: (String/Date) Purchase Order Date (stored as string, converted in C#)
        *   `SysDate`: (String/DateTime) System Date (when record was created/last modified, stored as string, converted in C#)
        *   `FileName`: (String) Name of the attached file
        *   `FileSize`: (Integer) Size of the attached file
        *   `ContentType`: (String) MIME type of the attached file
        *   `FileData`: (Binary) Actual binary content of the attached file
        *   `CompId`: (Integer) Company ID (used in WHERE clauses, likely a global context/session variable)

*   **Lookup Tables:**
    *   `SD_Cust_Master` (Customer Master)
        *   **Columns Inferred:** `CustomerId` (String), `CustomerName` (String), `CompId` (Integer)
    *   `tblFinancial_master` (Financial Year Master)
        *   **Columns Inferred:** `FinYearId` (Integer), `FinYear` (String)
    *   `tblHR_OfficeStaff` (HR Office Staff/Employee)
        *   **Columns Inferred:** `EmpId` (Integer), `Title` (String), `EmployeeName` (String), `CompId` (Integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core business logic in the ASP.NET code.

**Analysis:**
The `CustPO_Print` page primarily performs **Read (Search & List)** and **File Download** operations, along with navigation to a separate details page.

*   **Read Operation (Search & List):**
    *   **Data Source:** `SD_Cust_PO_Master` joined with `SD_Cust_Master`, `tblFinancial_master`, and `tblHR_OfficeStaff` to enrich data.
    *   **Filtering:**
        *   Based on `FinYearId` and `CompId` from session variables (global context).
        *   Dynamic filtering based on `DropDownList1` selection:
            *   `Customer Name` (`DropDownList1.SelectedValue == "0"`): Searches `SD_Cust_Master` by `CustomerName` and then uses the `CustomerId` to filter `SD_Cust_PO_Master`. The `AutoCompleteExtender` is used here.
            *   `Enquiry No` (`DropDownList1.SelectedValue == "1"`): Filters `SD_Cust_PO_Master` by `EnqId`.
            *   `PO No` (`DropDownList1.SelectedValue == "2"`): Filters `SD_Cust_PO_Master` by `PONo`.
    *   **Sorting:** Implicitly by `POId Desc` in the `strCustpo` query.
    *   **Pagination:** Handled by `GridView` (server-side).
    *   **Data Transformation:** Date fields (`PODate`, `SysDate`) are converted from specific string formats to displayable dates. The `FileName` field is conditional ("Download" or empty).

*   **File Download:**
    *   Triggered by `downloadImg` `LinkButton` command on the `GridView`.
    *   Redirects to a generic `DownloadFile.aspx` with parameters (`Id`, `tbl`, `qfd`, `qfn`, `qct`) to fetch and serve a file from the database. This suggests a dedicated file serving mechanism.

*   **Navigation to Details:**
    *   Triggered by `sel` `LinkButton` command on the `GridView` (PO No).
    *   Redirects to `CustPO_Print_Details.aspx` with multiple query parameters (`CustomerId`, `EnqId`, `PONo`, `POId`, `Key`, `ModId`, `SubModId`).

*   **Autocomplete/Lookup:**
    *   The `sql` web method provides `CustomerName` and `CustomerId` from `SD_Cust_master` for the `TxtSearchValue` textbox. It processes `prefixText` for filtering.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Search/Filter Area:**
    *   `DropDownList1`: Acts as a search criteria selector. Its `AutoPostBack` suggests a dynamic update of the search input fields.
    *   `txtEnqId`: Textbox for `Enquiry No` or `PO No` input.
    *   `TxtSearchValue`: Textbox for `Customer Name` input, enhanced with an `AutoCompleteExtender`.
    *   `btnSearch`: Button to trigger the search.
*   **Data Display:**
    *   `SearchGridView1`: The primary data display component, presenting a paginated, sortable list of POs. It's configured to use `yui-datatable-theme`, implying a visual styling similar to a data table.
*   **Actions within Grid:**
    *   `LinkButton` for `PO No`: Triggers navigation to a PO details page.
    *   `LinkButton` for `File`: Triggers file download.

---

### Step 4: Generate Django Code

We will create a new Django app named `salesdistribution` to encapsulate this functionality.

#### 4.1 Models (`salesdistribution/models.py`)

We'll define Django models that map directly to the existing database tables.

```python
from django.db import models

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"ID: {self.fin_year_id}"

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee details.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId is also here

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name or ''}".strip() or f"ID: {self.emp_id}"

class Customer(models.Model):
    """
    Maps to SD_Cust_Master for customer details.
    """
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) # Assuming it's varchar
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId is also here

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name or f"ID: {self.customer_id}"
    
    def get_code_from_name(cls, customer_name_with_code):
        """
        Extracts CustomerId from a string like "Customer Name [CustomerId]".
        This mimics the fun.getCode functionality.
        """
        if '[' in customer_name_with_code and ']' in customer_name_with_code:
            start = customer_name_with_code.rfind('[') + 1
            end = customer_name_with_code.rfind(']')
            if start != -1 and end != -1 and start < end:
                return customer_name_with_code[start:end]
        return customer_name_with_code # Return as-is if format not matched, or handle error

    # No specific business logic needed beyond data retrieval for this specific module
    # The get_code_from_name method is more of a utility for search parsing.


class CustPOMaster(models.Model):
    """
    Maps to SD_Cust_PO_Master for Customer Purchase Order details.
    """
    po_id = models.IntegerField(db_column='POId', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customer_id = models.CharField(db_column='CustomerId', max_length=50, blank=True, null=True) # Link via CharField
    session_id = models.IntegerField(db_column='SessionId', blank=True, null=True) # Link via IntegerField
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True) # Link via IntegerField
    po_date = models.CharField(db_column='PODate', max_length=20, blank=True, null=True) # Stored as string in DB
    sys_date = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True) # Stored as string in DB
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.IntegerField(db_column='FileSize', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Customer PO'
        verbose_name_plural = 'Customer POs'
        ordering = ['-po_id'] # Order by POId Desc as per ASP.NET

    def __str__(self):
        return self.po_no or f"PO ID: {self.po_id}"

    @property
    def customer_name(self):
        """Retrieves the customer name using CustomerId."""
        try:
            return Customer.objects.get(customer_id=self.customer_id, comp_id=self.comp_id).customer_name
        except Customer.DoesNotExist:
            return "N/A"

    @property
    def financial_year(self):
        """Retrieves the financial year string using FinYearId."""
        try:
            return FinancialYear.objects.get(fin_year_id=self.fin_year_id).fin_year
        except FinancialYear.DoesNotExist:
            return "N/A"

    @property
    def generated_by_employee_name(self):
        """Retrieves the employee name using SessionId (EmpId)."""
        try:
            emp = Employee.objects.get(emp_id=self.session_id, comp_id=self.comp_id)
            return f"{emp.title or ''} {emp.employee_name or ''}".strip()
        except Employee.DoesNotExist:
            return "N/A"

    @property
    def display_file_name(self):
        """Returns 'Download' if file exists, otherwise empty string."""
        return "Download" if self.file_name else ""

    def get_absolute_url(self):
        """URL for viewing details - mimicking the 'sel' command redirect."""
        # This will need to point to your new Django PO details view
        # Example: return reverse('salesdistribution:custpo_details', kwargs={'pk': self.po_id})
        # For now, it will return a placeholder
        return f"/salesdistribution/custpo/{self.po_id}/details/?customer_id={self.customer_id}&enq_id={self.enq_id}&po_no={self.po_no}"
```

#### 4.2 Forms (`salesdistribution/forms.py`)

We need a form for the search criteria. Since it's not directly mapping to a model instance creation/update, we'll use a regular `forms.Form`.

```python
from django import forms
from .models import Customer

class POSearchForm(forms.Form):
    SEARCH_BY_CHOICES = [
        ('select', 'Select'), # Mimics "Select" option in ASP.NET
        ('customer_name', 'Customer Name'),
        ('enquiry_no', 'Enquiry No'),
        ('po_no', 'PO No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-get': '/salesdistribution/custpo/search-inputs/', 'hx-target': '#search-inputs-container', 'hx-swap': 'outerHTML'}),
        label="Search By"
    )
    
    # These fields will be dynamically shown/hidden by HTMX
    search_value = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Enter Customer Name or PO No...',
            # HTMX for autocomplete for customer name
            'hx-get': '/salesdistribution/customer-autocomplete/',
            'hx-trigger': 'keyup changed delay:300ms, search',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#autocomplete-spinner',
            'autocomplete': 'off' # Disable browser autocomplete
        }),
        label="Search Value"
    )

    enquiry_po_value = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Enter Enquiry No or PO No...'
        }),
        label="Enquiry/PO Value"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initially hide the specific search fields, they will be controlled by HTMX
        # Or control visibility in the template directly based on `search_by`
        self.fields['search_value'].widget.attrs['class'] += ' hidden' # Default to hidden
        self.fields['enquiry_po_value'].widget.attrs['class'] += ' hidden' # Default to hidden

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        search_value = cleaned_data.get('search_value')
        enquiry_po_value = cleaned_data.get('enquiry_po_value')

        if search_by == 'customer_name' and not search_value:
            self.add_error('search_value', 'Customer Name is required for this search type.')
        elif (search_by == 'enquiry_no' or search_by == 'po_no') and not enquiry_po_value:
            self.add_error('enquiry_po_value', 'Enquiry No or PO No is required for this search type.')
        
        return cleaned_data
```

#### 4.3 Views (`salesdistribution/views.py`)

We'll define class-based views for listing, handling search inputs dynamically, serving the table, downloading files, and providing autocomplete data.

```python
import os
from django.views.generic import ListView, View
from django.http import HttpResponse, JsonResponse, Http404
from django.urls import reverse_lazy
from django.shortcuts import render
from django.db.models import Q
from django.core.paginator import Paginator
import re # For parsing customer ID from autocomplete string

from .models import CustPOMaster, Customer
from .forms import POSearchForm

# Assume CompId and FinYearId are available from a session or user profile
# For demonstration, we'll hardcode or retrieve from session placeholder
def get_user_context_vars(request):
    """Placeholder to retrieve CompId and FinYearId from session/user profile."""
    # In a real ERP, these would come from the logged-in user's session or profile
    # Example:
    # try:
    #     comp_id = request.session.get('compid', 1) # Default to 1
    #     fin_year_id = request.session.get('finyear', 1) # Default to 1
    # except AttributeError: # For testing outside of request context
    #     comp_id = 1
    #     fin_year_id = 1
    return {'comp_id': 1, 'fin_year_id': 1} # Placeholder

class CustPOPrintView(ListView):
    model = CustPOMaster
    template_name = 'salesdistribution/custpomaster/list.html'
    context_object_name = 'custpomasters'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        # Initial queryset filtering by user context
        user_context = get_user_context_vars(self.request)
        queryset = super().get_queryset().filter(
            comp_id=user_context['comp_id'],
            fin_year_id__lte=user_context['fin_year_id'] # ASP.NET uses '<=', so we match
        )

        form = POSearchForm(self.request.GET)
        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            search_value = form.cleaned_data.get('search_value')
            enquiry_po_value = form.cleaned_data.get('enquiry_po_value')

            if search_by == 'customer_name' and search_value:
                # Extract CustomerId from "Customer Name [CustomerId]" format
                customer_id_from_search = None
                match = re.search(r'\[(.*?)\]$', search_value)
                if match:
                    customer_id_from_search = match.group(1)
                else:
                    # Fallback: if no ID in brackets, try to find customer by exact name
                    try:
                        customer = Customer.objects.get(
                            customer_name__iexact=search_value,
                            comp_id=user_context['comp_id']
                        )
                        customer_id_from_search = customer.customer_id
                    except Customer.DoesNotExist:
                        customer_id_from_search = None # No matching customer found

                if customer_id_from_search:
                    queryset = queryset.filter(customer_id=customer_id_from_search)
                else:
                    queryset = queryset.none() # No results if customer not found or ID not extracted

            elif search_by == 'enquiry_no' and enquiry_po_value:
                queryset = queryset.filter(enq_id=enquiry_po_value)
            elif search_by == 'po_no' and enquiry_po_value:
                queryset = queryset.filter(po_no=enquiry_po_value)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = POSearchForm(self.request.GET or None)
        return context

# HTMX partial view for rendering just the search input fields
class SearchInputPartialView(View):
    def get(self, request, *args, **kwargs):
        search_by = request.GET.get('search_by', 'select')
        form = POSearchForm(initial={'search_by': search_by})
        
        # Render the partial based on the selected search_by value
        # This will contain the logic to show/hide the correct input field
        return render(request, 'salesdistribution/custpomaster/_search_inputs.html', {'form': form, 'selected_search_by': search_by})

# HTMX partial view for rendering the DataTables table
class CustPOTablePartialView(ListView):
    model = CustPOMaster
    template_name = 'salesdistribution/custpomaster/_custpo_table.html'
    context_object_name = 'custpomasters'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        # This queryset logic is identical to CustPOPrintView's get_queryset
        # to ensure consistency for HTMX updates
        user_context = get_user_context_vars(self.request)
        queryset = super().get_queryset().filter(
            comp_id=user_context['comp_id'],
            fin_year_id__lte=user_context['fin_year_id']
        )

        form = POSearchForm(self.request.GET)
        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            search_value = form.cleaned_data.get('search_value')
            enquiry_po_value = form.cleaned_data.get('enquiry_po_value')

            if search_by == 'customer_name' and search_value:
                customer_id_from_search = None
                match = re.search(r'\[(.*?)\]$', search_value)
                if match:
                    customer_id_from_search = match.group(1)
                else:
                    try:
                        customer = Customer.objects.get(
                            customer_name__iexact=search_value,
                            comp_id=user_context['comp_id']
                        )
                        customer_id_from_search = customer.customer_id
                    except Customer.DoesNotExist:
                        customer_id_from_search = None

                if customer_id_from_search:
                    queryset = queryset.filter(customer_id=customer_id_from_search)
                else:
                    queryset = queryset.none()

            elif search_by == 'enquiry_no' and enquiry_po_value:
                queryset = queryset.filter(enq_id=enquiry_po_value)
            elif search_by == 'po_no' and enquiry_po_value:
                queryset = queryset.filter(po_no=enquiry_po_value)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # DataTables server-side processing usually sends 'draw', 'start', 'length', 'search[value]', 'order[0][column]', etc.
        # This simple example doesn't fully implement server-side processing for DataTables,
        # but provides the paginated queryset for client-side DataTables initialization.
        # For full server-side processing, integrate a library like django-datatables-view.
        return context

class DownloadFileView(View):
    def get(self, request, pk):
        try:
            po_master = CustPOMaster.objects.get(po_id=pk)
        except CustPOMaster.DoesNotExist:
            raise Http404("File not found.")

        if not po_master.file_data:
            raise Http404("No file attached to this PO.")

        response = HttpResponse(po_master.file_data, content_type=po_master.content_type)
        response['Content-Disposition'] = f'attachment; filename="{po_master.file_name}"'
        response['Content-Length'] = po_master.file_size
        return response

class CustomerAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('term', '') # 'term' is common for autocomplete libraries
        user_context = get_user_context_vars(self.request)
        
        customers = Customer.objects.filter(
            Q(customer_name__icontains=query) | Q(customer_id__icontains=query),
            comp_id=user_context['comp_id']
        )[:10] # Limit results for performance, similar to CompletionSetCount

        results = []
        for customer in customers:
            results.append(f"{customer.customer_name} [{customer.customer_id}]")
        
        return JsonResponse(results, safe=False) # Return as a simple JSON array of strings
```

#### 4.4 Templates (`salesdistribution/templates/salesdistribution/custpomaster/`)

We'll define the main list template and partials for dynamic updates.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer PO - Print</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form id="po-search-form" 
              hx-get="{% url 'salesdistribution:custpo_table' %}" 
              hx-target="#custpo-table-container" 
              hx-swap="innerHTML"
              hx-trigger="submit, keyup changed delay:500ms from:#id_enquiry_po_value, keyup changed delay:500ms from:#id_search_value"
              hx-indicator="#loading-spinner">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="id_search_by" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ form.search_by }}
                </div>
                <div id="search-inputs-container" class="col-span-2">
                    {# This container will be swapped by hx-get on dropdown change #}
                    {# It will initially render with the correct input visible based on context #}
                    {% include 'salesdistribution/custpomaster/_search_inputs.html' with form=form selected_search_by=form.search_by.value %}
                </div>
                <div>
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            <div id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-auto"></div>
            <div id="loading-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div> Loading...
            </div>
        </form>
    </div>

    <div id="custpo-table-container"
         hx-trigger="load, refreshCustPOList from:body"
         hx-get="{% url 'salesdistribution:custpo_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Customer PO data...</p>
        </div>
    </div>
    
    {# Modal for form/delete if needed, not directly used by this specific ASPX page logic #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
    });

    // Event listener for HTMX afterSwap to initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'custpo-table-container') {
            // Re-initialize DataTables after new content is swapped in
            if ($.fn.DataTable.isDataTable('#custpoTable')) {
                $('#custpoTable').DataTable().destroy();
            }
            $('#custpoTable').DataTable({
                "pageLength": 20, // Matches ASP.NET PageSize
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Filter results:",
                    "lengthMenu": "Show _MENU_ entries"
                }
            });
        }
    });

    // Alpine.js for autocomplete results and selection
    document.addEventListener('DOMContentLoaded', () => {
        const searchInput = document.getElementById('id_search_value');
        const autocompleteResults = document.getElementById('autocomplete-results');

        // Close autocomplete results when clicking outside
        document.addEventListener('click', (e) => {
            if (!autocompleteResults.contains(e.target) && e.target !== searchInput) {
                autocompleteResults.innerHTML = '';
            }
        });

        // Set search input value on autocomplete item click
        autocompleteResults.addEventListener('click', (e) => {
            if (e.target.tagName === 'DIV') {
                searchInput.value = e.target.textContent;
                autocompleteResults.innerHTML = ''; // Clear results
            }
        });
    });
</script>
{% endblock %}
```

**`_search_inputs.html`** (Partial for dynamic search input fields)

```html
<div id="search-inputs-container">
    {% comment %}
    The form.search_by.value is passed from the view via 'selected_search_by' context variable.
    This partial will be swapped when search_by dropdown changes.
    {% endcomment %}
    {% if selected_search_by == 'customer_name' %}
        <label for="id_search_value" class="block text-sm font-medium text-gray-700">Customer Name</label>
        {{ form.search_value }}
    {% elif selected_search_by == 'enquiry_no' %}
        <label for="id_enquiry_po_value" class="block text-sm font-medium text-gray-700">Enquiry No</label>
        {{ form.enquiry_po_value }}
    {% elif selected_search_by == 'po_no' %}
        <label for="id_enquiry_po_value" class="block text-sm font-medium text-gray-700">PO No</label>
        {{ form.enquiry_po_value }}
    {% else %} {# 'select' or default #}
        <label class="block text-sm font-medium text-gray-700">Search Value</label>
        <input type="text" disabled placeholder="Please select a search type" class="box3 w-full bg-gray-100 cursor-not-allowed">
        {# Hide both dynamic fields if 'select' is chosen or on initial load #}
        <input type="hidden" name="search_value" value="">
        <input type="hidden" name="enquiry_po_value" value="">
    {% endif %}
</div>
```

**`_custpo_table.html`** (Partial for the DataTables grid)

```html
<table id="custpoTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No.</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in custpomasters %}
        <tr class="{% cycle 'bg-gray-50' 'bg-white' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter0|add:page_obj.start_index }}</td> {# Correct SN calculation #}
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.financial_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.customer_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">
                <a href="{{ obj.get_absolute_url }}" class="text-blue-600 hover:text-blue-800 hover:underline">
                    {{ obj.po_no }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.enq_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.po_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.generated_by_employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {% if obj.display_file_name %}
                <a href="{% url 'salesdistribution:download_file' pk=obj.po_id %}" class="text-green-600 hover:text-green-800 hover:underline">
                    {{ obj.display_file_name }}
                </a>
                {% else %}
                    -
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-red-500 font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{# No DataTables initialization script needed here directly, it's handled in the main list.html afterSwap #}
```

#### 4.5 URLs (`salesdistribution/urls.py`)

Define the URL patterns for the views.

```python
from django.urls import path
from .views import (
    CustPOPrintView, 
    CustPOTablePartialView, 
    DownloadFileView, 
    CustomerAutocompleteView,
    SearchInputPartialView
)

app_name = 'salesdistribution' # Namespace for this app's URLs

urlpatterns = [
    path('custpo/', CustPOPrintView.as_view(), name='custpo_list'),
    path('custpo/table/', CustPOTablePartialView.as_view(), name='custpo_table'),
    path('custpo/download/<int:pk>/', DownloadFileView.as_view(), name='download_file'),
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    path('custpo/search-inputs/', SearchInputPartialView.as_view(), name='search_inputs_partial'),
    # Note: The 'CustPO_Print_Details.aspx' redirect from ASP.NET would map to a new Django view,
    # e.g., path('custpo/<int:pk>/details/', CustPODetailsView.as_view(), name='custpo_details'),
    # but that's beyond the scope of this specific page's conversion.
]
```

#### 4.6 Tests (`salesdistribution/tests.py`)

Include comprehensive unit tests for models and integration tests for views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import CustPOMaster, Customer, Employee, FinancialYear
from unittest.mock import patch, MagicMock
import json

# Helper to mock session data for testing
def mock_get_user_context_vars_for_tests(request):
    return {'comp_id': 100, 'fin_year_id': 2023}

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.financial_year = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.employee = Employee.objects.create(emp_id=101, title='Mr.', employee_name='John Doe', comp_id=100)
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer', comp_id=100)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Another Customer', comp_id=100)

        cls.po1 = CustPOMaster.objects.create(
            po_id=1, po_no='PO-001', enq_id='ENQ-001', customer_id='CUST001',
            session_id=101, fin_year_id=2023, po_date='01-01-2023', sys_date='01-01-2023',
            file_name='doc1.pdf', file_size=1024, content_type='application/pdf', file_data=b'PDF_DATA_1',
            comp_id=100
        )
        cls.po2 = CustPOMaster.objects.create(
            po_id=2, po_no='PO-002', enq_id='ENQ-002', customer_id='CUST002',
            session_id=101, fin_year_id=2023, po_date='02-01-2023', sys_date='02-01-2023',
            file_name=None, file_size=None, content_type=None, file_data=None,
            comp_id=100
        )

    def test_customer_po_master_creation(self):
        self.assertEqual(self.po1.po_no, 'PO-001')
        self.assertEqual(self.po1.customer_id, 'CUST001')
        self.assertEqual(self.po1.comp_id, 100)
        self.assertEqual(self.po2.po_no, 'PO-002')

    def test_customer_name_property(self):
        self.assertEqual(self.po1.customer_name, 'Test Customer')
        # Test non-existent customer
        self.po1.customer_id = 'NONEXISTENT'
        self.assertEqual(self.po1.customer_name, 'N/A')
        self.po1.customer_id = 'CUST001' # Reset for other tests

    def test_financial_year_property(self):
        self.assertEqual(self.po1.financial_year, '2023-24')
        # Test non-existent financial year
        self.po1.fin_year_id = 9999
        self.assertEqual(self.po1.financial_year, 'N/A')
        self.po1.fin_year_id = 2023 # Reset

    def test_generated_by_employee_name_property(self):
        self.assertEqual(self.po1.generated_by_employee_name, 'Mr. John Doe')
        # Test non-existent employee
        self.po1.session_id = 999
        self.assertEqual(self.po1.generated_by_employee_name, 'N/A')
        self.po1.session_id = 101 # Reset

    def test_display_file_name_property(self):
        self.assertEqual(self.po1.display_file_name, 'Download')
        self.assertEqual(self.po2.display_file_name, '')

    def test_customer_get_code_from_name(self):
        self.assertEqual(Customer.get_code_from_name(None, "Test Customer [CUST001]"), "CUST001")
        self.assertEqual(Customer.get_code_from_name(None, "Another Customer"), "Another Customer") # Should return as-is if no brackets
        self.assertEqual(Customer.get_code_from_name(None, "No Bracket Here"), "No Bracket Here")

class CustPOPrintViewsTest(TestCase):
    client = Client()

    @classmethod
    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def setUpTestData(cls, mock_context):
        # Create test data for all tests
        FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        Employee.objects.create(emp_id=101, title='Mr.', employee_name='John Doe', comp_id=100)
        Customer.objects.create(customer_id='CUST001', customer_name='Test Customer One', comp_id=100)
        Customer.objects.create(customer_id='CUST002', customer_name='Test Customer Two', comp_id=100)
        Customer.objects.create(customer_id='ABC', customer_name='Customer ABC', comp_id=100)

        CustPOMaster.objects.create(
            po_id=1, po_no='PO-001', enq_id='ENQ-001', customer_id='CUST001',
            session_id=101, fin_year_id=2023, po_date='01-01-2023', sys_date='01-01-2023',
            file_name='doc1.pdf', file_size=1024, content_type='application/pdf', file_data=b'PDF_DATA_1',
            comp_id=100
        )
        CustPOMaster.objects.create(
            po_id=2, po_no='PO-002', enq_id='ENQ-002', customer_id='CUST002',
            session_id=101, fin_year_id=2023, po_date='02-01-2023', sys_date='02-01-2023',
            file_name=None, file_size=None, content_type=None, file_data=None,
            comp_id=100
        )
        CustPOMaster.objects.create(
            po_id=3, po_no='PO-003', enq_id='ENQ-003', customer_id='ABC',
            session_id=101, fin_year_id=2022, po_date='03-01-2023', sys_date='03-01-2023',
            file_name='doc3.pdf', file_size=2048, content_type='application/pdf', file_data=b'PDF_DATA_3',
            comp_id=100 # Should not be included in default view if fin_year_id__lte is 2023
        )
        CustPOMaster.objects.create(
            po_id=4, po_no='PO-004', enq_id='ENQ-004', customer_id='CUST001',
            session_id=101, fin_year_id=2023, po_date='04-01-2023', sys_date='04-01-2023',
            file_name='doc4.pdf', file_size=2048, content_type='application/pdf', file_data=b'PDF_DATA_4',
            comp_id=999 # Should not be included in default view if comp_id is 100
        )


    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_custpo_list_view(self, mock_context):
        response = self.client.get(reverse('salesdistribution:custpo_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/custpomaster/list.html')
        # Check initial queryset count (po3 and po4 excluded by context)
        self.assertEqual(len(response.context['custpomasters']), 2) # PO1, PO2

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_custpo_list_view_search_by_customer_name(self, mock_context):
        response = self.client.get(reverse('salesdistribution:custpo_list'), {
            'search_by': 'customer_name',
            'search_value': 'Test Customer One [CUST001]'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['custpomasters']), 1)
        self.assertEqual(response.context['custpomasters'][0].po_no, 'PO-001')

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_custpo_list_view_search_by_customer_name_no_id(self, mock_context):
        response = self.client.get(reverse('salesdistribution:custpo_list'), {
            'search_by': 'customer_name',
            'search_value': 'Test Customer Two' # Search by name only
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['custpomasters']), 1)
        self.assertEqual(response.context['custpomasters'][0].po_no, 'PO-002')
        
    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_custpo_list_view_search_by_enquiry_no(self, mock_context):
        response = self.client.get(reverse('salesdistribution:custpo_list'), {
            'search_by': 'enquiry_no',
            'enquiry_po_value': 'ENQ-001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['custpomasters']), 1)
        self.assertEqual(response.context['custpomasters'][0].enq_id, 'ENQ-001')

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_custpo_list_view_search_by_po_no(self, mock_context):
        response = self.client.get(reverse('salesdistribution:custpo_list'), {
            'search_by': 'po_no',
            'enquiry_po_value': 'PO-002'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['custpomasters']), 1)
        self.assertEqual(response.context['custpomasters'][0].po_no, 'PO-002')

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_custpo_list_view_no_results(self, mock_context):
        response = self.client.get(reverse('salesdistribution:custpo_list'), {
            'search_by': 'po_no',
            'enquiry_po_value': 'NONEXISTENT'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['custpomasters']), 0)

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_custpo_table_partial_view_htmx(self, mock_context):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('salesdistribution:custpo_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/custpomaster/_custpo_table.html')
        self.assertEqual(len(response.context['custpomasters']), 2) # PO1, PO2

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_download_file_view(self, mock_context):
        response = self.client.get(reverse('salesdistribution:download_file', args=[1]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="doc1.pdf"')
        self.assertEqual(response.content, b'PDF_DATA_1')

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_download_file_view_not_found(self, mock_context):
        response = self.client.get(reverse('salesdistribution:download_file', args=[999]))
        self.assertEqual(response.status_code, 404)

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_download_file_view_no_file_data(self, mock_context):
        response = self.client.get(reverse('salesdistribution:download_file', args=[2]))
        self.assertEqual(response.status_code, 404)

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_customer_autocomplete_view(self, mock_context):
        response = self.client.get(reverse('salesdistribution:customer_autocomplete'), {'term': 'Test'})
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('Test Customer One [CUST001]', data)
        self.assertIn('Test Customer Two [CUST002]', data)
        self.assertEqual(len(data), 2)

        response = self.client.get(reverse('salesdistribution:customer_autocomplete'), {'term': 'One'})
        data = json.loads(response.content)
        self.assertIn('Test Customer One [CUST001]', data)
        self.assertEqual(len(data), 1)

        response = self.client.get(reverse('salesdistribution:customer_autocomplete'), {'term': 'CUST'})
        data = json.loads(response.content)
        self.assertIn('Test Customer One [CUST001]', data)
        self.assertIn('Test Customer Two [CUST002]', data)
        self.assertEqual(len(data), 2)

    @patch('salesdistribution.views.get_user_context_vars', side_effect=mock_get_user_context_vars_for_tests)
    def test_search_input_partial_view(self, mock_context):
        response = self.client.get(reverse('salesdistribution:search_inputs_partial'), {'search_by': 'customer_name'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/custpomaster/_search_inputs.html')
        self.assertContains(response, 'name="search_value"')
        self.assertNotContains(response, 'name="enquiry_po_value"')

        response = self.client.get(reverse('salesdistribution:search_inputs_partial'), {'search_by': 'enquiry_no'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/custpomaster/_search_inputs.html')
        self.assertContains(response, 'name="enquiry_po_value"')
        self.assertNotContains(response, 'name="search_value"')

        response = self.client.get(reverse('salesdistribution:search_inputs_partial'), {'search_by': 'select'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/custpomaster/_search_inputs.html')
        self.assertContains(response, 'Please select a search type')
        self.assertContains(response, 'name="search_value" value=""') # Hidden fields present
        self.assertContains(response, 'name="enquiry_po_value" value=""')
```

### Step 5: HTMX and Alpine.js Integration

The provided Django templates and views are already structured to use HTMX for dynamic content updates and Alpine.js for frontend interactivity, as detailed in the template sections.

*   **HTMX for Search & Table Updates:**
    *   The `search_by` dropdown (in `list.html` and `_search_inputs.html`) has `hx-get` to `{% url 'salesdistribution:search_inputs_partial' %}` with `hx-target="#search-inputs-container"` and `hx-swap="outerHTML"`. This dynamically replaces the input fields when the search type is changed, mimicking the ASP.NET `AutoPostBack`.
    *   The search form itself (in `list.html`) has `hx-get="{% url 'salesdistribution:custpo_table' %}"`, `hx-target="#custpo-table-container"`, and `hx-swap="innerHTML"`. It triggers on `submit` and `keyup changed delay:500ms` from the input fields, meaning the table content is refreshed dynamically as the user types or clicks search.
    *   The `hx-indicator` is used to show a loading spinner during HTMX requests.
    *   The `refreshCustPOList` HTMX trigger will be broadcast from `CustPOPrintView` if needed (e.g., after an action that modifies the list, though this module is read-only).

*   **HTMX for Autocomplete:**
    *   The `search_value` input in `_search_inputs.html` (when `customer_name` is selected) has `hx-get="{% url 'salesdistribution:customer_autocomplete' %}"`, `hx-trigger="keyup changed delay:300ms, search"`, `hx-target="#autocomplete-results"`, and `hx-swap="innerHTML"`. This fetches autocomplete suggestions dynamically.
    *   Alpine.js is used to manage the visibility and selection logic for the autocomplete results, including closing the results when clicking outside.

*   **DataTables Integration:**
    *   The `_custpo_table.html` partial is designed to hold a standard HTML `<table>` with the ID `custpoTable`.
    *   A JavaScript block in `list.html` listens for `htmx:afterSwap` on `#custpo-table-container`. Once the new table content is loaded, it destroys any existing DataTable instance and re-initializes `$('#custpoTable').DataTable()`, ensuring proper DataTables functionality (client-side pagination, sorting, filtering) on the dynamically loaded content. This method allows DataTables to manage presentation while HTMX handles content fetching.

*   **File Download & PO Details Navigation:**
    *   The download link in `_custpo_table.html` is a standard `<a>` tag pointing to `{% url 'salesdistribution:download_file' pk=obj.po_id %}`, ensuring a direct file download.
    *   The PO No link is also a standard `<a>` tag pointing to `{{ obj.get_absolute_url }}`, leading to the separate PO details page (which would be another Django view).

*   **DRY Templates:**
    *   The `list.html` extends `core/base.html` for consistent header/footer/layout.
    *   Partials like `_search_inputs.html` and `_custpo_table.html` promote reusability and component-specific updates via HTMX, avoiding full page reloads.

---

## Final Notes:

This plan provides a comprehensive and automated approach to migrating the `CustPO_Print` functionality from ASP.NET to Django. By strictly adhering to the "fat model, thin view" principle, utilizing HTMX and Alpine.js for a responsive frontend, and implementing robust testing, the modernized application will be more maintainable, scalable, and performant. The AI-assisted approach ensures that this transition is systematic and reduces the need for extensive manual code rewriting. Remember to configure your Django `settings.py` to include the `salesdistribution` app and ensure database connections are correctly set up to point to your existing SQL Server database.