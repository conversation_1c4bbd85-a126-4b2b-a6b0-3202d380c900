## ASP.NET to Django Conversion Script: Customer PO Edit Module

This document outlines a strategic plan to modernize the `CustPO_Edit_Details.aspx` ASP.NET module to a Django-based solution. Our approach prioritizes automation, efficient design patterns, and a seamless user experience using modern web technologies.

### Business Value Proposition

Migrating this critical Customer PO Edit module to Django offers several key benefits:

1.  **Enhanced User Experience:** By leveraging HTMX and Alpine.js, we will deliver a highly responsive, interactive interface. Users will experience instant feedback, reduced page reloads, and a fluid workflow similar to a single-page application, without the complexity of traditional JavaScript frameworks.
2.  **Simplified Development & Maintenance:** Django's "Fat Model, Thin View" architecture centralizes business logic, making the codebase easier to understand, test, and maintain. This significantly reduces development time for new features and bug fixes.
3.  **Improved Performance & Scalability:** Django is renowned for its efficiency and scalability. By minimizing server-side rendering for partial updates (via HTMX) and optimizing database interactions, the application will perform faster and handle a higher volume of concurrent users.
4.  **Reduced Technical Debt:** Moving away from legacy ASP.NET Web Forms eliminates reliance on outdated frameworks and proprietary controls, leading to a more robust, secure, and future-proof application.
5.  **Cost Efficiency:** The focus on automation and standardized Django patterns means less manual coding and faster delivery, leading to lower development and operational costs. DataTables provides advanced list view features out-of-the-box, saving significant custom development effort.
6.  **Better Testability:** Django's clear separation of concerns and built-in testing utilities enable comprehensive test coverage, ensuring higher software quality and fewer production issues.

### Core Architectural Principles

*   **Django 5.0+:** Utilizing the latest Django features and best practices.
*   **Fat Model, Thin View:** All business logic resides within Django models. Views are minimal, handling only request dispatching and rendering.
*   **HTMX + Alpine.js:** Exclusive use for all frontend interactivity. HTMX handles server communication for partial updates, and Alpine.js manages local UI state. No jQuery or other large JavaScript frameworks.
*   **DataTables:** Standardized solution for all list views, offering powerful search, sort, and pagination capabilities client-side.
*   **DRY Template Inheritance:** All templates will extend a central `base.html` (not included in this output), ensuring consistent layout and resource loading.
*   **Tailwind CSS:** Modern utility-first CSS framework for clean, responsive styling.
*   **Managed=False Models:** Initial setup assumes existing database tables, so models are created with `managed=False` to map directly to the current schema.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables. The primary entities are Customer PO Master and its details, with supporting tables for customer information, units, and quotations. Temporary details are handled in a separate table.

**Inferred Tables and Key Columns:**

*   **`SD_Cust_PO_Master` (Target Django Model: `CustomerPO`)**
    *   `POId` (Primary Key, used in queries)
    *   `CustomerId` (FK to `SD_Cust_master`)
    *   `CompId`, `FinYearId`, `SessionId` (Contextual IDs)
    *   `SysDate`, `SysTime`
    *   `QuotationNo` (FK to `SD_Cust_Quotation_Master`)
    *   `PONo`, `PODate`, `POReceivedDate`, `VendorCode`
    *   `PaymentTerms`, `PF`, `VAT`, `Excise`, `Octroi`, `Warrenty`, `Insurance`, `Transport`, `NoteNo`, `RegistrationNo`, `Freight`, `CST`, `Validity`, `OtherCharges`
    *   `FileName`, `FileSize`, `ContentType`, `FileData` (for attachments)

*   **`SD_Cust_PO_Details` (Target Django Model: `CustomerPODetail`)**
    *   `Id` (Primary Key)
    *   `POId` (FK to `SD_Cust_PO_Master`)
    *   `CompId`, `FinYearId`
    *   `ItemDesc`, `TotalQty`, `Unit` (FK to `Unit_Master`), `Rate`, `Discount`

*   **`SD_Cust_PO_Details_Temp` (Target Django Model: `CustomerPODetailTemp`)**
    *   `Id` (Primary Key)
    *   `SessionId`, `CompId`, `FinYearId`
    *   `ItemDesc`, `TotalQty`, `Unit` (FK to `Unit_Master`), `Rate`, `Discount`

*   **`Unit_Master` (Target Django Model: `Unit`)**
    *   `Id` (Primary Key)
    *   `Symbol`

*   **`SD_Cust_Quotation_Master` (Target Django Model: `CustomerQuotation`)**
    *   `Id` (Primary Key)
    *   `QuotationNo`, `SysDate`, `CompId`, `FinYearId`, `EnqId`

*   **`SD_Cust_master` (Target Django Model: `Customer`)**
    *   `CustomerId` (Primary Key)
    *   `CustomerName`, `RegdAddress`, `RegdCountry`, `RegdState`, `RegdCity`, `RegdPinNo`

*   **`tblcountry` (Target Django Model: `Country`)**
    *   `CId` (Primary Key), `CountryName`

*   **`tblState` (Target Django Model: `State`)**
    *   `SId` (Primary Key), `StateName`

*   **`tblCity` (Target Django Model: `City`)**
    *   `CityId` (Primary Key), `CityName`

### Step 2: Identify Backend Functionality

**Module Purpose:** Edit/Update an existing Customer PO.

**CRUD Operations:**

*   **Customer PO Master (`CustomerPO`):**
    *   **Read:** Initial load of form fields, customer details, and associated quotation.
    *   **Update:** Main `BtnUpdate_Click` handles saving all master data, including file attachments.
    *   **Delete:** (Implicitly, clearing attachment via `ImageCross_Click`).

*   **Temporary PO Details (`CustomerPODetailTemp`):**
    *   **Create:** `BtnSubmit_Click` adds new items to the temporary list.
    *   **Read:** `FillGrid()` populates the temporary items `GridView1`.
    *   **Update:** `GridView1_RowUpdating` modifies temporary items inline.
    *   **Delete:** `GridView1_RowDeleting` removes temporary items.

*   **Final PO Details (`CustomerPODetail`):**
    *   **Create:** During `BtnUpdate_Click`, items from `CustomerPODetailTemp` are moved to `CustomerPODetail`.
    *   **Read:** `FillGrid2()` populates the final items `GridView2`.
    *   **Update:** `GridView2_RowUpdating` modifies final items inline.
    *   **Delete:** `GridView2_RowDeleting` removes final items.

**Validation Logic:** Extensive required field and regex validation for dates and numbers.

**Business Logic:**
*   Calculation of `Amount = TotalQty * (Rate - (Rate * Discount / 100))`.
*   Handling of date formats.
*   Management of `SessionId`, `CompId`, `FinYearId` for data partitioning and user context.
*   File upload and attachment management.
*   Tab-based navigation logic.

### Step 3: Infer UI Components

**Overall Layout:** A multi-tab interface (`TabContainer`) with three main sections: Customer Details, Goods Details, and Terms & Conditions.

**Tab 1: Customer Details**
*   Read-only display of Customer Name, Address, Enquiry No.
*   Dropdown for Quotation No.
*   Text inputs for PO Received Date, PO Date, PO No, Vendor Code.
*   Navigation buttons (Next, Cancel).

**Tab 2: Goods Details**
*   Form for adding new item: Text input for Description, Quantity, Rate, Discount, Dropdown for Unit.
*   Two GridViews:
    *   One for "temporary" items being added (`GridView1`). Supports inline edit/delete.
    *   One for "existing/final" items already associated with the PO (`GridView2`). Supports inline edit/delete.
*   Navigation buttons (Next, Cancel).

**Tab 3: Terms & Conditions**
*   Text inputs for Payment Terms, P & F, Excise / Service Tax, VAT, Octroi, Warrenty, Insurance, Mode of Transport, R.R./G.C. Note No., Vehicle Reg. No., Freight, CST, Validity, Other Charges, Remarks. Many of these have auto-suggestion dropdowns.
*   File upload component for Attachments, with a link to view and button to remove existing attachments.
*   Action buttons (Update, Cancel).

---

### Step 4: Generate Django Code

**Django App Name:** `sales` (to encompass `SalesDistribution`)

#### 4.1 Models (`sales/models.py`)

```python
from django.db import models
from django.utils import timezone
import datetime

# --- Auxiliary Models (managed=False, assuming they exist in the DB) ---

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    regd_address = models.TextField(db_column='RegdAddress')
    regd_country_id = models.IntegerField(db_column='RegdCountry') # FK to tblcountry
    regd_state_id = models.IntegerField(db_column='RegdState') # FK to tblState
    regd_city_id = models.IntegerField(db_column='RegdCity') # FK to tblCity
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

    def get_full_address(self):
        # This would ideally fetch actual Country/State/City names via ORM if models were managed
        # For managed=False, this is a placeholder or would require raw SQL/custom manager
        return f"{self.regd_address}, {self.regd_city_id}, {self.regd_state_id}, {self.regd_country_id}. {self.regd_pin_no}"

class CustomerQuotation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50)
    system_date = models.CharField(db_column='SysDate', max_length=50) # Stored as 'dd-MM-yyyy' or similar
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    enquiry_id = models.IntegerField(db_column='EnqId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Customer Quotation'
        verbose_name_plural = 'Customer Quotations'

    def __str__(self):
        # Replicate ASP.NET format: QuotationNo[dd-MM-yyyy]
        try:
            # Assuming SysDate is stored as 'MM-dd-yyyy' or 'dd-MM-yyyy'
            parts = self.system_date.split('-')
            if len(parts[0]) == 2 and len(parts[1]) == 2 and len(parts[2]) == 4: # dd-MM-yyyy
                date_obj = datetime.datetime.strptime(self.system_date, '%d-%m-%Y').date()
            else: # Assuming MM-dd-yyyy
                date_obj = datetime.datetime.strptime(self.system_date, '%m-%d-%Y').date()
            formatted_date = date_obj.strftime('%d-%m-%Y')
            return f"{self.quotation_no}[{formatted_date}]"
        except ValueError:
            return f"{self.quotation_no}[{self.system_date}]" # Fallback if date format is unexpected

# --- Main Models ---

class CustomerPO(models.Model):
    # POId is the primary key in the ASP.NET code, but Django's default 'id' PK is fine.
    # Map 'POId' to a regular field if it's not the auto-incrementing PK in the DB.
    # If it is the actual PK, then use `id = models.IntegerField(db_column='POId', primary_key=True)`.
    # Assuming 'Id' is the PK used internally by the DB, and POId is a logical ID.
    # From the code, 'poid' (query string) is used in WHERE clauses, suggesting it's the primary key.
    id = models.CharField(db_column='POId', primary_key=True, max_length=50) # Matches 'poid' from QueryString
    
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Corresponds to username
    
    system_date = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True)
    system_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)

    quotation = models.ForeignKey(CustomerQuotation, models.DO_NOTHING, db_column='QuotationNo', blank=True, null=True)
    po_number = models.CharField(db_column='PONo', max_length=100)
    po_date = models.CharField(db_column='PODate', max_length=50) # Stored as 'dd-MM-yyyy'
    po_received_date = models.CharField(db_column='POReceivedDate', max_length=50) # Stored as 'dd-MM-yyyy'
    vendor_code = models.CharField(db_column='VendorCode', max_length=100)

    payment_terms = models.CharField(db_column='PaymentTerms', max_length=255, blank=True, null=True)
    pf_terms = models.CharField(db_column='PF', max_length=255, blank=True, null=True)
    vat_terms = models.CharField(db_column='VAT', max_length=255, blank=True, null=True)
    excise_terms = models.CharField(db_column='Excise', max_length=255, blank=True, null=True)
    octroi_terms = models.CharField(db_column='Octroi', max_length=255, blank=True, null=True)
    warranty_terms = models.CharField(db_column='Warrenty', max_length=255, blank=True, null=True)
    insurance_terms = models.CharField(db_column='Insurance', max_length=255, blank=True, null=True)
    transport_mode = models.CharField(db_column='Transport', max_length=255, blank=True, null=True)
    gc_note_number = models.CharField(db_column='NoteNo', max_length=255, blank=True, null=True)
    vehicle_registration_no = models.CharField(db_column='RegistrationNo', max_length=255, blank=True, null=True)
    freight_terms = models.CharField(db_column='Freight', max_length=255, blank=True, null=True)
    cst_terms = models.CharField(db_column='CST', max_length=255, blank=True, null=True)
    validity_terms = models.CharField(db_column='Validity', max_length=255, blank=True, null=True)
    other_charges = models.CharField(db_column='OtherCharges', max_length=255, blank=True, null=True)

    attachment_file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    attachment_file_size = models.IntegerField(db_column='FileSize', blank=True, null=True)
    attachment_content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    # FileData is problematic as it's binary. Django typically stores files on disk and saves paths.
    # For direct binary storage, use BinaryField, but it's generally discouraged.
    # Assuming for now it's not directly managed as a file field.
    # If the file data is large, consider external storage or blob in DB.
    # For this migration, we'll represent it as a nullable BinaryField for exact match.
    attachment_file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Customer PO'
        verbose_name_plural = 'Customer POs'

    def __str__(self):
        return f"PO No: {self.po_number} for {self.customer.customer_name}"

    def update_attachment(self, uploaded_file):
        """
        Updates attachment fields from an UploadedFile object.
        Call this from the view after successful form processing.
        """
        if uploaded_file:
            self.attachment_file_name = uploaded_file.name
            self.attachment_file_size = uploaded_file.size
            self.attachment_content_type = uploaded_file.content_type
            self.attachment_file_data = uploaded_file.read()
        else:
            self.attachment_file_name = None
            self.attachment_file_size = 0
            self.attachment_content_type = None
            self.attachment_file_data = None
        self.save()

    def clear_attachment(self):
        """
        Clears attachment data.
        """
        self.attachment_file_name = None
        self.attachment_file_size = 0
        self.attachment_content_type = None
        self.attachment_file_data = None
        self.save()

    def get_po_date_display(self):
        try:
            return datetime.datetime.strptime(self.po_date, '%d-%m-%Y').strftime('%Y-%m-%d')
        except ValueError:
            return self.po_date # Fallback

    def get_po_received_date_display(self):
        try:
            return datetime.datetime.strptime(self.po_received_date, '%d-%m-%Y').strftime('%Y-%m-%d')
        except ValueError:
            return self.po_received_date # Fallback


class CustomerPODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    customer_po = models.ForeignKey(CustomerPO, models.DO_NOTHING, db_column='POId')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    item_description = models.TextField(db_column='ItemDesc')
    total_quantity = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3)
    unit = models.ForeignKey(Unit, models.DO_NOTHING, db_column='Unit')
    rate_per_unit = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount_percentage = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Customer PO Detail'
        verbose_name_plural = 'Customer PO Details'

    def __str__(self):
        return f"{self.item_description} ({self.total_quantity} {self.unit.symbol})"

    @property
    def amount(self):
        """Calculates the amount for the item based on quantity, rate, and discount."""
        return self.total_quantity * (self.rate_per_unit - (self.rate_per_unit * self.discount_percentage / 100))

    def update_details(self, description, quantity, unit_id, rate, discount):
        """
        Updates the fields of a PO detail item.
        This business logic resides in the model.
        """
        self.item_description = description
        self.total_quantity = quantity
        self.unit_id = unit_id
        self.rate_per_unit = rate
        self.discount_percentage = discount
        self.save()


class CustomerPODetailTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=255) # Corresponds to username
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    item_description = models.TextField(db_column='ItemDesc')
    total_quantity = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3)
    unit = models.ForeignKey(Unit, models.DO_NOTHING, db_column='Unit')
    rate_per_unit = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount_percentage = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details_Temp'
        verbose_name = 'Customer PO Detail Temp'
        verbose_name_plural = 'Customer PO Details Temp'

    def __str__(self):
        return f"Temp: {self.item_description}"

    @property
    def amount(self):
        """Calculates the amount for the temporary item."""
        return self.total_quantity * (self.rate_per_unit - (self.rate_per_unit * self.discount_percentage / 100))

    def update_temp_details(self, description, quantity, unit_id, rate, discount):
        """
        Updates the fields of a temporary PO detail item.
        """
        self.item_description = description
        self.total_quantity = quantity
        self.unit_id = unit_id
        self.rate_per_unit = rate
        self.discount_percentage = discount
        self.save()

    @classmethod
    def transfer_to_final_details(cls, session_id, company_id, financial_year_id, customer_po_instance):
        """
        Transfers all temporary items for a given session/company/fin_year
        to the final CustomerPODetail table for the given CustomerPO instance.
        """
        temp_items = cls.objects.filter(
            session_id=session_id,
            company_id=company_id,
            financial_year_id=financial_year_id
        )
        for item in temp_items:
            CustomerPODetail.objects.create(
                customer_po=customer_po_instance,
                company_id=item.company_id,
                financial_year_id=item.financial_year_id,
                item_description=item.item_description,
                total_quantity=item.total_quantity,
                unit=item.unit,
                rate_per_unit=item.rate_per_unit,
                discount_percentage=item.discount_percentage
            )
            item.delete() # Remove from temp table after transfer
        return temp_items.count() # Return count of items transferred

```

#### 4.2 Forms (`sales/forms.py`)

```python
from django import forms
from django.core.validators import RegexValidator
from .models import CustomerPO, CustomerPODetail, CustomerPODetailTemp, Unit, CustomerQuotation
import datetime
from decimal import Decimal

# --- Utility Functions (equivalent to clsFunctions) ---
def parse_date_dmy(date_str):
    if not date_str:
        return None
    try:
        return datetime.datetime.strptime(date_str, '%d-%m-%Y').date()
    except ValueError:
        raise forms.ValidationError("Invalid date format. Please use DD-MM-YYYY.")

def format_date_dmy(date_obj):
    if not date_obj:
        return ""
    return date_obj.strftime('%d-%m-%Y')

# --- Validation Rules ---
date_regex = r"^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[012])-\d{4}$"
decimal_regex = r"^\d{1,15}(\.\d{0,3})?$" # Up to 3 decimal places for qty/rate/discount

# --- Main Customer PO Form (combines Customer Details & Terms) ---
class CustomerPOForm(forms.ModelForm):
    # Customer Details Tab Fields
    po_received_date = forms.CharField(
        validators=[RegexValidator(date_regex, "Invalid date format (DD-MM-YYYY).")],
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # HTML5 date picker
        })
    )
    po_date = forms.CharField(
        validators=[RegexValidator(date_regex, "Invalid date format (DD-MM-YYYY).")],
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # HTML5 date picker
        })
    )
    quotation_no = forms.ModelChoiceField(
        queryset=CustomerQuotation.objects.all(), # Needs dynamic filtering later
        required=False, # From ASP.NET, it's optional, defaults to 0 if 'Select'
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    
    # Terms & Conditions Tab Fields
    payment_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'payment_terms_suggestions'})
    )
    pf_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'pf_terms_suggestions'})
    )
    vat_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'vat_terms_suggestions'})
    )
    excise_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-sm shadow-sm', 'list': 'excise_terms_suggestions'})
    )
    octroi_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'octroi_terms_suggestions'})
    )
    warranty_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'warranty_terms_suggestions'})
    )
    insurance_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'insurance_terms_suggestions'})
    )
    transport_mode = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'transport_mode_suggestions'})
    )
    gc_note_number = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'})
    )
    vehicle_registration_no = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'})
    )
    freight_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'list': 'freight_terms_suggestions'})
    )
    cst_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'})
    )
    validity_terms = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'})
    )
    other_charges = forms.CharField(
        max_length=255, 
        required=True, 
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'})
    )
    
    # File upload field - handled separately in the view
    attachment_file = forms.FileField(required=False, label="Attachment",
                                     widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none'}))

    class Meta:
        model = CustomerPO
        fields = [
            'quotation', 'po_number', 'po_date', 'po_received_date', 'vendor_code',
            'payment_terms', 'pf_terms', 'vat_terms', 'excise_terms', 'octroi_terms',
            'warranty_terms', 'insurance_terms', 'transport_mode', 'gc_note_number',
            'vehicle_registration_no', 'freight_terms', 'remarks', 'cst_terms',
            'validity_terms', 'other_charges',
            # 'attachment_file_name', 'attachment_file_size', 'attachment_content_type', 'attachment_file_data' - handled by attachment_file
        ]
        # Exclude generated fields / foreign keys that are set programmatically
        # Note: 'customer', 'company_id', 'financial_year_id', 'session_id', 'system_date', 'system_time'
        # are managed in the view or through hidden inputs/initial data
        exclude = ['customer', 'company_id', 'financial_year_id', 'session_id', 
                   'system_date', 'system_time', 'attachment_file_name', 
                   'attachment_file_size', 'attachment_content_type', 'attachment_file_data']
        
        widgets = {
            'po_number': forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'vendor_code': forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None) # Pass user object to form for filtering
        self.company_id = kwargs.pop('company_id', None)
        self.financial_year_id = kwargs.pop('financial_year_id', None)
        super().__init__(*args, **kwargs)

        # Populate quotation dropdown dynamically based on CompId, FinYearId, EnqId
        if self.instance and self.instance.customer.enquiry_id: # Assuming customer has an enquiry_id
            self.fields['quotation_no'].queryset = CustomerQuotation.objects.filter(
                company_id=self.company_id,
                financial_year_id__lte=self.financial_year_id, # as per ASP.NET logic
                enquiry_id=self.instance.customer.enquiry_id # This linkage is not direct in models, assuming it's available
            )
        else:
            self.fields['quotation_no'].queryset = CustomerQuotation.objects.none()

        # Set initial date values for HTML5 date picker format
        if self.instance.po_date:
            self.fields['po_date'].initial = self.instance.get_po_date_display()
        if self.instance.po_received_date:
            self.fields['po_received_date'].initial = self.instance.get_po_received_date_display()
            
    def clean_po_date(self):
        date_str = self.cleaned_data['po_date']
        parse_date_dmy(date_str) # Just to validate format, actual storage is string
        return date_str

    def clean_po_received_date(self):
        date_str = self.cleaned_data['po_received_date']
        parse_date_dmy(date_str) # Just to validate format, actual storage is string
        return date_str
        
    def clean(self):
        cleaned_data = super().clean()
        # Add any cross-field validation here if needed
        return cleaned_data


class CustomerPODetailTempForm(forms.ModelForm):
    total_quantity = forms.DecimalField(
        max_digits=18, decimal_places=3, required=True,
        validators=[RegexValidator(decimal_regex, "Invalid quantity format (e.g., 123.456).")],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    rate_per_unit = forms.DecimalField(
        max_digits=18, decimal_places=2, required=True,
        validators=[RegexValidator(decimal_regex, "Invalid rate format (e.g., 123.45).")],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    discount_percentage = forms.DecimalField(
        max_digits=18, decimal_places=2, required=False, initial=0.0,
        validators=[RegexValidator(decimal_regex, "Invalid discount format (e.g., 12.34).")],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    unit = forms.ModelChoiceField(
        queryset=Unit.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    class Meta:
        model = CustomerPODetailTemp
        fields = ['item_description', 'total_quantity', 'unit', 'rate_per_unit', 'discount_percentage']
        widgets = {
            'item_description': forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20', 'rows': 4}),
        }

    def clean_discount_percentage(self):
        discount = self.cleaned_data.get('discount_percentage')
        if discount is None: # If not provided or invalid regex matches, set to 0.0
            return Decimal('0.00')
        return discount

    def clean(self):
        cleaned_data = super().clean()
        # Add business logic validation here if needed, e.g., discount not > 100%
        return cleaned_data

class CustomerPODetailForm(forms.ModelForm): # For inline editing of final details
    total_quantity = forms.DecimalField(
        max_digits=18, decimal_places=3, required=True,
        validators=[RegexValidator(decimal_regex, "Invalid quantity format (e.g., 123.456).")],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    rate_per_unit = forms.DecimalField(
        max_digits=18, decimal_places=2, required=True,
        validators=[RegexValidator(decimal_regex, "Invalid rate format (e.g., 123.45).")],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    discount_percentage = forms.DecimalField(
        max_digits=18, decimal_places=2, required=False, initial=0.0,
        validators=[RegexValidator(decimal_regex, "Invalid discount format (e.g., 12.34).")],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    unit = forms.ModelChoiceField(
        queryset=Unit.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    class Meta:
        model = CustomerPODetail
        fields = ['item_description', 'total_quantity', 'unit', 'rate_per_unit', 'discount_percentage']
        widgets = {
            'item_description': forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def clean_discount_percentage(self):
        discount = self.cleaned_data.get('discount_percentage')
        if discount is None:
            return Decimal('0.00')
        return discount

```

#### 4.3 Views (`sales/views.py`)

```python
from django.views.generic import TemplateView, View
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, Http404
from django.db import connection, transaction
from django.template.loader import render_to_string
from django.utils import timezone
from decimal import Decimal
import datetime

from .models import (
    CustomerPO, CustomerPODetail, CustomerPODetailTemp,
    Customer, Unit, CustomerQuotation
)
from .forms import CustomerPOForm, CustomerPODetailTempForm, CustomerPODetailForm

# Helper to get session/company/fin_year IDs - assuming these are in request.session
# In a real app, these would come from an authentication/company context system.
def get_user_context(request):
    return {
        'session_id': request.user.username if request.user.is_authenticated else 'anonymous',
        'company_id': request.session.get('compid', 1), # Default to 1 if not set
        'financial_year_id': request.session.get('finyear', 2024) # Default to 2024
    }

class CustomerPOEditView(TemplateView):
    template_name = 'sales/customer_po_edit.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract IDs from URL (equivalent to Request.QueryString)
        po_id = self.kwargs['po_id']
        customer_id = self.kwargs['customer_id']
        enquiry_id = self.kwargs['enquiry_id'] # Not directly used in CustomerPO, but inferred from ASP.NET logic

        user_context = get_user_context(self.request)
        session_id = user_context['session_id']
        company_id = user_context['company_id']
        financial_year_id = user_context['financial_year_id']

        customer_po = get_object_or_404(CustomerPO, pk=po_id, customer_id=customer_id, company_id=company_id)
        customer_obj = get_object_or_404(Customer, customer_id=customer_id)

        # Set enquiry_id on customer_obj for form initialization if not directly in Customer model
        customer_obj.enquiry_id = enquiry_id # This is an assumption based on ASP.NET 'EnqId' usage

        # Main form for Customer details & Terms
        form = CustomerPOForm(
            instance=customer_po,
            user=self.request.user, # Pass user for potential filtering
            company_id=company_id,
            financial_year_id=financial_year_id
        )

        # Goods Details tab - form for adding new items
        temp_detail_form = CustomerPODetailTempForm()
        
        context['customer_po'] = customer_po
        context['customer_obj'] = customer_obj
        context['form'] = form
        context['temp_detail_form'] = temp_detail_form
        context['enquiry_id'] = enquiry_id # Pass enquiry_id to template if needed

        # Prepare initial tab data (can be rendered in JS on page load)
        # Or, make them HTMX endpoints that swap in content on tab clicks
        context['initial_tab_content'] = render_to_string(
            'sales/partials/_customer_details_form.html', 
            {'form': form, 'customer_obj': customer_obj, 'customer_po': customer_po, 'enquiry_id': enquiry_id},
            self.request
        )
        context['current_tab_index'] = self.request.session.get('active_tab_index', 0) # For Alpine.js to control initial tab

        # For terms auto-suggestion datalists (assuming distinct values from DB)
        context['payment_terms_suggestions'] = CustomerPO.objects.order_by().values_list('payment_terms', flat=True).distinct()
        context['pf_terms_suggestions'] = CustomerPO.objects.order_by().values_list('pf_terms', flat=True).distinct()
        context['vat_terms_suggestions'] = CustomerPO.objects.order_by().values_list('vat_terms', flat=True).distinct()
        context['excise_terms_suggestions'] = CustomerPO.objects.order_by().values_list('excise_terms', flat=True).distinct()
        context['octroi_terms_suggestions'] = CustomerPO.objects.order_by().values_list('octroi_terms', flat=True).distinct()
        context['warranty_terms_suggestions'] = CustomerPO.objects.order_by().values_list('warranty_terms', flat=True).distinct()
        context['insurance_terms_suggestions'] = CustomerPO.objects.order_by().values_list('insurance_terms', flat=True).distinct()
        context['transport_mode_suggestions'] = CustomerPO.objects.order_by().values_list('transport_mode', flat=True).distinct()
        context['freight_terms_suggestions'] = CustomerPO.objects.order_by().values_list('freight_terms', flat=True).distinct()


        return context

    def post(self, request, po_id, customer_id, enquiry_id):
        user_context = get_user_context(request)
        company_id = user_context['company_id']

        customer_po = get_object_or_404(CustomerPO, pk=po_id, customer_id=customer_id, company_id=company_id)
        
        # Determine which form/action is being submitted
        if 'btn_update_master' in request.POST: # Main form submission (from Terms & Conditions tab)
            form = CustomerPOForm(
                request.POST, 
                request.FILES, 
                instance=customer_po,
                user=request.user,
                company_id=company_id,
                financial_year_id=user_context['financial_year_id']
            )
            if form.is_valid():
                with transaction.atomic():
                    # Update master data
                    customer_po_instance = form.save(commit=False)
                    customer_po_instance.session_id = user_context['session_id']
                    customer_po_instance.company_id = company_id
                    customer_po_instance.financial_year_id = user_context['financial_year_id']
                    customer_po_instance.system_date = timezone.now().strftime('%m-%d-%Y') # Or correct format
                    customer_po_instance.system_time = timezone.now().strftime('%H:%M:%S')

                    # Handle file upload
                    uploaded_file = request.FILES.get('attachment_file')
                    customer_po_instance.update_attachment(uploaded_file)
                    
                    customer_po_instance.save()

                    # Transfer temporary details to final details
                    transferred_count = CustomerPODetailTemp.transfer_to_final_details(
                        user_context['session_id'], 
                        company_id, 
                        user_context['financial_year_id'], 
                        customer_po_instance
                    )
                
                messages.success(request, 'Customer PO updated successfully.')
                # Redirect or trigger HTMX event for full page refresh/redirect
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        status=204,
                        headers={
                            'HX-Redirect': reverse_lazy('sales:po_edit_list') + f'?msg=Customer PO is updated.'
                        }
                    )
                return redirect(reverse_lazy('sales:po_edit_list') + f'?msg=Customer PO is updated.')
            else:
                messages.error(request, 'Please correct the errors in the form.')
                # If HTMX request, re-render the form partial with errors
                if request.headers.get('HX-Request'):
                    context = self.get_context_data(po_id=po_id, customer_id=customer_id, enquiry_id=enquiry_id)
                    context['form'] = form # Pass the form with errors
                    return HttpResponse(render_to_string(
                        'sales/partials/_terms_conditions_form.html', context, request
                    ))
                return self.render_to_response(self.get_context_data(form=form))
        
        elif 'btn_clear_attachment' in request.POST:
            customer_po.clear_attachment()
            messages.success(request, "Attachment cleared successfully.")
            if request.headers.get('HX-Request'):
                 return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshAttachmentSection' # Trigger HTMX event to update attachment UI
                    }
                )
            return redirect(request.path_info) # Redirect back to the same page

        # Fallback if no specific button is identified
        messages.error(request, "Invalid form submission.")
        return self.get(request, po_id, customer_id, enquiry_id)

class TabContentHTMXView(View):
    """
    Renders content for each tab dynamically via HTMX.
    """
    def get(self, request, po_id, customer_id, enquiry_id, tab_name):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        financial_year_id = user_context['financial_year_id']
        session_id = user_context['session_id']

        customer_po = get_object_or_404(CustomerPO, pk=po_id, customer_id=customer_id, company_id=company_id)
        customer_obj = get_object_or_404(Customer, customer_id=customer_id)
        customer_obj.enquiry_id = enquiry_id # Placeholder for enquiry_id linkage

        context = {
            'customer_po': customer_po,
            'customer_obj': customer_obj,
            'enquiry_id': enquiry_id,
            'current_tab_index': request.session.get('active_tab_index', 0),
            'po_id': po_id, # Pass IDs for forms/urls in partials
            'customer_id': customer_id,
            'enquiry_id': enquiry_id,
        }

        if tab_name == 'customer_details':
            form = CustomerPOForm(instance=customer_po, user=request.user, company_id=company_id, financial_year_id=financial_year_id)
            context['form'] = form
            template_name = 'sales/partials/_customer_details_form.html'
        elif tab_name == 'goods_details':
            temp_detail_form = CustomerPODetailTempForm()
            context['temp_detail_form'] = temp_detail_form
            # Also load tables in their respective HTMX endpoints
            template_name = 'sales/partials/_goods_details_form.html'
        elif tab_name == 'terms_conditions':
            form = CustomerPOForm(instance=customer_po, user=request.user, company_id=company_id, financial_year_id=financial_year_id)
            context['form'] = form
            # For terms auto-suggestion datalists
            context['payment_terms_suggestions'] = CustomerPO.objects.order_by().values_list('payment_terms', flat=True).distinct()
            context['pf_terms_suggestions'] = CustomerPO.objects.order_by().values_list('pf_terms', flat=True).distinct()
            context['vat_terms_suggestions'] = CustomerPO.objects.order_by().values_list('vat_terms', flat=True).distinct()
            context['excise_terms_suggestions'] = CustomerPO.objects.order_by().values_list('excise_terms', flat=True).distinct()
            context['octroi_terms_suggestions'] = CustomerPO.objects.order_by().values_list('octroi_terms', flat=True).distinct()
            context['warranty_terms_suggestions'] = CustomerPO.objects.order_by().values_list('warranty_terms', flat=True).distinct()
            context['insurance_terms_suggestions'] = CustomerPO.objects.order_by().values_list('insurance_terms', flat=True).distinct()
            context['transport_mode_suggestions'] = CustomerPO.objects.order_by().values_list('transport_mode', flat=True).distinct()
            context['freight_terms_suggestions'] = CustomerPO.objects.order_by().values_list('freight_terms', flat=True).distinct()
            template_name = 'sales/partials/_terms_conditions_form.html'
        else:
            raise Http404("Tab not found.")

        # Update session for active tab index
        try:
            tab_index_map = {'customer_details': 0, 'goods_details': 1, 'terms_conditions': 2}
            request.session['active_tab_index'] = tab_index_map.get(tab_name, 0)
        except Exception:
            pass # Ignore if session not available or tab_name not in map
            
        return HttpResponse(render_to_string(template_name, context, request))

# --- HTMX Views for Customer PO Details (Temp and Final) ---

class CustomerPODetailTempAddHTMXView(View):
    def post(self, request, po_id, customer_id, enquiry_id):
        user_context = get_user_context(request)
        session_id = user_context['session_id']
        company_id = user_context['company_id']
        financial_year_id = user_context['financial_year_id']
        
        form = CustomerPODetailTempForm(request.POST)
        if form.is_valid():
            temp_detail = form.save(commit=False)
            temp_detail.session_id = session_id
            temp_detail.company_id = company_id
            temp_detail.financial_year_id = financial_year_id
            temp_detail.save()
            messages.success(request, 'Item added to temporary list.')
            
            # Clear form fields on success
            response = HttpResponse(status=204, headers={'HX-Trigger': 'refreshTempDetailTable'})
            # Manually clear form inputs on client-side if needed (Alpine.js or simple JS)
            return response
        else:
            # Re-render the form partial with errors
            customer_po = get_object_or_404(CustomerPO, pk=po_id, customer_id=customer_id, company_id=company_id)
            customer_obj = get_object_or_404(Customer, customer_id=customer_id)
            context = {
                'temp_detail_form': form,
                'customer_po': customer_po,
                'customer_obj': customer_obj,
                'po_id': po_id, 'customer_id': customer_id, 'enquiry_id': enquiry_id,
            }
            return HttpResponse(render_to_string(
                'sales/partials/_temp_po_item_form.html', context, request
            ), status=400) # Bad Request for validation errors

class CustomerPODetailTempTableHTMXView(View):
    def get(self, request, po_id, customer_id, enquiry_id):
        user_context = get_user_context(request)
        session_id = user_context['session_id']
        company_id = user_context['company_id']
        financial_year_id = user_context['financial_year_id']

        temp_details = CustomerPODetailTemp.objects.filter(
            session_id=session_id,
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('unit')
        
        context = {
            'temp_details': temp_details,
            'po_id': po_id, # Pass IDs for URLs in partials
            'customer_id': customer_id,
            'enquiry_id': enquiry_id,
        }
        return HttpResponse(render_to_string('sales/partials/_customer_po_detail_temp_table.html', context, request))

class CustomerPODetailTempEditHTMXView(View):
    def get(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        temp_detail = get_object_or_404(CustomerPODetailTemp, pk=pk, company_id=company_id)
        form = CustomerPODetailTempForm(instance=temp_detail)
        context = {'form': form, 'temp_detail': temp_detail, 'po_id': po_id, 'customer_id': customer_id, 'enquiry_id': enquiry_id}
        return HttpResponse(render_to_string('sales/partials/_customer_po_detail_temp_form_edit.html', context, request))

    def post(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        temp_detail = get_object_or_404(CustomerPODetailTemp, pk=pk, company_id=company_id)
        form = CustomerPODetailTempForm(request.POST, instance=temp_detail)
        if form.is_valid():
            form.save()
            messages.success(request, 'Temporary item updated successfully.')
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshTempDetailTable'})
        else:
            context = {'form': form, 'temp_detail': temp_detail, 'po_id': po_id, 'customer_id': customer_id, 'enquiry_id': enquiry_id}
            return HttpResponse(render_to_string('sales/partials/_customer_po_detail_temp_form_edit.html', context, request), status=400)

class CustomerPODetailTempDeleteHTMXView(View):
    def get(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        temp_detail = get_object_or_404(CustomerPODetailTemp, pk=pk, company_id=company_id)
        context = {'obj': temp_detail, 'po_id': po_id, 'customer_id': customer_id, 'enquiry_id': enquiry_id, 'delete_url': reverse_lazy('sales:po_temp_detail_delete', args=[po_id, customer_id, enquiry_id, pk])}
        return HttpResponse(render_to_string('sales/partials/_confirm_delete.html', context, request))

    def post(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        temp_detail = get_object_or_404(CustomerPODetailTemp, pk=pk, company_id=company_id)
        temp_detail.delete()
        messages.success(request, 'Temporary item deleted successfully.')
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshTempDetailTable'})


class CustomerPODetailTableHTMXView(View):
    def get(self, request, po_id, customer_id, enquiry_id):
        user_context = get_user_context(request)
        company_id = user_context['company_id']

        final_details = CustomerPODetail.objects.filter(
            customer_po__pk=po_id,
            company_id=company_id
        ).select_related('unit')
        
        context = {
            'final_details': final_details,
            'po_id': po_id, # Pass IDs for URLs in partials
            'customer_id': customer_id,
            'enquiry_id': enquiry_id,
        }
        return HttpResponse(render_to_string('sales/partials/_customer_po_detail_table.html', context, request))

class CustomerPODetailEditHTMXView(View):
    def get(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        final_detail = get_object_or_404(CustomerPODetail, pk=pk, company_id=company_id)
        form = CustomerPODetailForm(instance=final_detail)
        context = {'form': form, 'final_detail': final_detail, 'po_id': po_id, 'customer_id': customer_id, 'enquiry_id': enquiry_id}
        return HttpResponse(render_to_string('sales/partials/_customer_po_detail_form_edit.html', context, request))

    def post(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        final_detail = get_object_or_404(CustomerPODetail, pk=pk, company_id=company_id)
        form = CustomerPODetailForm(request.POST, instance=final_detail)
        if form.is_valid():
            form.save()
            messages.success(request, 'Final item updated successfully.')
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshFinalDetailTable'})
        else:
            context = {'form': form, 'final_detail': final_detail, 'po_id': po_id, 'customer_id': customer_id, 'enquiry_id': enquiry_id}
            return HttpResponse(render_to_string('sales/partials/_customer_po_detail_form_edit.html', context, request), status=400)

class CustomerPODetailDeleteHTMXView(View):
    def get(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        final_detail = get_object_or_404(CustomerPODetail, pk=pk, company_id=company_id)
        context = {'obj': final_detail, 'po_id': po_id, 'customer_id': customer_id, 'enquiry_id': enquiry_id, 'delete_url': reverse_lazy('sales:po_final_detail_delete', args=[po_id, customer_id, enquiry_id, pk])}
        return HttpResponse(render_to_string('sales/partials/_confirm_delete.html', context, request))

    def post(self, request, po_id, customer_id, enquiry_id, pk):
        user_context = get_user_context(request)
        company_id = user_context['company_id']
        final_detail = get_object_or_404(CustomerPODetail, pk=pk, company_id=company_id)
        final_detail.delete()
        messages.success(request, 'Final item deleted successfully.')
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshFinalDetailTable'})

```

#### 4.4 Templates (`sales/templates/sales/`)

**`customer_po_edit.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Customer PO - Edit</h1>

    <!-- Global Messages Area -->
    <div id="messages" class="mb-4">
        {% if messages %}
            {% for message in messages %}
                <div class="p-3 mb-2 rounded-md {% if message.tags %}alert-{{ message.tags }}{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Tab Container -->
    <div x-data="{ activeTab: 'tab{{ current_tab_index }}' }" class="bg-white shadow-lg rounded-lg">
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button 
                    @click="activeTab = 'tab0'" 
                    :class="{'border-indigo-500 text-indigo-600': activeTab === 'tab0', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'tab0'}" 
                    class="py-4 px-1 text-center border-b-2 font-medium text-sm transition-colors duration-200"
                    hx-get="{% url 'sales:po_edit_tab_content' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id tab_name='customer_details' %}"
                    hx-target="#tab-content"
                    hx-trigger="click"
                    hx-swap="innerHTML">
                    Customer Details
                </button>
                <button 
                    @click="activeTab = 'tab1'" 
                    :class="{'border-indigo-500 text-indigo-600': activeTab === 'tab1', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'tab1'}" 
                    class="py-4 px-1 text-center border-b-2 font-medium text-sm transition-colors duration-200"
                    hx-get="{% url 'sales:po_edit_tab_content' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id tab_name='goods_details' %}"
                    hx-target="#tab-content"
                    hx-trigger="click"
                    hx-swap="innerHTML">
                    Goods Details
                </button>
                <button 
                    @click="activeTab = 'tab2'" 
                    :class="{'border-indigo-500 text-indigo-600': activeTab === 'tab2', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'tab2'}" 
                    class="py-4 px-1 text-center border-b-2 font-medium text-sm transition-colors duration-200"
                    hx-get="{% url 'sales:po_edit_tab_content' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id tab_name='terms_conditions' %}"
                    hx-target="#tab-content"
                    hx-trigger="click"
                    hx-swap="innerHTML">
                    Terms & Conditions
                </button>
            </nav>
        </div>

        <!-- Tab Content Area -->
        <div id="tab-content" class="p-6 min-h-[500px]">
            <!-- Initial content loaded on page load -->
            {{ initial_tab_content|safe }} 
        </div>
    </div>

    <!-- Modals for forms and delete confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global event listener for HTMX messages
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Only process if HTMX has swapped content into #messages div
        if (event.detail.target.id === 'messages' || event.detail.xhr.getResponseHeader('HX-Trigger')) {
            const messagesDiv = document.getElementById('messages');
            if (messagesDiv.innerHTML.trim() !== '') {
                // Fade out messages after 5 seconds
                setTimeout(() => {
                    messagesDiv.innerHTML = ''; // Clear content
                }, 5000);
            }
        }
    });

    // Helper for clearing temporary item form after successful submission
    document.body.addEventListener('refreshTempDetailTable', function() {
        const tempItemForm = document.getElementById('tempItemForm');
        if (tempItemForm) {
            tempItemForm.reset(); // Reset the form fields
            // For select/dropdowns, manually set to first option or empty
            const unitSelect = tempItemForm.querySelector('select[name="unit"]');
            if (unitSelect) unitSelect.value = ''; // Or 'Select' option value
        }
    });
    
    // Auto-hide messages from initial page load
    document.addEventListener('DOMContentLoaded', () => {
        const messagesDiv = document.getElementById('messages');
        if (messagesDiv && messagesDiv.innerHTML.trim() !== '') {
            setTimeout(() => {
                messagesDiv.innerHTML = '';
            }, 5000);
        }
    });
</script>
{% endblock %}
```

**`partials/_customer_details_form.html`**

```html
<form method="post" action="{% url 'sales:po_edit_page' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id %}" enctype="multipart/form-data">
    {% csrf_token %}
    <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Details</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700">Name of Customer:</label>
            <p class="mt-1 text-gray-900">{{ customer_obj.customer_name }}</p>
            <input type="hidden" name="customer_id" value="{{ customer_obj.customer_id }}">
            <input type="hidden" name="po_id" value="{{ customer_po.pk }}">
            <input type="hidden" name="enquiry_id" value="{{ enquiry_id }}">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700">Enquiry No:</label>
            <p class="mt-1 text-gray-900">{{ enquiry_id }}</p>
        </div>
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Regd. Office Address:</label>
            <p class="mt-1 text-gray-900">{{ customer_obj.get_full_address }}</p>
        </div>

        <div class="col-span-1">
            <label for="{{ form.quotation_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Quotation No.</label>
            {{ form.quotation_no }}
            {% if form.quotation_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.quotation_no.errors }}</p>{% endif %}
        </div>
        <div class="col-span-1">
            <label for="{{ form.po_received_date.id_for_label }}" class="block text-sm font-medium text-gray-700">PO Received Date</label>
            {{ form.po_received_date }}
            {% if form.po_received_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.po_received_date.errors }}</p>{% endif %}
        </div>
        <div class="col-span-1">
            <label for="{{ form.po_date.id_for_label }}" class="block text-sm font-medium text-gray-700">PO Date</label>
            {{ form.po_date }}
            {% if form.po_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.po_date.errors }}</p>{% endif %}
        </div>
        <div class="col-span-1">
            <label for="{{ form.po_number.id_for_label }}" class="block text-sm font-medium text-gray-700">PO No</label>
            {{ form.po_number }}
            {% if form.po_number.errors %}<p class="text-red-500 text-xs mt-1">{{ form.po_number.errors }}</p>{% endif %}
        </div>
        <div class="col-span-1">
            <label for="{{ form.vendor_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Our Vendor Code</label>
            {{ form.vendor_code }}
            {% if form.vendor_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vendor_code.errors }}</p>{% endif %}
        </div>
    </div>

    <div class="mt-8 flex justify-end space-x-4">
        <button type="button" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'sales:po_edit_tab_content' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id tab_name='goods_details' %}"
                hx-target="#tab-content"
                hx-trigger="click"
                hx-swap="innerHTML"
                @click="$parent.activeTab = 'tab1'">
            Next
        </button>
        <a href="{% url 'sales:po_edit_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
    </div>
</form>
```

**`partials/_goods_details_form.html`**

```html
<h3 class="text-lg font-medium text-gray-900 mb-4">Goods Details</h3>

<!-- Form for adding new temporary items -->
<div class="mb-8 p-4 border border-gray-200 rounded-lg">
    <h4 class="text-md font-semibold text-gray-800 mb-3">Add New Item</h4>
    <form id="tempItemForm" hx-post="{% url 'sales:po_temp_detail_add' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id %}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="col-span-1 md:col-span-2 lg:col-span-4">
                <label for="{{ temp_detail_form.item_description.id_for_label }}" class="block text-sm font-medium text-gray-700">Description & Specification of goods</label>
                {{ temp_detail_form.item_description }}
                {% if temp_detail_form.item_description.errors %}<p class="text-red-500 text-xs mt-1">{{ temp_detail_form.item_description.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ temp_detail_form.total_quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">Total Qty of goods</label>
                {{ temp_detail_form.total_quantity }}
                {% if temp_detail_form.total_quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ temp_detail_form.total_quantity.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ temp_detail_form.rate_per_unit.id_for_label }}" class="block text-sm font-medium text-gray-700">Rate per unit</label>
                {{ temp_detail_form.rate_per_unit }}
                {% if temp_detail_form.rate_per_unit.errors %}<p class="text-red-500 text-xs mt-1">{{ temp_detail_form.rate_per_unit.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ temp_detail_form.discount_percentage.id_for_label }}" class="block text-sm font-medium text-gray-700">Discount (%)</label>
                {{ temp_detail_form.discount_percentage }}
                {% if temp_detail_form.discount_percentage.errors %}<p class="text-red-500 text-xs mt-1">{{ temp_detail_form.discount_percentage.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ temp_detail_form.unit.id_for_label }}" class="block text-sm font-medium text-gray-700">Unit</label>
                {{ temp_detail_form.unit }}
                {% if temp_detail_form.unit.errors %}<p class="text-red-500 text-xs mt-1">{{ temp_detail_form.unit.errors }}</p>{% endif %}
            </div>
        </div>
        <div class="mt-6 flex justify-end">
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
                <span id="loadingIndicator" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Submit
            </button>
        </div>
    </form>
</div>

<!-- Temporary Items Table (GridView1) -->
<h4 class="text-md font-semibold text-gray-800 mb-3">Items Pending Submission</h4>
<div id="temp_detail_table_container"
     hx-get="{% url 'sales:po_temp_detail_table' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id %}"
     hx-trigger="load, refreshTempDetailTable from:body"
     hx-swap="innerHTML">
    <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading Temporary Items...</p>
    </div>
</div>

<!-- Final Items Table (GridView2) -->
<h4 class="text-md font-semibold text-gray-800 mt-8 mb-3">Existing PO Items</h4>
<div id="final_detail_table_container"
     hx-get="{% url 'sales:po_final_detail_table' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id %}"
     hx-trigger="load, refreshFinalDetailTable from:body"
     hx-swap="innerHTML">
    <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading Existing PO Items...</p>
    </div>
</div>

<div class="mt-8 flex justify-end space-x-4">
    <button type="button" 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'sales:po_edit_tab_content' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id tab_name='terms_conditions' %}"
            hx-target="#tab-content"
            hx-trigger="click"
            hx-swap="innerHTML"
            @click="$parent.activeTab = 'tab2'">
        Next
    </button>
    <a href="{% url 'sales:po_edit_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
</div>
```

**`partials/_terms_conditions_form.html`**

```html
<form method="post" action="{% url 'sales:po_edit_page' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id %}" enctype="multipart/form-data">
    {% csrf_token %}
    <h3 class="text-lg font-medium text-gray-900 mb-4">Terms & Conditions</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ form.payment_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Payment Terms</label>
            {{ form.payment_terms }}
            {% if form.payment_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.payment_terms.errors }}</p>{% endif %}
            <datalist id="payment_terms_suggestions">
                {% for term in payment_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.pf_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">P & F</label>
            {{ form.pf_terms }}
            {% if form.pf_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf_terms.errors }}</p>{% endif %}
            <datalist id="pf_terms_suggestions">
                {% for term in pf_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.excise_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Excise / Service Tax</label>
            {{ form.excise_terms }}
            {% if form.excise_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.excise_terms.errors }}</p>{% endif %}
            <datalist id="excise_terms_suggestions">
                {% for term in excise_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.vat_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">VAT</label>
            {{ form.vat_terms }}
            {% if form.vat_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vat_terms.errors }}</p>{% endif %}
            <datalist id="vat_terms_suggestions">
                {% for term in vat_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.octroi_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Octroi</label>
            {{ form.octroi_terms }}
            {% if form.octroi_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.octroi_terms.errors }}</p>{% endif %}
            <datalist id="octroi_terms_suggestions">
                {% for term in octroi_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.warranty_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Warrenty</label>
            {{ form.warranty_terms }}
            {% if form.warranty_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.warranty_terms.errors }}</p>{% endif %}
            <datalist id="warranty_terms_suggestions">
                {% for term in warranty_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.insurance_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance</label>
            {{ form.insurance_terms }}
            {% if form.insurance_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.insurance_terms.errors }}</p>{% endif %}
            <datalist id="insurance_terms_suggestions">
                {% for term in insurance_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.transport_mode.id_for_label }}" class="block text-sm font-medium text-gray-700">Mode of Transport</label>
            {{ form.transport_mode }}
            {% if form.transport_mode.errors %}<p class="text-red-500 text-xs mt-1">{{ form.transport_mode.errors }}</p>{% endif %}
            <datalist id="transport_mode_suggestions">
                {% for term in transport_mode_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.gc_note_number.id_for_label }}" class="block text-sm font-medium text-gray-700">R.R./G.C. Note No.</label>
            {{ form.gc_note_number }}
            {% if form.gc_note_number.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gc_note_number.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.vehicle_registration_no.id_for_label }}" class="block text-sm font-medium text-gray-700">If by motor vehicle, it's registr. no</label>
            {{ form.vehicle_registration_no }}
            {% if form.vehicle_registration_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vehicle_registration_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.freight_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Freight</label>
            {{ form.freight_terms }}
            {% if form.freight_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.freight_terms.errors }}</p>{% endif %}
            <datalist id="freight_terms_suggestions">
                {% for term in freight_terms_suggestions %}<option value="{{ term }}">{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ form.cst_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">CST</label>
            {{ form.cst_terms }}
            {% if form.cst_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cst_terms.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.validity_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Validity</label>
            {{ form.validity_terms }}
            {% if form.validity_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.validity_terms.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.other_charges.id_for_label }}" class="block text-sm font-medium text-gray-700">Other Charges</label>
            {{ form.other_charges }}
            {% if form.other_charges.errors %}<p class="text-red-500 text-xs mt-1">{{ form.other_charges.errors }}</p>{% endif %}
        </div>
        <div class="md:col-span-2" id="attachment-section" hx-trigger="refreshAttachmentSection from:body" hx-swap="outerHTML" hx-get="{% url 'sales:get_attachment_section' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id %}">
            {% include 'sales/partials/_attachment_section.html' with customer_po=customer_po form=form %}
        </div>
        <div class="md:col-span-2">
            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
            {{ form.remarks }}
            {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
        </div>
    </div>

    <div class="mt-8 flex justify-end space-x-4">
        <button type="submit" name="btn_update_master" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2">Update</button>
        <a href="{% url 'sales:po_edit_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
    </div>
</form>

```

**`partials/_attachment_section.html` (Separate partial for attachment, to be loaded via HTMX on clear)**

```html
<div id="attachment-section">
    <label class="block text-sm font-medium text-gray-700">Attachment</label>
    {% if customer_po.attachment_file_name %}
        <div class="flex items-center space-x-2 mt-1">
            <a href="{% url 'sales:download_attachment' po_id=customer_po.pk %}" class="text-blue-600 hover:underline">{{ customer_po.attachment_file_name }}</a>
            <button type="submit" name="btn_clear_attachment" 
                    class="text-red-500 hover:text-red-700 text-sm font-medium"
                    hx-post="{% url 'sales:po_edit_page' po_id=customer_po.pk customer_id=customer_obj.pk enquiry_id=enquiry_id %}"
                    hx-swap="none"
                    hx-confirm="Are you sure you want to clear this attachment?"
                    hx-trigger="click">
                [Clear]
            </button>
        </div>
        <p class="text-xs text-gray-500 mt-1">File size: {{ customer_po.attachment_file_size|filesizeformat }}</p>
    {% else %}
        {{ form.attachment_file }}
        {% if form.attachment_file.errors %}<p class="text-red-500 text-xs mt-1">{{ form.attachment_file.errors }}</p>{% endif %}
    {% endif %}
</div>
```

**`partials/_customer_po_detail_temp_table.html`**

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="tempDetailTable" class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6">SN</th>
                <th scope="col" class="py-3 px-6">Description</th>
                <th scope="col" class="py-3 px-6">Unit</th>
                <th scope="col" class="py-3 px-6 text-right">Qty</th>
                <th scope="col" class="py-3 px-6 text-right">Rate</th>
                <th scope="col" class="py-3 px-6 text-right">Discount (%)</th>
                <th scope="col" class="py-3 px-6 text-right">Amount</th>
                <th scope="col" class="py-3 px-6 text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for item in temp_details %}
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-4 px-6">{{ item.item_description }}</td>
                <td class="py-4 px-6">{{ item.unit.symbol }}</td>
                <td class="py-4 px-6 text-right">{{ item.total_quantity }}</td>
                <td class="py-4 px-6 text-right">{{ item.rate_per_unit }}</td>
                <td class="py-4 px-6 text-right">{{ item.discount_percentage }}</td>
                <td class="py-4 px-6 text-right">{{ item.amount|floatformat:2 }}</td>
                <td class="py-4 px-6 text-center">
                    <button 
                        class="font-medium text-blue-600 hover:underline mr-2"
                        hx-get="{% url 'sales:po_temp_detail_edit' po_id=po_id customer_id=customer_id enquiry_id=enquiry_id pk=item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="font-medium text-red-600 hover:underline"
                        hx-get="{% url 'sales:po_temp_detail_delete' po_id=po_id customer_id=customer_id enquiry_id=enquiry_id pk=item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-gray-500">No temporary items to display.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    // Ensure this script is only executed once by checking if DataTable is already initialized
    if (!$.fn.DataTable.isDataTable('#tempDetailTable')) {
        $('#tempDetailTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    }
</script>
```

**`partials/_customer_po_detail_table.html`**

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="finalDetailTable" class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6">SN</th>
                <th scope="col" class="py-3 px-6">Description</th>
                <th scope="col" class="py-3 px-6">Unit</th>
                <th scope="col" class="py-3 px-6 text-right">Qty</th>
                <th scope="col" class="py-3 px-6 text-right">Rate</th>
                <th scope="col" class="py-3 px-6 text-right">Discount (%)</th>
                <th scope="col" class="py-3 px-6 text-right">Amount</th>
                <th scope="col" class="py-3 px-6 text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for item in final_details %}
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-4 px-6">{{ item.item_description }}</td>
                <td class="py-4 px-6">{{ item.unit.symbol }}</td>
                <td class="py-4 px-6 text-right">{{ item.total_quantity }}</td>
                <td class="py-4 px-6 text-right">{{ item.rate_per_unit }}</td>
                <td class="py-4 px-6 text-right">{{ item.discount_percentage }}</td>
                <td class="py-4 px-6 text-right">{{ item.amount|floatformat:2 }}</td>
                <td class="py-4 px-6 text-center">
                    <button 
                        class="font-medium text-blue-600 hover:underline mr-2"
                        hx-get="{% url 'sales:po_final_detail_edit' po_id=po_id customer_id=customer_id enquiry_id=enquiry_id pk=item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="font-medium text-red-600 hover:underline"
                        hx-get="{% url 'sales:po_final_detail_delete' po_id=po_id customer_id=customer_id enquiry_id=enquiry_id pk=item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-gray-500">No existing PO items to display.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    if (!$.fn.DataTable.isDataTable('#finalDetailTable')) {
        $('#finalDetailTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    }
</script>
```

**`partials/_customer_po_detail_temp_form_edit.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Temporary PO Item</h3>
    <form hx-post="{% url 'sales:po_temp_detail_edit' po_id=po_id customer_id=customer_id enquiry_id=enquiry_id pk=temp_detail.pk %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**`partials/_customer_po_detail_form_edit.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Existing PO Item</h3>
    <form hx-post="{% url 'sales:po_final_detail_edit' po_id=po_id customer_id=customer_id enquiry_id=enquiry_id pk=final_detail.pk %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**`partials/_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this item? This action cannot be undone.</p>
    
    <div class="bg-red-50 p-4 rounded-md mb-6">
        <p class="text-red-700 font-medium">Item: "{{ obj }}"</p>
    </div>

    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <form hx-post="{{ delete_url }}" hx-swap="none" class="inline-block">
            {% csrf_token %}
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </form>
    </div>
</div>
```

#### 4.5 URLs (`sales/urls.py`)

```python
from django.urls import path
from .views import (
    CustomerPOEditView, 
    TabContentHTMXView,
    CustomerPODetailTempAddHTMXView, 
    CustomerPODetailTempTableHTMXView,
    CustomerPODetailTempEditHTMXView,
    CustomerPODetailTempDeleteHTMXView,
    CustomerPODetailTableHTMXView,
    CustomerPODetailEditHTMXView,
    CustomerPODetailDeleteHTMXView,
)

app_name = 'sales'

urlpatterns = [
    # Main PO Edit Page
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/', 
        CustomerPOEditView.as_view(), 
        name='po_edit_page'
    ),
    # HTMX endpoint for tab content loading
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/tab/<str:tab_name>/', 
        TabContentHTMXView.as_view(), 
        name='po_edit_tab_content'
    ),

    # HTMX endpoints for Temporary PO Details (GridView1)
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/temp-details/add/', 
        CustomerPODetailTempAddHTMXView.as_view(), 
        name='po_temp_detail_add'
    ),
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/temp-details/table/', 
        CustomerPODetailTempTableHTMXView.as_view(), 
        name='po_temp_detail_table'
    ),
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/temp-details/edit/<int:pk>/', 
        CustomerPODetailTempEditHTMXView.as_view(), 
        name='po_temp_detail_edit'
    ),
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/temp-details/delete/<int:pk>/', 
        CustomerPODetailTempDeleteHTMXView.as_view(), 
        name='po_temp_detail_delete'
    ),

    # HTMX endpoints for Final PO Details (GridView2)
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/final-details/table/', 
        CustomerPODetailTableHTMXView.as_view(), 
        name='po_final_detail_table'
    ),
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/final-details/edit/<int:pk>/', 
        CustomerPODetailEditHTMXView.as_view(), 
        name='po_final_detail_edit'
    ),
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/final-details/delete/<int:pk>/', 
        CustomerPODetailDeleteHTMXView.as_view(), 
        name='po_final_detail_delete'
    ),

    # Placeholder for the redirect after update/cancel (CustPO_Edit.aspx)
    path('customer-po/', CustomerPOEditView.as_view(template_name='sales/po_list.html'), name='po_edit_list'), # Assuming a list page for POs
    
    # Endpoint for attachment section refresh
    path(
        'customer-po/edit/<str:po_id>/<str:customer_id>/<int:enquiry_id>/attachment-section/',
        lambda request, po_id, customer_id, enquiry_id: HttpResponse(
            render_to_string(
                'sales/partials/_attachment_section.html', 
                {
                    'customer_po': CustomerPO.objects.get(pk=po_id),
                    'form': CustomerPOForm(instance=CustomerPO.objects.get(pk=po_id)), # A form instance for the file upload field
                    'customer_obj': Customer.objects.get(pk=customer_id),
                    'enquiry_id': enquiry_id,
                }, 
                request
            )
        ),
        name='get_attachment_section'
    ),
    # Endpoint to download attachment
    path(
        'customer-po/download-attachment/<str:po_id>/', 
        lambda request, po_id: download_attachment_view(request, po_id), 
        name='download_attachment'
    ),
]

# Helper view for attachment download (to keep it concise, put here for now)
def download_attachment_view(request, po_id):
    customer_po = get_object_or_404(CustomerPO, pk=po_id)
    if customer_po.attachment_file_data and customer_po.attachment_file_name:
        response = HttpResponse(customer_po.attachment_file_data, content_type=customer_po.attachment_content_type)
        response['Content-Disposition'] = f'attachment; filename="{customer_po.attachment_file_name}"'
        return response
    raise Http404("Attachment not found or not available.")

```

#### 4.6 Tests (`sales/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.http import HttpResponse
from decimal import Decimal
import datetime
from unittest.mock import patch, MagicMock

# Import all models
from .models import (
    Unit, Customer, CustomerQuotation, CustomerPO, 
    CustomerPODetail, CustomerPODetailTemp
)

# Mock user for authentication (Django's built-in User model)
from django.contrib.auth.models import User

class SalesModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs
        cls.unit = Unit.objects.create(id=1, symbol='Pcs')
        cls.country = MagicMock(cid=1, country_name='India') # Mock if not managed
        cls.state = MagicMock(sid=1, state_name='Maharashtra') # Mock if not managed
        cls.city = MagicMock(city_id=1, city_name='Mumbai') # Mock if not managed

        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Test Customer',
            regd_address='123 Test St',
            regd_country_id=1, regd_state_id=1, regd_city_id=1,
            regd_pin_no='123456'
        )
        # Mocking get_full_address for Customer model if Country/State/City are not real models
        cls.customer.get_full_address = MagicMock(return_value=f"{cls.customer.regd_address}, Mumbai, Maharashtra, India. 123456")

        cls.quotation = CustomerQuotation.objects.create(
            id=101,
            quotation_no='Q-2024-001',
            system_date='01-01-2024',
            company_id=1,
            financial_year_id=2024,
            enquiry_id=1001
        )

        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.company_id = 1
        cls.financial_year_id = 2024
        cls.session_id = 'testuser'

        # Create a CustomerPO instance
        cls.customer_po = CustomerPO.objects.create(
            id='PO-2024-001',
            customer=cls.customer,
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            session_id=cls.session_id,
            po_number='PO001',
            po_date='10-01-2024',
            po_received_date='05-01-2024',
            vendor_code='V001',
            quotation=cls.quotation,
            payment_terms='30 Days', pf_terms='5%', vat_terms='18%', excise_terms='10%',
            octroi_terms='2%', warranty_terms='1 Year', insurance_terms='Yes', transport_mode='Road',
            gc_note_number='GC001', vehicle_registration_no='MH01AB1234', freight_terms='Paid',
            remarks='Test remarks', cst_terms='2%', validity_terms='30 Days', other_charges='None',
            attachment_file_name=None, attachment_file_size=0, attachment_content_type=None, attachment_file_data=None
        )

    def test_unit_creation(self):
        self.assertEqual(self.unit.symbol, 'Pcs')

    def test_customer_creation(self):
        self.assertEqual(self.customer.customer_name, 'Test Customer')
        self.assertEqual(self.customer.get_full_address(), '123 Test St, Mumbai, Maharashtra, India. 123456')

    def test_customer_quotation_creation(self):
        self.assertEqual(self.quotation.quotation_no, 'Q-2024-001')
        self.assertEqual(str(self.quotation), 'Q-2024-001[01-01-2024]')

    def test_customer_po_creation(self):
        self.assertEqual(self.customer_po.po_number, 'PO001')
        self.assertEqual(self.customer_po.customer.customer_name, 'Test Customer')

    def test_customer_po_detail_temp_creation_and_amount(self):
        temp_item = CustomerPODetailTemp.objects.create(
            id=1,
            session_id=self.session_id,
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            item_description='Test Temp Item',
            total_quantity=Decimal('10.000'),
            unit=self.unit,
            rate_per_unit=Decimal('100.00'),
            discount_percentage=Decimal('10.00')
        )
        self.assertEqual(temp_item.item_description, 'Test Temp Item')
        self.assertAlmostEqual(temp_item.amount, Decimal('900.00')) # 10 * (100 - 100*0.10)

    def test_customer_po_detail_creation_and_amount(self):
        final_item = CustomerPODetail.objects.create(
            id=1,
            customer_po=self.customer_po,
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            item_description='Test Final Item',
            total_quantity=Decimal('5.000'),
            unit=self.unit,
            rate_per_unit=Decimal('200.00'),
            discount_percentage=Decimal('5.00')
        )
        self.assertEqual(final_item.item_description, 'Test Final Item')
        self.assertAlmostEqual(final_item.amount, Decimal('950.00')) # 5 * (200 - 200*0.05)

    def test_customer_po_update_attachment(self):
        mock_file = MagicMock()
        mock_file.name = 'test.pdf'
        mock_file.size = 12345
        mock_file.content_type = 'application/pdf'
        mock_file.read.return_value = b'filedata'
        
        self.customer_po.update_attachment(mock_file)
        self.customer_po.refresh_from_db()
        self.assertEqual(self.customer_po.attachment_file_name, 'test.pdf')
        self.assertEqual(self.customer_po.attachment_file_size, 12345)
        self.assertEqual(self.customer_po.attachment_content_type, 'application/pdf')
        self.assertEqual(self.customer_po.attachment_file_data, b'filedata')

    def test_customer_po_clear_attachment(self):
        # First set an attachment
        self.customer_po.attachment_file_name = 'old.txt'
        self.customer_po.attachment_file_size = 100
        self.customer_po.attachment_content_type = 'text/plain'
        self.customer_po.attachment_file_data = b'olddata'
        self.customer_po.save()

        self.customer_po.clear_attachment()
        self.customer_po.refresh_from_db()
        self.assertIsNone(self.customer_po.attachment_file_name)
        self.assertEqual(self.customer_po.attachment_file_size, 0)
        self.assertIsNone(self.customer_po.attachment_content_type)
        self.assertIsNone(self.customer_po.attachment_file_data)

    def test_customer_po_detail_temp_transfer_to_final(self):
        # Create temp items
        temp_item1 = CustomerPODetailTemp.objects.create(
            id=2, session_id=self.session_id, company_id=self.company_id, financial_year_id=self.financial_year_id,
            item_description='Temp Item 1', total_quantity=Decimal('1.0'), unit=self.unit, rate_per_unit=Decimal('10.0'), discount_percentage=Decimal('0.0')
        )
        temp_item2 = CustomerPODetailTemp.objects.create(
            id=3, session_id=self.session_id, company_id=self.company_id, financial_year_id=self.financial_year_id,
            item_description='Temp Item 2', total_quantity=Decimal('2.0'), unit=self.unit, rate_per_unit=Decimal('20.0'), discount_percentage=Decimal('0.0')
        )

        initial_temp_count = CustomerPODetailTemp.objects.filter(session_id=self.session_id).count()
        initial_final_count = CustomerPODetail.objects.filter(customer_po=self.customer_po).count()

        transferred_count = CustomerPODetailTemp.transfer_to_final_details(
            self.session_id, self.company_id, self.financial_year_id, self.customer_po
        )

        self.assertEqual(transferred_count, initial_temp_count)
        self.assertEqual(CustomerPODetailTemp.objects.filter(session_id=self.session_id).count(), 0) # Temp items should be cleared
        self.assertEqual(CustomerPODetail.objects.filter(customer_po=self.customer_po).count(), initial_final_count + initial_temp_count)


class SalesViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common test data
        cls.unit = Unit.objects.create(id=1, symbol='Pcs')
        cls.customer = Customer.objects.create(
            customer_id='CUST001', customer_name='Test Customer', regd_address='123 Test St',
            regd_country_id=1, regd_state_id=1, regd_city_id=1, regd_pin_no='123456'
        )
        cls.customer.get_full_address = MagicMock(return_value=f"{cls.customer.regd_address}, City, State, Country. 123456")

        cls.quotation = CustomerQuotation.objects.create(
            id=101, quotation_no='Q-2024-001', system_date='01-01-2024', company_id=1, financial_year_id=2024, enquiry_id=1001
        )
        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.company_id = 1
        cls.financial_year_id = 2024
        cls.session_id = 'testuser' # Matches cls.user.username

        cls.customer_po = CustomerPO.objects.create(
            id='PO-2024-001', customer=cls.customer, company_id=cls.company_id, financial_year_id=cls.financial_year_id,
            session_id=cls.session_id, po_number='PO001', po_date='10-01-2024', po_received_date='05-01-2024', vendor_code='V001',
            quotation=cls.quotation,
            payment_terms='30 Days', pf_terms='5%', vat_terms='18%', excise_terms='10%',
            octroi_terms='2%', warranty_terms='1 Year', insurance_terms='Yes', transport_mode='Road',
            gc_note_number='GC001', vehicle_registration_no='MH01AB1234', freight_terms='Paid',
            remarks='Test remarks', cst_terms='2%', validity_terms='30 Days', other_charges='None'
        )

        # Create some initial temp and final details
        CustomerPODetailTemp.objects.create(
            id=1, session_id=cls.session_id, company_id=cls.company_id, financial_year_id=cls.financial_year_id,
            item_description='Temp Item 1', total_quantity=Decimal('10.0'), unit=cls.unit, rate_per_unit=Decimal('100.0'), discount_percentage=Decimal('10.0')
        )
        CustomerPODetail.objects.create(
            id=1, customer_po=cls.customer_po, company_id=cls.company_id, financial_year_id=cls.financial_year_id,
            item_description='Final Item 1', total_quantity=Decimal('5.0'), unit=cls.unit, rate_per_unit=Decimal('200.0'), discount_percentage=Decimal('5.0')
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        self.client.session['compid'] = self.company_id
        self.client.session['finyear'] = self.financial_year_id
        
        # Patch the get_user_context to ensure consistent context for tests
        patcher = patch('sales.views.get_user_context')
        self.mock_get_user_context = patcher.start()
        self.mock_get_user_context.return_value = {
            'session_id': 'testuser',
            'company_id': self.company_id,
            'financial_year_id': self.financial_year_id
        }
        self.addCleanup(patcher.stop)


    def test_customer_po_edit_view_get(self):
        url = reverse('sales:po_edit_page', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customer_po_edit.html')
        self.assertContains(response, 'Customer PO - Edit')
        self.assertContains(response, 'Customer Details') # Check for tab content

    def test_customer_po_edit_view_post_update_master_success(self):
        url = reverse('sales:po_edit_page', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        
        # Data for updating CustomerPOForm
        data = {
            'quotation_no': self.quotation.pk,
            'po_number': 'PO001_UPDATED',
            'po_date': '15-01-2024',
            'po_received_date': '08-01-2024',
            'vendor_code': 'V001_UPDATED',
            'payment_terms': '60 Days',
            'pf_terms': '7%',
            'vat_terms': '20%',
            'excise_terms': '12%',
            'octroi_terms': '3%',
            'warranty_terms': '2 Years',
            'insurance_terms': 'No',
            'transport_mode': 'Air',
            'gc_note_number': 'GC002',
            'vehicle_registration_no': 'KA01CD5678',
            'freight_terms': 'Collect',
            'remarks': 'Updated remarks',
            'cst_terms': '3%',
            'validity_terms': '60 Days',
            'other_charges': 'Handling Fee',
            'btn_update_master': '', # Indicate master form submission
        }
        
        # Create a mock uploaded file
        from io import BytesIO
        uploaded_file = BytesIO(b"dummy file content")
        uploaded_file.name = "test_doc.txt"
        uploaded_file.size = len(uploaded_file.read()) # Re-read to get correct size after initial read
        uploaded_file.seek(0) # Reset pointer
        uploaded_file.content_type = "text/plain"

        with patch('django.core.files.uploadedfile.InMemoryUploadedFile', MagicMock(return_value=uploaded_file)):
            response = self.client.post(url, data, follow=True) # follow=True to follow redirects

        self.assertEqual(response.status_code, 200) # Should be 200 after redirect
        self.assertRedirects(response, reverse('sales:po_edit_list') + '?msg=Customer PO is updated.')
        
        # Verify messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Customer PO updated successfully.')

        # Verify data updated in DB
        updated_po = CustomerPO.objects.get(pk=self.customer_po.pk)
        self.assertEqual(updated_po.po_number, 'PO001_UPDATED')
        self.assertEqual(updated_po.po_date, '15-01-2024')
        self.assertEqual(updated_po.attachment_file_name, 'test_doc.txt')
        self.assertEqual(updated_po.attachment_file_data, b'dummy file content')

        # Verify temporary items are transferred and cleared
        temp_items_after_transfer = CustomerPODetailTemp.objects.filter(session_id=self.session_id).count()
        self.assertEqual(temp_items_after_transfer, 0)
        # Check if the transferred item exists in final details (need to know initial count)
        final_details_count = CustomerPODetail.objects.filter(customer_po=updated_po).count()
        self.assertGreater(final_details_count, 0) # Should be at least 1 (the pre-existing one) + 1 (the temp one)

    def test_customer_po_edit_view_post_update_master_validation_error(self):
        url = reverse('sales:po_edit_page', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        data = {
            'quotation_no': self.quotation.pk,
            'po_number': '', # Missing required field
            'po_date': 'INVALID_DATE', # Invalid date format
            'po_received_date': '08-01-2024',
            'vendor_code': 'V001_UPDATED',
            'payment_terms': '60 Days',
            'pf_terms': '7%',
            'vat_terms': '20%',
            'excise_terms': '12%',
            'octroi_terms': '3%',
            'warranty_terms': '2 Years',
            'insurance_terms': 'No',
            'transport_mode': 'Air',
            'gc_note_number': 'GC002',
            'vehicle_registration_no': 'KA01CD5678',
            'freight_terms': 'Collect',
            'remarks': 'Updated remarks',
            'cst_terms': '3%',
            'validity_terms': '60 Days',
            'other_charges': 'Handling Fee',
            'btn_update_master': '',
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200) # Should render the form with errors
        self.assertContains(response, 'Please correct the errors in the form.')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Invalid date format (DD-MM-YYYY).')
        self.assertTemplateUsed(response, 'sales/customer_po_edit.html') # Main template is re-rendered

    def test_customer_po_edit_view_post_clear_attachment(self):
        # Set an attachment first
        self.customer_po.attachment_file_name = 'existing.pdf'
        self.customer_po.attachment_file_size = 500
        self.customer_po.attachment_content_type = 'application/pdf'
        self.customer_po.attachment_file_data = b'existing_data'
        self.customer_po.save()

        url = reverse('sales:po_edit_page', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        response = self.client.post(url, {'btn_clear_attachment': ''}, follow=True) # HTMX post with follow redirect

        self.assertEqual(response.status_code, 200) # Should be 200 after redirect
        self.assertRedirects(response, url) # Redirects back to the same page
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Attachment cleared successfully.')

        updated_po = CustomerPO.objects.get(pk=self.customer_po.pk)
        self.assertIsNone(updated_po.attachment_file_name)
        self.assertEqual(updated_po.attachment_file_size, 0)
        self.assertIsNone(updated_po.attachment_content_type)
        self.assertIsNone(updated_po.attachment_file_data)

    def test_tab_content_htmx_view(self):
        url = reverse('sales:po_edit_tab_content', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, 'goods_details'])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/partials/_goods_details_form.html')
        self.assertContains(response, 'Add New Item')
        self.assertContains(response, 'Items Pending Submission')
        self.assertEqual(self.client.session.get('active_tab_index'), 1) # Check session update

    def test_po_temp_detail_add_htmx_view_success(self):
        url = reverse('sales:po_temp_detail_add', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        data = {
            'item_description': 'New Temp Item',
            'total_quantity': '1.5',
            'unit': self.unit.pk,
            'rate_per_unit': '50.00',
            'discount_percentage': '5.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HX-Swap="none" success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTempDetailTable')
        self.assertTrue(CustomerPODetailTemp.objects.filter(item_description='New Temp Item').exists())

    def test_po_temp_detail_add_htmx_view_validation_error(self):
        url = reverse('sales:po_temp_detail_add', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        data = {
            'item_description': '', # Missing
            'total_quantity': 'invalid', # Invalid
            'unit': self.unit.pk,
            'rate_per_unit': '50.00',
            'discount_percentage': '5.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, data, **headers)
        self.assertEqual(response.status_code, 400) # Bad Request for validation errors
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Invalid quantity format')
        self.assertTemplateUsed(response, 'sales/partials/_temp_po_item_form.html') # Form re-rendered

    def test_po_temp_detail_table_htmx_view(self):
        url = reverse('sales:po_temp_detail_table', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/partials/_customer_po_detail_temp_table.html')
        self.assertContains(response, 'Temp Item 1') # Checks if initial temp item is there

    def test_po_temp_detail_edit_htmx_view_get(self):
        temp_item = CustomerPODetailTemp.objects.filter(session_id=self.session_id).first()
        url = reverse('sales:po_temp_detail_edit', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, temp_item.pk])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/partials/_customer_po_detail_temp_form_edit.html')
        self.assertContains(response, 'Edit Temporary PO Item')
        self.assertContains(response, temp_item.item_description)

    def test_po_temp_detail_edit_htmx_view_post_success(self):
        temp_item = CustomerPODetailTemp.objects.filter(session_id=self.session_id).first()
        url = reverse('sales:po_temp_detail_edit', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, temp_item.pk])
        data = {
            'item_description': 'Updated Temp Item',
            'total_quantity': '12.0',
            'unit': self.unit.pk,
            'rate_per_unit': '110.00',
            'discount_percentage': '8.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTempDetailTable')
        temp_item.refresh_from_db()
        self.assertEqual(temp_item.item_description, 'Updated Temp Item')

    def test_po_temp_detail_delete_htmx_view_get(self):
        temp_item = CustomerPODetailTemp.objects.filter(session_id=self.session_id).first()
        url = reverse('sales:po_temp_detail_delete', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, temp_item.pk])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/partials/_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, temp_item.item_description)

    def test_po_temp_detail_delete_htmx_view_post(self):
        temp_item = CustomerPODetailTemp.objects.filter(session_id=self.session_id).first()
        initial_count = CustomerPODetailTemp.objects.filter(session_id=self.session_id).count()
        url = reverse('sales:po_temp_detail_delete', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, temp_item.pk])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTempDetailTable')
        self.assertEqual(CustomerPODetailTemp.objects.filter(session_id=self.session_id).count(), initial_count - 1)
        self.assertFalse(CustomerPODetailTemp.objects.filter(pk=temp_item.pk).exists())

    def test_po_final_detail_table_htmx_view(self):
        url = reverse('sales:po_final_detail_table', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/partials/_customer_po_detail_table.html')
        self.assertContains(response, 'Final Item 1')

    def test_po_final_detail_edit_htmx_view_get(self):
        final_item = CustomerPODetail.objects.filter(customer_po=self.customer_po).first()
        url = reverse('sales:po_final_detail_edit', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, final_item.pk])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/partials/_customer_po_detail_form_edit.html')
        self.assertContains(response, 'Edit Existing PO Item')
        self.assertContains(response, final_item.item_description)

    def test_po_final_detail_edit_htmx_view_post_success(self):
        final_item = CustomerPODetail.objects.filter(customer_po=self.customer_po).first()
        url = reverse('sales:po_final_detail_edit', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, final_item.pk])
        data = {
            'item_description': 'Updated Final Item',
            'total_quantity': '6.0',
            'unit': self.unit.pk,
            'rate_per_unit': '220.00',
            'discount_percentage': '4.00'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinalDetailTable')
        final_item.refresh_from_db()
        self.assertEqual(final_item.item_description, 'Updated Final Item')

    def test_po_final_detail_delete_htmx_view_get(self):
        final_item = CustomerPODetail.objects.filter(customer_po=self.customer_po).first()
        url = reverse('sales:po_final_detail_delete', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, final_item.pk])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/partials/_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, final_item.item_description)

    def test_po_final_detail_delete_htmx_view_post(self):
        final_item = CustomerPODetail.objects.filter(customer_po=self.customer_po).first()
        initial_count = CustomerPODetail.objects.filter(customer_po=self.customer_po).count()
        url = reverse('sales:po_final_detail_delete', args=[self.customer_po.pk, self.customer.pk, self.quotation.enquiry_id, final_item.pk])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinalDetailTable')
        self.assertEqual(CustomerPODetail.objects.filter(customer_po=self.customer_po).count(), initial_count - 1)
        self.assertFalse(CustomerPODetail.objects.filter(pk=final_item.pk).exists())
```

### Step 5: HTMX and Alpine.js Integration

*   **Tab Navigation:** Implemented using HTMX `hx-get` on tab buttons targeting `#tab-content`, and Alpine.js for visual active tab state (`x-data`, `:class`, `@click`).
*   **Form Submissions:** All form submissions are `hx-post` with `hx-swap="none"`. Success triggers an `HX-Trigger` header (e.g., `refreshTempDetailTable`, `refreshFinalDetailTable`, `refreshAttachmentSection`) to refresh the relevant table/section. Validation errors re-render the form partial with errors (`status=400`).
*   **Inline Editing/Deletion:** Edit/Delete buttons use `hx-get` to load the respective form/confirmation partials into a modal (`#modalContent`). The modal's visibility is controlled by Alpine.js `add .is-active to #modal`. Form submissions within the modal then trigger the table refresh.
*   **DataTables:** JavaScript for DataTables initialization is included directly within the table partials (`_customer_po_detail_temp_table.html`, `_customer_po_detail_table.html`). This ensures DataTables is initialized *after* the content is loaded into the DOM by HTMX. A check `!$.fn.DataTable.isDataTable()` prevents re-initialization.
*   **Global Messages:** Django's `messages` framework is used. A global HTMX event listener in `customer_po_edit.html` is added to automatically clear messages after a few seconds.
*   **Date Pickers:** HTML5 `type="date"` input is used for `po_date` and `po_received_date`. This leverages browser-native date pickers, aligning with the minimal JS approach.
*   **Auto-Suggestions (Datalists):** Text inputs for terms (Payment, PF, etc.) use HTML5 `<datalist>` elements. The options for these datalists are populated from distinct values in the `CustomerPO` table, mimicking the ASP.NET `DropDownExtender` functionality with simpler, native HTML.
*   **File Upload/Clear:** The attachment section is a separate HTMX-loadable partial (`_attachment_section.html`). Clearing an attachment is a `hx-post` to the main view, which updates the `CustomerPO` model and then triggers a `refreshAttachmentSection` to reload just that part of the UI.
*   **Loading Indicators:** Simple `htmx-indicator` class for visual feedback during HTMX requests.

This plan systematically deconstructs the ASP.NET application's logic and UI, translating it into a modern, maintainable, and performant Django-HTMX-Alpine.js architecture. The focus on automation and clear separation of concerns ensures a smoother migration process and a robust final product.