## ASP.NET to Django Conversion Script: Work Order Edit Details

This document outlines a strategic plan to modernize your existing ASP.NET "Work Order - Edit" page to a robust, maintainable Django application. Our approach focuses on leveraging AI-assisted automation, adhering to modern Django best practices, and ensuring a seamless transition with enhanced performance and user experience.

The core business value of this modernization lies in:
*   **Improved Efficiency:** By breaking down the monolithic ASP.NET page into modular Django components, future development, maintenance, and debugging become significantly faster and less error-prone.
*   **Enhanced User Experience:** Utilizing HTMX and Alpine.js eliminates full-page reloads, providing a snappier, more interactive interface akin to a single-page application but with the simplicity of server-rendered HTML. DataTables will provide powerful client-side features for product lists, making data management intuitive.
*   **Scalability & Performance:** Django's ORM and efficient request handling, combined with a thin-view architecture, will lead to a more scalable and performant application capable of handling increased user loads.
*   **Reduced Technical Debt:** Migrating from a legacy framework to a modern one like Django significantly reduces technical debt, making your application easier to update, secure, and integrate with other systems.
*   **Future-Proofing:** Adopting a popular, actively maintained framework like Django ensures your application benefits from ongoing community support, security updates, and a vast ecosystem of tools and libraries.

---

### Step 1: Extract Database Schema

**Task:** We've identified the database tables and their columns involved in the "Work Order - Edit" functionality from your ASP.NET code.

**Instructions:**
The primary tables are for Work Order Master details and Product details. There's also a temporary table for product additions during the editing session.

*   **`SD_Cust_WorkOrder_Master` (for main Work Order details)**
    *   `Id` (Primary Key, inferred)
    *   `CustomerId` (Foreign Key, from `Request.QueryString["CustomerId"]`, `hfCustId.Text`)
    *   `EnqId` (Foreign Key, from `Request.QueryString["EnqId"]`, `hfEnqId.Text`)
    *   `PONo` (String, from `Request.QueryString["PONo"]`, `lblPONo.Text`)
    *   `WOId` (String, from `Request.QueryString["Id"]`, `lblWONo.Text` implies this is `WONo` from DB)
    *   `CId` (Foreign Key to Category, inferred from `lblCategory` logic)
    *   `WONo` (String, `lblWONo.Text`)
    *   `TaskWorkOrderDate` (Date, from `txtWorkOrderDate`)
    *   `TaskProjectTitle` (String, from `txtProjectTitle`)
    *   `TaskProjectLeader` (String, from `txtProjectLeader`)
    *   `TaskBusinessGroup` (Foreign Key to Business Group, from `DDLBusinessGroup`)
    *   `Buyer` (Foreign Key to Buyer, from `DDLBuyer`)
    *   `TaskTargetDAP_FDate` (Date, from `txtTaskTargetDAP_FDate`)
    *   `TaskTargetDAP_TDate` (Date, from `txtTaskTargetDAP_TDate`)
    *   `TaskDesignFinalization_FDate` (Date, from `txtTaskDesignFinalization_FDate`)
    *   `TaskDesignFinalization_TDate` (Date, from `txtTaskDesignFinalization_TDate`)
    *   `TaskTargetManufg_FDate` (Date, from `txtTaskTargetManufg_FDate`)
    *   `TaskTargetManufg_TDate` (Date, from `txtTaskTargetManufg_TDate`)
    *   `TaskTargetTryOut_FDate` (Date, from `txtTaskTargetTryOut_FDate`)
    *   `TaskTargetTryOut_TDate` (Date, from `txtTaskTargetTryOut_TDate`)
    *   `TaskTargetDespach_FDate` (Date, from `txtTaskTargetDespach_FDate`)
    *   `TaskTargetDespach_TDate` (Date, from `txtTaskTargetDespach_TDate`)
    *   `TaskTargetAssembly_FDate` (Date, from `txtTaskTargetAssembly_FDate`)
    *   `TaskTargetAssembly_TDate` (Date, from `txtTaskTargetAssembly_TDate`)
    *   `TaskTargetInstalation_FDate` (Date, from `txtTaskTargetInstalation_FDate`)
    *   `TaskTargetInstalation_TDate` (Date, from `txtTaskTargetInstalation_TDate`)
    *   `TaskCustInspection_FDate` (Date, from `txtTaskCustInspection_FDate`)
    *   `TaskCustInspection_TDate` (Date, from `txtTaskCustInspection_TDate`)
    *   `ManufMaterialDate` (Date, from `txtManufMaterialDate`)
    *   `BoughtoutMaterialDate` (Date, from `txtBoughtoutMaterialDate`)
    *   `ShippingAdd` (Text, from `txtShippingAdd`)
    *   `ShippingCountry` (Foreign Key to Country, from `DDLShippingCountry`)
    *   `ShippingState` (Foreign Key to State, from `DDLShippingState`)
    *   `ShippingCity` (Foreign Key to City, from `DDLShippingCity`)
    *   `ShippingContactPerson1` (String, from `txtShippingContactPerson1`)
    *   `ShippingContactNo1` (String, from `txtShippingContactNo1`)
    *   `ShippingEmail1` (String, from `txtShippingEmail1`)
    *   `ShippingContactPerson2` (String, from `txtShippingContactPerson2`)
    *   `ShippingContactNo2` (String, from `txtShippingContactNo2`)
    *   `ShippingEmail2` (String, from `txtShippingEmail2`)
    *   `ShippingFaxNo` (String, from `txtShippingFaxNo`)
    *   `ShippingEccNo` (String, from `txtShippingEccNo`)
    *   `ShippingTinCstNo` (String, from `txtShippingTinCstNo`)
    *   `ShippingTinVatNo` (String, from `txtShippingTinVatNo`)
    *   `InstractionPrimerPainting` (Boolean, from `CKInstractionPrimerPainting`)
    *   `InstractionPainting` (Boolean, from `CKInstractionPainting`)
    *   `InstractionSelfCertRept` (Boolean, from `CKInstractionSelfCertRept`)
    *   `InstractionOther` (Text, from `txtInstractionOther`)
    *   `InstractionExportCaseMark` (String, from `txtInstractionExportCaseMark`)
    *   `InstractionAttachAnnexure` (String, inferred, currently empty)
    *   `CompId` (Foreign Key to Company, from `Session["compid"]`)
    *   `FinYearId` (Foreign Key to Financial Year, from `Session["finyear"]`)
    *   `SessionId` (String, from `Session["username"]`)
    *   `SysDate` (Date, `fun.getCurrDate()`)
    *   `SysTime` (Time, `fun.getCurrTime()`)

*   **`SD_Cust_WorkOrder_Products_Details` (for permanent Work Order Product details)**
    *   `Id` (Primary Key, inferred)
    *   `MId` (Integer, Foreign Key to `SD_Cust_WorkOrder_Master`. This is the `WOId`.)
    *   `ItemCode` (String, from `txtItemCode`)
    *   `Description` (String, from `txtDescOfItem`)
    *   `Qty` (Decimal, from `txtQty`)
    *   `SessionId` (String, from `Session["username"]`, although not strictly used here, present in insert)
    *   `CompId` (Integer, from `Session["compid"]`)
    *   `FinYearId` (Integer, from `Session["finyear"]`)

*   **`SD_Cust_WorkOrder_Products_Temp` (for temporary Work Order Product details during session)**
    *   `Id` (Primary Key, inferred)
    *   `SessionId` (String, from `Session["username"]`)
    *   `CompId` (Integer, from `Session["compid"]`)
    *   `FinYearId` (Integer, from `Session["finyear"]`)
    *   `ItemCode` (String, from `txtItemCode`)
    *   `Description` (String, from `txtDescOfItem`)
    *   `Qty` (Decimal, from `txtQty`)

*   **Assumed Lookup Tables (for Foreign Keys):**
    *   `SD_Cust_master` (Customer)
    *   `tblSD_WO_Category` (Work Order Category)
    *   `tblSD_WO_SubCategory` (Work Order SubCategory)
    *   `Country` (Country, from `fun.dropdownCountry`)
    *   `State` (State, from `fun.dropdownState`)
    *   `City` (City, from `fun.dropdownCity`)
    *   `BusinessGroup` (Business Group, from `fun.dropdownBG`)
    *   `Buyer` (Buyer, from `fun.dropdownBuyer`)
    *   `Company` (Company, from `Session["compid"]`)
    *   `FinancialYear` (Financial Year, from `Session["finyear"]`)

---

### Step 2: Identify Backend Functionality

**Task:** We've identified the core operations and logic within your ASP.NET code-behind.

**Instructions:**

*   **Read (R):**
    *   On `Page_Load`, the `SD_Cust_WorkOrder_Master` table is queried to load all existing work order details (customer name, WO number, PO number, dates, project details, shipping info, instructions).
    *   `GridView1` is populated from `SD_Cust_WorkOrder_Products_Temp` (temporary products for the current user's session).
    *   `GridView2` is populated from `SD_Cust_WorkOrder_Products_Details` (permanent products associated with the work order).
    *   Dropdowns for Country, State, City, Business Group, Buyer are filled from respective lookup tables.
*   **Create (C):**
    *   When the "Submit" button (`btnProductSubmit_Click`) in the "Products" tab is clicked, new product items are inserted into `SD_Cust_WorkOrder_Products_Temp`. This is temporary storage until the main "Update" button is clicked.
*   **Update (U):**
    *   The primary "Update" button (`btnUpdate_Click`) handles the main save:
        *   It updates the `SD_Cust_WorkOrder_Master` record with all the edited fields.
        *   It then takes all products from `SD_Cust_WorkOrder_Products_Temp` and inserts them into `SD_Cust_WorkOrder_Products_Details`, associating them with the current Work Order.
        *   After successful transfer, it deletes the records from `SD_Cust_WorkOrder_Products_Temp`.
    *   In-place editing for `GridView1` (temporary products) and `GridView2` (permanent products) triggers updates to `SD_Cust_WorkOrder_Products_Temp` and `SD_Cust_WorkOrder_Products_Details` respectively.
*   **Delete (D):**
    *   In-place deletion for `GridView1` and `GridView2` triggers deletion from `SD_Cust_WorkOrder_Products_Temp` and `SD_Cust_WorkOrder_Products_Details` respectively.
*   **Validation:**
    *   `RequiredFieldValidator` ensures fields are not empty.
    *   `RegularExpressionValidator` handles date formats and quantity numerical formats.
    *   C# code includes `fun.NumberValidationQty`, `fun.EmailValidation`, `fun.DateValidation`.
*   **Navigation:**
    *   "Next" buttons (`btnTaskNext`, `btnShippingNext`, `btnProductNext`) simply change the active tab.
    *   "Cancel" buttons redirect to `WorkOrder_Edit.aspx`.

---

### Step 3: Infer UI Components

**Task:** We've analyzed the ASP.NET controls and their roles, mapping them to modern Django UI components and interaction patterns.

**Instructions:**

*   **Overall Structure:** The `TabContainer` will be replaced by a single Django template that uses **Alpine.js** to manage tab visibility client-side. The content of each tab will be handled within the same main Django form or partials.
*   **Data Display (Lists):**
    *   `GridView1` and `GridView2` will be replaced by standard HTML `<table>` elements. These will be dynamically enhanced using **DataTables.js** for client-side searching, sorting, and pagination.
    *   List content (rows) for temporary and permanent products will be rendered by dedicated partial templates loaded via **HTMX**.
*   **Input Fields:**
    *   `asp:Label` -> Standard Django template variable display.
    *   `asp:TextBox` -> `forms.TextInput` or `forms.Textarea` widgets. Date inputs will use `type="date"` for native browser pickers.
    *   `asp:DropDownList` -> `forms.Select` widgets.
    *   `asp:CheckBox` -> `forms.CheckboxInput` widgets.
*   **Buttons:**
    *   `asp:Button` and `asp:LinkButton` -> Standard HTML `<button>` elements.
    *   "Next" buttons for tab navigation will be handled by Alpine.js.
    *   "Submit", "Update", "Edit", "Delete" actions will be managed by **HTMX** requests (`hx-post`, `hx-get`, `hx-delete`) to dedicated Django view endpoints.
    *   Edit/Delete actions will typically load forms or confirmation messages into a **modal dialog** managed by Alpine.js.
*   **Validation:**
    *   All validation logic will be handled by Django forms, providing server-side validation feedback.
    *   Client-side validation can be augmented with basic HTML5 attributes or Alpine.js if complex real-time feedback is needed, but server-side validation is always primary.
*   **Date Pickers:** The `CalendarExtender` will be replaced by native HTML5 `type="date"` inputs for simplicity, or a lightweight JavaScript date picker library like `flatpickr.js` if more advanced features are required. We'll default to HTML5 `type="date"` for minimalism.

---

### Step 4: Generate Django Code

We will create a new Django application, say `workorders`, to house this functionality.

#### 4.1 Models (`workorders/models.py`)

We'll define the main models, assuming related lookup tables (Customer, Category, Country, etc.) are already present in the database as `managed = False` models or will be created as part of a broader migration.

```python
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import RegexValidator

# Assuming these lookup models already exist in your database or will be created
# and are marked as managed=False if they point to existing legacy tables.
# For simplicity, we define minimal versions here.
class Customer(models.Model):
    id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
    def __str__(self):
        return self.customer_name

class WOCategory(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    category_name = models.CharField(db_column='CName', max_length=255)
    has_sub_cat = models.BooleanField(db_column='HasSubCat', default=False)
    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
    def __str__(self):
        return f"{self.symbol} - {self.category_name}"

class WOSubCategory(models.Model):
    id = models.IntegerField(db_column='SCId', primary_key=True) # Assuming a SubCategory ID
    category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', related_name='subcategories')
    sub_category_name = models.CharField(db_column='SCName', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblSD_WO_SubCategory'
    def __str__(self):
        return self.sub_category_name

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CName', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblCountry' # Inferred table name
    def __str__(self):
        return self.country_name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId', related_name='states')
    state_name = models.CharField(db_column='SName', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblState' # Inferred table name
    def __str__(self):
        return self.state_name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId', related_name='cities')
    city_name = models.CharField(db_column='CityName', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblCity' # Inferred table name
    def __str__(self):
        return self.city_name

class BusinessGroup(models.Model):
    id = models.CharField(db_column='BGId', primary_key=True, max_length=50) # Inferred type
    group_name = models.CharField(db_column='BGName', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblBusinessGroup' # Inferred table name
    def __str__(self):
        return self.group_name

class Buyer(models.Model):
    id = models.CharField(db_column='BuyerId', primary_key=True, max_length=50) # Inferred type
    buyer_name = models.CharField(db_column='BuyerName', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblBuyer' # Inferred table name
    def __str__(self):
        return self.buyer_name

# Main Work Order Master Model
class WorkOrder(models.Model):
    # Core fields
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming identity column for primary key
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', verbose_name="Customer")
    enquiry_id = models.IntegerField(db_column='EnqId', verbose_name="Enquiry No.")
    po_no = models.CharField(db_column='PONo', max_length=255, verbose_name="PO No.")
    wo_no = models.CharField(db_column='WONo', max_length=255, verbose_name="Work Order No.")
    category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', verbose_name="Category")

    # Dates
    work_order_date = models.DateField(db_column='TaskWorkOrderDate', verbose_name="Date of WO")
    project_title = models.CharField(db_column='TaskProjectTitle', max_length=500, verbose_name="Project Title")
    project_leader = models.CharField(db_column='TaskProjectLeader', max_length=255, verbose_name="Project Leader")
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='TaskBusinessGroup', verbose_name="Business Group")
    buyer = models.ForeignKey(Buyer, on_delete=models.DO_NOTHING, db_column='Buyer', verbose_name="Buyer")

    # Task Target Dates (From-To pairs)
    target_dap_fdate = models.DateField(db_column='TaskTargetDAP_FDate', verbose_name="Target DAP From Date")
    target_dap_tdate = models.DateField(db_column='TaskTargetDAP_TDate', verbose_name="Target DAP To Date")
    design_finalization_fdate = models.DateField(db_column='TaskDesignFinalization_FDate', verbose_name="Design Finalization From Date")
    design_finalization_tdate = models.DateField(db_column='TaskDesignFinalization_TDate', verbose_name="Design Finalization To Date")
    target_manufg_fdate = models.DateField(db_column='TaskTargetManufg_FDate', verbose_name="Target Manufacturing From Date")
    target_manufg_tdate = models.DateField(db_column='TaskTargetManufg_TDate', verbose_name="Target Manufacturing To Date")
    target_try_out_fdate = models.DateField(db_column='TaskTargetTryOut_FDate', verbose_name="Target Try-out From Date")
    target_try_out_tdate = models.DateField(db_column='TaskTargetTryOut_TDate', verbose_name="Target Try-out To Date")
    target_despatch_fdate = models.DateField(db_column='TaskTargetDespach_FDate', verbose_name="Target Despatch From Date")
    target_despatch_tdate = models.DateField(db_column='TaskTargetDespach_TDate', verbose_name="Target Despatch To Date")
    target_assembly_fdate = models.DateField(db_column='TaskTargetAssembly_FDate', verbose_name="Target Assembly From Date")
    target_assembly_tdate = models.DateField(db_column='TaskTargetAssembly_TDate', verbose_name="Target Assembly To Date")
    target_installation_fdate = models.DateField(db_column='TaskTargetInstalation_FDate', verbose_name="Target Installation From Date")
    target_installation_tdate = models.DateField(db_column='TaskTargetInstalation_TDate', verbose_name="Target Installation To Date")
    cust_inspection_fdate = models.DateField(db_column='TaskCustInspection_FDate', verbose_name="Customer Inspection From Date")
    cust_inspection_tdate = models.DateField(db_column='TaskCustInspection_TDate', verbose_name="Customer Inspection To Date")

    # Material Procurement Dates
    manuf_material_date = models.DateField(db_column='ManufMaterialDate', verbose_name="Manufacturing Material Date")
    boughtout_material_date = models.DateField(db_column='BoughtoutMaterialDate', verbose_name="Boughtout Material Date")

    # Shipping Details
    shipping_address = models.TextField(db_column='ShippingAdd', verbose_name="Shipping Address")
    shipping_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='ShippingCountry', verbose_name="Shipping Country")
    shipping_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='ShippingState', verbose_name="Shipping State")
    shipping_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='ShippingCity', verbose_name="Shipping City")
    shipping_contact_person1 = models.CharField(db_column='ShippingContactPerson1', max_length=255, verbose_name="Contact Person 1")
    shipping_contact_no1 = models.CharField(db_column='ShippingContactNo1', max_length=50, verbose_name="Contact No 1")
    shipping_email1 = models.EmailField(db_column='ShippingEmail1', max_length=255, verbose_name="Email 1")
    shipping_contact_person2 = models.CharField(db_column='ShippingContactPerson2', max_length=255, blank=True, null=True, verbose_name="Contact Person 2")
    shipping_contact_no2 = models.CharField(db_column='ShippingContactNo2', max_length=50, blank=True, null=True, verbose_name="Contact No 2")
    shipping_email2 = models.EmailField(db_column='ShippingEmail2', max_length=255, blank=True, null=True, verbose_name="Email 2")
    shipping_fax_no = models.CharField(db_column='ShippingFaxNo', max_length=50, blank=True, null=True, verbose_name="Fax No")
    shipping_ecc_no = models.CharField(db_column='ShippingEccNo', max_length=50, blank=True, null=True, verbose_name="ECC No")
    shipping_tin_cst_no = models.CharField(db_column='ShippingTinCstNo', max_length=50, blank=True, null=True, verbose_name="TIN/CST No.")
    shipping_tin_vat_no = models.CharField(db_column='ShippingTinVatNo', max_length=50, blank=True, null=True, verbose_name="TIN/VAT No.")

    # Instructions
    instruction_primer_painting = models.BooleanField(db_column='InstractionPrimerPainting', default=False, verbose_name="Primer Painting to be done")
    instruction_painting = models.BooleanField(db_column='InstractionPainting', default=False, verbose_name="Painting to be done")
    instruction_self_cert_rept = models.BooleanField(db_column='InstractionSelfCertRept', default=False, verbose_name="Self Certification Report to be submitted")
    instruction_other = models.TextField(db_column='InstractionOther', blank=True, null=True, verbose_name="Other Instructions")
    instruction_export_case_mark = models.CharField(db_column='InstractionExportCaseMark', max_length=255, blank=True, null=True, verbose_name="Export Case Mark")
    instruction_attach_annexure = models.CharField(db_column='InstractionAttachAnnexure', max_length=255, blank=True, null=True, verbose_name="Attach Annexure") # Inferred

    # System fields
    sys_date = models.DateField(db_column='SysDate', auto_now=True, verbose_name="System Date")
    sys_time = models.CharField(db_column='SysTime', max_length=10, blank=True, null=True, verbose_name="System Time") # Store as string or convert to TimeField
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True, verbose_name="Session ID")
    # Assuming user_id field for Django's user
    user = models.ForeignKey(get_user_model(), on_delete=models.DO_NOTHING, db_column='UserId', null=True, blank=True, verbose_name="User")
    comp_id = models.IntegerField(db_column='CompId', verbose_name="Company ID") # Assuming CompId and FinYearId come from session/context
    fin_year_id = models.IntegerField(db_column='FinYearId', verbose_name="Financial Year ID")

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"WO: {self.wo_no} (PO: {self.po_no})"

    def move_temp_products_to_details(self, session_key, company_id, financial_year_id):
        """
        Business logic: Moves all temporary products for the current session
        and company to the permanent product details for this work order.
        """
        temp_products = WorkOrderProductTemp.objects.filter(
            session_key=session_key,
            comp_id=company_id,
            fin_year_id=financial_year_id
        )
        products_moved_count = 0
        for temp_product in temp_products:
            WorkOrderProductDetail.objects.create(
                work_order=self, # Link to this WorkOrder instance
                item_code=temp_product.item_code,
                description=temp_product.description,
                qty=temp_product.qty,
                comp_id=temp_product.comp_id,
                fin_year_id=temp_product.fin_year_id,
                session_key=temp_product.session_key # Keep session_key in details for trace if needed
            )
            products_moved_count += 1
            temp_product.delete() # Delete from temporary table

        return products_moved_count


class WorkOrderProductDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    work_order = models.ForeignKey(WorkOrder, on_delete=models.CASCADE, db_column='MId', related_name='product_details')
    item_code = models.CharField(db_column='ItemCode', max_length=255, verbose_name="Item Code")
    description = models.TextField(db_column='Description', verbose_name="Description of Item")
    qty = models.DecimalField(db_column='Qty', max_digits=15, decimal_places=3, verbose_name="Quantity")
    session_key = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Kept for consistency
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Products_Details'
        verbose_name = 'Work Order Product Detail'
        verbose_name_plural = 'Work Order Product Details'

    def __str__(self):
        return f"{self.item_code} - {self.description}"


class WorkOrderProductTemp(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    session_key = models.CharField(db_column='SessionId', max_length=255, verbose_name="Session ID")
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    item_code = models.CharField(db_column='ItemCode', max_length=255, verbose_name="Item Code")
    description = models.TextField(db_column='Description', verbose_name="Description of Item")
    qty = models.DecimalField(db_column='Qty', max_digits=15, decimal_places=3, verbose_name="Quantity")

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Products_Temp'
        verbose_name = 'Work Order Temporary Product'
        verbose_name_plural = 'Work Order Temporary Products'

    def __str__(self):
        return f"Temp: {self.item_code} - {self.qty}"

    def update_item(self, item_code, description, qty):
        """Business logic to update a temporary product item."""
        self.item_code = item_code
        self.description = description
        self.qty = qty
        self.save()

    def delete_item(self):
        """Business logic to delete a temporary product item."""
        self.delete()

```

#### 4.2 Forms (`workorders/forms.py`)

We'll define a main form for the `WorkOrder` master data, and a separate form for adding/editing product items.

```python
from django import forms
from .models import WorkOrder, WorkOrderProductTemp, Country, State, City, BusinessGroup, Buyer, WOCategory, WOSubCategory
from django.core.validators import RegexValidator

# Custom DateInput widget for consistency, assumes flatpickr or similar is loaded globally
class DateInput(forms.DateInput):
    input_type = 'date'
    def __init__(self, attrs=None):
        default_attrs = {'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
        if attrs:
            default_attrs.update(attrs)
        super().__init__(attrs=default_attrs)

class CustomSelect(forms.Select):
    def __init__(self, attrs=None):
        default_attrs = {'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
        if attrs:
            default_attrs.update(attrs)
        super().__init__(attrs=default_attrs)

class CustomTextInput(forms.TextInput):
    def __init__(self, attrs=None):
        default_attrs = {'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
        if attrs:
            default_attrs.update(attrs)
        super().__init__(attrs=default_attrs)

class CustomTextarea(forms.Textarea):
    def __init__(self, attrs=None):
        default_attrs = {'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
        if attrs:
            default_attrs.update(attrs)
        super().__init__(attrs=default_attrs)

class CustomCheckboxInput(forms.CheckboxInput):
    def __init__(self, attrs=None):
        default_attrs = {'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}
        if attrs:
            default_attrs.update(attrs)
        super().__init__(attrs=default_attrs)


class WorkOrderForm(forms.ModelForm):
    # Customer name, WO no, PO No, Category, Sub Category are display-only labels in ASP.NET
    # They should be managed via initial data or read-only fields if needed,
    # or passed as context to the template.

    shipping_email1 = forms.EmailField(
        label="Email",
        widget=CustomTextInput(),
        validators=[RegexValidator(r'^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$', message="Enter a valid email address.")],
        required=True
    )
    shipping_email2 = forms.EmailField(
        label="Email",
        widget=CustomTextInput(),
        validators=[RegexValidator(r'^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$', message="Enter a valid email address.")],
        required=False # Made optional based on ASP.NET's ReqProjTitle30 potentially being conditional or not always required
    )

    class Meta:
        model = WorkOrder
        fields = [
            'work_order_date', 'project_title', 'project_leader', 'business_group', 'buyer',
            'target_dap_fdate', 'target_dap_tdate', 'design_finalization_fdate', 'design_finalization_tdate',
            'target_manufg_fdate', 'target_manufg_tdate', 'target_try_out_fdate', 'target_try_out_tdate',
            'target_despatch_fdate', 'target_despatch_tdate', 'target_assembly_fdate', 'target_assembly_tdate',
            'target_installation_fdate', 'target_installation_tdate', 'cust_inspection_fdate', 'cust_inspection_tdate',
            'manuf_material_date', 'boughtout_material_date',
            'shipping_address', 'shipping_country', 'shipping_state', 'shipping_city',
            'shipping_contact_person1', 'shipping_contact_no1', 'shipping_email1',
            'shipping_contact_person2', 'shipping_contact_no2', 'shipping_email2',
            'shipping_fax_no', 'shipping_ecc_no', 'shipping_tin_cst_no', 'shipping_tin_vat_no',
            'instruction_primer_painting', 'instruction_painting', 'instruction_self_cert_rept',
            'instruction_other', 'instruction_export_case_mark', 'instruction_attach_annexure'
        ]
        widgets = {
            'work_order_date': DateInput(),
            'project_title': CustomTextInput(),
            'project_leader': CustomTextInput(),
            'business_group': CustomSelect(choices=[]), # Choices will be set in __init__
            'buyer': CustomSelect(choices=[]), # Choices will be set in __init__

            'target_dap_fdate': DateInput(), 'target_dap_tdate': DateInput(),
            'design_finalization_fdate': DateInput(), 'design_finalization_tdate': DateInput(),
            'target_manufg_fdate': DateInput(), 'target_manufg_tdate': DateInput(),
            'target_try_out_fdate': DateInput(), 'target_try_out_tdate': DateInput(),
            'target_despatch_fdate': DateInput(), 'target_despatch_tdate': DateInput(),
            'target_assembly_fdate': DateInput(), 'target_assembly_tdate': DateInput(),
            'target_installation_fdate': DateInput(), 'target_installation_tdate': DateInput(),
            'cust_inspection_fdate': DateInput(), 'cust_inspection_tdate': DateInput(),

            'manuf_material_date': DateInput(),
            'boughtout_material_date': DateInput(),

            'shipping_address': CustomTextarea(attrs={'rows': 3}),
            'shipping_country': CustomSelect(choices=[]),
            'shipping_state': CustomSelect(choices=[]),
            'shipping_city': CustomSelect(choices=[]),
            'shipping_contact_person1': CustomTextInput(),
            'shipping_contact_no1': CustomTextInput(),
            'shipping_contact_person2': CustomTextInput(),
            'shipping_contact_no2': CustomTextInput(),
            'shipping_fax_no': CustomTextInput(),
            'shipping_ecc_no': CustomTextInput(),
            'shipping_tin_cst_no': CustomTextInput(),
            'shipping_tin_vat_no': CustomTextInput(),

            'instruction_primer_painting': CustomCheckboxInput(),
            'instruction_painting': CustomCheckboxInput(),
            'instruction_self_cert_rept': CustomCheckboxInput(),
            'instruction_other': CustomTextInput(),
            'instruction_export_case_mark': CustomTextInput(),
            'instruction_attach_annexure': CustomTextInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dropdown choices dynamically
        self.fields['business_group'].choices = [('', 'Select')] + [(bg.id, bg.group_name) for bg in BusinessGroup.objects.all()]
        self.fields['buyer'].choices = [('', 'Select')] + [(b.id, b.buyer_name) for b in Buyer.objects.all()]
        self.fields['shipping_country'].choices = [('', 'Select')] + [(c.id, c.country_name) for c in Country.objects.all()]

        # Populate state and city based on initial data or selected country/state
        # This dynamic loading will be handled by HTMX on the frontend
        if self.instance and self.instance.shipping_country:
            self.fields['shipping_state'].choices = [('', 'Select')] + [(s.id, s.state_name) for s in State.objects.filter(country=self.instance.shipping_country)]
        else:
            self.fields['shipping_state'].choices = [('', 'Select')]
        
        if self.instance and self.instance.shipping_state:
            self.fields['shipping_city'].choices = [('', 'Select')] + [(c.id, c.city_name) for c in City.objects.filter(state=self.instance.shipping_state)]
        else:
            self.fields['shipping_city'].choices = [('', 'Select')]

    def clean(self):
        cleaned_data = super().clean()
        # Add any cross-field validation here if dates must be in specific ranges (From <= To)
        # Example:
        # dap_fdate = cleaned_data.get('target_dap_fdate')
        # dap_tdate = cleaned_data.get('target_dap_tdate')
        # if dap_fdate and dap_tdate and dap_fdate > dap_tdate:
        #     self.add_error('target_dap_tdate', 'To Date cannot be earlier than From Date.')
        return cleaned_data

class WorkOrderProductForm(forms.ModelForm):
    qty = forms.DecimalField(
        max_digits=15, 
        decimal_places=3, 
        widget=CustomTextInput(),
        validators=[RegexValidator(r'^\d{1,15}(\.\d{0,3})?$', message="Enter a valid quantity (max 15 digits, 3 decimal places).")],
        label="Quantity"
    )

    class Meta:
        model = WorkOrderProductTemp # Can be used for both Temp and Detail models
        fields = ['item_code', 'description', 'qty']
        widgets = {
            'item_code': CustomTextInput(),
            'description': CustomTextarea(attrs={'rows': 2}),
        }

```

#### 4.3 Views (`workorders/views.py`)

The main "Edit" page will be an `UpdateView` that handles the overall Work Order. The product lists will be managed via separate HTMX endpoints, and the main `UpdateView`'s `form_valid` will coordinate the final product transfer.

```python
from django.views.generic import UpdateView, View, ListView, CreateView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db import transaction

from .models import WorkOrder, WorkOrderProductDetail, WorkOrderProductTemp, Country, State, City, WOCategory, WOSubCategory
from .forms import WorkOrderForm, WorkOrderProductForm

class WorkOrderEditDetailsView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'workorders/workorder_edit_details.html'
    context_object_name = 'work_order' # This will be the instance for the main form
    
    # success_url is handled by HX-Trigger in the template, or a full redirect on non-HX
    # For now, we'll set it to a list view (WorkOrderList if it existed)
    success_url = reverse_lazy('workorder_list_view') # Assuming a work order list view exists

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Pass query string parameters to context for display
        customer_id = self.request.GET.get('CustomerId')
        po_no_param = self.request.GET.get('PONo')
        enq_id_param = self.request.GET.get('EnqId')

        # Load customer name and category/subcategory for display labels
        try:
            customer = WorkOrder.customer.field.related_model.objects.get(id=customer_id)
            context['customer_name'] = customer.customer_name
        except WorkOrder.customer.field.related_model.DoesNotExist:
            context['customer_name'] = "N/A"

        # Load category/subcategory for display labels
        if self.object and self.object.category:
            context['category_name'] = self.object.category.category_name
            if self.object.category.has_sub_cat:
                # This logic is simplified; ASP.NET had more complex subcategory loading
                # that picked ANY subcategory for that category. For exact match, 
                # you'd need a specific subcategory ID in WorkOrder model.
                # Assuming here it loads first available or N/A.
                sub_cat = WOSubCategory.objects.filter(category=self.object.category).first()
                context['sub_category_name'] = sub_cat.sub_category_name if sub_cat else "Not Applicable"
            else:
                context['sub_category_name'] = "Not Applicable"
        else:
            context['category_name'] = "N/A"
            context['sub_category_name'] = "N/A"

        context['po_no_display'] = po_no_param or self.object.po_no
        context['enq_id_display'] = enq_id_param or self.object.enquiry_id
        context['wo_no_display'] = self.object.wo_no

        # Pass empty product form for adding new temporary items
        context['product_form'] = WorkOrderProductForm()

        # Context for current session/company/financial year, assuming they are available
        # from request or user profile.
        context['current_session_key'] = self.request.session.session_key
        # Example: Assuming user has company/financial year in their profile or settings
        context['current_company_id'] = getattr(self.request.user, 'comp_id', 1) 
        context['current_fin_year_id'] = getattr(self.request.user, 'fin_year_id', 1)

        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        
        # Business logic: Transfer temporary products to permanent details
        session_key = self.request.session.session_key
        company_id = getattr(self.request.user, 'comp_id', 1) 
        financial_year_id = getattr(self.request.user, 'fin_year_id', 1)

        # The ASP.NET code attempts to move temp products to details on update.
        # This implies a final save of the WorkOrder master data AND associated products.
        try:
            with transaction.atomic():
                # Save the main work order (already done by super().form_valid(form))
                # Now move temp products
                products_moved = self.object.move_temp_products_to_details(
                    session_key=session_key, 
                    company_id=company_id, 
                    financial_year_id=financial_year_id
                )
            messages.success(self.request, f"Work Order updated successfully. {products_moved} new products added.")
        except Exception as e:
            messages.error(self.request, f"Error saving Work Order and products: {e}")
            # If an error occurs, perhaps keep the user on the page and show error
            return render(self.request, self.template_name, self.get_context_data(form=form))

        # Handle HTMX vs. regular request
        if self.request.headers.get('HX-Request'):
            # HTMX request: Trigger a refresh of the full page or relevant sections
            # Since this is the main update, we might want to refresh the whole page or redirect.
            # Using status 204 with HX-Redirect for a full page refresh if that's desired,
            # or HX-Trigger for specific elements (like the main table if we came from a list).
            return HttpResponse(
                status=204, 
                headers={
                    'HX-Trigger': 'refreshWorkOrderList', # Trigger a refresh on a potential parent list view
                    'HX-Redirect': reverse_lazy('workorder_list_view') # Redirect to work order list
                }
            )
        return response # Normal redirect


# HTMX endpoints for Product Temporary List (GridView1 equivalent)
class WorkOrderProductTempTablePartialView(ListView):
    model = WorkOrderProductTemp
    template_name = 'workorders/_workorder_product_temp_table.html'
    context_object_name = 'temp_products'

    def get_queryset(self):
        session_key = self.request.session.session_key
        company_id = self.request.GET.get('comp_id', 1) # Assuming comp_id passed as query param or from user
        financial_year_id = self.request.GET.get('fin_year_id', 1) # Assuming fin_year_id

        return WorkOrderProductTemp.objects.filter(
            session_key=session_key, 
            comp_id=company_id, 
            fin_year_id=financial_year_id
        ).order_by('-id')

class WorkOrderProductTempCreateView(CreateView):
    model = WorkOrderProductTemp
    form_class = WorkOrderProductForm
    template_name = 'workorders/_product_form.html' # Use a generic product form template

    def form_valid(self, form):
        # Assign session_id, comp_id, fin_year_id from the request context
        form.instance.session_key = self.request.session.session_key
        form.instance.comp_id = getattr(self.request.user, 'comp_id', 1) # Default 1 if not on user
        form.instance.fin_year_id = getattr(self.request.user, 'fin_year_id', 1) # Default 1 if not on user
        
        response = super().form_valid(form)
        messages.success(self.request, 'Temporary product added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProductTempList' # Trigger refresh of temp products table
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class WorkOrderProductTempUpdateView(UpdateView):
    model = WorkOrderProductTemp
    form_class = WorkOrderProductForm
    template_name = 'workorders/_product_form.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Temporary product updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProductTempList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class WorkOrderProductTempDeleteView(DeleteView):
    model = WorkOrderProductTemp
    template_name = 'workorders/_confirm_delete.html'

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Temporary product deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProductTempList'
                }
            )
        return response

# HTMX endpoints for Product Detail List (GridView2 equivalent)
class WorkOrderProductDetailTablePartialView(ListView):
    model = WorkOrderProductDetail
    template_name = 'workorders/_workorder_product_detail_table.html'
    context_object_name = 'detail_products'

    def get_queryset(self):
        work_order_id = self.kwargs['pk'] # Get Work Order ID from URL
        return WorkOrderProductDetail.objects.filter(work_order_id=work_order_id).order_by('-id')

class WorkOrderProductDetailUpdateView(UpdateView):
    model = WorkOrderProductDetail
    form_class = WorkOrderProductForm
    template_name = 'workorders/_product_form.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Product detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProductDetailList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class WorkOrderProductDetailDeleteView(DeleteView):
    model = WorkOrderProductDetail
    template_name = 'workorders/_confirm_delete.html'

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Product detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProductDetailList'
                }
            )
        return response

# HTMX endpoint for dynamic dropdowns (e.g., State based on Country)
class GetStatesView(View):
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('country_id')
        states = State.objects.filter(country_id=country_id).order_by('state_name')
        options = '<option value="">Select</option>'
        for state in states:
            options += f'<option value="{state.id}">{state.state_name}</option>'
        return HttpResponse(options)

class GetCitiesView(View):
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('state_id')
        cities = City.objects.filter(state_id=state_id).order_by('city_name')
        options = '<option value="">Select</option>'
        for city in cities:
            options += f'<option value="{city.id}">{city.city_name}</option>'
        return HttpResponse(options)

# Assuming a simple list view for redirection after update
class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'workorders/workorder_list.html' # A placeholder list view
    context_object_name = 'work_orders'

```

#### 4.4 Templates (`workorders/templates/workorders/`)

**`workorder_edit_details.html` (Main Page Template)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 0, customerName: '{{ customer_name|escapejs }}', woNo: '{{ wo_no_display|escapejs }}', poNo: '{{ po_no_display|escapejs }}', enqId: '{{ enq_id_display|escapejs }}', categoryName: '{{ category_name|escapejs }}', subCategoryName: '{{ sub_category_name|escapejs }}' }">
    <div class="bg-blue-600 text-white p-3 rounded-t-md mb-4">
        <h1 class="text-xl font-bold">Work Order - Edit</h1>
    </div>

    <div class="bg-white shadow-md rounded-b-md p-6">
        <div class="flex border-b border-gray-200" role="tablist" aria-label="Work Order Sections">
            <button @click="activeTab = 0" :class="{ 'border-b-2 border-blue-500 text-blue-600': activeTab === 0 }" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 focus:outline-none" role="tab">Task Execution</button>
            <button @click="activeTab = 1" :class="{ 'border-b-2 border-blue-500 text-blue-600': activeTab === 1 }" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 focus:outline-none" role="tab">Shipping</button>
            <button @click="activeTab = 2" :class="{ 'border-b-2 border-blue-500 text-blue-600': activeTab === 2 }" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 focus:outline-none" role="tab">Products</button>
            <button @click="activeTab = 3" :class="{ 'border-b-2 border-blue-500 text-blue-600': activeTab === 3 }" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 focus:outline-none" role="tab">Instructions</button>
        </div>

        <form method="post" action="" class="space-y-6 mt-6" 
              hx-post="{% url 'workorder_edit_details' work_order.pk %}" 
              hx-target="body" 
              hx-swap="none" 
              hx-on="htmx:afterRequest: if(event.detail.successful) { alert('Work Order updated successfully!'); window.location.href = '{% url 'workorder_list_view' %}'; } else { alert('Error updating Work Order!'); }">
            {% csrf_token %}

            <!-- Tab 1: Task Execution -->
            <div x-show="activeTab === 0" role="tabpanel" class="p-4 bg-gray-50 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm font-medium text-gray-700">
                    <div>
                        <span class="font-bold">Customer Name:</span> <span x-text="customerName" class="font-bold"></span>
                    </div>
                    <div>
                        <span class="font-bold">Work Order No:</span> <span x-text="woNo" class="font-bold"></span>
                    </div>
                    <div>
                        <span class="font-bold">PO No.:</span> <span x-text="poNo" class="font-bold"></span>
                    </div>
                    <div>
                        <span class="font-bold">Enquiry No:</span> <span x-text="enqId" class="font-bold"></span>
                    </div>
                    <div>
                        <span class="font-bold">Category:</span> <span x-text="categoryName" class="font-bold"></span>
                    </div>
                    <div>
                        <span class="font-bold">Sub Category:</span> <span x-text="subCategoryName" class="font-bold"></span>
                    </div>
                </div>

                <div class="mt-6 space-y-4">
                    {% include 'workorders/_task_execution_form.html' %}
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" @click="activeTab = 1" class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                    <button type="button" onclick="window.location.href='{% url 'workorder_list_view' %}'" class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Cancel</button>
                </div>
            </div>

            <!-- Tab 2: Shipping -->
            <div x-show="activeTab === 1" role="tabpanel" class="p-4 bg-gray-50 rounded-lg" x-init="
                $watch('$store.shipping.selectedCountry', (value) => {
                    if (value) {
                        htmx.trigger($('#id_shipping_state'), 'loadStates');
                    } else {
                        $('#id_shipping_state').html('<option value=\'\'>Select</option>');
                        $('#id_shipping_city').html('<option value=\'\'>Select</option>');
                    }
                });
                $watch('$store.shipping.selectedState', (value) => {
                    if (value) {
                        htmx.trigger($('#id_shipping_city'), 'loadCities');
                    } else {
                        $('#id_shipping_city').html('<option value=\'\'>Select</option>');
                    }
                });
            ">
                <div class="mt-6 space-y-4">
                    {% include 'workorders/_shipping_form.html' %}
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" @click="activeTab = 2" class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                    <button type="button" onclick="window.location.href='{% url 'workorder_list_view' %}'" class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Cancel</button>
                </div>
            </div>

            <!-- Tab 3: Products -->
            <div x-show="activeTab === 2" role="tabpanel" class="p-4 bg-gray-50 rounded-lg">
                <div class="mt-6 space-y-4">
                    {% include 'workorders/_products_form_and_list.html' with product_form=product_form work_order=work_order current_session_key=current_session_key current_company_id=current_company_id current_fin_year_id=current_fin_year_id %}
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" @click="activeTab = 3" class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                    <button type="button" onclick="window.location.href='{% url 'workorder_list_view' %}'" class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Cancel</button>
                </div>
            </div>

            <!-- Tab 4: Instructions -->
            <div x-show="activeTab === 3" role="tabpanel" class="p-4 bg-gray-50 rounded-lg">
                <div class="mt-6 space-y-4">
                    {% include 'workorders/_instructions_form.html' %}
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="submit" class="redbox bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" 
                            hx-confirm="Are you sure you want to update the Work Order? This will finalize temporary products.">Update</button>
                    <button type="button" onclick="window.location.href='{% url 'workorder_list_view' %}'" class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Cancel</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Modal for product forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         x-show="$store.modal.isOpen"
         x-cloak
         _="on click if event.target.id == 'modal' $store.modal.close()">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full" 
             @click.outside="$store.modal.close()"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js store for modal management and dynamic dropdowns
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true; },
            close() { this.isOpen = false; $('#modalContent').empty(); } // Clear content on close
        });

        Alpine.store('shipping', {
            selectedCountry: '{{ work_order.shipping_country_id|default:"" }}',
            selectedState: '{{ work_order.shipping_state_id|default:"" }}'
        });
    });

    // Initialize DataTables after HTMX loads content
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.target.id === 'product-temp-table-container' || evt.target.id === 'product-detail-table-container') {
            $('.datatable').DataTable({
                "pageLength": 5,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before re-initializing
            });
        }
    });

    // Manually trigger HTMX loads for dynamic dropdowns on initial page load if values exist
    document.addEventListener('DOMContentLoaded', function() {
        const countrySelect = document.getElementById('id_shipping_country');
        const stateSelect = document.getElementById('id_shipping_state');
        const citySelect = document.getElementById('id_shipping_city');

        if (countrySelect && countrySelect.value) {
            htmx.trigger(countrySelect, 'change'); // Trigger HTMX to load states
        }
        // State and City will be loaded as a chain reaction by HTMX upon country/state change.
        // Or if form is pre-filled, values will be set directly and no need for HTMX initial load.
        // However, if the form is edited and country/state changes, HTMX needs to re-fetch.
    });

    // Listen for HTMX success to close modal
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful && event.detail.xhr.status === 204) {
            Alpine.store('modal').close();
        }
    });

</script>
{% endblock %}
```

**`_task_execution_form.html` (Partial for Tab 1)**

```html
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
        <label for="{{ form.work_order_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.work_order_date.label }}
        </label>
        {{ form.work_order_date }}
        {% if form.work_order_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_order_date.errors }}</p>{% endif %}
    </div>
    <div class="col-span-1 md:col-span-2">
        <label for="{{ form.project_title.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.project_title.label }}
        </label>
        {{ form.project_title }}
        {% if form.project_title.errors %}<p class="text-red-500 text-xs mt-1">{{ form.project_title.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.project_leader.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.project_leader.label }}
        </label>
        {{ form.project_leader }}
        {% if form.project_leader.errors %}<p class="text-red-500 text-xs mt-1">{{ form.project_leader.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.business_group.label }}
        </label>
        {{ form.business_group }}
        {% if form.business_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.business_group.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.buyer.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.buyer.label }}
        </label>
        {{ form.buyer }}
        {% if form.buyer.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer.errors }}</p>{% endif %}
    </div>
</div>

<h4 class="text-md font-semibold mt-6 mb-2">Target Dates</h4>
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    {% for field_name_f, field_name_t, label in [
        ('target_dap_fdate', 'target_dap_tdate', 'Target DAP'),
        ('design_finalization_fdate', 'design_finalization_tdate', 'Design Finalization'),
        ('target_manufg_fdate', 'target_manufg_tdate', 'Target Manufg.'),
        ('target_try_out_fdate', 'target_try_out_tdate', 'Target Try-out'),
        ('target_despatch_fdate', 'target_despatch_tdate', 'Target Despatch'),
        ('target_assembly_fdate', 'target_assembly_tdate', 'Target Assembly'),
        ('target_installation_fdate', 'target_installation_tdate', 'Target Installation'),
        ('cust_inspection_fdate', 'cust_inspection_tdate', 'Cust. Inspection')
    ] %}
    <div class="col-span-1 md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-2 items-center">
        <label class="block text-sm font-medium text-gray-700">{{ label }} Date:</label>
        <div class="flex items-center space-x-2">
            <label for="{{ form|get_field_by_name:field_name_f.id_for_label }}" class="text-sm">From</label>
            {{ form|get_field_by_name:field_name_f }}
        </div>
        <div class="flex items-center space-x-2">
            <label for="{{ form|get_field_by_name:field_name_t.id_for_label }}" class="text-sm">To</label>
            {{ form|get_field_by_name:field_name_t }}
        </div>
        {% if form|get_field_by_name:field_name_f.errors %}<p class="text-red-500 text-xs mt-1 col-span-3">{{ form|get_field_by_name:field_name_f.errors }}</p>{% endif %}
        {% if form|get_field_by_name:field_name_t.errors %}<p class="text-red-500 text-xs mt-1 col-span-3">{{ form|get_field_by_name:field_name_t.errors }}</p>{% endif %}
    </div>
    {% endfor %}
</div>

<h4 class="text-md font-semibold mt-6 mb-2">Material Procurement</h4>
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
        <label for="{{ form.manuf_material_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Manufacturing Material Date
        </label>
        {{ form.manuf_material_date }}
        {% if form.manuf_material_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.manuf_material_date.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.boughtout_material_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Boughtout Material Date
        </label>
        {{ form.boughtout_material_date }}
        {% if form.boughtout_material_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.boughtout_material_date.errors }}</p>{% endif %}
    </div>
</div>

{# Helper to get field by name in template #}
{% load custom_filters %} 
```

**`_shipping_form.html` (Partial for Tab 2)**

```html
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="col-span-1">
        <label for="{{ form.shipping_address.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_address.label }}
        </label>
        {{ form.shipping_address }}
        {% if form.shipping_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_address.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_country.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_country.label }}
        </label>
        {{ form.shipping_country|attr:"hx-get=/workorders/get-states/"|attr:"hx-trigger=change"|attr:"hx-target=#id_shipping_state"|attr:"hx-params=country_id=this.value"|attr:"_="|attr:"on change $store.shipping.selectedCountry = event.target.value"|attr:"data-current-value"|attr:work_order.shipping_country.id }}
        {% if form.shipping_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_country.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_state.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_state.label }}
        </label>
        {{ form.shipping_state|attr:"hx-get=/workorders/get-cities/"|attr:"hx-trigger=change"|attr:"hx-target=#id_shipping_city"|attr:"hx-params=state_id=this.value"|attr:"_="|attr:"on change $store.shipping.selectedState = event.target.value"|attr:"data-current-value"|attr:work_order.shipping_state.id }}
        {% if form.shipping_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_state.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_city.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_city.label }}
        </label>
        {{ form.shipping_city|attr:"data-current-value"|attr:work_order.shipping_city.id }}
        {% if form.shipping_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_city.errors }}</p>{% endif %}
    </div>
</div>

<h4 class="text-md font-semibold mt-6 mb-2">Contact Details</h4>
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
        <label for="{{ form.shipping_contact_person1.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_contact_person1.label }}
        </label>
        {{ form.shipping_contact_person1 }}
        {% if form.shipping_contact_person1.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_person1.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_contact_no1.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_contact_no1.label }}
        </label>
        {{ form.shipping_contact_no1 }}
        {% if form.shipping_contact_no1.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_no1.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_email1.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_email1.label }}
        </label>
        {{ form.shipping_email1 }}
        {% if form.shipping_email1.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_email1.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_contact_person2.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_contact_person2.label }}
        </label>
        {{ form.shipping_contact_person2 }}
        {% if form.shipping_contact_person2.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_person2.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_contact_no2.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_contact_no2.label }}
        </label>
        {{ form.shipping_contact_no2 }}
        {% if form.shipping_contact_no2.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_contact_no2.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_email2.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_email2.label }}
        </label>
        {{ form.shipping_email2 }}
        {% if form.shipping_email2.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_email2.errors }}</p>{% endif %}
    </div>
</div>

<h4 class="text-md font-semibold mt-6 mb-2">Other Details</h4>
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
        <label for="{{ form.shipping_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_fax_no.label }}
        </label>
        {{ form.shipping_fax_no }}
        {% if form.shipping_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_fax_no.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_ecc_no.label }}
        </label>
        {{ form.shipping_ecc_no }}
        {% if form.shipping_ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_ecc_no.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_tin_cst_no.label }}
        </label>
        {{ form.shipping_tin_cst_no }}
        {% if form.shipping_tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_tin_cst_no.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.shipping_tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.shipping_tin_vat_no.label }}
        </label>
        {{ form.shipping_tin_vat_no }}
        {% if form.shipping_tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.shipping_tin_vat_no.errors }}</p>{% endif %}
    </div>
</div>

{% load custom_filters %}
```

**`_products_form_and_list.html` (Partial for Tab 3)**

```html
<h3 class="text-lg font-medium text-gray-900 mb-4">Add Product (Temporary)</h3>
<form hx-post="{% url 'workorder_product_temp_add' %}" hx-swap="outerHTML" 
      hx-target="#product-add-form-container" hx-indicator="#product-add-spinner">
    {% csrf_token %}
    <div id="product-add-form-container" class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
        <div>
            <label for="{{ product_form.item_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ product_form.item_code.label }}
            </label>
            {{ product_form.item_code }}
            {% if product_form.item_code.errors %}<p class="text-red-500 text-xs mt-1">{{ product_form.item_code.errors }}</p>{% endif %}
        </div>
        <div class="md:col-span-2">
            <label for="{{ product_form.description.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ product_form.description.label }}
            </label>
            {{ product_form.description }}
            {% if product_form.description.errors %}<p class="text-red-500 text-xs mt-1">{{ product_form.description.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ product_form.qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ product_form.qty.label }}
            </label>
            {{ product_form.qty }}
            {% if product_form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ product_form.qty.errors }}</p>{% endif %}
        </div>
        <div class="flex items-center space-x-2">
            <button type="submit" class="redbox bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">Submit</button>
            <div id="product-add-spinner" class="htmx-indicator animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
        </div>
    </div>
</form>

<h3 class="text-lg font-medium text-gray-900 mt-8 mb-4">Temporary Products (Pending Save)</h3>
<div id="product-temp-table-container"
     hx-trigger="load, refreshProductTempList from:body"
     hx-get="{% url 'workorder_product_temp_table' %}?comp_id={{ current_company_id }}&fin_year_id={{ current_fin_year_id }}"
     hx-swap="innerHTML">
    <!-- Temporary products table will be loaded here via HTMX -->
    <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading temporary products...</p>
    </div>
</div>

<h3 class="text-lg font-medium text-gray-900 mt-8 mb-4">Existing Products</h3>
<div id="product-detail-table-container"
     hx-trigger="load, refreshProductDetailList from:body"
     hx-get="{% url 'workorder_product_detail_table' work_order.pk %}"
     hx-swap="innerHTML">
    <!-- Permanent products table will be loaded here via HTMX -->
    <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading existing products...</p>
    </div>
</div>
```

**`_instructions_form.html` (Partial for Tab 4)**

```html
<div class="grid grid-cols-1 gap-4">
    <div class="flex items-center">
        {{ form.instruction_primer_painting }}
        <label for="{{ form.instruction_primer_painting.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
            {{ form.instruction_primer_painting.label }}
        </label>
        {% if form.instruction_primer_painting.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_primer_painting.errors }}</p>{% endif %}
    </div>
    <div class="flex items-center">
        {{ form.instruction_painting }}
        <label for="{{ form.instruction_painting.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
            {{ form.instruction_painting.label }}
        </label>
        {% if form.instruction_painting.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_painting.errors }}</p>{% endif %}
    </div>
    <div class="flex items-center">
        {{ form.instruction_self_cert_rept }}
        <label for="{{ form.instruction_self_cert_rept.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
            {{ form.instruction_self_cert_rept.label }}
        </label>
        {% if form.instruction_self_cert_rept.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_self_cert_rept.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.instruction_other.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.instruction_other.label }}
        </label>
        {{ form.instruction_other }}
        {% if form.instruction_other.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_other.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.instruction_export_case_mark.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.instruction_export_case_mark.label }}
        </label>
        {{ form.instruction_export_case_mark }}
        {% if form.instruction_export_case_mark.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_export_case_mark.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.instruction_attach_annexure.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.instruction_attach_annexure.label }}
        </label>
        {{ form.instruction_attach_annexure }}
        {% if form.instruction_attach_annexure.errors %}<p class="text-red-500 text-xs mt-1">{{ form.instruction_attach_annexure.errors }}</p>{% endif %}
        <p class="text-xs text-gray-500 mt-1">*Packing Instructions : Export Seaworthy / Wooden / Corrugated 7 day before desp.</p>
    </div>
</div>
```

**`_workorder_product_temp_table.html` (Partial for Temporary Products DataTable)**

```html
<table id="productTempTable" class="min-w-full bg-white datatable">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in temp_products %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.qty }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'workorder_product_temp_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click $store.modal.open()">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'workorder_product_temp_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click $store.modal.open()">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No temporary products to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // DataTable initialization is handled by htmx:afterSwap listener in main template
    // This script block ensures it runs if loaded directly, but the main Alpine.js handles it.
});
</script>
```

**`_workorder_product_detail_table.html` (Partial for Permanent Products DataTable)**

```html
<table id="productDetailTable" class="min-w-full bg-white datatable">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in detail_products %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.qty }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'workorder_product_detail_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click $store.modal.open()">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'workorder_product_detail_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click $store.modal.open()">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No existing products to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // DataTable initialization is handled by htmx:afterSwap listener in main template
});
</script>
```

**`_product_form.html` (Partial for Product Add/Edit Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Product</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#product-modal-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click $store.modal.close()">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <div id="product-modal-spinner" class="htmx-indicator animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
        </div>
    </form>
</div>
```

**`_confirm_delete.html` (Partial for Delete Confirmation Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this item?</p>
    <form hx-delete="{{ request.path }}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click $store.modal.close()">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <div id="delete-spinner" class="htmx-indicator animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
        </div>
    </form>
</div>
```

**`custom_filters.py` (Inside `workorders/templatetags/` directory)**

```python
from django import template

register = template.Library()

@register.filter
def get_field_by_name(form, field_name):
    """
    Returns a form field by its name.
    Useful for iterating and displaying specific fields from the form context.
    """
    return form[field_name]

@register.filter(name='attr')
def add_attr(field, css_class):
    attrs = {}
    if field.field.widget.attrs:
        attrs = field.field.widget.attrs.copy()

    # Special handling for hx attributes
    if '=' in css_class:
        key, value = css_class.split('=', 1)
        attrs[key] = value
    else:
        # For general classes, append to existing class or create new
        existing_class = attrs.get('class', '')
        new_class = f"{existing_class} {css_class}".strip()
        attrs['class'] = new_class
    
    # Preserve the initial 'value' for select fields that might be cleared by HTMX later
    # This ensures the correct initial selection when the form is rendered.
    if isinstance(field.field.widget, forms.Select) and 'data-current-value' in attrs:
        initial_value = attrs.pop('data-current-value')
        attrs['x-init'] = f"setTimeout(() => $el.value = '{initial_value}', 0)" # Alpine.js to set value after HTMX swap

    return field.as_widget(attrs=attrs)

```
*You will need to create `workorders/templatetags/` directory and an empty `__init__.py` inside it.*

#### 4.5 URLs (`workorders/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderEditDetailsView, WorkOrderProductTempTablePartialView,
    WorkOrderProductTempCreateView, WorkOrderProductTempUpdateView, 
    WorkOrderProductTempDeleteView, WorkOrderProductDetailTablePartialView,
    WorkOrderProductDetailUpdateView, WorkOrderProductDetailDeleteView,
    GetStatesView, GetCitiesView, WorkOrderListView # WorkOrderListView is a placeholder
)

urlpatterns = [
    # Main Work Order Edit Page
    path('<int:pk>/edit/', WorkOrderEditDetailsView.as_view(), name='workorder_edit_details'),

    # HTMX endpoints for Temporary Products (GridView1)
    path('temp-products-table/', WorkOrderProductTempTablePartialView.as_view(), name='workorder_product_temp_table'),
    path('temp-products/add/', WorkOrderProductTempCreateView.as_view(), name='workorder_product_temp_add'),
    path('temp-products/<int:pk>/edit/', WorkOrderProductTempUpdateView.as_view(), name='workorder_product_temp_edit'),
    path('temp-products/<int:pk>/delete/', WorkOrderProductTempDeleteView.as_view(), name='workorder_product_temp_delete'),

    # HTMX endpoints for Permanent Products (GridView2)
    path('<int:pk>/products-detail-table/', WorkOrderProductDetailTablePartialView.as_view(), name='workorder_product_detail_table'),
    path('products-detail/<int:pk>/edit/', WorkOrderProductDetailUpdateView.as_view(), name='workorder_product_detail_edit'),
    path('products-detail/<int:pk>/delete/', WorkOrderProductDetailDeleteView.as_view(), name='workorder_product_detail_delete'),

    # HTMX endpoints for dynamic dropdowns
    path('get-states/', GetStatesView.as_view(), name='get_states'),
    path('get-cities/', GetCitiesView.as_view(), name='get_cities'),

    # Placeholder for the work order list view (redirection target)
    path('list/', WorkOrderListView.as_view(), name='workorder_list_view'), 
]

```
*Don't forget to include `path('workorders/', include('workorders.urls'))` in your project's main `urls.py`.*

#### 4.6 Tests (`workorders/tests.py`)

Comprehensive tests cover models, forms, and views, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db import transaction
from django.contrib.auth import get_user_model

from .models import (
    WorkOrder, WorkOrderProductDetail, WorkOrderProductTemp, 
    Customer, WOCategory, WOSubCategory, Country, State, City, BusinessGroup, Buyer
)
from .forms import WorkOrderForm, WorkOrderProductForm

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for FKs
        cls.customer = Customer.objects.create(id=1, customer_name='Test Customer Inc.')
        cls.wo_category = WOCategory.objects.create(id=1, symbol='WO', category_name='General Work Order', has_sub_cat=True)
        cls.wo_subcategory = WOSubCategory.objects.create(id=1, category=cls.wo_category, sub_category_name='Standard')
        cls.country = Country.objects.create(id=1, country_name='India')
        cls.state = State.objects.create(id=1, country=cls.country, state_name='Maharashtra')
        cls.city = City.objects.create(id=1, state=cls.state, city_name='Mumbai')
        cls.business_group = BusinessGroup.objects.create(id='BG001', group_name='Engineering')
        cls.buyer = Buyer.objects.create(id='B001', buyer_name='John Doe')
        cls.user = get_user_model().objects.create_user(username='testuser', password='password123')
        cls.user.comp_id = 1 # Assuming comp_id and fin_year_id are attributes of the User model or can be mocked
        cls.user.fin_year_id = 1
        cls.user.save()

        # Create a WorkOrder instance
        cls.work_order = WorkOrder.objects.create(
            customer=cls.customer,
            enquiry_id=12345,
            po_no='PO-XYZ-2023',
            wo_no='WO-ABC-2023',
            category=cls.wo_category,
            work_order_date='2023-01-15',
            project_title='Sample Project Title',
            project_leader='Alice Johnson',
            business_group=cls.business_group,
            buyer=cls.buyer,
            target_dap_fdate='2023-02-01', target_dap_tdate='2023-02-28',
            design_finalization_fdate='2023-03-01', design_finalization_tdate='2023-03-15',
            target_manufg_fdate='2023-04-01', target_manufg_tdate='2023-04-30',
            target_try_out_fdate='2023-05-01', target_try_out_tdate='2023-05-15',
            target_despatch_fdate='2023-06-01', target_despatch_tdate='2023-06-15',
            target_assembly_fdate='2023-07-01', target_assembly_tdate='2023-07-15',
            target_installation_fdate='2023-08-01', target_installation_tdate='2023-08-15',
            cust_inspection_fdate='2023-09-01', cust_inspection_tdate='2023-09-15',
            manuf_material_date='2023-01-20',
            boughtout_material_date='2023-01-25',
            shipping_address='123 Shipping Lane',
            shipping_country=cls.country,
            shipping_state=cls.state,
            shipping_city=cls.city,
            shipping_contact_person1='Bob Smith',
            shipping_contact_no1='1234567890',
            shipping_email1='<EMAIL>',
            instruction_primer_painting=True,
            instruction_painting=False,
            instruction_self_cert_rept=True,
            instruction_other='Special handling required.',
            instruction_export_case_mark='MARK-EX-123',
            user=cls.user,
            comp_id=1,
            fin_year_id=1,
        )

        # Create temporary product for testing move_temp_products_to_details
        WorkOrderProductTemp.objects.create(
            session_key='test_session_key_123',
            comp_id=1,
            fin_year_id=1,
            item_code='TEMP-ITEM-001',
            description='Temporary Product 1',
            qty=10.500
        )
        WorkOrderProductTemp.objects.create(
            session_key='test_session_key_123',
            comp_id=1,
            fin_year_id=1,
            item_code='TEMP-ITEM-002',
            description='Temporary Product 2',
            qty=20.750
        )
        WorkOrderProductDetail.objects.create(
            work_order=cls.work_order,
            item_code='EXISTING-ITEM-001',
            description='Existing Product 1',
            qty=5.000,
            comp_id=1,
            fin_year_id=1
        )

    def test_work_order_creation(self):
        wo = WorkOrder.objects.get(id=self.work_order.id)
        self.assertEqual(wo.project_title, 'Sample Project Title')
        self.assertTrue(wo.instruction_primer_painting)
        self.assertFalse(wo.instruction_painting)
        self.assertEqual(wo.shipping_email1, '<EMAIL>')
        self.assertEqual(wo.customer.customer_name, 'Test Customer Inc.')
        self.assertEqual(wo.category.category_name, 'General Work Order')

    def test_move_temp_products_to_details(self):
        # Ensure initial state is correct
        self.assertEqual(WorkOrderProductDetail.objects.count(), 1) # One existing product
        self.assertEqual(WorkOrderProductTemp.objects.count(), 2)

        # Call the business logic method
        products_moved = self.work_order.move_temp_products_to_details(
            session_key='test_session_key_123',
            company_id=1,
            financial_year_id=1
        )
        self.assertEqual(products_moved, 2)
        
        # Verify products are moved and temporary ones deleted
        self.assertEqual(WorkOrderProductDetail.objects.count(), 3) # 1 existing + 2 new
        self.assertEqual(WorkOrderProductTemp.objects.count(), 0)

        # Verify details of moved products
        moved_product_1 = WorkOrderProductDetail.objects.get(item_code='TEMP-ITEM-001')
        self.assertEqual(moved_product_1.work_order, self.work_order)
        self.assertEqual(moved_product_1.qty, 10.500)

class WorkOrderFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for FKs for form choices
        cls.customer = Customer.objects.create(id=1, customer_name='Test Customer Inc.')
        cls.wo_category = WOCategory.objects.create(id=1, symbol='WO', category_name='General Work Order')
        cls.country = Country.objects.create(id=1, country_name='India')
        cls.state = State.objects.create(id=1, country=cls.country, state_name='Maharashtra')
        cls.city = City.objects.create(id=1, state=cls.state, city_name='Mumbai')
        cls.business_group = BusinessGroup.objects.create(id='BG001', group_name='Engineering')
        cls.buyer = Buyer.objects.create(id='B001', buyer_name='John Doe')

    def test_work_order_form_valid(self):
        form_data = {
            'work_order_date': '2023-01-15', 'project_title': 'New Project', 'project_leader': 'Jane Doe',
            'business_group': 'BG001', 'buyer': 'B001',
            'target_dap_fdate': '2023-02-01', 'target_dap_tdate': '2023-02-28',
            'design_finalization_fdate': '2023-03-01', 'design_finalization_tdate': '2023-03-15',
            'target_manufg_fdate': '2023-04-01', 'target_manufg_tdate': '2023-04-30',
            'target_try_out_fdate': '2023-05-01', 'target_try_out_tdate': '2023-05-15',
            'target_despatch_fdate': '2023-06-01', 'target_despatch_tdate': '2023-06-15',
            'target_assembly_fdate': '2023-07-01', 'target_assembly_tdate': '2023-07-15',
            'target_installation_fdate': '2023-08-01', 'target_installation_tdate': '2023-08-15',
            'cust_inspection_fdate': '2023-09-01', 'cust_inspection_tdate': '2023-09-15',
            'manuf_material_date': '2023-01-20', 'boughtout_material_date': '2023-01-25',
            'shipping_address': '456 Shipping Road', 'shipping_country': 1, 'shipping_state': 1, 'shipping_city': 1,
            'shipping_contact_person1': 'Chris Green', 'shipping_contact_no1': '0987654321', 'shipping_email1': '<EMAIL>',
            'instruction_primer_painting': True, 'instruction_painting': False, 'instruction_self_cert_rept': True,
            'instruction_other': 'Handle with care.', 'instruction_export_case_mark': 'EXPORT-MK-001',
        }
        form = WorkOrderForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors.as_data())

    def test_work_order_form_invalid_email(self):
        form_data = {
            # ... (all other valid fields)
            'shipping_email1': 'invalid-email',
            'work_order_date': '2023-01-15', 'project_title': 'New Project', 'project_leader': 'Jane Doe',
            'business_group': 'BG001', 'buyer': 'B001',
            'target_dap_fdate': '2023-02-01', 'target_dap_tdate': '2023-02-28',
            'design_finalization_fdate': '2023-03-01', 'design_finalization_tdate': '2023-03-15',
            'target_manufg_fdate': '2023-04-01', 'target_manufg_tdate': '2023-04-30',
            'target_try_out_fdate': '2023-05-01', 'target_try_out_tdate': '2023-05-15',
            'target_despatch_fdate': '2023-06-01', 'target_despatch_tdate': '2023-06-15',
            'target_assembly_fdate': '2023-07-01', 'target_assembly_tdate': '2023-07-15',
            'target_installation_fdate': '2023-08-01', 'target_installation_tdate': '2023-08-15',
            'cust_inspection_fdate': '2023-09-01', 'cust_inspection_tdate': '2023-09-15',
            'manuf_material_date': '2023-01-20', 'boughtout_material_date': '2023-01-25',
            'shipping_address': '456 Shipping Road', 'shipping_country': 1, 'shipping_state': 1, 'shipping_city': 1,
            'shipping_contact_person1': 'Chris Green', 'shipping_contact_no1': '0987654321',
            'instruction_primer_painting': True, 'instruction_painting': False, 'instruction_self_cert_rept': True,
            'instruction_other': 'Handle with care.', 'instruction_export_case_mark': 'EXPORT-MK-001',
        }
        form = WorkOrderForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Enter a valid email address.', form.errors['shipping_email1'][0])

    def test_product_form_valid(self):
        form_data = {'item_code': 'PROD-101', 'description': 'Product Description', 'qty': 123.456}
        form = WorkOrderProductForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_product_form_invalid_qty(self):
        form_data = {'item_code': 'PROD-101', 'description': 'Product Description', 'qty': 'invalid'}
        form = WorkOrderProductForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Enter a valid quantity', form.errors['qty'][0])

class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup similar base data as in WorkOrderModelTest
        cls.customer = Customer.objects.create(id=1, customer_name='Test Customer Inc.')
        cls.wo_category = WOCategory.objects.create(id=1, symbol='WO', category_name='General Work Order', has_sub_cat=True)
        cls.wo_subcategory = WOSubCategory.objects.create(id=1, category=cls.wo_category, sub_category_name='Standard')
        cls.country = Country.objects.create(id=1, country_name='India')
        cls.state = State.objects.create(id=1, country=cls.country, state_name='Maharashtra')
        cls.city = City.objects.create(id=1, state=cls.state, city_name='Mumbai')
        cls.business_group = BusinessGroup.objects.create(id='BG001', group_name='Engineering')
        cls.buyer = Buyer.objects.create(id='B001', buyer_name='John Doe')
        cls.user = get_user_model().objects.create_user(username='testuser', password='password123')
        cls.user.comp_id = 1 # Assuming comp_id and fin_year_id for user
        cls.user.fin_year_id = 1
        cls.user.save()

        cls.work_order = WorkOrder.objects.create(
            customer=cls.customer, enquiry_id=123, po_no='PO-XYZ', wo_no='WO-ABC', category=cls.wo_category,
            work_order_date='2023-01-01', project_title='Test Project', project_leader='Test Leader',
            business_group=cls.business_group, buyer=cls.buyer,
            target_dap_fdate='2023-02-01', target_dap_tdate='2023-02-28',
            design_finalization_fdate='2023-03-01', design_finalization_tdate='2023-03-15',
            target_manufg_fdate='2023-04-01', target_manufg_tdate='2023-04-30',
            target_try_out_fdate='2023-05-01', target_try_out_tdate='2023-05-15',
            target_despatch_fdate='2023-06-01', target_despatch_tdate='2023-06-15',
            target_assembly_fdate='2023-07-01', target_assembly_tdate='2023-07-15',
            target_installation_fdate='2023-08-01', target_installation_tdate='2023-08-15',
            cust_inspection_fdate='2023-09-01', cust_inspection_tdate='2023-09-15',
            manuf_material_date='2023-01-20', boughtout_material_date='2023-01-25',
            shipping_address='123 Main St', shipping_country=cls.country, shipping_state=cls.state, shipping_city=cls.city,
            shipping_contact_person1='Contact 1', shipping_contact_no1='111', shipping_email1='<EMAIL>',
            instruction_primer_painting=True, instruction_painting=False, instruction_self_cert_rept=True,
            comp_id=1, fin_year_id=1, user=cls.user
        )
        # Create an existing product detail
        WorkOrderProductDetail.objects.create(
            work_order=cls.work_order,
            item_code='P001', description='Product 1', qty=10.0,
            comp_id=1, fin_year_id=1
        )

    def setUp(self):
        self.client = Client()
        self.client.force_login(self.user)
        # Ensure the session has a key for WorkOrderProductTemp
        session = self.client.session
        session.save()
        self.session_key = session.session_key
        # Clear temporary products before each test
        WorkOrderProductTemp.objects.all().delete()


    def test_edit_details_view_get(self):
        url = reverse('workorder_edit_details', args=[self.work_order.pk])
        response = self.client.get(url, {'CustomerId': self.customer.pk, 'PONo': self.work_order.po_no, 'EnqId': self.work_order.enquiry_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder_edit_details.html')
        self.assertContains(response, self.work_order.project_title)
        self.assertContains(response, self.customer.customer_name)
        self.assertContains(response, self.work_order.wo_no)
        self.assertContains(response, self.work_order.po_no)
        self.assertContains(response, self.work_order.enquiry_id)

    def test_edit_details_view_post_success(self):
        url = reverse('workorder_edit_details', args=[self.work_order.pk])
        
        # Create a temporary product to be moved
        WorkOrderProductTemp.objects.create(
            session_key=self.session_key,
            comp_id=1,
            fin_year_id=1,
            item_code='NEW-TEMP-ITEM',
            description='A newly added item',
            qty=7.777
        )

        updated_data = {
            'work_order_date': '2023-01-02', 'project_title': 'Updated Project', 'project_leader': 'Updated Leader',
            'business_group': self.business_group.id, 'buyer': self.buyer.id,
            'target_dap_fdate': '2023-02-01', 'target_dap_tdate': '2023-02-28',
            'design_finalization_fdate': '2023-03-01', 'design_finalization_tdate': '2023-03-15',
            'target_manufg_fdate': '2023-04-01', 'target_manufg_tdate': '2023-04-30',
            'target_try_out_fdate': '2023-05-01', 'target_try_out_tdate': '2023-05-15',
            'target_despatch_fdate': '2023-06-01', 'target_despatch_tdate': '2023-06-15',
            'target_assembly_fdate': '2023-07-01', 'target_assembly_tdate': '2023-07-15',
            'target_installation_fdate': '2023-08-01', 'target_installation_tdate': '2023-08-15',
            'cust_inspection_fdate': '2023-09-01', 'cust_inspection_tdate': '2023-09-15',
            'manuf_material_date': '2023-01-20', 'boughtout_material_date': '2023-01-25',
            'shipping_address': '456 New St', 'shipping_country': self.country.id, 'shipping_state': self.state.id, 'shipping_city': self.city.id,
            'shipping_contact_person1': 'New Contact', 'shipping_contact_no1': '222', 'shipping_email1': '<EMAIL>',
            'instruction_primer_painting': False, 'instruction_painting': True, 'instruction_self_cert_rept': False,
            'instruction_other': 'New instructions', 'instruction_export_case_mark': 'NEWMARK-001'
            # Note: customer_id, enquiry_id, po_no, wo_no, category are not posted via form
        }

        response = self.client.post(url, updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX successful update
        self.assertEqual(response['HX-Redirect'], reverse('workorder_list_view')) # Check redirect trigger

        # Verify WorkOrder was updated
        self.work_order.refresh_from_db()
        self.assertEqual(self.work_order.project_title, 'Updated Project')
        self.assertEqual(self.work_order.shipping_email1, '<EMAIL>')

        # Verify temporary product was moved to details
        self.assertEqual(WorkOrderProductTemp.objects.count(), 0)
        self.assertEqual(WorkOrderProductDetail.objects.filter(work_order=self.work_order).count(), 2) # 1 original + 1 new
        self.assertTrue(WorkOrderProductDetail.objects.filter(item_code='NEW-TEMP-ITEM', work_order=self.work_order).exists())
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order updated successfully. 1 new products added.')


    def test_product_temp_table_partial_view(self):
        WorkOrderProductTemp.objects.create(
            session_key=self.session_key, comp_id=1, fin_year_id=1,
            item_code='TEMPO-001', description='Temporary Product Item', qty=15.0
        )
        url = reverse('workorder_product_temp_table')
        response = self.client.get(url, {'comp_id': 1, 'fin_year_id': 1}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_workorder_product_temp_table.html')
        self.assertContains(response, 'TEMPO-001')

    def test_product_temp_create_view(self):
        url = reverse('workorder_product_temp_add')
        data = {
            'item_code': 'NEW-ITEM-ABC', 
            'description': 'Description for new item', 
            'qty': 50.0
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response)
        self.assertEqual(response['HX-Trigger'], 'refreshProductTempList')
        self.assertTrue(WorkOrderProductTemp.objects.filter(item_code='NEW-ITEM-ABC', session_key=self.session_key).exists())

    def test_product_temp_update_view(self):
        temp_prod = WorkOrderProductTemp.objects.create(
            session_key=self.session_key, comp_id=1, fin_year_id=1,
            item_code='OLD-ITEM', description='Old Desc', qty=10.0
        )
        url = reverse('workorder_product_temp_edit', args=[temp_prod.pk])
        data = {
            'item_code': 'UPDATED-ITEM', 
            'description': 'Updated Desc', 
            'qty': 25.0
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response)
        self.assertEqual(response['HX-Trigger'], 'refreshProductTempList')
        temp_prod.refresh_from_db()
        self.assertEqual(temp_prod.item_code, 'UPDATED-ITEM')
        self.assertEqual(temp_prod.qty, 25.0)

    def test_product_temp_delete_view(self):
        temp_prod = WorkOrderProductTemp.objects.create(
            session_key=self.session_key, comp_id=1, fin_year_id=1,
            item_code='TO-DELETE', description='Will be deleted', qty=1.0
        )
        url = reverse('workorder_product_temp_delete', args=[temp_prod.pk])
        response = self.client.delete(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response)
        self.assertEqual(response['HX-Trigger'], 'refreshProductTempList')
        self.assertFalse(WorkOrderProductTemp.objects.filter(pk=temp_prod.pk).exists())

    def test_product_detail_table_partial_view(self):
        url = reverse('workorder_product_detail_table', args=[self.work_order.pk])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_workorder_product_detail_table.html')
        self.assertContains(response, 'Product 1')

    def test_product_detail_update_view(self):
        detail_prod = WorkOrderProductDetail.objects.get(work_order=self.work_order, item_code='P001')
        url = reverse('workorder_product_detail_edit', args=[detail_prod.pk])
        data = {
            'item_code': 'P001', # Keep original item code
            'description': 'Updated Product 1 Description', 
            'qty': 12.5
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response)
        self.assertEqual(response['HX-Trigger'], 'refreshProductDetailList')
        detail_prod.refresh_from_db()
        self.assertEqual(detail_prod.description, 'Updated Product 1 Description')
        self.assertEqual(detail_prod.qty, 12.5)

    def test_product_detail_delete_view(self):
        detail_prod = WorkOrderProductDetail.objects.get(work_order=self.work_order, item_code='P001')
        url = reverse('workorder_product_detail_delete', args=[detail_prod.pk])
        response = self.client.delete(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response)
        self.assertEqual(response['HX-Trigger'], 'refreshProductDetailList')
        self.assertFalse(WorkOrderProductDetail.objects.filter(pk=detail_prod.pk).exists())

    def test_get_states_view(self):
        Country.objects.create(id=99, country_name='TestLand')
        State.objects.create(id=999, country_id=99, state_name='TestState')
        url = reverse('get_states') + '?country_id=99'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="999">TestState</option>')

    def test_get_cities_view(self):
        State.objects.create(id=888, country_id=1, state_name='AnotherState')
        City.objects.create(id=8888, state_id=888, city_name='TestCity')
        url = reverse('get_cities') + '?state_id=888'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="8888">TestCity</option>')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content and Forms:**
    *   The main `workorder_edit_details.html` template uses `hx-post` for the final "Update" form submission, targeting `body` with `hx-swap="none"` and then redirecting using `HX-Redirect` header upon success.
    *   The temporary and permanent product tables (`_workorder_product_temp_table.html` and `_workorder_product_detail_table.html`) are loaded into their respective containers using `hx-get` on page `load` and `refreshProductTempList`/`refreshProductDetailList` custom events.
    *   Adding new temporary products is handled by an `hx-post` on the product form, triggering a `refreshProductTempList` event.
    *   Edit/Delete buttons on product tables use `hx-get` to fetch the form/confirmation partial into a modal (`#modalContent`), and `hx-post`/`hx-delete` for submission from within the modal, triggering list refreshes.
    *   Dynamic dropdowns (Country -> State -> City) use `hx-get` on `change` events, targeting the subsequent dropdown's `<select>` element.
    *   `HX-Trigger` headers are explicitly used by views to send events back to the client after successful CRUD operations, allowing relevant parts of the UI to auto-refresh without full page reloads.

*   **Alpine.js for UI State Management:**
    *   `x-data` is used on the main container to manage the `activeTab` state, controlling the visibility of each tab's content.
    *   A global Alpine.js store (`$store.modal`) is used to manage the visibility and content of the universal modal for product forms and delete confirmations.
    *   Another Alpine.js store (`$store.shipping`) is used to track the selected Country and State values for enabling reactive `hx-get` calls for dependent dropdowns.
    *   `x-show` directives conditionally display tab panels and the modal.
    *   `@click` handlers on tab buttons update the `activeTab` variable.
    *   `_` (Hyperscript) is used for simple client-side actions, like opening/closing the modal.
    *   `$watch` allows reacting to changes in Alpine.js store values to trigger HTMX requests for dynamic dropdowns.

*   **DataTables for List Views:**
    *   Both `_workorder_product_temp_table.html` and `_workorder_product_detail_table.html` partials include JavaScript to initialize DataTables on their respective `<table>` elements.
    *   The `htmx:afterSwap` event listener in `workorder_edit_details.html` ensures DataTables are re-initialized correctly after HTMX loads new table content, preventing issues with previously initialized tables.
    *   This provides instant client-side search, sort, and pagination without server roundtrips for basic table interactions.

*   **No Additional JavaScript:**
    *   Beyond HTMX, Alpine.js, and DataTables, no custom JavaScript is required for the dynamic behavior. This keeps the frontend highly maintainable and consistent.

---

### Final Notes

*   **Placeholder Management:** Remember to replace `reverse_lazy('workorder_list_view')` with the actual URL of your Work Order list page once defined.
*   **User Context:** The `comp_id` and `fin_year_id` are assumed to be available from the `request.user` object. In a real-world scenario, you might need to extend Django's `User` model or use a custom user profile to store these, or retrieve them from session/middleware.
*   **Database Migration:** After defining your Django models (with `managed=False`), you will use Django's `inspectdb` to verify the existing database structure matches, and `makemigrations --empty` followed by `migrate --fake` to trick Django into thinking the initial migrations have been applied without altering the legacy database.
*   **External CSS/JS:** Ensure your `core/base.html` includes necessary CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables (and optionally a date picker like Flatpickr if native HTML5 date inputs are not sufficient).
*   **Error Handling and User Feedback:** Django's `messages` framework is used for success/error notifications. For HTMX requests, these messages can be displayed as toast notifications using Alpine.js or a simple JS library, triggered by `HX-Trigger` events.
*   **Security:** Always ensure proper authentication and authorization (e.g., using Django's `LoginRequiredMixin` for views) are implemented for sensitive operations.
*   **Idempotency:** When `hx-post` is used for `form_valid`, ensure that the backend operations are idempotent or handle potential double-submissions appropriately (e.g., by checking if `temp_products` have already been processed). The transactional block helps here.