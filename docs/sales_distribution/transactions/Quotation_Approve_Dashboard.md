## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code is a placeholder. It defines a page structure but contains no specific logic or UI components. This is common in legacy systems where functionality is often embedded within controls or master pages, or dynamically loaded. Based on the page name "Quotation_Approve_Dashboard", we infer that this page is intended to display and manage sales quotations, likely with an approval workflow.

Our modernization plan will establish a robust Django application for "Quotation" management, adhering to modern best practices, emphasizing automation-driven development, and ensuring a seamless user experience with HTMX and Alpine.js.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not explicitly define database interactions, we will infer a common database schema for a "Quotation" dashboard. This inference is based on typical business requirements for managing sales quotations.

-   **[TABLE_NAME]**: `tblQuotation`
-   **Columns Inferred**:
    *   `QuotationNo` (Primary Key / Unique Identifier, string type)
    *   `QuotationDate` (Date of quotation, date type)
    *   `CustomerName` (Name of the customer, string type)
    *   `TotalAmount` (Total value of the quotation, decimal type)
    *   `Status` (Approval status: e.g., 'Pending', 'Approved', 'Rejected', string type)
    *   `ApprovedBy` (User who approved, optional, string type)
    *   `ApprovalDate` (Date of approval, optional, date type)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the page title "Quotation Approve Dashboard", the core functionality is **Read** (displaying quotations) and **Update** (approving quotations). For a complete and flexible system, we will include the full suite of CRUD (Create, Read, Update, Delete) operations.

-   **Create**: Add new sales quotations.
-   **Read**: View a list of all quotations, with filters for status (e.g., 'Pending' for approval).
-   **Update**: Modify existing quotations, including changing their 'Status' to 'Approved' or 'Rejected'. This covers the "Approve" functionality.
-   **Delete**: Remove quotations from the system.
-   **Validation Logic**: Basic field validation (e.g., required fields, data type conformity) will be implemented in the Django Form layer. Specific business logic for approvals (e.g., only certain roles can approve) will reside in the Model.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Based on a typical "Dashboard" UI, we infer the following components and their Django/HTMX/Alpine.js equivalents:

-   **Data List Display**:
    *   **ASP.NET**: Likely a `GridView` or `Repeater`.
    *   **Django**: A standard HTML `<table>` integrated with **DataTables.js** for client-side searching, sorting, and pagination. This will be loaded dynamically via HTMX.
-   **Action Buttons**:
    *   **ASP.NET**: `Button` or `LinkButton` (e.g., "Add New Quotation", "Edit", "Delete", "Approve").
    *   **Django**: Standard HTML `<button>` or `<a>` tags with **HTMX** attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`, `hx-trigger`) to load forms into a modal or trigger actions without full page reloads.
-   **Data Entry Forms**:
    *   **ASP.NET**: `TextBox`, `DropDownList`, `CalendarExtender` (for dates) within `FormView` or separate pages.
    *   **Django**: Standard HTML `<input>`, `<select>`, etc., rendered by Django Forms. These forms will be loaded into a **modal** using **Alpine.js** for UI state management (showing/hiding the modal) and HTMX for submission.
-   **Modals/Pop-ups**:
    *   **ASP.NET**: AJAX Modal Pop-up Extender or custom JavaScript.
    *   **Django**: A simple `div` structure controlled by **Alpine.js** to toggle visibility, with content loaded via HTMX.

## Step 4: Generate Django Code

We will create a new Django application, let's call it `quotation_app`, within the Django project.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

We'll define the `Quotation` model mapping directly to the `tblQuotation` table, setting `managed = False` as per the AutoERP guidelines.

```python
# quotation_app/models.py
from django.db import models
from decimal import Decimal

class Quotation(models.Model):
    STATUS_CHOICES = [
        ('P', 'Pending'),
        ('A', 'Approved'),
        ('R', 'Rejected'),
    ]

    quotation_no = models.CharField(
        db_column='QuotationNo', 
        max_length=50, 
        unique=True, 
        primary_key=True,
        verbose_name='Quotation Number'
    )
    quotation_date = models.DateField(
        db_column='QuotationDate', 
        verbose_name='Quotation Date'
    )
    customer_name = models.CharField(
        db_column='CustomerName', 
        max_length=255, 
        verbose_name='Customer Name'
    )
    total_amount = models.DecimalField(
        db_column='TotalAmount', 
        max_digits=18, 
        decimal_places=2, 
        default=Decimal('0.00'),
        verbose_name='Total Amount'
    )
    status = models.CharField(
        db_column='Status', 
        max_length=1, 
        choices=STATUS_CHOICES, 
        default='P',
        verbose_name='Status'
    )
    approved_by = models.CharField(
        db_column='ApprovedBy', 
        max_length=255, 
        blank=True, 
        null=True,
        verbose_name='Approved By'
    )
    approval_date = models.DateField(
        db_column='ApprovalDate', 
        blank=True, 
        null=True,
        verbose_name='Approval Date'
    )

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'tblQuotation'
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'
        ordering = ['-quotation_date', 'quotation_no']

    def __str__(self):
        return f'{self.quotation_no} - {self.customer_name} ({self.get_status_display()})'
        
    def is_pending(self):
        """
        Model method to check if the quotation is pending approval.
        Business logic resides in the model.
        """
        return self.status == 'P'

    def approve(self, approved_by_user):
        """
        Model method to approve a quotation.
        Encapsulates approval logic.
        """
        if self.is_pending():
            self.status = 'A'
            self.approved_by = approved_by_user
            self.approval_date = models.DateField.today() # Placeholder, use datetime.date.today() in real app
            self.save()
            return True
        return False

    def reject(self, rejected_by_user):
        """
        Model method to reject a quotation.
        Encapsulates rejection logic.
        """
        if self.is_pending():
            self.status = 'R'
            self.approved_by = rejected_by_user # Store who rejected
            self.approval_date = models.DateField.today()
            self.save()
            return True
        return False
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be used for `Quotation` creation and updates.

```python
# quotation_app/forms.py
from django import forms
from .models import Quotation

class QuotationForm(forms.ModelForm):
    class Meta:
        model = Quotation
        fields = ['quotation_no', 'quotation_date', 'customer_name', 'total_amount', 'status']
        widgets = {
            'quotation_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'quotation_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'type': 'date'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'step': '0.01'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
        
    def clean_quotation_no(self):
        quotation_no = self.cleaned_data['quotation_no']
        # Example validation: Quotation number must be alphanumeric
        if not quotation_no.isalnum():
            raise forms.ValidationError("Quotation number must be alphanumeric.")
        return quotation_no
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We will define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the `Quotation` model. An additional `TablePartialView` is crucial for HTMX to fetch and swap the DataTables content.

```python
# quotation_app/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import Quotation
from .forms import QuotationForm
import datetime # For demonstration of approval date

class QuotationListView(ListView):
    model = Quotation
    template_name = 'quotation_app/quotation/list.html'
    context_object_name = 'quotations'

    # Views should be thin, data fetching is usually handled by ListView's default get_queryset
    # Any filtering specific to a dashboard (e.g., 'pending' status) would be in get_queryset
    # Example:
    # def get_queryset(self):
    #     return Quotation.objects.filter(status='P') # For pending quotations dashboard

class QuotationTablePartialView(ListView):
    model = Quotation
    template_name = 'quotation_app/quotation/_quotation_table.html'
    context_object_name = 'quotations' # This will be used in the partial template

    # This view is specifically for HTMX to load the table content
    def get_queryset(self):
        # Example: Allow filtering via URL parameters if needed by HTMX
        # status_filter = self.request.GET.get('status', None)
        # if status_filter:
        #     return Quotation.objects.filter(status=status_filter)
        return Quotation.objects.all()

class QuotationCreateView(CreateView):
    model = Quotation
    form_class = QuotationForm
    template_name = 'quotation_app/quotation/_quotation_form.html' # Partial for modal
    success_url = reverse_lazy('quotation_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Quotation added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return render(self.request, self.template_name, {'form': form})
        return response


class QuotationUpdateView(UpdateView):
    model = Quotation
    form_class = QuotationForm
    template_name = 'quotation_app/quotation/_quotation_form.html' # Partial for modal
    success_url = reverse_lazy('quotation_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Quotation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class QuotationDeleteView(DeleteView):
    model = Quotation
    template_name = 'quotation_app/quotation/_quotation_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('quotation_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Quotation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return response

class QuotationApproveView(View):
    # This view demonstrates a specific action beyond basic CRUD.
    # It updates the status of a quotation.
    def post(self, request, pk):
        try:
            quotation = Quotation.objects.get(pk=pk)
            # In a real app, 'request.user' would be used instead of a hardcoded string
            if quotation.approve("Admin User"): # Business logic handled by model
                messages.success(self.request, f'Quotation {quotation.quotation_no} approved successfully.')
            else:
                messages.error(self.request, f'Quotation {quotation.quotation_no} could not be approved (already approved/rejected).')
        except Quotation.DoesNotExist:
            messages.error(self.request, 'Quotation not found.')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQuotationList'
                }
            )
        return self.get(request) # Fallback to GET for non-HTMX if needed, or redirect
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates are structured for reusability and HTMX integration. `list.html` is the main page, and the others are partials loaded into a modal.

```html
{# quotation_app/templates/quotation_app/quotation/list.html #}
{% extends 'core/base.html' %} {# IMPORTANT: Assumes core/base.html exists and contains boilerplate, CDNs for Tailwind, HTMX, Alpine.js, and DataTables #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Quotations Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'quotation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Quotation
        </button>
    </div>
    
    {# Container for HTMX-loaded DataTable #}
    <div id="quotationTable-container"
         hx-trigger="load, refreshQuotationList from:body" {# Loads on page load and on custom event #}
         hx-get="{% url 'quotation_table_partial' %}" {# Fetches the table content #}
         hx-swap="innerHTML">
        {# Initial loading state #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Quotations...</p>
        </div>
    </div>
    
    {# Modal for CRUD forms (Add/Edit/Delete) #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         x-data="{ showModal: false }" x-show="showModal"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 sm:mx-auto relative transform transition-all sm:my-8 sm:align-middle">
            {# Content loaded here via HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is primarily used here for the modal's internal state
        // but its primary control for showing/hiding is via htmx/_hyperscript due to modal structure
        // A more complex Alpine state could be defined here if needed for UI elements within the dashboard
    });

    // Event listener to manage modal visibility from HTMX triggers
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status !== 204) {
            document.getElementById('modal').classList.add('is-active');
        } else if (event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Ensure DataTables reinitializes after HTMX swap
    document.body.addEventListener('refreshQuotationList', function() {
        console.log('Quotation list refreshed via HTMX.');
        // DataTables reinitialization will happen within the partial template itself
        // as it's swapped in.
    });
</script>
{% endblock %}
```

```html
{# quotation_app/templates/quotation_app/quotation/_quotation_table.html #}
<div class="overflow-x-auto bg-white shadow-lg rounded-lg">
    <table id="quotationTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No.</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for quotation in quotations %}
            <tr class="hover:bg-gray-50">
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ quotation.quotation_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ quotation.quotation_date|date:"Y-m-d" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ quotation.customer_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">${{ quotation.total_amount|floatformat:2 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if quotation.status == 'A' %}bg-green-100 text-green-800
                        {% elif quotation.status == 'P' %}bg-yellow-100 text-yellow-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        {{ quotation.get_status_display }}
                    </span>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3 transition duration-300 ease-in-out"
                        hx-get="{% url 'quotation_edit' quotation.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    {% if quotation.is_pending %}
                    <button 
                        class="text-green-600 hover:text-green-900 mr-3 transition duration-300 ease-in-out"
                        hx-post="{% url 'quotation_approve' quotation.pk %}"
                        hx-confirm="Are you sure you want to approve quotation {{ quotation.quotation_no }}?"
                        hx-indicator="#loadingIndicator"
                        hx-swap="none"> {# Status code 204 will trigger refresh #}
                        Approve
                    </button>
                    {% endif %}
                    <button 
                        class="text-red-600 hover:text-red-900 transition duration-300 ease-in-out"
                        hx-get="{% url 'quotation_delete' quotation.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {# Loading indicator for HTMX actions #}
    <div id="loadingIndicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-[100]">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        <p class="mt-2 text-white ml-3 text-lg">Processing...</p>
    </div>
</div>

<script>
    // Initialize DataTables once the table content is swapped in
    $(document).ready(function() {
        $('#quotationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [6] } // Disable sorting on Actions column
            ]
        });
    });
</script>
```

```html
{# quotation_app/templates/quotation_app/quotation/_quotation_form.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Quotation</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" {# HTMX will receive 204 and handle modal close/list refresh #}
          hx-target="#modalContent" {# Re-target to form itself if validation fails #}
          hx-indicator="#loadingIndicator"> {# Show global loading indicator #}
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4">
            {% for field in form %}
            <div class="col-span-1">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal"> {# Alpine.js/Hyperscript for modal close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save Quotation
            </button>
        </div>
    </form>
</div>
```

```html
{# quotation_app/templates/quotation_app/quotation/_quotation_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete quotation **{{ object.quotation_no }}** ({{ object.customer_name }})?</p>
    
    <form hx-post="{% url 'quotation_delete' object.pk %}" 
          hx-swap="none" {# HTMX will receive 204 and handle modal close/list refresh #}
          hx-indicator="#loadingIndicator"> {# Show global loading indicator #}
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal"> {# Alpine.js/Hyperscript for modal close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Define paths within `quotation_app/urls.py`.

```python
# quotation_app/urls.py
from django.urls import path
from .views import (
    QuotationListView, 
    QuotationTablePartialView, 
    QuotationCreateView, 
    QuotationUpdateView, 
    QuotationDeleteView,
    QuotationApproveView
)

urlpatterns = [
    path('quotations/', QuotationListView.as_view(), name='quotation_list'),
    path('quotations/table/', QuotationTablePartialView.as_view(), name='quotation_table_partial'),
    path('quotations/add/', QuotationCreateView.as_view(), name='quotation_add'),
    path('quotations/edit/<str:pk>/', QuotationUpdateView.as_view(), name='quotation_edit'), # Use str:pk for CharField primary key
    path('quotations/delete/<str:pk>/', QuotationDeleteView.as_view(), name='quotation_delete'),
    path('quotations/approve/<str:pk>/', QuotationApproveView.as_view(), name='quotation_approve'),
]
```

Remember to include these URLs in your project's main `urls.py`:

```python
# myproject/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('quotation_app.urls')), # Include your new app's URLs
    # Add other app URLs here
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `Quotation` model and integration tests for all views to ensure functionality and HTMX interactions work as expected.

```python
# quotation_app/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import Quotation
from datetime import date
from decimal import Decimal

class QuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Quotation.objects.create(
            quotation_no='Q001',
            quotation_date=date(2023, 1, 15),
            customer_name='ABC Corp',
            total_amount=Decimal('1000.00'),
            status='P',
        )
        Quotation.objects.create(
            quotation_no='Q002',
            quotation_date=date(2023, 1, 16),
            customer_name='XYZ Ltd',
            total_amount=Decimal('2500.50'),
            status='A',
            approved_by='John Doe',
            approval_date=date(2023, 1, 17)
        )
  
    def test_quotation_creation(self):
        q1 = Quotation.objects.get(quotation_no='Q001')
        self.assertEqual(q1.quotation_date, date(2023, 1, 15))
        self.assertEqual(q1.customer_name, 'ABC Corp')
        self.assertEqual(q1.total_amount, Decimal('1000.00'))
        self.assertEqual(q1.status, 'P')
        self.assertIsNone(q1.approved_by)
        self.assertIsNone(q1.approval_date)

    def test_quotation_str_method(self):
        q1 = Quotation.objects.get(quotation_no='Q001')
        self.assertEqual(str(q1), 'Q001 - ABC Corp (Pending)')
        q2 = Quotation.objects.get(quotation_no='Q002')
        self.assertEqual(str(q2), 'Q002 - XYZ Ltd (Approved)')

    def test_quotation_is_pending_method(self):
        q1 = Quotation.objects.get(quotation_no='Q001')
        self.assertTrue(q1.is_pending())
        q2 = Quotation.objects.get(quotation_no='Q002')
        self.assertFalse(q2.is_pending())

    def test_quotation_approve_method(self):
        q1 = Quotation.objects.get(quotation_no='Q001')
        self.assertTrue(q1.approve('Test Approver'))
        q1.refresh_from_db()
        self.assertEqual(q1.status, 'A')
        self.assertEqual(q1.approved_by, 'Test Approver')
        self.assertEqual(q1.approval_date, date.today())
        
        # Try to approve an already approved quotation
        self.assertFalse(q1.approve('Another Approver'))

    def test_quotation_reject_method(self):
        q_pending = Quotation.objects.create(
            quotation_no='Q003',
            quotation_date=date(2023, 2, 1),
            customer_name='Reject Co.',
            total_amount=Decimal('500.00'),
            status='P',
        )
        self.assertTrue(q_pending.reject('Test Rejector'))
        q_pending.refresh_from_db()
        self.assertEqual(q_pending.status, 'R')
        self.assertEqual(q_pending.approved_by, 'Test Rejector')
        self.assertEqual(q_pending.approval_date, date.today())
        
        # Try to reject an already rejected quotation
        self.assertFalse(q_pending.reject('Another Rejector'))


class QuotationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Quotation.objects.create(
            quotation_no='Q001',
            quotation_date=date(2023, 1, 15),
            customer_name='ABC Corp',
            total_amount=Decimal('1000.00'),
            status='P',
        )
        Quotation.objects.create(
            quotation_no='Q002',
            quotation_date=date(2023, 1, 16),
            customer_name='XYZ Ltd',
            total_amount=Decimal('2500.50'),
            status='A',
            approved_by='John Doe',
            approval_date=date(2023, 1, 17)
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('quotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotation_app/quotation/list.html')
        # The list view only loads the container, the actual table is loaded via HTMX
        # So we assert the main template is used.

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('quotation_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotation_app/quotation/_quotation_table.html')
        self.assertContains(response, 'ABC Corp')
        self.assertContains(response, 'XYZ Ltd')
        self.assertContains(response, 'Q001')
        self.assertContains(response, 'Q002')
        self.assertContains(response, 'quotationTable') # Check for DataTables ID

    def test_create_view_get(self):
        response = self.client.get(reverse('quotation_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotation_app/quotation/_quotation_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Quotation')

    def test_create_view_post_success(self):
        data = {
            'quotation_no': 'Q003',
            'quotation_date': '2023-03-01',
            'customer_name': 'New Customer Inc',
            'total_amount': '5000.00',
            'status': 'P',
        }
        response = self.client.post(reverse('quotation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        self.assertTrue(Quotation.objects.filter(quotation_no='Q003').exists())
        self.assertContains(response, 'Quotation added successfully', status_code=204) # Messages are set on redirect

    def test_create_view_post_invalid(self):
        data = {
            'quotation_no': '', # Invalid
            'quotation_date': 'invalid-date', # Invalid
            'customer_name': '', # Invalid
            'total_amount': 'abc', # Invalid
            'status': 'X', # Invalid
        }
        response = self.client.post(reverse('quotation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Re-renders form with errors
        self.assertTemplateUsed(response, 'quotation_app/quotation/_quotation_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid date.')
        self.assertContains(response, 'Enter a number.')

    def test_update_view_get(self):
        obj = Quotation.objects.get(quotation_no='Q001')
        response = self.client.get(reverse('quotation_edit', args=[obj.quotation_no]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotation_app/quotation/_quotation_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Quotation')
        self.assertContains(response, 'ABC Corp')

    def test_update_view_post_success(self):
        obj = Quotation.objects.get(quotation_no='Q001')
        data = {
            'quotation_no': obj.quotation_no,
            'quotation_date': '2023-01-15',
            'customer_name': 'ABC Corporation Updated',
            'total_amount': '1100.00',
            'status': 'P',
        }
        response = self.client.post(reverse('quotation_edit', args=[obj.quotation_no]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        obj.refresh_from_db()
        self.assertEqual(obj.customer_name, 'ABC Corporation Updated')
        self.assertEqual(obj.total_amount, Decimal('1100.00'))

    def test_delete_view_get(self):
        obj = Quotation.objects.get(quotation_no='Q001')
        response = self.client.get(reverse('quotation_delete', args=[obj.quotation_no]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quotation_app/quotation/_quotation_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, 'ABC Corp')

    def test_delete_view_post_success(self):
        obj_to_delete = Quotation.objects.create(
            quotation_no='Q004',
            quotation_date=date(2023, 4, 1),
            customer_name='Delete Me',
            total_amount=Decimal('100.00'),
            status='P',
        )
        response = self.client.post(reverse('quotation_delete', args=[obj_to_delete.quotation_no]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        self.assertFalse(Quotation.objects.filter(quotation_no='Q004').exists())

    def test_approve_view_post_success(self):
        obj = Quotation.objects.get(quotation_no='Q001') # This is pending
        response = self.client.post(reverse('quotation_approve', args=[obj.quotation_no]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'A')
        self.assertEqual(obj.approved_by, 'Admin User')
        self.assertEqual(obj.approval_date, date.today())
        
    def test_approve_view_post_already_approved(self):
        obj = Quotation.objects.get(quotation_no='Q002') # This is already approved
        response = self.client.post(reverse('quotation_approve', args=[obj.quotation_no]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Still 204, but no change
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'A') # Status should remain 'A'
        self.assertEqual(obj.approved_by, 'John Doe') # Should not change
        self.assertEqual(obj.approval_date, date(2023, 1, 17)) # Should not change
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided templates and views already integrate HTMX and Alpine.js as per best practices:

-   **HTMX for dynamic interactions:**
    *   The main `list.html` uses `hx-get` on load (and on a custom `refreshQuotationList` event) to fetch the table content from `quotation_table_partial`.
    *   "Add New Quotation" button uses `hx-get` to load the `quotation_add` form into the `#modalContent` div.
    *   "Edit" and "Delete" buttons similarly use `hx-get` to load their respective forms into the modal.
    *   Form submissions (`_quotation_form.html`, `_quotation_confirm_delete.html`) use `hx-post` with `hx-swap="none"` and the backend returns a `204 No Content` status along with an `HX-Trigger` header (`refreshQuotationList`) to instruct the client to refresh the main table.
    *   The "Approve" button directly uses `hx-post` for immediate action, triggering a list refresh upon success.
    *   A global `htmx-indicator` is set up to provide visual feedback during HTMX requests.

-   **Alpine.js for UI state management:**
    *   The modal (`#modal`) uses `x-data` and `x-show` for basic state management. While the `hx-target` and `_` (Hyperscript) are primarily used to toggle its `is-active` class, Alpine.js could manage more complex UI states within the modal if needed.
    *   The `on click remove .is-active from me` attribute on the modal overlay and cancel buttons uses Hyperscript (often used alongside HTMX and Alpine.js) to close the modal by removing the `is-active` class.

-   **DataTables for list views:**
    *   The `_quotation_table.html` partial contains the `<script>` tag to initialize DataTables on the `quotationTable` ID immediately after it's loaded into the DOM by HTMX. This ensures that features like search, sort, and pagination are available without a full page refresh.

-   **No additional JavaScript:**
    *   All interactions are handled declaratively with HTMX attributes, or via simple Alpine.js/Hyperscript for UI state. No complex custom JavaScript files are required for this functionality, promoting a lean and maintainable frontend.

## Final Notes

This comprehensive plan provides a clear, step-by-step approach to modernizing the ASP.NET Quotation Dashboard to Django. It adheres to the specified guidelines, focusing on AI-assisted automation by providing structured, complete code examples that can be generated and integrated into a modern Django project. The emphasis on fat models, thin views, HTMX, Alpine.js, and DataTables ensures a performant, maintainable, and user-friendly solution that significantly improves upon legacy ASP.NET architectures.