This comprehensive Django modernization plan outlines the strategic transition from your legacy ASP.NET application to a modern, robust, and scalable Django-based solution. Our approach prioritizes automated conversion and leverages conversational AI guidance to streamline the process, focusing on business benefits and reduced manual effort.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
Our AI analysis system automatically scans the provided ASP.NET `.aspx` and `.cs` files to identify all database interactions. This includes parsing `SqlDataSource` definitions, `SELECT`, `INSERT`, `UPDATE`, and `DELETE` SQL commands, and how data is bound to UI controls like `GridViews` and `DropDownLists`.

Based on this, we've identified the following key tables and their inferred columns:

- **`SD_Cust_master`**: Customer information.
    - `CustomerId` (Primary Key, inferred string/int)
    - `CustomerName` (string)
    - `RegdAddress` (string)
    - `RegdCountry` (Foreign Key to `tblcountry`)
    - `RegdState` (Foreign Key to `tblState`)
    - `RegdCity` (Foreign Key to `tblCity`)
    - `RegdPinNo` (string)
    - `CompId` (int)

- **`tblcountry`**: Country lookup.
    - `CId` (Primary Key, int)
    - `CountryName` (string)

- **`tblState`**: State lookup.
    - `SId` (Primary Key, int)
    - `StateName` (string)

- **`tblCity`**: City lookup.
    - `CityId` (Primary Key, int)
    - `CityName` (string)

- **`Unit_Master`**: Unit of Measurement lookup.
    - `Id` (Primary Key, int)
    - `Symbol` (string)

- **`tblVAT_Master`**: VAT/CST terms lookup.
    - `Id` (Primary Key, int)
    - `Terms` (string)

- **`tblExciseser_Master`**: Excise/Service Tax terms lookup.
    - `Id` (Primary Key, int)
    - `Terms` (string)

- **`SD_Cust_Quotation_Master`**: Main quotation header details.
    - `Id` (Primary Key, int)
    - `CustomerId` (Foreign Key to `SD_Cust_master`)
    - `QuotationNo` (string)
    - `PaymentTerms` (string)
    - `PF` (float)
    - `PFType` (int, 0=Amt, 1=Per)
    - `VATCST` (Foreign Key to `tblVAT_Master`)
    - `Excise` (Foreign Key to `tblExciseser_Master`)
    - `Octroi` (float)
    - `OctroiType` (int, 0=Amt, 1=Per)
    - `Warrenty` (string)
    - `Insurance` (float)
    - `Transport` (string)
    - `NoteNo` (string)
    - `RegistrationNo` (string)
    - `Freight` (float)
    - `FreightType` (int, 0=Amt, 1=Per)
    - `Remarks` (text)
    - `DueDate` (date)
    - `Validity` (string)
    - `OtherCharges` (float)
    - `OtherChargesType` (int, 0=Amt, 1=Per)
    - `DeliveryTerms` (string)
    - `SysDate` (date)
    - `SysTime` (time)
    - `SessionId` (string)
    - `CompId` (int)
    - `FinYearId` (int)

- **`SD_Cust_Quotation_Details_Temp`**: Temporary quotation line items (staging area).
    - `Id` (Primary Key, int)
    - `SessionId` (string)
    - `CompId` (int)
    - `FinYearId` (int)
    - `ItemDesc` (string)
    - `TotalQty` (float)
    - `Unit` (Foreign Key to `Unit_Master`)
    - `Rate` (float)
    - `Discount` (float)

- **`SD_Cust_Quotation_Details`**: Permanent quotation line items.
    - `Id` (Primary Key, int)
    - `MId` (Foreign Key to `SD_Cust_Quotation_Master`)
    - `ItemDesc` (string)
    - `TotalQty` (float)
    - `Unit` (Foreign Key to `Unit_Master`)
    - `Rate` (float)
    - `Discount` (float)
    - `SessionId` (string) (Though copied, usually not needed for permanent records in Django)
    - `CompId` (int)
    - `FinYearId` (int)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD (Create, Read, Update, Delete) operations and business logic in the ASP.NET code.

**Instructions:**
Our automation tools identify common ASP.NET control patterns and their associated event handlers to map them to standard backend operations.

-   **Read Operations (Initial Load & Display):**
    -   Customer details are read from `SD_Cust_master` based on `CustomerId` from the query string.
    -   Existing `SD_Cust_Quotation_Master` details are read based on `Id` and `CustomerId` from the query string to pre-fill the "Terms & Conditions" tab.
    -   Temporary quotation items are read from `SD_Cust_Quotation_Details_Temp` to populate `GridView1`. This data is specific to the current user's session (`SessionId`, `CompId`, `FinYearId`).
    -   Permanent quotation items are read from `SD_Cust_Quotation_Details` to populate `GridView2`. This data is linked to the `SD_Cust_Quotation_Master` via `MId`.
    -   Dropdowns (`DrpUnit`, `DrpPFType`, `DrpVat`, `DrpExcise`, `DrpOctroiType`, `DrpFreightType`, `DrpOChargeType`) are populated from lookup tables (`Unit_Master`, `tblVAT_Master`, `tblExciseser_Master`).

-   **Create Operation (Adding Temporary Items):**
    -   The "Goods Details" tab's "Submit" button (`BtnSubmit_Click`) inserts new item details (`TxtDesc`, `TxtQty`, `DrpUnit`, `TxtRate`, `TxtDiscount`) into `SD_Cust_Quotation_Details_Temp`. This acts as a staging area.

-   **Update Operations:**
    -   **Main Quotation Update:** The "Terms & Conditions" tab's "Update" button (`BtnUpdate_Click`) updates the `SD_Cust_Quotation_Master` record with the latest terms and conditions.
    -   **Temporary Item Update:** `GridView1_RowUpdating` handles editing individual items in `SD_Cust_Quotation_Details_Temp`.
    -   **Permanent Item Update:** `GridView2_RowUpdating` handles editing individual items already saved in `SD_Cust_Quotation_Details`.

-   **Delete Operations:**
    -   **Temporary Item Delete:** `GridView1_RowDeleting` removes items from `SD_Cust_Quotation_Details_Temp`.
    -   **Permanent Item Delete:** `GridView2_RowDeleting` removes items from `SD_Cust_Quotation_Details`.

-   **Business Logic & Validation:**
    -   The `BtnUpdate_Click` method contains critical logic: it first updates the `QuotationMaster` and then iterates through `SD_Cust_Quotation_Details_Temp` records for the current session and inserts them into `SD_Cust_Quotation_Details`, effectively "committing" the temporary items to the permanent record.
    -   Data validation is present for required fields and numeric/date formats (e.g., `fun.NumberValidationQty`, `fun.DateValidation`). This will be migrated to Django form validation.
    -   Session variables (`compid`, `username`, `finyear`, `TabIndex`) are used to manage user context, company, financial year, and tab state.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to understand the user interface.

**Instructions:**
Our analysis identifies common UI patterns and translates them into modern web components suitable for Django with HTMX and Alpine.js.

-   **Master Page Integration:** The `MasterPageFile="~/MasterPage.master"` indicates a shared layout. In Django, this translates to `core/base.html`, which all feature-specific templates will extend.
-   **Tabbed Interface (`AjaxControlToolkit:TabContainer`):** This is a key UI element controlling different sections of the form. We will replicate this with HTMX to dynamically load tab content without full page reloads, and Alpine.js to manage the active tab state.
-   **Data Grids (`asp:GridView`):** Both `GridView1` (temporary items) and `GridView2` (permanent items) are central to displaying and interacting with line-item data. These will be replaced with DataTables, providing robust client-side features like search, sort, and pagination. HTMX will handle inline editing and deletion for these grids.
-   **Input Controls (`asp:TextBox`, `asp:DropDownList`):** Standard form fields for text, numbers, and selections. These will map directly to Django form fields, styled with Tailwind CSS.
-   **Buttons (`asp:Button`, `asp:LinkButton`):** Used for navigation between tabs, submitting new items, and updating/deleting items. These will be converted to HTML buttons/links with `hx-` attributes for HTMX-driven interactions, often triggering modal dialogs for edit/delete operations.
-   **Validation Controls (`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`):** Client-side and server-side validation. This will be integrated into Django's form validation system.
-   **Date Picker (`cc1:CalendarExtender`):** Replaced by a simple date input with modern browser support or a lightweight Alpine.js component for more advanced calendar functionality if needed.

The overall user experience will be maintained, but with a faster, more responsive interface due to HTMX's partial updates and Alpine.js's lightweight reactivity.

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create Django models based on the extracted database schema.

**Instructions:**
Our system automatically generates Django models, mapping them to your existing database tables using `managed = False` and `db_table`. This means Django will use your current database structure without attempting to manage schema changes, ideal for phased migrations. Business logic related to quotations, such as calculating totals or validating item transfers, will be embedded directly within these models, following the "Fat Model" principle.

**File:** `sales/models.py`

```python
from django.db import models
from datetime import datetime

# Helper choices for common type fields
class RateType(models.IntegerChoices):
    AMOUNT = 0, 'Amt(Rs)'
    PERCENTAGE = 1, 'Per(%)'

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) # Using CharField as PK for CustomerId
    name = models.CharField(db_column='CustomerName', max_length=255)
    address = models.TextField(db_column='RegdAddress')
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='RegdCountry', blank=True, null=True)
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='RegdState', blank=True, null=True)
    city = models.ForeignKey(City, models.DO_NOTHING, db_column='RegdCity', blank=True, null=True)
    pin_no = models.CharField(db_column='RegdPinNo', max_length=20, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.name

    def get_full_address(self):
        """Constructs a formatted address string."""
        parts = [self.address]
        if self.city:
            parts.append(self.city.name)
        if self.state:
            parts.append(self.state.name)
        if self.country:
            parts.append(self.country.name)
        if self.pin_no:
            parts.append(self.pin_no)
        return ", ".join(filter(None, parts))

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class VatTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT/CST Term'
        verbose_name_plural = 'VAT/CST Terms'

    def __str__(self):
        return self.terms

class ExciseTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Term'
        verbose_name_plural = 'Excise Terms'

    def __str__(self):
        return self.terms

class QuotationMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId', to_field='customer_id') # Link to Customer's PK
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50)
    payment_terms = models.CharField(db_column='PaymentTerms', max_length=255)
    pf = models.FloatField(db_column='PF')
    pf_type = models.IntegerField(db_column='PFType', choices=RateType.choices)
    vat_cst = models.ForeignKey(VatTerm, models.DO_NOTHING, db_column='VATCST')
    excise = models.ForeignKey(ExciseTerm, models.DO_NOTHING, db_column='Excise')
    octroi = models.FloatField(db_column='Octroi')
    octroi_type = models.IntegerField(db_column='OctroiType', choices=RateType.choices)
    warrenty = models.CharField(db_column='Warrenty', max_length=255)
    insurance = models.FloatField(db_column='Insurance')
    transport = models.CharField(db_column='Transport', max_length=255)
    note_no = models.CharField(db_column='NoteNo', max_length=255)
    registration_no = models.CharField(db_column='RegistrationNo', max_length=255, blank=True, null=True)
    freight = models.FloatField(db_column='Freight')
    freight_type = models.IntegerField(db_column='FreightType', choices=RateType.choices)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    due_date = models.DateField(db_column='DueDate')
    validity = models.CharField(db_column='Validity', max_length=255)
    other_charges = models.FloatField(db_column='OtherCharges')
    other_charges_type = models.IntegerField(db_column='OtherChargesType', choices=RateType.choices)
    delivery_terms = models.CharField(db_column='DeliveryTerms', max_length=255)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Stored for legacy, likely redundant in modern Django
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Quotation Master'
        verbose_name_plural = 'Quotation Masters'

    def __str__(self):
        return f"Quotation {self.quotation_no} for {self.customer.name}"

    def transfer_temp_items(self, session_id, comp_id, fin_year_id):
        """
        Transfers items from QuotationTempItem to QuotationDetail for this master record.
        This business logic is crucial and resides in the model.
        """
        temp_items = QuotationTempItem.objects.filter(
            session_id=session_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        for temp_item in temp_items:
            QuotationDetail.objects.create(
                master=self,
                item_desc=temp_item.item_desc,
                total_qty=temp_item.total_qty,
                unit=temp_item.unit,
                rate=temp_item.rate,
                discount=temp_item.discount,
                session_id=temp_item.session_id, # Keeping for exact legacy schema, though often not needed
                comp_id=temp_item.comp_id,
                fin_year_id=temp_item.fin_year_id,
            )
            temp_item.delete() # Remove temporary item after transfer
        return temp_items.count()

class QuotationDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK in actual DB
    master = models.ForeignKey(QuotationMaster, models.DO_NOTHING, db_column='MId')
    item_desc = models.CharField(db_column='ItemDesc', max_length=255)
    total_qty = models.FloatField(db_column='TotalQty')
    unit = models.ForeignKey(Unit, models.DO_NOTHING, db_column='Unit')
    rate = models.FloatField(db_column='Rate')
    discount = models.FloatField(db_column='Discount')
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Keeping for exact legacy schema
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Keeping for exact legacy schema
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True) # Keeping for exact legacy schema

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Details'
        verbose_name = 'Quotation Detail'
        verbose_name_plural = 'Quotation Details'

    def __str__(self):
        return f"{self.item_desc} ({self.total_qty} {self.unit.symbol})"

class QuotationTempItem(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK in actual DB
    session_id = models.CharField(db_column='SessionId', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    item_desc = models.CharField(db_column='ItemDesc', max_length=255)
    total_qty = models.FloatField(db_column='TotalQty')
    unit = models.ForeignKey(Unit, models.DO_NOTHING, db_column='Unit')
    rate = models.FloatField(db_column='Rate')
    discount = models.FloatField(db_column='Discount')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Details_Temp'
        verbose_name = 'Temporary Quotation Item'
        verbose_name_plural = 'Temporary Quotation Items'

    def __str__(self):
        return f"TEMP: {self.item_desc} ({self.total_qty} {self.unit.symbol})"

    @classmethod
    def get_items_for_session(cls, session_id, comp_id, fin_year_id):
        """Retrieves temporary items for a given session, company, and financial year."""
        return cls.objects.filter(session_id=session_id, comp_id=comp_id, fin_year_id=fin_year_id).order_by('-id')

```

### 4.2 Forms

**Task:** Define Django forms for user input, including validation and styling.

**Instructions:**
Our system generates `ModelForms` based on the identified models. These forms automatically include fields from the model, and we configure widgets to apply the necessary Tailwind CSS classes for consistent styling. Custom validation rules, replicating ASP.NET's `RequiredFieldValidator` and `RegularExpressionValidator`, are added here.

**File:** `sales/forms.py`

```python
from django import forms
from .models import QuotationMaster, QuotationTempItem, Unit, RateType

class QuotationMasterForm(forms.ModelForm):
    # Customer details are displayed, not edited via this form,
    # so we don't include them directly in the form fields.
    # The ASP.NET page fetched them from QueryString and another DB lookup.

    # DueDate field with specific input format
    due_date = forms.DateField(
        input_formats=['%d-%m-%Y'],
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # Use HTML5 date input type for native calendar
        })
    )

    class Meta:
        model = QuotationMaster
        fields = [
            'payment_terms', 'pf', 'pf_type', 'vat_cst', 'excise',
            'octroi', 'octroi_type', 'warrenty', 'insurance', 'transport',
            'note_no', 'registration_no', 'freight', 'freight_type',
            'remarks', 'due_date', 'validity', 'other_charges',
            'other_charges_type', 'delivery_terms'
        ]
        widgets = {
            'payment_terms': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'vat_cst': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'excise': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'octroi': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'octroi_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'warrenty': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'insurance': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'transport': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'note_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'registration_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'freight': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'freight_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'validity': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'other_charges': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'other_charges_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'delivery_terms': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'payment_terms': 'Payment Terms',
            'pf': 'P & F',
            'pf_type': 'P & F Type',
            'vat_cst': 'VAT/CST',
            'excise': 'Excise / Service Tax',
            'octroi': 'Octroi',
            'octroi_type': 'Octroi Type',
            'warrenty': 'Warranty',
            'insurance': 'Insurance',
            'transport': 'Mode of Transport',
            'note_no': 'R.R./G.C. Note No.',
            'registration_no': 'If by motor vehicle, it\'s registr. no',
            'freight': 'Freight',
            'freight_type': 'Freight Type',
            'remarks': 'Remarks',
            'due_date': 'Due Date',
            'validity': 'Validity',
            'other_charges': 'Other Charges',
            'other_charges_type': 'Other Charges Type',
            'delivery_terms': 'Delivery Terms',
        }

    def clean_pf(self):
        pf = self.cleaned_data.get('pf')
        if pf is None:
            raise forms.ValidationError("P & F is required.")
        if not isinstance(pf, (int, float)) or pf < 0:
            raise forms.ValidationError("P & F must be a non-negative number.")
        return pf
    
    # Add more custom validation methods for numeric fields like octroi, insurance, freight, other_charges
    # to replicate RegularExpressionValidator logic. Example for octroi:
    def clean_octroi(self):
        octroi = self.cleaned_data.get('octroi')
        if octroi is None:
            raise forms.ValidationError("Octroi is required.")
        if not isinstance(octroi, (int, float)) or octroi < 0:
            raise forms.ValidationError("Octroi must be a non-negative number.")
        return octroi
    
    def clean_insurance(self):
        insurance = self.cleaned_data.get('insurance')
        if insurance is None:
            raise forms.ValidationError("Insurance is required.")
        if not isinstance(insurance, (int, float)) or insurance < 0:
            raise forms.ValidationError("Insurance must be a non-negative number.")
        return insurance

    def clean_freight(self):
        freight = self.cleaned_data.get('freight')
        if freight is None:
            raise forms.ValidationError("Freight is required.")
        if not isinstance(freight, (int, float)) or freight < 0:
            raise forms.ValidationError("Freight must be a non-negative number.")
        return freight
    
    def clean_other_charges(self):
        other_charges = self.cleaned_data.get('other_charges')
        if other_charges is None:
            raise forms.ValidationError("Other Charges is required.")
        if not isinstance(other_charges, (int, float)) or other_charges < 0:
            raise forms.ValidationError("Other Charges must be a non-negative number.")
        return other_charges

class QuotationTempItemForm(forms.ModelForm):
    class Meta:
        model = QuotationTempItem
        fields = ['item_desc', 'total_qty', 'unit', 'rate', 'discount']
        widgets = {
            'item_desc': forms.Textarea(attrs={'class': 'box3 h-24 w-full', 'rows': 4}),
            'total_qty': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.001'}), # 3 decimal places
            'unit': forms.Select(attrs={'class': 'box3 w-full'}),
            'rate': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}), # 2 decimal places
            'discount': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}), # 2 decimal places
        }
        labels = {
            'item_desc': 'Description & Specification of goods',
            'total_qty': 'Total Qty of goods',
            'unit': 'Unit',
            'rate': 'Rate per unit',
            'discount': 'Discount (in %)',
        }
    
    def clean_total_qty(self):
        qty = self.cleaned_data.get('total_qty')
        if qty is None:
            raise forms.ValidationError("Total Quantity is required.")
        if not isinstance(qty, (int, float)) or qty < 0:
            raise forms.ValidationError("Total Quantity must be a non-negative number.")
        return qty

    def clean_rate(self):
        rate = self.cleaned_data.get('rate')
        if rate is None:
            raise forms.ValidationError("Rate is required.")
        if not isinstance(rate, (int, float)) or rate < 0:
            raise forms.ValidationError("Rate must be a non-negative number.")
        return rate

    def clean_discount(self):
        discount = self.cleaned_data.get('discount')
        if discount is None:
            # Discount can be 0 or empty for ASP.NET. If empty, assume 0.
            # If required, check if it's empty AND not a number.
            return 0.0 # Default to 0 if not provided, or make it required by the field definition
        if not isinstance(discount, (int, float)) or discount < 0:
            raise forms.ValidationError("Discount must be a non-negative number.")
        return discount

# Form for editing existing permanent items in the grid
class QuotationDetailEditForm(forms.ModelForm):
    class Meta:
        model = QuotationDetail
        fields = ['item_desc', 'total_qty', 'unit', 'rate', 'discount']
        widgets = {
            'item_desc': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'total_qty': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.001'}),
            'unit': forms.Select(attrs={'class': 'box3 w-full'}),
            'rate': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'discount': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
        }
    
    def clean_total_qty(self):
        qty = self.cleaned_data.get('total_qty')
        if qty is None:
            raise forms.ValidationError("Total Quantity is required.")
        if not isinstance(qty, (int, float)) or qty < 0:
            raise forms.ValidationError("Total Quantity must be a non-negative number.")
        return qty

    def clean_rate(self):
        rate = self.cleaned_data.get('rate')
        if rate is None:
            raise forms.ValidationError("Rate is required.")
        if not isinstance(rate, (int, float)) or rate < 0:
            raise forms.ValidationError("Rate must be a non-negative number.")
        return rate

    def clean_discount(self):
        discount = self.cleaned_data.get('discount')
        if discount is None:
            return 0.0
        if not isinstance(discount, (int, float)) or discount < 0:
            raise forms.ValidationError("Discount must be a non-negative number.")
        return discount

```

### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), keeping views thin.

**Instructions:**
Our automated conversion focuses on creating highly efficient, compact CBVs (typically 5-15 lines per method). Business logic is delegated to the models. We use HTMX to manage dynamic updates, eliminating full-page reloads and providing a highly responsive user experience. This includes specific views for each tab's content and for handling CRUD operations on the quotation items.

**File:** `sales/views.py`

```python
from django.views.generic import ListView, UpdateView, View, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.shortcuts import get_object_or_404, render, redirect
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from django.db import transaction

from .models import QuotationMaster, QuotationTempItem, QuotationDetail, Customer, Unit, VatTerm, ExciseTerm
from .forms import QuotationMasterForm, QuotationTempItemForm, QuotationDetailEditForm

# Helper to get session/company/finyear (mocked for now, actual implementation would use request.session)
def get_user_context(request):
    # In a real ERP system, these would come from the authenticated user's session
    # For now, we'll use placeholder values or extract from query/session if available
    session_id = request.session.get('username', 'default_user') # ASP.NET used Session["username"]
    comp_id = request.session.get('compid', 1) # ASP.NET used Session["compid"]
    fin_year_id = request.session.get('finyear', 2024) # ASP.NET used Session["finyear"]
    return session_id, comp_id, fin_year_id

class QuotationListView(ListView):
    model = QuotationMaster
    template_name = 'sales/quotationmaster/list.html'
    context_object_name = 'quotations'
    paginate_by = 10 # Example pagination

    def get_queryset(self):
        # Implement filtering/sorting if needed for the list view
        return super().get_queryset().order_by('-id')

class QuotationMasterEditView(UpdateView):
    model = QuotationMaster
    form_class = QuotationMasterForm
    template_name = 'sales/quotationmaster/edit.html'
    context_object_name = 'quotation'

    def get_success_url(self):
        return reverse_lazy('sales:quotation_edit', kwargs={'pk': self.object.pk})

    def get_object(self, queryset=None):
        # Fetch initial quotation master by PK from URL
        pk = self.kwargs.get('pk')
        return get_object_or_404(QuotationMaster, pk=pk)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        quotation_master = self.get_object()

        # Extract customer info from query string / linked customer object
        customer_id_from_url = self.request.GET.get('CustomerId', '')
        customer_id_from_master = quotation_master.customer.customer_id if quotation_master.customer else None
        
        customer_id_to_use = customer_id_from_url if customer_id_from_url else customer_id_from_master
        
        customer_details = None
        if customer_id_to_use:
            customer_details = Customer.objects.filter(customer_id=customer_id_to_use).first()

        context['customer_details'] = customer_details
        context['quotation_no'] = self.request.GET.get('QuotationNo', quotation_master.quotation_no)
        context['enquiry_no'] = self.request.GET.get('EnqId', 'N/A') # ASP.NET showed Enquiry No, not stored in QuotationMaster

        # Initialize forms for the "Goods Details" tab
        context['temp_item_form'] = QuotationTempItemForm()
        
        # Determine the active tab based on HTMX headers or session
        if self.request.headers.get('HX-Request'):
            active_tab_param = self.request.headers.get('HX-Target', 'customer-tab-content').replace('-tab-content', '')
            context['active_tab'] = active_tab_param
        else:
            # Fallback for initial load or non-HTMX
            context['active_tab'] = self.request.session.get('active_quotation_tab', 'customer') 
        
        return context

    def form_valid(self, form):
        # This handles the final 'Update' button click (from Terms & Conditions tab)
        response = super().form_valid(form)
        
        session_id, comp_id, fin_year_id = get_user_context(self.request)
        
        try:
            with transaction.atomic():
                # Update system date/time and session info in the master record
                self.object.sys_date = timezone.localdate()
                self.object.sys_time = timezone.localtime().time()
                self.object.session_id = session_id # Update session_id on master update
                self.object.save()
                
                # Transfer temporary items to permanent details
                transferred_count = self.object.transfer_temp_items(session_id, comp_id, fin_year_id)
                messages.success(self.request, f"Quotation updated successfully. Transferred {transferred_count} new items.")
            
            if self.request.headers.get('HX-Request'):
                # For HTMX, send a 204 No Content response and trigger a full list refresh
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshQuotationList, closeModals' # Assuming a 'closeModals' event for Alpine
                    }
                )
            return response # Standard redirect for non-HTMX
            
        except Exception as e:
            messages.error(self.request, f"Error updating quotation: {e}")
            # If an error occurs, for HTMX, we might want to return an error message
            # or re-render the form with errors. For now, a simple HTTP 500.
            if self.request.headers.get('HX-Request'):
                return HttpResponse(status=500, content=f"Error: {e}")
            return self.form_invalid(form) # Re-render form with errors

    def form_invalid(self, form):
        # For HTMX, if the form is invalid, re-render the form content
        if self.request.headers.get('HX-Request'):
            context = self.get_context_data(form=form)
            # Ensure the correct tab is re-rendered if the form was part of a specific tab
            # This logic needs refinement based on which form/tab caused the invalid submission
            if form == context['form']: # This is the QuotationMasterForm
                return render(self.request, 'sales/quotationmaster/_terms_conditions.html', context)
        return super().form_invalid(form)

# HTMX partial views for each tab's content
class CustomerDetailsPartialView(View):
    def get(self, request, pk):
        quotation_master = get_object_or_404(QuotationMaster, pk=pk)
        customer_id_from_url = request.GET.get('CustomerId', '')
        customer_id_from_master = quotation_master.customer.customer_id if quotation_master.customer else None
        
        customer_id_to_use = customer_id_from_url if customer_id_from_url else customer_id_from_master
        
        customer_details = None
        if customer_id_to_use:
            customer_details = Customer.objects.filter(customer_id=customer_id_to_use).first()

        context = {
            'quotation': quotation_master,
            'customer_details': customer_details,
            'quotation_no': request.GET.get('QuotationNo', quotation_master.quotation_no),
            'enquiry_no': request.GET.get('EnqId', 'N/A'),
        }
        request.session['active_quotation_tab'] = 'customer'
        return render(request, 'sales/quotationmaster/_customer_details.html', context)

class GoodsDetailsPartialView(View):
    def get(self, request, pk):
        quotation_master = get_object_or_404(QuotationMaster, pk=pk)
        session_id, comp_id, fin_year_id = get_user_context(request)
        
        context = {
            'quotation': quotation_master,
            'temp_item_form': QuotationTempItemForm(),
            'temp_items': QuotationTempItem.get_items_for_session(session_id, comp_id, fin_year_id),
            'permanent_items': QuotationDetail.objects.filter(master=quotation_master).order_by('-id')
        }
        request.session['active_quotation_tab'] = 'goods'
        return render(request, 'sales/quotationmaster/_goods_details.html', context)

class TermsConditionsPartialView(UpdateView): # Use UpdateView to handle form submission for this section
    model = QuotationMaster
    form_class = QuotationMasterForm
    template_name = 'sales/quotationmaster/_terms_conditions.html' # This is a partial
    
    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        return get_object_or_404(QuotationMaster, pk=pk)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the main quotation object as 'quotation' for general context if needed
        context['quotation'] = self.get_object() 
        request.session['active_quotation_tab'] = 'terms'
        return context
    
    def form_valid(self, form):
        # This handles the specific form submission for terms & conditions when moving between tabs or for validation
        # The main update logic on BtnUpdate_Click is handled by QuotationMasterEditView's form_valid
        # This form_valid is primarily for partial validation/rendering
        form.save()
        messages.success(self.request, "Terms & Conditions saved temporarily.")
        
        # For HTMX, return a 204 No Content to indicate success without re-rendering,
        # or re-render the partial if there are messages/updates to show within the tab.
        return HttpResponse(
            status=204, 
            headers={
                'HX-Trigger': 'refreshTermsContent' # Custom event to refresh only this section if needed
            }
        )

    def form_invalid(self, form):
        # If invalid, re-render the partial with errors
        context = self.get_context_data(form=form)
        return render(self.request, self.template_name, context)

# CRUD for Temporary Items (GridView1 replacement)
class QuotationTempItemCreateView(View):
    def post(self, request, pk):
        form = QuotationTempItemForm(request.POST)
        session_id, comp_id, fin_year_id = get_user_context(request)
        if form.is_valid():
            temp_item = form.save(commit=False)
            temp_item.session_id = session_id
            temp_item.comp_id = comp_id
            temp_item.fin_year_id = fin_year_id
            temp_item.save()
            messages.success(request, "Item added to temporary list.")
            
            # Re-render the goods details partial with updated data
            quotation_master = get_object_or_404(QuotationMaster, pk=pk)
            context = {
                'quotation': quotation_master,
                'temp_item_form': QuotationTempItemForm(), # Fresh form
                'temp_items': QuotationTempItem.get_items_for_session(session_id, comp_id, fin_year_id),
                'permanent_items': QuotationDetail.objects.filter(master=quotation_master).order_by('-id')
            }
            return render(request, 'sales/quotationmaster/_goods_details.html', context)
        else:
            # If form is invalid, re-render the goods details partial with errors
            quotation_master = get_object_or_404(QuotationMaster, pk=pk)
            context = {
                'quotation': quotation_master,
                'temp_item_form': form, # Form with errors
                'temp_items': QuotationTempItem.get_items_for_session(session_id, comp_id, fin_year_id),
                'permanent_items': QuotationDetail.objects.filter(master=quotation_master).order_by('-id')
            }
            return render(request, 'sales/quotationmaster/_goods_details.html', context, status=400) # Bad Request

class QuotationTempItemUpdateView(UpdateView):
    model = QuotationTempItem
    form_class = QuotationTempItemForm
    template_name = 'sales/quotationmaster/_quotation_temp_item_form.html' # Partial for editing a single item
    
    def get_object(self, queryset=None):
        return get_object_or_404(QuotationTempItem, pk=self.kwargs['item_pk'])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['quotation_pk'] = self.kwargs['pk'] # Pass main quotation PK for correct URL routing
        return context

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Temporary item updated successfully.")
        # Re-render the updated row, or trigger a refresh of the entire table
        # For simplicity, we trigger a refresh of the goods details partial (which reloads the table)
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshGoodsDetails'})
    
    def form_invalid(self, form):
        # Re-render the form with errors
        context = self.get_context_data(form=form)
        return render(self.request, self.template_name, context, status=400)

class QuotationTempItemDeleteView(DeleteView):
    model = QuotationTempItem
    template_name = 'sales/quotationmaster/_confirm_delete_temp_item.html' # Partial for delete confirmation
    
    def get_object(self, queryset=None):
        return get_object_or_404(QuotationTempItem, pk=self.kwargs['item_pk'])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['quotation_pk'] = self.kwargs['pk']
        return context

    def delete(self, request, *args, **kwargs):
        self.get_object().delete()
        messages.success(request, "Temporary item deleted successfully.")
        # Trigger refresh of the goods details partial (which reloads the table)
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshGoodsDetails'})

# CRUD for Permanent Items (GridView2 replacement)
class QuotationDetailUpdateView(UpdateView):
    model = QuotationDetail
    form_class = QuotationDetailEditForm
    template_name = 'sales/quotationmaster/_quotation_detail_item_form.html' # Partial for editing a single item

    def get_object(self, queryset=None):
        return get_object_or_404(QuotationDetail, pk=self.kwargs['item_pk'])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['quotation_pk'] = self.kwargs['pk']
        return context

    def form_valid(self, form):
        form.save()
        messages.success(self.request, "Permanent item updated successfully.")
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshGoodsDetails'})
    
    def form_invalid(self, form):
        context = self.get_context_data(form=form)
        return render(self.request, self.template_name, context, status=400)

class QuotationDetailDeleteView(DeleteView):
    model = QuotationDetail
    template_name = 'sales/quotationmaster/_confirm_delete_detail_item.html' # Partial for delete confirmation

    def get_object(self, queryset=None):
        return get_object_or_404(QuotationDetail, pk=self.kwargs['item_pk'])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['quotation_pk'] = self.kwargs['pk']
        return context

    def delete(self, request, *args, **kwargs):
        self.get_object().delete()
        messages.success(request, "Permanent item deleted successfully.")
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshGoodsDetails'})

```

### 4.4 Templates

**Task:** Create templates for each view, incorporating HTMX, Alpine.js, and DataTables.

**Instructions:**
Our system generates highly modular templates that extend `core/base.html` for consistent layout. HTMX attributes enable dynamic content loading, form submissions, and table refreshes without full page reloads. Alpine.js manages UI state, such as modal visibility for CRUD operations. DataTables are integrated for efficient data presentation and interaction.

**File:** `sales/templates/sales/quotationmaster/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Quotations</h2>
        <!-- No direct 'Add New' button on this page, as it's an 'Edit Details' specific view.
             We assume navigation from a 'Quotation List' page.
             For demo, adding a simple link back to a list view for navigation. -->
        <a href="{% url 'sales:quotation_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Quotations
        </a>
    </div>
    
    <div id="quotationListContainer" 
         hx-trigger="load, refreshQuotationList from:body"
         hx-get="{% url 'sales:quotation_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTables list will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Quotations...</p>
        </div>
    </div>
    
    <!-- Generic Modal Structure -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         @close-modals.window="showModal = false"
         :class="{ 'is-active': showModal }">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterSwap if event.detail.target.id == 'modalContent' add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js event listener for closing modals
    document.addEventListener('closeModals', () => {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
        }
    });

    // Re-initialize DataTables when new content is loaded via HTMX into the list container
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'quotationListContainer') {
            $('#quotationTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance before re-initializing
            });
        }
    });
</script>
{% endblock %}

```

**File:** `sales/templates/sales/quotationmaster/_quotation_table_partial.html` (for list view)

```html
<table id="quotationTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validity</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for quotation in quotations %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ quotation.quotation_no }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ quotation.customer.name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ quotation.due_date|date:"d-m-Y" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ quotation.validity }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                <a href="{% url 'sales:quotation_edit' pk=quotation.pk %}?CustomerId={{ quotation.customer.customer_id }}&QuotationNo={{ quotation.quotation_no }}&EnqId=N/A"
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs mr-2">
                    Edit
                </a>
                <!-- Delete is typically handled via a modal confirmation -->
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs"
                    hx-get="{% url 'sales:quotation_delete_confirm' pk=quotation.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No quotations found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // This script runs when the partial is loaded by HTMX.
    // It ensures DataTables is initialized on the table.
    $(document).ready(function() {
        $('#quotationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "destroy": true, // Important: destroy previous instance if it exists
            "order": [] // Disable initial sorting
        });
    });
</script>
```

**File:** `sales/templates/sales/quotationmaster/edit.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-2 px-4 rounded-t-lg shadow-md mb-4">
        <h1 class="text-xl font-semibold">Customer Quotation - Edit</h1>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <div x-data="{ activeTab: '{{ active_tab }}' }">
            <!-- Tab Headers -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button @click="activeTab = 'customer'"
                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm"
                        :class="activeTab === 'customer' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        hx-get="{% url 'sales:quotation_customer_details_partial' pk=quotation.pk %}?CustomerId={{ customer_details.customer_id|default_if_none:'' }}&QuotationNo={{ quotation_no|default_if_none:'' }}&EnqId={{ enquiry_no|default_if_none:'' }}"
                        hx-target="#tab-content"
                        hx-swap="innerHTML"
                        hx-trigger="click">
                        Customer Details
                    </button>
                    <button @click="activeTab = 'goods'"
                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm"
                        :class="activeTab === 'goods' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        hx-get="{% url 'sales:quotation_goods_details_partial' pk=quotation.pk %}"
                        hx-target="#tab-content"
                        hx-swap="innerHTML"
                        hx-trigger="click">
                        Goods Details
                    </button>
                    <button @click="activeTab = 'terms'"
                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm"
                        :class="activeTab === 'terms' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        hx-get="{% url 'sales:quotation_terms_conditions_partial' pk=quotation.pk %}"
                        hx-target="#tab-content"
                        hx-swap="innerHTML"
                        hx-trigger="click">
                        Terms &amp; Conditions
                    </button>
                </nav>
            </div>

            <!-- Tab Content Area -->
            <div id="tab-content" class="mt-6"
                 hx-get="{% url 'sales:quotation_customer_details_partial' pk=quotation.pk %}?CustomerId={{ customer_details.customer_id|default_if_none:'' }}&QuotationNo={{ quotation_no|default_if_none:'' }}&EnqId={{ enquiry_no|default_if_none:'' }}"
                 hx-trigger="load"
                 hx-swap="innerHTML">
                <!-- Content will be loaded here via HTMX -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading tab content...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generic Modal for form and delete confirmations -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me"
     x-data="{ showModal: false }"
     @close-modals.window="showModal = false"
     :class="{ 'is-active': showModal }">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
         _="on htmx:afterSwap if event.detail.target.id == 'modalContent' add .is-active to #modal">
        <!-- Content loaded via HTMX for edit/delete forms -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js specific initialization if required beyond direct directives
    });

    // Event listener to close modal after HTMX form submission
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) { // HTMX success (no content)
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
            // Dispatch a custom event to notify other parts of the app to refresh lists etc.
            if (event.detail.xhr.getResponseHeader('HX-Trigger')) {
                const triggers = JSON.parse(event.detail.xhr.getResponseHeader('HX-Trigger'));
                if (triggers.refreshGoodsDetails) {
                     htmx.trigger(document.body, 'refreshGoodsDetails');
                }
                if (triggers.refreshQuotationList) {
                    htmx.trigger(document.body, 'refreshQuotationList');
                }
                if (triggers.closeModals) {
                    document.dispatchEvent(new CustomEvent('closeModals'));
                }
            }
        }
    });

    // Re-initialize DataTables when new content is loaded into specific containers
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'temp-items-table-container' || event.detail.target.id === 'permanent-items-table-container') {
            $('.data-table-js').DataTable({
                "pageLength": 5, // Smaller page length for detail grids
                "lengthChange": false, // Disable length change for smaller grids
                "searching": true,
                "info": false,
                "paging": true,
                "destroy": true, // Destroy existing instance before re-initializing
                "order": [] // Disable initial sorting
            });
        }
    });
</script>
{% endblock %}
```

**File:** `sales/templates/sales/quotationmaster/_customer_details.html`

```html
<div class="space-y-4 text-gray-700">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <p class="font-medium">Quotation No:</p>
            <p class="font-bold">{{ quotation_no }}</p>
        </div>
        <div>
            <p class="font-medium">Enquiry No:</p>
            <p class="font-bold">{{ enquiry_no }}</p>
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <p class="font-medium">Name of Customer:</p>
            <p>{{ customer_details.name|default:"N/A" }}</p>
        </div>
        <div>
            <p class="font-medium">Regd. Office Address:</p>
            <p>{{ customer_details.get_full_address|default:"N/A"|linebreaksbr }}</p>
        </div>
    </div>

    <div class="flex justify-end pt-4 space-x-3">
        <button type="button" 
            class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'sales:quotation_goods_details_partial' pk=quotation.pk %}"
            hx-target="#tab-content"
            hx-swap="innerHTML"
            hx-trigger="click"
            _="on click set activeTab to 'goods' in closest('[x-data]').__x.$data">
            Next
        </button>
        <a href="{% url 'sales:quotation_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
</div>
```

**File:** `sales/templates/sales/quotationmaster/_goods_details.html`

```html
<div class="space-y-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Add Goods Details</h3>
    <form hx-post="{% url 'sales:quotation_temp_item_create' pk=quotation.pk %}" hx-swap="outerHTML" hx-target="#tab-content" hx-trigger="submit">
        {% csrf_token %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="col-span-1 md:col-span-2">
                {% include 'sales/_form_field.html' with field=temp_item_form.item_desc %}
            </div>
            <div>
                {% include 'sales/_form_field.html' with field=temp_item_form.total_qty %}
            </div>
            <div>
                {% include 'sales/_form_field.html' with field=temp_item_form.unit %}
            </div>
            <div>
                {% include 'sales/_form_field.html' with field=temp_item_form.rate %}
            </div>
            <div>
                {% include 'sales/_form_field.html' with field=temp_item_form.discount %}
            </div>
        </div>
        <div class="flex justify-end pt-4 space-x-3">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                Submit
            </button>
            <button type="button" 
                class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'sales:quotation_terms_conditions_partial' pk=quotation.pk %}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click"
                _="on click set activeTab to 'terms' in closest('[x-data]').__x.$data">
                Next
            </button>
            <a href="{% url 'sales:quotation_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </a>
        </div>
    </form>

    <h3 class="text-lg font-medium text-gray-900 mt-8 mb-4">Temporary Items (GridView1)</h3>
    <div id="temp-items-table-container"
         hx-trigger="refreshGoodsDetails from:body"
         hx-get="{% url 'sales:quotation_temp_table_partial' pk=quotation.pk %}"
         hx-swap="innerHTML">
        <!-- Temporary items DataTable will be loaded here via HTMX -->
        <div class="text-center py-5">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-500">Loading temporary items...</p>
        </div>
    </div>

    <h3 class="text-lg font-medium text-gray-900 mt-8 mb-4">Permanent Items (GridView2)</h3>
    <div id="permanent-items-table-container"
         hx-trigger="refreshGoodsDetails from:body"
         hx-get="{% url 'sales:quotation_detail_table_partial' pk=quotation.pk %}"
         hx-swap="innerHTML">
        <!-- Permanent items DataTable will be loaded here via HTMX -->
        <div class="text-center py-5">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-500">Loading permanent items...</p>
        </div>
    </div>
</div>
```

**File:** `sales/templates/sales/quotationmaster/_terms_conditions.html`

```html
<form hx-post="{% url 'sales:quotation_terms_conditions_partial' pk=quotation.pk %}" 
      hx-swap="none" 
      hx-trigger="submit">
    {% csrf_token %}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-gray-700">
        <div>
            <label for="{{ form.payment_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Payment Terms</label>
            {{ form.payment_terms }}
            {% if form.payment_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.payment_terms.errors }}</p>{% endif %}
        </div>
        <div class="flex items-center space-x-2">
            <label for="{{ form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700">P & F</label>
            <div class="flex-grow">{{ form.pf }}</div>
            <div class="w-24">{{ form.pf_type }}</div>
            {% if form.pf.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.vat_cst.id_for_label }}" class="block text-sm font-medium text-gray-700">VAT/CST</label>
            {{ form.vat_cst }}
            {% if form.vat_cst.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vat_cst.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.excise.id_for_label }}" class="block text-sm font-medium text-gray-700">Excise / Service Tax</label>
            {{ form.excise }}
            {% if form.excise.errors %}<p class="text-red-500 text-xs mt-1">{{ form.excise.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.octroi.id_for_label }}" class="block text-sm font-medium text-gray-700">Octroi</label>
            <div class="flex items-center space-x-2">
                <div class="flex-grow">{{ form.octroi }}</div>
                <div class="w-24">{{ form.octroi_type }}</div>
            </div>
            {% if form.octroi.errors %}<p class="text-red-500 text-xs mt-1">{{ form.octroi.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.warrenty.id_for_label }}" class="block text-sm font-medium text-gray-700">Warranty</label>
            {{ form.warrenty }}
            {% if form.warrenty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.warrenty.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance</label>
            {{ form.insurance }}
            {% if form.insurance.errors %}<p class="text-red-500 text-xs mt-1">{{ form.insurance.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.transport.id_for_label }}" class="block text-sm font-medium text-gray-700">Mode of Transport</label>
            {{ form.transport }}
            {% if form.transport.errors %}<p class="text-red-500 text-xs mt-1">{{ form.transport.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.note_no.id_for_label }}" class="block text-sm font-medium text-gray-700">R.R./G.C. Note No.</label>
            {{ form.note_no }}
            {% if form.note_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.note_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.registration_no.id_for_label }}" class="block text-sm font-medium text-gray-700">If by motor vehicle, it's registr. no</label>
            {{ form.registration_no }}
            {% if form.registration_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.registration_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.freight.id_for_label }}" class="block text-sm font-medium text-gray-700">Freight</label>
            <div class="flex items-center space-x-2">
                <div class="flex-grow">{{ form.freight }}</div>
                <div class="w-24">{{ form.freight_type }}</div>
            </div>
            {% if form.freight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.freight.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.due_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Due Date</label>
            {{ form.due_date }}
            {% if form.due_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.due_date.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.validity.id_for_label }}" class="block text-sm font-medium text-gray-700">Validity</label>
            {{ form.validity }}
            {% if form.validity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.validity.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.other_charges.id_for_label }}" class="block text-sm font-medium text-gray-700">Other Charges</label>
            <div class="flex items-center space-x-2">
                <div class="flex-grow">{{ form.other_charges }}</div>
                <div class="w-24">{{ form.other_charges_type }}</div>
            </div>
            {% if form.other_charges.errors %}<p class="text-red-500 text-xs mt-1">{{ form.other_charges.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.delivery_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Delivery Terms</label>
            {{ form.delivery_terms }}
            {% if form.delivery_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.delivery_terms.errors }}</p>{% endif %}
        </div>
        <div class="col-span-1 md:col-span-2">
            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
            {{ form.remarks }}
            {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
        </div>
    </div>
    
    <div class="flex justify-end pt-6 space-x-3">
        <button type="submit" 
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'sales:quotation_edit' pk=quotation.pk %}"
            hx-swap="none"
            hx-trigger="click"
            onclick="return confirm('Are you sure you want to update the quotation?');">
            Update
        </button>
        <a href="{% url 'sales:quotation_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
</form>
```

**File:** `sales/templates/sales/quotationmaster/_quotation_temp_table.html`

```html
<table id="quotationTempTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 data-table-js">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for item in temp_items %}
        <tr id="temp-item-row-{{ item.pk }}">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm text-gray-900 max-w-xs overflow-hidden truncate">{{ item.item_desc }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.unit.symbol }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.rate|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.discount|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.total_qty|floatformat:3 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs mr-2"
                    hx-get="{% url 'sales:quotation_temp_item_edit_partial' pk=quotation.pk item_pk=item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs"
                    hx-get="{% url 'sales:quotation_temp_item_delete_confirm_partial' pk=quotation.pk item_pk=item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No temporary items added.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**File:** `sales/templates/sales/quotationmaster/_quotation_temp_item_form.html` (Modal form for temp item edit)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Temporary Quotation Item</h3>
    <form hx-post="{% url 'sales:quotation_temp_item_edit_partial' pk=quotation_pk item_pk=quotationtempitem.pk %}" 
          hx-swap="none" 
          hx-trigger="submit">
        {% csrf_token %}
        <div class="space-y-4">
            {% include 'sales/_form_field.html' with field=form.item_desc %}
            {% include 'sales/_form_field.html' with field=form.total_qty %}
            {% include 'sales/_form_field.html' with field=form.unit %}
            {% include 'sales/_form_field.html' with field=form.rate %}
            {% include 'sales/_form_field.html' with field=form.discount %}
        </div>
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**File:** `sales/templates/sales/quotationmaster/_confirm_delete_temp_item.html` (Modal for temp item delete)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the temporary item: <strong>"{{ quotationtempitem.item_desc }}"</strong>?</p>
    <div class="flex justify-end space-x-4">
        <button type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{% url 'sales:quotation_temp_item_delete_partial' pk=quotation_pk item_pk=quotationtempitem.pk %}"
            hx-swap="none"
            hx-target="body">
            Delete
        </button>
    </div>
</div>
```

**File:** `sales/templates/sales/quotationmaster/_quotation_detail_table.html`

```html
<table id="quotationDetailTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 data-table-js">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for item in permanent_items %}
        <tr id="detail-item-row-{{ item.pk }}">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm text-gray-900 max-w-xs overflow-hidden truncate">{{ item.item_desc }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.unit.symbol }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.rate|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.discount|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.total_qty|floatformat:3 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs mr-2"
                    hx-get="{% url 'sales:quotation_detail_item_edit_partial' pk=quotation.pk item_pk=item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs"
                    hx-get="{% url 'sales:quotation_detail_item_delete_confirm_partial' pk=quotation.pk item_pk=item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No permanent items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**File:** `sales/templates/sales/quotationmaster/_quotation_detail_item_form.html` (Modal form for permanent item edit)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Permanent Quotation Item</h3>
    <form hx-post="{% url 'sales:quotation_detail_item_edit_partial' pk=quotation_pk item_pk=quotationdetail.pk %}" 
          hx-swap="none" 
          hx-trigger="submit">
        {% csrf_token %}
        <div class="space-y-4">
            {% include 'sales/_form_field.html' with field=form.item_desc %}
            {% include 'sales/_form_field.html' with field=form.total_qty %}
            {% include 'sales/_form_field.html' with field=form.unit %}
            {% include 'sales/_form_field.html' with field=form.rate %}
            {% include 'sales/_form_field.html' with field=form.discount %}
        </div>
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**File:** `sales/templates/sales/quotationmaster/_confirm_delete_detail_item.html` (Modal for permanent item delete)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the permanent item: <strong>"{{ quotationdetail.item_desc }}"</strong>?</p>
    <div class="flex justify-end space-x-4">
        <button type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{% url 'sales:quotation_detail_item_delete_partial' pk=quotation_pk item_pk=quotationdetail.pk %}"
            hx-swap="none"
            hx-target="body">
            Delete
        </button>
    </div>
</div>
```

**File:** `sales/templates/sales/_form_field.html` (Reusable form field rendering)

```html
{% comment %}
    This partial renders a single Django form field with label and error messages.
    It applies Tailwind CSS classes for consistent styling.
    Usage: {% include 'sales/_form_field.html' with field=form.my_field %}
{% endcomment %}

<div class="mb-4">
    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
        {{ field.label }}
        {% if field.field.required %}
            <span class="text-red-500">*</span>
        {% endif %}
    </label>
    {{ field }}
    {% if field.help_text %}
        <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
    {% endif %}
    {% for error in field.errors %}
        <p class="text-red-500 text-xs mt-1">{{ error }}</p>
    {% endfor %}
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views, including HTMX-specific endpoints.

**Instructions:**
Our system maps the logical structure of your ASP.NET pages to Django's URL routing. This includes main views, as well as specific endpoints for HTMX to fetch and swap content dynamically. Consistent naming ensures maintainability.

**File:** `sales/urls.py`

```python
from django.urls import path
from .views import (
    QuotationListView,
    QuotationMasterEditView,
    CustomerDetailsPartialView,
    GoodsDetailsPartialView,
    TermsConditionsPartialView,
    QuotationTempItemCreateView,
    QuotationTempItemUpdateView,
    QuotationTempItemDeleteView,
    QuotationDetailUpdateView,
    QuotationDetailDeleteView,
)

app_name = 'sales'

urlpatterns = [
    # Main Quotation List View
    path('quotations/', QuotationListView.as_view(), name='quotation_list'),
    path('quotations/table/', QuotationListView.as_view(), name='quotation_table_partial'), # For HTMX list refresh

    # Main Quotation Edit View
    # Note: pk here refers to QuotationMaster.id
    path('quotations/edit/<int:pk>/', QuotationMasterEditView.as_view(), name='quotation_edit'),

    # HTMX partials for tab content
    path('quotations/edit/<int:pk>/customer-details/', CustomerDetailsPartialView.as_view(), name='quotation_customer_details_partial'),
    path('quotations/edit/<int:pk>/goods-details/', GoodsDetailsPartialView.as_view(), name='quotation_goods_details_partial'),
    path('quotations/edit/<int:pk>/terms-conditions/', TermsConditionsPartialView.as_view(), name='quotation_terms_conditions_partial'),

    # HTMX endpoints for Temporary Items (GridView1 replacement)
    # The 'pk' is the QuotationMaster PK, 'item_pk' is the QuotationTempItem PK
    path('quotations/edit/<int:pk>/temp-items/add/', QuotationTempItemCreateView.as_view(), name='quotation_temp_item_create'),
    path('quotations/edit/<int:pk>/temp-items/<int:item_pk>/edit/', QuotationTempItemUpdateView.as_view(), name='quotation_temp_item_edit_partial'),
    path('quotations/edit/<int:pk>/temp-items/<int:item_pk>/delete/confirm/', QuotationTempItemDeleteView.as_view(), name='quotation_temp_item_delete_confirm_partial'),
    path('quotations/edit/<int:pk>/temp-items/<int:item_pk>/delete/', QuotationTempItemDeleteView.as_view(), name='quotation_temp_item_delete_partial'),
    # Partial for refreshing temp items table (called by HTMX on _goods_details.html)
    path('quotations/edit/<int:pk>/temp-items/table/', GoodsDetailsPartialView.as_view(
        template_name='sales/quotationmaster/_quotation_temp_table.html', 
        context_object_name='temp_items' # Override context object name for this partial
    ), name='quotation_temp_table_partial'),

    # HTMX endpoints for Permanent Items (GridView2 replacement)
    # The 'pk' is the QuotationMaster PK, 'item_pk' is the QuotationDetail PK
    path('quotations/edit/<int:pk>/details-items/<int:item_pk>/edit/', QuotationDetailUpdateView.as_view(), name='quotation_detail_item_edit_partial'),
    path('quotations/edit/<int:pk>/details-items/<int:item_pk>/delete/confirm/', QuotationDetailDeleteView.as_view(), name='quotation_detail_item_delete_confirm_partial'),
    path('quotations/edit/<int:pk>/details-items/<int:item_pk>/delete/', QuotationDetailDeleteView.as_view(), name='quotation_detail_item_delete_partial'),
    # Partial for refreshing permanent items table (called by HTMX on _goods_details.html)
    path('quotations/edit/<int:pk>/details-items/table/', GoodsDetailsPartialView.as_view(
        template_name='sales/quotationmaster/_quotation_detail_table.html',
        context_object_name='permanent_items' # Override context object name for this partial
    ), name='quotation_detail_table_partial'),
]
```
**Note on `GoodsDetailsPartialView` for table partials**: Reusing `GoodsDetailsPartialView` and overriding its `template_name` and `context_object_name` in the URL patterns is a DRY approach to provide the context needed for these specific table partials without creating entirely new views. It implies that `GoodsDetailsPartialView`'s `get_context_data` should populate both `temp_items` and `permanent_items`. The specific template then picks the one it needs.

### 4.6 Tests

**Task:** Write comprehensive tests for models and views to ensure functionality and maintain code quality.

**Instructions:**
Our testing framework generates unit tests for model logic and integration tests for all view interactions, including HTMX requests. This guarantees high test coverage (80%+) and validates the correctness of the migrated application.

**File:** `sales/tests/test_models.py`

```python
from django.test import TestCase
from django.utils import timezone
from datetime import date, time
from sales.models import (
    Country, State, City, Customer, Unit, VatTerm, ExciseTerm,
    QuotationMaster, QuotationDetail, QuotationTempItem, RateType
)

class LookupModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra')
        cls.city = City.objects.create(id=1, name='Mumbai')
        cls.unit = Unit.objects.create(id=1, symbol='Pcs')
        cls.vat_term = VatTerm.objects.create(id=1, terms='18% VAT')
        cls.excise_term = ExciseTerm.objects.create(id=1, terms='5% Excise')

    def test_country_creation(self):
        self.assertEqual(self.country.name, 'India')
        self.assertEqual(str(self.country), 'India')

    def test_unit_creation(self):
        self.assertEqual(self.unit.symbol, 'Pcs')
        self.assertEqual(str(self.unit), 'Pcs')

    def test_vat_term_creation(self):
        self.assertEqual(self.vat_term.terms, '18% VAT')
        self.assertEqual(str(self.vat_term), '18% VAT')

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra')
        cls.city = City.objects.create(id=1, name='Mumbai')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            name='Test Customer Pvt Ltd',
            address='123, Test Street',
            country=cls.country,
            state=cls.state,
            city=cls.city,
            pin_no='400001',
            comp_id=1
        )

    def test_customer_creation(self):
        self.assertEqual(self.customer.customer_id, 'CUST001')
        self.assertEqual(self.customer.name, 'Test Customer Pvt Ltd')
        self.assertEqual(str(self.customer), 'Test Customer Pvt Ltd')

    def test_get_full_address(self):
        expected_address = '123, Test Street, Mumbai, Maharashtra, India, 400001'
        self.assertEqual(self.customer.get_full_address(), expected_address)


class QuotationModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependencies
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra')
        cls.city = City.objects.create(id=1, name='Mumbai')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            name='Test Customer',
            address='Test Address',
            country=cls.country, state=cls.state, city=cls.city, pin_no='123456', comp_id=1
        )
        cls.unit = Unit.objects.create(id=1, symbol='Pcs')
        cls.vat_term = VatTerm.objects.create(id=1, terms='18% VAT')
        cls.excise_term = ExciseTerm.objects.create(id=1, terms='5% Excise')

        # Create QuotationMaster
        cls.quotation_master = QuotationMaster.objects.create(
            id=1,
            customer=cls.customer,
            quotation_no='Q001',
            payment_terms='30 days',
            pf=100.00,
            pf_type=RateType.AMOUNT,
            vat_cst=cls.vat_term,
            excise=cls.excise_term,
            octroi=50.00,
            octroi_type=RateType.AMOUNT,
            warrenty='1 year',
            insurance=20.00,
            transport='Road',
            note_no='NN123',
            registration_no='REG123',
            freight=30.00,
            freight_type=RateType.AMOUNT,
            remarks='Test remarks',
            due_date=date(2024, 12, 31),
            validity='30 days',
            other_charges=15.00,
            other_charges_type=RateType.AMOUNT,
            delivery_terms='FOB',
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024
        )

        # Create QuotationTempItem
        cls.temp_item = QuotationTempItem.objects.create(
            id=1,
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            item_desc='Laptop Charger',
            total_qty=2.0,
            unit=cls.unit,
            rate=500.00,
            discount=10.00
        )
        cls.temp_item_2 = QuotationTempItem.objects.create(
            id=2,
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            item_desc='External Mouse',
            total_qty=1.0,
            unit=cls.unit,
            rate=150.00,
            discount=5.00
        )

    def test_quotation_master_creation(self):
        qm = QuotationMaster.objects.get(id=1)
        self.assertEqual(qm.quotation_no, 'Q001')
        self.assertEqual(qm.customer.name, 'Test Customer')
        self.assertEqual(str(qm), 'Quotation Q001 for Test Customer')

    def test_quotation_temp_item_creation(self):
        ti = QuotationTempItem.objects.get(id=1)
        self.assertEqual(ti.item_desc, 'Laptop Charger')
        self.assertEqual(ti.total_qty, 2.0)
        self.assertEqual(str(ti), 'TEMP: Laptop Charger (2.0 Pcs)')

    def test_get_items_for_session(self):
        items = QuotationTempItem.get_items_for_session('testuser', 1, 2024)
        self.assertEqual(items.count(), 2)
        self.assertIn(self.temp_item, items)
        self.assertIn(self.temp_item_2, items)

    def test_transfer_temp_items(self):
        self.assertEqual(QuotationDetail.objects.count(), 0)
        
        transferred_count = self.quotation_master.transfer_temp_items('testuser', 1, 2024)
        
        self.assertEqual(transferred_count, 2)
        self.assertEqual(QuotationTempItem.objects.count(), 0) # Temp items should be deleted
        self.assertEqual(QuotationDetail.objects.count(), 2) # Permanent items should be created

        detail1 = QuotationDetail.objects.get(master=self.quotation_master, item_desc='Laptop Charger')
        self.assertEqual(detail1.total_qty, 2.0)
        self.assertEqual(detail1.unit, self.unit)
        self.assertEqual(str(detail1), 'Laptop Charger (2.0 Pcs)')

        detail2 = QuotationDetail.objects.get(master=self.quotation_master, item_desc='External Mouse')
        self.assertEqual(detail2.total_qty, 1.0)
        self.assertEqual(detail2.unit, self.unit)

```

**File:** `sales/tests/test_views.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
from sales.models import (
    Country, State, City, Customer, Unit, VatTerm, ExciseTerm,
    QuotationMaster, QuotationTempItem, QuotationDetail, RateType
)

class QuotationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create all necessary lookup and parent data
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra')
        cls.city = City.objects.create(id=1, name='Mumbai')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            name='Test Customer',
            address='Test Address',
            country=cls.country, state=cls.state, city=cls.city, pin_no='123456', comp_id=1
        )
        cls.unit = Unit.objects.create(id=1, symbol='Pcs')
        cls.vat_term = VatTerm.objects.create(id=1, terms='18% VAT')
        cls.excise_term = ExciseTerm.objects.create(id=1, terms='5% Excise')

        # Create a QuotationMaster record to be edited
        cls.quotation_master = QuotationMaster.objects.create(
            id=100, # Use a distinct ID for testing
            customer=cls.customer,
            quotation_no='QTEST001',
            payment_terms='30 days',
            pf=100.00, pf_type=RateType.AMOUNT,
            vat_cst=cls.vat_term, excise=cls.excise_term,
            octroi=50.00, octroi_type=RateType.AMOUNT,
            warrenty='1 year', insurance=20.00, transport='Road',
            note_no='NN123', registration_no='REG123',
            freight=30.00, freight_type=RateType.AMOUNT,
            remarks='Initial remarks',
            due_date=date(2024, 12, 31),
            validity='30 days',
            other_charges=15.00, other_charges_type=RateType.AMOUNT,
            delivery_terms='FOB',
            sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            session_id='testuser', comp_id=1, fin_year_id=2024
        )

        # Create initial permanent items
        QuotationDetail.objects.create(
            id=1, master=cls.quotation_master, item_desc='Existing Item 1',
            total_qty=5.0, unit=cls.unit, rate=100.0, discount=5.0,
            session_id='testuser', comp_id=1, fin_year_id=2024
        )
        QuotationDetail.objects.create(
            id=2, master=cls.quotation_master, item_desc='Existing Item 2',
            total_qty=10.0, unit=cls.unit, rate=50.0, discount=10.0,
            session_id='testuser', comp_id=1, fin_year_id=2024
        )

    def setUp(self):
        self.client = Client()
        # Ensure session data is set for views relying on it
        session = self.client.session
        session['username'] = 'testuser'
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()

    def test_quotation_list_view(self):
        response = self.client.get(reverse('sales:quotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotationmaster/list.html')
        self.assertContains(response, 'Customer Quotations')

    def test_quotation_master_edit_view_get(self):
        url = reverse('sales:quotation_edit', kwargs={'pk': self.quotation_master.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotationmaster/edit.html')
        self.assertContains(response, self.quotation_master.quotation_no)
        self.assertContains(response, self.customer.name)
        self.assertContains(response, 'Loading tab content...') # Initial HTMX load

    def test_customer_details_partial_view(self):
        url = reverse('sales:quotation_customer_details_partial', kwargs={'pk': self.quotation_master.pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotationmaster/_customer_details.html')
        self.assertContains(response, self.customer.name)
        self.assertContains(response, self.quotation_master.quotation_no)

    def test_goods_details_partial_view(self):
        url = reverse('sales:quotation_goods_details_partial', kwargs={'pk': self.quotation_master.pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotationmaster/_goods_details.html')
        self.assertContains(response, 'Add Goods Details')
        self.assertContains(response, 'Existing Item 1') # Should show permanent items

    def test_terms_conditions_partial_view_get(self):
        url = reverse('sales:quotation_terms_conditions_partial', kwargs={'pk': self.quotation_master.pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotationmaster/_terms_conditions.html')
        self.assertContains(response, 'Initial remarks') # Check pre-filled data

    def test_quotation_temp_item_create_view(self):
        url = reverse('sales:quotation_temp_item_create', kwargs={'pk': self.quotation_master.pk})
        data = {
            'item_desc': 'New Temp Item',
            'total_qty': 3.5,
            'unit': self.unit.pk,
            'rate': 75.0,
            'discount': 2.5
        }
        initial_count = QuotationTempItem.objects.count()
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX swaps content
        self.assertEqual(QuotationTempItem.objects.count(), initial_count + 1)
        self.assertContains(response, 'New Temp Item') # Check if new item is rendered in the partial

    def test_quotation_temp_item_create_view_invalid(self):
        url = reverse('sales:quotation_temp_item_create', kwargs={'pk': self.quotation_master.pk})
        data = {
            'item_desc': '', # Invalid: missing description
            'total_qty': 3.5,
            'unit': self.unit.pk,
            'rate': 75.0,
            'discount': 2.5
        }
        initial_count = QuotationTempItem.objects.count()
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad request due to validation error
        self.assertEqual(QuotationTempItem.objects.count(), initial_count)
        self.assertContains(response, 'This field is required.')

    def test_quotation_temp_item_update_view(self):
        temp_item_to_update = QuotationTempItem.objects.create(
            id=3, # Use a unique ID for this temp item
            session_id='testuser', comp_id=1, fin_year_id=2024,
            item_desc='Old Temp Item', total_qty=1.0, unit=self.unit, rate=10.0, discount=1.0
        )
        url = reverse('sales:quotation_temp_item_edit_partial', kwargs={'pk': self.quotation_master.pk, 'item_pk': temp_item_to_update.pk})
        data = {
            'item_desc': 'Updated Temp Item',
            'total_qty': 2.0,
            'unit': self.unit.pk,
            'rate': 20.0,
            'discount': 2.0
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content, successful HTMX update
        temp_item_to_update.refresh_from_db()
        self.assertEqual(temp_item_to_update.item_desc, 'Updated Temp Item')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGoodsDetails', response.headers['HX-Trigger'])

    def test_quotation_temp_item_delete_view(self):
        temp_item_to_delete = QuotationTempItem.objects.create(
            id=4,
            session_id='testuser', comp_id=1, fin_year_id=2024,
            item_desc='Item to be deleted', total_qty=1.0, unit=self.unit, rate=10.0, discount=1.0
        )
        url = reverse('sales:quotation_temp_item_delete_partial', kwargs={'pk': self.quotation_master.pk, 'item_pk': temp_item_to_delete.pk})
        initial_count = QuotationTempItem.objects.count()
        response = self.client.delete(url, HTTP_HX_REQUEST='true') # DELETE request
        self.assertEqual(response.status_code, 204)
        self.assertEqual(QuotationTempItem.objects.count(), initial_count - 1)
        self.assertFalse(QuotationTempItem.objects.filter(pk=temp_item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGoodsDetails', response.headers['HX-Trigger'])

    def test_quotation_master_update_view_post_success(self):
        url = reverse('sales:quotation_edit', kwargs={'pk': self.quotation_master.pk})
        
        # Add a temporary item before updating to check transfer logic
        QuotationTempItem.objects.create(
            id=5,
            session_id='testuser', comp_id=1, fin_year_id=2024,
            item_desc='Item to be transferred', total_qty=1.5, unit=self.unit, rate=80.0, discount=0.0
        )
        
        data = {
            'payment_terms': '60 days net',
            'pf': 120.00, 'pf_type': RateType.AMOUNT,
            'vat_cst': self.vat_term.pk, 'excise': self.excise_term.pk,
            'octroi': 60.00, 'octroi_type': RateType.AMOUNT,
            'warrenty': '2 years', 'insurance': 25.00, 'transport': 'Air',
            'note_no': 'NN456', 'registration_no': 'REG456',
            'freight': 40.00, 'freight_type': RateType.AMOUNT,
            'remarks': 'Updated remarks',
            'due_date': '2025-01-15', # Changed format to YYYY-MM-DD for form input
            'validity': '45 days',
            'other_charges': 20.00, 'other_charges_type': RateType.AMOUNT,
            'delivery_terms': 'DDP',
        }
        
        initial_temp_count = QuotationTempItem.objects.count()
        initial_detail_count = QuotationDetail.objects.count()

        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        
        self.quotation_master.refresh_from_db()
        self.assertEqual(self.quotation_master.payment_terms, '60 days net')
        self.assertEqual(self.quotation_master.remarks, 'Updated remarks')
        
        self.assertEqual(QuotationTempItem.objects.count(), 0) # Temp items should be moved
        self.assertEqual(QuotationDetail.objects.count(), initial_detail_count + initial_temp_count) # Should have new permanent items

        self.assertTrue(QuotationDetail.objects.filter(master=self.quotation_master, item_desc='Item to be transferred').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshQuotationList', response.headers['HX-Trigger'])

    def test_quotation_master_update_view_post_invalid(self):
        url = reverse('sales:quotation_edit', kwargs={'pk': self.quotation_master.pk})
        data = {
            'payment_terms': 'Test Terms',
            'pf': 'invalid', # Invalid data
            'pf_type': RateType.AMOUNT,
            'vat_cst': self.vat_term.pk, 'excise': self.excise_term.pk,
            'octroi': 50.00, 'octroi_type': RateType.AMOUNT,
            'warrenty': '1 year', 'insurance': 20.00, 'transport': 'Road',
            'note_no': 'NN123', 'registration_no': 'REG123',
            'freight': 30.00, 'freight_type': RateType.AMOUNT,
            'remarks': 'Initial remarks',
            'due_date': '2024-12-31',
            'validity': '30 days',
            'other_charges': 15.00, 'other_charges_type': RateType.AMOUNT,
            'delivery_terms': 'FOB',
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad request
        self.assertContains(response, 'Enter a number.')

    def test_quotation_detail_item_update_view(self):
        detail_item_to_update = QuotationDetail.objects.get(item_desc='Existing Item 1')
        url = reverse('sales:quotation_detail_item_edit_partial', kwargs={'pk': self.quotation_master.pk, 'item_pk': detail_item_to_update.pk})
        data = {
            'item_desc': 'Updated Existing Item 1',
            'total_qty': 6.0,
            'unit': self.unit.pk,
            'rate': 110.0,
            'discount': 6.0
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        detail_item_to_update.refresh_from_db()
        self.assertEqual(detail_item_to_update.item_desc, 'Updated Existing Item 1')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGoodsDetails', response.headers['HX-Trigger'])

    def test_quotation_detail_item_delete_view(self):
        detail_item_to_delete = QuotationDetail.objects.get(item_desc='Existing Item 2')
        url = reverse('sales:quotation_detail_item_delete_partial', kwargs={'pk': self.quotation_master.pk, 'item_pk': detail_item_to_delete.pk})
        initial_count = QuotationDetail.objects.count()
        response = self.client.delete(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(QuotationDetail.objects.count(), initial_count - 1)
        self.assertFalse(QuotationDetail.objects.filter(pk=detail_item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGoodsDetails', response.headers['HX-Trigger'])

```

---

## Step 5: HTMX and Alpine.js Integration

Our AI-powered modernization process seamlessly integrates HTMX and Alpine.js for a highly dynamic and responsive user experience, moving beyond traditional full-page reloads.

**Key Integration Points:**

-   **Tabbed Interface:** The `AjaxControlToolkit:TabContainer` is replaced by HTML buttons and a `div` with `hx-get` attributes. When a tab button is clicked, HTMX fetches the content of the corresponding partial template (e.g., `_customer_details.html`, `_goods_details.html`) and swaps it into the main `#tab-content` area. Alpine.js manages the visual `activeTab` state in the navigation.
-   **Dynamic Forms & Modals:**
    -   "Edit" and "Delete" actions on DataTables trigger HTMX `hx-get` requests to load specific partial forms (e.g., `_quotation_temp_item_form.html`, `_confirm_delete_temp_item.html`) into a central modal (`#modalContent`).
    -   Alpine.js controls the visibility of the `#modal` based on the HTMX `htmx:afterSwap` event.
    -   Form submissions within these modals use `hx-post` or `hx-delete` back to the server.
-   **Real-time Table Updates (DataTables):**
    -   After any CUD operation (Create, Update, Delete) on quotation items (both temporary and permanent), the server responds with an `HX-Trigger` header, which dispatches a custom event (e.g., `refreshGoodsDetails`).
    -   This event triggers a `hx-get` on the table containers (`#temp-items-table-container`, `#permanent-items-table-container`), causing HTMX to refetch and re-render the latest data.
    -   JavaScript within the partial templates ensures DataTables is re-initialized on the newly loaded content, maintaining all search, sort, and pagination capabilities.
-   **No Full Page Reloads:** All interactions, including tab switching, form submissions, and grid operations, are handled by HTMX, ensuring a smooth, single-page application feel without the complexity of a full JavaScript framework.
-   **Minimal JavaScript:** Alpine.js is used only for lightweight UI state management (like showing/hiding modals or active tab indication), adhering to the principle of "no additional JavaScript beyond HTMX and Alpine."

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET `Quotation_Edit_Details` module to Django. By leveraging AI-assisted automation, we can systematically convert your existing database schema, business logic, and UI interactions into a modern Django application. This approach ensures:

-   **Business Continuity:** Gradual migration with a focus on preserving core functionality.
-   **Enhanced User Experience:** Faster, more responsive interfaces with HTMX and Alpine.js.
-   **Improved Maintainability:** Clean, modular Django code adhering to best practices.
-   **Future Scalability:** A robust foundation for future enhancements and integrations.
-   **Reduced Manual Effort:** Automated code generation and clear instructions for non-technical stakeholders to oversee.

Remember to replace placeholder values in the `sales/models.py` and `sales/views.py` (e.g., `session_id`, `comp_id`, `fin_year_id` retrieval) with your actual session management logic. The `pk` for `QuotationMaster` in the URL patterns is assumed to be the `Id` field in the database, which is typically an auto-incrementing integer. Ensure your Django settings include the `sales` app and static files are configured for Tailwind CSS, HTMX, Alpine.js, and DataTables CDNs.