## ASP.NET to Django Conversion Script: Customer Enquiry Print Details

This modernization plan outlines the transformation of the legacy ASP.NET Customer Enquiry Print Details page into a robust, modern Django application. Our approach prioritizes automated conversion and leverages contemporary web technologies like Django 5.0+, HTMX, and Alpine.js to deliver a highly performant and user-friendly solution.

### Business Value Proposition:

Migrating from ASP.NET to Django brings significant benefits:

1.  **Reduced Technical Debt:** Moves away from outdated Crystal Reports and ASP.NET Web Forms, streamlining maintenance and enabling future enhancements.
2.  **Enhanced Performance:** Django's efficient ORM and server-side rendering, combined with HTMX for partial page updates, deliver a snappier user experience.
3.  **Improved Scalability:** Django's architecture is designed for scalability, allowing the application to handle more users and data as your business grows.
4.  **Cost Efficiency:** Open-source technologies like Django and Python reduce licensing costs and provide access to a vast, active developer community.
5.  **Modern User Experience:** Integration with HTMX and Alpine.js provides dynamic, interactive elements without the complexity of traditional JavaScript frameworks, leading to a smoother and more intuitive user interface.
6.  **Simplified Development & Maintenance:** Adherence to "Fat Model, Thin View" and clear separation of concerns makes the codebase easier to understand, develop, and maintain, reducing future development costs.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several database tables to retrieve customer enquiry details and associated location information.

*   **Primary Data Table:** `SD_Cust_Enquiry_Master`
    *   **Columns Identified:** `EnqId` (likely primary key), `CompId` (Company ID), `SysDate` (System Date), `RegdCity`, `RegdState`, `RegdCountry` (IDs for registered address), `WorkCity`, `WorkState`, `WorkCountry` (IDs for work address), `MaterialDelCity`, `MaterialDelState`, `MaterialDelCountry` (IDs for material delivery address), `CustomerId`.
    *   **Inferred Relationships:** Multiple foreign key relationships to `tblCity`, `tblState`, `tblCountry` via their respective ID columns.

*   **Lookup Tables:**
    *   `tblCity`:
        *   **Columns Identified:** `CityId` (primary key), `CityName`.
    *   `tblState`:
        *   **Columns Identified:** `SId` (primary key), `StateName`.
    *   `tblCountry`:
        *   **Columns Identified:** `CId` (primary key), `CountryName`.

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET page is primarily a **report generation and display** page, not a standard CRUD (Create, Read, Update, Delete) interface for the `Customer Enquiry` entity itself.

*   **Read/Retrieve:** It fetches a single `CustomerEnquiry` record using `EnqId` and `CompId` from the URL query string.
*   **Data Enrichment:** It then performs multiple lookups to resolve city, state, and country names from their respective ID columns stored in the `SD_Cust_Enquiry_Master` table, using `tblCity`, `tblState`, and `tblCountry`.
*   **Report Generation:** It loads a `CrystalReport` (`CustEnquiry.rpt`), sets the fetched data as its source, and passes resolved city/state/country names, company name, and address as parameters.
*   **Display:** It displays the generated report using `CrystalReportViewer`.
*   **Navigation:** A "Cancel" button redirects the user to `CustEnquiry_Print.aspx`.

While this specific page is for reporting, a complete modernization plan includes the underlying CRUD capabilities for the `Customer Enquiry` module, which will be covered in Step 4.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The ASP.NET UI is minimal and focused on displaying a report.

*   **`CrystalReportViewer`:** This is the central component for displaying the report. In Django, this functionality will be replaced by server-side report generation (e.g., HTML rendering or PDF generation) and display within a standard web page, or served as a PDF.
*   **`Panel`:** Used for grouping and controlling scrollbars for the report viewer. This will be replaced by standard HTML `div` elements with Tailwind CSS for layout and styling.
*   **`Button` (Cancel):** A simple navigation button. This will be a standard HTML `<a>` tag or `button` that redirects to the appropriate Django URL.
*   **Static HTML/CSS:** Basic table layout, header image (`hdbg.JPG`), and styling from `StyleSheet.css`. These will be translated to modern HTML with Tailwind CSS and image assets.

### Step 4: Generate Django Code

We will create a new Django application, for example, `sales_distribution`, to house the modernized code.

#### 4.1 Models (`sales_distribution/models.py`)

**Task:** Create Django models based on the identified database schema. These models will encapsulate data retrieval and business logic, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone

# Assuming these lookup tables exist and are managed elsewhere
# or need to be defined if not already present.
# For this migration, we'll define them simply.

class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class State(models.Model):
    state_id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class Country(models.Model):
    country_id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

# Assuming a Company model or similar utility for CompId
# For demonstration, we'll define a placeholder Company model.
class Company(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Inferred table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
    
    def __str__(self):
        return self.company_name

class CustomerEnquiry(models.Model):
    enq_id = models.IntegerField(db_column='EnqId', primary_key=True)
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='enquiries')
    customer_id = models.IntegerField(db_column='CustomerId') # Assuming this exists
    sys_date = models.DateTimeField(db_column='SysDate')
    
    # Registered Address
    regd_city_id = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='RegdCity', related_name='regd_enquiries')
    regd_state_id = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='RegdState', related_name='regd_enquiries')
    regd_country_id = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='RegdCountry', related_name='regd_enquiries')

    # Work Address
    work_city_id = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='WorkCity', related_name='work_enquiries')
    work_state_id = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='WorkState', related_name='work_enquiries')
    work_country_id = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='WorkCountry', related_name='work_enquiries')

    # Material Delivery Address
    material_del_city_id = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='MaterialDelCity', related_name='material_del_enquiries')
    material_del_state_id = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='MaterialDelState', related_name='material_del_enquiries')
    material_del_country_id = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='MaterialDelCountry', related_name='material_del_enquiries')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Enquiry_Master'
        verbose_name = 'Customer Enquiry'
        verbose_name_plural = 'Customer Enquiries'

    def __str__(self):
        return f"Enquiry {self.enq_id} for Customer {self.customer_id}"

    # Business logic methods (Fat Model approach)

    @property
    def formatted_sys_date(self):
        """Returns the system date in DD/MM/YYYY format."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

    @property
    def registered_address(self):
        """Returns a dictionary of registered address components."""
        return {
            'city': self.regd_city_id.city_name if self.regd_city_id else '',
            'state': self.regd_state_id.state_name if self.regd_state_id else '',
            'country': self.regd_country_id.country_name if self.regd_country_id else '',
        }

    @property
    def work_address(self):
        """Returns a dictionary of work address components."""
        return {
            'city': self.work_city_id.city_name if self.work_city_id else '',
            'state': self.work_state_id.state_name if self.work_state_id else '',
            'country': self.work_country_id.country_name if self.work_country_id else '',
        }

    @property
    def material_delivery_address(self):
        """Returns a dictionary of material delivery address components."""
        return {
            'city': self.material_del_city_id.city_name if self.material_del_city_id else '',
            'state': self.material_del_state_id.state_name if self.material_del_state_id else '',
            'country': self.material_del_country_id.country_name if self.material_del_country_id else '',
        }

    @classmethod
    def get_enquiry_details_for_report(cls, enq_id, comp_id):
        """
        Retrieves comprehensive details for a specific customer enquiry report.
        This method replaces the multiple SQL selects and parameter settings in the ASP.NET code.
        """
        try:
            enquiry = cls.objects.select_related(
                'comp_id',
                'regd_city_id', 'regd_state_id', 'regd_country_id',
                'work_city_id', 'work_state_id', 'work_country_id',
                'material_del_city_id', 'material_del_state_id', 'material_del_country_id'
            ).get(enq_id=enq_id, comp_id=comp_id)

            return {
                'enquiry': enquiry,
                'company_name': enquiry.comp_id.company_name if enquiry.comp_id else 'N/A',
                'company_address': enquiry.comp_id.address if enquiry.comp_id else 'N/A',
                'reg_date': enquiry.formatted_sys_date,
                'reg_address': enquiry.registered_address,
                'work_address': enquiry.work_address,
                'del_address': enquiry.material_delivery_address,
            }
        except cls.DoesNotExist:
            return None
        except Exception as e:
            # Log the exception for debugging
            print(f"Error fetching enquiry details: {e}")
            return None

```

#### 4.2 Forms (`sales_distribution/forms.py`)

**Task:** Define a Django ModelForm for `CustomerEnquiry`. While the original page was for printing, a modern application would likely have CRUD for this entity.

```python
from django import forms
from .models import CustomerEnquiry, City, State, Country

class CustomerEnquiryForm(forms.ModelForm):
    # Using ModelChoiceField for foreign key relationships to show dropdowns
    regd_city_id = forms.ModelChoiceField(
        queryset=City.objects.all(),
        empty_label="Select City",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    regd_state_id = forms.ModelChoiceField(
        queryset=State.objects.all(),
        empty_label="Select State",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    regd_country_id = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    # ... (similar fields for work_city_id, work_state_id, etc.)

    class Meta:
        model = CustomerEnquiry
        # Exclude comp_id as it might be set automatically based on user's context
        # Exclude primary key 'enq_id' for creation (auto-incremented or system generated)
        fields = [
            'customer_id', 'sys_date',
            'regd_city_id', 'regd_state_id', 'regd_country_id',
            'work_city_id', 'work_state_id', 'work_country_id',
            'material_del_city_id', 'material_del_state_id', 'material_del_country_id',
        ]
        widgets = {
            'customer_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            # Add widgets for other address fields. For brevity, omitting redundant widget definitions here.
            # Example for work_city_id, etc. would follow the pattern of regd_city_id.
            # work_city_id = forms.ModelChoiceField(queryset=City.objects.all(), empty_label="Select City", widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}))
            # ... and so on for all 9 FK fields
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamic queryset for foreign key fields if needed, or pre-filter
        self.fields['work_city_id'].queryset = City.objects.all()
        self.fields['work_state_id'].queryset = State.objects.all()
        self.fields['work_country_id'].queryset = Country.objects.all()
        self.fields['material_del_city_id'].queryset = City.objects.all()
        self.fields['material_del_state_id'].queryset = State.objects.all()
        self.fields['material_del_country_id'].queryset = Country.objects.all()


    def clean(self):
        cleaned_data = super().clean()
        # Add custom validation logic here if needed
        return cleaned_data

```

#### 4.3 Views (`sales_distribution/views.py`)

**Task:** Implement the report view and generic CRUD operations using Class-Based Views (CBVs), adhering to the "Thin View" principle.

```python
from django.views.generic import DetailView, ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import CustomerEnquiry, Company # Assuming Company model exists or can be dynamically retrieved
from .forms import CustomerEnquiryForm

# --- Report View (direct replacement for ASP.NET page functionality) ---
class CustomerEnquiryReportView(DetailView):
    model = CustomerEnquiry
    template_name = 'sales_distribution/customer_enquiry/report.html'
    context_object_name = 'report_data'
    pk_url_kwarg = 'enq_id' # Expect 'enq_id' as primary key for fetching

    def get_object(self, queryset=None):
        """
        Retrieves the CustomerEnquiry object and related data for the report.
        Simulates the ASP.NET page's data fetching based on query string.
        """
        enq_id = self.kwargs.get(self.pk_url_kwarg)
        # In a real application, comp_id would come from session or authenticated user
        # For this example, let's assume a hardcoded or default company ID or a query param.
        # You would integrate with your authentication/company selection system here.
        # e.g., current_comp_id = self.request.session.get('compid') or self.request.user.company_id
        current_comp_id = self.request.GET.get('comp_id', 1) # Example: default to 1, or get from session/user

        report_details = CustomerEnquiry.get_enquiry_details_for_report(enq_id, current_comp_id)
        if report_details is None:
            raise Http404("Customer Enquiry not found or inaccessible for this company.")
        return report_details

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 'report_data' is already set by get_object
        return context

# --- CRUD Views for Customer Enquiry (for a comprehensive module) ---

class CustomerEnquiryListView(ListView):
    model = CustomerEnquiry
    template_name = 'sales_distribution/customer_enquiry/list.html'
    context_object_name = 'customer_enquiries'

    def get_queryset(self):
        # In a real app, you might filter by company_id from session/user
        # return CustomerEnquiry.objects.filter(comp_id=self.request.session.get('compid', 1))
        return CustomerEnquiry.objects.all()

class CustomerEnquiryTablePartialView(CustomerEnquiryListView):
    """
    Renders only the table rows for HTMX updates.
    """
    template_name = 'sales_distribution/customer_enquiry/_customer_enquiry_table.html'

class CustomerEnquiryCreateView(CreateView):
    model = CustomerEnquiry
    form_class = CustomerEnquiryForm
    template_name = 'sales_distribution/customer_enquiry/_customer_enquiry_form.html'
    success_url = reverse_lazy('customer_enquiry_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Set comp_id based on current user/session context
        # For demonstration, assuming a default company or fetching from user's session
        # comp_id = self.request.session.get('compid', 1) # Example
        # form.instance.comp_id = Company.objects.get(pk=comp_id) # Assuming Company model exists
        # form.instance.sys_date = timezone.now() # Auto-set system date if not in form

        response = super().form_valid(form)
        messages.success(self.request, 'Customer Enquiry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Add'
        return context


class CustomerEnquiryUpdateView(UpdateView):
    model = CustomerEnquiry
    form_class = CustomerEnquiryForm
    template_name = 'sales_distribution/customer_enquiry/_customer_enquiry_form.html'
    success_url = reverse_lazy('customer_enquiry_list') # Fallback if not HTMX
    pk_url_kwarg = 'enq_id' # Match URL pattern for primary key

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Enquiry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Edit'
        return context

class CustomerEnquiryDeleteView(DeleteView):
    model = CustomerEnquiry
    template_name = 'sales_distribution/customer_enquiry/_customer_enquiry_confirm_delete.html'
    success_url = reverse_lazy('customer_enquiry_list') # Fallback if not HTMX
    pk_url_kwarg = 'enq_id' # Match URL pattern for primary key

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Enquiry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList'
                }
            )
        return response

```

#### 4.4 Templates (`sales_distribution/templates/sales_distribution/customer_enquiry/`)

**Task:** Create templates for the report view and the standard CRUD operations, utilizing HTMX for dynamic interactions and DataTables for list presentation.

**1. Report Template (`report.html`)**
This template will display the customer enquiry details as a printable report. It's the direct replacement for the Crystal Report display.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl bg-white shadow-lg rounded-lg">
    <div class="flex justify-between items-center mb-6 border-b pb-4">
        <h1 class="text-3xl font-bold text-gray-800">Customer Enquiry - Print</h1>
        <a href="{% url 'customer_enquiry_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
            Cancel
        </a>
    </div>

    {% if report_data %}
    <div class="space-y-6 text-gray-700">
        <!-- Company Information -->
        <div class="border-b pb-4">
            <h2 class="text-xl font-semibold mb-2">Company Details</h2>
            <p><strong>Company Name:</strong> {{ report_data.company_name }}</p>
            <p><strong>Company Address:</strong> {{ report_data.company_address }}</p>
        </div>

        <!-- Enquiry Header -->
        <div class="border-b pb-4">
            <h2 class="text-xl font-semibold mb-2">Enquiry Details (Enquiry ID: {{ report_data.enquiry.enq_id }})</h2>
            <p><strong>Customer ID:</strong> {{ report_data.enquiry.customer_id }}</p>
            <p><strong>Enquiry Date:</strong> {{ report_data.enquiry.formatted_sys_date }}</p>
        </div>

        <!-- Addresses -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <h3 class="text-lg font-medium mb-2">Registered Address</h3>
                <p><strong>City:</strong> {{ report_data.reg_address.city }}</p>
                <p><strong>State:</strong> {{ report_data.reg_address.state }}</p>
                <p><strong>Country:</strong> {{ report_data.reg_address.country }}</p>
            </div>
            <div>
                <h3 class="text-lg font-medium mb-2">Work Address</h3>
                <p><strong>City:</strong> {{ report_data.work_address.city }}</p>
                <p><strong>State:</strong> {{ report_data.work_address.state }}</p>
                <p><strong>Country:</strong> {{ report_data.work_address.country }}</p>
            </div>
            <div>
                <h3 class="text-lg font-medium mb-2">Material Delivery Address</h3>
                <p><strong>City:</strong> {{ report_data.del_address.city }}</p>
                <p><strong>State:</strong> {{ report_data.del_address.state }}</p>
                <p><strong>Country:</strong> {{ report_data.del_address.country }}</p>
            </div>
        </div>

        <!-- Add more enquiry details here as per your Crystal Report layout -->
        <div class="mt-6 pt-4 border-t text-sm text-gray-500">
            <p>This report is generated automatically by the system.</p>
        </div>
    </div>
    {% else %}
    <div class="text-center py-10">
        <p class="text-red-500 text-lg">Report data could not be loaded. Please check the Enquiry ID and Company ID.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for print controls or other UI elements
    });
</script>
{% endblock %}

```

**2. List Template (`list.html`)**
This is the main page for listing all customer enquiries, utilizing DataTables and HTMX for dynamic interactions.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customer Enquiries</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
            hx-get="{% url 'customer_enquiry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Enquiry
        </button>
    </div>

    <div id="customer_enquiryTable-container"
         hx-trigger="load, refreshCustomerEnquiryList from:body"
         hx-get="{% url 'customer_enquiry_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading customer enquiries...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit) and Delete Confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed, e.g., for general modal state management
    });
</script>
{% endblock %}

```

**3. Table Partial Template (`_customer_enquiry_table.html`)**
This partial renders only the table for DataTables, loaded via HTMX.

```html
<div class="overflow-x-auto">
    <table id="customer_enquiryTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry ID</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer ID</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered City</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for enquiry in customer_enquiries %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.enq_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.customer_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.regd_city_id.city_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 transition duration-150 ease-in-out text-sm"
                        hx-get="{% url 'customer_enquiry_edit' enquiry.enq_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded mr-2 transition duration-150 ease-in-out text-sm"
                        hx-get="{% url 'customer_enquiry_delete' enquiry.enq_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                    <a href="{% url 'customer_enquiry_report' enquiry.enq_id %}" target="_blank"
                       class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded transition duration-150 ease-in-out text-sm">
                        Print Report
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No customer enquiries found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Initialize DataTables after content is loaded via HTMX
$(document).ready(function() {
    $('#customer_enquiryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>

```

**4. Form Partial Template (`_customer_enquiry_form.html`)**
Used for both creation and update, loaded into a modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ action }} Customer Enquiry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="{{ form.customer_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer ID</label>
                {{ form.customer_id }}
                {% if form.customer_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.customer_id.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.sys_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Enquiry Date</label>
                {{ form.sys_date }}
                {% if form.sys_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sys_date.errors }}</p>{% endif %}
            </div>

            <!-- Registered Address Fields -->
            <div class="md:col-span-2">
                <h4 class="text-md font-medium text-gray-800 mb-2 mt-4 border-t pt-4">Registered Address</h4>
            </div>
            <div class="mb-4">
                <label for="{{ form.regd_city_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Registered City</label>
                {{ form.regd_city_id }}
                {% if form.regd_city_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_city_id.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.regd_state_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Registered State</label>
                {{ form.regd_state_id }}
                {% if form.regd_state_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_state_id.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.regd_country_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Registered Country</label>
                {{ form.regd_country_id }}
                {% if form.regd_country_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_country_id.errors }}</p>{% endif %}
            </div>

            <!-- Work Address Fields -->
            <div class="md:col-span-2">
                <h4 class="text-md font-medium text-gray-800 mb-2 mt-4 border-t pt-4">Work Address</h4>
            </div>
            <div class="mb-4">
                <label for="{{ form.work_city_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Work City</label>
                {{ form.work_city_id }}
                {% if form.work_city_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_city_id.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.work_state_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Work State</label>
                {{ form.work_state_id }}
                {% if form.work_state_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_state_id.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.work_country_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Country</label>
                {{ form.work_country_id }}
                {% if form.work_country_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_country_id.errors }}</p>{% endif %}
            </div>

            <!-- Material Delivery Address Fields -->
            <div class="md:col-span-2">
                <h4 class="text-md font-medium text-gray-800 mb-2 mt-4 border-t pt-4">Material Delivery Address</h4>
            </div>
            <div class="mb-4">
                <label for="{{ form.material_del_city_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Delivery City</label>
                {{ form.material_del_city_id }}
                {% if form.material_del_city_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_city_id.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.material_del_state_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Delivery State</label>
                {{ form.material_del_state_id }}
                {% if form.material_del_state_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_state_id.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.material_del_country_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Delivery Country</label>
                {{ form.material_del_country_id }}
                {% if form.material_del_country_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_country_id.errors }}</p>{% endif %}
            </div>

        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>

```

**5. Delete Confirmation Partial Template (`_customer_enquiry_confirm_delete.html`)**
Used for delete confirmation, loaded into a modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Customer Enquiry with ID:
        <span class="font-bold">{{ object.enq_id }}</span>? This action cannot be undone.
    </p>

    <div class="flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <form hx-post="{% url 'customer_enquiry_delete' object.enq_id %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
            {% csrf_token %}
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Delete
            </button>
        </form>
    </div>
</div>

```

#### 4.5 URLs (`sales_distribution/urls.py`)

**Task:** Define URL patterns for all views.

```python
from django.urls import path
from .views import (
    CustomerEnquiryReportView,
    CustomerEnquiryListView,
    CustomerEnquiryCreateView,
    CustomerEnquiryUpdateView,
    CustomerEnquiryDeleteView,
    CustomerEnquiryTablePartialView,
)

urlpatterns = [
    # Main Report View (replaces original ASP.NET page)
    path('customer_enquiry/report/<int:enq_id>/', CustomerEnquiryReportView.as_view(), name='customer_enquiry_report'),

    # CRUD Operations for Customer Enquiry
    path('customer_enquiry/', CustomerEnquiryListView.as_view(), name='customer_enquiry_list'),
    path('customer_enquiry/add/', CustomerEnquiryCreateView.as_view(), name='customer_enquiry_add'),
    path('customer_enquiry/edit/<int:enq_id>/', CustomerEnquiryUpdateView.as_view(), name='customer_enquiry_edit'),
    path('customer_enquiry/delete/<int:enq_id>/', CustomerEnquiryDeleteView.as_view(), name='customer_enquiry_delete'),
    
    # HTMX Partial for DataTables
    path('customer_enquiry/table/', CustomerEnquiryTablePartialView.as_view(), name='customer_enquiry_table'),
]

```

#### 4.6 Tests (`sales_distribution/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views, ensuring high test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock

from .models import CustomerEnquiry, City, State, Country, Company

class CustomerEnquiryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs
        cls.company = Company.objects.create(comp_id=1, company_name='Test Company', address='123 Test St')
        cls.city = City.objects.create(city_id=1, city_name='Test City')
        cls.state = State.objects.create(state_id=1, state_name='Test State')
        cls.country = Country.objects.create(country_id=1, country_name='Test Country')

        # Create a test CustomerEnquiry instance
        cls.enquiry = CustomerEnquiry.objects.create(
            enq_id=1,
            comp_id=cls.company,
            customer_id=101,
            sys_date=timezone.datetime(2023, 1, 15, 10, 30, tzinfo=timezone.utc),
            regd_city_id=cls.city,
            regd_state_id=cls.state,
            regd_country_id=cls.country,
            work_city_id=cls.city,
            work_state_id=cls.state,
            work_country_id=cls.country,
            material_del_city_id=cls.city,
            material_del_state_id=cls.state,
            material_del_country_id=cls.country,
        )
        
    def test_customer_enquiry_creation(self):
        self.assertEqual(self.enquiry.enq_id, 1)
        self.assertEqual(self.enquiry.customer_id, 101)
        self.assertEqual(self.enquiry.comp_id, self.company)

    def test_formatted_sys_date_property(self):
        self.assertEqual(self.enquiry.formatted_sys_date, '15/01/2023')

    def test_address_properties(self):
        self.assertEqual(self.enquiry.registered_address['city'], 'Test City')
        self.assertEqual(self.enquiry.work_address['state'], 'Test State')
        self.assertEqual(self.enquiry.material_delivery_address['country'], 'Test Country')

    def test_get_enquiry_details_for_report(self):
        report_details = CustomerEnquiry.get_enquiry_details_for_report(1, self.company.comp_id)
        self.assertIsNotNone(report_details)
        self.assertEqual(report_details['enquiry'], self.enquiry)
        self.assertEqual(report_details['company_name'], 'Test Company')
        self.assertEqual(report_details['reg_address']['city'], 'Test City')

    def test_get_enquiry_details_for_report_not_found(self):
        report_details = CustomerEnquiry.get_enquiry_details_for_report(999, self.company.comp_id)
        self.assertIsNone(report_details)

    def test_get_enquiry_details_for_report_wrong_company(self):
        # Create another company
        Company.objects.create(comp_id=2, company_name='Other Company')
        report_details = CustomerEnquiry.get_enquiry_details_for_report(1, 2)
        self.assertIsNone(report_details)


class CustomerEnquiryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(comp_id=1, company_name='Test Company', address='123 Test St')
        cls.city = City.objects.create(city_id=1, city_name='Test City')
        cls.state = State.objects.create(state_id=1, state_name='Test State')
        cls.country = Country.objects.create(country_id=1, country_name='Test Country')

        cls.enquiry1 = CustomerEnquiry.objects.create(
            enq_id=1, comp_id=cls.company, customer_id=101, sys_date=timezone.datetime(2023, 1, 15, tzinfo=timezone.utc),
            regd_city_id=cls.city, regd_state_id=cls.state, regd_country_id=cls.country,
            work_city_id=cls.city, work_state_id=cls.state, work_country_id=cls.country,
            material_del_city_id=cls.city, material_del_state_id=cls.state, material_del_country_id=cls.country,
        )
        cls.enquiry2 = CustomerEnquiry.objects.create(
            enq_id=2, comp_id=cls.company, customer_id=102, sys_date=timezone.datetime(2023, 2, 20, tzinfo=timezone.utc),
            regd_city_id=cls.city, regd_state_id=cls.state, regd_country_id=cls.country,
            work_city_id=cls.city, work_state_id=cls.state, work_country_id=cls.country,
            material_del_city_id=cls.city, material_del_state_id=cls.state, material_del_country_id=cls.country,
        )
    
    def setUp(self):
        self.client = Client()
        # Mock session for comp_id if needed by get_object in report view
        # self.client.session['compid'] = self.company.comp_id # Example for session
    
    # --- Report View Tests ---
    def test_report_view_success(self):
        url = reverse('customer_enquiry_report', args=[self.enquiry1.enq_id])
        response = self.client.get(f"{url}?comp_id={self.company.comp_id}") # Pass comp_id as query param
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/report.html')
        self.assertIn('report_data', response.context)
        self.assertEqual(response.context['report_data']['enquiry'], self.enquiry1)
    
    def test_report_view_not_found(self):
        url = reverse('customer_enquiry_report', args=[999])
        response = self.client.get(f"{url}?comp_id={self.company.comp_id}")
        self.assertEqual(response.status_code, 404) # Should raise Http404

    def test_report_view_missing_comp_id(self):
        url = reverse('customer_enquiry_report', args=[self.enquiry1.enq_id])
        response = self.client.get(url) # Missing comp_id in query params, will use default 1
        self.assertEqual(response.status_code, 200) # Assumes default comp_id is 1
        self.assertIn('report_data', response.context)

    # --- List View Tests ---
    def test_list_view(self):
        response = self.client.get(reverse('customer_enquiry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/list.html')
        self.assertTrue('customer_enquiries' in response.context)
        self.assertEqual(len(response.context['customer_enquiries']), 2)

    def test_list_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('customer_enquiry_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/_customer_enquiry_table.html')
        self.assertTrue('customer_enquiries' in response.context)
        self.assertContains(response, '<table id="customer_enquiryTable"') # Check for table ID

    # --- Create View Tests ---
    def test_create_view_get(self):
        response = self.client.get(reverse('customer_enquiry_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/_customer_enquiry_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['action'], 'Add')

    def test_create_view_post_success(self):
        data = {
            'customer_id': 200,
            'sys_date': '2024-03-01',
            'regd_city_id': self.city.city_id,
            'regd_state_id': self.state.state_id,
            'regd_country_id': self.country.country_id,
            'work_city_id': self.city.city_id,
            'work_state_id': self.state.state_id,
            'work_country_id': self.country.country_id,
            'material_del_city_id': self.city.city_id,
            'material_del_state_id': self.state.state_id,
            'material_del_country_id': self.country.country_id,
        }
        # Simulate HTMX request
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customer_enquiry_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for success
        self.assertTrue(CustomerEnquiry.objects.filter(customer_id=200).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')

    def test_create_view_post_invalid(self):
        data = {'customer_id': 'invalid'} # Invalid data
        response = self.client.post(reverse('customer_enquiry_add'), data)
        self.assertEqual(response.status_code, 200) # Stays on form with errors
        self.assertFalse(CustomerEnquiry.objects.filter(customer_id='invalid').exists())
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)

    # --- Update View Tests ---
    def test_update_view_get(self):
        url = reverse('customer_enquiry_edit', args=[self.enquiry1.enq_id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/_customer_enquiry_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['action'], 'Edit')
        self.assertEqual(response.context['form'].instance, self.enquiry1)

    def test_update_view_post_success(self):
        updated_date = '2024-04-01'
        data = {
            'customer_id': self.enquiry1.customer_id, # Keep existing customer_id
            'sys_date': updated_date,
            'regd_city_id': self.city.city_id,
            'regd_state_id': self.state.state_id,
            'regd_country_id': self.country.country_id,
            'work_city_id': self.city.city_id,
            'work_state_id': self.state.state_id,
            'work_country_id': self.country.country_id,
            'material_del_city_id': self.city.city_id,
            'material_del_state_id': self.state.state_id,
            'material_del_country_id': self.country.country_id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customer_enquiry_edit', args=[self.enquiry1.enq_id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.enquiry1.refresh_from_db()
        self.assertEqual(self.enquiry1.sys_date.strftime('%Y-%m-%d'), updated_date)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')

    # --- Delete View Tests ---
    def test_delete_view_get(self):
        response = self.client.get(reverse('customer_enquiry_delete', args=[self.enquiry1.enq_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/_customer_enquiry_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.enquiry1)

    def test_delete_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customer_enquiry_delete', args=[self.enquiry1.enq_id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(CustomerEnquiry.objects.filter(enq_id=self.enquiry1.enq_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')

    def test_delete_view_post_not_found(self):
        response = self.client.post(reverse('customer_enquiry_delete', args=[999]))
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content:**
    *   The `customer_enquiry_list.html` page uses `hx-get="{% url 'customer_enquiry_table' %}" hx-swap="innerHTML"` to load the DataTable partial upon page load and when triggered by `refreshCustomerEnquiryList`.
    *   Buttons for "Add New Enquiry," "Edit," and "Delete" use `hx-get` to load forms or confirmation modals into a `#modalContent` div.
    *   Form submissions (`hx-post`) for `add` and `edit` views are configured to return `204 No Content` on success, with an `HX-Trigger` header (`refreshCustomerEnquiryList`) to inform the list page to re-fetch its data, ensuring the table is always up-to-date without a full page reload.
    *   The `hx-on::after-request` attribute on forms is used to close the modal after a successful HTMX submission (`status === 204`).

*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses Alpine.js (via `_` directives from htmx-alpine) for basic state management. The `on click add .is-active to #modal` shows the modal, and `on click if event.target.id == 'modal' remove .is-active from me` closes it if the overlay is clicked, providing a smooth user experience.

*   **DataTables for List Views:**
    *   The `_customer_enquiry_table.html` partial includes a `table` element with `id="customer_enquiryTable"`.
    *   A JavaScript snippet at the end of the partial initializes DataTables on this table upon loading into the DOM. This provides client-side searching, sorting, and pagination for the list of enquiries.

*   **No Custom JavaScript:**
    *   All dynamic interactions are handled declaratively with HTMX and Alpine.js, minimizing the need for complex, imperative JavaScript code.

### Final Notes

*   **Placeholders:** The values like `[TABLE_NAME]`, `[FIELD1]`, `[MODEL_NAME]` etc., have been replaced with concrete names derived from the ASP.NET code (e.g., `SD_Cust_Enquiry_Master` became `CustomerEnquiry`).
*   **DRY Templates:** We've used partial templates (`_customer_enquiry_table.html`, `_customer_enquiry_form.html`, `_customer_enquiry_confirm_delete.html`) to ensure reusability and maintain a clean structure. All main templates extend `core/base.html` as instructed.
*   **Fat Model, Thin View:** Business logic, such as resolving address names and formatting dates, has been encapsulated within the `CustomerEnquiry` model via properties and class methods. Views remain concise, primarily handling HTTP requests and delegating data operations to the models.
*   **Comprehensive Tests:** Unit tests for models ensure data integrity and correct behavior of business logic. Integration tests for views confirm that the application's endpoints respond correctly, render appropriate templates, and handle data interactions as expected, including HTMX-specific responses.
*   **Company ID Handling:** The original ASP.NET code heavily relies on `Session["compid"]`. In Django, this would typically be managed via user authentication and profiles (e.g., `request.user.company`). For this migration plan, `comp_id` is passed as a query parameter for the report view and assumed to be handled by a session or user context for CRUD operations, highlighting where integration with an existing authentication system would occur.
*   **Report Generation (Beyond HTML):** While the `CustomerEnquiryReportView` currently renders an HTML report, for a true replacement of Crystal Reports, the next step would be to integrate a PDF generation library like `WeasyPrint` or `ReportLab`. The view could then serve a PDF file directly or provide a "Download PDF" button, offering the same print-ready output as the legacy system. This would involve adding a `render_to_pdf` utility function or a dedicated PDF generation service.