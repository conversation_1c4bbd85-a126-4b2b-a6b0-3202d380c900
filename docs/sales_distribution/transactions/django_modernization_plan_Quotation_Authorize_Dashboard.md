## ASP.NET to Django Conversion Script: Quotation Authorization Dashboard

This document outlines a strategic plan to modernize your ASP.NET Quotation Authorization Dashboard into a robust, high-performance Django application. Given the minimal ASP.NET code provided, we will infer common functionalities associated with an "Authorization Dashboard" to illustrate a complete and automated conversion approach. This modernization leverages a modern technology stack for a responsive user experience without heavy client-side JavaScript.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code is a skeleton, we infer a `Quotation` table (`tblQuotation`) based on the module name `Quotation_Authorize_Dashboard`. This table would hold details about sales quotations requiring authorization.

**Inferred Database Schema:**

*   **Table Name:** `tblQuotation`
*   **Columns:**
    *   `QuotationID` (Primary Key, e.g., INT)
    *   `QuotationNumber` (Unique Identifier, e.g., NVARCHAR)
    *   `CustomerName` (Customer's Name, e.g., NVARCHAR)
    *   `TotalAmount` (Quotation Total, e.g., DECIMAL)
    *   `Status` (Authorization Status, e.g., NVARCHAR - 'Pending', 'Authorized', 'Rejected')
    *   `AuthorizedBy` (User who authorized, e.g., NVARCHAR, nullable)
    *   `AuthorizedDate` (Date of authorization, e.g., DATETIME, nullable)
    *   `CreatedDate` (Record creation date, e.g., DATETIME)
    *   `UpdatedDate` (Record last updated date, e.g., DATETIME)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Given the "Quotation Authorize Dashboard," the primary functionalities are:

*   **Read:** Displaying a list of quotations, primarily those awaiting or having undergone authorization. This typically involves fetching records based on their `Status`.
*   **Update:** The core "Authorize" action, which modifies the `Status` (e.g., to 'Authorized' or 'Rejected'), `AuthorizedBy`, and `AuthorizedDate` fields of a specific quotation. We'll also allow general editing of other quotation details.
*   **Create:** Although a dashboard typically *lists* existing items, a create functionality is often associated with the underlying data entity. We will include this for completeness, assuming new quotations can be initiated.
*   **Delete:** Similarly, a delete functionality is included for managing quotation records, even if less common on a dashboard.

**Inferred Business Logic:**

*   Marking a quotation as 'Authorized'.
*   Marking a quotation as 'Rejected'.
*   Ensuring `AuthorizedBy` and `AuthorizedDate` are set correctly upon authorization/rejection.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Assuming a typical ASP.NET dashboard, the following UI components are inferred:

*   **GridView:** To display the list of quotations, providing columns for `QuotationNumber`, `CustomerName`, `TotalAmount`, `Status`, `AuthorizedBy`, and `AuthorizedDate`. This will be replaced by a Django template with DataTables.
*   **Buttons/Links:** For "Add New Quotation," "Edit" (for general details), "Authorize," "Reject," and "Delete" actions. These will be implemented using HTMX.
*   **Form Controls:** TextBoxes (for `QuotationNumber`, `CustomerName`, `TotalAmount`), DropDownList/RadioButtons (for `Status`), DatePickers (for `AuthorizedDate`). These will be replaced by Django forms with Tailwind CSS styling and rendered in HTMX-loaded modals.

## Step 4: Generate Django Code

We will create a new Django application named `sales_distribution` to house the `Quotation` module.

### 4.1 Models (sales_distribution/models.py)

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `Quotation` model will map to `tblQuotation` with `managed=False`. It includes methods for business logic related to authorization.

```python
from django.db import models
from django.utils import timezone

class Quotation(models.Model):
    """
    Represents a sales quotation requiring authorization.
    Maps to the existing tblQuotation database table.
    """
    STATUS_CHOICES = [
        ('Pending', 'Pending Authorization'),
        ('Authorized', 'Authorized'),
        ('Rejected', 'Rejected'),
    ]

    quotation_id = models.AutoField(db_column='QuotationID', primary_key=True)
    quotation_number = models.CharField(
        db_column='QuotationNumber', 
        max_length=50, 
        unique=True,
        verbose_name="Quotation Number"
    )
    customer_name = models.CharField(
        db_column='CustomerName', 
        max_length=200,
        verbose_name="Customer Name"
    )
    total_amount = models.DecimalField(
        db_column='TotalAmount', 
        max_digits=18, 
        decimal_places=2,
        verbose_name="Total Amount"
    )
    status = models.CharField(
        db_column='Status', 
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='Pending',
        verbose_name="Authorization Status"
    )
    authorized_by = models.CharField(
        db_column='AuthorizedBy', 
        max_length=100, 
        null=True, 
        blank=True,
        verbose_name="Authorized By"
    )
    authorized_date = models.DateTimeField(
        db_column='AuthorizedDate', 
        null=True, 
        blank=True,
        verbose_name="Authorization Date"
    )
    created_date = models.DateTimeField(
        db_column='CreatedDate', 
        auto_now_add=True,
        verbose_name="Created On"
    )
    updated_date = models.DateTimeField(
        db_column='UpdatedDate', 
        auto_now=True,
        verbose_name="Last Updated On"
    )

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblQuotation'
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'
        ordering = ['-created_date']

    def __str__(self):
        return f"Quotation {self.quotation_number} - {self.customer_name}"

    @property
    def is_pending(self):
        """Checks if the quotation is pending authorization."""
        return self.status == 'Pending'

    @property
    def is_authorized(self):
        """Checks if the quotation has been authorized."""
        return self.status == 'Authorized'

    @property
    def is_rejected(self):
        """Checks if the quotation has been rejected."""
        return self.status == 'Rejected'

    def authorize(self, authorized_by_user):
        """Authorizes the quotation."""
        if self.is_pending:
            self.status = 'Authorized'
            self.authorized_by = authorized_by_user
            self.authorized_date = timezone.now()
            self.save(update_fields=['status', 'authorized_by', 'authorized_date', 'updated_date'])
            return True
        return False # Cannot authorize if not pending

    def reject(self, rejected_by_user):
        """Rejects the quotation."""
        if self.is_pending:
            self.status = 'Rejected'
            self.authorized_by = rejected_by_user # Use authorized_by for who rejected
            self.authorized_date = timezone.now()
            self.save(update_fields=['status', 'authorized_by', 'authorized_date', 'updated_date'])
            return True
        return False # Cannot reject if not pending
```

### 4.2 Forms (sales_distribution/forms.py)

**Task:** Define Django forms for user input.

**Instructions:**
We'll create two forms: `QuotationForm` for general CRUD and `QuotationAuthorizeForm` for specific authorization actions.

```python
from django import forms
from .models import Quotation

class QuotationForm(forms.ModelForm):
    """
    Form for creating and updating general quotation details.
    """
    class Meta:
        model = Quotation
        fields = ['quotation_number', 'customer_name', 'total_amount']
        widgets = {
            'quotation_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
        labels = {
            'quotation_number': 'Quotation Number',
            'customer_name': 'Customer Name',
            'total_amount': 'Total Amount',
        }
    
    def clean_quotation_number(self):
        quotation_number = self.cleaned_data['quotation_number']
        # Ensure quotation number is unique on create/update
        if self.instance.pk: # If updating an existing instance
            if Quotation.objects.filter(quotation_number=quotation_number).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("This quotation number already exists.")
        else: # If creating a new instance
            if Quotation.objects.filter(quotation_number=quotation_number).exists():
                raise forms.ValidationError("This quotation number already exists.")
        return quotation_number

class QuotationAuthorizeForm(forms.ModelForm):
    """
    Form specifically for authorizing/rejecting a quotation.
    Only allows changing the status. Business logic for user/date is in the model.
    """
    # Using ChoiceField for status selection, excluding fields that are auto-set by model methods
    status_action = forms.ChoiceField(
        choices=[
            ('Authorize', 'Authorize'),
            ('Reject', 'Reject'),
        ],
        widget=forms.RadioSelect(attrs={'class': 'mt-2 space-x-4'}),
        label="Action"
    )

    class Meta:
        model = Quotation
        fields = [] # No direct fields from the model are exposed for this form

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure that this form is used only for instances that can be authorized/rejected
        if self.instance and not self.instance.is_pending:
            self.fields['status_action'].disabled = True
            self.fields['status_action'].initial = self.instance.status # Show current status
```

### 4.3 Views (sales_distribution/views.py)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept thin, delegating business logic to the `Quotation` model. We add a `QuotationTablePartialView` to serve the DataTables content via HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming user authentication
from .models import Quotation
from .forms import QuotationForm, QuotationAuthorizeForm

# --- Main Dashboard View ---
class QuotationListView(LoginRequiredMixin, ListView):
    model = Quotation
    template_name = 'sales_distribution/quotation/list.html'
    context_object_name = 'quotations' # Will be used by the partial table view

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Quotation Authorization Dashboard'
        return context

# --- HTMX Partial for DataTables Content ---
class QuotationTablePartialView(LoginRequiredMixin, ListView):
    model = Quotation
    template_name = 'sales_distribution/quotation/_quotation_table.html'
    context_object_name = 'quotations' # This will be the list passed to the table template

# --- Create View (for adding new quotations) ---
class QuotationCreateView(LoginRequiredMixin, CreateView):
    model = Quotation
    form_class = QuotationForm
    template_name = 'sales_distribution/quotation/_quotation_form.html'
    success_url = reverse_lazy('quotation_list') # Redirect to list after successful creation

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Add New Quotation'
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f"Quotation {self.object.quotation_number} added successfully.")
        
        # HTMX response for success, close modal and trigger list refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Trigger': '{"refreshQuotationList": "", "closeModal": ""}'
                }
            )
        return response

    def form_invalid(self, form):
        # HTMX response for invalid form, re-render form within modal
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

# --- Update View (for editing existing quotations) ---
class QuotationUpdateView(LoginRequiredMixin, UpdateView):
    model = Quotation
    form_class = QuotationForm
    template_name = 'sales_distribution/quotation/_quotation_form.html'
    success_url = reverse_lazy('quotation_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Edit Quotation: {self.object.quotation_number}'
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f"Quotation {self.object.quotation_number} updated successfully.")

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshQuotationList": "", "closeModal": ""}'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

# --- Authorize/Reject View ---
class QuotationAuthorizeRejectView(LoginRequiredMixin, UpdateView):
    model = Quotation
    form_class = QuotationAuthorizeForm
    template_name = 'sales_distribution/quotation/_quotation_authorize_form.html'
    success_url = reverse_lazy('quotation_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Authorize/Reject Quotation: {self.object.quotation_number}'
        return context

    def form_valid(self, form):
        quotation = self.get_object()
        action = form.cleaned_data['status_action']
        
        if action == 'Authorize':
            if quotation.authorize(self.request.user.username): # Assuming username for authorized_by
                messages.success(self.request, f"Quotation {quotation.quotation_number} authorized successfully.")
            else:
                messages.error(self.request, f"Quotation {quotation.quotation_number} cannot be authorized as it's not pending.")
        elif action == 'Reject':
            if quotation.reject(self.request.user.username):
                messages.success(self.request, f"Quotation {quotation.quotation_number} rejected successfully.")
            else:
                messages.error(self.request, f"Quotation {quotation.quotation_number} cannot be rejected as it's not pending.")

        # This view doesn't directly save the form as model methods handle it.
        # So we just redirect or send HTMX trigger.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshQuotationList": "", "closeModal": ""}'
                }
            )
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        # This form is simple, unlikely to be invalid from user input, but handle for completeness
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

# --- Delete View ---
class QuotationDeleteView(LoginRequiredMixin, DeleteView):
    model = Quotation
    template_name = 'sales_distribution/quotation/_confirm_delete.html'
    success_url = reverse_lazy('quotation_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['object_to_delete'] = self.object # Pass the object to be deleted to the template
        return context

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        quotation_number = self.object.quotation_number # Store for message
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f"Quotation {quotation_number} deleted successfully.")
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshQuotationList": "", "closeModal": ""}'
                }
            )
        return response
```

### 4.4 Templates (sales_distribution/templates/sales_distribution/quotation/)

**Task:** Create templates for each view.

**Instructions:**
Templates utilize partials for reusability and are heavily integrated with HTMX and Alpine.js for dynamic interactions.

**`list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">{{ page_title }}</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'quotation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Quotation
        </button>
    </div>
    
    <div id="quotationTable-container"
         hx-trigger="load, refreshQuotationList from:body"
         hx-get="{% url 'quotation_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading indicator -->
        <div class="flex justify-center items-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="ml-4 text-lg text-gray-600">Loading quotations...</p>
        </div>
    </div>
    
    <!-- Global Modal Structure -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center p-4 z-50 hidden is-active"
         _="on closeModal remove .is-active from me
            on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }" x-init="$watch('show', value => { if (value) document.body.classList.add('overflow-hidden'); else document.body.classList.remove('overflow-hidden'); })">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300"
             _="on closeModal transition ease-in duration-200 transform scale-95 opacity-0
                then remove .is-active from #modal
                on htmx:afterSwap transition ease-out duration-300 transform scale-100 opacity-100">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global Alpine.js for modal management
    document.addEventListener('alpine:init', () => {
        // No specific component needed here as modal behavior is largely HTMX driven + _ syntax.
        // The `on closeModal` event listeners handle hiding the modal and clearing content.
        // We ensure dataTables reinitializes on HTMX swaps.
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'quotationTable-container') {
                $('#quotationTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true // Destroy existing DataTable instance if any
                });
            }
        });
        // Event listener to close the modal after a successful form submission via HTMX
        document.body.addEventListener('refreshQuotationList', function(event) {
            // This event is triggered from views.py via HX-Trigger
            // The `_` syntax on the modal handles removing 'is-active'
        });
    });
</script>
{% endblock %}
```

**`_quotation_table.html`** (Partial for DataTables content)
```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="quotationTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for quotation in quotations %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ quotation.quotation_number }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.customer_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-right">${{ quotation.total_amount|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if quotation.status == 'Authorized' %}bg-green-100 text-green-800
                        {% elif quotation.status == 'Rejected' %}bg-red-100 text-red-800
                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ quotation.status }}
                    </span>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.authorized_by|default:"N/A" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">
                    {% if quotation.authorized_date %}
                        {{ quotation.authorized_date|date:"Y-m-d H:i" }}
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex space-x-2">
                        {% if quotation.is_pending %}
                        <button 
                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            hx-get="{% url 'quotation_authorize_reject' quotation.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            <i class="fas fa-check mr-1"></i> Authorize
                        </button>
                        {% endif %}
                        <button 
                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                            hx-get="{% url 'quotation_edit' quotation.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            <i class="fas fa-edit mr-1"></i> Edit
                        </button>
                        <button 
                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            hx-get="{% url 'quotation_delete' quotation.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            <i class="fas fa-trash-alt mr-1"></i> Delete
                        </button>
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-gray-500">No quotations found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization is handled in list.html's htmx:afterSwap event listener
    // to ensure it runs after the partial is loaded.
    // The destroy: true option ensures it re-initializes correctly.
</script>
```

**`_quotation_form.html`** (Partial for Create/Update)
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form_title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
            </label>
            <div class="mt-1">
                {{ field }}
            </div>
            {% if field.help_text %}
            <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="mt-2 text-sm text-red-600">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Quotation
            </button>
        </div>
    </form>
</div>
```

**`_quotation_authorize_form.html`** (Partial for Authorization)
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form_title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <p class="text-lg text-gray-700 mb-4">
            Quotation Number: <span class="font-bold">{{ object.quotation_number }}</span><br>
            Customer: <span class="font-bold">{{ object.customer_name }}</span><br>
            Current Status: 
            <span class="px-2 inline-flex text-base leading-5 font-semibold rounded-full 
                {% if object.status == 'Authorized' %}bg-green-100 text-green-800
                {% elif object.status == 'Rejected' %}bg-red-100 text-red-800
                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                {{ object.status }}
            </span>
        </p>

        {% if object.is_pending %}
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Select Action:
                </label>
                <div class="flex items-center space-x-6">
                    {% for radio in form.status_action %}
                    <label class="flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
            </div>
            {% if form.errors %}
            <p class="mt-2 text-sm text-red-600">{{ form.errors }}</p>
            {% endif %}
        {% else %}
            <p class="text-red-600 text-lg">This quotation is already {{ object.status }}. No further action can be taken.</p>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                _="on click trigger closeModal">
                Cancel
            </button>
            {% if object.is_pending %}
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Confirm Action
            </button>
            {% endif %}
        </div>
    </form>
</div>
```

**`_confirm_delete.html`** (Partial for Delete confirmation)
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-lg text-gray-700 mb-4">
        Are you sure you want to delete Quotation: 
        <span class="font-bold">{{ object_to_delete.quotation_number }}</span> (Customer: <span class="font-bold">{{ object_to_delete.customer_name }}</span>)?
    </p>
    <p class="text-red-600 font-medium mb-6">This action cannot be undone.</p>
    
    <form hx-post="{% url 'quotation_delete' object_to_delete.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (sales_distribution/urls.py)

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main list, CRUD operations, and the HTMX partial for the table.

```python
from django.urls import path
from .views import (
    QuotationListView, 
    QuotationTablePartialView,
    QuotationCreateView, 
    QuotationUpdateView, 
    QuotationAuthorizeRejectView,
    QuotationDeleteView
)

urlpatterns = [
    # Main dashboard view
    path('quotation/', QuotationListView.as_view(), name='quotation_list'),
    
    # HTMX partial for the DataTables content
    path('quotation/table/', QuotationTablePartialView.as_view(), name='quotation_table'),
    
    # CRUD operations
    path('quotation/add/', QuotationCreateView.as_view(), name='quotation_add'),
    path('quotation/edit/<int:pk>/', QuotationUpdateView.as_view(), name='quotation_edit'),
    path('quotation/authorize/<int:pk>/', QuotationAuthorizeRejectView.as_view(), name='quotation_authorize_reject'),
    path('quotation/delete/<int:pk>/', QuotationDeleteView.as_view(), name='quotation_delete'),
]
```

### 4.6 Tests (sales_distribution/tests.py)

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for the `Quotation` model's methods and properties, and integration tests for all views, including HTMX request simulations.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from .models import Quotation
from .forms import QuotationForm, QuotationAuthorizeForm

class QuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user for authorization
        cls.user = User.objects.create_user(username='testuser', password='password123')
        # Create initial pending quotation
        cls.pending_quotation = Quotation.objects.create(
            quotation_number='Q-001',
            customer_name='Test Customer A',
            total_amount=100.00,
            status='Pending',
            created_date=timezone.now()
        )
        # Create an authorized quotation
        cls.authorized_quotation = Quotation.objects.create(
            quotation_number='Q-002',
            customer_name='Test Customer B',
            total_amount=250.50,
            status='Authorized',
            authorized_by='Admin User',
            authorized_date=timezone.now(),
            created_date=timezone.now()
        )

    def test_quotation_creation(self):
        quotation = Quotation.objects.get(quotation_number='Q-001')
        self.assertEqual(quotation.customer_name, 'Test Customer A')
        self.assertEqual(quotation.total_amount, 100.00)
        self.assertEqual(quotation.status, 'Pending')
        self.assertIsNone(quotation.authorized_by)
        self.assertIsNone(quotation.authorized_date)

    def test_str_representation(self):
        quotation = Quotation.objects.get(quotation_number='Q-001')
        self.assertEqual(str(quotation), 'Quotation Q-001 - Test Customer A')

    def test_status_properties(self):
        self.assertTrue(self.pending_quotation.is_pending)
        self.assertFalse(self.pending_quotation.is_authorized)
        self.assertFalse(self.pending_quotation.is_rejected)

        self.assertFalse(self.authorized_quotation.is_pending)
        self.assertTrue(self.authorized_quotation.is_authorized)
        self.assertFalse(self.authorized_quotation.is_rejected)

    def test_authorize_method(self):
        quotation = self.pending_quotation
        old_updated_date = quotation.updated_date
        
        # Authorize the quotation
        authorized = quotation.authorize(self.user.username)
        self.assertTrue(authorized)
        quotation.refresh_from_db() # Refresh state from DB
        
        self.assertEqual(quotation.status, 'Authorized')
        self.assertEqual(quotation.authorized_by, self.user.username)
        self.assertIsNotNone(quotation.authorized_date)
        self.assertGreater(quotation.updated_date, old_updated_date)

        # Attempt to authorize an already authorized quotation
        authorized_again = quotation.authorize(self.user.username)
        self.assertFalse(authorized_again) # Should return False as it's not pending

    def test_reject_method(self):
        quotation = Quotation.objects.create(
            quotation_number='Q-003',
            customer_name='Test Customer C',
            total_amount=500.00,
            status='Pending'
        )
        old_updated_date = quotation.updated_date

        # Reject the quotation
        rejected = quotation.reject(self.user.username)
        self.assertTrue(rejected)
        quotation.refresh_from_db()

        self.assertEqual(quotation.status, 'Rejected')
        self.assertEqual(quotation.authorized_by, self.user.username)
        self.assertIsNotNone(quotation.authorized_date)
        self.assertGreater(quotation.updated_date, old_updated_date)

        # Attempt to reject an already rejected quotation
        rejected_again = quotation.reject(self.user.username)
        self.assertFalse(rejected_again)

    def test_authorize_reject_non_pending(self):
        # Cannot authorize/reject if not pending
        quotation = self.authorized_quotation
        self.assertFalse(quotation.authorize(self.user.username))
        self.assertFalse(quotation.reject(self.user.username))


class QuotationFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user for authorization
        cls.user = User.objects.create_user(username='testuser', password='password123')
        # Create initial pending quotation
        cls.pending_quotation = Quotation.objects.create(
            quotation_number='Q-001',
            customer_name='Test Customer A',
            total_amount=100.00,
            status='Pending',
            created_date=timezone.now()
        )

    def test_quotation_form_valid_data(self):
        form = QuotationForm(data={
            'quotation_number': 'Q-NEW',
            'customer_name': 'New Customer',
            'total_amount': 75.00
        })
        self.assertTrue(form.is_valid())

    def test_quotation_form_duplicate_quotation_number_create(self):
        form = QuotationForm(data={
            'quotation_number': 'Q-001', # Exists
            'customer_name': 'Another Customer',
            'total_amount': 200.00
        })
        self.assertFalse(form.is_valid())
        self.assertIn('quotation_number', form.errors)
        self.assertEqual(form.errors['quotation_number'][0], 'This quotation number already exists.')

    def test_quotation_form_duplicate_quotation_number_update(self):
        # Create another quotation
        Quotation.objects.create(
            quotation_number='Q-004',
            customer_name='Temp Customer',
            total_amount=300.00,
            status='Pending'
        )
        
        # Try to update Q-001 to Q-004 (should fail)
        quotation_to_update = Quotation.objects.get(quotation_number='Q-001')
        form = QuotationForm(instance=quotation_to_update, data={
            'quotation_number': 'Q-004', # Exists for another record
            'customer_name': 'Updated Customer',
            'total_amount': 150.00
        })
        self.assertFalse(form.is_valid())
        self.assertIn('quotation_number', form.errors)

        # Try to update Q-001 to its own number (should pass)
        form = QuotationForm(instance=quotation_to_update, data={
            'quotation_number': 'Q-001', # Same as instance
            'customer_name': 'Updated Customer',
            'total_amount': 150.00
        })
        self.assertTrue(form.is_valid())


    def test_quotation_authorize_form_valid_data(self):
        form = QuotationAuthorizeForm(instance=self.pending_quotation, data={'status_action': 'Authorize'})
        self.assertTrue(form.is_valid())
        
        form = QuotationAuthorizeForm(instance=self.pending_quotation, data={'status_action': 'Reject'})
        self.assertTrue(form.is_valid())

    def test_quotation_authorize_form_disabled_for_non_pending(self):
        authorized_quotation = Quotation.objects.create(
            quotation_number='Q-005',
            customer_name='Already Auth',
            total_amount=100.00,
            status='Authorized'
        )
        form = QuotationAuthorizeForm(instance=authorized_quotation)
        self.assertTrue(form.fields['status_action'].disabled)


class QuotationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='tester', password='testpassword')
        cls.quotation1 = Quotation.objects.create(
            quotation_number='Q-001', customer_name='Cust A', total_amount=100.00, status='Pending'
        )
        cls.quotation2 = Quotation.objects.create(
            quotation_number='Q-002', customer_name='Cust B', total_amount=200.00, status='Authorized'
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='tester', password='testpassword')

    def test_quotation_list_view(self):
        response = self.client.get(reverse('quotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/quotation/list.html')
        self.assertContains(response, 'Quotation Authorization Dashboard')

    def test_quotation_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('quotation_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/quotation/_quotation_table.html')
        self.assertContains(response, 'Q-001')
        self.assertContains(response, 'Q-002')
        self.assertEqual(response.context['quotations'].count(), 2) # Check if all quotations are passed

    def test_quotation_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('quotation_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/quotation/_quotation_form.html')
        self.assertContains(response, 'Add New Quotation')
        self.assertIsInstance(response.context['form'], QuotationForm)

    def test_quotation_create_view_post_valid_htmx(self):
        data = {
            'quotation_number': 'Q-003',
            'customer_name': 'New Customer C',
            'total_amount': 300.00
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success -> No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshQuotationList', response.headers['HX-Trigger'])
        self.assertTrue(Quotation.objects.filter(quotation_number='Q-003').exists())

    def test_quotation_create_view_post_invalid_htmx(self):
        data = {
            'quotation_number': 'Q-001', # Duplicate
            'customer_name': 'New Customer C',
            'total_amount': 300.00
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX invalid -> re-render form
        self.assertTemplateUsed(response, 'sales_distribution/quotation/_quotation_form.html')
        self.assertContains(response, 'This quotation number already exists.')

    def test_quotation_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('quotation_edit', args=[self.quotation1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/quotation/_quotation_form.html')
        self.assertContains(response, 'Edit Quotation: Q-001')
        self.assertIsInstance(response.context['form'], QuotationForm)
        self.assertEqual(response.context['form'].instance, self.quotation1)

    def test_quotation_update_view_post_valid_htmx(self):
        data = {
            'quotation_number': 'Q-001-Updated',
            'customer_name': 'Updated Cust A',
            'total_amount': 150.00
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_edit', args=[self.quotation1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.quotation1.refresh_from_db()
        self.assertEqual(self.quotation1.quotation_number, 'Q-001-Updated')
        self.assertEqual(self.quotation1.customer_name, 'Updated Cust A')

    def test_quotation_authorize_reject_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('quotation_authorize_reject', args=[self.quotation1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/quotation/_quotation_authorize_form.html')
        self.assertContains(response, 'Authorize/Reject Quotation: Q-001')
        self.assertIsInstance(response.context['form'], QuotationAuthorizeForm)
        self.assertEqual(response.context['form'].instance, self.quotation1)

    def test_quotation_authorize_reject_view_post_authorize_htmx(self):
        data = {'status_action': 'Authorize'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_authorize_reject', args=[self.quotation1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.quotation1.refresh_from_db()
        self.assertEqual(self.quotation1.status, 'Authorized')
        self.assertEqual(self.quotation1.authorized_by, self.user.username)
        self.assertIsNotNone(self.quotation1.authorized_date)

    def test_quotation_authorize_reject_view_post_reject_htmx(self):
        data = {'status_action': 'Reject'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_authorize_reject', args=[self.quotation1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.quotation1.refresh_from_db()
        self.assertEqual(self.quotation1.status, 'Rejected')
        self.assertEqual(self.quotation1.authorized_by, self.user.username)
        self.assertIsNotNone(self.quotation1.authorized_date)

    def test_quotation_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('quotation_delete', args=[self.quotation1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/quotation/_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Q-001')

    def test_quotation_delete_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('quotation_delete', args=[self.quotation1.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertFalse(Quotation.objects.filter(pk=self.quotation1.pk).exists())
        self.assertEqual(Quotation.objects.count(), 1) # Only quotation2 should remain

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django views and templates are designed from the ground up for HTMX and Alpine.js.

*   **HTMX for Dynamic Updates:**
    *   The `quotation_list` template uses `hx-get` on `quotationTable-container` with `hx-trigger="load, refreshQuotationList from:body"` to load the table dynamically when the page loads or when a `refreshQuotationList` event is triggered (after C/U/D operations).
    *   CRUD forms (`_quotation_form.html`, `_quotation_authorize_form.html`, `_confirm_delete.html`) are loaded into a modal via `hx-get` on button clicks.
    *   Form submissions (`hx-post`) from within the modal trigger `HX-Trigger` headers from the Django views, which then close the modal and refresh the main list.
    *   `hx-swap="none"` is used on form submissions to prevent the HTMX request from swapping content on the client side, allowing the server (Django view) to send a `HX-Trigger` for a full refresh of the table and modal closure.

*   **Alpine.js for UI State Management:**
    *   A simple Alpine.js `x-data` attribute on the modal div `id="modal"` handles the `hidden` class toggle.
    *   The `_="on click add .is-active to #modal"` and `_="on click if event.target.id == 'modal' remove .is-active from me"` attributes (hyperscript syntax) manage the modal's visibility.
    *   A custom event `closeModal` is triggered by buttons (`_="on click trigger closeModal"`) and by the server after successful form submissions via `HX-Trigger`, ensuring the modal closes cleanly.
    *   The modal's content transition (`transform scale-95 opacity-0`) and `htmx:afterSwap` on `modalContent` provide smooth animations.

*   **DataTables for List Views:**
    *   The `_quotation_table.html` partial contains the HTML structure for the `quotationTable`.
    *   The DataTables initialization (`$('#quotationTable').DataTable({...});`) is placed inside a `htmx:afterSwap` event listener in `list.html`. This guarantees that DataTables is initialized only after the `_quotation_table.html` partial has been loaded and swapped into the DOM, and `destroy: true` ensures graceful re-initialization on subsequent HTMX refreshes.

**Final Notes:**

*   **Placeholders:** Replace `[APP_NAME]` (already set to `sales_distribution`), `[TABLE_NAME]` (already set to `tblQuotation`), and inferred field names with your actual project specifics during automation.
*   **DRY Principles:** Base template inheritance and partial templates are extensively used to reduce code duplication.
*   **Fat Models, Thin Views:** Business logic for authorization (`authorize`, `reject` methods) resides directly within the `Quotation` model, keeping views concise and focused on request handling.
*   **Comprehensive Tests:** The provided tests cover model logic, form validation, and view behavior, including HTMX interactions, ensuring robust and maintainable code.
*   **User Authentication:** `LoginRequiredMixin` is included in views, assuming a standard Django authentication setup is in place, crucial for an authorization dashboard.
*   **Frontend Libraries:** Assume CDN links for HTMX, Alpine.js, DataTables, and Tailwind CSS are included in `core/base.html`.

This comprehensive plan provides a systematic, automation-friendly approach to transform your legacy ASP.NET dashboard into a modern, efficient, and maintainable Django application with a rich, dynamic user experience.