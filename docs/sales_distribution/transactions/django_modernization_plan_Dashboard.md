This ASP.NET to Django modernization plan focuses on transforming a legacy ASP.NET application into a modern Django-based solution. Given that the provided ASP.NET code (both the `.aspx` and C# code-behind files) is empty, containing only content placeholders and an empty `Page_Load` method, it provides no specific functionality, database schema, or UI components to analyze.

Therefore, this plan will demonstrate a **standard, robust Django CRUD (Create, Read, Update, Delete) module for a generic `Item` entity**, as a foundational blueprint for how any part of your ASP.NET application would be modernized. This approach allows us to illustrate the target architecture (Fat Models, Thin Views, HTMX, Alpine.js, DataTables) in a complete and runnable manner, which can then be applied systematically to actual identified functionalities once the ASP.NET code is fully analyzed.

This blueprint emphasizes automation and patterns, suitable for execution through conversational AI guidance.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The provided ASP.NET `.aspx` and C# code-behind files are empty and contain no database-related elements or explicit UI bindings. Therefore, no specific database schema can be extracted directly from this input.

**Action:** For demonstration purposes, we will infer a simple, generic database table named `tbl_items` with basic columns that would typically be managed on a dashboard. This represents a placeholder for actual tables you might discover in a more complete ASP.NET application.

**Inferred Details:**
*   **[TABLE_NAME]:** `tbl_items`
*   **Columns:**
    *   `item_id` (Primary Key, inferred auto-increment)
    *   `item_name` (Text, e.g., product name, report title)
    *   `item_description` (Longer text, e.g., details about the item)
    *   `is_active` (Boolean, common for dashboard visibility)
    *   `created_at` (Datetime, common timestamp)

### Step 2: Identify Backend Functionality

**Analysis:** The provided ASP.NET code has no backend functionality defined in its empty `Page_Load` method or the `.aspx` markup.

**Action:** We will assume a standard set of CRUD operations (Create, Read, Update, Delete) as these are fundamental to nearly all business applications and would be present for any data managed through a dashboard-like interface.

**Functionality Assumed:**
*   **Create:** Adding new items to the system.
*   **Read:** Displaying a list of all items and viewing details of a single item.
*   **Update:** Modifying existing item details.
*   **Delete:** Removing items from the system.
*   **Validation Logic:** Basic validation for required fields (e.g., `item_name`).

### Step 3: Infer UI Components

**Analysis:** The provided ASP.NET `.aspx` file is empty, only containing content placeholders. No UI controls (like GridView, TextBox, Button) are present.

**Action:** We will infer the need for typical UI components required for managing a list of items and performing CRUD operations, following modern web development best practices (DataTables for lists, modals for forms).

**Inferred UI Components:**
*   **List View:** A table to display a list of items, with columns for `item_name`, `item_description`, `is_active`, and an 'Actions' column for Edit/Delete buttons. This will be implemented using DataTables.
*   **Form (Create/Update):** Input fields for `item_name`, `item_description`, and a checkbox for `is_active`. These forms will be loaded dynamically within a modal using HTMX.
*   **Confirmation Dialog (Delete):** A simple modal to confirm deletion.
*   **Buttons:** "Add New Item" button (opens create form), "Edit" buttons (opens update form), "Delete" buttons (opens delete confirmation).

---

## Step 4: Generate Django Code

We will create a Django application named `dashboard` to encapsulate the `Item` management module.

### 4.1 Models

**Task:** Create a Django model representing the `Item` entity, mapped to the `tbl_items` database table. Business logic for an `Item` will reside here.

**Instructions:**
*   **[MODEL_NAME]:** `Item`
*   **[TABLE_NAME]:** `tbl_items`
*   **Fields:** `name` (CharField), `description` (TextField), `is_active` (BooleanField), `created_at` (DateTimeField).
*   **Business Logic:** A simple method `get_summary` is added to demonstrate placing logic in the model.

**File:** `dashboard/models.py`

```python
from django.db import models

class Item(models.Model):
    # Primary key is typically inferred by Django if not explicitly set and named 'id'.
    # If 'item_id' exists as an explicit PK in the legacy database, adjust accordingly:
    # item_id = models.AutoField(db_column='item_id', primary_key=True)
    
    name = models.CharField(db_column='item_name', max_length=255, verbose_name='Item Name')
    description = models.TextField(db_column='item_description', blank=True, null=True, verbose_name='Description')
    is_active = models.BooleanField(db_column='is_active', default=True, verbose_name='Is Active')
    created_at = models.DateTimeField(db_column='created_at', auto_now_add=True, verbose_name='Created At')

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tbl_items'  # Maps to the existing legacy table
        verbose_name = 'Item'
        verbose_name_plural = 'Items'
        ordering = ['-created_at'] # Default ordering for list views

    def __str__(self):
        return self.name
        
    # Business logic methods (Fat Model principle)
    def get_summary(self):
        """Returns a concise summary of the item."""
        return f"{self.name} (Active: {self.is_active})"

    def deactivate(self):
        """Deactivates the item."""
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False

    def activate(self):
        """Activates the item."""
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False
```

### 4.2 Forms

**Task:** Define a Django ModelForm for `Item` to handle user input for create and update operations.

**Instructions:**
*   Create a `ModelForm` for the `Item` model.
*   Include `name`, `description`, and `is_active` fields.
*   Apply Tailwind CSS classes to widgets for consistent styling.

**File:** `dashboard/forms.py`

```python
from django import forms
from .models import Item

class ItemForm(forms.ModelForm):
    class Meta:
        model = Item
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 4}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'name': 'Item Name',
            'description': 'Description',
            'is_active': 'Is Active',
        }
        
    def clean_name(self):
        """Custom validation for item name."""
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise forms.ValidationError("Item name must be at least 3 characters long.")
        return name
```

### 4.3 Views

**Task:** Implement Django Class-Based Views (CBVs) for List, Create, Update, and Delete operations for `Item`. Also, a partial view for the HTMX-loaded table.

**Instructions:**
*   Ensure views are "thin" (5-15 lines) by delegating business logic to the model.
*   Use `reverse_lazy` for `success_url` to prevent import issues.
*   Implement HTMX-specific responses (`HX-Trigger`) for dynamic updates.

**File:** `dashboard/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Item
from .forms import ItemForm

class ItemListView(ListView):
    """Displays a list of all items."""
    model = Item
    template_name = 'dashboard/item/list.html'
    context_object_name = 'items' # Renamed for clarity in template

class ItemTablePartialView(ListView):
    """Renders only the table rows for HTMX updates."""
    model = Item
    template_name = 'dashboard/item/_item_table.html' # Partial template
    context_object_name = 'items'

class ItemCreateView(CreateView):
    """Handles creation of new items."""
    model = Item
    form_class = ItemForm
    template_name = 'dashboard/item/_item_form.html' # Partial template for modal
    success_url = reverse_lazy('item_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Item "{self.object.name}" added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,  # No content to indicate success without full page reload
                headers={'HX-Trigger': 'refreshItemList'} # Trigger HTMX event to refresh table
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(response.render().content, status=400) # Render form with errors for HTMX
        return response

class ItemUpdateView(UpdateView):
    """Handles updating existing items."""
    model = Item
    form_class = ItemForm
    template_name = 'dashboard/item/_item_form.html' # Partial template for modal
    success_url = reverse_lazy('item_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Item "{self.object.name}" updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshItemList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(response.render().content, status=400)
        return response

class ItemDeleteView(DeleteView):
    """Handles deletion of items."""
    model = Item
    template_name = 'dashboard/item/_item_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('item_list')

    def delete(self, request, *args, **kwargs):
        item_name = self.get_object().name # Capture name before deletion for message
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Item "{item_name}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshItemList'}
            )
        return response
```

### 4.4 Templates

**Task:** Create HTML templates for each view, ensuring DRY principles and HTMX/Alpine.js integration.

**Instructions:**
*   `list.html` extends `core/base.html` and loads the table via HTMX.
*   `_item_table.html` is the HTMX partial for the DataTables content.
*   `_item_form.html` and `_item_confirm_delete.html` are partials for modal content.
*   All templates use Tailwind CSS classes.

**File:** `dashboard/templates/dashboard/item/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Items Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'item_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Item
        </button>
    </div>
    
    <!-- Messages Container (for Django messages) -->
    <div id="messages" class="mb-4">
        {% include 'partials/_messages.html' %} {# Assume _messages.html exists in common partials #}
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div id="itemTable-container"
             hx-trigger="load, refreshItemList from:body"
             hx-get="{% url 'item_table_partial' %}"
             hx-swap="innerHTML"
             class="p-4">
            <!-- Initial loading spinner -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading items...</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden transition-opacity duration-300 ease-out opacity-0"
         _="on hx-after-swap from:#modalContent if not event.detail.xhr.status == 204 or 400 add .is-active to me then add opacity-100 to me"
         x-data="{ show: false }"
         x-show="show"
         x-init="$watch('show', value => { if (!value) { document.getElementById('modalContent').innerHTML = ''; } })"
         @click.self="show = false; $el.classList.remove('opacity-100'); $el.classList.remove('is-active')"
         >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-lg w-full transform transition-all duration-300 ease-out scale-95"
             @click.away="show = false; $el.closest('#modal').classList.remove('opacity-100'); $el.closest('#modal').classList.remove('is-active')">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup (if not already in base.html)
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalHandler', () => ({
            show: false,
            openModal() {
                this.show = true;
                document.getElementById('modal').classList.add('is-active', 'opacity-100');
            },
            closeModal() {
                this.show = false;
                document.getElementById('modal').classList.remove('opacity-100');
                // Give time for transition before hiding
                setTimeout(() => {
                    document.getElementById('modal').classList.remove('is-active');
                    document.getElementById('modalContent').innerHTML = ''; // Clear content
                }, 300);
            }
        }));
    });

    // Listen for HTMX triggers to close modal
    document.body.addEventListener('refreshItemList', () => {
        // If a modal is open, close it
        const modal = document.getElementById('modal');
        if (modal && modal.classList.contains('is-active')) {
            modal.classList.remove('opacity-100');
            setTimeout(() => {
                modal.classList.remove('is-active');
                document.getElementById('modalContent').innerHTML = '';
            }, 300);
        }
    });

    // Re-initialize DataTable on HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'itemTable-container') {
            $('#itemTable').DataTable({
                "paging": true,
                "lengthChange": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]]
            });
        }
    });
</script>
{% endblock %}
```

**File:** `dashboard/templates/dashboard/item/_item_table.html` (HTMX Partial)

```html
<table id="itemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in items %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.name }}</td>
            <td class="py-3 px-4 text-sm text-gray-500 max-w-xs truncate">{{ obj.description }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {% if obj.is_active %}Active{% else %}Inactive{% endif %}
                </span>
            </td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.created_at|date:"Y-m-d H:i" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-yellow-500 hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 mr-2"
                    hx-get="{% url 'item_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click call modalHandler.openModal()">
                    <i class="fas fa-edit mr-1"></i> Edit
                </button>
                <button 
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    hx-get="{% url 'item_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click call modalHandler.openModal()">
                    <i class="fas fa-trash-alt mr-1"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Ensure DataTables script is loaded and initialized -->
<!-- This script needs to be placed after the table is rendered, typically handled by base.html or a specific script block within list.html -->
```

**File:** `dashboard/templates/dashboard/item/_item_form.html` (HTMX Partial)

```html
<div class="p-6 bg-white rounded-lg shadow-lg" x-data="{}" _="on htmx:afterRequest if event.detail.xhr.status == 204 call modalHandler.closeModal()">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6 border-b pb-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} Item
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click call modalHandler.closeModal()">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Item
                <span id="loadingIndicator" class="htmx-indicator ml-2">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
            </button>
        </div>
    </form>
</div>
```

**File:** `dashboard/templates/dashboard/item/_item_confirm_delete.html` (HTMX Partial)

```html
<div class="p-6 bg-white rounded-lg shadow-lg" x-data="{}" _="on htmx:afterRequest if event.detail.xhr.status == 204 call modalHandler.closeModal()">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the item "<strong>{{ object.name }}</strong>"? This action cannot be undone.
    </p>
    <form hx-post="{% url 'item_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click call modalHandler.closeModal()">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete Item
            </button>
        </div>
    </form>
</div>
```
**Note:** For `partials/_messages.html`, a common include for Django messages, you might have something like this in your `core/templates/partials/_messages.html`:
```html
{% for message in messages %}
<div class="mb-4 p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %} flex justify-between items-center"
     role="alert"
     x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)">
    <span>{{ message }}</span>
    <button @click="show = false" class="text-current font-bold opacity-75 hover:opacity-100 text-xl leading-none">&times;</button>
</div>
{% endfor %}
```

### 4.5 URLs

**Task:** Define URL patterns for the `Item` views, including paths for HTMX-loaded partials.

**Instructions:**
*   Create paths for the list view, CRUD operations, and the table partial.
*   Use consistent naming conventions (`item_list`, `item_add`, etc.).

**File:** `dashboard/urls.py`

```python
from django.urls import path
from .views import (
    ItemListView, 
    ItemCreateView, 
    ItemUpdateView, 
    ItemDeleteView, 
    ItemTablePartialView
)

urlpatterns = [
    # Main list view (full page load, but table loads via HTMX)
    path('items/', ItemListView.as_view(), name='item_list'),
    
    # HTMX endpoints for modal forms and table refresh
    path('items/add/', ItemCreateView.as_view(), name='item_add'),
    path('items/edit/<int:pk>/', ItemUpdateView.as_view(), name='item_edit'),
    path('items/delete/<int:pk>/', ItemDeleteView.as_view(), name='item_delete'),
    path('items/table/', ItemTablePartialView.as_view(), name='item_table_partial'),
]
```

### 4.6 Tests

**Task:** Write comprehensive unit tests for the `Item` model and integration tests for all `Item` views.

**Instructions:**
*   Model tests should cover field properties and custom methods.
*   View tests should cover GET and POST requests for all CRUD operations, including assertions for template usage, context data, and successful redirects/HTMX responses.
*   Ensure test coverage for edge cases and validation.

**File:** `dashboard/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import Item
from datetime import datetime
import json

class ItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.item1 = Item.objects.create(
            name='Test Item 1',
            description='This is a description for test item 1.',
            is_active=True,
            created_at=datetime(2023, 1, 1, 10, 0, 0)
        )
        cls.item2 = Item.objects.create(
            name='Inactive Item',
            description='This item should be inactive.',
            is_active=False,
            created_at=datetime(2023, 1, 2, 11, 0, 0)
        )
  
    def test_item_creation(self):
        """Test that an Item object is created correctly."""
        self.assertEqual(self.item1.name, 'Test Item 1')
        self.assertEqual(self.item1.description, 'This is a description for test item 1.')
        self.assertTrue(self.item1.is_active)
        self.assertEqual(str(self.item1), 'Test Item 1') # Test __str__ method

    def test_field_labels(self):
        """Test verbose names for fields."""
        field_name = self.item1._meta.get_field('name').verbose_name
        self.assertEqual(field_name, 'Item Name')
        field_description = self.item1._meta.get_field('description').verbose_name
        self.assertEqual(field_description, 'Description')

    def test_get_summary_method(self):
        """Test the custom get_summary method."""
        self.assertEqual(self.item1.get_summary(), 'Test Item 1 (Active: True)')
        self.assertEqual(self.item2.get_summary(), 'Inactive Item (Active: False)')

    def test_deactivate_method(self):
        """Test the deactivate method."""
        self.assertTrue(self.item1.is_active)
        self.assertTrue(self.item1.deactivate())
        self.assertFalse(self.item1.is_active)
        self.item1.refresh_from_db()
        self.assertFalse(self.item1.is_active)
        # Test deactivating an already inactive item
        self.assertFalse(self.item2.is_active)
        self.assertFalse(self.item2.deactivate())

    def test_activate_method(self):
        """Test the activate method."""
        self.assertFalse(self.item2.is_active)
        self.assertTrue(self.item2.activate())
        self.assertTrue(self.item2.is_active)
        self.item2.refresh_from_db()
        self.assertTrue(self.item2.is_active)
        # Test activating an already active item
        self.assertTrue(self.item1.is_active)
        self.assertFalse(self.item1.activate())


class ItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.item = Item.objects.create(
            name='Initial Item',
            description='An item for testing views.',
            is_active=True
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        """Test the Item list view (main page)."""
        response = self.client.get(reverse('item_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/item/list.html')
        self.assertIn('items', response.context) # Check if 'items' is in context
        self.assertContains(response, 'Items Dashboard') # Check for specific text
        self.assertIsInstance(response.context['items'].first(), Item) # Check object type

    def test_item_table_partial_view(self):
        """Test the HTMX partial for the item table."""
        response = self.client.get(reverse('item_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/item/_item_table.html')
        self.assertIn('items', response.context)
        self.assertContains(response, self.item.name) # Check if item name is in the partial

    def test_create_view_get(self):
        """Test GET request to the create item form."""
        response = self.client.get(reverse('item_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/item/_item_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Item') # Check for form title

    def test_create_view_post_success(self):
        """Test successful POST request to create a new item."""
        data = {
            'name': 'New Test Item',
            'description': 'Description for new item.',
            'is_active': 'on', # Checkbox value for True
        }
        response = self.client.post(reverse('item_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX successful creation should return 204 No Content
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemList')
        
        # Verify object was created in the database
        self.assertTrue(Item.objects.filter(name='New Test Item').exists())
        
        # Check messages framework output
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item "New Test Item" added successfully.')

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data for item creation."""
        data = {
            'name': 'ab', # Too short, will trigger validation error
            'description': '',
            'is_active': 'on',
        }
        response = self.client.post(reverse('item_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX invalid form submission should return 400 Bad Request
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'dashboard/item/_item_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Item name must be at least 3 characters long.') # Check for error message

    def test_update_view_get(self):
        """Test GET request to the update item form."""
        response = self.client.get(reverse('item_edit', args=[self.item.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/item/_item_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Item')
        self.assertEqual(response.context['form'].instance, self.item)

    def test_update_view_post_success(self):
        """Test successful POST request to update an existing item."""
        updated_data = {
            'name': 'Updated Item Name',
            'description': 'Updated description.',
            'is_active': 'off', # Change status
        }
        response = self.client.post(reverse('item_edit', args=[self.item.id]), updated_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemList')
        
        # Verify object was updated in the database
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, 'Updated Item Name')
        self.assertFalse(self.item.is_active)
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item "Updated Item Name" updated successfully.')

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation page."""
        response = self.client.get(reverse('item_delete', args=[self.item.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/item/_item_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.item)
        self.assertContains(response, f'Are you sure you want to delete the item "<strong>{self.item.name}</strong>"?')

    def test_delete_view_post_success(self):
        """Test successful POST request to delete an item."""
        item_to_delete = Item.objects.create(name="Delete Me", description="temp")
        initial_count = Item.objects.count()

        response = self.client.post(reverse('item_delete', args=[item_to_delete.id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemList')
        
        # Verify object was deleted from the database
        self.assertEqual(Item.objects.count(), initial_count - 1)
        self.assertFalse(Item.objects.filter(id=item_to_delete.id).exists())
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), f'Item "{item_to_delete.name}" deleted successfully.')
```

## Step 5: HTMX and Alpine.js Integration

The generated code demonstrates the core principles:

*   **HTMX for Dynamic Interactions:**
    *   The "Add New Item", "Edit", and "Delete" buttons use `hx-get` to fetch forms/confirmations into the `#modalContent` div.
    *   Form submissions (`hx-post`) on the modal forms trigger `hx-swap="none"` and rely on `HX-Trigger` headers (`refreshItemList`) from Django views to update the main list.
    *   The `itemTable-container` uses `hx-trigger="load, refreshItemList from:body"` and `hx-get="{% url 'item_table_partial' %}"` to initially load the table and refresh it after any CRUD operation.
    *   Error handling for invalid forms (status 400) re-renders the form content within the modal.

*   **Alpine.js for UI State Management:**
    *   The main modal `div` uses `x-data`, `x-show`, and `@click.self` (and `@click.away` on `modalContent`) for simple modal visibility and closing logic.
    *   A global `modalHandler` object is conceptually defined in the `extra_js` block to allow button clicks to interact with the modal's Alpine.js state.
    *   `x-init` is used to clear modal content when it's closed.

*   **DataTables for List Views:**
    *   The `_item_table.html` partial contains a standard `<table>` element with an `id="itemTable"`.
    *   The `extra_js` block in `list.html` includes JavaScript to initialize DataTables on the `#itemTable` *after* HTMX has swapped in the new table content (`htmx:afterSwap` event listener). This ensures DataTables works correctly with dynamic content.

*   **No Full Page Reloads:** All CRUD operations are designed to occur within modals and refresh only the necessary table portion via HTMX, avoiding full page reloads for a smoother user experience.

## Final Notes

This comprehensive plan provides a blueprint for migrating a single logical module from ASP.NET to Django. The approach can be systematically applied to other modules, focusing on:

1.  **Automated Schema Extraction:** Tools could be developed (or existing ones utilized) to parse legacy database schema definitions and automatically generate Django model definitions (`managed = False`, `db_table`).
2.  **Automated UI Component Mapping:** While complex, patterns can be identified in ASP.NET UI controls (e.g., GridView) to suggest Django template structures (`DataTables`, `{% for %}`). Conversational AI can guide a user to select appropriate Django equivalents for ASP.NET controls.
3.  **Business Logic Migration:** This remains the most manual part, but the "Fat Model, Thin View" principle helps by isolating this logic. AI can assist in translating C# methods to Python, but human review for idiomatic Python and Django patterns is crucial.
4.  **Test-Driven Migration:** Developing tests before or alongside migration ensures functional parity and prevents regressions. Automation can scaffold test files, but writing comprehensive test cases will require understanding of the original application's behavior.

By following these patterns, organizations can achieve a modern, maintainable Django application with significant improvements in performance, scalability, and developer experience, while minimizing the risks and costs associated with manual, unguided migrations. The transition to HTMX and Alpine.js drastically simplifies the frontend stack, removing the need for complex JavaScript frameworks and making maintenance easier for full-stack Django developers.