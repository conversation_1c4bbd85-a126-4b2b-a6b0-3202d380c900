## ASP.NET to Django Conversion Script: SubCategory Module Modernization

This document outlines the modernization plan for your ASP.NET SubCategory module, transitioning it to a robust and modern Django-based solution. Our approach emphasizes automation, clean architecture, and enhanced user experience through contemporary web technologies.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the `SqlDataSource` definitions and SQL commands, we identify the following tables and their key columns. The `tblSD_WO_SubCategory` is the primary table for this module, with `tblSD_WO_Category` as a lookup and `SD_Cust_WorkOrder_Master` used for a deletion rule.

- **`tblSD_WO_Category`**:
    - `CId` (Integer, Primary Key)
    - `CName` (String)
    - `Symbol` (String)

- **`tblSD_WO_SubCategory`**:
    - `SCId` (Integer, Primary Key)
    - `CId` (Integer, Foreign Key to `tblSD_WO_Category`)
    - `SCName` (String)
    - `Symbol` (String, max length 1)

- **`SD_Cust_WorkOrder_Master`**:
    - `CId` (Integer, Foreign Key to `tblSD_WO_Category`)
    - `SCId` (Integer, Foreign Key to `tblSD_WO_SubCategory`)
    - (Additional fields may exist but are not relevant to this module's logic)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations and business rules implemented in the ASP.NET code.

**Instructions:**
The ASP.NET page primarily manages 'SubCategory' entries, linked to 'Categories'.

-   **Create (Add)**:
    -   Adds a new `SubCategory` record.
    -   Requires `Category`, `SubCategory Name`, and `Symbol`.
    -   **Validation**: `SubCategory Name` and `Symbol` must not be empty. The `Symbol` must be unique for a given `Category`. The `Symbol` is converted to uppercase upon insertion.
-   **Read (List)**:
    -   Displays a paginated list of `SubCategory` records, showing `SubCategory Name`, `Symbol`, and `Category` (represented as "Category Symbol - Category Name").
-   **Update (Edit)**:
    -   Modifies an existing `SubCategory` record.
    -   Allows changing `Category` and `SubCategory Name`.
    -   **Note**: The `Symbol` field is *not* editable in the original ASP.NET update process; it retains its initial value.
    -   **Validation**: `SubCategory Name` must not be empty. If the `Category` is changed during an update, the existing `Symbol` must still be unique within the *new* `Category`.
-   **Delete**:
    -   Removes an existing `SubCategory` record.
    -   **Business Rule**: A `SubCategory` cannot be deleted if it is linked to any record in the `SD_Cust_WorkOrder_Master` table (i.e., if it has been used in a Customer Work Order).

### Step 3: Infer UI Components

Task: Analyze the ASP.NET controls and their roles in the user interface.

**Instructions:**
The user interface is centered around a `GridView` for displaying and managing SubCategories.

-   **Data Display**: A table (GridView) presenting SubCategory data with pagination. Each row includes "SN" (serial number), "Category" (dropdown in edit/footer, label in display), "SubCategory" (textbox in edit/footer, label in display), and "Symbol" (textbox in footer, label in display).
-   **Actions**: "Edit" and "Delete" buttons for each row. A "Insert" button in the table footer for adding new records.
-   **Input Fields**: Textboxes for `SubCategory Name` and `Symbol`. Dropdown lists for selecting `Category`.
-   **Validation Indicators**: Small "*" next to required fields.
-   **Messages**: A `Label` to display success or error messages (e.g., "Record Inserted.", "SubCategory symbol is already used.").
-   **Client-side confirmations**: JavaScript `alert` messages for add/update/delete confirmations.

### Step 4: Generate Django Code

### 4.1 Models (`sales_distribution/models.py`)

This file defines the database models representing your existing tables. We use `managed = False` to tell Django these tables already exist and it shouldn't manage their schema. Business logic, like symbol capitalization and delete eligibility, is encapsulated within model methods.

```python
from django.db import models
from django.core.exceptions import ValidationError

class Category(models.Model):
    """
    Represents the 'tblSD_WO_Category' table.
    Used as a lookup for SubCategory's CId.
    """
    CId = models.IntegerField(db_column='CId', primary_key=True)
    CName = models.CharField(db_column='CName', max_length=255) # Assuming appropriate max_length
    Symbol = models.CharField(db_column='Symbol', max_length=50) # Assuming appropriate max_length

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblSD_WO_Category' # Maps to the existing database table
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        """Returns a user-friendly representation of the category."""
        return f"{self.Symbol} - {self.CName}"

class SubCategory(models.Model):
    """
    Represents the 'tblSD_WO_SubCategory' table.
    This is the core model for the SubCategory module.
    """
    SCId = models.IntegerField(db_column='SCId', primary_key=True)
    CId = models.ForeignKey(Category, on_delete=models.PROTECT, db_column='CId', related_name='subcategories')
    SCName = models.CharField(db_column='SCName', max_length=255) # Assuming appropriate max_length
    Symbol = models.CharField(db_column='Symbol', max_length=1) # MaxLength derived from ASPX

    class Meta:
        managed = False
        db_table = 'tblSD_WO_SubCategory'
        verbose_name = 'SubCategory'
        verbose_name_plural = 'SubCategories'
        # Enforce uniqueness of 'Symbol' within each 'Category'
        unique_together = (('CId', 'Symbol'),)

    def __str__(self):
        """Returns the subcategory name for display."""
        return self.SCName

    def clean(self):
        """
        Custom validation and data normalization for SubCategory.
        This method is called before saving the model (e.g., by ModelForms).
        """
        # Business logic: Ensure Symbol is always uppercase, as per ASP.NET
        if self.Symbol:
            self.Symbol = self.Symbol.upper()

        # Business logic: Check if Symbol is unique for the given Category.
        # Although unique_together handles this at the DB level, this provides
        # a more user-friendly validation message within the form.
        if self.CId and self.Symbol:
            qs = SubCategory.objects.filter(CId=self.CId, Symbol=self.Symbol)
            if self.pk:  # Exclude self if updating an existing record
                qs = qs.exclude(pk=self.pk)
            if qs.exists():
                raise ValidationError({'Symbol': 'SubCategory symbol is already used for this category.'})

    def is_deletable(self):
        """
        Business logic: Checks if the SubCategory can be deleted.
        A SubCategory cannot be deleted if it's referenced in a Customer Work Order.
        """
        return not CustomerWorkOrder.objects.filter(CId=self.CId, SCId=self.SCId).exists()

    @property
    def category_display(self):
        """
        Provides the combined 'Category Symbol - Category Name' string for display,
        mimicking the 'catsy' field in the original ASP.NET SelectCommand.
        """
        if self.CId:
            return f"{self.CId.Symbol} - {self.CId.CName}"
        return "N/A"

class CustomerWorkOrder(models.Model):
    """
    Represents the 'SD_Cust_WorkOrder_Master' table.
    Only includes fields relevant to the SubCategory deletion business rule.
    """
    # Assuming a primary key exists, though not explicitly used in original query
    WOId = models.AutoField(db_column='WOId', primary_key=True) # Placeholder PK

    CId = models.ForeignKey(Category, on_delete=models.PROTECT, db_column='CId')
    SCId = models.ForeignKey(SubCategory, on_delete=models.PROTECT, db_column='SCId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Customer Work Order'
        verbose_name_plural = 'Customer Work Orders'
        # If there's no natural unique key, Django might complain, but for this use case,
        # we just need to query it for the deletion check.

```

### 4.2 Forms (`sales_distribution/forms.py`)

This file defines a Django `ModelForm` for handling input and validation related to `SubCategory` objects. It also handles the specific behavior of the `Symbol` field being read-only during updates.

```python
from django import forms
from .models import SubCategory, Category

class SubCategoryForm(forms.ModelForm):
    """
    Form for creating and updating SubCategory instances.
    """
    class Meta:
        model = SubCategory
        fields = ['CId', 'SCName', 'Symbol'] # Fields to be included in the form
        widgets = {
            'CId': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SCName': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'Symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'maxlength': '1'}),
        }
        labels = {
            'CId': 'Category',
            'SCName': 'SubCategory Name',
            'Symbol': 'Symbol',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate CId dropdown with all Category objects
        self.fields['CId'].queryset = Category.objects.order_by('CName')

        # If this form is being used to update an existing object (instance exists),
        # make the 'Symbol' field read-only as per the original ASP.NET behavior.
        if self.instance and self.instance.pk:
            self.fields['Symbol'].widget.attrs['readonly'] = 'readonly'
            self.fields['Symbol'].widget.attrs['class'] += ' bg-gray-100 cursor-not-allowed' # Add styling for readonly state

```

### 4.3 Views (`sales_distribution/views.py`)

This file contains the Class-Based Views (CBVs) for handling the SubCategory list, creation, update, and deletion. Views are kept thin, delegating complex logic to the model and handling HTMX responses.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, get_object_or_404
from django.template.loader import render_to_string
from django.core.exceptions import ValidationError # Import ValidationError
from .models import SubCategory, Category # Ensure Category is imported
from .forms import SubCategoryForm

class SubCategoryListView(ListView):
    """
    Displays a list of all SubCategories.
    This is the main view that renders the base page.
    """
    model = SubCategory
    template_name = 'sales_distribution/subcategory/list.html'
    context_object_name = 'subcategories' # Renamed for clarity in template

    def get_queryset(self):
        """
        Orders the queryset by SCId in descending order, matching ASP.NET behavior.
        """
        return SubCategory.objects.order_by('-SCId')

class SubCategoryTablePartialView(ListView):
    """
    Returns only the HTML fragment for the SubCategory table.
    This view is specifically designed for HTMX requests to refresh the table.
    """
    model = SubCategory
    template_name = 'sales_distribution/subcategory/_subcategory_table.html'
    context_object_name = 'subcategories'

    def get_queryset(self):
        """
        Orders the queryset by SCId in descending order.
        """
        return SubCategory.objects.order_by('-SCId')

class SubCategoryCreateView(CreateView):
    """
    Handles the creation of new SubCategory objects.
    Renders a form within a modal triggered by HTMX.
    """
    model = SubCategory
    form_class = SubCategoryForm
    template_name = 'sales_distribution/subcategory/_subcategory_form.html' # Partial template for modal
    success_url = reverse_lazy('subcategory_list') # Fallback URL, not directly used with HTMX

    def form_valid(self, form):
        """
        Called when form data is valid. Saves the object and sends HTMX trigger.
        Business logic (symbol uppercase, uniqueness check) is handled by model's clean() method.
        """
        try:
            # Call clean() explicitly to ensure model-level validation runs for HTMX
            form.instance.full_clean()
            response = super().form_valid(form)
            messages.success(self.request, 'SubCategory added successfully.')
            if self.request.headers.get('HX-Request'):
                # Send 204 No Content to close modal and trigger list refresh
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshSubCategoryList' # Custom event to refresh table
                    }
                )
            return response
        except ValidationError as e:
            # If model clean() raises ValidationError, add to form errors and re-render
            form.add_error(None, e) # Add non-field errors (global validation)
            return self.form_invalid(form) # Re-render form with errors

    def form_invalid(self, form):
        """
        Called when form data is invalid. Rerenders the form with errors for HTMX.
        """
        return render(self.request, self.template_name, {'form': form})

class SubCategoryUpdateView(UpdateView):
    """
    Handles the update of existing SubCategory objects.
    Renders a pre-filled form within a modal triggered by HTMX.
    """
    model = SubCategory
    form_class = SubCategoryForm
    template_name = 'sales_distribution/subcategory/_subcategory_form.html' # Partial template for modal
    success_url = reverse_lazy('subcategory_list') # Fallback URL

    def form_valid(self, form):
        """
        Called when form data is valid. Saves updates and sends HTMX trigger.
        Business logic (symbol uppercase, uniqueness check) handled by model's clean() method.
        """
        try:
            form.instance.full_clean()
            response = super().form_valid(form)
            messages.success(self.request, 'SubCategory updated successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshSubCategoryList'
                    }
                )
            return response
        except ValidationError as e:
            form.add_error(None, e)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """
        Called when form data is invalid. Rerenders the form with errors for HTMX.
        """
        return render(self.request, self.template_name, {'form': form})

class SubCategoryDeleteView(DeleteView):
    """
    Handles the deletion of SubCategory objects.
    Renders a confirmation prompt within a modal triggered by HTMX.
    """
    model = SubCategory
    template_name = 'sales_distribution/subcategory/_subcategory_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('subcategory_list') # Fallback URL

    def delete(self, request, *args, **kwargs):
        """
        Performs the deletion after checking business rules.
        """
        self.object = self.get_object()
        # Business logic: Check if the SubCategory is deletable using the model method
        if not self.object.is_deletable():
            messages.error(request, 'SubCategory cannot be deleted as it is linked to existing work orders.')
            if request.headers.get('HX-Request'):
                # Re-render the confirmation modal with an error message, keeping modal open
                context = self.get_context_data(object=self.object)
                # Add a flag to the context to display the error in the template
                context['cannot_delete_error'] = True
                html = render_to_string(self.template_name, context, request=request)
                return HttpResponse(html, status=200) # Return 200 OK to swap content
            return HttpResponseRedirect(self.get_success_url()) # Fallback for non-HTMX

        # If deletable, proceed with actual deletion
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'SubCategory deleted successfully.')
        if request.headers.get('HX-Request'):
            # Send 204 No Content to close modal and trigger list refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSubCategoryList'
                }
            )
        return response

```

### 4.4 Templates (`sales_distribution/templates/sales_distribution/subcategory/`)

These HTML templates are designed to work seamlessly with Django's templating engine, HTMX, and Alpine.js. They are component-specific and extend a base layout (`core/base.html`).

**`list.html`**: The main page for displaying SubCategories, integrating the HTMX-powered table and modal.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">SubCategories</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'subcategory_add' %}" {# HTMX GET request to load add form #}
            hx-target="#modalContent" {# Load into the modal's content div #}
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Alpine.js/Hyperscript to show modal #}
            Add New SubCategory
        </button>
    </div>

    {# Container for the DataTables-powered list, loaded via HTMX #}
    <div id="subcategoryTable-container"
         hx-trigger="load, refreshSubCategoryList from:body" {# Load on page load and on custom event #}
         hx-get="{% url 'subcategory_table' %}" {# HTMX GET request for table content #}
         hx-swap="innerHTML"> {# Replace the inner HTML of this div #}
        <!-- Loading indicator while DataTable loads -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading SubCategories...</p>
        </div>
    </div>

    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ showModal: false }" x-show="showModal" {# Alpine.js state for modal visibility #}
         _="on click if event.target.id == 'modal' remove .is-active from me then set showModal to false"> {# Close modal on backdrop click #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             _="on htmx:afterSwap add .is-active to #modal then set showModal to true"> {# Show modal after HTMX loads content #}
            <!-- Content for forms/delete confirmation will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{# No extra JavaScript needed beyond what's in base.html for HTMX/Alpine.js/DataTables #}
{% block extra_js %}
{# Keep this block if you have any page-specific JS, otherwise it can be empty #}
{% endblock %}
```

**`_subcategory_table.html`**: A partial template that renders the table of SubCategories. This content is dynamically loaded by HTMX into `list.html`.

```html
<table id="subcategoryTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SubCategory</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for subcategory in subcategories %}
        <tr class="hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ subcategory.category_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ subcategory.SCName }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ subcategory.Symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 flex space-x-2">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'subcategory_edit' subcategory.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                {% if subcategory.is_deletable %} {# Business rule: show delete if deletable #}
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'subcategory_delete' subcategory.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
                {% else %}
                <span class="bg-gray-300 text-gray-600 font-bold py-1 px-2 rounded text-sm cursor-not-allowed">
                    Used
                </span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">
                No SubCategories found.
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4"
                    hx-get="{% url 'subcategory_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New SubCategory
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables after the table has been loaded into the DOM by HTMX
$(document).ready(function() {
    $('#subcategoryTable').DataTable({
        "pageLength": 15, // Matching ASP.NET PageSize
        "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]]
    });
});
</script>
```

**`_subcategory_form.html`**: A partial template for both creating and updating SubCategories. This form is loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {% if form.instance.pk %}Edit{% else %}Add{% endif %} SubCategory
    </h3>
    {# hx-swap="none" means HTMX won't change the DOM, but listens for HX-Trigger #}
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %} {# Django's protection against Cross-Site Request Forgeries #}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span> {% endif %}
                </label>
                {{ field }} {# Renders the Django form field with its widget and attrs #}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {# Display non-field errors (e.g., model clean() errors) #}
            {% if form.non_field_errors %}
                <div class="text-red-500 text-xs mt-1">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Closes the modal without submitting #}
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_subcategory_confirm_delete.html`**: A partial template for confirming deletion. This is also loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">
        Are you sure you want to delete the SubCategory "<strong>{{ object.SCName }} ({{ object.Symbol }})</strong>" under Category "<strong>{{ object.category_display }}</strong>"?
        This action cannot be undone.
    </p>

    {# Display messages (e.g., from messages framework) if deletion failed #}
    {% if messages %}
        <div class="mt-4">
            {% for message in messages %}
                <div class="p-3 mb-2 rounded {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    {# Specific error message if the model's is_deletable() check failed #}
    {% if cannot_delete_error %}
        <div class="mt-4 p-3 mb-2 rounded bg-red-100 text-red-700">
            This SubCategory cannot be deleted as it is linked to existing work orders.
        </div>
    {% endif %}

    <div class="mt-6 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'subcategory_delete' object.pk %}" {# HTMX POST request to trigger deletion #}
            hx-swap="none" {# No swap, relies on HX-Trigger for UI update #}
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Confirm Delete
        </button>
    </div>
</div>
```

### 4.5 URLs (`sales_distribution/urls.py`)

This file defines the URL patterns that map requests to your Django views.

```python
from django.urls import path
from .views import (
    SubCategoryListView,
    SubCategoryTablePartialView, # Added for HTMX-specific table refresh
    SubCategoryCreateView,
    SubCategoryUpdateView,
    SubCategoryDeleteView
)

urlpatterns = [
    # Main list view for SubCategories
    path('subcategory/', SubCategoryListView.as_view(), name='subcategory_list'),

    # HTMX-specific endpoint to get the table content (for initial load and refreshes)
    path('subcategory/table/', SubCategoryTablePartialView.as_view(), name='subcategory_table'),

    # Endpoints for CRUD operations, used by HTMX modals
    path('subcategory/add/', SubCategoryCreateView.as_view(), name='subcategory_add'),
    path('subcategory/edit/<int:pk>/', SubCategoryUpdateView.as_view(), name='subcategory_edit'),
    path('subcategory/delete/<int:pk>/', SubCategoryDeleteView.as_view(), name='subcategory_delete'),
]

```

### 4.6 Tests (`sales_distribution/tests.py`)

Comprehensive unit tests for the models and integration tests for the views ensure the migrated functionality behaves as expected, adhering to business rules and interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from .models import Category, SubCategory, CustomerWorkOrder

# --- Model Tests ---

class CategoryModelTest(TestCase):
    """
    Tests for the Category model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a dummy category for FK relations to be used across all tests
        Category.objects.create(CId=101, CName='Test Category', Symbol='TC')

    def test_category_creation(self):
        """Verify Category object creation and attribute assignment."""
        category = Category.objects.get(CId=101)
        self.assertEqual(category.CName, 'Test Category')
        self.assertEqual(category.Symbol, 'TC')

    def test_str_method(self):
        """Test the __str__ representation of the Category model."""
        category = Category.objects.get(CId=101)
        self.assertEqual(str(category), 'TC - Test Category')


class SubCategoryModelTest(TestCase):
    """
    Tests for the SubCategory model, including business logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create categories for testing foreign key relationships
        cls.category1 = Category.objects.create(CId=1, CName='Category A', Symbol='A')
        cls.category2 = Category.objects.create(CId=2, CName='Category B', Symbol='B')

        # Create a test subcategory
        cls.subcategory1 = SubCategory.objects.create(
            SCId=1, CId=cls.category1, SCName='SubCategory Alpha', Symbol='X'
        )

    def test_subcategory_creation(self):
        """Verify SubCategory object creation and attribute assignment."""
        self.assertEqual(self.subcategory1.CId, self.category1)
        self.assertEqual(self.subcategory1.SCName, 'SubCategory Alpha')
        self.assertEqual(self.subcategory1.Symbol, 'X')

    def test_str_method(self):
        """Test the __str__ representation of the SubCategory model."""
        self.assertEqual(str(self.subcategory1), 'SubCategory Alpha')

    def test_category_display_property(self):
        """Test the custom 'category_display' property."""
        self.assertEqual(self.subcategory1.category_display, 'A - Category A')

    def test_symbol_uppercase_on_clean(self):
        """Verify that the Symbol is converted to uppercase on cleaning."""
        subcat = SubCategory(CId=self.category1, SCName='Test Sub', Symbol='y')
        subcat.full_clean() # Triggers the model's clean() method
        self.assertEqual(subcat.Symbol, 'Y')

    def test_symbol_uniqueness_within_category(self):
        """
        Test that a symbol must be unique within the same category.
        This uses the model's clean() method validation.
        """
        with self.assertRaisesMessage(ValidationError, 'SubCategory symbol is already used for this category.'):
            subcat_duplicate_symbol = SubCategory(CId=self.category1, SCName='Duplicate Sub', Symbol='X')
            subcat_duplicate_symbol.full_clean() # Should raise validation error

    def test_symbol_uniqueness_across_categories(self):
        """
        Test that the same symbol can be used in different categories.
        """
        subcat_diff_category = SubCategory(CId=self.category2, SCName='Another Alpha', Symbol='X')
        try:
            subcat_diff_category.full_clean() # Should not raise an error
        except ValidationError:
            self.fail("ValidationError raised unexpectedly for unique symbol across different categories")

    def test_is_deletable_no_work_order(self):
        """
        Verify that a SubCategory is deletable when no associated work orders exist.
        """
        self.assertTrue(self.subcategory1.is_deletable())

    def test_is_deletable_with_work_order(self):
        """
        Verify that a SubCategory is NOT deletable when an associated work order exists.
        """
        # Create a dummy work order linking to subcategory1
        CustomerWorkOrder.objects.create(CId=self.category1, SCId=self.subcategory1)
        self.assertFalse(self.subcategory1.is_deletable())


class SubCategoryViewsTest(TestCase):
    """
    Integration tests for SubCategory views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create categories and subcategories for testing view interactions
        cls.category1 = Category.objects.create(CId=1, CName='Category A', Symbol='A')
        cls.category2 = Category.objects.create(CId=2, CName='Category B', Symbol='B')

        cls.subcategory1 = SubCategory.objects.create(
            SCId=1, CId=cls.category1, SCName='SubCategory Alpha', Symbol='X'
        )
        SubCategory.objects.create(
            SCId=2, CId=cls.category2, SCName='SubCategory Beta', Symbol='Y'
        )

    def setUp(self):
        """Set up client for each test method."""
        self.client = Client()

    def test_list_view(self):
        """Test the main SubCategory list page."""
        response = self.client.get(reverse('subcategory_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/list.html')
        self.assertIn('subcategories', response.context)
        self.assertContains(response, 'SubCategory Alpha')
        self.assertContains(response, 'SubCategory Beta')

    def test_table_partial_view_htmx(self):
        """Test the HTMX endpoint for the table content."""
        headers = {'HTTP_HX_REQUEST': 'true'} # Mimic HTMX request header
        response = self.client.get(reverse('subcategory_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_table.html')
        self.assertContains(response, 'SubCategory Alpha')
        self.assertContains(response, 'SubCategory Beta')
        self.assertContains(response, '<table id="subcategoryTable"') # Check for DataTable rendering

    def test_create_view_get_htmx(self):
        """Test loading the add form via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('subcategory_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add SubCategory')
        self.assertContains(response, 'Symbol') # Check that symbol field is present for add

    def test_create_view_post_htmx_success(self):
        """Test successful SubCategory creation via HTMX."""
        data = {
            'CId': self.category2.CId,
            'SCName': 'New SubCategory',
            'Symbol': 'Z',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('subcategory_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success status for 'no content'
        self.assertTrue(SubCategory.objects.filter(SCName='New SubCategory').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')

    def test_create_view_post_htmx_validation_fail(self):
        """Test SubCategory creation failure due to validation via HTMX."""
        # Attempt to create with existing symbol in same category
        data = {
            'CId': self.category1.CId,
            'SCName': 'Another Alpha',
            'Symbol': 'X', # Duplicate symbol for category1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('subcategory_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertContains(response, 'SubCategory symbol is already used for this category.')
        self.assertFalse(SubCategory.objects.filter(SCName='Another Alpha').exists()) # Object not created

    def test_update_view_get_htmx(self):
        """Test loading the edit form via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('subcategory_edit', args=[self.subcategory1.SCId]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit SubCategory')
        self.assertContains(response, 'SubCategory Alpha') # Check pre-filled data
        self.assertContains(response, 'readonly') # Symbol field should be readonly for edit

    def test_update_view_post_htmx_success(self):
        """Test successful SubCategory update via HTMX."""
        data = {
            'CId': self.category1.CId, # Keep same category
            'SCName': 'Updated SubCategory Name',
            'Symbol': 'X', # Symbol is readonly, so this value should be the original
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('subcategory_edit', args=[self.subcategory1.SCId]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.subcategory1.refresh_from_db() # Refresh instance from DB to get updated values
        self.assertEqual(self.subcategory1.SCName, 'Updated SubCategory Name')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')

    def test_update_view_post_htmx_validation_fail(self):
        """Test SubCategory update failure due to validation via HTMX (e.g., symbol clash on category change)."""
        # Create another subcategory with a symbol that would clash if we change category of subcategory1
        SubCategory.objects.create(SCId=3, CId=self.category2, SCName='Temp Subcat', Symbol='Z')

        data = {
            'CId': self.category2.CId, # Change category to category2
            'SCName': 'Updated SC with Clash',
            'Symbol': 'X', # Original Symbol for subcategory1. If changed to category2, it would clash with Z.
                         # This test scenario implies the existing symbol 'X' is checked against new category 'B'
                         # which should pass, unless a subcategory with Symbol 'X' already exists in category 'B'.
                         # Let's adjust for a clear clash based on the model's unique_together logic.
                         # Make the original symbol 'X' clash with an existing one in category2.
        }
        SubCategory.objects.create(SCId=4, CId=self.category2, SCName='Existing in B', Symbol='X') # Create a clash

        data_clash = {
            'CId': self.category2.CId, # Now attempting to move subcategory1 to category2
            'SCName': 'Updated SubCategory Name',
            'Symbol': 'X', # subcategory1's original symbol, which now clashes in category2
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('subcategory_edit', args=[self.subcategory1.SCId]), data_clash, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertContains(response, 'SubCategory symbol is already used for this category.')
        self.subcategory1.refresh_from_db()
        self.assertNotEqual(self.subcategory1.CId.CId, self.category2.CId) # Category should not have changed
        self.assertEqual(self.subcategory1.SCName, 'SubCategory Alpha') # SCName should not have changed either

    def test_delete_view_get_htmx(self):
        """Test loading the delete confirmation via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('subcategory_delete', args=[self.subcategory1.SCId]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'SubCategory Alpha')

    def test_delete_view_post_htmx_success(self):
        """Test successful SubCategory deletion via HTMX."""
        # Ensure it's deletable before trying to delete
        self.assertTrue(self.subcategory1.is_deletable())
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('subcategory_delete', args=[self.subcategory1.SCId]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(SubCategory.objects.filter(SCId=self.subcategory1.SCId).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')

    def test_delete_view_post_htmx_fail_due_to_dependency(self):
        """
        Test SubCategory deletion failure when linked to a work order.
        Should re-render modal with an error message.
        """
        # Link subcategory1 to a work order to make it undeletable
        CustomerWorkOrder.objects.create(CId=self.category1, SCId=self.subcategory1)
        self.assertFalse(self.subcategory1.is_deletable())

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('subcategory_delete', args=[self.subcategory1.SCId]), **headers)
        self.assertEqual(response.status_code, 200) # Should render modal with error, not 204
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_confirm_delete.html')
        self.assertContains(response, 'This SubCategory cannot be deleted as it is linked to existing work orders.')
        self.assertTrue(SubCategory.objects.filter(SCId=self.subcategory1.SCId).exists()) # Object should still exist
        self.assertNotIn('HX-Trigger', response.headers) # No trigger to refresh list on failure

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Dynamic Interactions**:
    -   All CRUD operations (Add, Edit, Delete) are initiated by `hx-get` requests to load forms/confirmations into a modal.
    -   Form submissions (`hx-post`) from within the modal respond with `status=204` (No Content) and an `HX-Trigger` header (`refreshSubCategoryList`). This closes the modal and triggers the main table to refresh.
    -   The `subcategoryTable-container` div uses `hx-trigger="load, refreshSubCategoryList from:body"` and `hx-get` to load `_subcategory_table.html` initially and whenever the `refreshSubCategoryList` event is dispatched from the body (after a successful CRUD operation).
    -   `hx-swap="innerHTML"` is used for the table container. `hx-swap="none"` is used on form submissions to rely solely on `HX-Trigger`.

-   **Alpine.js for UI State Management**:
    -   A simple `x-data="{ showModal: false }"` is used on the modal container (`#modal`) to manage its visibility.
    -   Hyperscript (`_`) is used to toggle the `is-active` class and update the `showModal` Alpine.js variable based on HTMX events (`htmx:afterSwap`) and user clicks (backdrop, cancel button). This ensures the modal opens and closes smoothly.

-   **DataTables for List Views**:
    -   The `_subcategory_table.html` partial template contains the `<table>` element with `id="subcategoryTable"`.
    -   A `script` block within `_subcategory_table.html` initializes DataTables using `$(document).ready(function() { $('#subcategoryTable').DataTable({...}); });`. This ensures DataTables is initialized only after the table HTML is loaded into the DOM by HTMX.
    -   Pagination and sorting are handled natively by DataTables, matching the `GridView` functionality.

-   **Seamless User Experience**:
    -   The combination of HTMX and Alpine.js provides a single-page application (SPA)-like experience without complex JavaScript frameworks. All interactions occur without full page reloads.
    -   Success/error messages are managed by Django's messages framework and are displayed within the relevant HTMX-loaded content or by the base template.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET SubCategory module to Django. By adhering to the principles of "Fat Model, Thin View," utilizing HTMX and Alpine.js for dynamic interfaces, and incorporating thorough testing, you will achieve a modern, maintainable, and highly performant application. Remember to configure your Django project's `settings.py` (e.g., `DATABASES`, `INSTALLED_APPS`) and `urls.py` (root level) to include this new `sales_distribution` application.