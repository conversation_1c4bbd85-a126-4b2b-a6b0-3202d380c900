## ASP.NET to Django Conversion Script: Dashboard Module

This modernization plan outlines the conversion of the ASP.NET Dashboard module to a modern Django-based solution. Given the minimal ASP.NET code provided (an empty ASPX page and an empty C# code-behind `Page_Load` method), there is no explicit business logic, UI components, or database interactions to extract. Therefore, this plan will demonstrate the principles of modern Django migration by *inferring* a common use case for a "Dashboard" within a "Sales Distribution" context, which typically involves managing core entities.

For demonstration purposes, we will assume the "Dashboard" serves as an entry point for managing a `Product` entity within the `SalesDistribution` module, enabling common CRUD (Create, Read, Update, Delete) operations. This allows us to showcase the `Fat Model, Thin View` architecture, HTMX, Alpine.js, and DataTables as requested.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code (empty `.aspx` and `.aspx.cs` files) contains no database-related elements. There are no `SqlDataSource` controls, connection strings, or explicit SQL commands.

**Inference for Modernization:**
To demonstrate the full migration process, we will *assume* the existence of a database table representing a core entity in a Sales Distribution system, for example, a `Product` table. This table will serve as the basis for our Django model.

**Assumed Table Name:** `tbl_products`
**Assumed Columns:**
- `product_id` (Primary Key, Integer)
- `product_name` (String, e.g., `NVARCHAR(255)`)
- `description` (Text, e.g., `NTEXT` or `NVARCHAR(MAX)`)
- `price` (Decimal, e.g., `DECIMAL(10, 2)`)
- `stock_quantity` (Integer, e.g., `INT`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code has an empty `Page_Load` method and no explicit UI controls (like `GridViews`, `Textboxes`, `Buttons`) or event handlers that would indicate CRUD operations.

**Inference for Modernization:**
Since a "Dashboard" often provides quick access to managing core data, we will *infer* that the module should support the following standard CRUD operations for the assumed `Product` entity:

- **Create:** Ability to add new product records.
- **Read:** Ability to view a list of all products, with searching, sorting, and pagination.
- **Update:** Ability to modify existing product details.
- **Delete:** Ability to remove product records.

No specific validation logic can be extracted, so standard Django form validation will be applied, including required fields.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The provided ASP.NET `.aspx` file only contains content placeholders and a reference to `loadingNotifier.js`. There are no `GridView`, `TextBox`, `DropDownList`, `Button`, or `LinkButton` controls present in the markup.

**Inference for Modernization:**
Based on the assumed CRUD functionality for a `Product` entity and the requirements for a modern Django application (HTMX, Alpine.js, DataTables), we will infer the following UI components:

- **Product List View:** A main page displaying all products in a table, powered by DataTables for client-side functionality.
- **Add Product Form:** A modal form (loaded via HTMX) for creating new products.
- **Edit Product Form:** A modal form (loaded via HTMX) for updating existing products.
- **Delete Product Confirmation:** A modal confirmation dialog (loaded via HTMX) for deleting products.
- **Dynamic Interactions:** All form submissions and table refreshes will utilize HTMX for a smooth, single-page application feel. Alpine.js will manage modal visibility.

The `loadingNotifier.js` hints at client-side loading feedback, which will be naturally handled by HTMX's `hx-indicator` patterns and simple loading messages.

### Step 4: Generate Django Code

We will structure this code within a new Django application, for example, named `sales_distribution`.

#### 4.1 Models
**File:** `sales_distribution/models.py`

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
- Name the model `Product`.
- Define fields with appropriate Django field types based on inferred column data types.
- Use `db_column` to map to the assumed SQL column names.
- Set `managed = False` and `db_table = 'tbl_products'` in the `Meta` class.
- Include model methods for business logic (fat model approach) – for `Product`, we might add methods for calculating stock value, etc.

```python
from django.db import models

class Product(models.Model):
    product_id = models.AutoField(db_column='product_id', primary_key=True) # Assuming auto-incrementing PK
    product_name = models.CharField(db_column='product_name', max_length=255, verbose_name='Product Name')
    description = models.TextField(db_column='description', blank=True, null=True, verbose_name='Description')
    price = models.DecimalField(db_column='price', max_digits=10, decimal_places=2, verbose_name='Unit Price')
    stock_quantity = models.IntegerField(db_column='stock_quantity', verbose_name='Stock Quantity')

    class Meta:
        managed = False  # Important: Django will not create or modify this table's schema.
        db_table = 'tbl_products'
        verbose_name = 'Product'
        verbose_name_plural = 'Products'

    def __str__(self):
        return self.product_name
        
    def get_stock_value(self):
        """
        Business logic: Calculates the total value of the current stock for this product.
        """
        return self.price * self.stock_quantity

    def is_in_stock(self):
        """
        Business logic: Checks if the product is currently in stock.
        """
        return self.stock_quantity > 0

    def decrease_stock(self, quantity):
        """
        Business logic: Decreases the stock quantity.
        Raises ValueError if stock goes below zero.
        """
        if self.stock_quantity - quantity < 0:
            raise ValueError("Not enough stock available.")
        self.stock_quantity -= quantity
        self.save()
        return self.stock_quantity

    def increase_stock(self, quantity):
        """
        Business logic: Increases the stock quantity.
        """
        self.stock_quantity += quantity
        self.save()
        return self.stock_quantity
```

#### 4.2 Forms
**File:** `sales_distribution/forms.py`

**Task:** Define a Django form for user input.

**Instructions:**
- Create a `ModelForm` for the `Product` model.
- Include editable fields and add widgets with Tailwind CSS classes for styling.
- Implement any specific form validation logic as needed (e.g., ensuring price is positive).

```python
from django import forms
from .models import Product

class ProductForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = ['product_name', 'description', 'price', 'stock_quantity']
        widgets = {
            'product_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'price': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'stock_quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price is not None and price <= 0:
            raise forms.ValidationError("Price must be a positive value.")
        return price

    def clean_stock_quantity(self):
        stock_quantity = self.cleaned_data.get('stock_quantity')
        if stock_quantity is not None and stock_quantity < 0:
            raise forms.ValidationError("Stock quantity cannot be negative.")
        return stock_quantity
```

#### 4.3 Views
**File:** `sales_distribution/views.py`

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
- Define `ListView`, `CreateView`, `UpdateView`, `DeleteView` for the `Product` model.
- Use `Product` as the model, `ProductForm` for forms, and set appropriate `template_name` and `success_url`.
- Add success messages using `messages.success`.
- Keep views thin (5-15 lines) by moving business logic to models (already done in `Product` model methods).
- Implement a `ProductTablePartialView` to serve the DataTables content via HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Product
from .forms import ProductForm

class ProductListView(ListView):
    model = Product
    template_name = 'sales_distribution/product/list.html'
    context_object_name = 'products' # Will be used in list.html to refer to the product list

class ProductTablePartialView(ListView):
    model = Product
    template_name = 'sales_distribution/product/_product_table.html'
    context_object_name = 'products'

    def get_queryset(self):
        # Example of adding simple filtering logic if needed (Fat Model principle can extend this)
        return Product.objects.all().order_by('product_name') # Always order for consistency

class ProductCreateView(CreateView):
    model = Product
    form_class = ProductForm
    template_name = 'sales_distribution/product/form.html'
    success_url = reverse_lazy('product_list') # Redirect after successful form submission

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Product added successfully.')
        if self.request.headers.get('HX-Request'): # Check if it's an HTMX request
            return HttpResponse(
                status=204, # No Content response for HTMX
                headers={
                    'HX-Trigger': 'refreshProductList' # Custom HTMX trigger to refresh table
                }
            )
        return response # Normal redirect for non-HTMX requests

class ProductUpdateView(UpdateView):
    model = Product
    form_class = ProductForm
    template_name = 'sales_distribution/product/form.html'
    success_url = reverse_lazy('product_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Product updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProductList'
                }
            )
        return response

class ProductDeleteView(DeleteView):
    model = Product
    template_name = 'sales_distribution/product/confirm_delete.html'
    success_url = reverse_lazy('product_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Product deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProductList'
                }
            )
        return response
```

#### 4.4 Templates
**Directory:** `sales_distribution/templates/sales_distribution/product/`

**Task:** Create templates for each view, adhering to HTMX and DataTables requirements.

**Instructions:**
- `list.html`: Main page, extends `core/base.html`, holds the container for the DataTables partial.
- `_product_table.html`: Partial template for the DataTables table, loaded via HTMX.
- `_product_form.html`: Partial template for Create/Update forms, loaded into a modal via HTMX.
- `_product_confirm_delete.html`: Partial template for Delete confirmation, loaded into a modal via HTMX.

**File:** `sales_distribution/templates/sales_distribution/product/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Product List</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'product_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Product
        </button>
    </div>
    
    <div id="productList-container"
         hx-trigger="load, refreshProductList from:body"
         hx-get="{% url 'product_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading products...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:w-full">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js components here if needed, or global setup -->
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });

    // Handle HTMX close modal trigger
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Reinitialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSettle', function(event) {
        if (event.target.id === 'productList-container') {
            if ($.fn.DataTable.isDataTable('#productTable')) {
                $('#productTable').DataTable().destroy();
            }
            $('#productTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers"
            });
        }
    });
</script>
{% endblock %}
```

**File:** `sales_distribution/templates/sales_distribution/product/_product_table.html`

```html
<div class="overflow-x-auto">
    <table id="productTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Quantity</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for product in products %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ product.product_name }}</td>
                <td class="py-3 px-4 text-sm text-gray-500 max-w-xs truncate">{{ product.description|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">${{ product.price|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ product.stock_quantity }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 px-3 py-1 rounded-md border border-indigo-600 hover:bg-indigo-50 transition duration-150 ease-in-out mr-2"
                        hx-get="{% url 'product_edit' product.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 px-3 py-1 rounded-md border border-red-600 hover:bg-red-50 transition duration-150 ease-in-out"
                        hx-get="{% url 'product_delete' product.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No products found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTable initialization is handled by the parent list.html's htmx:afterSettle event listener
    // This ensures DataTable is initialized ONLY after the table content is loaded and replaces existing instances.
</script>
```

**File:** `sales_distribution/templates/sales_distribution/product/_product_form.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Product</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <ul class="mt-1 text-sm text-red-600">
                {% for error in field.errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Product
            </button>
        </div>
    </form>
</div>
```

**File:** `sales_distribution/templates/sales_distribution/product/_product_confirm_delete.html`

```html
<div class="p-6 text-center">
    <svg class="mx-auto h-12 w-12 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
    <h3 class="mt-5 text-lg leading-6 font-medium text-gray-900">Delete Product</h3>
    <div class="mt-2">
        <p class="text-sm text-gray-500">Are you sure you want to delete the product "{{ product.product_name }}"? This action cannot be undone.</p>
    </div>
    <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
        <form hx-post="{% url 'product_delete' product.pk %}" hx-swap="none" class="sm:col-start-2">
            {% csrf_token %}
            <button 
                type="submit" 
                class="inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm">
                Delete
            </button>
        </form>
        <button 
            type="button" 
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
    </div>
</div>
```

#### 4.5 URLs
**File:** `sales_distribution/urls.py`

**Task:** Define URL patterns for the views.

**Instructions:**
- Create paths for list, create, update, delete, and the partial table view.
- Use appropriate naming patterns and consistent URL structure.
- Remember to include this `urls.py` in your project's main `urls.py` (e.g., `path('sales/', include('sales_distribution.urls'))`).

```python
from django.urls import path
from .views import (
    ProductListView, 
    ProductCreateView, 
    ProductUpdateView, 
    ProductDeleteView,
    ProductTablePartialView
)

urlpatterns = [
    # Main list view (full page load, but content is loaded via HTMX)
    path('products/', ProductListView.as_view(), name='product_list'),
    
    # HTMX partial views for CRUD operations and table refresh
    path('products/table/', ProductTablePartialView.as_view(), name='product_table'),
    path('products/add/', ProductCreateView.as_view(), name='product_add'),
    path('products/edit/<int:pk>/', ProductUpdateView.as_view(), name='product_edit'),
    path('products/delete/<int:pk>/', ProductDeleteView.as_view(), name='product_delete'),
]
```

#### 4.6 Tests
**File:** `sales_distribution/tests.py`

**Task:** Write comprehensive tests for the model and views.

**Instructions:**
- Include unit tests for model methods and properties.
- Add integration tests for all views (list, create, update, delete) including HTMX interactions.
- Aim for at least 80% test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Product
from .forms import ProductForm

class ProductModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a single test product for all tests that don't modify it
        cls.product1 = Product.objects.create(
            product_name='Test Product 1',
            description='A description for test product 1.',
            price=19.99,
            stock_quantity=100
        )
  
    def test_product_creation(self):
        product = Product.objects.get(product_id=self.product1.product_id)
        self.assertEqual(product.product_name, 'Test Product 1')
        self.assertEqual(product.price, 19.99)
        self.assertEqual(product.stock_quantity, 100)
        
    def test_product_name_label(self):
        product = Product.objects.get(product_id=self.product1.product_id)
        field_label = product._meta.get_field('product_name').verbose_name
        self.assertEqual(field_label, 'Product Name')

    def test_get_stock_value_method(self):
        product = Product.objects.get(product_id=self.product1.product_id)
        self.assertEqual(product.get_stock_value(), 19.99 * 100) # 1999.00
        
    def test_is_in_stock_method(self):
        product = Product.objects.get(product_id=self.product1.product_id)
        self.assertTrue(product.is_in_stock())
        product.stock_quantity = 0
        product.save()
        self.assertFalse(product.is_in_stock())

    def test_decrease_stock_method(self):
        product = Product.objects.get(product_id=self.product1.product_id) # Reload to ensure fresh state
        initial_stock = product.stock_quantity
        product.decrease_stock(10)
        self.assertEqual(product.stock_quantity, initial_stock - 10)
        
        with self.assertRaises(ValueError):
            product.decrease_stock(initial_stock) # Should fail if stock becomes negative

    def test_increase_stock_method(self):
        product = Product.objects.get(product_id=self.product1.product_id)
        initial_stock = product.stock_quantity
        product.increase_stock(50)
        self.assertEqual(product.stock_quantity, initial_stock + 50)

class ProductViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.product1 = Product.objects.create(
            product_name='Test Product A',
            description='Description A',
            price=10.00,
            stock_quantity=50
        )
        cls.product2 = Product.objects.create(
            product_name='Test Product B',
            description='Description B',
            price=20.00,
            stock_quantity=100
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('product_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/product/list.html')
        self.assertIn('products', response.context) # Check if context contains products
        # Verify that the partial table is loaded via HTMX
        response_table = self.client.get(reverse('product_table'))
        self.assertEqual(response_table.status_code, 200)
        self.assertTemplateUsed(response_table, 'sales_distribution/product/_product_table.html')
        self.assertContains(response_table, 'Test Product A')
        self.assertContains(response_table, 'Test Product B')
        
    def test_create_view_get(self):
        response = self.client.get(reverse('product_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/product/form.html')
        self.assertIsInstance(response.context['form'], ProductForm)
        
    def test_create_view_post_success(self):
        initial_count = Product.objects.count()
        data = {
            'product_name': 'New Product C',
            'description': 'A newly added product.',
            'price': 25.50,
            'stock_quantity': 200,
        }
        response = self.client.post(reverse('product_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response
        self.assertEqual(Product.objects.count(), initial_count + 1)
        self.assertTrue(Product.objects.filter(product_name='New Product C').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProductList')

    def test_create_view_post_invalid(self):
        initial_count = Product.objects.count()
        data = {
            'product_name': '', # Invalid, name is required
            'description': 'Invalid product.',
            'price': -10.00, # Invalid price
            'stock_quantity': 50,
        }
        response = self.client.post(reverse('product_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'sales_distribution/product/form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Price must be a positive value.')
        self.assertEqual(Product.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        response = self.client.get(reverse('product_edit', args=[self.product1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/product/form.html')
        self.assertIsInstance(response.context['form'], ProductForm)
        self.assertEqual(response.context['form'].instance, self.product1)
        
    def test_update_view_post_success(self):
        updated_name = 'Updated Product A'
        data = {
            'product_name': updated_name,
            'description': 'Updated description.',
            'price': 12.50,
            'stock_quantity': 60,
        }
        response = self.client.post(reverse('product_edit', args=[self.product1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.product1.refresh_from_db()
        self.assertEqual(self.product1.product_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProductList')

    def test_update_view_post_invalid(self):
        original_name = self.product1.product_name
        data = {
            'product_name': '', # Invalid
            'description': 'Invalid update.',
            'price': 10.00,
            'stock_quantity': -5, # Invalid
        }
        response = self.client.post(reverse('product_edit', args=[self.product1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/product/form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Stock quantity cannot be negative.')
        self.product1.refresh_from_db()
        self.assertEqual(self.product1.product_name, original_name) # Ensure no change

    def test_delete_view_get(self):
        response = self.client.get(reverse('product_delete', args=[self.product1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/product/confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete the product "Test Product A"?')
        
    def test_delete_view_post_success(self):
        product_to_delete_pk = self.product2.pk
        initial_count = Product.objects.count()
        response = self.client.post(reverse('product_delete', args=[product_to_delete_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Product.objects.count(), initial_count - 1)
        self.assertFalse(Product.objects.filter(pk=product_to_delete_pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProductList')

    def test_delete_view_post_product_not_found(self):
        initial_count = Product.objects.count()
        response = self.client.post(reverse('product_delete', args=[999]), HTTP_HX_REQUEST='true') # Non-existent PK
        self.assertEqual(response.status_code, 404) # Django's default for not found
        self.assertEqual(Product.objects.count(), initial_count) # No change in count
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates and views already include the necessary HTMX and Alpine.js integration:

- **HTMX for Dynamic Updates:**
    - The main `list.html` uses `hx-trigger="load, refreshProductList from:body"` and `hx-get="{% url 'product_table' %}" hx-swap="innerHTML"` to initially load and dynamically refresh the product list table.
    - `Add`, `Edit`, and `Delete` buttons on the table use `hx-get` to fetch modal content (`_product_form.html` or `_product_confirm_delete.html`) into `#modalContent`.
    - Form submissions (`hx-post`) from the modals are handled by Django views which return `204 No Content` and `HX-Trigger: refreshProductList` headers, prompting the `productList-container` to refresh itself without a full page reload.
    - The `_product_table.html` is designed to be a partial, containing only the table structure.

- **Alpine.js for UI State Management:**
    - A simple `x-data` attribute with `on click add .is-active to #modal` is used to show the modal when buttons are clicked.
    - `on click if event.target.id == 'modal' remove .is-active from me` handles closing the modal when clicking outside the content.
    - An `alpine:init` event listener is included in `list.html` for any more complex UI state management if needed.

- **DataTables for List Views:**
    - The `_product_table.html` contains the `<table>` element with the ID `productTable`.
    - JavaScript within `list.html` (in `extra_js` block) listens for HTMX `afterSettle` events on the `productList-container` and reinitializes DataTables, ensuring it works correctly after the table content is dynamically updated. It first destroys any existing DataTable instance to prevent re-initialization errors.

- **No Custom JavaScript Requirements:**
    - All dynamic interactions are managed by HTMX and Alpine.js attributes directly in the HTML, or by the minimal DataTables initialization script. No complex custom JavaScript files are required beyond the CDN imports (which are assumed to be in `core/base.html`).

## Final Notes

- **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete examples related to the `Product` entity within a `SalesDistribution` module.
- **DRY Templates:** The use of partial templates (`_product_table.html`, `_product_form.html`, `_product_confirm_delete.html`) ensures that reusable components are not duplicated.
- **Fat Models, Thin Views:** Business logic, such as calculating stock value or managing stock levels, is encapsulated within the `Product` model methods, keeping the Django views concise and focused solely on handling HTTP requests and responses (typically 5-15 lines for the core logic).
- **Comprehensive Tests:** Both `ProductModelTest` (unit tests for business logic) and `ProductViewsTest` (integration tests for web interactions, including HTMX) are provided to ensure robust code quality and maintainability.
- **Automation Focus:** This structured approach is ideal for AI-assisted automation. An AI tool could:
    1. Scan ASP.NET pages for patterns (e.g., `GridView`, `SqlDataSource`) to infer entities and CRUD operations.
    2. Map inferred schema and operations to Django models, forms, views, URLs, and tests using pre-defined templates and rules.
    3. Generate the Python and HTML code blocks directly.
    4. Provide prompts for reviewing and refining the generated components.

This detailed plan provides a clear, actionable roadmap for modernizing the ASP.NET Dashboard module to a Django-based system, focusing on automation-friendly patterns and delivering significant business benefits through improved performance, maintainability, and scalability.