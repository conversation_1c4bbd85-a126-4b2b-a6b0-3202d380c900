## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

Given the minimal ASP.NET input, which primarily shows an empty page and an empty `Page_Load` method in the code-behind, we must infer the underlying database schema and functionality based on the page's name: `WO_SubCategory_Dashboard`. This page name strongly suggests it's related to managing "Work Order Sub-Categories."

We will proceed by assuming a standard CRUD (Create, Read, Update, Delete) functionality for a `WoSubCategory` entity, and inferring a plausible database table structure.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is empty and lacks any `SqlDataSource` or explicit database commands, we are inferring the database schema based on the ASP.NET page name: `WO_SubCategory_Dashboard`.

Based on this name, we assume a database table responsible for storing Work Order Sub-Category information.

-   **Table Name:** `tbl_WoSubCategory` (a common ASP.NET pattern uses `tbl_` prefix).
-   **Columns Inferred:**
    *   `id`: Primary key (integer)
    *   `sub_category_name`: Name of the sub-category (string, e.g., 'Installation', 'Repair').
    *   `description`: A longer description of the sub-category (text).
    *   `is_active`: Boolean flag to indicate if the sub-category is currently active.
    *   `created_at`: Timestamp for when the record was created.
    *   `updated_at`: Timestamp for when the record was last updated.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given that this is a "Dashboard" page for "WO_SubCategory", it's highly probable that the core functionality revolves around managing these sub-category records. We infer the following standard backend functionalities:

-   **Create:** Ability to add new Work Order Sub-Categories.
-   **Read:** Ability to view a list of all existing Work Order Sub-Categories. This would be the primary function of a "Dashboard".
-   **Update:** Ability to modify details of an existing Work Order Sub-Category.
-   **Delete:** Ability to remove a Work Order Sub-Category.

No specific validation logic can be extracted, so standard Django form validation will be applied, including required fields.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Based on the inferred CRUD functionality for a "Dashboard," we anticipate the following UI components will be needed in the Django application:

-   **Data Display:** A table (Django's equivalent of an ASP.NET GridView) to list all `WoSubCategory` records, with columns for `sub_category_name`, `description`, `is_active`, and an "Actions" column for edit/delete buttons. This table will be powered by DataTables for client-side search, sort, and pagination.
-   **Input Forms:** Input fields (Django's equivalent of ASP.NET TextBoxes or DropDownLists) for `sub_category_name`, `description`, and a checkbox for `is_active` for creating and updating records. These will be rendered within modals for a smooth user experience.
-   **Action Buttons:** Buttons (e.g., "Add New Sub-Category", "Edit", "Delete") to trigger the respective CRUD operations. These will leverage HTMX to load forms and confirm dialogs dynamically.
-   **Modals:** A general-purpose modal will be used to display the add/edit forms and delete confirmation dialogs, improving user flow and preventing full page reloads.

No client-side JavaScript was found in the ASP.NET, so we will implement modern dynamic interactions purely with HTMX and Alpine.js.

## Step 4: Generate Django Code

We will create a new Django application, for example, named `work_order`, to house the `WoSubCategory` module.

### 4.1 Models (`work_order/models.py`)

Task: Create a Django model based on the inferred database schema.

## Instructions:

The `WoSubCategory` model will represent the `tbl_WoSubCategory` table in the database.

```python
from django.db import models
from django.utils import timezone

class WoSubCategory(models.Model):
    """
    Represents a Work Order Sub-Category in the system.
    Maps to the existing tbl_WoSubCategory database table.
    """
    id = models.AutoField(db_column='id', primary_key=True)
    sub_category_name = models.CharField(
        db_column='sub_category_name', 
        max_length=255, 
        unique=True, 
        verbose_name="Sub-Category Name"
    )
    description = models.TextField(
        db_column='description', 
        blank=True, 
        null=True, 
        verbose_name="Description"
    )
    is_active = models.BooleanField(
        db_column='is_active', 
        default=True, 
        verbose_name="Is Active"
    )
    created_at = models.DateTimeField(
        db_column='created_at', 
        auto_now_add=True, 
        verbose_name="Created At"
    )
    updated_at = models.DateTimeField(
        db_column='updated_at', 
        auto_now=True, 
        verbose_name="Updated At"
    )

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tbl_WoSubCategory'  # Name of the existing database table
        verbose_name = 'Work Order Sub-Category'
        verbose_name_plural = 'Work Order Sub-Categories'
        ordering = ['sub_category_name'] # Default ordering for lists

    def __str__(self):
        """String representation for a WoSubCategory object."""
        return self.sub_category_name
        
    def activate(self):
        """Business logic: Activates the sub-category."""
        if not self.is_active:
            self.is_active = True
            self.save(update_fields=['is_active', 'updated_at'])
            return True
        return False

    def deactivate(self):
        """Business logic: Deactivates the sub-category."""
        if self.is_active:
            self.is_active = False
            self.save(update_fields=['is_active', 'updated_at'])
            return True
        return False

    def get_display_status(self):
        """Returns a user-friendly status string."""
        return "Active" if self.is_active else "Inactive"
```

### 4.2 Forms (`work_order/forms.py`)

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for the `WoSubCategory` model, including fields for `sub_category_name`, `description`, and `is_active`. Widgets will be styled with Tailwind CSS classes.

```python
from django import forms
from .models import WoSubCategory

class WoSubCategoryForm(forms.ModelForm):
    """
    Form for creating and updating WoSubCategory instances.
    """
    class Meta:
        model = WoSubCategory
        fields = ['sub_category_name', 'description', 'is_active']
        widgets = {
            'sub_category_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'e.g., Installation'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'rows': 3,
                'placeholder': 'Provide a brief description of the sub-category'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'
            }),
        }
        labels = {
            'sub_category_name': 'Sub-Category Name',
            'description': 'Description',
            'is_active': 'Active',
        }
        
    def clean_sub_category_name(self):
        """
        Custom validation for sub_category_name to ensure uniqueness case-insensitively
        and trim whitespace.
        """
        sub_category_name = self.cleaned_data['sub_category_name'].strip()
        
        # Check for uniqueness, excluding the current instance during update
        if self.instance.pk:
            if WoSubCategory.objects.filter(sub_category_name__iexact=sub_category_name).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("A sub-category with this name already exists.")
        else:
            if WoSubCategory.objects.filter(sub_category_name__iexact=sub_category_name).exists():
                raise forms.ValidationError("A sub-category with this name already exists.")
        
        return sub_category_name
```

### 4.3 Views (`work_order/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

Four main CBVs will handle the CRUD operations: `ListView`, `CreateView`, `UpdateView`, and `DeleteView`. An additional `ListView` will render the DataTables partial via HTMX. Each view is kept thin, adhering to the 5-15 line limit for methods.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import WoSubCategory
from .forms import WoSubCategoryForm

class WoSubCategoryListView(ListView):
    """
    Displays the dashboard for Work Order Sub-Categories.
    Renders the main page that will load the table via HTMX.
    """
    model = WoSubCategory
    template_name = 'work_order/wosubcategory/list.html'
    context_object_name = 'wosubcategories' # Not directly used by this view, but good practice.

class WoSubCategoryTablePartialView(ListView):
    """
    Renders the partial HTML for the WoSubCategory list table.
    Designed to be fetched via HTMX.
    """
    model = WoSubCategory
    template_name = 'work_order/wosubcategory/_wosubcategory_table.html'
    context_object_name = 'wosubcategories'

class WoSubCategoryCreateView(CreateView):
    """
    Handles creation of new Work Order Sub-Categories.
    """
    model = WoSubCategory
    form_class = WoSubCategoryForm
    template_name = 'work_order/wosubcategory/_wosubcategory_form.html' # Use partial template for modal
    success_url = reverse_lazy('wosubcategory_list') # Not strictly used with HTMX, but for Django fallback

    def form_valid(self, form):
        """
        Processes valid form submission for creation.
        Returns a no-content response with HX-Trigger for HTMX.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order Sub-Category added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshWoSubCategoryList'})
        return response

class WoSubCategoryUpdateView(UpdateView):
    """
    Handles updating existing Work Order Sub-Categories.
    """
    model = WoSubCategory
    form_class = WoSubCategoryForm
    template_name = 'work_order/wosubcategory/_wosubcategory_form.html' # Use partial template for modal
    context_object_name = 'wosubcategory'
    success_url = reverse_lazy('wosubcategory_list') # Not strictly used with HTMX, but for Django fallback

    def form_valid(self, form):
        """
        Processes valid form submission for update.
        Returns a no-content response with HX-Trigger for HTMX.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order Sub-Category updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshWoSubCategoryList'})
        return response

class WoSubCategoryDeleteView(DeleteView):
    """
    Handles deletion of Work Order Sub-Categories.
    """
    model = WoSubCategory
    template_name = 'work_order/wosubcategory/_wosubcategory_confirm_delete.html' # Use partial for modal
    context_object_name = 'wosubcategory'
    success_url = reverse_lazy('wosubcategory_list') # Not strictly used with HTMX, but for Django fallback

    def delete(self, request, *args, **kwargs):
        """
        Processes delete request.
        Returns a no-content response with HX-Trigger for HTMX.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order Sub-Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshWoSubCategoryList'})
        return response
```

### 4.4 Templates (`work_order/templates/work_order/wosubcategory/`)

Task: Create templates for each view.

## Instructions:

Templates will extend `core/base.html` (not included here) and use HTMX for dynamic content loading, especially for the DataTables list and modal forms.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block title %}Work Order Sub-Categories{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Work Order Sub-Categories</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105"
            hx-get="{% url 'wosubcategory_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i>Add New Sub-Category
        </button>
    </div>
    
    <div id="wosubcategoryTable-container"
         hx-trigger="load, refreshWoSubCategoryList from:body"
         hx-get="{% url 'wosubcategory_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Work Order Sub-Categories...</p>
        </div>
    </div>
    
    <!-- Global Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300"
             _="on modal.active transition transform scale-100 opacity-100">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally in base.html
    // Specific Alpine.js components can be defined here if needed for this page
    document.addEventListener('htmx:afterSwap', function(event) {
        // If content swapped into modalContent, ensure Alpine.js re-initializes itself.
        // This is often handled by Alpine's own observers, but good to be aware.
        if (event.target.id === 'modalContent') {
            // Check if DataTable was loaded and re-initialize it
            if ($(event.detail.target).find('.dataTable').length) {
                $(event.detail.target).find('.dataTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": true
                });
            }
        }
    });

    // Close modal when an HX-Trigger is received
    document.body.addEventListener('refreshWoSubCategoryList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

#### `_wosubcategory_table.html` (Partial)

```html
<table id="wosubcategoryTable" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm dataTable">
    <thead class="bg-gray-100">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Sub-Category Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in wosubcategories %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700 font-medium">{{ obj.sub_category_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.description|default:"-" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {{ obj.get_display_status }}
                </span>
            </td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-lg mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'wosubcategory_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-lg transition duration-150 ease-in-out"
                    hx-get="{% url 'wosubcategory_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No Work Order Sub-Categories found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization must happen *after* the table HTML is loaded into the DOM
    // This script block is part of the HTMX response, so it will run immediately.
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#wosubcategoryTable')) {
            $('#wosubcategoryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true // Add responsiveness
            });
        }
    });
</script>
```

#### `_wosubcategory_form.html` (Partial)

```html
<div class="p-8">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Work Order Sub-Category
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on--after-request="if(event.detail.successful) { console.log('Form submission successful'); document.getElementById('modal').classList.remove('is-active'); } else { console.error('Form submission failed'); }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {% if field.widget_type == 'checkbox' %}
                <div class="flex items-center">
                    {{ field }}
                    <span class="ml-2 text-sm text-gray-900">{{ field.label }}</span>
                </div>
                {% else %}
                    {{ field }}
                {% endif %}
                {% if field.errors %}
                <p class="mt-2 text-sm text-red-600">{{ field.errors }}</p>
                {% endif %}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out transform hover:scale-105">
                <i class="fas fa-save mr-2"></i>Save Sub-Category
            </button>
        </div>
    </form>
</div>
```

#### `_wosubcategory_confirm_delete.html` (Partial)

```html
<div class="p-8 text-center">
    <div class="text-red-500 text-5xl mb-6">
        <i class="fas fa-exclamation-triangle"></i>
    </div>
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">
        Are you sure you want to delete the Work Order Sub-Category: 
        <span class="font-bold text-red-700">"{{ wosubcategory.sub_category_name }}"</span>?
    </p>
    <p class="text-gray-500 text-sm mb-8">This action cannot be undone.</p>
    
    <div class="flex justify-center space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'wosubcategory_delete' wosubcategory.pk %}"
            hx-swap="none"
            hx-on--after-request="if(event.detail.successful) { console.log('Deletion successful'); document.getElementById('modal').classList.remove('is-active'); }"
            class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out transform hover:scale-105">
            <i class="fas fa-trash-alt mr-2"></i>Delete
        </button>
    </div>
</div>
```

### 4.5 URLs (`work_order/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

This file will contain the URL configurations for the `work_order` Django app.

```python
from django.urls import path
from .views import (
    WoSubCategoryListView, 
    WoSubCategoryTablePartialView,
    WoSubCategoryCreateView, 
    WoSubCategoryUpdateView, 
    WoSubCategoryDeleteView
)

urlpatterns = [
    # Main dashboard view
    path('wosubcategories/', WoSubCategoryListView.as_view(), name='wosubcategory_list'),
    
    # HTMX partial for the DataTables content
    path('wosubcategories/table/', WoSubCategoryTablePartialView.as_view(), name='wosubcategory_table'),
    
    # CRUD operations, loaded into modal via HTMX
    path('wosubcategories/add/', WoSubCategoryCreateView.as_view(), name='wosubcategory_add'),
    path('wosubcategories/edit/<int:pk>/', WoSubCategoryUpdateView.as_view(), name='wosubcategory_edit'),
    path('wosubcategories/delete/<int:pk>/', WoSubCategoryDeleteView.as_view(), name='wosubcategory_delete'),
]
```
*Note: Remember to include this `work_order/urls.py` in your project's main `urls.py` (e.g., `path('app/', include('work_order.urls'))`).*

### 4.6 Tests (`work_order/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `WoSubCategory` model and integration tests for all `WoSubCategory` views will ensure functionality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib import messages
from .models import WoSubCategory

class WoSubCategoryModelTest(TestCase):
    """
    Tests for the WoSubCategory model, ensuring correct field behavior and business logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test instance for use across all model tests
        cls.subcategory1 = WoSubCategory.objects.create(
            sub_category_name='Installation',
            description='Installation services for new equipment.',
            is_active=True
        )
        cls.subcategory2 = WoSubCategory.objects.create(
            sub_category_name='Repair',
            description='Repair services for existing equipment.',
            is_active=False
        )
  
    def test_wosubcategory_creation(self):
        """Verify WoSubCategory objects are created correctly."""
        self.assertEqual(self.subcategory1.sub_category_name, 'Installation')
        self.assertEqual(self.subcategory1.description, 'Installation services for new equipment.')
        self.assertTrue(self.subcategory1.is_active)
        self.assertIsNotNone(self.subcategory1.created_at)
        self.assertIsNotNone(self.subcategory1.updated_at)
        
    def test_sub_category_name_label(self):
        """Verify the verbose name for sub_category_name field."""
        field_label = self.subcategory1._meta.get_field('sub_category_name').verbose_name
        self.assertEqual(field_label, 'Sub-Category Name')
        
    def test_str_method(self):
        """Verify the __str__ method returns the sub_category_name."""
        self.assertEqual(str(self.subcategory1), 'Installation')

    def test_activate_method(self):
        """Test the activate business logic method."""
        initial_status = self.subcategory2.is_active
        self.assertFalse(initial_status)
        
        activated = self.subcategory2.activate()
        self.assertTrue(activated)
        self.subcategory2.refresh_from_db() # Reload to get updated state
        self.assertTrue(self.subcategory2.is_active)

        # Test activating an already active subcategory
        activated_again = self.subcategory1.activate()
        self.assertFalse(activated_again) # Should return False as no change occurred

    def test_deactivate_method(self):
        """Test the deactivate business logic method."""
        initial_status = self.subcategory1.is_active
        self.assertTrue(initial_status)
        
        deactivated = self.subcategory1.deactivate()
        self.assertTrue(deactivated)
        self.subcategory1.refresh_from_db()
        self.assertFalse(self.subcategory1.is_active)

        # Test deactivating an already inactive subcategory
        deactivated_again = self.subcategory2.deactivate()
        self.assertFalse(deactivated_again) # Should return False as no change occurred

    def test_get_display_status_method(self):
        """Test the get_display_status method."""
        self.assertEqual(self.subcategory1.get_display_status(), 'Inactive') # After deactivating it above
        self.assertEqual(self.subcategory2.get_display_status(), 'Active')   # After activating it above

class WoSubCategoryViewsTest(TestCase):
    """
    Tests for WoSubCategory views, covering CRUD operations and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for all tests
        cls.subcategory = WoSubCategory.objects.create(
            sub_category_name='Maintenance',
            description='Routine maintenance checks.',
            is_active=True
        )
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolated requests
        self.client = Client()
    
    def test_list_view(self):
        """Test the WoSubCategory list dashboard view."""
        response = self.client.get(reverse('wosubcategory_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order/wosubcategory/list.html')
        # We don't check for object presence here because the table is loaded via HTMX
        
    def test_table_partial_view(self):
        """Test the HTMX partial for the WoSubCategory table."""
        response = self.client.get(reverse('wosubcategory_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_table.html')
        self.assertContains(response, 'Maintenance') # Check if the object is present in the table HTML
        self.assertContains(response, 'id="wosubcategoryTable"') # Ensure DataTable element is there

    def test_create_view_get(self):
        """Test GET request to the create form view."""
        response = self.client.get(reverse('wosubcategory_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        """Test successful POST request to create a new WoSubCategory."""
        data = {
            'sub_category_name': 'Inspection',
            'description': 'Safety inspection services.',
            'is_active': True,
        }
        response = self.client.post(reverse('wosubcategory_add'), data, HTTP_HX_REQUEST='true')
        
        # Check for HTMX 204 No Content response on success
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWoSubCategoryList')
        
        # Verify object was created in the database
        self.assertTrue(WoSubCategory.objects.filter(sub_category_name='Inspection').exists())
        
        # Check for success message (messages framework requires session)
        # client.post doesn't automatically process messages, would need a follow-up request
        # For simplicity, we rely on the 204 status and database check for HTMX flows.
    
    def test_create_view_post_invalid(self):
        """Test POST request with invalid data for creation."""
        data = {
            'sub_category_name': '', # Invalid: required field missing
            'description': 'Invalid attempt.',
            'is_active': True,
        }
        response = self.client.post(reverse('wosubcategory_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX will swap the form back with errors, so status is 200
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')
        self.assertContains(response, 'This field is required.') # Check for validation error message

    def test_update_view_get(self):
        """Test GET request to the update form view."""
        response = self.client.get(reverse('wosubcategory_edit', args=[self.subcategory.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.subcategory)
        
    def test_update_view_post_success(self):
        """Test successful POST request to update an existing WoSubCategory."""
        updated_name = 'Updated Maintenance'
        data = {
            'sub_category_name': updated_name,
            'description': 'Updated routine maintenance checks.',
            'is_active': False,
        }
        response = self.client.post(reverse('wosubcategory_edit', args=[self.subcategory.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWoSubCategoryList')
        
        self.subcategory.refresh_from_db()
        self.assertEqual(self.subcategory.sub_category_name, updated_name)
        self.assertFalse(self.subcategory.is_active)

    def test_update_view_post_invalid(self):
        """Test POST request with invalid data for update."""
        existing_subcategory = WoSubCategory.objects.create(sub_category_name='Existing Sub', description='...', is_active=True)
        
        data = {
            'sub_category_name': self.subcategory.sub_category_name, # Duplicate name
            'description': 'Attempt to create duplicate.',
            'is_active': True,
        }
        response = self.client.post(reverse('wosubcategory_edit', args=[existing_subcategory.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')
        self.assertContains(response, 'A sub-category with this name already exists.')

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation view."""
        response = self.client.get(reverse('wosubcategory_delete', args=[self.subcategory.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_confirm_delete.html')
        self.assertTrue('wosubcategory' in response.context)
        self.assertEqual(response.context['wosubcategory'], self.subcategory)
        
    def test_delete_view_post_success(self):
        """Test successful POST request to delete a WoSubCategory."""
        pk_to_delete = self.subcategory.pk # Store PK before deletion
        response = self.client.post(reverse('wosubcategory_delete', args=[pk_to_delete]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWoSubCategoryList')
        
        # Verify object was deleted from the database
        self.assertFalse(WoSubCategory.objects.filter(pk=pk_to_delete).exists())

    def test_delete_view_post_non_existent(self):
        """Test POST request to delete a non-existent WoSubCategory."""
        response = self.client.post(reverse('wosubcategory_delete', args=[9999]), HTTP_HX_REQUEST='true')
        # Expect 404 since the object doesn't exist
        self.assertEqual(response.status_code, 404)
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The modernized Django application leverages HTMX and Alpine.js for a dynamic, single-page application feel without heavy JavaScript frameworks.

-   **HTMX for dynamic updates:**
    -   The main `wosubcategory_list.html` page uses `hx-get="{% url 'wosubcategory_table' %}"` to load the DataTables content into a `div` on `load` and `refreshWoSubCategoryList` custom events. This means the table is always up-to-date without a full page refresh.
    -   "Add", "Edit", and "Delete" buttons use `hx-get` to fetch the respective form/confirmation partials (`_wosubcategory_form.html` or `_wosubcategory_confirm_delete.html`) into a global modal's `modalContent` div.
    -   Form submissions (`hx-post`) on the partials return an `HTTP 204 No Content` status along with an `HX-Trigger: refreshWoSubCategoryList` header. This instructs HTMX to trigger the `refreshWoSubCategoryList` event on the `<body>`, which then causes the table container to re-fetch its content, refreshing the list automatically.
    -   The modal itself is closed via Alpine.js on successful form submission, or an `_` (hyperscript) command after a click.

-   **Alpine.js for UI state management:**
    -   A global modal structure (`#modal`) is used, controlled by Alpine.js (or simple JavaScript with `_` hyperscript). The `is-active` class toggles its visibility.
    -   The `on click if event.target.id == 'modal' remove .is-active from me` hyperscript command allows clicking outside the modal content to close it.
    -   The `on modal.active transition transform scale-100 opacity-100` adds smooth transition effects when the modal appears.

-   **DataTables for list views:**
    -   The `_wosubcategory_table.html` partial contains a standard `<table>` element with an ID (`wosubcategoryTable`).
    -   A `<script>` block *within* this partial ensures that `$(document).ready()` executes `$('#wosubcategoryTable').DataTable()` immediately after the partial is loaded and swapped into the DOM by HTMX. This initializes DataTables for searching, sorting, and pagination on the dynamically loaded content.

-   **No full page reloads:** All CRUD operations (add, edit, delete) happen within the modal and update the main list view without requiring a full page refresh, providing a modern, responsive user experience.

## Final Notes

This comprehensive plan addresses the modernization of the `WO_SubCategory_Dashboard` ASP.NET page to a robust, modern Django 5.0+ application.

-   **Assumptions:** Due to the extremely minimal ASP.NET input, we've inferred the existence of a `tbl_WoSubCategory` table and standard CRUD operations. In a real-world scenario, AI-assisted tools would analyze database schemas and more complex UI/backend code to precisely map these elements.
-   **Fat Model, Thin View:** Business logic (like `activate`/`deactivate`/`get_display_status` methods and custom form validation) is encapsulated within the `WoSubCategory` model and its form, keeping the Django views concise and focused on handling HTTP requests and responses.
-   **DRY Principles:** Template inheritance (`{% extends 'core/base.html' %}`) and partial templates (`_wosubcategory_table.html`, `_wosubcategory_form.html`, `_wosubcategory_confirm_delete.html`) are heavily utilized to avoid code duplication and promote reusability.
-   **Automation Focus:** This entire structure is designed for automation. AI can be trained to identify ASP.NET control patterns, infer database interactions, and then automatically generate these Django models, forms, views, URLs, and templates based on predefined patterns and conversion rules. The `_` (hyperscript) attributes also demonstrate a programmatic way to add JavaScript-like behavior without writing extensive JS.
-   **Business Value:** By moving to Django, the organization gains:
    -   **Modern Architecture:** A robust, scalable, and maintainable application built on a leading web framework.
    -   **Enhanced User Experience:** Dynamic interactions with HTMX and Alpine.js provide a faster, more responsive interface without complex JavaScript.
    -   **Improved Development Speed:** Django's "batteries-included" approach, combined with the structured migration and automation tools, significantly reduces development time for new features and maintenance.
    -   **Cost Efficiency:** Moving away from proprietary legacy systems reduces licensing costs and broadens the talent pool for future development.
    -   **Future-Proofing:** Adopting open-source technologies ensures long-term viability and community support.
    -   **Testability:** A strong testing framework ensures the application's reliability and stability.

This plan provides a clear, actionable blueprint for an automated transition, focusing on the core business benefits of modernization.