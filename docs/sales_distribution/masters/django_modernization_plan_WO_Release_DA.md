This comprehensive modernization plan outlines the strategic transition of your legacy ASP.NET application, "Work Order Release & Dispatch Authority," to a robust, modern Django-based solution. Our approach prioritizes automation, leverages contemporary web technologies like Django 5.0+, HTMX, Alpine.js, and DataTables, and adheres to a "fat model, thin view" architecture for maintainability and scalability.

By adopting this plan, your organization will benefit from:
- **Enhanced Performance:** Modern Django and efficient database interactions provide a faster user experience.
- **Improved Maintainability:** Clean, modular code simplifies future updates and reduces technical debt.
- **Increased Scalability:** Django's architecture is built to handle growing user loads and data volumes.
- **Streamlined Development:** Automation and clear patterns reduce manual coding effort and potential errors.
- **Better User Experience:** HTMX and Alpine.js deliver dynamic, responsive interfaces without complex JavaScript.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

The ASP.NET code interacts with two primary database tables:
- `tblHR_OfficeStaff`: This is the main table storing employee information and their permissions.
- `tblHR_Departments`: This is a lookup table used to retrieve the department symbol.

**Inferred Schema Details:**

**Table: `tblHR_OfficeStaff`**
- `EmpId`: Employee Identifier (used as `EmpNo` in the UI, likely a unique key).
- `Title`: Employee's title (e.g., Mr., Ms.).
- `EmployeeName`: Full name of the employee.
- `Department`: Foreign key referencing `tblHR_Departments.Id`.
- `WR`: Work Order Release permission (Boolean, 0 or 1).
- `DA`: Dispatch Authority permission (Boolean, 0 or 1).
- `CompId`: Company Identifier (used for filtering data by company).
- `ResignationDate`: Date of resignation (used to filter active employees; empty string implies active).
- `UserID`: User ID (used for filtering out specific system users, e.g., `UserID != '1'`).

**Table: `tblHR_Departments`**
- `Id`: Primary key for the department.
- `Symbol`: Abbreviation or symbol for the department.

## Step 2: Identify Backend Functionality

Task: Determine the data operations performed by the ASP.NET code.

The ASP.NET page provides functionality for managing employee permissions related to "Work Order Release" and "Dispatch Authority."

-   **Read (Display List):**
    -   The `fillGrid()` method retrieves a list of active employees from `tblHR_OfficeStaff`.
    -   It filters employees by `CompId`, `ResignationDate` (active employees), `EmployeeName` (excludes 'ERP'), and `UserID` (excludes '1').
    -   For each employee, it fetches their department `Symbol` from `tblHR_Departments`.
    -   It then dynamically creates a `DataTable` and binds it to `GridView2` to display the employee's name, number, department, and their current `WR` and `DA` permission states.
-   **Update (Bulk Permissions):**
    -   The `btnsubmit_Click` event handles the form submission.
    -   It iterates through each row of the displayed `GridView2`.
    -   For each employee, it reads the current state of the `chkrelease` (WR) and `chkdispatch` (DA) checkboxes.
    -   It then updates the `WR` and `DA` fields in the `tblHR_OfficeStaff` table for that specific employee and `CompId`.
    -   After all updates are processed, the page is reloaded.

**No direct "Create" or "Delete" operations are identified for employee records on this specific page.** The page's purpose is a bulk update of existing employee permissions.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

The ASP.NET page uses standard Web Forms controls to present and capture data:

-   **`asp:GridView ID="GridView2"`**: This is the primary component for displaying a tabular list of employees. It includes:
    -   `TemplateField` for "SN" (Serial Number), dynamically generated row index.
    -   `TemplateField` for "Name of Employee" showing `EmpName`.
    -   `TemplateField` for "Emp. No" showing `EmpNo`.
    -   `TemplateField` for "Department" showing `Dept`.
    -   `TemplateField` for "Release" with an `asp:CheckBox ID="chkrelease"`.
    -   `TemplateField` for "Dispatch" with an `asp:CheckBox ID="chkdispatch"`.
    -   It supports client-side styling (`CssClass="yui-datatable-theme"`) and server-side pagination (`onpageindexchanging`).
-   **`asp:Panel ID="Panel1"`**: A container for the `GridView`, providing scrolling if content overflows.
-   **`asp:Button ID="btnsubmit"`**: A standard button that triggers the server-side `btnsubmit_Click` event to save changes.

The UI's core function is to present a list of employees with checkboxes that can be toggled and then submitted as a batch update. There are no individual row-level edit/delete buttons, only a single "Submit" for the entire grid.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create Django models based on the identified database schema.

We will create two models: `Department` and `OfficeStaff`, mapping directly to `tblHR_Departments` and `tblHR_OfficeStaff` respectively. The `OfficeStaff` model will include a class method to handle the specific permission update logic, adhering to the "fat model" principle.

```python
# salesdistribution/models.py
from django.db import models

class Department(models.Model):
    """
    Represents a department from tblHR_Departments.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255) # Assuming max_length for Symbol

    class Meta:
        managed = False  # Tells Django not to manage this table's schema in migrations
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.symbol

class OfficeStaff(models.Model):
    """
    Represents an office staff member from tblHR_OfficeStaff,
    including their work order release (WR) and dispatch authority (DA) permissions.
    """
    # Assuming EmpId is a unique identifier, not Django's default PK.
    # If EmpId is the primary key in your actual database, set primary_key=True.
    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True)
    title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', null=True, blank=True)
    wr = models.BooleanField(db_column='WR', default=False)  # Work Order Release permission
    da = models.BooleanField(db_column='DA', default=False)  # Dispatch Authority permission
    comp_id = models.IntegerField(db_column='CompId')  # Company Identifier
    resignation_date = models.DateField(db_column='ResignationDate', null=True, blank=True) # Date of resignation
    user_id = models.IntegerField(db_column='UserID', null=True, blank=True) # Associated User ID

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff Member'
        verbose_name_plural = 'Office Staff Members'

    def __str__(self):
        return f"{self.full_employee_name} ({self.emp_id})"

    @property
    def full_employee_name(self):
        """Combines title and employee name for display."""
        if self.title:
            return f"{self.title}. {self.employee_name}"
        return self.employee_name

    @classmethod
    def update_employee_permissions(cls, emp_id, wr_status, da_status, company_id):
        """
        Business logic to update WR and DA permissions for a specific employee
        within a given company. This adheres to the 'fat model' principle.
        """
        try:
            # Retrieve the specific staff member using emp_id and company_id
            staff_member = cls.objects.get(emp_id=emp_id, comp_id=company_id)
            # Update the permission fields
            staff_member.wr = wr_status
            staff_member.da = da_status
            # Save the changes to the database
            staff_member.save()
            return True  # Indicate successful update
        except cls.DoesNotExist:
            return False # Indicate that the staff member was not found
        except Exception as e:
            # Log the error (in a real application)
            print(f"Error updating permissions for EmpId {emp_id}: {e}")
            return False # Indicate failure due to an exception

```

### 4.2 Forms

Task: Define a Django formset for managing bulk updates of employee permissions.

Since the ASP.NET page handles a batch update of checkboxes across multiple employees, a Django formset is the most appropriate mechanism. We will create a base form for a single row and then use `formset_factory` to create a formset for all employees.

```python
# salesdistribution/forms.py
from django import forms
from .models import OfficeStaff

class OfficeStaffPermissionForm(forms.Form):
    """
    A form representing a single row in the employee permissions grid.
    It captures the employee ID (hidden) and their Release (WR) and Dispatch (DA) permissions.
    """
    emp_id = forms.CharField(widget=forms.HiddenInput())
    wr = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-5 w-5 text-blue-600 rounded'})
    )
    da = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-5 w-5 text-blue-600 rounded'})
    )

# Create a formset factory. 'extra=0' means no empty forms are added by default.
OfficeStaffPermissionFormSet = forms.formset_factory(OfficeStaffPermissionForm, extra=0)

```

### 4.3 Views

Task: Implement a single Django view to display the employee permissions and handle the bulk update.

This view will use a `TemplateView` to render the formset and handle both GET (display) and POST (update) requests, leveraging HTMX for dynamic updates. It ensures business logic remains in the model and the view stays thin.

```python
# salesdistribution/views.py
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponseRedirect, HttpResponse
from django.shortcuts import render
from django.db import transaction

from .models import OfficeStaff, Department
from .forms import OfficeStaffPermissionFormSet

class OfficeStaffPermissionView(TemplateView):
    """
    Manages the display and bulk update of Work Order Release (WR) and Dispatch Authority (DA)
    permissions for office staff members.
    """
    template_name = 'salesdistribution/wo_release_da/list.html'
    
    def get_context_data(self, **kwargs):
        """
        Prepares the context for displaying the employee permissions grid.
        Fetches active employees and their current permission states to populate the formset.
        """
        context = super().get_context_data(**kwargs)
        
        # In a real application, current_comp_id would come from request.user.profile.comp_id
        # For this example, we'll use a placeholder company ID.
        current_comp_id = 1 

        # Retrieve active employees matching ASP.NET filtering logic
        staff_members = OfficeStaff.objects.filter(
            comp_id=current_comp_id,
            resignation_date__isnull=True, # Employees who have not resigned
        ).exclude(
            employee_name__iexact='ERP' # Exclude 'ERP' user
        ).exclude(
            user_id=1 # Exclude user with ID '1'
        ).select_related('department').order_by('employee_name') # Optimize department lookup and order

        initial_data = []
        for member in staff_members:
            # Prepare initial data for each form in the formset
            initial_data.append({
                'emp_id': member.emp_id,
                'wr': member.wr,
                'da': member.da,
            })
        
        # Populate the formset with initial data based on current permissions
        context['formset'] = OfficeStaffPermissionFormSet(initial=initial_data)
        
        # Also pass the full staff member objects for displaying name, emp no, department symbol
        # as these are not part of the form fields themselves
        context['staff_members_display_data'] = staff_members

        return context

    def post(self, request, *args, **kwargs):
        """
        Handles the submission of the employee permissions formset.
        Validates the data and updates permissions via the model's business logic.
        """
        # In a real application, current_comp_id would come from request.user.profile.comp_id
        current_comp_id = 1 

        # Instantiate the formset with POST data
        formset = OfficeStaffPermissionFormSet(request.POST)

        if formset.is_valid():
            # Use a database transaction to ensure all updates succeed or none do
            with transaction.atomic():
                for form in formset:
                    emp_id = form.cleaned_data['emp_id']
                    wr_status = form.cleaned_data['wr']
                    da_status = form.cleaned_data['da']
                    
                    # Call the 'fat model' method to update permissions
                    OfficeStaff.update_employee_permissions(emp_id, wr_status, da_status, current_comp_id)
            
            messages.success(self.request, 'Employee permissions updated successfully.')
            
            # HTMX response for success: 204 No Content with a trigger to refresh the list
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshPermissionsList'}
                )
            # Fallback for non-HTMX requests (should not happen with this setup)
            return HttpResponseRedirect(self.request.path_info)
        else:
            # If formset is invalid, re-render the page with errors
            messages.error(self.request, 'There were errors updating permissions. Please check the form.')
            # Re-call get_context_data to get staff_members_display_data, and then override formset with invalid one
            context = self.get_context_data() 
            context['formset'] = formset # Pass the formset with errors back to the template
            return render(request, self.template_name, context)

```

### 4.4 Templates

Task: Create templates for the view.

We'll have a main `list.html` and a partial `_permission_table.html`. The main template will provide the structure and trigger the initial load of the table via HTMX, while the partial will contain the actual form and table structure for the bulk update.

```html
{# salesdistribution/wo_release_da/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Work Order Release & Dispatch Authority</h2>
    </div>
    
    <div id="permissions-table-container"
         hx-trigger="load, refreshPermissionsList from:body"
         hx-get="{% url 'salesdistribution:wo_release_da_table' %}"
         hx-swap="innerHTML">
        <!-- Loading spinner while content loads -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading employee permissions...</p>
        </div>
    </div>
    
    {# No modal needed for this bulk update page based on ASP.NET functionality #}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for general UI state
        // This specific page doesn't require complex Alpine.js for its core functionality
        // as HTMX handles the interactions.
    });
</script>
{% endblock %}
```

```html
{# salesdistribution/wo_release_da/_permission_table.html #}
{# This partial is loaded via HTMX into #permissions-table-container #}

<form hx-post="{% url 'salesdistribution:wo_release_da_list' %}"
      hx-swap="outerHTML"
      hx-target="#permissions-table-container"
      class="bg-white shadow-sm rounded-lg p-6">
    {% csrf_token %}
    {{ formset.management_form }} {# Required for formsets #}

    <div class="overflow-x-auto">
        <table id="employeePermissionsTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Employee</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp. No</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Release</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Dispatch</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for form in formset %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {# Display data from staff_members_display_data as it's not part of the form #}
                        {{ staff_members_display_data.forloop.counter0.full_employee_name }}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ staff_members_display_data.forloop.counter0.emp_id }}
                        {{ form.emp_id }} {# Hidden input for emp_id #}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ staff_members_display_data.forloop.counter0.department.symbol|default:"N/A" }}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ form.wr }}
                        {% if form.wr.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.wr.errors }}</p>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ form.da }}
                        {% if form.da.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.da.errors }}</p>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="mt-6 text-center">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
            Submit Permissions
        </button>
    </div>
</form>

{# Initialize DataTables #}
<script>
    $(document).ready(function() {
        $('#employeePermissionsTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "columnDefs": [
                { "orderable": false, "targets": [4, 5] } // Disable sorting for 'Release' and 'Dispatch' checkbox columns
            ]
        });
    });
</script>
```

### 4.5 URLs

Task: Define URL patterns for the view.

We'll define two URL patterns: one for the main page (`list`) and one for the HTMX-loaded partial table (`table`). This ensures that the HTMX requests can target a specific endpoint that returns only the table content.

```python
# salesdistribution/urls.py
from django.urls import path
from .views import OfficeStaffPermissionView

app_name = 'salesdistribution' # Namespace for URLs

urlpatterns = [
    # Main URL for the permission management page
    path('wo_release_da/', OfficeStaffPermissionView.as_view(), name='wo_release_da_list'),
    # HTMX-specific URL to load only the table content (used for initial load and refresh)
    path('wo_release_da/table/', OfficeStaffPermissionView.as_view(), name='wo_release_da_table'),
]

```

**Important:** In your project's main `urls.py`, you need to include these URLs:
`path('sales/', include('salesdistribution.urls')),`

### 4.6 Tests

Task: Write tests for the model and views.

Comprehensive unit tests for the `OfficeStaff` model's `update_employee_permissions` method and integration tests for the `OfficeStaffPermissionView` (GET and POST requests, including HTMX interactions) are crucial.

```python
# salesdistribution/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection

from salesdistribution.models import OfficeStaff, Department

class OfficeStaffModelTest(TestCase):
    """
    Tests for the OfficeStaff model and its business logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup test database structure using raw SQL for unmanaged models
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE tblHR_Departments (
                    Id INT PRIMARY KEY,
                    Symbol NVARCHAR(50)
                );
            """)
            cursor.execute("""
                CREATE TABLE tblHR_OfficeStaff (
                    EmpId NVARCHAR(50) PRIMARY KEY,
                    Title NVARCHAR(50),
                    EmployeeName NVARCHAR(255),
                    Department INT,
                    WR BIT,
                    DA BIT,
                    CompId INT,
                    ResignationDate DATE,
                    UserID INT
                );
            """)

            # Insert sample data for testing
            Department.objects.create(id=1, symbol='IT')
            Department.objects.create(id=2, symbol='HR')

            OfficeStaff.objects.create(
                emp_id='EMP001',
                title='Mr',
                employee_name='John Doe',
                department_id=1,
                wr=False,
                da=False,
                comp_id=1,
                resignation_date=None,
                user_id=101
            )
            OfficeStaff.objects.create(
                emp_id='EMP002',
                title='Ms',
                employee_name='Jane Smith',
                department_id=2,
                wr=True,
                da=False,
                comp_id=1,
                resignation_date=None,
                user_id=102
            )
            OfficeStaff.objects.create(
                emp_id='EMP003',
                title='Dr',
                employee_name='ERP User', # Should be excluded
                department_id=1,
                wr=False,
                da=False,
                comp_id=1,
                resignation_date=None,
                user_id=103
            )
            OfficeStaff.objects.create(
                emp_id='EMP004',
                title='Mr',
                employee_name='Inactive User', # Should be excluded
                department_id=1,
                wr=False,
                da=False,
                comp_id=1,
                resignation_date='2023-01-01',
                user_id=104
            )
            OfficeStaff.objects.create(
                emp_id='EMP005',
                title='Mr',
                employee_name='System User', # Should be excluded by UserID
                department_id=1,
                wr=False,
                da=False,
                comp_id=1,
                resignation_date=None,
                user_id=1
            )
            OfficeStaff.objects.create(
                emp_id='EMP006',
                title='Mrs',
                employee_name='Sarah Johnson',
                department_id=2,
                wr=True,
                da=True,
                comp_id=2, # Different company ID
                resignation_date=None,
                user_id=105
            )
    
    def test_office_staff_creation(self):
        """Test that OfficeStaff objects are created correctly."""
        emp1 = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(emp1.employee_name, 'John Doe')
        self.assertEqual(emp1.department.symbol, 'IT')
        self.assertFalse(emp1.wr)
        self.assertFalse(emp1.da)

    def test_full_employee_name_property(self):
        """Test the full_employee_name property."""
        emp1 = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(emp1.full_employee_name, 'Mr. John Doe')
        
        # Test case with no title
        emp_no_title = OfficeStaff.objects.create(
            emp_id='EMP_NT', employee_name='No Title', department_id=1, comp_id=1
        )
        self.assertEqual(emp_no_title.full_employee_name, 'No Title')

    def test_update_employee_permissions_success(self):
        """Test successful update of employee permissions."""
        initial_emp = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertFalse(initial_emp.wr)
        self.assertFalse(initial_emp.da)

        success = OfficeStaff.update_employee_permissions('EMP001', True, True, 1)
        self.assertTrue(success)

        updated_emp = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertTrue(updated_emp.wr)
        self.assertTrue(updated_emp.da)

    def test_update_employee_permissions_not_found(self):
        """Test update fails when employee is not found."""
        success = OfficeStaff.update_employee_permissions('NONEXISTENT', True, True, 1)
        self.assertFalse(success)

    def test_update_employee_permissions_wrong_company(self):
        """Test update fails when company ID does not match."""
        emp_diff_comp = OfficeStaff.objects.get(emp_id='EMP006')
        self.assertTrue(emp_diff_comp.wr) # Initially True

        success = OfficeStaff.update_employee_permissions('EMP006', False, False, 999) # Wrong company ID
        self.assertFalse(success)

        # Verify permissions did not change
        emp_diff_comp_after = OfficeStaff.objects.get(emp_id='EMP006')
        self.assertTrue(emp_diff_comp_after.wr)


class OfficeStaffPermissionViewTest(TestCase):
    """
    Integration tests for the OfficeStaffPermissionView.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup test database structure (same as ModelTest for consistency)
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE tblHR_Departments (
                    Id INT PRIMARY KEY,
                    Symbol NVARCHAR(50)
                );
            """)
            cursor.execute("""
                CREATE TABLE tblHR_OfficeStaff (
                    EmpId NVARCHAR(50) PRIMARY KEY,
                    Title NVARCHAR(50),
                    EmployeeName NVARCHAR(255),
                    Department INT,
                    WR BIT,
                    DA BIT,
                    CompId INT,
                    ResignationDate DATE,
                    UserID INT
                );
            """)

            Department.objects.create(id=1, symbol='IT')
            Department.objects.create(id=2, symbol='HR')

            OfficeStaff.objects.create(
                emp_id='EMP001', title='Mr', employee_name='John Doe', department_id=1,
                wr=False, da=False, comp_id=1, resignation_date=None, user_id=101
            )
            OfficeStaff.objects.create(
                emp_id='EMP002', title='Ms', employee_name='Jane Smith', department_id=2,
                wr=True, da=False, comp_id=1, resignation_date=None, user_id=102
            )
            OfficeStaff.objects.create(
                emp_id='EMP003', title='Dr', employee_name='ERP User', department_id=1,
                wr=False, da=False, comp_id=1, resignation_date=None, user_id=103
            )
            OfficeStaff.objects.create(
                emp_id='EMP004', title='Mr', employee_name='Inactive User', department_id=1,
                wr=False, da=False, comp_id=1, resignation_date='2023-01-01', user_id=104
            )
            OfficeStaff.objects.create(
                emp_id='EMP005', title='Mr', employee_name='System User', department_id=1,
                wr=False, da=False, comp_id=1, resignation_date=None, user_id=1
            )
            OfficeStaff.objects.create(
                emp_id='EMP006', title='Mrs', employee_name='Sarah Johnson', department_id=2,
                wr=True, da=True, comp_id=2, resignation_date=None, user_id=105
            )

    def setUp(self):
        self.client = Client()
        self.list_url = reverse('salesdistribution:wo_release_da_list')
        self.table_url = reverse('salesdistribution:wo_release_da_table') # For HTMX partial

    def test_list_view_get(self):
        """Test GET request to the main list view."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/wo_release_da/list.html')
        self.assertIn('formset', response.context)
        self.assertIn('staff_members_display_data', response.context)
        
        # Check that only active employees for comp_id=1 are in the display data
        staff_data = response.context['staff_members_display_data']
        self.assertEqual(len(staff_data), 2) # EMP001, EMP002
        self.assertTrue(any(s.emp_id == 'EMP001' for s in staff_data))
        self.assertTrue(any(s.emp_id == 'EMP002' for s in staff_data))
        self.assertFalse(any(s.emp_id == 'EMP003' for s in staff_data)) # Excluded ERP User
        self.assertFalse(any(s.emp_id == 'EMP004' for s in staff_data)) # Excluded Inactive User
        self.assertFalse(any(s.emp_id == 'EMP005' for s in staff_data)) # Excluded System User
        self.assertFalse(any(s.emp_id == 'EMP006' for s in staff_data)) # Excluded different comp_id

    def test_table_partial_view_get_htmx(self):
        """Test HTMX GET request to the partial table view."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/wo_release_da/_permission_table.html')
        self.assertContains(response, '<table id="employeePermissionsTable"')
        
        # Verify content for EMP001 and EMP002 is present
        self.assertContains(response, 'Mr. John Doe')
        self.assertContains(response, 'EMP001')
        self.assertContains(response, 'Ms. Jane Smith')
        self.assertContains(response, 'EMP002')

    def test_post_update_permissions_success(self):
        """Test POST request to update permissions successfully."""
        initial_emp1 = OfficeStaff.objects.get(emp_id='EMP001')
        initial_emp2 = OfficeStaff.objects.get(emp_id='EMP002')
        self.assertFalse(initial_emp1.wr) # EMP001: WR=False, DA=False
        self.assertTrue(initial_emp2.wr)  # EMP002: WR=True, DA=False

        # Prepare formset data for updating
        form_data = {
            'form-TOTAL_FORMS': '2', # Number of forms in the formset
            'form-INITIAL_FORMS': '2',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '',
            
            # Data for EMP001: Change WR=True, DA=True
            'form-0-emp_id': 'EMP001',
            'form-0-wr': 'on', # Checkbox checked
            'form-0-da': 'on',
            
            # Data for EMP002: Change WR=False, DA=True
            'form-1-emp_id': 'EMP002',
            'form-1-wr': '', # Checkbox unchecked
            'form-1-da': 'on',
        }
        
        headers = {'HTTP_HX_REQUEST': 'true'} # Indicate HTMX request
        response = self.client.post(self.list_url, form_data, **headers)
        
        # Expect 204 No Content for successful HTMX post
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPermissionsList')

        # Verify database updates
        updated_emp1 = OfficeStaff.objects.get(emp_id='EMP001')
        updated_emp2 = OfficeStaff.objects.get(emp_id='EMP002')
        self.assertTrue(updated_emp1.wr)
        self.assertTrue(updated_emp1.da)
        self.assertFalse(updated_emp2.wr)
        self.assertTrue(updated_emp2.da)

    def test_post_invalid_data(self):
        """Test POST request with invalid data."""
        form_data = {
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '1',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '',
            
            # Invalid data: Missing emp_id
            'form-0-emp_id': '', 
            'form-0-wr': 'on',
            'form-0-da': 'on',
        }
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.list_url, form_data, **headers)
        
        # Expect 200 OK for invalid formset (re-render with errors)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesdistribution/wo_release_da/list.html')
        self.assertContains(response, 'There were errors updating permissions.')
        self.assertContains(response, 'This field is required.') # Error message for emp_id
        
        # Verify no database changes occurred for EMP001
        emp1 = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertFalse(emp1.wr)
        self.assertFalse(emp1.da)

```

## Step 5: HTMX and Alpine.js Integration

The plan fully integrates HTMX for dynamic interactions and DataTables for enhanced list presentation. Alpine.js, while included in the base template, is not strictly necessary for the core functionality of this specific page, as HTMX handles all submissions and refreshes.

-   **HTMX for Form Submission:** The `_permission_table.html` uses `hx-post` on its `<form>` to submit the entire formset without a full page reload.
    -   `hx-swap="outerHTML"`: Replaces the entire form (and the table) with the fresh content from the server after a successful submission.
    -   `hx-target="#permissions-table-container"`: Directs the response to replace the content within this div.
    -   `hx-trigger="submit"`: Triggers the HTMX request when the form is submitted.
-   **HTMX for Initial Load & Refresh:** The `list.html` uses `hx-get` to load `_permission_table.html` into the `#permissions-table-container` on `load` and `refreshPermissionsList` event.
    -   `hx-trigger="load, refreshPermissionsList from:body"`: Ensures the table is loaded initially and refreshed whenever the `refreshPermissionsList` custom event is triggered from the body (e.g., after a successful form submission).
-   **DataTables:** The `_permission_table.html` includes the JavaScript for DataTables initialization, ensuring client-side searching, sorting, and pagination for the employee list. Column definitions are used to disable sorting on the checkbox columns as appropriate.
-   **No Custom JavaScript:** All dynamic interactions are managed by HTMX, adhering to the "no additional JavaScript" preference. Alpine.js is present for potential future UI state management but not actively used for this page's core logic.

## Final Notes

-   **Placeholders:** Replace `current_comp_id = 1` in `views.py` with actual logic to retrieve the company ID from the authenticated user's profile (e.g., `self.request.user.profile.comp_id`).
-   **DRY Templates:** The use of `{% extends 'core/base.html' %}` and a partial template (`_permission_table.html`) ensures adherence to DRY principles.
-   **Fat Model, Thin View:** Business logic for updating permissions is encapsulated within the `OfficeStaff.update_employee_permissions` class method, keeping the `OfficeStaffPermissionView` concise and focused on request handling and context preparation.
-   **Testing:** The provided tests cover both model business logic and view interactions, including HTMX-specific responses, aiming for high test coverage.
-   **Error Handling:** Basic error messages are provided using Django's `messages` framework. A more robust implementation might include more specific error messages for individual fields in the formset.
-   **Database:** Remember that `managed = False` means Django will not create or modify these tables. You must ensure `tblHR_Departments` and `tblHR_OfficeStaff` already exist in your database with the correct schema inferred.