## ASP.NET to Django Conversion Script: Customer Master - Edit Module

This document outlines a strategic plan for migrating the Customer Master - Edit module from its legacy ASP.NET framework to a modern Django-based solution. Our approach prioritizes automation, leveraging AI-assisted tools to streamline the conversion process, minimize manual effort, and ensure a robust, maintainable, and scalable application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the C# code, we identified interactions with `SD_Cust_master` for customer details, and inferred lookup tables for `Country`, `State`, and `City` based on dropdown population logic.

**Identified Tables and Columns:**

*   **Main Table:** `SD_Cust_master`
    *   `CustomerId` (Primary Key, integer)
    *   `CustomerName` (string)
    *   `CompId` (Company ID, integer)
    *   `SysDate` (Date, last updated)
    *   `SysTime` (Time, last updated)
    *   `SessionId` (User session ID, string)
    *   `RegdAddress` (Text)
    *   `RegdCountry` (Foreign Key to Country, integer ID)
    *   `RegdState` (Foreign Key to State, integer ID)
    *   `RegdCity` (Foreign Key to City, integer ID)
    *   `RegdPinNo` (string)
    *   `RegdContactNo` (string)
    *   `RegdFaxNo` (string)
    *   `WorkAddress` (Text)
    *   `WorkCountry` (Foreign Key to Country, integer ID)
    *   `WorkState` (Foreign Key to State, integer ID)
    *   `WorkCity` (Foreign Key to City, integer ID)
    *   `WorkPinNo` (string)
    *   `WorkContactNo` (string)
    *   `WorkFaxNo` (string)
    *   `MaterialDelAddress` (Text)
    *   `MaterialDelCountry` (Foreign Key to Country, integer ID)
    *   `MaterialDelState` (Foreign Key to State, integer ID)
    *   `MaterialDelCity` (Foreign Key to City, integer ID)
    *   `MaterialDelPinNo` (string)
    *   `MaterialDelContactNo` (string)
    *   `MaterialDelFaxNo` (string)
    *   `ContactPerson` (string)
    *   `JuridictionCode` (string)
    *   `Commissionurate` (string)
    *   `TinVatNo` (string)
    *   `Email` (string)
    *   `EccNo` (string)
    *   `Divn` (string)
    *   `TinCstNo` (string)
    *   `ContactNo` (string)
    *   `Range` (string)
    *   `PanNo` (string)
    *   `TDSCode` (string)
    *   `Remark` (Text)

*   **Inferred Lookup Tables:**
    *   `Country_Master` (Assumed table name, with `CId` and `CountryName`)
    *   `State_Master` (Assumed table name, with `SId`, `StateName`, and `CId`)
    *   `City_Master` (Assumed table name, with `CityId`, `CityName`, and `SId`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Identified Functionality:**

*   **Read (R):**
    *   The `Page_Load` event fetches an existing customer's data (`SELECT * from SD_Cust_master where CustomerId='...' And CompId='...'`) based on a `CustomerId` passed via query string. This data is then used to pre-populate the form fields.
    *   Dropdown lists for Country, State, and City are populated on initial load, and dynamically updated upon selection changes (cascading dropdowns).
*   **Update (U):**
    *   The `Update_Click` event handles the submission of the form. It performs comprehensive server-side validation against all input fields and an email regular expression.
    *   If validation passes, it constructs and executes an `UPDATE` SQL command to persist changes back to the `SD_Cust_master` table.
*   **Validation:**
    *   ASP.NET `RequiredFieldValidator` and `RegularExpressionValidator` provide client-side and basic server-side validation.
    *   Extensive manual server-side validation checks for non-empty strings and specific "Select" values for dropdowns are present in `Update_Click`.
*   **Navigation:**
    *   Successful updates and cancellations redirect to `CustomerMaster_Edit.aspx?ModId=2&SubModId=7`, implying a return to a list or dashboard page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Identified UI Components and Their Roles:**

*   **`asp:Label` (`lblCustName`, `hfCustomerId`):** Displays the customer's name and holds a hidden customer ID. In Django, this will be static text or context data in the template, and the ID handled by the URL parameter.
*   **`asp:TextBox` (`txtEditRegdAdd`, `txtEditWorkAdd`, `txtEditMaterialDelAdd`, `txtEditRemark`, and numerous others):** Used for capturing various text inputs (addresses, contact details, codes, etc.). These will map to Django `forms.CharField` or `forms.TextField` with `TextInput` or `Textarea` widgets.
*   **`asp:DropDownList` (e.g., `DDListEditRegdCountry`, `DDListEditRegdState`, `DDListEditRegdCity`):** Used for selecting values from predefined lists with cascading dependencies. These will map to Django `forms.ModelChoiceField` with `Select` widgets. The cascading behavior will be managed by HTMX.
*   **`asp:Button` (`Update`, `btnCancel`):** Triggers form submission and cancellation actions. These will map to HTML `<button type="submit">` and `<button type="button">` with HTMX attributes for dynamic form submission and modal control.
*   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`:** Client-side and basic server-side validation. Django's `ModelForm` and form field validation will handle this, with custom validation methods in the form or model if needed.
*   **CSS and JavaScript:**
    *   Inline `<style>` and external CSS (`StyleSheet.css`) will be replaced by Tailwind CSS utility classes.
    *   External JavaScript files (`PopUpMsg.js`, `loadingNotifier.js`) and `onclientclick` attributes will be replaced by HTMX for dynamic interactions and Alpine.js for UI state management (e.g., modal show/hide).

---

### Step 4: Generate Django Code

We will create a new Django application, `sd_module`, to house these components.

#### 4.1 Models (`sd_module/models.py`)

**Task:** Create Django models based on the database schema.
We'll define models for `Country`, `State`, and `City` as lookup tables, and then the main `Customer` model, mapping to the existing database tables.

```python
from django.db import models
from django.utils import timezone

# Assume these are lookup tables pre-populated in the database
# Or can be managed within Django if migration strategy allows
class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=255)

    class Meta:
        managed = False
        db_table = 'Country_Master'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=255)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId')

    class Meta:
        managed = False
        db_table = 'State_Master'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=255)
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId')

    class Meta:
        managed = False
        db_table = 'City_Master'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name


class Customer(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True, verbose_name="Customer's Name")
    company_id = models.IntegerField(db_column='CompId', verbose_name='Company ID')

    # System tracking fields, auto_now ensures update on save
    sys_date = models.DateField(db_column='SysDate', auto_now=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now=True)
    session_id = models.CharField(db_column='SessionId', max_length=255)

    # Registered Office Address
    regd_address = models.TextField(db_column='RegdAddress', verbose_name='Regd. Address')
    regd_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='RegdCountry', related_name='regd_customers', verbose_name='Regd. Country')
    regd_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='RegdState', related_name='regd_customers', verbose_name='Regd. State')
    regd_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='RegdCity', related_name='regd_customers', verbose_name='Regd. City')
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=50, verbose_name='Regd. PIN No.')
    regd_contact_no = models.CharField(db_column='RegdContactNo', max_length=50, verbose_name='Regd. Contact No.')
    regd_fax_no = models.CharField(db_column='RegdFaxNo', max_length=50, verbose_name='Regd. Fax No.')

    # Works/Factory Address
    work_address = models.TextField(db_column='WorkAddress', verbose_name='Works Address')
    work_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='WorkCountry', related_name='work_customers', verbose_name='Works Country')
    work_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='WorkState', related_name='work_customers', verbose_name='Works State')
    work_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='WorkCity', related_name='work_customers', verbose_name='Works City')
    work_pin_no = models.CharField(db_column='WorkPinNo', max_length=50, verbose_name='Works PIN No.')
    work_contact_no = models.CharField(db_column='WorkContactNo', max_length=50, verbose_name='Works Contact No.')
    work_fax_no = models.CharField(db_column='WorkFaxNo', max_length=50, verbose_name='Works Fax No.')

    # Material Delivery Address
    material_del_address = models.TextField(db_column='MaterialDelAddress', verbose_name='Material Del. Address')
    material_del_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='MaterialDelCountry', related_name='material_customers', verbose_name='Material Del. Country')
    material_del_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='MaterialDelState', related_name='material_customers', verbose_name='Material Del. State')
    material_del_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='MaterialDelCity', related_name='material_customers', verbose_name='Material Del. City')
    material_del_pin_no = models.CharField(db_column='MaterialDelPinNo', max_length=50, verbose_name='Material Del. PIN No.')
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, verbose_name='Material Del. Contact No.')
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, verbose_name='Material Del. Fax No.')

    # Other Details
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, verbose_name='Contact Person')
    juridiction_code = models.CharField(db_column='JuridictionCode', max_length=255, verbose_name='Juridiction Code')
    commissionurate = models.CharField(db_column='Commissionurate', max_length=255, verbose_name='Commissionurate')
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=255, verbose_name='TIN/VAT No.')
    email = models.EmailField(db_column='Email', max_length=255, verbose_name='E-mail')
    ecc_no = models.CharField(db_column='EccNo', max_length=255, verbose_name='ECC.No.')
    divn = models.CharField(db_column='Divn', max_length=255, verbose_name='Divn')
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=255, verbose_name='TIN/CST No.')
    contact_no = models.CharField(db_column='ContactNo', max_length=50, verbose_name='Contact No.') # General contact no
    range_field = models.CharField(db_column='Range', max_length=255, verbose_name='Range') # Renamed to avoid Python's 'range' keyword
    pan_no = models.CharField(db_column='PanNo', max_length=255, verbose_name='PAN No.')
    tds_code = models.CharField(db_column='TDSCode', max_length=255, verbose_name='TDS Code')
    remark = models.TextField(db_column='Remark', blank=True, null=True, verbose_name='Remarks')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name or f"Customer ID: {self.customer_id}"

    def update_details(self, data, user_session_id, company_id):
        """
        Updates customer details from provided data.
        This method encapsulates business logic related to updating a customer.
        """
        # Update fields from data dictionary
        for field, value in data.items():
            setattr(self, field, value)

        # Update system tracking fields
        self.session_id = user_session_id
        self.company_id = company_id
        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().time()
        
        self.save()

    def get_full_address(self, address_type='regd'):
        """Helper to get full address string for display."""
        if address_type == 'regd':
            return f"{self.regd_address}, {self.regd_city}, {self.regd_state}, {self.regd_country} - {self.regd_pin_no}"
        elif address_type == 'work':
            return f"{self.work_address}, {self.work_city}, {self.work_state}, {self.work_country} - {self.work_pin_no}"
        elif address_type == 'material_del':
            return f"{self.material_del_address}, {self.material_del_city}, {self.material_del_state}, {self.material_del_country} - {self.material_del_pin_no}"
        return "N/A"

```

#### 4.2 Forms (`sd_module/forms.py`)

**Task:** Define a Django form for user input.
We will create a `ModelForm` for the `Customer` model, including all fields that are editable through the form. We'll also add custom validation for the dropdowns (to prevent "Select" as a valid choice) and apply Tailwind CSS classes.

```python
from django import forms
from .models import Customer, Country, State, City

# Custom widget to add a placeholder option to Select fields
class SelectWithPlaceholder(forms.Select):
    def __init__(self, attrs=None, choices=(), placeholder=None):
        super().__init__(attrs, choices)
        self.placeholder = placeholder

    def create_option(self, name, value, label, selected, index, subindex=None, attrs=None):
        option = super().create_option(name, value, label, selected, index, subindex, attrs)
        if value == '' and self.placeholder:
            option['attrs']['disabled'] = 'disabled'
            option['attrs']['selected'] = 'selected' # Pre-select placeholder
            option['label'] = self.placeholder # Set placeholder text
        return option

class CustomerForm(forms.ModelForm):
    # Initial choices for cascading dropdowns (can be empty initially)
    regd_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=SelectWithPlaceholder(placeholder="Select Country", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/sd_module/get-states/', 'hx-target': '#id_regd_state', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'innerHTML'})
    )
    regd_state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Empty queryset initially
        empty_label="Select State",
        widget=SelectWithPlaceholder(placeholder="Select State", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/sd_module/get-cities/', 'hx-target': '#id_regd_city', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'innerHTML'})
    )
    regd_city = forms.ModelChoiceField(
        queryset=City.objects.none(), # Empty queryset initially
        empty_label="Select City",
        widget=SelectWithPlaceholder(placeholder="Select City", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    work_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=SelectWithPlaceholder(placeholder="Select Country", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/sd_module/get-states/', 'hx-target': '#id_work_state', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'innerHTML'})
    )
    work_state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Empty queryset initially
        empty_label="Select State",
        widget=SelectWithPlaceholder(placeholder="Select State", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/sd_module/get-cities/', 'hx-target': '#id_work_city', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'innerHTML'})
    )
    work_city = forms.ModelChoiceField(
        queryset=City.objects.none(), # Empty queryset initially
        empty_label="Select City",
        widget=SelectWithPlaceholder(placeholder="Select City", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    material_del_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=SelectWithPlaceholder(placeholder="Select Country", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/sd_module/get-states/', 'hx-target': '#id_material_del_state', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'innerHTML'})
    )
    material_del_state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Empty queryset initially
        empty_label="Select State",
        widget=SelectWithPlaceholder(placeholder="Select State", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/sd_module/get-cities/', 'hx-target': '#id_material_del_city', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'innerHTML'})
    )
    material_del_city = forms.ModelChoiceField(
        queryset=City.objects.none(), # Empty queryset initially
        empty_label="Select City",
        widget=SelectWithPlaceholder(placeholder="Select City", attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = Customer
        fields = [
            'regd_address', 'regd_country', 'regd_state', 'regd_city', 'regd_pin_no', 'regd_contact_no', 'regd_fax_no',
            'work_address', 'work_country', 'work_state', 'work_city', 'work_pin_no', 'work_contact_no', 'work_fax_no',
            'material_del_address', 'material_del_country', 'material_del_state', 'material_del_city', 'material_del_pin_no', 'material_del_contact_no', 'material_del_fax_no',
            'contact_person', 'juridiction_code', 'commissionurate', 'tin_vat_no', 'email', 'ecc_no', 'divn', 'tin_cst_no',
            'contact_no', 'range_field', 'pan_no', 'tds_code', 'remark'
        ]
        widgets = {
            'regd_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'regd_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'work_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'material_del_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_person': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'juridiction_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'commissionurate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tin_vat_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ecc_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'divn': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tin_cst_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'range_field': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tds_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remark': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial dropdowns if instance exists (for edit view)
        if self.instance and self.instance.pk:
            if self.instance.regd_country:
                self.fields['regd_state'].queryset = State.objects.filter(country=self.instance.regd_country)
            if self.instance.regd_state:
                self.fields['regd_city'].queryset = City.objects.filter(state=self.instance.regd_state)

            if self.instance.work_country:
                self.fields['work_state'].queryset = State.objects.filter(country=self.instance.work_country)
            if self.instance.work_state:
                self.fields['work_city'].queryset = City.objects.filter(state=self.instance.work_state)

            if self.instance.material_del_country:
                self.fields['material_del_state'].queryset = State.objects.filter(country=self.instance.material_del_country)
            if self.instance.material_del_state:
                self.fields['material_del_city'].queryset = City.objects.filter(state=self.instance.material_del_state)

        # Mark all fields as required based on ASP.NET validation
        for field_name in self.fields:
            if field_name not in ['remark']: # Remark is optional in ASP.NET
                self.fields[field_name].required = True

    def clean(self):
        cleaned_data = super().clean()
        # Custom validation for ModelChoiceFields if 'Select' placeholder was chosen
        # This can be handled by `empty_label` and `required=True` automatically
        # but added for explicit clarity from ASP.NET's InitialValue="Select"
        dropdown_fields = [
            'regd_country', 'regd_state', 'regd_city',
            'work_country', 'work_state', 'work_city',
            'material_del_country', 'material_del_state', 'material_del_city'
        ]
        for field_name in dropdown_fields:
            if field_name in cleaned_data and cleaned_data[field_name] is None:
                self.add_error(field_name, "This field is required.")
        return cleaned_data

```

#### 4.3 Views (`sd_module/views.py`)

**Task:** Implement CRUD operations using CBVs.
We'll create a `CustomerListView`, `CustomerUpdateView`, and specific views for HTMX-driven partials like the table and cascading dropdowns.

```python
from django.views.generic import ListView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import render, get_object_or_404
from .models import Customer, Country, State, City
from .forms import CustomerForm

class CustomerListView(ListView):
    model = Customer
    template_name = 'sd_module/customer/list.html'
    context_object_name = 'customers'

    def get_queryset(self):
        # Implement filtering/sorting based on query parameters if needed
        # For now, return all customers.
        return Customer.objects.all()

class CustomerTablePartialView(ListView):
    model = Customer
    template_name = 'sd_module/customer/_customer_table.html'
    context_object_name = 'customers'

    def get_queryset(self):
        # This view is for HTMX to refresh the table.
        # It should reflect any active filters/sorting if present in the main list view.
        return Customer.objects.all()

class CustomerUpdateView(UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'sd_module/customer/_customer_form.html' # Use partial for modal
    success_url = reverse_lazy('customer_list') # Redirects to the list page

    def get_object(self, queryset=None):
        customer_id = self.kwargs.get('pk')
        if not customer_id:
            raise Http404("Customer ID not provided.")
        return get_object_or_404(Customer, customer_id=customer_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['customer_name'] = self.object.customer_name # Pass customer name to template
        return context

    def form_valid(self, form):
        # In a real application, user_session_id and company_id would come from request/session
        # For this example, we use placeholders.
        user_session_id = self.request.user.username if self.request.user.is_authenticated else "anonymous"
        company_id = self.request.session.get('compid', 1) # Assuming compid is stored in session

        self.object.update_details(form.cleaned_data, user_session_id, company_id)
        messages.success(self.request, f"Customer '{self.object.customer_name}' updated successfully.")

        if self.request.headers.get('HX-Request'):
            # For HTMX POST, return 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList'
                }
            )
        return super().form_valid(form) # Fallback for non-HTMX requests

    def form_invalid(self, form):
        # Render the form again for HTMX to swap errors
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form, 'customer_name': self.get_object().customer_name})
        return super().form_invalid(form)

class CustomerDeleteView(DeleteView):
    model = Customer
    template_name = 'sd_module/customer/_confirm_delete.html'
    success_url = reverse_lazy('customer_list')

    def get_object(self, queryset=None):
        customer_id = self.kwargs.get('pk')
        if not customer_id:
            raise Http404("Customer ID not provided.")
        return get_object_or_404(Customer, customer_id=customer_id)

    def delete(self, request, *args, **kwargs):
        customer = self.get_object()
        customer_name = customer.customer_name
        response = super().delete(request, *args, **kwargs)
        messages.success(request, f"Customer '{customer_name}' deleted successfully.")
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList'
                }
            )
        return response

# HTMX endpoints for cascading dropdowns
class GetStatesView(View):
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('country_id')
        states = State.objects.none()
        if country_id:
            try:
                states = State.objects.filter(country_id=country_id).order_by('name')
            except ValueError:
                pass # Invalid country_id, return empty queryset

        context = {'states': states, 'is_city_dropdown': False}
        return render(request, 'sd_module/customer/_state_options.html', context)

class GetCitiesView(View):
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('state_id')
        cities = City.objects.none()
        if state_id:
            try:
                cities = City.objects.filter(state_id=state_id).order_by('name')
            except ValueError:
                pass # Invalid state_id, return empty queryset

        context = {'cities': cities}
        return render(request, 'sd_module/customer/_city_options.html', context)

```

#### 4.4 Templates (`sd_module/templates/sd_module/customer/`)

**Task:** Create templates for each view, ensuring HTMX and Alpine.js integration and DRY principles.

**`list.html`**
This template provides the main page for listing customers and triggering CRUD operations via modal dialogs.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Master - Edit</h2>
        <!-- While the original is an edit page, listing is usually present for navigation -->
        <!-- Add New Customer functionality if a create page exists -->
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md"
            hx-get="{% url 'customer_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .block to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent scale:95%">
            Add New Customer
        </button>
    </div>
    
    <!-- Customer Table will be loaded here via HTMX -->
    <div id="customerTable-container"
         hx-trigger="load, refreshCustomerList from:body"
         hx-get="{% url 'customer_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Customers...</p>
        </div>
    </div>
    
    <!-- Modal for forms (edit/delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden transition-opacity duration-300 z-50"
         _="on click if event.target.id == 'modal' remove .block from me then remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
        <div id="modalOverlay" class="absolute inset-0 bg-gray-600 opacity-0 transition-opacity duration-300"></div>
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-auto my-12 transform scale-95 transition-transform duration-300 overflow-y-auto max-h-[90vh]"
             _="on htmx:afterSwap remove .block from #modal then remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent if event.detail.xhr.status == 204">
            <!-- Content loaded via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

**`_customer_table.html`**
This partial template renders the customer list in a DataTables-powered table.

```html
<table id="customerTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Email</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Contact Person</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for customer in customers %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ customer.customer_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ customer.email }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ customer.contact_person }}</td>
            <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-sm mr-2 shadow-sm"
                    hx-get="{% url 'customer_edit' customer.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .block to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent scale:95%">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-sm shadow-sm"
                    hx-get="{% url 'customer_delete' customer.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .block to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent scale:95%">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No customers found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only once and on the rendered element
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#customerTable')) {
            $('#customerTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
        }
    });
</script>
```

**`_customer_form.html`**
This partial template renders the customer edit form, used within a modal.

```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">
        {{ form.instance.pk|yesno:'Edit,Add' }} Customer Details
    </h3>
    <p class="text-gray-700 mb-4">
        Customer Name: <span class="font-medium text-gray-900">{{ customer_name }}</span>
    </p>

    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        <input type="hidden" name="customer_id" value="{{ form.instance.customer_id }}"> {# Pass PK for managed=False #}

        <!-- Address/Details Section -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="col-span-1 py-3 px-4 bg-gray-100 font-semibold text-gray-700 rounded-md">Address/Details</div>
            <div class="col-span-1 py-3 px-4 bg-gray-100 font-semibold text-gray-700 rounded-md">REGD. OFFICE</div>
            <div class="col-span-1 py-3 px-4 bg-gray-100 font-semibold text-gray-700 rounded-md">WORKS/FACTORY</div>
            <div class="col-span-1 py-3 px-4 bg-gray-100 font-semibold text-gray-700 rounded-md">MATERIAL DELIVERY</div>

            {% for field_prefix in ['regd', 'work', 'material_del'] %}
                {% for field_name_suffix, field_type in [
                    ('address', 'textarea'), 
                    ('country', 'select'), 
                    ('state', 'select'), 
                    ('city', 'select'), 
                    ('pin_no', 'text'), 
                    ('contact_no', 'text'), 
                    ('fax_no', 'text')
                ] %}
                    {% with field=form|get_field:field_prefix|add_field_suffix:field_name_suffix %}
                        <div class="col-span-1 flex items-center bg-gray-50 text-gray-700 font-medium pl-4 py-2 border-l border-gray-200">
                            {{ field.label }}
                        </div>
                        <div class="col-span-1 mb-4">
                            {% if field_type == 'textarea' %}
                                {{ field }}
                            {% elif field_type == 'select' %}
                                {{ field }}
                            {% else %}
                                {{ field }}
                            {% endif %}
                            {% if field.errors %}
                                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                            {% endif %}
                        </div>
                    {% endwith %}
                {% endfor %}
            {% endfor %}
        </div>

        <!-- Other Details Section -->
        <div class="grid grid-cols-1 md:grid-cols-6 gap-6 items-center">
            {% for field in form.contact_person, form.email, form.contact_no, form.juridiction_code, form.ecc_no, form.range_field, form.commissionurate, form.divn, form.pan_no, form.tin_vat_no, form.tin_cst_no, form.tds_code %}
            <div class="col-span-2">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <!-- Remarks Section -->
        <div class="mb-4">
            <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.remark.label }}
            </label>
            {{ form.remark }}
            {% if form.remark.errors %}
            <p class="text-red-600 text-xs mt-1">{{ form.remark.errors }}</p>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-400 hover:bg-gray-500 text-white font-bold py-2 px-5 rounded-md shadow-sm"
                _="on click remove .block from #modal then remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-5 rounded-md shadow-sm">
                Update
            </button>
        </div>
    </form>
</div>

<!-- Custom template tags for accessing form fields dynamically -->
{% load custom_filters %}

```

**`_confirm_delete.html`**
This partial template provides a delete confirmation dialog.

```html
<div class="p-6 bg-white rounded-lg shadow-lg text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete customer **"{{ object.customer_name }}"**? This action cannot be undone.
    </p>
    <form hx-delete="{% url 'customer_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-400 hover:bg-gray-500 text-white font-bold py-2 px-5 rounded-md shadow-sm"
                _="on click remove .block from #modal then remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`_state_options.html`** (for HTMX cascading dropdowns)

```html
<option value="">Select State</option>
{% for state in states %}
<option value="{{ state.id }}">{{ state.name }}</option>
{% endfor %}
```

**`_city_options.html`** (for HTMX cascading dropdowns)

```html
<option value="">Select City</option>
{% for city in cities %}
<option value="{{ city.id }}">{{ city.name }}</option>
{% endfor %}
```

**`sd_module/templatetags/custom_filters.py`**
A simple custom template tag to access form fields dynamically by concatenated name.

```python
from django import template

register = template.Library()

@register.filter
def get_field(form, field_name):
    return form[field_name]

@register.filter
def add_field_suffix(value, arg):
    return f"{value}_{arg}"
```

*(You'll need to create `sd_module/templatetags/__init__.py` and `sd_module/templatetags/custom_filters.py` and ensure `custom_filters` is added to `INSTALLED_APPS` for this to work.)*

#### 4.5 URLs (`sd_module/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import (
    CustomerListView, CustomerUpdateView, CustomerDeleteView,
    CustomerTablePartialView, GetStatesView, GetCitiesView
)

urlpatterns = [
    path('customers/', CustomerListView.as_view(), name='customer_list'),
    path('customers/table/', CustomerTablePartialView.as_view(), name='customer_table'),
    path('customers/add/', CustomerUpdateView.as_view(), name='customer_add'), # Reusing form for add, just instantiate without PK
    path('customers/edit/<int:pk>/', CustomerUpdateView.as_view(), name='customer_edit'),
    path('customers/delete/<int:pk>/', CustomerDeleteView.as_view(), name='customer_delete'),
    
    # HTMX endpoints for cascading dropdowns
    path('get-states/', GetStatesView.as_view(), name='get_states'),
    path('get-cities/', GetCitiesView.as_view(), name='get_cities'),
]
```

#### 4.6 Tests (`sd_module/tests/test_models.py` and `sd_module/tests/test_views.py`)

**Task:** Write comprehensive tests for the model and views.

**`sd_module/tests/test_models.py`**
```python
from django.test import TestCase
from datetime import date, time
from sd_module.models import Customer, Country, State, City

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs
        cls.country = Country.objects.create(id=1, name='Test Country')
        cls.state = State.objects.create(id=1, name='Test State', country=cls.country)
        cls.city = City.objects.create(id=1, name='Test City', state=cls.state)

        # Create a sample customer instance
        Customer.objects.create(
            customer_id=101,
            customer_name='Test Customer',
            company_id=1,
            session_id='test_user',
            regd_address='123 Test Regd Rd',
            regd_country=cls.country,
            regd_state=cls.state,
            regd_city=cls.city,
            regd_pin_no='123456',
            regd_contact_no='9876543210',
            regd_fax_no='11223344',
            work_address='456 Test Work St',
            work_country=cls.country,
            work_state=cls.state,
            work_city=cls.city,
            work_pin_no='654321',
            work_contact_no='0123456789',
            work_fax_no='44332211',
            material_del_address='789 Test Del Ave',
            material_del_country=cls.country,
            material_del_state=cls.state,
            material_del_city=cls.city,
            material_del_pin_no='789012',
            material_del_contact_no='5678901234',
            material_del_fax_no='99887766',
            contact_person='John Doe',
            juridiction_code='JUR001',
            commissionurate='COMM001',
            tin_vat_no='TINVAT001',
            email='<EMAIL>',
            ecc_no='ECC001',
            divn='DIV001',
            tin_cst_no='TINCST001',
            contact_no='9998887777',
            range_field='RANGE001',
            pan_no='PAN001',
            tds_code='TDS001',
            remark='Initial remark'
        )

    def test_customer_creation(self):
        customer = Customer.objects.get(customer_id=101)
        self.assertEqual(customer.customer_name, 'Test Customer')
        self.assertEqual(customer.email, '<EMAIL>')
        self.assertEqual(customer.regd_country.name, 'Test Country')

    def test_update_details_method(self):
        customer = Customer.objects.get(customer_id=101)
        original_email = customer.email
        
        new_data = {
            'email': '<EMAIL>',
            'contact_person': 'Jane Doe'
        }
        customer.update_details(new_data, 'updated_user', 2)
        
        customer.refresh_from_db()
        self.assertEqual(customer.email, '<EMAIL>')
        self.assertEqual(customer.contact_person, 'Jane Doe')
        self.assertEqual(customer.session_id, 'updated_user')
        self.assertEqual(customer.company_id, 2)
        self.assertEqual(customer.sys_date, date.today())
        self.assertIsInstance(customer.sys_time, time)

    def test_get_full_address(self):
        customer = Customer.objects.get(customer_id=101)
        expected_regd_address = "123 Test Regd Rd, Test City, Test State, Test Country - 123456"
        self.assertEqual(customer.get_full_address('regd'), expected_regd_address)
        self.assertEqual(customer.get_full_address('work'), "456 Test Work St, Test City, Test State, Test Country - 654321")
        self.assertEqual(customer.get_full_address('material_del'), "789 Test Del Ave, Test City, Test State, Test Country - 789012")
        self.assertEqual(customer.get_full_address('invalid'), "N/A")

    def test_field_verbose_names(self):
        customer = Customer.objects.get(customer_id=101)
        field_label = customer._meta.get_field('customer_name').verbose_name
        self.assertEqual(field_label, "Customer's Name")
        field_label = customer._meta.get_field('email').verbose_name
        self.assertEqual(field_label, "E-mail")
```

**`sd_module/tests/test_views.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from sd_module.models import Customer, Country, State, City
from unittest.mock import patch, MagicMock

class CustomerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create mock data for FKs
        cls.country1 = Country.objects.create(id=1, name='Country A')
        cls.country2 = Country.objects.create(id=2, name='Country B')
        cls.state1 = State.objects.create(id=101, name='State X', country=cls.country1)
        cls.state2 = State.objects.create(id=102, name='State Y', country=cls.country1)
        cls.city1 = City.objects.create(id=201, name='City P', state=cls.state1)
        cls.city2 = City.objects.create(id=202, name='City Q', state=cls.state1)
        
        # Create a customer for editing/deleting
        cls.customer_data = {
            'customer_id': 101,
            'customer_name': 'Original Customer',
            'company_id': 1,
            'session_id': 'original_user',
            'regd_address': '1 Original Rd',
            'regd_country': cls.country1,
            'regd_state': cls.state1,
            'regd_city': cls.city1,
            'regd_pin_no': '111111',
            'regd_contact_no': '1111111111',
            'regd_fax_no': '1111',
            'work_address': '2 Original St',
            'work_country': cls.country1,
            'work_state': cls.state1,
            'work_city': cls.city1,
            'work_pin_no': '222222',
            'work_contact_no': '2222222222',
            'work_fax_no': '2222',
            'material_del_address': '3 Original Ave',
            'material_del_country': cls.country1,
            'material_del_state': cls.state1,
            'material_del_city': cls.city1,
            'material_del_pin_no': '333333',
            'material_del_contact_no': '3333333333',
            'material_del_fax_no': '3333',
            'contact_person': 'Original Contact',
            'juridiction_code': 'ORI_JUR',
            'commissionurate': 'ORI_COM',
            'tin_vat_no': 'ORI_TIN',
            'email': '<EMAIL>',
            'ecc_no': 'ORI_ECC',
            'divn': 'ORI_DIV',
            'tin_cst_no': 'ORI_CST',
            'contact_no': '4444444444',
            'range_field': 'ORI_RNG',
            'pan_no': 'ORI_PAN',
            'tds_code': 'ORI_TDS',
            'remark': 'Original remark'
        }
        cls.customer = Customer.objects.create(**cls.customer_data)

    def setUp(self):
        self.client = Client()
        # Mock request.user and session for update_details method
        self.client.force_login(MagicMock(username='test_user'))
        session = self.client.session
        session['compid'] = 1
        session.save()

    def test_customer_list_view(self):
        response = self.client.get(reverse('customer_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sd_module/customer/list.html')
        self.assertIn('customers', response.context)
        self.assertQuerySetEqual(response.context['customers'], [self.customer])

    def test_customer_table_partial_view(self):
        response = self.client.get(reverse('customer_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sd_module/customer/_customer_table.html')
        self.assertIn('customers', response.context)
        self.assertQuerySetEqual(response.context['customers'], [self.customer])

    def test_customer_update_view_get(self):
        response = self.client.get(reverse('customer_edit', args=[self.customer.customer_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sd_module/customer/_customer_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'].instance, Customer)
        self.assertEqual(response.context['form'].instance.pk, self.customer.customer_id)
        self.assertEqual(response.context['customer_name'], self.customer.customer_name)


    @patch('sd_module.models.timezone.now')
    def test_customer_update_view_post_success(self, mock_now):
        mock_now.return_value = MagicMock(date=lambda: date(2023, 1, 1), time=lambda: time(12, 0, 0))
        
        updated_data = {
            'regd_address': 'New Regd Address',
            'regd_country': self.country1.id,
            'regd_state': self.state1.id,
            'regd_city': self.city1.id,
            'regd_pin_no': '999999',
            'regd_contact_no': '9999999999',
            'regd_fax_no': '9999',
            'work_address': 'New Work Address',
            'work_country': self.country1.id,
            'work_state': self.state1.id,
            'work_city': self.city1.id,
            'work_pin_no': '888888',
            'work_contact_no': '8888888888',
            'work_fax_no': '8888',
            'material_del_address': 'New Del Address',
            'material_del_country': self.country1.id,
            'material_del_state': self.state1.id,
            'material_del_city': self.city1.id,
            'material_del_pin_no': '777777',
            'material_del_contact_no': '7777777777',
            'material_del_fax_no': '7777',
            'contact_person': 'New Contact',
            'juridiction_code': 'NEW_JUR',
            'commissionurate': 'NEW_COM',
            'tin_vat_no': 'NEW_TIN',
            'email': '<EMAIL>',
            'ecc_no': 'NEW_ECC',
            'divn': 'NEW_DIV',
            'tin_cst_no': 'NEW_CST',
            'contact_no': '5555555555',
            'range_field': 'NEW_RNG',
            'pan_no': 'NEW_PAN',
            'tds_code': 'NEW_TDS',
            'remark': 'New remark',
            'customer_id': self.customer.customer_id # Include primary key for update
        }
        
        response = self.client.post(
            reverse('customer_edit', args=[self.customer.customer_id]),
            updated_data,
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerList')

        self.customer.refresh_from_db()
        self.assertEqual(self.customer.email, '<EMAIL>')
        self.assertEqual(self.customer.regd_address, 'New Regd Address')
        self.assertEqual(self.customer.session_id, 'test_user')
        self.assertEqual(self.customer.sys_date, date(2023, 1, 1))

    def test_customer_update_view_post_invalid(self):
        invalid_data = self.customer_data.copy()
        invalid_data['email'] = 'invalid-email' # Make email invalid
        
        response = self.client.post(
            reverse('customer_edit', args=[self.customer.customer_id]),
            invalid_data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sd_module/customer/_customer_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('email', response.context['form'].errors)

    def test_customer_delete_view_get(self):
        response = self.client.get(reverse('customer_delete', args=[self.customer.customer_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sd_module/customer/_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.customer)

    def test_customer_delete_view_post_success(self):
        response = self.client.delete(
            reverse('customer_delete', args=[self.customer.customer_id]),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerList')
        self.assertFalse(Customer.objects.filter(customer_id=self.customer.customer_id).exists())

    def test_get_states_view(self):
        response = self.client.get(reverse('get_states'), {'country_id': self.country1.id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sd_module/customer/_state_options.html')
        self.assertIn('states', response.context)
        self.assertQuerySetEqual(response.context['states'], [self.state1, self.state2], ordered=False)

    def test_get_cities_view(self):
        response = self.client.get(reverse('get_cities'), {'state_id': self.state1.id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sd_module/customer/_city_options.html')
        self.assertIn('cities', response.context)
        self.assertQuerySetEqual(response.context['cities'], [self.city1, self.city2], ordered=False)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates and views are designed for a seamless HTMX and Alpine.js experience.

*   **HTMX for Dynamic Content:**
    *   **List View Refresh:** `hx-get` on `customerTable-container` triggers `load, refreshCustomerList from:body` to fetch `customer_table` partial.
    *   **Form Loading:** `hx-get` on "Edit" and "Delete" buttons targets `#modalContent` to load the `_customer_form.html` or `_confirm_delete.html` partials into the modal.
    *   **Form Submission:** `hx-post` for the form with `hx-swap="none"` and `HX-Trigger="refreshCustomerList"` ensures the list reloads after successful submission without a full page refresh.
    *   **Cascading Dropdowns:** `hx-get` on country dropdowns targets corresponding state dropdowns, passing the selected country ID. Similarly, state dropdowns trigger `hx-get` for city dropdowns.
*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses `x-data`, `x-show`, and `x-transition` for declarative show/hide and smooth transitions, triggered by HTMX `on click` events.
    *   The `on click` attributes on buttons handle adding/removing specific Tailwind classes (`.block`, `.opacity-100`, `.scale-100`) to control modal visibility and animation.
*   **DataTables for List Views:**
    *   The `_customer_table.html` partial initializes `DataTables` on `$(document).ready()`, ensuring client-side searching, sorting, and pagination.

---

### Final Notes

This comprehensive plan provides a blueprint for an automated and structured migration of the Customer Master - Edit module to Django. By adhering to the principles of fat models, thin views, and HTMX/Alpine.js for dynamic interactions, the resulting application will be modern, highly performant, and easily maintainable. The emphasis on testing ensures a high degree of confidence in the migrated functionality. Automated tools can now be directed to generate much of this code, significantly reducing development time and potential human error.