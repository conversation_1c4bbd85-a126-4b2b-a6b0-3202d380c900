## ASP.NET to Django Conversion Script: Customer Master Print Details

This document outlines a modernization plan to transition your existing ASP.NET customer print details functionality to a modern Django-based solution. Our approach emphasizes automated conversion, leveraging Django's robust framework, HTMX for dynamic interactions, Alpine.js for lightweight UI state management, and DataTables for enhanced data presentation. We will transform the legacy Crystal Reports viewer into a clean, web-native reporting solution.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (e.g., `sales` application).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination where applicable (for list views).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

The original ASP.NET code for `CustomerMaster_Print_Details.aspx` primarily serves as a report viewer for a specific customer. It retrieves customer details, along with associated city, state, and country names, and company information, then displays it via Crystal Reports. While the ASP.NET page itself is a detail/report view, a complete modernization plan typically implies the underlying entity (Customer) would also have standard CRUD (Create, Read, Update, Delete) operations. Therefore, this plan will provide the full Django CRUD setup for `Customer` (including a list view using DataTables) and then specifically address the "print details" functionality as a detailed read view.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
Based on the `SELECT` queries and `ReportParameter` settings in the C# code, we identify the following tables and their key columns:

*   **`SD_Cust_master`**: This is the primary customer table.
    *   `CustomerId` (likely primary key, integer)
    *   `CompId` (foreign key to a Company table, integer)
    *   `SysDate` (date/time)
    *   `RegdCity` (foreign key to `tblCity`, integer)
    *   `RegdState` (foreign key to `tblState`, integer)
    *   `RegdCountry` (foreign key to `tblCountry`, integer)
    *   `WorkCity` (foreign key to `tblCity`, integer)
    *   `WorkState` (foreign key to `tblState`, integer)
    *   `WorkCountry` (foreign key to `tblCountry`, integer)
    *   `MaterialDelCity` (foreign key to `tblCity`, integer)
    *   `MaterialDelState` (foreign key to `tblState`, integer)
    *   `MaterialDelCountry` (foreign key to `tblCountry`, integer)
    *   `CustomerName` (inferred string field for display/identification, as `SELECT *` is used)
    *   `RegdAddress`, `WorkAddress`, `MaterialDelAddress` (inferred string fields for full addresses, as city/state/country alone don't form a complete address).

*   **`tblCity`**:
    *   `CityId` (primary key, integer)
    *   `CityName` (string)

*   **`tblState`**:
    *   `SId` (primary key, integer, effectively `StateId`)
    *   `StateName` (string)

*   **`tblCountry`**:
    *   `CId` (primary key, integer, effectively `CountryId`)
    *   `CountryName` (string)

*   **`tblCompany`**: This table is inferred from the `fun.getCompany(cId)` and `fun.CompAdd(cId)` calls.
    *   `CompId` (primary key, integer)
    *   `CompanyName` (string)
    *   `Address` (string)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET code primarily performs a **Read (Detail View)** operation:
*   It retrieves a single customer record based on `CustomerId` and `CompId`.
*   It then performs several additional `SELECT` queries to resolve City, State, and Country names from their respective ID fields stored in `SD_Cust_master`.
*   It also retrieves company name and address using helper functions.
*   All this aggregated data is then displayed via a Crystal Report viewer.
*   A "Cancel" button redirects the user, implying navigation back to a list or summary page.

No direct Create, Update, or Delete operations are present in this specific ASP.NET file. However, for a comprehensive Django modernization, we will implement the full CRUD cycle for the `Customer` entity, with the "print details" becoming a dedicated `DetailView` within that suite.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The primary UI component is `CR:CrystalReportViewer`. This component is responsible for rendering the report generated by `CrystalReports.Engine`.
*   **`CrystalReportViewer`**: Displays formatted customer details and related location/company information in a printable report format. In Django, this will be replaced by a dedicated HTML template (`print_details.html`) that dynamically renders the fetched data, optimized for printing.
*   **`asp:Button ID="btnCancel"`**: Triggers a page redirect. In Django, this will be a link or button that uses HTMX to navigate or close a modal, or a standard link redirect.

### Step 4: Generate Django Code

We will create a new Django application named `sales` for this module.

#### 4.1 Models (`sales/models.py`)

**Task:** Create Django models based on the identified database schema.

**Instructions:**
We define models for `Customer`, `City`, `State`, `Country`, and `Company`. Foreign key relationships will automatically handle the lookups that were done manually in the C# code. Methods will be added to the `Customer` and `Company` models to encapsulate the business logic of retrieving formatted data, adhering to the "Fat Model" principle.

```python
from django.db import models

class Company(models.Model):
    # This model is inferred from fun.getCompany(cId) and fun.CompAdd(cId)
    # Assuming 'CompId' in SD_Cust_master maps to this table's PK
    company_id = models.IntegerField(primary_key=True, db_column='CompId')
    company_name = models.CharField(max_length=255, db_column='CompanyName')
    address = models.CharField(max_length=500, db_column='Address', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblCompany'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name or f"Company {self.company_id}"

    def get_company_details(self):
        """Returns a dictionary of company name and address."""
        return {
            'name': self.company_name,
            'address': self.address,
        }

class City(models.Model):
    city_id = models.IntegerField(primary_key=True, db_column='CityId')
    city_name = models.CharField(max_length=255, db_column='CityName')

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class State(models.Model):
    state_id = models.IntegerField(primary_key=True, db_column='SId')
    state_name = models.CharField(max_length=255, db_column='StateName')

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class Country(models.Model):
    country_id = models.IntegerField(primary_key=True, db_column='CId')
    country_name = models.CharField(max_length=255, db_column='CountryName')

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class Customer(models.Model):
    customer_id = models.IntegerField(primary_key=True, db_column='CustomerId')
    customer_name = models.CharField(max_length=255, db_column='CustomerName', null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True, db_column='CompId')
    sys_date = models.DateTimeField(db_column='SysDate', null=True, blank=True)

    regd_address = models.CharField(max_length=255, db_column='RegdAddress', null=True, blank=True)
    regd_city = models.ForeignKey(City, on_delete=models.SET_NULL, null=True, blank=True, db_column='RegdCity', related_name='regd_customers')
    regd_state = models.ForeignKey(State, on_delete=models.SET_NULL, null=True, blank=True, db_column='RegdState', related_name='regd_customers')
    regd_country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True, db_column='RegdCountry', related_name='regd_customers')

    work_address = models.CharField(max_length=255, db_column='WorkAddress', null=True, blank=True)
    work_city = models.ForeignKey(City, on_delete=models.SET_NULL, null=True, blank=True, db_column='WorkCity', related_name='work_customers')
    work_state = models.ForeignKey(State, on_delete=models.SET_NULL, null=True, blank=True, db_column='WorkState', related_name='work_customers')
    work_country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True, db_column='WorkCountry', related_name='work_customers')

    material_del_address = models.CharField(max_length=255, db_column='MaterialDelAddress', null=True, blank=True)
    material_del_city = models.ForeignKey(City, on_delete=models.SET_NULL, null=True, blank=True, db_column='MaterialDelCity', related_name='material_del_customers')
    material_del_state = models.ForeignKey(State, on_delete=models.SET_NULL, null=True, blank=True, db_column='MaterialDelState', related_name='material_del_customers')
    material_del_country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True, db_column='MaterialDelCountry', related_name='material_del_customers')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name or f"Customer {self.customer_id}"

    def get_regd_location_details(self):
        """Returns a dict with registered city, state, country names."""
        return {
            'city': self.regd_city.city_name if self.regd_city else 'N/A',
            'state': self.regd_state.state_name if self.regd_state else 'N/A',
            'country': self.regd_country.country_name if self.regd_country else 'N/A',
        }

    def get_work_location_details(self):
        """Returns a dict with work city, state, country names."""
        return {
            'city': self.work_city.city_name if self.work_city else 'N/A',
            'state': self.work_state.state_name if self.work_state else 'N/A',
            'country': self.work_country.country_name if self.work_country else 'N/A',
        }

    def get_material_del_location_details(self):
        """Returns a dict with material delivery city, state, country names."""
        return {
            'city': self.material_del_city.city_name if self.material_del_city else 'N/A',
            'state': self.material_del_state.state_name if self.material_del_state else 'N/A',
            'country': self.material_del_country.country_name if self.material_del_country else 'N/A',
        }

    def get_formatted_regd_date(self):
        """Formats the system date as DD-MM-YYYY (matching ASP.NET's FromDateDMY)."""
        if self.sys_date:
            return self.sys_date.strftime('%d-%m-%Y')
        return 'N/A'

    def get_absolute_url(self):
        """Returns the URL to the detail print view of the customer."""
        from django.urls import reverse
        return reverse('sales:customer_print_details', args=[self.customer_id])
```

#### 4.2 Forms (`sales/forms.py`)

**Task:** Define a Django form for customer input.

**Instructions:**
A `ModelForm` is created for `Customer`. We include essential fields for a typical CRUD operation. For foreign key fields, Django automatically provides dropdowns. Tailwind CSS classes are applied to widgets for consistent styling.

```python
from django import forms
from .models import Customer, Company, City, State, Country

class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        # Include a subset of fields for typical CRUD, adjust as needed.
        # Ensure 'customer_id' is not included if it's an auto-generated PK,
        # but here it's an existing PK so we might need to handle it or infer a different PK.
        # Assuming customer_id is assigned externally or from DB.
        fields = [
            'customer_name', 'company', 'sys_date',
            'regd_address', 'regd_city', 'regd_state', 'regd_country',
            'work_address', 'work_city', 'work_state', 'work_country',
            'material_del_address', 'material_del_city', 'material_del_state', 'material_del_country'
        ]
        widgets = {
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            
            'regd_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'regd_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'work_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'work_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'material_del_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'material_del_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

#### 4.3 Views (`sales/views.py`)

**Task:** Implement CRUD operations and the specific "Print Details" view using Class-Based Views (CBVs).

**Instructions:**
We'll define `ListView`, `CreateView`, `UpdateView`, `DeleteView` for general customer management.
For the specific "Print Details" functionality, we'll create a `CustomerPrintDetailView` that fetches all necessary related data using `select_related` to optimize database queries. This view will pass formatted data to its template.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Customer, Company # Import other models if needed directly
from .forms import CustomerForm

# Helper for getting company ID from session (replace with actual session management logic)
# In a real app, this would come from the authenticated user's profile or request context.
def get_company_id_from_session(request):
    # This is a placeholder. In a real Django app, company ID would typically be
    # linked to the authenticated user or passed securely.
    # For demonstration, we'll mimic the ASP.NET's session logic.
    # Ensure Django's session middleware is configured.
    return request.session.get('compid', 1) # Default to 1 if not found in session

class CustomerListView(ListView):
    model = Customer
    template_name = 'sales/customer/list.html'
    context_object_name = 'customers'

    def get_queryset(self):
        # Filter customers by company ID from session, mirroring ASP.NET logic
        company_id = get_company_id_from_session(self.request)
        return Customer.objects.filter(company_id=company_id).select_related('company')

class CustomerTablePartialView(ListView):
    # This view is for HTMX to load the DataTables content dynamically
    model = Customer
    template_name = 'sales/customer/_customer_table.html'
    context_object_name = 'customers'

    def get_queryset(self):
        company_id = get_company_id_from_session(self.request)
        return Customer.objects.filter(company_id=company_id).select_related(
            'regd_city', 'regd_state', 'regd_country',
            'work_city', 'work_state', 'work_country',
            'material_del_city', 'material_del_state', 'material_del_country'
        )

class CustomerCreateView(CreateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'sales/customer/form.html'
    success_url = reverse_lazy('sales:customer_list') # Redirect to list after success

    def form_valid(self, form):
        # Assign company ID from session before saving, mirroring ASP.NET logic
        form.instance.company_id = get_company_id_from_session(self.request)
        response = super().form_valid(form)
        messages.success(self.request, 'Customer added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to swap
                headers={
                    'HX-Trigger': 'refreshCustomerList', # Custom event to trigger list refresh
                    'HX-Location': '{"path": "%s", "target": "#main-content"}' % reverse_lazy('sales:customer_list') # Optional: navigate if full page needed
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Prefill sys_date with current date if it's a new form
        if not self.object:
            context['form'].fields['sys_date'].initial = timezone.now().date()
        return context


class CustomerUpdateView(UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'sales/customer/form.html'
    pk_url_kwarg = 'pk' # Ensure it looks for 'pk' in URL
    success_url = reverse_lazy('sales:customer_list')

    def form_valid(self, form):
        # Ensure company ID is maintained or updated based on session if desired
        form.instance.company_id = get_company_id_from_session(self.request)
        response = super().form_valid(form)
        messages.success(self.request, 'Customer updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList',
                    'HX-Location': '{"path": "%s", "target": "#main-content"}' % reverse_lazy('sales:customer_list')
                }
            )
        return response

class CustomerDeleteView(DeleteView):
    model = Customer
    template_name = 'sales/customer/confirm_delete.html'
    pk_url_kwarg = 'pk'
    success_url = reverse_lazy('sales:customer_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList',
                    'HX-Location': '{"path": "%s", "target": "#main-content"}' % reverse_lazy('sales:customer_list')
                }
            )
        return response

# Specific view for the "Print Details" functionality
class CustomerPrintDetailView(DetailView):
    model = Customer
    template_name = 'sales/customer/print_details.html'
    context_object_name = 'customer'
    pk_url_kwarg = 'customer_id' # Match the query string param name

    def get_queryset(self):
        # Eager load all related objects to avoid N+1 queries for location details
        # and the company details.
        company_id = get_company_id_from_session(self.request)
        return super().get_queryset().filter(company_id=company_id).select_related(
            'company',
            'regd_city', 'regd_state', 'regd_country',
            'work_city', 'work_state', 'work_country',
            'material_del_city', 'material_del_state', 'material_del_country'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer = context['customer']

        # Prepare parameters as seen in Crystal Reports logic
        # Company details
        company_details = customer.company.get_company_details() if customer.company else {'name': 'N/A', 'address': 'N/A'}
        context['company_name'] = company_details['name']
        context['company_address'] = company_details['address']
        
        # Formatted registration date
        context['reg_date_formatted'] = customer.get_formatted_regd_date()

        # Location details
        context['regd_location'] = customer.get_regd_location_details()
        context['work_location'] = customer.get_work_location_details()
        context['material_del_location'] = customer.get_material_del_location_details()
        
        return context

```

#### 4.4 Templates (`sales/templates/sales/customer/`)

**Task:** Create templates for each view, including list, form (partial), delete confirmation (partial), and the specific print details template.

**Instructions:**
Templates will follow the DRY principle, extend `core/base.html`, and heavily utilize HTMX for dynamic content updates (e.g., loading forms into modals) and DataTables for list presentation.

**1. `list.html`** (Main Customer List Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customers</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300"
            hx-get="{% url 'sales:customer_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Customer
        </button>
    </div>
    
    <div id="customerTable-container"
         hx-trigger="load, refreshCustomerList from:body"
         hx-get="{% url 'sales:customer_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Customers...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         @refreshCustomerList.window="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 my-8"
             @click.stop # Prevents clicks inside modal from closing it
             x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
             <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modalState', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });

        // Initialize modal's Alpine state when HTMX loads content into it
        htmx.onLoad(function(content) {
            const modal = document.getElementById('modal');
            if (modal && modal.querySelector('#modalContent')) {
                Alpine.store('modalState').open();
            }
        });

        // Hide modal on HX-Trigger that refreshes list, indicating success
        document.body.addEventListener('refreshCustomerList', function() {
            Alpine.store('modalState').close();
            // Also explicitly hide the modal div
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
                modal.classList.add('hidden');
            }
        });

        // Event listener for closing modal via 'Cancel' button or direct click outside
        document.body.addEventListener('click', function(event) {
            if (event.target.closest('#modal') && event.target.id === 'modal') {
                const modal = document.getElementById('modal');
                if (modal) {
                    modal.classList.remove('is-active');
                    modal.classList.add('hidden');
                    Alpine.store('modalState').close();
                }
            }
        });
    });
</script>
{% endblock %}
```

**2. `_customer_table.html`** (Partial for DataTables)

```html
<div class="overflow-x-auto bg-white shadow-lg rounded-lg">
    <table id="customerTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered City</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registration Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for customer in customers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ customer.customer_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ customer.company.company_name|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ customer.regd_city.city_name|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ customer.get_formatted_regd_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 mr-2"
                        hx-get="{% url 'sales:customer_edit' customer.customer_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 mr-2"
                        hx-get="{% url 'sales:customer_delete' customer.customer_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                    <a 
                        href="{% url 'sales:customer_print_details' customer.customer_id %}"
                        target="_blank"
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded-md transition duration-300">
                        Print Details
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-3 px-4 text-center text-sm text-gray-500">No customers found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#customerTable')) {
            $('#customerTable').DataTable().destroy();
        }
        $('#customerTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "search": "Filter records:",
                "lengthMenu": "Show _MENU_ entries",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "infoEmpty": "Showing 0 to 0 of 0 entries",
                "infoFiltered": "(filtered from _MAX_ total entries)",
                "zeroRecords": "No matching records found",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            },
            "dom": 'lfrtip' // 'l' for length menu, 'f' for filtering input, 'r' for processing display, 't' for table, 'i' for table information, 'p' for pagination.
        });
    });
</script>
```

**3. `form.html`** (Partial for Create/Update Forms)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Customer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.successful) htmx.trigger(document.body, 'refreshCustomerList')">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-medium text-gray-800 mb-3">Basic Information</h4>
                <div class="mb-4">
                    <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer Name</label>
                    {{ form.customer_name }}
                    {% if form.customer_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.customer_name.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.company.id_for_label }}" class="block text-sm font-medium text-gray-700">Company</label>
                    {{ form.company }}
                    {% if form.company.errors %}<p class="text-red-500 text-xs mt-1">{{ form.company.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.sys_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Registration Date</label>
                    {{ form.sys_date }}
                    {% if form.sys_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sys_date.errors }}</p>{% endif %}
                </div>
            </div>

            <div>
                <h4 class="text-lg font-medium text-gray-800 mb-3">Registered Address</h4>
                <div class="mb-4">
                    <label for="{{ form.regd_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address Line</label>
                    {{ form.regd_address }}
                    {% if form.regd_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_address.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.regd_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
                    {{ form.regd_city }}
                    {% if form.regd_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_city.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.regd_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
                    {{ form.regd_state }}
                    {% if form.regd_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_state.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.regd_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                    {{ form.regd_country }}
                    {% if form.regd_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_country.errors }}</p>{% endif %}
                </div>
            </div>
        </div>

        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-medium text-gray-800 mb-3">Work Address</h4>
                <div class="mb-4">
                    <label for="{{ form.work_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address Line</label>
                    {{ form.work_address }}
                    {% if form.work_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_address.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.work_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
                    {{ form.work_city }}
                    {% if form.work_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_city.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.work_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
                    {{ form.work_state }}
                    {% if form.work_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_state.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.work_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                    {{ form.work_country }}
                    {% if form.work_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_country.errors }}</p>{% endif %}
                </div>
            </div>

            <div>
                <h4 class="text-lg font-medium text-gray-800 mb-3">Material Delivery Address</h4>
                <div class="mb-4">
                    <label for="{{ form.material_del_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address Line</label>
                    {{ form.material_del_address }}
                    {% if form.material_del_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_address.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.material_del_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
                    {{ form.material_del_city }}
                    {% if form.material_del_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_city.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.material_del_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
                    {{ form.material_del_state }}
                    {% if form.material_del_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_state.errors }}</p>{% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.material_del_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                    {{ form.material_del_country }}
                    {% if form.material_del_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_country.errors }}</p>{% endif %}
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
                Save
            </button>
        </div>
    </form>
</div>
```

**4. `confirm_delete.html`** (Partial for Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete customer "{{ customer.customer_name }}"?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.successful) htmx.trigger(document.body, 'refreshCustomerList')">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
                Delete
            </button>
        </div>
    </form>
</div>
```

**5. `print_details.html`** (Specific for Print Details, mimics Crystal Report)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 bg-white shadow-lg rounded-lg print-area">
    <div class="flex justify-between items-center mb-6 border-b pb-4">
        <h2 class="text-3xl font-bold text-gray-800">Customer Details Report</h2>
        <div class="flex space-x-2 no-print">
            <button 
                onclick="window.print()" 
                class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
                <i class="fas fa-print mr-2"></i> Print
            </button>
            <a 
                href="{% url 'sales:customer_list' %}" 
                class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
                <i class="fas fa-arrow-left mr-2"></i> Back to List
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Company Details</h3>
            <p class="text-gray-700"><span class="font-medium">Company Name:</span> {{ company_name }}</p>
            <p class="text-gray-700"><span class="font-medium">Company Address:</span> {{ company_address }}</p>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Customer Basic Information</h3>
            <p class="text-gray-700"><span class="font-medium">Customer ID:</span> {{ customer.customer_id }}</p>
            <p class="text-gray-700"><span class="font-medium">Customer Name:</span> {{ customer.customer_name }}</p>
            <p class="text-gray-700"><span class="font-medium">Registration Date:</span> {{ reg_date_formatted }}</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8 border-t pt-8">
        <div>
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Registered Address</h3>
            <p class="text-gray-700"><span class="font-medium">Address:</span> {{ customer.regd_address|default:"N/A" }}</p>
            <p class="text-gray-700"><span class="font-medium">City:</span> {{ regd_location.city }}</p>
            <p class="text-gray-700"><span class="font-medium">State:</span> {{ regd_location.state }}</p>
            <p class="text-gray-700"><span class="font-medium">Country:</span> {{ regd_location.country }}</p>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Work Address</h3>
            <p class="text-gray-700"><span class="font-medium">Address:</span> {{ customer.work_address|default:"N/A" }}</p>
            <p class="text-gray-700"><span class="font-medium">City:</span> {{ work_location.city }}</p>
            <p class="text-gray-700"><span class="font-medium">State:</span> {{ work_location.state }}</p>
            <p class="text-gray-700"><span class="font-medium">Country:</span> {{ work_location.country }}</p>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Material Delivery Address</h3>
            <p class="text-gray-700"><span class="font-medium">Address:</span> {{ customer.material_del_address|default:"N/A" }}</p>
            <p class="text-gray-700"><span class="font-medium">City:</span> {{ material_del_location.city }}</p>
            <p class="text-gray-700"><span class="font-medium">State:</span> {{ material_del_location.state }}</p>
            <p class="font-medium">Country:</span> {{ material_del_location.country }}</p>
        </div>
    </div>
    
    <div class="mt-8 pt-4 border-t text-sm text-gray-600 print-footer">
        <p>Report Generated on: {{ "now"|date:"d M Y H:i" }}</p>
        <p>Source: AutoERP Django System</p>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .print-area, .print-area * {
            visibility: visible;
        }
        .print-area {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            margin: 0;
            padding: 20px;
            box-shadow: none;
            border-radius: 0;
        }
        .no-print {
            display: none !important;
        }
        /* Adjust for better print layout */
        .grid {
            display: block; /* Ensure grid columns stack on print */
        }
        .grid > div {
            margin-bottom: 20px;
            page-break-inside: avoid; /* Avoid breaking sections across pages */
        }
    }
</style>
{% endblock %}
```

#### 4.5 URLs (`sales/urls.py`)

**Task:** Define URL patterns for all customer-related views.

**Instructions:**
We'll create paths for the list view, partial table view (for HTMX), CRUD operations (add, edit, delete), and the specific print details view. Using a namespace (`app_name = 'sales'`) is a good practice.

```python
from django.urls import path
from .views import (
    CustomerListView, CustomerTablePartialView,
    CustomerCreateView, CustomerUpdateView, CustomerDeleteView,
    CustomerPrintDetailView
)

app_name = 'sales'

urlpatterns = [
    # Customer List and HTMX partial
    path('customers/', CustomerListView.as_view(), name='customer_list'),
    path('customers/table/', CustomerTablePartialView.as_view(), name='customer_table'),

    # Customer CRUD operations (HTMX driven via modals)
    path('customers/add/', CustomerCreateView.as_view(), name='customer_add'),
    path('customers/edit/<int:pk>/', CustomerUpdateView.as_view(), name='customer_edit'),
    path('customers/delete/<int:pk>/', CustomerDeleteView.as_view(), name='customer_delete'),

    # Customer Print Details (replaces Crystal Report viewer)
    # The 'customer_id' in the path matches the 'pk_url_kwarg' in CustomerPrintDetailView
    path('customers/print/<int:customer_id>/', CustomerPrintDetailView.as_view(), name='customer_print_details'),
]
```

#### 4.6 Tests (`sales/tests.py`)

**Task:** Write comprehensive tests for models and views.

**Instructions:**
Include unit tests for model methods and properties, ensuring business logic works as expected. Add integration tests for all views, covering GET and POST requests, template usage, context data, and HTMX responses.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Customer, Company, City, State, Country

# Helper function to set session data for company_id in tests
def set_company_id_in_session(client, company_id):
    session = client.session
    session['compid'] = company_id
    session.save()

class LocationModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.city = City.objects.create(city_id=1, city_name='Springfield')
        cls.state = State.objects.create(state_id=1, state_name='Illinois')
        cls.country = Country.objects.create(country_id=1, country_name='USA')

    def test_city_creation(self):
        self.assertEqual(self.city.city_name, 'Springfield')
        self.assertEqual(str(self.city), 'Springfield')
    
    def test_state_creation(self):
        self.assertEqual(self.state.state_name, 'Illinois')
        self.assertEqual(str(self.state), 'Illinois')

    def test_country_creation(self):
        self.assertEqual(self.country.country_name, 'USA')
        self.assertEqual(str(self.country), 'USA')

class CompanyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(
            company_id=100,
            company_name='Acme Corp',
            address='123 Main St, Anytown'
        )
    
    def test_company_creation(self):
        self.assertEqual(self.company.company_name, 'Acme Corp')
        self.assertEqual(self.company.address, '123 Main St, Anytown')
        self.assertEqual(str(self.company), 'Acme Corp')

    def test_get_company_details(self):
        details = self.company.get_company_details()
        self.assertEqual(details['name'], 'Acme Corp')
        self.assertEqual(details['address'], '123 Main St, Anytown')

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(company_id=1, company_name='Global Inc', address='456 Tech Ave')
        cls.city = City.objects.create(city_id=10, city_name='Metropolis')
        cls.state = State.objects.create(state_id=20, state_name='New York')
        cls.country = Country.objects.create(country_id=30, country_name='USA')
        
        cls.customer = Customer.objects.create(
            customer_id=1,
            customer_name='John Doe',
            company=cls.company,
            sys_date=timezone.datetime(2023, 1, 15, 10, 30, 0, tzinfo=timezone.utc),
            regd_address='123 Regd St',
            regd_city=cls.city,
            regd_state=cls.state,
            regd_country=cls.country,
            work_address='456 Work Blvd',
            work_city=cls.city,
            work_state=cls.state,
            work_country=cls.country,
            material_del_address='789 Del Rd',
            material_del_city=cls.city,
            material_del_state=cls.state,
            material_del_country=cls.country,
        )

    def test_customer_creation(self):
        customer = Customer.objects.get(customer_id=1)
        self.assertEqual(customer.customer_name, 'John Doe')
        self.assertEqual(customer.company.company_name, 'Global Inc')
        self.assertEqual(customer.regd_city.city_name, 'Metropolis')
    
    def test_get_regd_location_details(self):
        details = self.customer.get_regd_location_details()
        self.assertEqual(details['city'], 'Metropolis')
        self.assertEqual(details['state'], 'New York')
        self.assertEqual(details['country'], 'USA')

    def test_get_work_location_details(self):
        details = self.customer.get_work_location_details()
        self.assertEqual(details['city'], 'Metropolis')
        self.assertEqual(details['state'], 'New York')
        self.assertEqual(details['country'], 'USA')

    def test_get_material_del_location_details(self):
        details = self.customer.get_material_del_location_details()
        self.assertEqual(details['city'], 'Metropolis')
        self.assertEqual(details['state'], 'New York')
        self.assertEqual(details['country'], 'USA')

    def test_get_formatted_regd_date(self):
        self.assertEqual(self.customer.get_formatted_regd_date(), '15-01-2023')

    def test_get_formatted_regd_date_no_date(self):
        customer_no_date = Customer.objects.create(customer_id=2, customer_name='Jane Doe', company=self.company, sys_date=None)
        self.assertEqual(customer_no_date.get_formatted_regd_date(), 'N/A')

class CustomerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_active = Company.objects.create(company_id=1, company_name='Active Co')
        cls.company_inactive = Company.objects.create(company_id=2, company_name='Inactive Co')
        cls.city = City.objects.create(city_id=1, city_name='TestCity')
        cls.state = State.objects.create(state_id=1, state_name='TestState')
        cls.country = Country.objects.create(country_id=1, country_name='TestCountry')

        cls.customer1 = Customer.objects.create(
            customer_id=101, customer_name='Customer A', company=cls.company_active, sys_date=timezone.now(),
            regd_city=cls.city, regd_state=cls.state, regd_country=cls.country
        )
        cls.customer2 = Customer.objects.create(
            customer_id=102, customer_name='Customer B', company=cls.company_active, sys_date=timezone.now(),
            regd_city=cls.city, regd_state=cls.state, regd_country=cls.country
        )
        cls.customer3 = Customer.objects.create(
            customer_id=103, customer_name='Customer C', company=cls.company_inactive, sys_date=timezone.now(),
            regd_city=cls.city, regd_state=cls.state, regd_country=cls.country
        )
    
    def setUp(self):
        self.client = Client()
        # Set session company ID for all tests
        set_company_id_in_session(self.client, self.company_active.company_id)
    
    def test_customer_list_view(self):
        response = self.client.get(reverse('sales:customer_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customer/list.html')
        self.assertIn('customers', response.context)
        # Should only show customers for the active company
        self.assertEqual(response.context['customers'].count(), 2) 
        self.assertContains(response, 'Customer A')
        self.assertNotContains(response, 'Customer C')
        
    def test_customer_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:customer_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customer/_customer_table.html')
        self.assertIn('customers', response.context)
        self.assertEqual(response.context['customers'].count(), 2)
        self.assertContains(response, 'Customer A')
        self.assertNotContains(response, 'Customer C') # Not for inactive company

    def test_customer_create_view_get(self):
        response = self.client.get(reverse('sales:customer_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customer/form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], CustomerForm)
        
    def test_customer_create_view_post_htmx_success(self):
        new_customer_id = 104 # Assuming it auto-increments or is provided
        data = {
            'customer_id': new_customer_id, # If PK is manual/integer type
            'customer_name': 'New Customer X',
            'company': self.company_active.company_id,
            'sys_date': timezone.now().strftime('%Y-%m-%d'),
            'regd_city': self.city.city_id, 'regd_state': self.state.state_id, 'regd_country': self.country.country_id,
            'work_city': self.city.city_id, 'work_state': self.state.state_id, 'work_country': self.country.country_id,
            'material_del_city': self.city.city_id, 'material_del_state': self.state.state_id, 'material_del_country': self.country.country_id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sales:customer_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(Customer.objects.filter(customer_name='New Customer X').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_update_view_get(self):
        response = self.client.get(reverse('sales:customer_edit', args=[self.customer1.customer_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customer/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.customer1)
        
    def test_customer_update_view_post_htmx_success(self):
        updated_name = 'Customer A Updated'
        data = {
            'customer_id': self.customer1.customer_id,
            'customer_name': updated_name,
            'company': self.company_active.company_id,
            'sys_date': self.customer1.sys_date.strftime('%Y-%m-%d'),
            'regd_city': self.city.city_id, 'regd_state': self.state.state_id, 'regd_country': self.country.country_id,
            'work_city': self.city.city_id, 'work_state': self.state.state_id, 'work_country': self.country.country_id,
            'material_del_city': self.city.city_id, 'material_del_state': self.state.state_id, 'material_del_country': self.country.country_id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sales:customer_edit', args=[self.customer1.customer_id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.customer1.refresh_from_db()
        self.assertEqual(self.customer1.customer_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_delete_view_get(self):
        response = self.client.get(reverse('sales:customer_delete', args=[self.customer1.customer_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customer/confirm_delete.html')
        self.assertIn('customer', response.context)
        self.assertEqual(response.context['customer'], self.customer1)

    def test_customer_delete_view_post_htmx_success(self):
        customer_id_to_delete = self.customer1.customer_id
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sales:customer_delete', args=[customer_id_to_delete]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Customer.objects.filter(customer_id=customer_id_to_delete).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_print_detail_view(self):
        response = self.client.get(reverse('sales:customer_print_details', args=[self.customer1.customer_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customer/print_details.html')
        self.assertIn('customer', response.context)
        self.assertEqual(response.context['customer'], self.customer1)
        self.assertIn('company_name', response.context)
        self.assertIn('regd_location', response.context)
        self.assertContains(response, self.customer1.customer_name)
        self.assertContains(response, self.company_active.company_name)
        self.assertContains(response, self.city.city_name)
        self.assertContains(response, self.customer1.get_formatted_regd_date())
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates demonstrate extensive use of HTMX and Alpine.js.

*   **HTMX for dynamic content**:
    *   The `customerTable-container` uses `hx-trigger="load, refreshCustomerList from:body"` and `hx-get="{% url 'sales:customer_table' %}"` to load the DataTables partial dynamically on page load and whenever a `refreshCustomerList` event is triggered (after CRUD operations).
    *   Add/Edit/Delete buttons use `hx-get` to fetch modal content (`form.html` or `confirm_delete.html`) into the `#modalContent` div.
    *   Form submissions (`hx-post`) on the partial forms use `hx-swap="none"` and `hx-on::after-request="if(event.detail.successful) htmx.trigger(document.body, 'refreshCustomerList')"` to trigger a refresh of the customer list without swapping content directly on the form.
    *   HTTP 204 (No Content) status codes are used for successful HTMX POST requests to prevent unwanted content swaps.
    *   `HX-Trigger` headers are sent from the views to signal the frontend to refresh the customer list, closing the modal.

*   **Alpine.js for UI state management**:
    *   A main `#modal` div uses `x-data="{ showModal: false }"` and `x-show="showModal"` to control its visibility.
    *   The modal is opened by adding `add .is-active to #modal` via `_` (hyperscript) when add/edit/delete buttons are clicked.
    *   A custom Alpine.js store `Alpine.store('modalState')` is used to manage the modal's open/close state, ensuring it's properly hidden after HTMX actions or manual cancellation.
    *   `@refreshCustomerList.window="showModal = false"` on the modal listens for the HTMX trigger event to close itself gracefully.
    *   `on click if event.target.id == 'modal' remove .is-active from me` provides a way to close the modal by clicking outside of its content area.

*   **DataTables for list views**:
    *   The `_customer_table.html` partial initializes DataTables on the `customerTable` element using `$(document).ready(function() { $('#customerTable').DataTable({...}); });`. This ensures the table is interactive with client-side searching, sorting, and pagination.
    *   `lfrtip` DOM option for DataTables provides standard features.

*   **Print Details View**:
    *   The `print_details.html` template includes CSS media queries (`@media print`) to hide non-printing elements (like buttons) and optimize the layout for a print output, providing a web-native printable report without complex Crystal Reports dependencies.

### Final Notes

This comprehensive plan provides a clear path for modernizing the ASP.NET customer print details functionality to a robust Django application. By leveraging Django's ORM, Class-Based Views, HTMX, Alpine.js, and DataTables, we achieve:
*   **Improved Performance:** Optimized database queries with `select_related` and dynamic content loading with HTMX.
*   **Modern User Experience:** Seamless interactions without full page reloads, responsive design with Tailwind CSS, and enhanced data navigation with DataTables.
*   **Maintainability:** Clean code separation with Fat Models and Thin Views, making the application easier to understand, extend, and debug.
*   **Scalability:** Django's architecture is built for scalability, allowing the application to grow with business needs.
*   **Reduced Licensing Costs:** Eliminating proprietary reporting tools like Crystal Reports reduces software licensing and dependency.

This approach significantly reduces manual effort by focusing on established patterns and automation-friendly structures, enabling a smoother transition managed effectively with AI-assisted guidance.