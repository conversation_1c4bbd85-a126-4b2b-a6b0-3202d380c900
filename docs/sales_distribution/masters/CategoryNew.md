## ASP.NET to Django Conversion Script:

This document outlines the modernization plan for transitioning your ASP.NET Category of Work Order module to a modern Django-based solution. Our approach prioritizes automation, leverages contemporary web technologies like HTMX and Alpine.js, and adheres to Django's best practices, ensuring a robust, scalable, and maintainable application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET code extensively interacts with `tblSD_WO_Category`. Based on `fillGrid()`'s SQL `SELECT` and `GridView1`'s `DataKeyNames` and `TemplateFields`, we identify the following:

- **Table Name:** `tblSD_WO_Category`
- **Columns:**
    - `CId`: Integer (Primary Key, identified by `DataKeyNames="CId"`)
    - `CName`: String (Category Name)
    - `Symbol`: String (single character, for abbreviation)
    - `HasSubCat`: String ('1' for Yes, '0' for No)
    - `SysDate`: Date (System Date, audit field)
    - `SysTime`: Time (System Time, audit field)
    - `CompId`: String (Company ID, from session, filtering context)
    - `FinYearId`: String (Financial Year ID, from session, filtering context)
    - `SessionId`: String (User Session ID/Username, audit field)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

- **Create:**
    - New category insertion triggered by `btnInsert` (CommandName="Add" or "Add1").
    - Data collected from `txtCName`, `txtAbb` (Symbol), and `CheckBox1` (HasSubCat) in the GridView's footer or EmptyDataTemplate.
    - **Validation:** `CName` and `Symbol` are required fields.
    - **Business Logic:** `Symbol` must be unique within the combination of `CompId` and `FinYearId`. If not unique, an alert message "Category symbol is already used." is displayed. `Symbol` is converted to uppercase before insertion.
    - **Audit Fields:** `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId` are inserted along with user input.
- **Read:**
    - Data displayed in `GridView1` via `fillGrid()` method.
    - Retrieves all columns from `tblSD_WO_Category`.
    - Filters records by `CompId` and `FinYearId` (`FinYearId<='...'`).
    - Orders by `CId` descending.
    - `HasSubCat` (stored as '1'/'0') is transformed to "Yes"/"No" for display.
    - Supports pagination (`AllowPaging="True"`, `GridView1_PageIndexChanging1`).
- **Update:** Not explicitly implemented in the provided ASP.NET code. However, a modern Django application would include this functionality.
- **Delete:** Not explicitly implemented in the provided ASP.NET code. A modern Django application would include this functionality.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

- **GridView1:** This will be replaced by a Django template using HTML `<table>` elements and enhanced with `DataTables.js` for client-side features (pagination, sorting, searching). HTMX will be used to load this table dynamically.
- **asp:Label (e.g., lblCName, lblAbbrivation):** Displaying read-only data from the database.
- **asp:TextBox (e.g., txtCName, txtAbb):** Used for user input, primarily in the "add new" row (footer/empty template). These will map to Django `forms.TextInput` widgets.
- **asp:CheckBox (e.g., CheckBox1):** Used for `HasSubCat` input. This will map to a Django `forms.CheckboxInput` widget.
- **asp:Button (e.g., btnInsert):** Triggers form submission (create action). This will be an HTML `<button>` with HTMX `hx-post` attributes for dynamic submission and modal interactions.
- **asp:RequiredFieldValidator:** Django's form validation will handle these required field checks.
- **ClientScript.RegisterStartupScript (for `alert`):** Django's messages framework will handle success/error messages, and HTMX/Alpine.js can be used to display these as toasts or in-page notifications.
- **MasterPageFile, ContentPlaceHolders:** These concepts are replaced by Django's template inheritance, specifically extending `core/base.html`.

### Step 4: Generate Django Code

We will structure the Django application as `sales`, with a `category` module inside it.

#### 4.1 Models (`sales/models.py`)

This model will represent the `tblSD_WO_Category` table. We use `managed = False` and `db_table` to map to the existing database table. Business logic, such as `Symbol` uniqueness and `HasSubCat` display, is encapsulated here.

```python
from django.db import models
from django.core.exceptions import ValidationError

class Category(models.Model):
    # CId is the primary key and corresponds to the existing database column 'CId'.
    # Django's AutoField maps perfectly for auto-incrementing integer primary keys.
    CId = models.AutoField(db_column='CId', primary_key=True)
    CName = models.CharField(db_column='CName', max_length=255) # Max length assumed, adjust as per DB schema
    Symbol = models.CharField(db_column='Symbol', max_length=1) # Max length is 1, as per ASP.NET MaxLength="1"
    HasSubCat = models.CharField(db_column='HasSubCat', max_length=1) # Stored as '1' or '0' in the DB

    # Audit/Context fields from ASP.NET session, assumed to be populated by the application
    SysDate = models.DateField(db_column='SysDate', auto_now_add=True) # Automatically sets on creation
    SysTime = models.TimeField(db_column='SysTime', auto_now_add=True) # Automatically sets on creation
    CompId = models.CharField(db_column='CompId', max_length=50) # Max length assumed
    FinYearId = models.CharField(db_column='FinYearId', max_length=50) # Max length assumed
    SessionId = models.CharField(db_column='SessionId', max_length=100) # Max length assumed (username)

    class Meta:
        managed = False  # Tells Django not to manage table creation/modification
        db_table = 'tblSD_WO_Category' # Maps to the existing database table name
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        # Enforce uniqueness for Symbol combined with CompId and FinYearId, mirroring ASP.NET logic
        unique_together = (('Symbol', 'CompId', 'FinYearId'),)

    def __str__(self):
        """Returns a human-readable representation of the category."""
        return f"{self.CName} ({self.Symbol})"
        
    @property
    def has_subcategory_display(self):
        """Converts '1'/'0' from DB to 'Yes'/'No' for display, mirroring ASP.NET logic."""
        return 'Yes' if self.HasSubCat == '1' else 'No'

    @classmethod
    def get_filtered_categories(cls, comp_id, fin_year_id):
        """
        Retrieves categories filtered by company ID and financial year ID,
        ordered by CId descending, replicating ASP.NET's fillGrid() logic.
        """
        return cls.objects.filter(
            CompId=comp_id,
            FinYearId__lte=fin_year_id # Replicates the 'FinYearId<=' condition
        ).order_by('-CId')

    def save(self, *args, **kwargs):
        """
        Overrides the save method to apply business logic before saving.
        - Converts Symbol to uppercase (replicates ASP.NET logic).
        - Ensures HasSubCat is stored as '1' or '0'.
        """
        if self.Symbol:
            self.Symbol = self.Symbol.upper() # ASP.NET converted to uppercase
        
        # Ensure HasSubCat is stored as '1' or '0' based on boolean input from form
        if isinstance(self.HasSubCat, bool):
            self.HasSubCat = '1' if self.HasSubCat else '0'
        elif self.HasSubCat not in ['1', '0']:
             raise ValueError("HasSubCat must be '1' or '0' or a boolean value.")
             
        super().save(*args, **kwargs)

    def clean(self):
        """
        Provides custom validation for the model instance.
        While `unique_together` handles the database constraint,
        this provides a more user-friendly error message that matches the ASP.NET alert.
        """
        # This custom clean method might be redundant if unique_together handles it
        # perfectly with a database error, but it's good practice for custom messages.
        if self.Symbol:
            existing_categories = Category.objects.filter(
                Symbol__iexact=self.Symbol, # Use iexact for case-insensitive check like ASP.NET's .ToUpper()
                CompId=self.CompId,
                FinYearId=self.FinYearId
            )
            # If updating an existing object, exclude itself from the check
            if self.pk:
                existing_categories = existing_categories.exclude(pk=self.pk)
            
            if existing_categories.exists():
                raise ValidationError({'Symbol': 'Category symbol is already used for this Company and Financial Year.'})

```

#### 4.2 Forms (`sales/forms.py`)

A Django `ModelForm` for `Category` to handle user input and validation. We introduce a separate `BooleanField` for `HasSubCat` for better form interaction, mapping it back to '1'/'0' in the `save` method.

```python
from django import forms
from .models import Category

class CategoryForm(forms.ModelForm):
    # This BooleanField is for form input, will be mapped to model's HasSubCat ('1'/'0')
    has_sub_cat_checkbox = forms.BooleanField(
        label="Has SubCategory",
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-5 w-5 text-indigo-600'})
    )

    class Meta:
        model = Category
        # Only include fields that are directly editable by the user from the ASP.NET page.
        # Audit/context fields like CompId, FinYearId, SessionId are set in the view.
        fields = ['CName', 'Symbol'] 
        widgets = {
            'CName': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter category name'}),
            'Symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'maxlength': '1', 'placeholder': 'Enter 1-char symbol'}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize the checkbox based on the model instance's HasSubCat value
        if self.instance and self.instance.pk:
            self.fields['has_sub_cat_checkbox'].initial = (self.instance.HasSubCat == '1')
        
        # Set required attributes for fields that had RequiredFieldValidator in ASP.NET
        self.fields['CName'].required = True
        self.fields['Symbol'].required = True

    def clean_Symbol(self):
        """Ensure Symbol is uppercase, consistent with ASP.NET storage."""
        symbol = self.cleaned_data['Symbol']
        if symbol:
            return symbol.upper()
        return symbol

    def save(self, commit=True):
        """
        Overrides save to handle the mapping of the form's boolean checkbox
        back to the model's '1'/'0' string representation for HasSubCat.
        """
        instance = super().save(commit=False)
        # Map the checkbox value to '1' or '0' for the model's HasSubCat field
        instance.HasSubCat = '1' if self.cleaned_data.get('has_sub_cat_checkbox') else '0'
        
        # The audit/context fields (CompId, FinYearId, SessionId) are set in the view
        # because they depend on the request/session, which forms don't directly access.
        
        if commit:
            instance.save()
        return instance

```

#### 4.3 Views (`sales/views.py`)

Django Class-Based Views (CBVs) will manage CRUD operations. Views are kept thin, delegating business logic to the model and form. HTMX requests are specifically handled for partial updates and modal interactions.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import render, get_object_or_404
import datetime

from .models import Category
from .forms import CategoryForm

# Helper function to simulate getting session-dependent context data
# In a real application, this would integrate with Django's authentication
# system and potentially a Company/FinancialYear model.
def get_session_context(request):
    """
    Retrieves company, financial year, and session user from the request session.
    Provides default values for testing/initial setup.
    """
    comp_id = request.session.get('compid', 'DEFAULT_COMP_ID') 
    fin_year_id = request.session.get('finyear', 'DEFAULT_FIN_YEAR_ID')
    session_id = request.session.get('username', 'DEFAULT_SESSION_USER') 
    return comp_id, fin_year_id, session_id

class CategoryListView(ListView):
    """Displays a list of categories."""
    model = Category
    template_name = 'sales/category/list.html'
    context_object_name = 'categories'

    def get_queryset(self):
        """Filters categories based on session context (CompId, FinYearId)."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        return Category.get_filtered_categories(comp_id, fin_year_id)

class CategoryTablePartialView(ListView):
    """
    Renders only the category table, intended for HTMX requests to refresh
    the list dynamically without a full page reload.
    """
    model = Category
    template_name = 'sales/category/_category_table.html'
    context_object_name = 'categories'

    def get_queryset(self):
        """Filters categories based on session context (CompId, FinYearId)."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        return Category.get_filtered_categories(comp_id, fin_year_id)

    def dispatch(self, request, *args, **kwargs):
        """Ensures this view is only accessed via HTMX."""
        if not request.headers.get('HX-Request'):
            raise Http404("This view is intended for HTMX requests only.")
        return super().dispatch(request, *args, **kwargs)

class CategoryCreateView(CreateView):
    """Handles creation of new categories via a modal form."""
    model = Category
    form_class = CategoryForm
    template_name = 'sales/category/_category_form.html' # Rendered as a partial in a modal
    success_url = reverse_lazy('category_list') # Fallback if not HTMX

    def form_valid(self, form):
        """
        Sets audit/context fields before saving the new category and
        sends HTMX triggers for dynamic UI updates.
        """
        comp_id, fin_year_id, session_id = get_session_context(self.request)
        
        # Assign audit/context fields from the session
        form.instance.CompId = comp_id
        form.instance.FinYearId = fin_year_id
        form.instance.SessionId = session_id
        
        response = super().form_valid(form)
        messages.success(self.request, 'Category added successfully.')
        
        # If an HTMX request, send a 204 No Content response to trigger client-side refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshCategoryList'} # Custom event to refresh the table
            )
        return response

    def form_invalid(self, form):
        """Re-renders the form with validation errors for HTMX requests."""
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # Return the rendered form partial with errors
        return response # Full page render with errors for non-HTMX

class CategoryUpdateView(UpdateView):
    """Handles updating existing categories via a modal form."""
    model = Category
    form_class = CategoryForm
    template_name = 'sales/category/_category_form.html' # Rendered as a partial in a modal
    pk_url_kwarg = 'pk' # Specifies the URL keyword argument for the primary key
    success_url = reverse_lazy('category_list') # Fallback if not HTMX

    def form_valid(self, form):
        """
        Saves the updated category and sends HTMX triggers for dynamic UI updates.
        Audit fields are updated if necessary (e.g., last editor).
        """
        # Optionally, update audit fields on modification if required by business rules
        # comp_id, fin_year_id, session_id = get_session_context(self.request)
        # form.instance.SessionId = session_id # Example: update last modified user
        
        response = super().form_valid(form)
        messages.success(self.request, 'Category updated successfully.')

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshCategoryList'}
            )
        return response

    def form_invalid(self, form):
        """Re-renders the form with validation errors for HTMX requests."""
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class CategoryDeleteView(DeleteView):
    """Handles deletion of categories via a confirmation modal."""
    model = Category
    template_name = 'sales/category/_category_confirm_delete.html' # Partial template for confirmation
    pk_url_kwarg = 'pk'
    success_url = reverse_lazy('category_list')

    def delete(self, request, *args, **kwargs):
        """
        Deletes the category and sends HTMX triggers for dynamic UI updates.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshCategoryList'}
            )
        return response

    def dispatch(self, request, *args, **kwargs):
        """Ensures this view is only accessed via HTMX."""
        if not request.headers.get('HX-Request'):
            raise Http404("This view is intended for HTMX requests only.")
        return super().dispatch(request, *args, **kwargs)

```

#### 4.4 Templates (`sales/templates/sales/category/`)

Templates are designed for modularity, with a main list page and partials for table content, forms, and delete confirmations. HTMX handles loading these partials into a modal.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Categories</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'category_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Alpine.js/hyperscript for modal toggle #}
            Add New Category
        </button>
    </div>
    
    <div id="categoryTable-container"
         hx-trigger="load, refreshCategoryList from:body" {# Initial load and refresh via custom event #}
         hx-get="{% url 'category_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX. A loading spinner is shown initially. -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Categories...</p>
        </div>
    </div>
    
    <!-- Modal structure for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto"
             _="on htmx:afterOnLoad add .is-active to #modal"></div> {# Show modal after content loads #}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization could go here for more complex UI states
        // For simple modal show/hide, _hyperscript directly with HTMX is often sufficient.
    });

    // Listen for the custom 'refreshCategoryList' event triggered by CRUD operations
    document.body.addEventListener('refreshCategoryList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active'); // Close modal after successful operation
        }
        // If you need to display Django messages (e.g., success messages)
        // you would typically have a messages partial in base.html and
        // trigger its refresh here, or use a client-side library to show them.
        // For simplicity, this example assumes messages are handled on page reload
        // or a global messages component is listening.
    });
</script>
{% endblock %}

```

**`_category_table.html`**

```html
<table id="categoryTable" class="min-w-full bg-white table-auto border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Has SubCategory</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for category in categories %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ category.CName }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ category.Symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ category.has_subcategory_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                    hx-get="{% url 'category_edit' category.CId %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'category_delete' category.CId %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No categories found. Click "Add New Category" to get started!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization for client-side functionality
// Ensure jQuery and DataTables CDN links are included in core/base.html
$(document).ready(function() {
    $('#categoryTable').DataTable({
        "pageLength": 17, // Replicate ASP.NET's PageSize
        "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
        "ordering": true,  // Allow sorting
        "searching": true, // Allow search
        "paging": true,    // Enable pagination
        "info": true       // Show info about entries
    });
});
</script>
```

**`_category_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Category</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="json-enc"> {# Use json-enc for clean JSON payload #}
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.CName.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.CName.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.CName }}
                {% if form.CName.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.CName.errors|join:", " }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.Symbol.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.Symbol.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.Symbol }}
                {% if form.Symbol.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.Symbol.errors|join:", " }}</p>
                {% endif %}
            </div>

            <div class="mb-4 flex items-center">
                {{ form.has_sub_cat_checkbox }}
                <label for="{{ form.has_sub_cat_checkbox.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700 cursor-pointer">
                    {{ form.has_sub_cat_checkbox.label }}
                </label>
                {% if form.has_sub_cat_checkbox.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.has_sub_cat_checkbox.errors|join:", " }}</p>
                {% endif %}
            </div>
            
            {# Non-field errors if any #}
            {% if form.non_field_errors %}
                <div class="text-red-500 text-xs mt-1">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Close modal on cancel #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_category_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4 text-gray-700">Are you sure you want to delete the category: <strong class="font-semibold">{{ object.CName }} ({{ object.Symbol }})</strong>?</p>
    
    <form hx-post="{% url 'category_delete' object.CId %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Close modal on cancel #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`sales/urls.py`)

Defines the URL patterns for accessing the Category module views.

```python
from django.urls import path
from .views import CategoryListView, CategoryCreateView, CategoryUpdateView, CategoryDeleteView, CategoryTablePartialView

urlpatterns = [
    # Main list view (full page)
    path('category/', CategoryListView.as_view(), name='category_list'),
    
    # HTMX endpoints for modal forms and table refresh
    path('category/add/', CategoryCreateView.as_view(), name='category_add'),
    path('category/edit/<int:pk>/', CategoryUpdateView.as_view(), name='category_edit'),
    path('category/delete/<int:pk>/', CategoryDeleteView.as_view(), name='category_delete'),
    path('category/table/', CategoryTablePartialView.as_view(), name='category_table'), # For HTMX to fetch table content
]

```

#### 4.6 Tests (`sales/tests.py`)

Comprehensive unit tests for the model and integration tests for all views to ensure functionality and robustness.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.core.exceptions import ValidationError
from .models import Category
from .forms import CategoryForm
import datetime

class CategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a base category for common test scenarios
        cls.category_data = {
            'CName': 'Test Category',
            'Symbol': 'T',
            'HasSubCat': '1', 
            'CompId': 'COMP001',
            'FinYearId': 'FY2023',
            'SessionId': 'testuser',
        }
        cls.category = Category.objects.create(**cls.category_data)
  
    def test_category_creation(self):
        """Ensures a Category object can be created and its attributes are correct."""
        obj = Category.objects.get(CId=self.category.CId)
        self.assertEqual(obj.CName, 'Test Category')
        self.assertEqual(obj.Symbol, 'T')
        self.assertEqual(obj.HasSubCat, '1')
        self.assertEqual(obj.CompId, 'COMP001')
        self.assertEqual(obj.FinYearId, 'FY2023')
        self.assertEqual(obj.SessionId, 'testuser')
        
    def test_str_method(self):
        """Tests the __str__ method for correct string representation."""
        obj = Category.objects.get(CId=self.category.CId)
        self.assertEqual(str(obj), 'Test Category (T)')

    def test_has_subcategory_display_property(self):
        """Tests the `has_subcategory_display` property for correct string conversion."""
        cat_yes = Category.objects.create(
            CName='Cat With Sub', Symbol='Y', HasSubCat='1',
            CompId='COMP001', FinYearId='FY2024', SessionId='user1'
        )
        cat_no = Category.objects.create(
            CName='Cat No Sub', Symbol='N', HasSubCat='0',
            CompId='COMP001', FinYearId='FY2024', SessionId='user2'
        )
        self.assertEqual(cat_yes.has_subcategory_display, 'Yes')
        self.assertEqual(cat_no.has_subcategory_display, 'No')

    def test_symbol_uppercase_on_save(self):
        """Ensures the Symbol is converted to uppercase when saving."""
        new_cat = Category.objects.create(
            CName='Lowercase Symbol', Symbol='l', HasSubCat='0',
            CompId='COMP002', FinYearId='FY2023', SessionId='testuser'
        )
        self.assertEqual(new_cat.Symbol, 'L')

    def test_has_sub_cat_boolean_to_string_conversion(self):
        """Tests that boolean values for HasSubCat are correctly converted to '1'/'0'."""
        cat_bool_true = Category.objects.create(
            CName='Bool True', Symbol='B', HasSubCat=True,
            CompId='COMP003', FinYearId='FY2023', SessionId='user'
        )
        cat_bool_false = Category.objects.create(
            CName='Bool False', Symbol='F', HasSubCat=False,
            CompId='COMP003', FinYearId='FY2023', SessionId='user'
        )
        self.assertEqual(cat_bool_true.HasSubCat, '1')
        self.assertEqual(cat_bool_false.HasSubCat, '0')

    def test_get_filtered_categories(self):
        """Tests the `get_filtered_categories` class method for correct filtering and ordering."""
        # Add more categories for filtering tests
        Category.objects.create(CName='Category B', Symbol='B', HasSubCat='0', CompId='COMP001', FinYearId='FY2022', SessionId='user')
        Category.objects.create(CName='Category C', Symbol='C', HasSubCat='1', CompId='COMP002', FinYearId='FY2023', SessionId='user')
        
        # Test filtering for COMP001 and FY2023 (should include FY2022 because of <=)
        filtered_cats = Category.get_filtered_categories('COMP001', 'FY2023')
        self.assertEqual(filtered_cats.count(), 2) # Test Category (FY2023) and Category B (FY2022)
        self.assertEqual(filtered_cats.first().CName, 'Test Category') # Check order (desc by CId, so higher CId first)

    def test_symbol_uniqueness(self):
        """Tests that a symbol is unique within a given CompId and FinYearId."""
        # Attempt to create a category with the same symbol, compid, finyearid
        with self.assertRaises(Exception) as cm: # Expecting IntegrityError or ValidationError
            Category.objects.create(
                CName='Duplicate Cat', Symbol='T', HasSubCat='0',
                CompId='COMP001', FinYearId='FY2023', SessionId='anotheruser'
            )
        # Check for specific error message if custom validation is used or database error is caught
        self.assertIn("duplicate key", str(cm.exception).lower()) # For IntegrityError
        # If using model.clean() for validation, would check ValidationError:
        # with self.assertRaisesMessage(ValidationError, 'Category symbol is already used...'):
        #     new_cat = Category(CName='Duplicate Cat', Symbol='T', HasSubCat='0', CompId='COMP001', FinYearId='FY2023', SessionId='anotheruser')
        #     new_cat.full_clean() # Calls clean() and validates all fields

class CategoryFormTest(TestCase):
    def test_category_form_valid(self):
        """Tests that the form is valid with correct data."""
        form_data = {
            'CName': 'New Test Category',
            'Symbol': 'N',
            'has_sub_cat_checkbox': True,
        }
        form = CategoryForm(data=form_data)
        self.assertTrue(form.is_valid(), msg=form.errors.as_text())
        
        # Verify has_sub_cat_checkbox maps correctly to instance.HasSubCat
        instance = form.save(commit=False)
        self.assertEqual(instance.HasSubCat, '1')
        
    def test_category_form_invalid_empty_fields(self):
        """Tests form validation for required fields."""
        form_data = {
            'CName': '', # Missing
            'Symbol': '', # Missing
            'has_sub_cat_checkbox': False,
        }
        form = CategoryForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('CName', form.errors)
        self.assertIn('Symbol', form.errors)
        self.assertEqual(form.errors['CName'], ['This field is required.'])
        self.assertEqual(form.errors['Symbol'], ['This field is required.'])

    def test_category_form_symbol_max_length(self):
        """Tests form validation for Symbol's max length."""
        form_data = {
            'CName': 'Valid Name',
            'Symbol': 'TOO', # Exceeds max length of 1
            'has_sub_cat_checkbox': False,
        }
        form = CategoryForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Symbol', form.errors)
        self.assertEqual(form.errors['Symbol'], ['Ensure this value has at most 1 character (it has 3).'])

    def test_category_form_symbol_uppercase_conversion(self):
        """Tests that the form's clean_Symbol converts input to uppercase."""
        form_data = {
            'CName': 'Valid Name',
            'Symbol': 'a', # Lowercase input
            'has_sub_cat_checkbox': False,
        }
        form = CategoryForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['Symbol'], 'A') # Should be uppercase

class CategoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create an initial category for read/update/delete tests
        cls.category = Category.objects.create(
            CName='Existing Category',
            Symbol='E',
            HasSubCat='1',
            CompId='COMP001',
            FinYearId='FY2023',
            SessionId='testuser'
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables to mimic the ASP.NET environment
        session = self.client.session
        session['compid'] = 'COMP001'
        session['finyear'] = 'FY2023'
        session['username'] = 'testuser'
        session.save()
    
    def test_list_view_get(self):
        """Tests that the list view renders correctly and contains categories."""
        response = self.client.get(reverse('category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/category/list.html')
        self.assertIn('categories', response.context)
        self.assertEqual(response.context['categories'].count(), 1)
        self.assertContains(response, 'Existing Category')

    def test_table_partial_view_get(self):
        """Tests the HTMX partial for the category table."""
        headers = {'HTTP_HX_REQUEST': 'true'} # Mimic an HTMX request
        response = self.client.get(reverse('category_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/category/_category_table.html')
        self.assertIn('categories', response.context)
        self.assertEqual(response.context['categories'].count(), 1)
        self.assertContains(response, 'Existing Category')

    def test_create_view_get_htmx(self):
        """Tests GET request for the create form via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('category_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/category/_category_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Category')

    def test_create_view_post_success_htmx(self):
        """Tests successful creation of a category via HTMX post."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'CName': 'New Category From HTMX',
            'Symbol': 'N',
            'has_sub_cat_checkbox': 'on', # Checkbox sends 'on' when checked
        }
        response = self.client.post(reverse('category_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for successful trigger
        self.assertEqual(response['HX-Trigger'], 'refreshCategoryList')
        self.assertTrue(Category.objects.filter(CName='New Category From HTMX', Symbol='N').exists())
        
        # Verify success message (requires messages middleware to capture)
        messages_sent = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_sent), 1)
        self.assertEqual(str(messages_sent[0]), 'Category added successfully.')

    def test_create_view_post_duplicate_symbol_htmx(self):
        """Tests creation failure due to duplicate symbol via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'CName': 'Another Category',
            'Symbol': 'E', # Duplicate of existing category's symbol
            'has_sub_cat_checkbox': 'off',
        }
        response = self.client.post(reverse('category_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales/category/_category_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Category symbol is already used for this Company and Financial Year.')
        self.assertFalse(Category.objects.filter(CName='Another Category').exists()) # Object not created

    def test_update_view_get_htmx(self):
        """Tests GET request for the update form via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('category_edit', args=[self.category.CId]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/category/_category_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Category')
        self.assertEqual(response.context['form'].instance.CName, self.category.CName)

    def test_update_view_post_success_htmx(self):
        """Tests successful update of a category via HTMX post."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'CName': 'Updated Category Name',
            'Symbol': 'U',
            'has_sub_cat_checkbox': 'off',
        }
        response = self.client.post(reverse('category_edit', args=[self.category.CId]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshCategoryList')
        
        # Refresh object from DB to check changes
        self.category.refresh_from_db()
        self.assertEqual(self.category.CName, 'Updated Category Name')
        self.assertEqual(self.category.Symbol, 'U')
        self.assertEqual(self.category.HasSubCat, '0') # Checkbox was 'off'
        
        messages_sent = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_sent), 1)
        self.assertEqual(str(messages_sent[0]), 'Category updated successfully.')

    def test_delete_view_get_htmx(self):
        """Tests GET request for the delete confirmation via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('category_delete', args=[self.category.CId]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/category/_category_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.category)
        self.assertContains(response, 'Confirm Delete')

    def test_delete_view_post_success_htmx(self):
        """Tests successful deletion of a category via HTMX post."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        category_to_delete = Category.objects.create(
            CName='Temporary Category', Symbol='X', HasSubCat='0',
            CompId='COMP001', FinYearId='FY2023', SessionId='user_temp'
        )
        response = self.client.post(reverse('category_delete', args=[category_to_delete.CId]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshCategoryList')
        self.assertFalse(Category.objects.filter(CId=category_to_delete.CId).exists()) # Verify deletion

        messages_sent = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_sent), 1)
        self.assertEqual(str(messages_sent[0]), 'Category deleted successfully.')

```