This comprehensive plan outlines the modernization of your ASP.NET Customer Master application to a modern Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a systematic and efficient transition, minimizing manual effort and potential errors. We will leverage Django's powerful ORM, HTMX for dynamic frontends, Alpine.js for lightweight interactivity, and DataTables for robust data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, specifically the `BindDataCust` method and `sql` WebMethod, we identify the following tables and their approximate columns:

-   **Main Table:** `SD_Cust_master`
    -   `CustomerId` (Primary Key, CharField - as per ASPX `DataKeyNames` and C# usage)
    -   `CustomerName` (CharField)
    -   `RegdAddress` (TextField)
    -   `RegdCountry` (Integer/FK to `tblcountry.CId`)
    -   `RegdState` (Integer/FK to `tblState.SId`)
    -   `RegdCity` (Integer/FK to `tblCity.CityId`)
    -   `FinYearId` (Integer/FK to `tblFinancial_master.FinYearId`)
    -   `SessionId` (Integer/FK to `tblHR_OfficeStaff.EmpId`)
    -   `SysDate` (CharField - due to complex string formatting in C#, ideally `DateField`)
    -   `CompId` (IntegerField - used for filtering by Company)
    -   `SalesId` (BigIntegerField - used for ordering in C#)

-   **Supporting Tables (Lookups):**
    -   `tblFinancial_master`: `FinYearId` (PK), `FinYear`
    -   `tblHR_OfficeStaff`: `EmpId` (PK), `Title`, `EmployeeName`, `CompId`
    -   `tblcountry`: `CId` (PK), `CountryName`
    -   `tblState`: `SId` (PK), `StateName`
    -   `tblCity`: `CityId` (PK), `CityName`

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
The provided ASP.NET page primarily demonstrates **Read** operations:
-   **Read (List View):** Displays a list of customer records in a `GridView`.
-   **Search:** Allows searching customers by `CustomerName` (potentially with `CustomerId` extracted from a formatted string) using a `TextBox` and `Button`.
-   **Pagination & Sorting:** The `GridView` supports `AllowPaging` and `AllowSorting`. DataTables will handle this in Django.
-   **Autocomplete:** An `AutoCompleteExtender` provides real-time suggestions for the search box by querying `SD_Cust_master`.
-   **Data Aggregation:** The `BindDataCust` method performs complex lookups to stitch together financial year, employee name, and a full address string from multiple related tables. This complex data preparation for display will be handled by **fat models** and Django ORM's `select_related`/`prefetch_related`.

While this specific `.aspx` focuses on listing and searching, the page title "Customer Master - Edit" and the `HyperLinkField` pointing to `CustomerMaster_Edit_Details.aspx` imply that **Create**, **Update**, and **Delete** functionalities exist on other pages or will be part of the full customer master module. We will include Django views and templates for these operations, designed to work as HTMX-loaded modals, ensuring a complete CRUD pattern.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
-   **Search Input:** `asp:TextBox` (`TxtSearchValue`) with `cc1:AutoCompleteExtender` for dynamic suggestions.
-   **Search Action:** `asp:Button` (`Search`) to trigger the search.
-   **Data Display:** `asp:GridView` (`SearchGridView1`) for tabular data presentation, including:
    -   Serial Number (SN)
    -   Financial Year (`FinYear`)
    -   Customer Name (`CustomerName`) (as a hyperlink to a detail/edit page)
    -   Customer Code (`CustomerId`)
    -   Address (`Address` - a composite string)
    -   Generation Date (`SysDate`)
    -   Generated By (`EmployeeName`)
-   **Styling:** Basic CSS links and inline styles. This will be replaced by Tailwind CSS.
-   **JavaScript:** `loadingNotifier.js` and implicit AJAX from `AjaxControlToolkit`. This will be replaced by HTMX and Alpine.js.

### Step 4: Generate Django Code

We will create a Django application named `customer_management`.

#### 4.1 Models (`customer_management/models.py`)

This section defines Django models that map directly to your existing database tables. `managed = False` is crucial as it tells Django not to manage table creation, alteration, or deletion, preserving your existing schema. We will use `db_table` to specify the exact table names.

```python
import re
from datetime import datetime
from django.db import models

# Supporting Tables
class FinancialYear(models.Model):
    FinYearId = models.IntegerField(db_column='FinYearId', primary_key=True)
    FinYear = models.CharField(db_column='FinYear', max_length=50) 
    
    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.FinYear

class Employee(models.Model):
    EmpId = models.IntegerField(db_column='EmpId', primary_key=True)
    Title = models.CharField(db_column='Title', max_length=10)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255)
    CompId = models.IntegerField(db_column='CompId')
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.Title}.{self.EmployeeName}"

class Country(models.Model):
    CId = models.IntegerField(db_column='CId', primary_key=True)
    CountryName = models.CharField(db_column='CountryName', max_length=100)
    
    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.CountryName

class State(models.Model):
    SId = models.IntegerField(db_column='SId', primary_key=True)
    StateName = models.CharField(db_column='StateName', max_length=100)
    
    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.StateName

class City(models.Model):
    CityId = models.IntegerField(db_column='CityId', primary_key=True)
    CityName = models.CharField(db_column='CityName', max_length=100)
    
    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.CityName

# Main Customer Model with Business Logic (Fat Model)
class Customer(models.Model):
    CustomerId = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    CustomerName = models.CharField(db_column='CustomerName', max_length=255)
    RegdAddress = models.TextField(db_column='RegdAddress')
    RegdCountry = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='RegdCountry', to_field='CId')
    RegdState = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='RegdState', to_field='SId')
    RegdCity = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='RegdCity', to_field='CityId')
    FinYearId = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', to_field='FinYearId')
    SessionId = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', to_field='EmpId') 
    SysDate = models.CharField(db_column='SysDate', max_length=50) # Stored as varchar in original DB
    CompId = models.IntegerField(db_column='CompId')
    SalesId = models.BigIntegerField(db_column='SalesId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'
        ordering = ['-SalesId'] # Replicating the C# order by SalesId Desc

    def __str__(self):
        return self.CustomerName
        
    @property
    def formatted_sys_date(self):
        """
        Parses the SysDate string (expected 'MM-DD-YYYY') and returns 'DD/MM/YYYY'.
        This mirrors the custom date conversion logic in the ASP.NET code-behind.
        """
        if not self.SysDate:
            return None
        try:
            date_obj = datetime.strptime(self.SysDate, "%m-%d-%Y")
            return date_obj.strftime("%d/%m/%Y")
        except ValueError:
            return self.SysDate # Return original string if parsing fails or format differs
            
    @property
    def generated_by_employee_name(self):
        """Returns the full employee name including title."""
        # SessionId is the ForeignKey to Employee. We use select_related in the view
        # to ensure this is loaded in one query.
        return str(self.SessionId) if self.SessionId else "N/A"

    @property
    def financial_year_name(self):
        """Returns the Financial Year string."""
        # FinYearId is the ForeignKey to FinancialYear.
        return str(self.FinYearId) if self.FinYearId else "N/A"
        
    @property
    def full_address(self):
        """
        Constructs the full address string mirroring the C# logic:
        "RegdAddress,\nCityName, StateName,\nCountryName."
        """
        address_parts = []
        if self.RegdAddress:
            address_parts.append(self.RegdAddress)
        
        city_state_parts = []
        if self.RegdCity and self.RegdCity.CityName:
            city_state_parts.append(self.RegdCity.CityName)
        if self.RegdState and self.RegdState.StateName:
            city_state_parts.append(self.RegdState.StateName)
        
        if city_state_parts:
            address_parts.append(", ".join(city_state_parts))
            
        if self.RegdCountry and self.RegdCountry.CountryName:
            address_parts.append(self.RegdCountry.CountryName + ".")
        
        return ",\n".join(address_parts)

    @staticmethod
    def get_customer_id_from_formatted_string(formatted_string):
        """
        Extracts CustomerId from a string like "CustomerName [CustomerId]".
        This mirrors the fun.getCode() functionality for the search parameter.
        """
        match = re.search(r'\[(.*?)\]$', formatted_string)
        if match:
            return match.group(1)
        return formatted_string # Return as is if format not found, could be just ID or name
```

#### 4.2 Forms (`customer_management/forms.py`)

Django forms handle data validation and rendering. We'll create a form for the customer master data and a simple form for the search input.

```python
from django import forms
from .models import Customer, Country, State, City, FinancialYear, Employee

class CustomerForm(forms.ModelForm):
    # Dynamically populate choices for FK fields if not handled by default ModelChoiceField
    # Or ensure your database has these reference tables populated correctly.
    # For managed=False, ModelChoiceField typically works fine if FKs are defined.
    # If using specific IDs instead of objects in the form (e.g., if DB stores INT and not a FK object),
    # you might need to use IntegerField and handle conversion manually.
    # For this example, assuming standard FK mapping for form submission.

    class Meta:
        model = Customer
        fields = [
            'CustomerId', 'CustomerName', 'RegdAddress', 
            'RegdCountry', 'RegdState', 'RegdCity', 
            'FinYearId', 'SessionId', 'SysDate', 'CompId'
            # SalesId is likely auto-generated or managed internally, not user editable
        ]
        widgets = {
            'CustomerId': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'readonly': 'readonly'}),
            'CustomerName': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'RegdAddress': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'RegdCountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'RegdState': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'RegdCity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'FinYearId': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SessionId': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SysDate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM-DD-YYYY'}), # Guide user on expected format
            'CompId': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # For new entries, CustomerId might be auto-generated or derived from a sequence
        # If it's a specific format, you might need a custom field or method to generate it.
        if not self.instance.pk:
            self.fields['CustomerId'].widget.attrs['readonly'] = False # Allow input for new customer ID
            # Example: Generate a new ID if needed
            # self.fields['CustomerId'].initial = self.generate_new_customer_id()

    # Add custom validation methods here if needed, e.g., for SysDate format
    def clean_SysDate(self):
        sys_date = self.cleaned_data.get('SysDate')
        if sys_date:
            try:
                # Validate that it's in the expected MM-DD-YYYY format
                datetime.strptime(sys_date, "%m-%d-%Y")
            except ValueError:
                raise forms.ValidationError("Please enter SysDate in MM-DD-YYYY format.")
        return sys_date

class CustomerSearchForm(forms.Form):
    search_value = forms.CharField(
        required=False,
        label="Customer Name / ID",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name or Code',
            'hx-get': '/customer/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'list': 'customer-suggestions' # For basic HTML datalist
        })
    )
    # This form is used for the search bar, not for data submission to create/edit.
    # The 'list' attribute connects it to a <datalist> for suggestions.
```

#### 4.3 Views (`customer_management/views.py`)

Django Class-Based Views (CBVs) provide a structured way to handle common web development patterns. Views are kept thin, delegating complex logic to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q # For complex queries
from .models import Customer, FinancialYear, Employee, Country, State, City
from .forms import CustomerForm, CustomerSearchForm
import json # For JSON responses

class CustomerListView(ListView):
    model = Customer
    template_name = 'customer_management/customer/list.html'
    context_object_name = 'customers'

    def get_queryset(self):
        # Initial queryset for the main list page.
        # Ensure related data is pre-fetched for efficiency in template rendering.
        # This mirrors the JOINs implicitly done by C# `BindDataCust`.
        return Customer.objects.select_related(
            'FinYearId', 'SessionId', 'RegdCountry', 'RegdState', 'RegdCity'
        ).order_by('-SalesId') # Order by SalesId Desc as in ASP.NET

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CustomerSearchForm(self.request.GET or None)
        return context

class CustomerTablePartialView(ListView):
    """
    Renders only the customer table rows, intended for HTMX requests.
    Handles search and potentially pagination/sorting from DataTables (if client-side).
    If DataTables is configured for server-side processing, this view would need
    to parse DataTable's specific GET parameters (draw, start, length, search[value], order[0][column], etc.)
    and filter/paginate/order the queryset accordingly.
    For this plan, we assume client-side DataTables handles pagination/sorting on loaded data.
    Search will be handled by filtering the initial queryset.
    """
    model = Customer
    template_name = 'customer_management/customer/_customer_table.html'
    context_object_name = 'customers'

    def get_queryset(self):
        # Fetch data with related objects for efficient access in templates
        queryset = Customer.objects.select_related(
            'FinYearId', 'SessionId', 'RegdCountry', 'RegdState', 'RegdCity'
        )

        search_value = self.request.GET.get('search_value')
        if search_value:
            # Replicate C# search logic: search by formatted ID or customer name
            customer_id_from_formatted = Customer.get_customer_id_from_formatted_string(search_value)
            
            # Use Q objects for OR condition
            queryset = queryset.filter(
                Q(CustomerName__icontains=search_value) | 
                Q(CustomerId__iexact=customer_id_from_formatted) # Exact match for extracted ID
            )
        
        # Apply C# filtering by session parameters (FinYearId, CompId)
        # These would typically come from Django session/user context
        # For demonstration, using dummy values or assuming they are passed as GET params
        # In a real app, this would be from request.session or request.user.profile
        
        # Example for session values (replace with actual session handling)
        # Assuming current_fin_year_id = self.request.session.get('finyear')
        # Assuming current_comp_id = self.request.session.get('compid')
        current_fin_year_id = 2023 # Placeholder for Session["finyear"]
        current_comp_id = 1 # Placeholder for Session["compid"]

        if current_fin_year_id:
            queryset = queryset.filter(FinYearId__FinYearId__lte=current_fin_year_id)
        if current_comp_id:
            queryset = queryset.filter(CompId=current_comp_id)

        # Apply default ordering from model Meta
        return queryset.order_by('-SalesId')

class CustomerCreateView(CreateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'customer_management/customer/_customer_form.html'
    success_url = reverse_lazy('customer_list') # Not directly used for HTMX success

    def form_valid(self, form):
        # Pre-populate session-dependent fields before saving
        # For demonstration, using dummy values for CompId and SessionId
        # In a real application, these would come from the current user's session or profile
        form.instance.CompId = self.request.session.get('compid', 1) # Get from session, default to 1
        # Assuming 'username' session maps to an Employee ID
        employee_id = self.request.session.get('employee_id', None) 
        if employee_id:
            try:
                form.instance.SessionId = Employee.objects.get(EmpId=employee_id)
            except Employee.DoesNotExist:
                # Handle error if employee not found
                pass
        
        response = super().form_valid(form)
        messages.success(self.request, 'Customer added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return HTTP 204 No Content for HTMX success
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshCustomerList': None,
                        'closeModal': None # Custom event to close modal via Alpine.js
                    })
                }
            )
        return response # Fallback for non-HTMX requests

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure CustomerId is not readonly for create if it's auto-generated
        context['form'].fields['CustomerId'].widget.attrs['readonly'] = False
        return context

class CustomerUpdateView(UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'customer_management/customer/_customer_form.html'
    success_url = reverse_lazy('customer_list') # Not directly used for HTMX success

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshCustomerList': None,
                        'closeModal': None
                    })
                }
            )
        return response

class CustomerDeleteView(DeleteView):
    model = Customer
    template_name = 'customer_management/customer/_customer_confirm_delete.html'
    success_url = reverse_lazy('customer_list') # Not directly used for HTMX success

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshCustomerList': None,
                        'closeModal': None
                    })
                }
            )
        return response

class CustomerAutocompleteView(View):
    """
    Provides autocomplete suggestions for customer names, replicating ASP.NET's WebMethod.
    Returns HTML list items for HTMX to inject into a suggestion box.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        if not query:
            return HttpResponse("")

        # Filter by CompId from session, similar to C# WebMethod
        current_comp_id = request.session.get('compid', 1) # Placeholder
        
        customers = Customer.objects.filter(
            CompId=current_comp_id, 
            CustomerName__icontains=query
        ).values('CustomerName', 'CustomerId')[:10] # Limit to 10 suggestions

        # Format suggestions as HTML list items for HTMX to swap in
        # Each item should have a 'data-value' attribute to set the input field when clicked
        html_suggestions = ""
        for customer in customers:
            # Replicate C# format: "CustomerName [CustomerId]"
            formatted_suggestion = f"{customer['CustomerName']} [{customer['CustomerId']}]"
            html_suggestions += f"<li class='p-2 hover:bg-gray-200 cursor-pointer' hx-on:click='this.closest(\"#autocomplete-results\").innerHTML=\"\"; document.getElementById(\"id_search_value\").value=\"{formatted_suggestion}\";' >{formatted_suggestion}</li>"
        
        # Wrap in a div or ul for HTMX target
        return HttpResponse(f"<ul class='absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto mt-1'>{html_suggestions}</ul>")
```

#### 4.4 Templates (`customer_management/templates/customer_management/customer/`)

These templates define the user interface components. They extend a base template (`core/base.html`) for consistent layout and CDN imports.

**`list.html`** (Main Customer List Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Master - Edit</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'customer_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal">
            Add New Customer
        </button>
    </div>

    <div class="mb-6 bg-white p-4 rounded-lg shadow-md relative">
        <form hx-get="{% url 'customer_table' %}" hx-target="#customerTable-container" hx-swap="innerHTML">
            <div class="flex items-end space-x-4">
                <div class="flex-grow">
                    <label for="id_search_value" class="block text-sm font-medium text-gray-700 mb-1">Customer Name / ID</label>
                    {{ search_form.search_value }}
                    <div id="autocomplete-results" class="z-20"></div> {# Autocomplete suggestions will appear here #}
                </div>
                <button 
                    type="submit" 
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </form>
    </div>
    
    <div id="customerTable-container"
         hx-trigger="load, refreshCustomerList from:body"
         hx-get="{% url 'customer_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading customer data...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 hidden items-center justify-center transition-opacity duration-300 ease-in-out opacity-0 z-50"
         _="on closeModal remove .opacity-100 from me then remove .flex from me"
         x-data="{ showModal: false }" x-init="$watch('showModal', value => { if(value) { $el.classList.add('flex'); $el.classList.add('opacity-100'); } else { $el.classList.remove('opacity-100'); $el.classList.remove('flex'); } })">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-auto"
             _="on click outside of #modalContent if #modal.classList.contains('flex') remove .opacity-100 from #modal then remove .flex from #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script>
    // Alpine.js component initialization
    document.addEventListener('alpine:init', () => {
        Alpine.data('customerModal', () => ({
            open: false,
            // You can add more state management here for the modal
        }));
    });
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">
{% endblock %}
```

**`_customer_table.html`** (Partial for the DataTables table, loaded via HTMX)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="customerTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for customer in customers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ customer.financial_year_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800">
                    <button 
                        hx-get="{% url 'customer_edit' customer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        class="underline"
                        _="on click add .flex to #modal then add .opacity-100 to #modal">
                        {{ customer.CustomerName }}
                    </button>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ customer.CustomerId }}</td>
                <td class="py-3 px-4 text-sm text-gray-500 whitespace-pre-line text-left">{{ customer.full_address }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ customer.formatted_sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ customer.generated_by_employee_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'customer_edit' customer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'customer_delete' customer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-lg text-maroon-600 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#customerTable')) {
            $('#customerTable').DataTable().destroy();
        }
        $('#customerTable').DataTable({
            "paging": true,
            "searching": false, // Search handled by HTMX form
            "info": true,
            "ordering": true, // Enable client-side ordering
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 20, // Replicating ASP.NET PageSize
            "columnDefs": [
                { "orderable": false, "targets": [0, 7] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>
```

**`_customer_form.html`** (Partial for Add/Edit Form)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Customer</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-indicator="#form-spinner" 
          _="on hx-after-request remove .flex from #modal then remove .opacity-100 from #modal">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm mt-4">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save
            </button>
        </div>
    </form>
</div>
```

**`_customer_confirm_delete.html`** (Partial for Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete customer "{{ customer.CustomerName }}" (ID: {{ customer.pk }})?</p>
    
    <form hx-post="{% url 'customer_delete' customer.pk %}" 
          hx-swap="none" 
          hx-indicator="#delete-spinner"
          _="on hx-after-request remove .flex from #modal then remove .opacity-100 from #modal">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                <span id="delete-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`customer_management/urls.py`)

This file defines the URL routing for your customer management application.

```python
from django.urls import path
from .views import (
    CustomerListView, CustomerTablePartialView, 
    CustomerCreateView, CustomerUpdateView, CustomerDeleteView,
    CustomerAutocompleteView
)

urlpatterns = [
    path('customer/', CustomerListView.as_view(), name='customer_list'),
    path('customer/table/', CustomerTablePartialView.as_view(), name='customer_table'),
    path('customer/add/', CustomerCreateView.as_view(), name='customer_add'),
    path('customer/edit/<str:pk>/', CustomerUpdateView.as_view(), name='customer_edit'), # CustomerId is CharField
    path('customer/delete/<str:pk>/', CustomerDeleteView.as_view(), name='customer_delete'), # CustomerId is CharField
    path('customer/autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
]
```
*Note: In your project's main `urls.py`, you would include these URLs using `path('sales/', include('customer_management.urls'))` or similar.*

#### 4.6 Tests (`customer_management/tests.py`)

Thorough testing ensures the reliability and correctness of your application. We include unit tests for models and integration tests for views.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.http import HttpResponse, JsonResponse
import json
from .models import Customer, FinancialYear, Employee, Country, State, City
from .forms import CustomerForm

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.fin_year = FinancialYear.objects.create(FinYearId=2023, FinYear='2023-2024')
        cls.employee = Employee.objects.create(EmpId=1, Title='Mr', EmployeeName='John Doe', CompId=1)
        cls.country = Country.objects.create(CId=1, CountryName='India')
        cls.state = State.objects.create(SId=1, StateName='Maharashtra')
        cls.city = City.objects.create(CityId=1, CityName='Mumbai')

        # Create a test customer instance
        cls.customer = Customer.objects.create(
            CustomerId='CUST001',
            CustomerName='Test Customer One',
            RegdAddress='123 Main St',
            RegdCountry=cls.country,
            RegdState=cls.state,
            RegdCity=cls.city,
            FinYearId=cls.fin_year,
            SessionId=cls.employee,
            SysDate='01-15-2023', # MM-DD-YYYY format
            CompId=1,
            SalesId=100
        )
  
    def test_customer_creation(self):
        customer = Customer.objects.get(CustomerId='CUST001')
        self.assertEqual(customer.CustomerName, 'Test Customer One')
        self.assertEqual(customer.RegdAddress, '123 Main St')
        self.assertEqual(customer.RegdCountry.CountryName, 'India')
        self.assertEqual(customer.SysDate, '01-15-2023')
        
    def test_customer_str_method(self):
        customer = Customer.objects.get(CustomerId='CUST001')
        self.assertEqual(str(customer), 'Test Customer One')

    def test_formatted_sys_date_property(self):
        customer = Customer.objects.get(CustomerId='CUST001')
        self.assertEqual(customer.formatted_sys_date, '15/01/2023')
        
        # Test with invalid date format
        customer.SysDate = '2023-01-15' # YYYY-MM-DD
        customer.save()
        self.assertEqual(customer.formatted_sys_date, '2023-01-15') # Should return original if parsing fails

    def test_generated_by_employee_name_property(self):
        customer = Customer.objects.get(CustomerId='CUST001')
        self.assertEqual(customer.generated_by_employee_name, 'Mr.John Doe')

    def test_financial_year_name_property(self):
        customer = Customer.objects.get(CustomerId='CUST001')
        self.assertEqual(customer.financial_year_name, '2023-2024')

    def test_full_address_property(self):
        customer = Customer.objects.get(CustomerId='CUST001')
        expected_address = "123 Main St,\nMumbai, Maharashtra,\nIndia."
        self.assertEqual(customer.full_address, expected_address)

    def test_get_customer_id_from_formatted_string_method(self):
        formatted_str = "CustomerName [CUST002]"
        customer_id = Customer.get_customer_id_from_formatted_string(formatted_str)
        self.assertEqual(customer_id, "CUST002")

        # Test with just a name
        just_name = "CustomerName"
        customer_id = Customer.get_customer_id_from_formatted_string(just_name)
        self.assertEqual(customer_id, "CustomerName") # Should return as is

class CustomerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for views
        cls.fin_year = FinancialYear.objects.create(FinYearId=2023, FinYear='2023-2024')
        cls.employee = Employee.objects.create(EmpId=1, Title='Mr', EmployeeName='John Doe', CompId=1)
        cls.country = Country.objects.create(CId=1, CountryName='India')
        cls.state = State.objects.create(SId=1, StateName='Maharashtra')
        cls.city = City.objects.create(CityId=1, CityName='Mumbai')

        cls.customer1 = Customer.objects.create(
            CustomerId='CUST001', CustomerName='Alpha Customer', RegdAddress='123 Main St',
            RegdCountry=cls.country, RegdState=cls.state, RegdCity=cls.city,
            FinYearId=cls.fin_year, SessionId=cls.employee, SysDate='01-15-2023', CompId=1, SalesId=100
        )
        cls.customer2 = Customer.objects.create(
            CustomerId='CUST002', CustomerName='Beta Corp', RegdAddress='456 Elm St',
            RegdCountry=cls.country, RegdState=cls.state, RegdCity=cls.city,
            FinYearId=cls.fin_year, SessionId=cls.employee, SysDate='02-20-2023', CompId=1, SalesId=90
        )
    
    def setUp(self):
        self.client = Client()
        # Set session data if needed by views (e.g., compid, finyear)
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session['employee_id'] = 1 # Assuming SessionId maps to EmpId
        session.save()

    def test_customer_list_view(self):
        response = self.client.get(reverse('customer_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_management/customer/list.html')
        self.assertIn('customers', response.context)
        # Check if the search form is in context
        self.assertIn('search_form', response.context)
        # Verify content from both customers (default order by SalesId Desc)
        self.assertContains(response, 'Alpha Customer')
        self.assertContains(response, 'Beta Corp')

    def test_customer_table_partial_view(self):
        # Test basic load
        response = self.client.get(reverse('customer_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_management/customer/_customer_table.html')
        self.assertContains(response, 'Alpha Customer')
        self.assertContains(response, 'Beta Corp')
        
        # Test search by CustomerName
        response = self.client.get(reverse('customer_table'), {'search_value': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Customer')
        self.assertNotContains(response, 'Beta Corp')

        # Test search by CustomerId (formatted)
        response = self.client.get(reverse('customer_table'), {'search_value': 'Some Name [CUST002]'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Beta Corp')
        self.assertNotContains(response, 'Alpha Customer')

    def test_customer_create_view_get(self):
        response = self.client.get(reverse('customer_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_management/customer/_customer_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Customer') # Check for specific text

    def test_customer_create_view_post_htmx(self):
        data = {
            'CustomerId': 'CUST003',
            'CustomerName': 'Gamma Inc',
            'RegdAddress': '789 Pine St',
            'RegdCountry': self.country.CId,
            'RegdState': self.state.SId,
            'RegdCity': self.city.CityId,
            'FinYearId': self.fin_year.FinYearId,
            'SessionId': self.employee.EmpId,
            'SysDate': '03-10-2024',
            'CompId': 1
        }
        response = self.client.post(reverse('customer_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response (No Content)
        self.assertTrue(Customer.objects.filter(CustomerId='CUST003').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_update_view_get(self):
        response = self.client.get(reverse('customer_edit', args=[self.customer1.CustomerId]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_management/customer/_customer_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.CustomerName, 'Alpha Customer')
        self.assertContains(response, 'Edit Customer')

    def test_customer_update_view_post_htmx(self):
        updated_name = 'Alpha Customer Updated'
        data = {
            'CustomerId': self.customer1.CustomerId,
            'CustomerName': updated_name,
            'RegdAddress': self.customer1.RegdAddress,
            'RegdCountry': self.customer1.RegdCountry.CId,
            'RegdState': self.customer1.RegdState.SId,
            'RegdCity': self.customer1.RegdCity.CityId,
            'FinYearId': self.customer1.FinYearId.FinYearId,
            'SessionId': self.customer1.SessionId.EmpId,
            'SysDate': self.customer1.SysDate,
            'CompId': self.customer1.CompId
        }
        response = self.client.post(reverse('customer_edit', args=[self.customer1.CustomerId]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response (No Content)
        self.customer1.refresh_from_db()
        self.assertEqual(self.customer1.CustomerName, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_delete_view_get(self):
        response = self.client.get(reverse('customer_delete', args=[self.customer1.CustomerId]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_management/customer/_customer_confirm_delete.html')
        self.assertIn('customer', response.context)
        self.assertContains(response, 'Confirm Delete')

    def test_customer_delete_view_post_htmx(self):
        response = self.client.post(reverse('customer_delete', args=[self.customer1.CustomerId]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response (No Content)
        self.assertFalse(Customer.objects.filter(CustomerId='CUST001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete'), {'q': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Customer [CUST001]')
        self.assertNotContains(response, 'Beta Corp')

        response = self.client.get(reverse('customer_autocomplete'), {'q': 'nonexistent'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '') # Should return empty HTML
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **Full Page Load (`customer_list`):** The `CustomerListView` serves the `list.html` which acts as the main container. It loads the `customerTable-container` via `hx-get` on `load` and `refreshCustomerList` events.
-   **Search Functionality:** The search form in `list.html` uses `hx-get` to `{% url 'customer_table' %}`. When the search button is clicked, or the input changes (with delay), HTMX reloads only the `_customer_table.html` partial, applying the new search filters.
-   **DataTables Integration:** DataTables is initialized within `_customer_table.html`'s `<script>` block. This ensures that every time the table content is reloaded by HTMX, DataTables re-initializes on the new DOM element, providing client-side pagination, sorting, and filtering.
-   **Add/Edit/Delete Modals:**
    -   Buttons in `list.html` and `_customer_table.html` use `hx-get` to fetch `customer_add`, `customer_edit`, or `customer_delete` URLs.
    -   `hx-target="#modalContent"` directs the HTML response from these views into the hidden modal's content area.
    -   `_="on click add .flex to #modal then add .opacity-100 to #modal"` (Alpine.js integration) is used to reveal the modal.
    -   Form submissions inside the modal (`_customer_form.html`, `_customer_confirm_delete.html`) use `hx-post` to the same URL.
    -   Upon successful form submission (status 204), the `HX-Trigger` header in the Django view sends a `refreshCustomerList` event to the body (which reloads `customerTable-container`) and a `closeModal` event.
    -   `_="on closeModal remove .opacity-100 from me then remove .flex from me"` on `#modal` handles closing the modal when the `closeModal` event is triggered.
    -   The `Cancel` button in modals also closes the modal directly using Alpine.js (`on click remove .flex from #modal`).
-   **Autocomplete:** The search `input` has `hx-get` to `customer_autocomplete/` and `hx-target="#autocomplete-results"`. As the user types, HTMX fetches an HTML `<ul>` of suggestions which is swapped into the `autocomplete-results` div. A `hx-on:click` attribute on each `<li>` allows the user to select a suggestion and populate the input field.

This systematic conversion plan addresses the core functionalities of your ASP.NET application, translating them into a robust, modern Django application leveraging best practices and efficient frontend technologies like HTMX and Alpine.js for a seamless user experience.

### Final Notes

-   **Placeholders:** Replace all placeholder values like `[TABLE_NAME]`, `[APP_NAME]`, `[FRIENDLY_NAME]`, and `[TEST_VALUE]` with the actual values derived from your ASP.NET application analysis.
-   **Base Template (`core/base.html`):** Ensure your `base.html` includes all necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS for the templates to render correctly.
-   **Session Management:** The ASP.NET code heavily relies on `Session` variables (`username`, `finyear`, `compid`). In Django, this would typically involve:
    -   Django's built-in authentication system (`request.user`).
    -   Custom user profiles or middleware to store `compid` and `finyear` associated with the logged-in user.
    -   Accessing these via `request.user.profile.compid` or similar in your views. The examples above use `request.session.get('compid', 1)` as a placeholder for these values.
-   **Security:** Ensure proper authentication and authorization are implemented in Django, potentially using `django.contrib.auth` and mixins like `LoginRequiredMixin`.
-   **Error Handling:** Implement robust error handling and logging beyond basic `try-catch` blocks, using Django's logging system and custom exception handling.
-   **Data Migration:** A critical step is the migration of existing data from the ASP.NET application's database to be compatible with Django's ORM, especially handling the `SysDate` format conversion if a `DateField` is desired instead of `CharField`.
-   **Further Optimization:** For very large datasets, consider server-side processing for DataTables, which involves the `CustomerTablePartialView` parsing additional parameters for pagination, ordering, and global search directly from DataTables.