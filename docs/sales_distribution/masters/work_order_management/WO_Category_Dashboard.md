## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

The provided ASP.NET code for `WO_Category_Dashboard.aspx` and its code-behind is minimalistic, essentially an empty page placeholder. Therefore, direct extraction of database schema is not possible from the provided snippet.

However, based on the page name `WO_Category_Dashboard` (Work Order Category Dashboard), we can infer the likely existence of a database table related to Work Order Categories. For the purpose of this modernization plan, we will infer a common schema for such a table.

*   **Inferred Table Name:** `tbl_WO_Category` (following common ASP.NET database naming conventions, e.g., `tbl_` prefix)
*   **Inferred Columns:**
    *   `CategoryID` (Primary Key, Integer, Identity)
    *   `CategoryName` (String, e.g., `NVARCHAR(255)`, likely unique and required)
    *   `CategoryDescription` (Text, e.g., `NVARCHAR(MAX)`)
    *   `IsActive` (Boolean, e.g., `BIT`, to indicate if the category is currently active)
    *   `CreatedDate` (DateTime, e.g., `DATETIME`, automatically set on creation)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

Given the empty `Page_Load` method and lack of any ASP.NET controls in the `.aspx` file, there is no explicit backend functionality in the provided code.

Based on the nature of a "Dashboard" for "Work Order Categories," standard CRUD (Create, Read, Update, Delete) operations are expected for managing these categories.

*   **Read:** Displaying a list of all existing Work Order Categories. This would typically involve binding data to a `GridView` or similar control.
*   **Create:** Adding a new Work Order Category. This would involve a form with input fields for `CategoryName`, `CategoryDescription`, `IsActive`, and a submit button.
*   **Update:** Modifying an existing Work Order Category. This would typically be triggered from the list view (e.g., an "Edit" button next to a category record), loading the category's data into a form for editing.
*   **Delete:** Removing an existing Work Order Category. This would also be triggered from the list view (e.g., a "Delete" button), likely requiring a confirmation step.
*   **Validation Logic:** While not present, we infer typical validations like `CategoryName` being required and unique.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

As with backend functionality, no UI components are present in the provided `.aspx` file. Based on common ASP.NET dashboard patterns and the inferred functionality, we can infer the following UI components and their Django/HTMX/Alpine.js equivalents:

*   **Data Listing:** An ASP.NET `GridView` or `ListView` is typically used to display a list of records. In Django, this will be replaced by an HTML `<table>` integrated with **DataTables.js** for client-side sorting, searching, and pagination.
*   **Input Forms:** ASP.NET `TextBox`, `CheckBox`, etc., within `Panel` or `FormView` controls. In Django, this will be rendered by a `ModelForm`, styled with Tailwind CSS, and handled dynamically using **HTMX** for submission within a modal.
*   **Action Buttons:** ASP.NET `Button` or `LinkButton` controls. In Django, these will be standard HTML `<button>` elements with **HTMX** attributes (`hx-get`, `hx-post`, `hx-target`, `hx-trigger`) to load forms into modals or submit data without full page reloads.
*   **Modals:** ASP.NET might use AJAX-enabled popups or custom JavaScript solutions. In Django, these will be achieved using simple HTML `<div>` elements, controlled by **Alpine.js** for showing/hiding, and loaded with form content via **HTMX**.

---

### Step 4: Generate Django Code

We will create a new Django application, for example, `sales_distribution`, to house the `WO_Category` module.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**

The `WoCategory` model will represent the `tbl_WO_Category` table. We use `managed = False` because Django will connect to an existing database table rather than creating it. The `db_table` attribute points to the exact table name in the database.

**File: `sales_distribution/models.py`**

```python
from django.db import models

class WoCategory(models.Model):
    """
    Represents a Work Order Category in the database.
    Maps to the existing 'tbl_WO_Category' table.
    """
    category_id = models.IntegerField(db_column='CategoryID', primary_key=True)
    category_name = models.CharField(db_column='CategoryName', max_length=255, unique=True, verbose_name="Category Name")
    category_description = models.TextField(db_column='CategoryDescription', blank=True, null=True, verbose_name="Description")
    is_active = models.BooleanField(db_column='IsActive', default=True, verbose_name="Is Active")
    created_date = models.DateTimeField(db_column='CreatedDate', auto_now_add=True, verbose_name="Created Date")

    class Meta:
        managed = False  # Tells Django not to manage this table's creation, deletion, etc.
        db_table = 'tbl_WO_Category'
        verbose_name = 'Work Order Category'
        verbose_name_plural = 'Work Order Categories'
        ordering = ['category_name'] # Default ordering for lists

    def __str__(self):
        """String representation of the Work Order Category."""
        return self.category_name

    def save(self, *args, **kwargs):
        """
        Custom save method to potentially handle auto-population of CategoryID
        if it's not an identity column in the database and needs Django to set it.
        For identity columns, it's usually handled by the database.
        """
        # Example: If CategoryID is NOT auto-incrementing in DB and you need to generate it
        # if not self.category_id:
        #     last_id = WoCategory.objects.all().order_by('-category_id').first()
        #     self.category_id = (last_id.category_id + 1) if last_id else 1
        super().save(*args, **kwargs)

    def can_be_deleted(self):
        """
        Business logic: Checks if this category can be deleted.
        Example: A category cannot be deleted if it's linked to active work orders.
        This method would query related tables.
        """
        # Placeholder for actual business logic
        # For demonstration, let's say categories created in the last 24 hours cannot be deleted.
        # This would require linking to a 'WorkOrder' model, for example.
        # if self.workorder_set.filter(status='active').exists(): # Assuming a reverse relation 'workorder_set'
        #    return False
        # return True
        return True # For now, allow deletion always.
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**

We create a `ModelForm` for `WoCategory`. This form will be used for both creating new categories and updating existing ones. Widgets are used to apply Tailwind CSS classes for consistent styling. Custom validation for `category_name` uniqueness is added.

**File: `sales_distribution/forms.py`**

```python
from django import forms
from .models import WoCategory

class WoCategoryForm(forms.ModelForm):
    """
    Form for creating and updating Work Order Categories.
    """
    class Meta:
        model = WoCategory
        fields = ['category_name', 'category_description', 'is_active']
        widgets = {
            'category_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter category name'
            }),
            'category_description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24',
                'placeholder': 'Enter category description',
                'rows': 3
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
            }),
        }

    def clean_category_name(self):
        """
        Ensure the category name is unique, excluding the current instance during updates.
        """
        category_name = self.cleaned_data['category_name']
        query = WoCategory.objects.filter(category_name__iexact=category_name)
        if self.instance.pk:  # If updating an existing instance
            query = query.exclude(pk=self.instance.pk)
        if query.exists():
            raise forms.ValidationError("A category with this name already exists.")
        return category_name

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply additional styling to fields if needed
        self.fields['category_name'].required = True
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

Views are kept very thin, primarily handling rendering and delegation to models for business logic. HTMX specific responses (e.g., `HX-Trigger`) are added for dynamic updates. A separate `TablePartialView` is created to handle the HTMX-driven loading of the DataTables content.

**File: `sales_distribution/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from .models import WoCategory
from .forms import WoCategoryForm
from django.template.loader import render_to_string

class WoCategoryListView(ListView):
    """
    Displays the main dashboard page for Work Order Categories.
    The actual table content is loaded via HTMX from WoCategoryTablePartialView.
    """
    model = WoCategory
    template_name = 'sales_distribution/wocategory/list.html'
    context_object_name = 'wocategories' # This is only used if the main list template itself renders data.
                                         # For HTMX setup, this view mainly provides the structural page.

class WoCategoryTablePartialView(ListView):
    """
    Renders only the Work Order Categories table as a partial HTMX response.
    Used to refresh the table after CRUD operations.
    """
    model = WoCategory
    template_name = 'sales_distribution/wocategory/_wocategory_table.html'
    context_object_name = 'wocategories'
    
    def get_queryset(self):
        """
        Ensures the list is ordered consistently.
        """
        return super().get_queryset().order_by('category_name')

class WoCategoryCreateView(CreateView):
    """
    Handles creation of new Work Order Categories.
    Responds with HTMX triggers for dynamic updates.
    """
    model = WoCategory
    form_class = WoCategoryForm
    template_name = 'sales_distribution/wocategory/_wocategory_form.html' # This is a partial template for modal
    success_url = reverse_lazy('wocategory_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order Category added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # and trigger a custom event to refresh the category list.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWoCategoryList, showToastMessage'
                }
            )
        return response # For non-HTMX requests, redirect

class WoCategoryUpdateView(UpdateView):
    """
    Handles updating existing Work Order Categories.
    Responds with HTMX triggers for dynamic updates.
    """
    model = WoCategory
    form_class = WoCategoryForm
    template_name = 'sales_distribution/wocategory/_wocategory_form.html' # Partial template for modal
    success_url = reverse_lazy('wocategory_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order Category updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWoCategoryList, showToastMessage'
                }
            )
        return response

class WoCategoryDeleteView(DeleteView):
    """
    Handles deletion of Work Order Categories.
    Responds with HTMX triggers for dynamic updates.
    """
    model = WoCategory
    template_name = 'sales_distribution/wocategory/confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('wocategory_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to add HTMX specific headers and messages.
        Includes business logic check from model.
        """
        self.object = self.get_object()
        
        # Call the business logic method from the model
        if not self.object.can_be_deleted():
            messages.error(self.request, "This category cannot be deleted due to associated records.")
            if request.headers.get('HX-Request'):
                # For HTMX, render error message back into the modal or a toast
                return HttpResponse(
                    render_to_string('sales_distribution/wocategory/delete_error.html', {'object': self.object}, request),
                    status=400 # Indicate a bad request/client error
                )
            return HttpResponseRedirect(self.get_success_url()) # Fallback for non-HTMX

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWoCategoryList, showToastMessage'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

Templates follow a DRY principle, extending `core/base.html` and using partials for reusable components like forms and the DataTables table. HTMX attributes are heavily used for dynamic loading and interactions, while Alpine.js is leveraged for modal state management.

**File: `sales_distribution/wocategory/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Work Order Categories{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-900">Work Order Categories</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'wocategory_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Category
        </button>
    </div>
    
    <div id="wocategoryTable-container"
         hx-trigger="load, refreshWoCategoryList from:body"
         hx-get="{% url 'wocategory_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-xl rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading categories...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }"
         x-show="show" x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-90">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto"
             _="on htmx:afterSwap remove .is-active from #modal end">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is automatically initialized if included in base.html
    // DataTables initialization handled within the _wocategory_table.html partial.
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Close modal after successful form submission (status 204 no content)
        if (evt.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
            // Hide any open modal if it's managed by Alpine.js
            let modalElement = document.getElementById('modal');
            if (modalElement && modalElement.__alpine && modalElement.__alpine.data) {
                modalElement.__alpine.data.show = false;
            }
        }
    });

    document.body.addEventListener('showToastMessage', function(evt) {
        // Placeholder for toast message display logic
        // This would typically involve a separate toast component in base.html
        // and a custom event listener that reads messages from a global store or a dedicated HTMX response.
        console.log("Show toast message event triggered!");
        // Example: You might have an Alpine.js component for toasts
        // const toastComponent = document.getElementById('toast-container');
        // if (toastComponent && toastComponent.__alpine) {
        //     toastComponent.__alpine.data.addToast(message);
        // }
    });

</script>
{% endblock %}
```

**File: `sales_distribution/wocategory/_wocategory_table.html`** (Partial template for DataTables content)

```html
<table id="wocategoryTable" class="min-w-full bg-white table-auto border-collapse">
    <thead>
        <tr class="bg-gray-100 border-b border-gray-200">
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider rounded-tl-lg">SN</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Category Name</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Active</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created Date</th>
            <th class="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider rounded-tr-lg">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in wocategories %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-4 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ obj.category_name }}</td>
            <td class="py-3 px-4 text-sm text-gray-800 truncate max-w-xs">{{ obj.category_description|default_if_none:"-" }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">
                <span class="{% if obj.is_active %}text-green-600{% else %}text-red-600{% endif %}">
                    {% if obj.is_active %}<i class="fas fa-check-circle"></i> Yes{% else %}<i class="fas fa-times-circle"></i> No{% endif %}
                </span>
            </td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ obj.created_date|date:"M d, Y H:i" }}</td>
            <td class="py-3 px-4 text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded-lg shadow-sm mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'wocategory_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'wocategory_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-8 text-center text-gray-500 text-lg">No work order categories found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is loaded and initialized only once per table load
    // Check if DataTables has already been initialized on this table
    if ($.fn.DataTable.isDataTable('#wocategoryTable')) {
        $('#wocategoryTable').DataTable().destroy();
    }
    
    $('#wocategoryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
        "pagingType": "full_numbers",
        "responsive": true,
        "language": {
            "search": "Filter records:",
            "lengthMenu": "Show _MENU_ entries"
        }
    });
</script>
```

**File: `sales_distribution/wocategory/_wocategory_form.html`** (Partial template for Add/Edit forms)

```html
<div class="bg-white p-6 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-5">
        <h3 class="text-2xl font-semibold text-gray-900">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order Category</h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                _="on click remove .is-active from #modal">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="relative">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-1 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save Changes
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**File: `sales_distribution/wocategory/confirm_delete.html`** (Partial template for delete confirmation)

```html
<div class="bg-white p-6 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-5">
        <h3 class="text-2xl font-semibold text-gray-900">Confirm Deletion</h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                _="on click remove .is-active from #modal">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Work Order Category: <strong class="text-red-600">"{{ object.category_name }}"</strong>?</p>
    
    <div class="mt-6 flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'wocategory_delete' object.pk %}" 
            hx-swap="none"
            hx-confirm="This action cannot be undone. Are you absolutely sure?"
            class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
            <i class="fas fa-trash-alt mr-2"></i> Delete
        </button>
    </div>
</div>
```

**File: `sales_distribution/wocategory/delete_error.html`** (Partial template for delete error response)

```html
<div class="bg-white p-6 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-5">
        <h3 class="text-2xl font-semibold text-red-700">Deletion Failed</h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                _="on click remove .is-active from #modal">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>
    <p class="text-gray-700 mb-6">The Work Order Category "<strong class="text-red-600">{{ object.category_name }}</strong>" cannot be deleted.</p>
    <p class="text-gray-700 mb-6">{% for message in messages %}{{ message }}{% endfor %}</p>
    
    <div class="mt-6 flex justify-end">
        <button 
            type="button" 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            OK
        </button>
    </div>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**

URL patterns for the `sales_distribution` app, including the main list page, and separate endpoints for HTMX partials like the table content, and CRUD forms.

**File: `sales_distribution/urls.py`**

```python
from django.urls import path
from .views import (
    WoCategoryListView,
    WoCategoryTablePartialView,
    WoCategoryCreateView,
    WoCategoryUpdateView,
    WoCategoryDeleteView,
)

urlpatterns = [
    # Main dashboard view, loads the initial page structure
    path('wocategory/', WoCategoryListView.as_view(), name='wocategory_list'),
    
    # HTMX endpoint for the DataTables table content
    path('wocategory/table/', WoCategoryTablePartialView.as_view(), name='wocategory_table'),

    # HTMX endpoints for CRUD operations (forms loaded into modal)
    path('wocategory/add/', WoCategoryCreateView.as_view(), name='wocategory_add'),
    path('wocategory/edit/<int:pk>/', WoCategoryUpdateView.as_view(), name='wocategory_edit'),
    path('wocategory/delete/<int:pk>/', WoCategoryDeleteView.as_view(), name='wocategory_delete'),
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**

Comprehensive tests are included for the `WoCategory` model and all associated views (list, create, update, delete), including checks for HTMX-specific responses and error handling.

**File: `sales_distribution/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import WoCategory
from .forms import WoCategoryForm
from datetime import datetime, timedelta
import json

class WoCategoryModelTest(TestCase):
    """
    Unit tests for the WoCategory model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single test category for all tests
        cls.category1 = WoCategory.objects.create(
            category_id=1,  # Assuming DB manages this, but for tests, we set it.
            category_name='Test Category One',
            category_description='Description for test category one.',
            is_active=True,
            created_date=datetime.now() - timedelta(days=5)
        )
        cls.category2 = WoCategory.objects.create(
            category_id=2,
            category_name='Another Category',
            category_description='Description for another category.',
            is_active=False,
            created_date=datetime.now() - timedelta(days=1)
        )
  
    def test_wocategory_creation(self):
        """Test that a WoCategory object can be created successfully."""
        self.assertEqual(self.category1.category_name, 'Test Category One')
        self.assertEqual(self.category1.is_active, True)
        self.assertTrue(WoCategory.objects.filter(category_id=1).exists())
        self.assertEqual(WoCategory.objects.count(), 2)
        
    def test_category_name_label(self):
        """Test the verbose name for category_name field."""
        field_label = self.category1._meta.get_field('category_name').verbose_name
        self.assertEqual(field_label, 'Category Name')
        
    def test_str_representation(self):
        """Test the __str__ method returns the category name."""
        self.assertEqual(str(self.category1), 'Test Category One')

    def test_can_be_deleted_method(self):
        """Test the custom can_be_deleted business logic method."""
        # As implemented, it always returns True for now.
        self.assertTrue(self.category1.can_be_deleted())
        # In a real scenario, you'd mock related objects or create more complex test data.

    def test_unique_category_name_validation(self):
        """Test that category names must be unique."""
        form_data = {
            'category_name': 'Test Category One', # This name already exists
            'category_description': 'Duplicate description',
            'is_active': True
        }
        form = WoCategoryForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('category_name', form.errors)
        self.assertIn('A category with this name already exists.', form.errors['category_name'])
    
    def test_unique_category_name_validation_on_update(self):
        """Test unique validation doesn't block saving the same instance."""
        form_data = {
            'category_name': 'Test Category One',
            'category_description': 'Updated description',
            'is_active': True
        }
        form = WoCategoryForm(instance=self.category1, data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['category_name'], 'Test Category One')


class WoCategoryViewsTest(TestCase):
    """
    Integration tests for WoCategory views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.category1 = WoCategory.objects.create(
            category_id=101,
            category_name='Existing Category',
            category_description='Description for existing category.',
            is_active=True,
            created_date=datetime.now()
        )
        cls.category2 = WoCategory.objects.create(
            category_id=102,
            category_name='Another Existing',
            category_description='Another description.',
            is_active=False,
            created_date=datetime.now()
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        """Test the main category list page loads correctly."""
        response = self.client.get(reverse('wocategory_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/wocategory/list.html')
        # The list.html template now uses HTMX to load content, so we don't check context here directly.
        
    def test_table_partial_view(self):
        """Test the HTMX partial for the category table."""
        response = self.client.get(reverse('wocategory_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_table.html')
        self.assertIn('wocategories', response.context)
        self.assertEqual(len(response.context['wocategories']), 2)
        self.assertContains(response, 'Existing Category')
        self.assertContains(response, 'Another Existing')

    def test_create_view_get(self):
        """Test GET request for the add category form."""
        response = self.client.get(reverse('wocategory_add'), HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], WoCategoryForm)
        
    def test_create_view_post_htmx_success(self):
        """Test POST request for adding a new category with HTMX."""
        data = {
            'category_name': 'New Category From HTMX',
            'category_description': 'Description for new category.',
            'is_active': True,
        }
        # Simulate HTMX request for the POST
        response = self.client.post(reverse('wocategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWoCategoryList', response.headers['HX-Trigger'])
        self.assertTrue(WoCategory.objects.filter(category_name='New Category From HTMX').exists())
        self.assertEqual(WoCategory.objects.count(), 3) # Two existing + one new
        
    def test_create_view_post_non_htmx_success(self):
        """Test POST request for adding a new category without HTMX (redirect)."""
        data = {
            'category_name': 'New Category Non HTMX',
            'category_description': 'Description for non-htmx category.',
            'is_active': False,
        }
        response = self.client.post(reverse('wocategory_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX
        self.assertRedirects(response, reverse('wocategory_list'))
        self.assertTrue(WoCategory.objects.filter(category_name='New Category Non HTMX').exists())
        self.assertEqual(WoCategory.objects.count(), 3)
        
    def test_create_view_post_invalid(self):
        """Test POST request with invalid data."""
        data = {
            'category_name': 'Existing Category', # Duplicate name
            'category_description': 'Description',
            'is_active': True,
        }
        response = self.client.post(reverse('wocategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_form.html')
        self.assertContains(response, 'A category with this name already exists.')
        self.assertEqual(WoCategory.objects.count(), 2) # No new object created

    def test_update_view_get(self):
        """Test GET request for the edit category form."""
        response = self.client.get(reverse('wocategory_edit', args=[self.category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.category1)
        self.assertContains(response, self.category1.category_name)
        
    def test_update_view_post_htmx_success(self):
        """Test POST request for updating a category with HTMX."""
        data = {
            'category_name': 'Updated Category Name',
            'category_description': 'Updated description.',
            'is_active': False,
        }
        response = self.client.post(reverse('wocategory_edit', args=[self.category1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWoCategoryList', response.headers['HX-Trigger'])
        self.category1.refresh_from_db()
        self.assertEqual(self.category1.category_name, 'Updated Category Name')
        self.assertEqual(self.category1.is_active, False)
        
    def test_update_view_post_non_htmx_success(self):
        """Test POST request for updating a category without HTMX (redirect)."""
        data = {
            'category_name': 'Updated Category No HTMX',
            'category_description': 'Updated description.',
            'is_active': True,
        }
        response = self.client.post(reverse('wocategory_edit', args=[self.category2.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('wocategory_list'))
        self.category2.refresh_from_db()
        self.assertEqual(self.category2.category_name, 'Updated Category No HTMX')

    def test_delete_view_get(self):
        """Test GET request for delete confirmation."""
        response = self.client.get(reverse('wocategory_delete', args=[self.category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/wocategory/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.category1)
        self.assertContains(response, f'delete the Work Order Category: "{self.category1.category_name}"')
        
    def test_delete_view_post_htmx_success(self):
        """Test POST request for deleting a category with HTMX."""
        category_to_delete = WoCategory.objects.create(category_id=103, category_name='Temp Delete', is_active=True)
        self.assertEqual(WoCategory.objects.count(), 3) # Initial count
        
        response = self.client.post(reverse('wocategory_delete', args=[category_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWoCategoryList', response.headers['HX-Trigger'])
        self.assertFalse(WoCategory.objects.filter(pk=category_to_delete.pk).exists())
        self.assertEqual(WoCategory.objects.count(), 2) # Count after deletion

    def test_delete_view_post_non_htmx_success(self):
        """Test POST request for deleting a category without HTMX (redirect)."""
        category_to_delete = WoCategory.objects.create(category_id=104, category_name='Temp Delete 2', is_active=True)
        self.assertEqual(WoCategory.objects.count(), 3)
        
        response = self.client.post(reverse('wocategory_delete', args=[category_to_delete.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('wocategory_list'))
        self.assertFalse(WoCategory.objects.filter(pk=category_to_delete.pk).exists())
        self.assertEqual(WoCategory.objects.count(), 2)
    
    # Test for the can_be_deleted business logic (if implemented to return False sometimes)
    # def test_delete_view_post_blocked_by_business_logic(self):
    #     category_undel = WoCategory.objects.create(category_id=105, category_name='Cannot Delete', is_active=True)
    #     # Temporarily patch the can_be_deleted method for this test
    #     with self.patch.object(WoCategory, 'can_be_deleted', return_value=False):
    #         response = self.client.post(reverse('wocategory_delete', args=[category_undel.pk]), HTTP_HX_REQUEST='true')
    #         self.assertEqual(response.status_code, 400) # Or 200, depending on how you handle the error
    #         self.assertTemplateUsed(response, 'sales_distribution/wocategory/delete_error.html')
    #         self.assertContains(response, "This category cannot be deleted due to associated records.")
    #         self.assertTrue(WoCategory.objects.filter(pk=category_undel.pk).exists()) # Should still exist
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated Django code already incorporates HTMX and Alpine.js for a modern, dynamic user experience:

*   **HTMX for Dynamic Interactions:**
    *   **List View:** The `list.html` loads the actual table content (`_wocategory_table.html`) via an `hx-get` on `load` and `refreshWoCategoryList` custom event. This means the table can be reloaded independently without refreshing the whole page.
    *   **CRUD Modals:** "Add," "Edit," and "Delete" buttons use `hx-get` to fetch the respective form/confirmation templates (`_wocategory_form.html`, `confirm_delete.html`) into a modal container (`#modalContent`).
    *   **Form Submission:** Forms (`_wocategory_form.html`) use `hx-post` to submit data asynchronously. Upon successful submission (HTTP 204 No Content), the backend sends an `HX-Trigger` header (`refreshWoCategoryList, showToastMessage`) to close the modal and refresh the table on the client side.
    *   **Delete Confirmation:** The delete confirmation also uses `hx-post` and triggers the same refresh event. Error handling for deletion (e.g., if `can_be_deleted()` returns False) can render a specific error partial back into the modal.
    *   **Loading Indicators:** `htmx-indicator` class is used on the form and table container to show a spinner during AJAX requests, improving user feedback.

*   **Alpine.js for UI State Management:**
    *   **Modal Management:** The main modal `div` in `list.html` uses `x-data` and `x-show` with `x-transition` for a smooth show/hide animation.
    *   **_hyperscript:** The `_hyperscript` attribute (`_="on click add .is-active to #modal"`) is used as a concise way to toggle the `is-active` class on the modal, effectively showing it when a button is clicked. This is a simple, no-build-step alternative to full Alpine.js for basic DOM manipulation.

*   **DataTables for List Views:**
    *   The `_wocategory_table.html` partial explicitly initializes DataTables on the `wocategoryTable`. This provides client-side features like search, sorting, and pagination without requiring complex server-side logic for these basic table interactions.
    *   The DataTables script ensures it destroys previous instances before re-initializing, which is crucial when content is swapped via HTMX.

*   **Comprehensive HTML Templates:**
    *   All templates extend `core/base.html` for a consistent layout and included CDN links (e.g., for Tailwind CSS, Font Awesome, jQuery, DataTables, HTMX, Alpine.js).
    *   Partial templates (`_wocategory_table.html`, `_wocategory_form.html`, `confirm_delete.html`, `delete_error.html`) are used to ensure modularity and reusability, minimizing redundant HTML.

This combination allows for a highly interactive and modern user experience similar to a Single Page Application (SPA), but with the simplicity and SEO benefits of traditional server-rendered Django.

---

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values from the prompt have been replaced with inferred, concrete examples relevant to a "Work Order Category" module.
*   **DRY Principles:** Adhered to through template inheritance and partials, ensuring common HTML structures are defined once.
*   **Fat Model, Thin View:** Business logic (like `can_be_deleted` and unique name validation) is encapsulated within the `WoCategory` model and `WoCategoryForm` respectively, keeping views focused on orchestrating requests and responses.
*   **Test Coverage:** Extensive unit and integration tests are provided, covering model behavior and all major view operations, including HTMX interactions.
*   **Automated Conversion:** The structured output provides clear, component-specific code that can be generated and inserted into a Django project by an AI-assisted automation tool. The plan explicitly details the inferred database schema, UI components, and backend functionality, guiding the automation process even when original code is minimal. This minimizes manual coding and reduces the risk of human error during migration.