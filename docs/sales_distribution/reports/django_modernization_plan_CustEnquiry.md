## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

The provided ASP.NET code (`CustEnquiry.aspx` and its code-behind) is very minimal, essentially a placeholder page within a master page. It contains no explicit database interactions, UI controls, or business logic.

Based on the page name `CustEnquiry` (Customer Enquiry) and its location `Module_SalesDistribution_Reports`, we can infer that this page is intended for viewing customer-related information, likely in a tabular format, and potentially with search or filter capabilities. While the provided code doesn't explicitly show CRUD operations, a modern web application typically requires them. Therefore, this modernization plan will outline a comprehensive Django solution that *would* be implemented for a "Customer" module, including CRUD functionality, to fully demonstrate the target architecture and best practices.

**Business Value of this Django Modernization:**

Migrating `CustEnquiry` to Django offers significant benefits:

1.  **Enhanced Maintainability & Scalability**: Django's structured MVC-like (MVT) architecture, combined with modern patterns like "Fat Models, Thin Views," makes the codebase easier to understand, maintain, and extend compared to legacy ASP.NET Web Forms.
2.  **Improved User Experience with HTMX/Alpine.js**: By leveraging HTMX for dynamic content loading and Alpine.js for lightweight client-side interactions, the application will provide a faster, more responsive experience without full page reloads, similar to a Single Page Application (SPA) but with simpler development.
3.  **Future-Proof Technology Stack**: Django is a widely-used, actively maintained, and secure framework. HTMX, Alpine.js, and DataTables are modern, efficient tools that avoid the complexity of large JavaScript frameworks, ensuring longevity and ease of development.
4.  **Reduced Development Costs**: The proposed automation-driven approach, combined with Django's "batteries-included" philosophy and the clear separation of concerns, will lead to faster development cycles and reduced debugging time.
5.  **Robustness through Comprehensive Testing**: Built-in unit and integration tests ensure the application's reliability and stability, catching issues early and preventing regressions during future updates.
6.  **Consistent UI/UX with Tailwind CSS**: Using Tailwind CSS ensures a modern, mobile-responsive, and consistent look and feel across the application, improving the user experience and branding.

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not contain explicit database interaction elements (like `SqlDataSource` or direct ADO.NET calls), we must infer the schema based on the page name `CustEnquiry`. We assume a primary table named `TblCustomer` which stores customer details.

-   **Inferred Table Name**: `TblCustomer`
-   **Inferred Columns**:
    -   `CustomerID` (Primary Key, integer)
    -   `CustomerName` (String, required)
    -   `ContactPerson` (String)
    -   `PhoneNumber` (String)
    -   `Email` (String, unique)
    -   `Address` (String)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code-behind file (`CustEnquiry.aspx.cs`) contains only a `Page_Load` method with no custom logic. Therefore, no explicit Create, Read, Update, or Delete (CRUD) operations are visible. However, for a "Customer Enquiry" module in an ERP system, typical functionalities would include:

-   **Read**: Displaying a list of customers, searching, and filtering. (Likely handled by a `GridView` in ASP.NET).
-   **Create**: Adding new customer records. (Likely via a form with `TextBox` and `Button` controls).
-   **Update**: Editing existing customer records. (Likely via a `GridView` edit mode or a dedicated form).
-   **Delete**: Removing customer records. (Likely via a `LinkButton` or `Button` within a `GridView` row).

This Django modernization plan will implement all these inferred CRUD operations to provide a complete and functional module.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Given the minimal ASP.NET code, no specific UI controls are directly visible. However, a typical `CustEnquiry` page would feature:

-   **`GridView`**: For displaying a list of customer records with columns for `Customer Name`, `Contact Person`, `Phone Number`, `Email`, `Address`, and an "Actions" column for Edit/Delete buttons.
-   **`TextBox`**: For search filters or input fields in an Add/Edit form (e.g., `txtCustomerName`, `txtContactPerson`).
-   **`Button` / `LinkButton`**: For submitting forms, triggering searches, or initiating CRUD actions (e.g., `btnAddCustomer`, `btnSave`, `btnEdit`, `btnDelete`).
-   **`Label`**: To display field names or messages.

In Django, these will be replaced by:

-   **DataTables**: For the list view, providing client-side searching, sorting, and pagination.
-   **Django Forms**: For handling user input in Create/Update operations, rendering as HTML forms.
-   **HTMX and Alpine.js**: For dynamic interactions like loading forms in modals, submitting data without full page reloads, and updating the list view.
-   **Tailwind CSS**: For consistent styling of all UI elements.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `Customer` model will map to the `TblCustomer` table. We'll include methods for common business logic, demonstrating the "fat model" approach.

```python
# customer_enquiry/models.py
from django.db import models

class Customer(models.Model):
    customer_id = models.IntegerField(db_column='CustomerID', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name='Customer Name')
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True, verbose_name='Contact Person')
    phone_number = models.CharField(db_column='PhoneNumber', max_length=50, blank=True, null=True, verbose_name='Phone Number')
    email = models.EmailField(db_column='Email', max_length=255, unique=True, blank=True, null=True, verbose_name='Email')
    address = models.TextField(db_column='Address', blank=True, null=True, verbose_name='Address')

    class Meta:
        managed = False  # Set to True if Django should manage the table, False if it already exists
        db_table = 'TblCustomer'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name or f"Customer ID: {self.customer_id}"
        
    def get_full_contact_info(self):
        """
        Example of a business logic method in the model.
        Combines phone and email for a comprehensive contact string.
        """
        info = []
        if self.phone_number:
            info.append(f"Phone: {self.phone_number}")
        if self.email:
            info.append(f"Email: {self.email}")
        return ", ".join(info) if info else "No contact info"

    # Additional business logic methods would go here, e.g.,
    # def calculate_total_orders(self):
    #     # Logic to sum orders for this customer
    #     pass
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` for the `Customer` model will handle data input and validation. Tailwind CSS classes are applied to the widgets for consistent styling.

```python
# customer_enquiry/forms.py
from django import forms
from .models import Customer

class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = ['customer_name', 'contact_person', 'phone_number', 'email', 'address']
        widgets = {
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_person': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'phone_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        
    def clean_email(self):
        """
        Example of custom form validation.
        Ensures email is unique, excluding the current instance during update.
        """
        email = self.cleaned_data.get('email')
        if email and Customer.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("This email address is already registered.")
        return email
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are kept thin, delegating business logic to the models and using HTMX for dynamic updates. A `CustomerTablePartialView` is added to specifically serve the DataTables content via HTMX.

```python
# customer_enquiry/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Customer
from .forms import CustomerForm

class CustomerListView(ListView):
    model = Customer
    template_name = 'customer_enquiry/customer/list.html'
    context_object_name = 'customers' # Not directly used by list.html, but good practice

class CustomerTablePartialView(ListView):
    model = Customer
    template_name = 'customer_enquiry/customer/_customer_table.html'
    context_object_name = 'customers'

class CustomerCreateView(CreateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'customer_enquiry/customer/_customer_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('customer_list')

    def form_valid(self, form):
        # Business logic for creation (if any specific to view)
        response = super().form_valid(form)
        messages.success(self.request, 'Customer added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success for HTMX
                headers={
                    'HX-Trigger': 'refreshCustomerList' # Custom event to refresh the list
                }
            )
        return response

class CustomerUpdateView(UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'customer_enquiry/customer/_customer_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('customer_list')

    def form_valid(self, form):
        # Business logic for update (if any specific to view)
        response = super().form_valid(form)
        messages.success(self.request, 'Customer updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList'
                }
            )
        return response

class CustomerDeleteView(DeleteView):
    model = Customer
    template_name = 'customer_enquiry/customer/_customer_confirm_delete.html' # Use partial for HTMX modal
    success_url = reverse_lazy('customer_list')

    def delete(self, request, *args, **kwargs):
        # Business logic for deletion (if any specific to view)
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates follow DRY principles by extending `core/base.html` and using partials for HTMX-loaded content (forms, confirmation dialogs, and the DataTables table itself).

**`customer_enquiry/customer/list.html`**
This is the main page that loads the DataTables content dynamically.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer List</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'customer_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Customer
        </button>
    </div>
    
    <div id="customerTable-container"
         hx-trigger="load, refreshCustomerList from:body"
         hx-get="{% url 'customer_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Customer Data...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-0 transform transition-all sm:my-8 sm:align-middle">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js global components or setup if needed
        // For this specific modal, Alpine.js handles show/hide via _ syntax
    });
</script>
{% endblock %}
```

**`customer_enquiry/customer/_customer_table.html`**
This partial renders only the DataTables table and its necessary JavaScript initialization.

```html
<table id="customerTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone Number</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in customers %}
        <tr>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.customer_name }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.contact_person }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.phone_number }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.email }}</td>
            <td class="py-2 px-4 text-sm text-gray-900">{{ obj.address }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'customer_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                    hx-get="{% url 'customer_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 text-center text-gray-500">No customers found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization needs to run AFTER the table is loaded into the DOM by HTMX
// This script block is part of the HTMX loaded content, ensuring it runs at the right time.
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent reinitialization errors
    if ($.fn.DataTable.isDataTable('#customerTable')) {
        $('#customerTable').DataTable().destroy();
    }
    $('#customerTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers",
        "responsive": true,
        "autoWidth": false,
        "language": {
            "search": "Search Customer:",
            "lengthMenu": "Show _MENU_ entries"
        }
    });
});
</script>
```

**`customer_enquiry/customer/_customer_form.html`**
This partial is used for both Create and Update operations, loaded into the modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out">
                Save Customer
            </button>
        </div>
    </form>
</div>
```

**`customer_enquiry/customer/_customer_confirm_delete.html`**
This partial provides a delete confirmation dialog, loaded into the modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the customer <strong>{{ object.customer_name }}</strong> (ID: {{ object.pk }})?</p>
    
    <form hx-post="{% url 'customer_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out">
                Delete Customer
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

This provides clear and RESTful URLs for all customer-related operations, including the HTMX specific endpoint for the table partial.

```python
# customer_enquiry/urls.py
from django.urls import path
from .views import CustomerListView, CustomerCreateView, CustomerUpdateView, CustomerDeleteView, CustomerTablePartialView

urlpatterns = [
    path('customers/', CustomerListView.as_view(), name='customer_list'),
    path('customers/add/', CustomerCreateView.as_view(), name='customer_add'),
    path('customers/edit/<int:pk>/', CustomerUpdateView.as_view(), name='customer_edit'),
    path('customers/delete/<int:pk>/', CustomerDeleteView.as_view(), name='customer_delete'),
    path('customers/table/', CustomerTablePartialView.as_view(), name='customer_table'), # For HTMX partial load
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `Customer` model and integration tests for all `Customer` views ensure high code quality and reliability.

```python       
# customer_enquiry/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import Customer
from .forms import CustomerForm

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.customer1 = Customer.objects.create(
            customer_id=1,
            customer_name='Test Customer 1',
            contact_person='John Doe',
            phone_number='************',
            email='<EMAIL>',
            address='123 Main St'
        )
        cls.customer2 = Customer.objects.create(
            customer_id=2,
            customer_name='Test Customer 2',
            contact_person='Jane Smith',
            phone_number='************',
            email='<EMAIL>',
            address='456 Oak Ave'
        )
  
    def test_customer_creation(self):
        self.assertEqual(self.customer1.customer_name, 'Test Customer 1')
        self.assertEqual(self.customer1.email, '<EMAIL>')
        self.assertEqual(self.customer2.customer_id, 2)
        
    def test_customer_name_label(self):
        field_label = self.customer1._meta.get_field('customer_name').verbose_name
        self.assertEqual(field_label, 'Customer Name')
        
    def test_email_label(self):
        field_label = self.customer1._meta.get_field('email').verbose_name
        self.assertEqual(field_label, 'Email')

    def test_str_method(self):
        self.assertEqual(str(self.customer1), 'Test Customer 1')
        
    def test_get_full_contact_info(self):
        self.assertEqual(self.customer1.get_full_contact_info(), 'Phone: ************, Email: <EMAIL>')
        
        customer_no_contact = Customer.objects.create(
            customer_id=3,
            customer_name='No Contact Customer',
            phone_number=None,
            email=None
        )
        self.assertEqual(customer_no_contact.get_full_contact_info(), 'No contact info')
        
    def test_email_unique_validation(self):
        form_data = {
            'customer_name': 'Another Customer',
            'email': '<EMAIL>', # Duplicate email
            'contact_person': 'Someone',
            'phone_number': '************',
            'address': 'Test Address'
        }
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
        self.assertIn("This email address is already registered.", form.errors['email'])


class CustomerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create an initial customer for update/delete tests
        cls.initial_customer = Customer.objects.create(
            customer_id=1, # Ensure ID is distinct from any auto-increment behavior
            customer_name='Initial Customer',
            contact_person='Init Contact',
            phone_number='************',
            email='<EMAIL>',
            address='Initial Address'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('customer_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_enquiry/customer/list.html')
        # Check that the table partial view is loaded via HTMX trigger
        # For a full integration test, you might check for specific HTML content or JS.

    def test_table_partial_view(self):
        # This view is explicitly for HTMX requests
        response = self.client.get(reverse('customer_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_enquiry/customer/_customer_table.html')
        self.assertContains(response, 'Initial Customer')
        self.assertTrue('customers' in response.context) # Check if context contains queryset

    def test_create_view_get(self):
        response = self.client.get(reverse('customer_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_enquiry/customer/_customer_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'customer_name': 'New Customer',
            'contact_person': 'New Contact',
            'phone_number': '************',
            'email': '<EMAIL>',
            'address': 'New Address'
        }
        response = self.client.post(reverse('customer_add'), data)
        # For HTMX requests, the view returns status 204 No Content with HX-Trigger
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshCustomerList')
        self.assertTrue(Customer.objects.filter(customer_name='New Customer').exists())
        
    def test_create_view_post_invalid(self):
        data = {
            'customer_name': '', # Invalid, as it's required
            'email': 'invalid-email', # Invalid email format
        }
        response = self.client.post(reverse('customer_add'), data)
        # For HTMX, if form is invalid, it returns 200 with the form partial again
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_enquiry/customer/_customer_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid email address.')
        self.assertFalse(Customer.objects.filter(customer_name='').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('customer_edit', args=[self.initial_customer.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_enquiry/customer/_customer_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.customer_name, 'Initial Customer')
        
    def test_update_view_post_success(self):
        updated_data = {
            'customer_name': 'Updated Customer',
            'contact_person': 'Updated Contact',
            'phone_number': '************',
            'email': '<EMAIL>', # Same email as existing
            'address': 'Updated Address'
        }
        response = self.client.post(reverse('customer_edit', args=[self.initial_customer.pk]), updated_data)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshCustomerList')
        self.initial_customer.refresh_from_db()
        self.assertEqual(self.initial_customer.customer_name, 'Updated Customer')
        
    def test_update_view_post_invalid(self):
        customer = Customer.objects.create(
            customer_id=99,
            customer_name='Temp Customer',
            email='<EMAIL>'
        )
        data = {
            'customer_name': 'Temp Customer',
            'email': self.initial_customer.email, # Duplicate email, but not self.initial_customer's
            'contact_person': 'Temp Contact',
            'phone_number': '************',
            'address': 'Temp Address'
        }
        response = self.client.post(reverse('customer_edit', args=[customer.pk]), data)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_enquiry/customer/_customer_form.html')
        self.assertContains(response, 'This email address is already registered.')
        
    def test_delete_view_get(self):
        response = self.client.get(reverse('customer_delete', args=[self.initial_customer.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customer_enquiry/customer/_customer_confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete')
        
    def test_delete_view_post_success(self):
        customer_to_delete = Customer.objects.create(
            customer_id=98, # Unique ID
            customer_name='To Be Deleted',
            email='<EMAIL>'
        )
        response = self.client.post(reverse('customer_delete', args=[customer_to_delete.pk]))
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshCustomerList')
        self.assertFalse(Customer.objects.filter(pk=customer_to_delete.pk).exists())

    def test_htmx_headers_on_form_submission(self):
        data = {
            'customer_name': 'HTMX Customer',
            'contact_person': 'HTMX Contact',
            'phone_number': '************',
            'email': '<EMAIL>',
            'address': 'HTMX Address'
        }
        # Simulate HTMX request with HX-Request header
        response = self.client.post(reverse('customer_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response)
        self.assertEqual(response['HX-Trigger'], 'refreshCustomerList')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The Django modernization plan leverages HTMX and Alpine.js to create a highly dynamic and interactive user experience without requiring complex client-side JavaScript frameworks.

-   **HTMX for Dynamic Content**:
    -   **List View Refresh**: The `customerTable-container` `div` in `list.html` uses `hx-trigger="load, refreshCustomerList from:body"` and `hx-get="{% url 'customer_table' %}"` to load the customer table dynamically upon page load and whenever a custom `refreshCustomerList` event is triggered. This event is sent by the `form_valid` (for create/update) and `delete` methods in the Django views using the `HX-Trigger` header.
    -   **Modal Forms**: The "Add New Customer", "Edit", and "Delete" buttons use `hx-get` to fetch the form or confirmation partials (`_customer_form.html`, `_customer_confirm_delete.html`) into the `#modalContent` div.
    -   **Form Submission**: All forms use `hx-post="{{ request.path }}" hx-swap="none"`. `hx-swap="none"` ensures that the HTMX response (status 204 with `HX-Trigger`) does not attempt to swap content, as the page state is updated via the `refreshCustomerList` event.
    -   **Success Messages**: Django's `messages` framework is used. After a successful HTMX submission, a message is added. The `base.html` (which is assumed to exist) should contain logic to display these messages.

-   **Alpine.js for UI State Management**:
    -   **Modal Visibility**: The `_` (Alpine.js) syntax is used directly in the HTML for simple imperative UI behaviors.
        -   `_="on click add .is-active to #modal"`: When a button to open the modal is clicked, this adds a CSS class to reveal the modal.
        -   `_="on click if event.target.id == 'modal' remove .is-active from me"`: Clicking outside the modal content (on the modal overlay itself) removes the `.is-active` class, hiding the modal.
        -   `_="on click remove .is-active from #modal"`: The "Cancel" button within the modal explicitly closes it.
    -   Alpine.js is specifically chosen for its minimalist approach, avoiding the overhead of full-blown JavaScript frameworks, aligning with the "no additional JavaScript" preference.

-   **DataTables for List Views**:
    -   The `_customer_table.html` partial includes the JavaScript to initialize DataTables (`$('#customerTable').DataTable({...});`).
    -   Crucially, this script is part of the HTMX-loaded content, ensuring it runs *after* the table is loaded into the DOM.
    -   It also includes logic `if ($.fn.DataTable.isDataTable('#customerTable')) { $('#customerTable').DataTable().destroy(); }` to prevent re-initialization errors when HTMX reloads the table.
    -   DataTables provides client-side searching, sorting, and pagination without requiring any backend code changes for these features.

-   **Seamless User Experience**: All these integrations ensure that user interactions for adding, editing, and deleting customers happen without full page reloads, providing a modern, fast, and responsive feel, improving the overall user experience of the ERP module.

## Final Notes

-   **App Integration**: This Django app (`customer_enquiry`) would need to be added to your Django project's `settings.py` (`INSTALLED_APPS`).
-   **Database Setup**: Ensure your `settings.py` is configured to connect to your existing ASP.NET SQL Server database. If `managed = False` in the `Customer` model's `Meta` class, Django will *not* create or alter the `TblCustomer` table. If the database schema needs to be managed by Django, `managed` should be set to `True` and migrations run.
-   **Tailwind CSS**: The template snippets assume Tailwind CSS is configured and available in `core/base.html`.
-   **CDN Links**: `core/base.html` should contain the CDN links for jQuery, DataTables, HTMX, and Alpine.js, as per the DRY principle.
-   **Error Handling**: Robust error handling (e.g., displaying specific error messages from the backend on form validation failures) is facilitated by Django Forms and the HTMX swap behavior.
-   **Authentication/Authorization**: For a production ERP system, Django's built-in authentication system and `LoginRequiredMixin` / `PermissionRequiredMixin` would be integrated into the views to secure access to these operations.