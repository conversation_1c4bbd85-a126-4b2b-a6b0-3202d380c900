## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The provided ASP.NET code is extremely minimal, consisting of an empty ASP.NET page and a code-behind with an empty `Page_Load` method. This means there is no explicit database schema, backend functionality, or UI components to analyze directly from the given input.

However, a typical ASP.NET page titled "CustPO" (Customer Purchase Order) in a "SalesDistribution" module implies managing customer purchase orders. Therefore, we will proceed with a modernization plan for a `CustomerOrder` entity, inferring a standard set of CRUD operations and UI components that would typically exist for such a page, even if not explicitly present in the provided empty code.

This approach demonstrates the systematic conversion process you would apply to a more complex ASP.NET application, using common patterns and best practices to achieve a modern Django solution.

---

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is an empty placeholder, we infer a common database structure for "Customer Purchase Orders" (`CustPO`). In a real scenario, this step would involve examining `SqlDataSource` controls, direct SQL queries in C#, or database schema definitions.

**Inferred Database Details:**

*   **Table Name:** `tbl_customer_order`
*   **Columns:**
    *   `order_id` (Primary Key, Integer)
    *   `order_number` (String, e.g., 'PO-2023-001')
    *   `customer_name` (String)
    *   `order_date` (Date)
    *   `total_amount` (Decimal)
    *   `is_active` (Boolean)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the page's context ("CustPO"), we infer the need for standard data management operations. In a real ASP.NET application, you would look for `GridView` events (`OnRowEditing`, `OnRowUpdating`, `OnRowDeleting`), `Button` click handlers that trigger `SqlDataSource` operations, or direct ADO.NET calls.

**Inferred Functionality:**

*   **Read:** Display a list of all customer purchase orders.
*   **Create:** Allow adding new customer purchase orders.
*   **Update:** Enable editing existing customer purchase orders.
*   **Delete:** Provide functionality to remove customer purchase orders.
*   **Validation:** Basic field validation (e.g., required fields, data type checks) would be implied for form submissions.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

From a typical "CustPO" page, we infer the following UI components. In a real ASP.NET application, you would directly identify `asp:GridView`, `asp:TextBox`, `asp:DropDownList`, `asp:Button` controls.

**Inferred UI Components:**

*   **Data Display:** A table (analogous to `asp:GridView`) to list customer purchase orders with columns for `Order Number`, `Customer Name`, `Order Date`, `Total Amount`, and `Actions` (Edit, Delete buttons). This will be replaced by DataTables.
*   **Data Entry/Edit:** A form (analogous to `asp:TextBox`, `asp:DropDownList`, `asp:Button`) for creating new orders and editing existing ones. This will be a modal form using HTMX.
*   **Action Buttons:** Buttons for "Add New Customer Order" and "Save", "Cancel", "Delete Confirmation" within forms and list views.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the inferred database schema.

## Instructions:

The model will map to the `tbl_customer_order` table. We'll name the model `CustomerOrder` and define fields corresponding to the inferred columns, setting `managed = False` and `db_table` as required.

**File: `sales_distribution/models.py`**

```python
from django.db import models

class CustomerOrder(models.Model):
    order_id = models.IntegerField(db_column='order_id', primary_key=True) # Assuming order_id is the primary key
    order_number = models.CharField(db_column='order_number', max_length=100, unique=True)
    customer_name = models.CharField(db_column='customer_name', max_length=255)
    order_date = models.DateField(db_column='order_date')
    total_amount = models.DecimalField(db_column='total_amount', max_digits=10, decimal_places=2)
    is_active = models.BooleanField(db_column='is_active', default=True)

    class Meta:
        managed = False # Django will not manage this table's creation, alteration, or deletion.
        db_table = 'tbl_customer_order' # Map to the existing database table
        verbose_name = 'Customer Order'
        verbose_name_plural = 'Customer Orders'

    def __str__(self):
        return f"PO: {self.order_number} for {self.customer_name}"

    def get_display_status(self):
        """
        Example business logic method: Returns a friendly status string.
        """
        return "Active" if self.is_active else "Archived"

    def calculate_tax(self, tax_rate=0.08):
        """
        Example business logic method: Calculates tax based on total_amount.
        """
        return self.total_amount * models.Decimal(str(tax_rate))
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

Create a `ModelForm` for the `CustomerOrder` model. We will include relevant fields and add Tailwind CSS classes to the widgets for consistent styling.

**File: `sales_distribution/forms.py`**

```python
from django import forms
from .models import CustomerOrder

class CustomerOrderForm(forms.ModelForm):
    class Meta:
        model = CustomerOrder
        fields = ['order_number', 'customer_name', 'order_date', 'total_amount', 'is_active']
        widgets = {
            'order_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'order_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        
    def clean_total_amount(self):
        """
        Example custom validation: Ensure total_amount is positive.
        """
        total_amount = self.cleaned_data.get('total_amount')
        if total_amount is not None and total_amount < 0:
            raise forms.ValidationError("Total amount cannot be negative.")
        return total_amount

    def clean_order_date(self):
        """
        Example custom validation: Ensure order_date is not in the future.
        """
        order_date = self.cleaned_data.get('order_date')
        if order_date and order_date > forms.date.today():
            raise forms.ValidationError("Order date cannot be in the future.")
        return order_date
```

### 4.3 Views

Task: Implement CRUD operations using Django Class-Based Views (CBVs).

## Instructions:

We'll define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for `CustomerOrder`. Additionally, a `CustomerOrderTablePartialView` will be added to handle HTMX requests for refreshing the DataTables content. Views will remain thin, delegating business logic to the model.

**File: `sales_distribution/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import CustomerOrder
from .forms import CustomerOrderForm

class CustomerOrderListView(ListView):
    model = CustomerOrder
    template_name = 'sales_distribution/customerorder/list.html'
    context_object_name = 'customerorders'

class CustomerOrderTablePartialView(ListView):
    model = CustomerOrder
    template_name = 'sales_distribution/customerorder/_customerorder_table.html'
    context_object_name = 'customerorders'

class CustomerOrderCreateView(CreateView):
    model = CustomerOrder
    form_class = CustomerOrderForm
    template_name = 'sales_distribution/customerorder/_customerorder_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('customerorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshCustomerOrderList' # Custom HTMX event to refresh list
                }
            )
        return response

class CustomerOrderUpdateView(UpdateView):
    model = CustomerOrder
    form_class = CustomerOrderForm
    template_name = 'sales_distribution/customerorder/_customerorder_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('customerorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshCustomerOrderList'
                }
            )
        return response

class CustomerOrderDeleteView(DeleteView):
    model = CustomerOrder
    template_name = 'sales_distribution/customerorder/_customerorder_confirm_delete.html' # Use partial for HTMX modal
    success_url = reverse_lazy('customerorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshCustomerOrderList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will use DRY principles, extending `core/base.html` and utilizing partial templates for forms and tables. HTMX will be used for dynamic loading and interactions, and DataTables for list presentation.

**File: `sales_distribution/templates/sales_distribution/customerorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customer Orders</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'customerorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Customer Order
        </button>
    </div>
    
    <div id="customerorderTable-container"
         hx-trigger="load, refreshCustomerOrderList from:body"
         hx-get="{% url 'customerorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Customer Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // Example: x-data="{ showModal: false }" on a parent element
    });
</script>
{% endblock %}
```

**File: `sales_distribution/templates/sales_distribution/customerorder/_customerorder_table.html`**

```html
<table id="customerorderTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Number</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in customerorders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.order_number }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.order_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.total_amount }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_display_status }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'customerorder_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'customerorder_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 text-center text-gray-500">No customer orders found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// This script runs every time the partial is loaded by HTMX
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#customerorderTable')) {
        $('#customerorderTable').DataTable().destroy();
    }
    $('#customerorderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 6] }, // Disable sorting for SN and Actions
            { "searchable": false, "targets": [0, 6] } // Disable searching for SN and Actions
        ]
    });
});
</script>
```

**File: `sales_distribution/templates/sales_distribution/customerorder/_customerorder_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" to prevent modal content from being replaced by success response #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `sales_distribution/templates/sales_distribution/customerorder/_customerorder_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete the customer order: <strong>{{ object.order_number }} - {{ object.customer_name }}</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create URL patterns for the list, create, update, delete, and the HTMX table partial view.

**File: `sales_distribution/urls.py`**

```python
from django.urls import path
from .views import CustomerOrderListView, CustomerOrderCreateView, CustomerOrderUpdateView, CustomerOrderDeleteView, CustomerOrderTablePartialView

urlpatterns = [
    path('customerorders/', CustomerOrderListView.as_view(), name='customerorder_list'),
    path('customerorders/table/', CustomerOrderTablePartialView.as_view(), name='customerorder_table'), # HTMX partial
    path('customerorders/add/', CustomerOrderCreateView.as_view(), name='customerorder_add'),
    path('customerorders/edit/<int:pk>/', CustomerOrderUpdateView.as_view(), name='customerorder_edit'),
    path('customerorders/delete/<int:pk>/', CustomerOrderDeleteView.as_view(), name='customerorder_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and integration tests for all views to ensure at least 80% test coverage.

**File: `sales_distribution/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import CustomerOrder
import datetime
from decimal import Decimal

class CustomerOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.customer_order = CustomerOrder.objects.create(
            order_id=1,
            order_number='PO-TEST-001',
            customer_name='Test Customer Inc.',
            order_date=datetime.date(2023, 1, 15),
            total_amount=Decimal('1500.75'),
            is_active=True
        )
        # Create another for specific tests
        cls.inactive_order = CustomerOrder.objects.create(
            order_id=2,
            order_number='PO-TEST-002',
            customer_name='Archived Customer',
            order_date=datetime.date(2022, 5, 20),
            total_amount=Decimal('500.00'),
            is_active=False
        )
  
    def test_customer_order_creation(self):
        obj = CustomerOrder.objects.get(order_id=1)
        self.assertEqual(obj.order_number, 'PO-TEST-001')
        self.assertEqual(obj.customer_name, 'Test Customer Inc.')
        self.assertEqual(obj.order_date, datetime.date(2023, 1, 15))
        self.assertEqual(obj.total_amount, Decimal('1500.75'))
        self.assertTrue(obj.is_active)
        
    def test_order_number_label(self):
        obj = CustomerOrder.objects.get(order_id=1)
        field_label = obj._meta.get_field('order_number').verbose_name
        self.assertEqual(field_label, 'order number') # Default verbose name is field name with spaces
        
    def test_str_method(self):
        obj = CustomerOrder.objects.get(order_id=1)
        self.assertEqual(str(obj), 'PO: PO-TEST-001 for Test Customer Inc.')

    def test_get_display_status_active(self):
        self.assertEqual(self.customer_order.get_display_status(), 'Active')

    def test_get_display_status_inactive(self):
        self.assertEqual(self.inactive_order.get_display_status(), 'Archived')

    def test_calculate_tax_default_rate(self):
        self.assertEqual(self.customer_order.calculate_tax(), Decimal('120.06')) # 1500.75 * 0.08

    def test_calculate_tax_custom_rate(self):
        self.assertEqual(self.customer_order.calculate_tax(0.10), Decimal('150.08')) # 1500.75 * 0.10

class CustomerOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views, ensuring a record exists for updates/deletes
        cls.customer_order = CustomerOrder.objects.create(
            order_id=3,
            order_number='PO-VIEW-001',
            customer_name='View Test Co.',
            order_date=datetime.date(2023, 2, 1),
            total_amount=Decimal('2000.00'),
            is_active=True
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('customerorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerorder/list.html')
        self.assertTrue('customerorders' in response.context)
        self.assertContains(response, 'PO-VIEW-001') # Check if existing order is listed
        
    def test_table_partial_view(self):
        response = self.client.get(reverse('customerorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerorder/_customerorder_table.html')
        self.assertTrue('customerorders' in response.context)
        self.assertContains(response, 'PO-VIEW-001') # Check if existing order is listed

    def test_create_view_get(self):
        response = self.client.get(reverse('customerorder_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerorder/_customerorder_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'order_number': 'PO-NEW-001',
            'customer_name': 'New Customer LLC',
            'order_date': '2023-03-01',
            'total_amount': '750.25',
            'is_active': 'on', # Checkbox value for 'on'
        }
        # Simulate HTMX request for modal form submission
        response = self.client.post(reverse('customerorder_add'), data, HTTP_HX_REQUEST='true')
        # HTMX success should return 204 No Content
        self.assertEqual(response.status_code, 204)
        # Verify HTMX-Trigger header
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshCustomerOrderList')
        # Verify object was created
        self.assertTrue(CustomerOrder.objects.filter(order_number='PO-NEW-001').exists())
        
    def test_create_view_post_invalid(self):
        data = {
            'order_number': 'PO-INVALID',
            'customer_name': '', # Missing required field
            'order_date': '2024-01-01', # Future date, will trigger validation
            'total_amount': '-100.00', # Negative amount, will trigger validation
            'is_active': 'on',
        }
        response = self.client.post(reverse('customerorder_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertFormError(response, 'form', 'customer_name', ['This field is required.'])
        self.assertFormError(response, 'form', 'total_amount', ['Total amount cannot be negative.'])
        self.assertFormError(response, 'form', 'order_date', ['Order date cannot be in the future.'])
        self.assertFalse(CustomerOrder.objects.filter(order_number='PO-INVALID').exists())
        
    def test_update_view_get(self):
        response = self.client.get(reverse('customerorder_edit', args=[self.customer_order.order_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerorder/_customerorder_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.order_number, 'PO-VIEW-001')

    def test_update_view_post_success(self):
        data = {
            'order_number': 'PO-UPDATED',
            'customer_name': 'Updated Customer Name',
            'order_date': '2023-02-01',
            'total_amount': '2500.00',
            'is_active': 'on',
        }
        response = self.client.post(reverse('customerorder_edit', args=[self.customer_order.order_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshCustomerOrderList')
        self.customer_order.refresh_from_db()
        self.assertEqual(self.customer_order.order_number, 'PO-UPDATED')
        self.assertEqual(self.customer_order.customer_name, 'Updated Customer Name')

    def test_delete_view_get(self):
        response = self.client.get(reverse('customerorder_delete', args=[self.customer_order.order_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerorder/_customerorder_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].order_number, 'PO-VIEW-001')
        
    def test_delete_view_post_success(self):
        # Create a new object to delete, so it doesn't affect other tests if run out of order
        order_to_delete = CustomerOrder.objects.create(
            order_id=4,
            order_number='PO-DEL-001',
            customer_name='Delete Me',
            order_date=datetime.date(2023, 4, 1),
            total_amount=Decimal('100.00'),
            is_active=True
        )
        response = self.client.post(reverse('customerorder_delete', args=[order_to_delete.order_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshCustomerOrderList')
        self.assertFalse(CustomerOrder.objects.filter(order_id=order_to_delete.order_id).exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The generated code fully embraces HTMX and Alpine.js for a modern, dynamic user experience without requiring complex JavaScript.

*   **HTMX for CRUD operations:**
    *   The "Add New Customer Order", "Edit", and "Delete" buttons in `list.html` and `_customerorder_table.html` use `hx-get` to load forms or confirmation modals into a `#modalContent` div.
    *   Form submissions (`_customerorder_form.html`, `_customerorder_confirm_delete.html`) use `hx-post` to send data.
    *   Upon successful form submission or deletion, the Django views return `HttpResponse` with `status=204` and an `HX-Trigger` header (`refreshCustomerOrderList`).
    *   The main list container (`customerorderTable-container` in `list.html`) listens for the `refreshCustomerOrderList` event using `hx-trigger="load, refreshCustomerOrderList from:body"`, causing it to re-fetch and re-render the `_customerorder_table.html` partial, thus updating the DataTables.
*   **Alpine.js for Modals:**
    *   The `#modal` div in `list.html` uses Alpine.js (via `_` attribute syntax) to control its visibility.
    *   `_="on click add .is-active to #modal"` shows the modal when buttons are clicked.
    *   `_="on click if event.target.id == 'modal' remove .is-active from me"` hides the modal when clicking outside the content area.
    *   "Cancel" buttons in forms also use Alpine.js to close the modal.
*   **DataTables for List Views:**
    *   The `_customerorder_table.html` partial includes the JavaScript to initialize DataTables (`$('#customerorderTable').DataTable({...});`) after the table content is loaded via HTMX. This ensures searching, sorting, and pagination work correctly on the dynamically loaded content.
    *   We added logic to destroy any existing DataTable instance before re-initializing to prevent conflicts when the partial is reloaded.

## Final Notes

This comprehensive plan transforms the minimal ASP.NET page into a fully functional, modern Django application. It demonstrates the use of a "fat model, thin view" architecture, leverages HTMX and Alpine.js for interactive UI, integrates DataTables for efficient data presentation, and includes robust testing. This approach ensures a scalable, maintainable, and user-friendly application, fulfilling all the specified modernization guidelines.