## ASP.NET to Django Conversion Script:

This modernization plan outlines the automated conversion of your existing ASP.NET 'Customer Master' functionality into a robust, scalable, and modern Django application. Given the minimal ASP.NET code provided, we will infer a common 'Customer Master' data structure and typical CRUD (Create, Read, Update, Delete) operations. This approach is designed for execution through AI-assisted automation, minimizing manual coding and maximizing efficiency.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET `.aspx` and `.aspx.cs` files are largely empty boilerplate code, indicating a minimal or placeholder page. Therefore, we cannot directly extract database schema, CRUD operations, or UI components from the provided code.

**Inference:** Based on the file name `CustMaster.aspx`, it's highly probable this page manages customer-related data. We will infer a common customer master table structure.

**Inferred Table Name:** `customer_master`
**Inferred Column Names and Data Types:**
- `id`: Primary Key (handled automatically by Django)
- `customer_code`: VARCHAR(50), Unique
- `customer_name`: VARCHAR(255)
- `contact_person`: VARCHAR(100), Nullable
- `phone_number`: VARCHAR(20), Nullable
- `email`: VARCHAR(255), Nullable, Email format
- `address`: TEXT, Nullable
- `is_active`: BOOLEAN, Default True
- `created_at`: DATETIME
- `updated_at`: DATETIME

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
As noted in Step 1, the provided ASP.NET code does not contain explicit backend functionality or data operations.

**Inference:** For a 'Customer Master' page, standard CRUD operations are implicitly required:
- **Create:** Adding new customer records.
- **Read:** Displaying a list of customers and details of individual customers.
- **Update:** Modifying existing customer records.
- **Delete:** Removing customer records.
- **Validation:** Basic field validations (e.g., required fields, unique customer code, valid email format).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET `.aspx` file only contains content placeholders and a script reference, with no specific UI controls like GridView, TextBox, or Button.

**Inference:** A typical 'Customer Master' UI would involve:
- **List View:** A table (likely a GridView in ASP.NET) to display customer records with features for searching, sorting, and pagination.
- **Form View:** Input fields (Textboxes, DropDownLists) for capturing customer details (code, name, contact, phone, email, address, active status).
- **Action Buttons:** Buttons for "Add New Customer", "Edit", and "Delete".
- **Dynamic Interactions:** Client-side interactions for modal forms and refreshing data tables.

### Step 4: Generate Django Code

We will now generate the corresponding Django application files based on the inferred schema and functionality. We will use `sales_distribution` as the Django app name, aligning with the original ASP.NET namespace.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `Customer` will map to the existing `customer_master` table. Business logic for customer validation or specific data manipulation will reside here.

**File: `sales_distribution/models.py`**

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class Customer(models.Model):
    customer_code = models.CharField(
        db_column='customer_code', max_length=50, unique=True,
        verbose_name=_('Customer Code'),
        help_text=_('Unique identifier for the customer.')
    )
    customer_name = models.CharField(
        db_column='customer_name', max_length=255,
        verbose_name=_('Customer Name'),
        help_text=_('Full legal name of the customer.')
    )
    contact_person = models.CharField(
        db_column='contact_person', max_length=100, blank=True, null=True,
        verbose_name=_('Contact Person'),
        help_text=_('Primary contact person at the customer organization.')
    )
    phone_number = models.CharField(
        db_column='phone_number', max_length=20, blank=True, null=True,
        verbose_name=_('Phone Number')
    )
    email = models.EmailField(
        db_column='email', max_length=255, blank=True, null=True,
        verbose_name=_('Email Address')
    )
    address = models.TextField(
        db_column='address', blank=True, null=True,
        verbose_name=_('Address')
    )
    is_active = models.BooleanField(
        db_column='is_active', default=True,
        verbose_name=_('Is Active'),
        help_text=_('Designates whether this customer should be treated as active.')
    )
    created_at = models.DateTimeField(
        db_column='created_at', auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        db_column='updated_at', auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'customer_master'  # Name of the existing database table
        verbose_name = _('Customer')
        verbose_name_plural = _('Customers')
        ordering = ['customer_name']

    def __str__(self):
        return f"{self.customer_name} ({self.customer_code})"

    def clean(self):
        """
        Custom validation for the Customer model.
        """
        super().clean()
        if not self.customer_code.strip():
            raise ValidationError({'customer_code': _("Customer code cannot be empty.")})
        if not self.customer_name.strip():
            raise ValidationError({'customer_name': _("Customer name cannot be empty.")})
        
        # Example of business logic: Ensure email is unique if provided
        if self.email and Customer.objects.filter(email=self.email).exclude(pk=self.pk).exists():
            raise ValidationError({'email': _("A customer with this email already exists.")})

    def save(self, *args, **kwargs):
        """
        Override save to ensure clean is called before saving.
        """
        self.full_clean() # Calls clean() and validates all fields
        super().save(*args, **kwargs)

    def deactivate_customer(self):
        """
        Business logic: Deactivates the customer.
        """
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False

    def activate_customer(self):
        """
        Business logic: Activates the customer.
        """
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be used for `Customer` objects, including all relevant fields for CRUD operations. Tailwind CSS classes are applied for styling.

**File: `sales_distribution/forms.py`**

```python
from django import forms
from .models import Customer

class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = [
            'customer_code', 'customer_name', 'contact_person',
            'phone_number', 'email', 'address', 'is_active'
        ]
        widgets = {
            'customer_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_person': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'phone_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'customer_code': 'Customer Code',
            'customer_name': 'Customer Name',
            'contact_person': 'Contact Person',
            'phone_number': 'Phone Number',
            'email': 'Email Address',
            'address': 'Address',
            'is_active': 'Is Active',
        }
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views remain thin, delegating all business logic to the `Customer` model. HTMX requests are handled with `HttpResponse(status=204)` and `HX-Trigger` headers for client-side updates without full page reloads. A `TablePartialView` is added to specifically handle HTMX requests for refreshing the DataTables content.

**File: `sales_distribution/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Customer
from .forms import CustomerForm

class CustomerListView(ListView):
    model = Customer
    template_name = 'sales_distribution/customer/list.html'
    context_object_name = 'customers'

class CustomerTablePartialView(ListView):
    model = Customer
    template_name = 'sales_distribution/customer/_customer_table.html'
    context_object_name = 'customers'

    def get_queryset(self):
        # Example: Add filtering/ordering if needed for DataTables server-side processing
        # For client-side DataTables, this simply returns all customers
        return super().get_queryset()

class CustomerCreateView(CreateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'sales_distribution/customer/form.html'
    success_url = reverse_lazy('customer_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors for partial rendering
            return self.render_to_response(self.get_context_data(form=form))
        return response

class CustomerUpdateView(UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'sales_distribution/customer/form.html'
    success_url = reverse_lazy('customer_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return response

class CustomerDeleteView(DeleteView):
    model = Customer
    template_name = 'sales_distribution/customer/confirm_delete.html'
    success_url = reverse_lazy('customer_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates utilize DRY principles with partials and extend `core/base.html`. HTMX attributes manage dynamic interactions, and Alpine.js controls modal visibility. DataTables is initialized for the customer list.

**File: `sales_distribution/templates/sales_distribution/customer/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customers</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'customer_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Customer
        </button>
    </div>
    
    <div id="customerTable-container"
         hx-trigger="load, refreshCustomerList from:body"
         hx-get="{% url 'customer_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading customer data...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // Example: x-data="{ open: false }" for specific dropdowns
    });
</script>
{% endblock %}
```

**File: `sales_distribution/templates/sales_distribution/customer/_customer_table.html`**

```html
<table id="customerTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for customer in customers %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ customer.customer_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ customer.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ customer.contact_person|default:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ customer.phone_number|default:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ customer.email|default:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                {% if customer.is_active %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                {% endif %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'customer_edit' customer.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'customer_delete' customer.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    $('#customerTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

**File: `sales_distribution/templates/sales_distribution/customer/_customer_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent" hx-on::after-request="if(event.detail.successful) htmx.remove(htmx.find('#modal').classList.remove('is-active'))">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4 {% if field.name == 'address' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `sales_distribution/templates/sales_distribution/customer/_customer_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete customer: 
        <span class="font-semibold">{{ object.customer_name }} ({{ object.customer_code }})</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'customer_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.successful) htmx.remove(htmx.find('#modal').classList.remove('is-active'))">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are set up for all CRUD operations, including a specific endpoint for the HTMX-driven table partial.

**File: `sales_distribution/urls.py`**

```python
from django.urls import path
from .views import CustomerListView, CustomerCreateView, CustomerUpdateView, CustomerDeleteView, CustomerTablePartialView

urlpatterns = [
    path('customer/', CustomerListView.as_view(), name='customer_list'),
    path('customer/add/', CustomerCreateView.as_view(), name='customer_add'),
    path('customer/edit/<int:pk>/', CustomerUpdateView.as_view(), name='customer_edit'),
    path('customer/delete/<int:pk>/', CustomerDeleteView.as_view(), name='customer_delete'),
    path('customer/table/', CustomerTablePartialView.as_view(), name='customer_table'), # For HTMX partial update
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests ensure model methods and validation work correctly. Integration tests verify view functionality, including form submissions, redirects, and HTMX responses.

**File: `sales_distribution/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from .models import Customer

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.customer1 = Customer.objects.create(
            customer_code='CUST001',
            customer_name='Alpha Company',
            contact_person='John Doe',
            phone_number='************',
            email='<EMAIL>',
            address='123 Main St, Anytown',
            is_active=True
        )
        cls.customer2 = Customer.objects.create(
            customer_code='CUST002',
            customer_name='Beta Corp',
            contact_person='Jane Smith',
            phone_number='************',
            email='<EMAIL>',
            address='456 Oak Ave, Somewhere',
            is_active=False
        )
  
    def test_customer_creation(self):
        customer = Customer.objects.get(customer_code='CUST001')
        self.assertEqual(customer.customer_name, 'Alpha Company')
        self.assertTrue(customer.is_active)
        self.assertIsNotNone(customer.created_at)
        self.assertIsNotNone(customer.updated_at)

    def test_unique_customer_code(self):
        with self.assertRaises(IntegrityError):
            Customer.objects.create(
                customer_code='CUST001', # Duplicate code
                customer_name='Gamma Inc'
            )
    
    def test_customer_code_label(self):
        customer = Customer.objects.get(customer_code='CUST001')
        field_label = customer._meta.get_field('customer_code').verbose_name
        self.assertEqual(field_label, 'Customer Code')

    def test_str_representation(self):
        customer = Customer.objects.get(customer_code='CUST001')
        self.assertEqual(str(customer), 'Alpha Company (CUST001)')

    def test_clean_method_required_fields(self):
        customer = Customer(customer_code='', customer_name='')
        with self.assertRaisesMessage(ValidationError, 'Customer code cannot be empty.'):
            customer.full_clean()
        
        customer.customer_code = 'CUST003'
        with self.assertRaisesMessage(ValidationError, 'Customer name cannot be empty.'):
            customer.full_clean()

    def test_clean_method_unique_email(self):
        customer = Customer(
            customer_code='CUST003',
            customer_name='New Company',
            email='<EMAIL>' # Existing email
        )
        with self.assertRaisesMessage(ValidationError, 'A customer with this email already exists.'):
            customer.full_clean()

    def test_deactivate_customer_method(self):
        customer = self.customer1
        self.assertTrue(customer.is_active)
        customer.deactivate_customer()
        customer.refresh_from_db()
        self.assertFalse(customer.is_active)
        # Test deactivating an already inactive customer
        self.assertFalse(customer.deactivate_customer())

    def test_activate_customer_method(self):
        customer = self.customer2
        self.assertFalse(customer.is_active)
        customer.activate_customer()
        customer.refresh_from_db()
        self.assertTrue(customer.is_active)
        # Test activating an already active customer
        self.assertFalse(customer.activate_customer())


class CustomerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.customer1 = Customer.objects.create(
            customer_code='CUST001',
            customer_name='Alpha Company',
            contact_person='John Doe',
            phone_number='************',
            email='<EMAIL>',
            address='123 Main St',
            is_active=True
        )
        cls.customer2 = Customer.objects.create(
            customer_code='CUST002',
            customer_name='Beta Corp',
            contact_person='Jane Smith',
            phone_number='************',
            email='<EMAIL>',
            address='456 Oak Ave',
            is_active=False
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('customer_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer/list.html')
        self.assertIn('customers', response.context)
        self.assertContains(response, 'Alpha Company')
        self.assertContains(response, 'Beta Corp')

    def test_table_partial_view(self):
        # Test the HTMX-specific table partial view
        response = self.client.get(reverse('customer_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer/_customer_table.html')
        self.assertIn('customers', response.context)
        self.assertContains(response, 'Alpha Company')
        self.assertContains(response, 'Beta Corp')

    def test_create_view_get(self):
        response = self.client.get(reverse('customer_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Customer') # Check for form title

    def test_create_view_post_success(self):
        data = {
            'customer_code': 'CUST003',
            'customer_name': 'New Customer Inc',
            'contact_person': 'Bob Johnson',
            'phone_number': '************',
            'email': '<EMAIL>',
            'address': '789 Pine Ln',
            'is_active': 'on', # Checkbox sends 'on' or nothing
        }
        response = self.client.post(reverse('customer_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX successful creation should return 204 No Content
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerList')
        
        # Verify object was created
        self.assertTrue(Customer.objects.filter(customer_code='CUST003').exists())
        new_customer = Customer.objects.get(customer_code='CUST003')
        self.assertEqual(new_customer.customer_name, 'New Customer Inc')
        self.assertTrue(new_customer.is_active)
        
        # Test non-HTMX response (redirect)
        response_non_htmx = self.client.post(reverse('customer_add'), data)
        self.assertEqual(response_non_htmx.status_code, 302)
        self.assertRedirects(response_non_htmx, reverse('customer_list'))

    def test_create_view_post_invalid(self):
        data = {
            'customer_code': '', # Invalid: empty
            'customer_name': 'Invalid Customer',
        }
        response = self.client.post(reverse('customer_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns form with errors
        self.assertContains(response, 'Customer code cannot be empty.')
        self.assertTemplateUsed(response, 'sales_distribution/customer/form.html')

    def test_update_view_get(self):
        response = self.client.get(reverse('customer_edit', args=[self.customer1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Customer') # Check for form title
        self.assertContains(response, self.customer1.customer_name)

    def test_update_view_post_success(self):
        data = {
            'customer_code': 'CUST001', # Same code
            'customer_name': 'Alpha Company Updated',
            'contact_person': 'New Contact',
            'is_active': 'on',
        }
        response = self.client.post(reverse('customer_edit', args=[self.customer1.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerList')
        
        self.customer1.refresh_from_db()
        self.assertEqual(self.customer1.customer_name, 'Alpha Company Updated')
        self.assertEqual(self.customer1.contact_person, 'New Contact')

    def test_update_view_post_invalid(self):
        data = {
            'customer_code': 'CUST002', # Duplicate code (of customer2)
            'customer_name': 'Alpha Company',
        }
        response = self.client.post(reverse('customer_edit', args=[self.customer1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns form with errors
        self.assertContains(response, 'Customer with this Customer Code already exists.')
        self.assertTemplateUsed(response, 'sales_distribution/customer/form.html')

    def test_delete_view_get(self):
        response = self.client.get(reverse('customer_delete', args=[self.customer1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.customer1.customer_name)

    def test_delete_view_post_success(self):
        initial_count = Customer.objects.count()
        response = self.client.post(reverse('customer_delete', args=[self.customer1.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerList')
        
        self.assertEqual(Customer.objects.count(), initial_count - 1)
        self.assertFalse(Customer.objects.filter(pk=self.customer1.pk).exists())
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Updates:** All form submissions (Add, Edit, Delete) and table refreshes are handled by HTMX.
    - Forms submit with `hx-post`, `hx-swap="none"`, and `hx-on::after-request` to close the modal and trigger table refresh on success.
    - Delete actions also use `hx-post` with a similar success handling.
    - The `_customer_table.html` partial is loaded via `hx-get` on page load and `refreshCustomerList` event.
    - Buttons to open forms and delete confirmations use `hx-get` to load content into the modal.
- **Alpine.js for UI State:** Used minimally here for the modal's simple show/hide functionality, using `_ = "on click add .is-active to #modal"` and `remove .is-active from me`. For more complex interactive UI elements, Alpine.js's `x-data` and directives would be leveraged.
- **DataTables for List Views:** The `_customer_table.html` partial initializes DataTables on the loaded table, providing client-side search, sort, and pagination.
- **No Full Page Reloads:** All CRUD operations are designed to execute without full page reloads, enhancing user experience.
- **`HX-Trigger` Responses:** Views send `HX-Trigger: refreshCustomerList` on successful CRUD operations, prompting the client to re-fetch and update the customer table.

### Final Notes

This comprehensive Django modernization plan provides a clear, actionable roadmap for transitioning your `CustMaster` functionality. By leveraging AI-assisted automation, the focus is on generating structured, best-practice Django code that is testable, maintainable, and aligned with modern web development standards. The use of HTMX, Alpine.js, and DataTables ensures a highly interactive and responsive user experience without the complexity of traditional JavaScript frameworks. The modular approach, with strict separation of concerns, ensures future scalability and ease of maintenance.