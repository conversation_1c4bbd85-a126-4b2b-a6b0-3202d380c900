## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Analysis of ASP.NET Code:

The provided ASP.NET `.aspx` file consists only of `<asp:Content>` tags pointing to various content placeholders within a `MasterPage.master`. This indicates a page that uses a master layout but has no explicit UI elements defined within the `.aspx` itself. The corresponding C# code-behind file (`ProjectSummery.aspx.cs`) is equally minimal, containing only an empty `Page_Load` event handler.

This means there is no direct information about database interaction, UI controls (like `GridView`, `TextBox`, `Button`), or business logic.

### Inferred Business Context:

Given the page name `ProjectSummery.aspx`, located within `Module_DailyReportingSystem_Reports`, it is highly probable that this page is intended to display a summary or a list of "Projects". While the provided code is empty, a real-world `ProjectSummery` page would typically involve:

*   **Reading/Listing:** Displaying a list of projects from a database.
*   **Filtering/Searching:** Options to refine the list of projects.
*   **Drill-down:** Links to view project details.

For the purpose of demonstrating a complete Django modernization, we will infer a common "Project" entity and implement basic CRUD (Create, Read, Update, Delete) operations, as a summary page often involves administrative capabilities or at least linking to them.

**Assumptions for Django Modernization:**

*   **Django App Name:** `reports` (derived from `Module_DailyReportingSystem_Reports`)
*   **Django Model Name:** `Project`
*   **Inferred Database Table:** `tbl_project`
*   **Inferred Columns:** `project_id` (Primary Key), `project_name`, `start_date`, `end_date`, `status`.

---

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since no explicit database schema was found in the empty ASP.NET code, we infer a `Project` entity common for a "Project Summary" page.

*   **[TABLE_NAME]:** `tbl_project` (common convention for ASP.NET legacy systems)
*   **Columns:**
    *   `project_id` (likely an integer primary key)
    *   `project_name` (string)
    *   `start_date` (date)
    *   `end_date` (date)
    *   `status` (string, e.g., 'Active', 'Completed', 'On Hold')

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

As the ASP.NET code was empty, no explicit CRUD operations are identified. However, for a "Project Summary" page to be useful in a system, the ability to view (Read) projects is fundamental. To provide a complete modernization plan, we will implement full CRUD capabilities for the `Project` entity, which is a common requirement for such modules.

*   **Create:** Assume the need to add new projects.
*   **Read:** Displaying a list of projects is the primary inferred function.
*   **Update:** Assume the need to edit existing project details.
*   **Delete:** Assume the need to remove projects.
*   **Validation Logic:** Simple required field validation will be implemented at the Django form level.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

No UI controls are explicitly present in the provided `.aspx`. Based on the page name `ProjectSummery`, we infer:

*   **GridView equivalent:** A tabular display of projects, which will be implemented using DataTables in Django.
*   **Input Controls:** `TextBox` for project name, `DateTime` pickers for dates, `DropDownList` for status. These will be rendered via Django Forms.
*   **Buttons:** `Button` or `LinkButton` for 'Add New Project', 'Edit', 'Delete' actions, which will be implemented with HTMX-triggered buttons opening modals.

---

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

*   **[MODEL_NAME]:** `Project`
*   **[TABLE_NAME]:** `tbl_project`
*   Fields are defined with appropriate Django field types.
*   `managed = False` and `db_table = 'tbl_project'` are set.
*   A placeholder business logic method `is_active` is added to demonstrate the "fat model" approach.

```python
# reports/models.py
from django.db import models

class Project(models.Model):
    project_id = models.AutoField(db_column='project_id', primary_key=True) # Assuming project_id is primary key
    project_name = models.CharField(db_column='project_name', max_length=255, verbose_name='Project Name')
    start_date = models.DateField(db_column='start_date', verbose_name='Start Date')
    end_date = models.DateField(db_column='end_date', verbose_name='End Date', null=True, blank=True)
    status = models.CharField(db_column='status', max_length=50, verbose_name='Status')

    class Meta:
        managed = False # Set to True if Django should manage the table creation/migrations
        db_table = 'tbl_project'
        verbose_name = 'Project'
        verbose_name_plural = 'Projects'

    def __str__(self):
        return self.project_name
        
    def is_active(self):
        """
        Business logic: Determine if the project is currently active.
        """
        today = models.DateField(auto_now=True).today()
        return self.status == 'Active' and (self.start_date <= today and (self.end_date is None or self.end_date >= today))

    def update_status(self, new_status):
        """
        Business logic: Update the project status and save.
        """
        valid_statuses = ['Active', 'Completed', 'On Hold', 'Cancelled']
        if new_status not in valid_statuses:
            raise ValueError(f"Invalid status: {new_status}. Must be one of {valid_statuses}")
        self.status = new_status
        self.save()

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

*   A `ModelForm` for `Project` is created.
*   All editable fields are included.
*   Widgets with Tailwind CSS classes are added for styling.
*   Custom validation (e.g., `clean_end_date`) is included as an example.

```python
# reports/forms.py
from django import forms
from .models import Project

class ProjectForm(forms.ModelForm):
    class Meta:
        model = Project
        fields = ['project_name', 'start_date', 'end_date', 'status']
        widgets = {
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[
                ('Active', 'Active'),
                ('Completed', 'Completed'),
                ('On Hold', 'On Hold'),
                ('Cancelled', 'Cancelled')
            ], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_end_date(self):
        end_date = self.cleaned_data.get('end_date')
        start_date = self.cleaned_data.get('start_date')

        if end_date and start_date and end_date < start_date:
            raise forms.ValidationError("End date cannot be before start date.")
        return end_date
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

*   `ProjectListView` to display all projects.
*   `ProjectCreateView` to add new projects.
*   `ProjectUpdateView` to edit existing projects.
*   `ProjectDeleteView` to delete projects.
*   `ProjectTablePartialView` for HTMX to fetch only the table content.
*   Views are kept thin (5-15 lines) by offloading logic to models (though for simple CRUD, models might not have complex logic).
*   HTMX `HX-Trigger` headers are used for refreshing the list after successful form submissions or deletions.

```python
# reports/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Project
from .forms import ProjectForm

class ProjectListView(ListView):
    model = Project
    template_name = 'reports/project/list.html'
    context_object_name = 'projects' # Renamed from 'project_plural_lower' for clarity

class ProjectTablePartialView(ListView):
    model = Project
    template_name = 'reports/project/_project_table.html' # This partial view will only render the table
    context_object_name = 'projects'

class ProjectCreateView(CreateView):
    model = Project
    form_class = ProjectForm
    template_name = 'reports/project/_project_form.html' # Partial template for modal
    success_url = reverse_lazy('project_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Project added successfully.')
        if self.request.headers.get('HX-Request'):
            # This is an HTMX request, trigger a refresh for the list
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshProjectList' # Custom event to trigger HTMX reload
                }
            )
        return response

class ProjectUpdateView(UpdateView):
    model = Project
    form_class = ProjectForm
    template_name = 'reports/project/_project_form.html' # Partial template for modal
    success_url = reverse_lazy('project_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Project updated successfully.')
        if self.request.headers.get('HX-Request'):
            # This is an HTMX request, trigger a refresh for the list
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshProjectList' # Custom event to trigger HTMX reload
                }
            )
        return response

class ProjectDeleteView(DeleteView):
    model = Project
    template_name = 'reports/project/_project_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('project_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Project deleted successfully.')
        if request.headers.get('HX-Request'):
            # This is an HTMX request, trigger a refresh for the list
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshProjectList' # Custom event to trigger HTMX reload
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

*   **`reports/project/list.html`**: The main page extending `core/base.html`, setting up the modal and DataTables container.
*   **`reports/project/_project_table.html`**: Partial for the DataTables table, loaded via HTMX into `list.html`.
*   **`reports/project/_project_form.html`**: Partial for Create/Update forms, loaded into the modal.
*   **`reports/project/_project_confirm_delete.html`**: Partial for delete confirmation, loaded into the modal.

#### List Template (`reports/project/list.html`):

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Projects</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'project_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Project
        </button>
    </div>
    
    <div id="projectTable-container"
         hx-trigger="load, refreshProjectList from:body" {# Listen for custom event #}
         hx-get="{% url 'project_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading projects...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally.
    // If you need specific Alpine.js components on this page, define them here.
</script>
{% endblock %}
```

#### Table Partial Template (`reports/project/_project_table.html`):

```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="projectTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for project in projects %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ project.project_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ project.start_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ project.end_date|date:"Y-m-d"|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ project.status }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-sm mr-2"
                        hx-get="{% url 'project_edit' project.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                        hx-get="{% url 'project_delete' project.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No projects found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Only initialize DataTables if the table has rows, to avoid errors with empty tables
    if ($('#projectTable tbody tr').length > 0 && !$('#projectTable tbody tr td').hasClass('text-center')) {
        $('#projectTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true
        });
    }
});
</script>
```

#### Form Partial Template (`reports/project/_project_form.html`):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Project</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

#### Delete Confirmation Partial Template (`reports/project/_project_confirm_delete.html`):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the project: <strong>"{{ object.project_name }}"</strong>?</p>
    
    <form hx-post="{% url 'project_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

*   Paths for list, create, update, delete, and the HTMX table partial view.
*   URL names are consistent (e.g., `project_list`, `project_add`).

```python
# reports/urls.py
from django.urls import path
from .views import (
    ProjectListView,
    ProjectCreateView,
    ProjectUpdateView,
    ProjectDeleteView,
    ProjectTablePartialView,
)

urlpatterns = [
    path('projects/', ProjectListView.as_view(), name='project_list'),
    path('projects/table/', ProjectTablePartialView.as_view(), name='project_table'), # HTMX partial
    path('projects/add/', ProjectCreateView.as_view(), name='project_add'),
    path('projects/edit/<int:pk>/', ProjectUpdateView.as_view(), name='project_edit'),
    path('projects/delete/<int:pk>/', ProjectDeleteView.as_view(), name='project_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

*   Comprehensive unit tests for model methods and properties.
*   Integration tests for all views (list, create, update, delete) including HTMX behavior.
*   Aim for at least 80% test coverage.

```python
# reports/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Project
import json

class ProjectModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.project1 = Project.objects.create(
            project_name='Test Project One',
            start_date=timezone.now().date(),
            end_date=None,
            status='Active'
        )
        cls.project2 = Project.objects.create(
            project_name='Completed Project',
            start_date=timezone.datetime(2022, 1, 1).date(),
            end_date=timezone.datetime(2022, 12, 31).date(),
            status='Completed'
        )
        cls.project3 = Project.objects.create(
            project_name='Future Project',
            start_date=timezone.datetime(2025, 1, 1).date(),
            end_date=timezone.datetime(2025, 12, 31).date(),
            status='On Hold'
        )
  
    def test_project_creation(self):
        self.assertEqual(self.project1.project_name, 'Test Project One')
        self.assertEqual(self.project1.status, 'Active')
        self.assertIsNone(self.project1.end_date)
        
    def test_project_name_label(self):
        field_label = self.project1._meta.get_field('project_name').verbose_name
        self.assertEqual(field_label, 'Project Name')
        
    def test_object_name_is_project_name(self):
        expected_object_name = self.project1.project_name
        self.assertEqual(str(self.project1), expected_object_name)

    def test_is_active_method(self):
        # Test an active project
        self.assertTrue(self.project1.is_active())
        # Test a completed project
        self.assertFalse(self.project2.is_active())
        # Test a future project (not active yet)
        self.assertFalse(self.project3.is_active())
        # Test a project with end_date in the past
        past_project = Project.objects.create(
            project_name='Past Project',
            start_date=timezone.datetime(2020, 1, 1).date(),
            end_date=timezone.datetime(2021, 1, 1).date(),
            status='Active'
        )
        self.assertFalse(past_project.is_active())

    def test_update_status_method(self):
        self.project1.update_status('On Hold')
        self.project1.refresh_from_db()
        self.assertEqual(self.project1.status, 'On Hold')

        with self.assertRaises(ValueError):
            self.project1.update_status('Invalid Status')

class ProjectViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.project = Project.objects.create(
            project_name='Initial Project',
            start_date=timezone.now().date(),
            status='Active'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('project_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/project/list.html')
        self.assertContains(response, 'Add New Project')
        
    def test_table_partial_view(self):
        response = self.client.get(reverse('project_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/project/_project_table.html')
        self.assertContains(response, 'Initial Project')
        self.assertTrue('projects' in response.context)
        self.assertEqual(response.context['projects'].count(), Project.objects.count())

    def test_create_view_get(self):
        response = self.client.get(reverse('project_add'), HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/project/_project_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        initial_project_count = Project.objects.count()
        data = {
            'project_name': 'New Project Title',
            'start_date': '2023-01-01',
            'end_date': '',
            'status': 'Active',
        }
        response = self.client.post(reverse('project_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')
        self.assertEqual(Project.objects.count(), initial_project_count + 1)
        self.assertTrue(Project.objects.filter(project_name='New Project Title').exists())
        
    def test_create_view_post_invalid(self):
        initial_project_count = Project.objects.count()
        data = {
            'project_name': '', # Invalid: missing required field
            'start_date': '2023-01-01',
            'status': 'Active',
        }
        response = self.client.post(reverse('project_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form render with errors
        self.assertTemplateUsed(response, 'reports/project/_project_form.html')
        self.assertTrue('form' in response.context)
        self.assertFormError(response, 'form', 'project_name', ['This field is required.'])
        self.assertEqual(Project.objects.count(), initial_project_count) # No new object created

    def test_update_view_get(self):
        response = self.client.get(reverse('project_edit', args=[self.project.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/project/_project_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.project)
        
    def test_update_view_post_success(self):
        updated_name = 'Updated Project Name'
        data = {
            'project_name': updated_name,
            'start_date': self.project.start_date.isoformat(),
            'end_date': '',
            'status': 'On Hold',
        }
        response = self.client.post(reverse('project_edit', args=[self.project.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')
        self.project.refresh_from_db()
        self.assertEqual(self.project.project_name, updated_name)
        self.assertEqual(self.project.status, 'On Hold')

    def test_update_view_post_invalid(self):
        original_name = self.project.project_name
        data = {
            'project_name': '', # Invalid: missing required field
            'start_date': self.project.start_date.isoformat(),
            'status': 'Active',
        }
        response = self.client.post(reverse('project_edit', args=[self.project.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/project/_project_form.html')
        self.assertFormError(response, 'form', 'project_name', ['This field is required.'])
        self.project.refresh_from_db()
        self.assertEqual(self.project.project_name, original_name) # Name should not be updated

    def test_delete_view_get(self):
        response = self.client.get(reverse('project_delete', args=[self.project.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/project/_project_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.project)
        
    def test_delete_view_post_success(self):
        project_to_delete = Project.objects.create(
            project_name='Temp Project for Delete',
            start_date=timezone.now().date(),
            status='Active'
        )
        initial_project_count = Project.objects.count()
        response = self.client.post(reverse('project_delete', args=[project_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')
        self.assertEqual(Project.objects.count(), initial_project_count - 1)
        self.assertFalse(Project.objects.filter(pk=project_to_delete.pk).exists())

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for Dynamic Interactions:**
    *   The "Add New Project", "Edit", and "Delete" buttons in `list.html` and `_project_table.html` use `hx-get` to fetch the form/confirmation partials into `#modalContent`.
    *   The forms in `_project_form.html` and `_project_confirm_delete.html` use `hx-post` for submission.
    *   `hx-swap="none"` and `hx-on::after-request` on forms handle closing the modal after a successful submission (status 204).
    *   `HX-Trigger` headers (`refreshProjectList`) are sent by the views upon successful CRUD operations, causing the `#projectTable-container` to reload its content via `hx-get="{% url 'project_table' %}"` on `refreshProjectList` event.

*   **Alpine.js for UI State Management (Modal):**
    *   The modal in `list.html` uses `_=` (Hyperscript, compatible with Alpine.js) for `on click add .is-active to #modal` to show the modal and `on click if event.target.id == 'modal' remove .is-active from me` to close it when clicking outside.
    *   The "Cancel" buttons in form/delete partials also use `_=` to remove the `.is-active` class from `#modal`.

*   **DataTables for List Views:**
    *   The `_project_table.html` partial contains the `<table>` element with `id="projectTable"`.
    *   A `<script>` block within this partial initializes DataTables on `$(document).ready()`. This ensures DataTables is re-initialized every time the partial is loaded via HTMX, which is crucial for dynamic table updates. The initialization also checks for table content to prevent errors with empty tables.

*   **No Full Page Reloads:** All CRUD operations and table refreshes are designed to occur without full page reloads, using HTMX for seamless user experience.

## Final Notes

*   This plan provides a complete, runnable Django solution for a hypothetical "Project Summary" module, inferred from the minimal ASP.NET code. In a real migration, the specifics of the database schema, UI controls, and business logic would be extracted directly from the existing ASP.NET application.
*   **Django Project Setup:** This plan assumes a Django project is already set up, and the `reports` app has been added to `INSTALLED_APPS` in `settings.py`. Database connection details would also be configured there.
*   **Tailwind CSS:** It's assumed Tailwind CSS (and its PostCSS processing) is correctly set up in the Django project to apply the utility classes used in the templates.
*   **jQuery & DataTables CDN:** The `core/base.html` template (not included) must contain CDN links for jQuery and DataTables (JS and CSS) for the `_project_table.html` script to function.
*   **HTMX & Alpine.js CDN:** The `core/base.html` template must also include the CDN links for HTMX and Alpine.js.
*   **Error Handling:** While `messages.success` is used, more robust error handling for HTMX (e.g., displaying form errors within the modal, or global error messages) would be needed in a production application.
*   **Security:** This example focuses on functional conversion. Production applications would require proper authentication, authorization, and other security measures.