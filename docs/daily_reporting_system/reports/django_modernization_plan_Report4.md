This comprehensive modernization plan outlines the automated conversion of your legacy ASP.NET application to a modern Django-based system. We will leverage AI-assisted tools to systematically transform your existing functionality into a robust, maintainable, and scalable Django solution, focusing on business benefits and clear, actionable steps.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Value:** This step ensures a seamless connection to your existing data, preserving historical information and minimizing data migration complexities. By directly mapping to your current database, we reduce risks and accelerate the transition.

**Instructions:**

From the ASP.NET `SqlDataSource` with `SelectCommand="SELECT * FROM [DRTS_VENDOR_PLAN]"`, we identify:

*   **Table Name:** `DRTS_VENDOR_PLAN`
*   **Columns:**
    *   `idwono`
    *   `idsr`
    *   `idfxn`
    *   `idnpm`
    *   `idpln`
    *   `idfcl`
    *   `idpri`
    *   `idwef`
    *   `idwl`
    *   `idnpr`
    *   `idnap`
    *   `idpmp`
    *   `idbpt`
    *   `idpbp`
    *   `idnpc`
    *   `idnprap`
    *   `ID` (Identified as the primary key from the data fields and common naming conventions for database IDs.)

### Step 2: Identify Backend Functionality

**Business Value:** Understanding existing functionality allows us to replicate and enhance it in the new system. Even for a simple display, this analysis helps ensure no critical business rules are missed during the automated conversion.

**Instructions:**

The provided ASP.NET code primarily performs a "Read" operation, displaying all records from the `DRTS_VENDOR_PLAN` table using a `GridView`. There is no explicit C# code-behind logic for custom data manipulation, creation, update, or deletion.

For a complete modernization and to provide a fully functional administrative interface, our Django plan will automate the generation of **full CRUD (Create, Read, Update, Delete) capabilities** for the `DRTS_VENDOR_PLAN` table, even though only 'Read' is explicitly shown in the original ASP.NET code. This proactive approach ensures the new system is ready for future enhancements and data management.

### Step 3: Infer UI Components

**Business Value:** By mapping ASP.NET UI controls to modern web components, we ensure that the user experience is intuitive and familiar, while gaining the benefits of modern, responsive design and dynamic interactions without full page reloads.

**Instructions:**

The primary UI component is an `asp:gridview` which is used for displaying tabular data.

*   **`asp:gridview`:** This will be converted into a dynamic HTML table managed by **DataTables.js** for client-side sorting, searching, and pagination.
*   **No user input controls (TextBox, DropDownList, Button) are explicitly shown for data entry.** However, as part of the modernization to support full CRUD, we will automatically generate forms using Django's `ModelForm` with appropriate input fields for each column, styled with **Tailwind CSS**.
*   **Client-side interactivity:** There's no custom JavaScript in the original code. We will implement all dynamic interactions (e.g., loading forms in modals, refreshing lists) using **HTMX** for efficient partial page updates and **Alpine.js** for simple UI state management, eliminating the need for complex JavaScript frameworks.

---

### Step 4: Generate Django Code

**Business Value:** This step is where the core transformation happens. By generating standardized, high-quality Django code for models, forms, views, templates, and URLs, we establish a robust foundation that is easy to maintain, extend, and understand, even for non-technical stakeholders. Automation ensures consistency and reduces development time and errors.

#### 4.1 Models

**Business Value:** The Django model acts as the single source of truth for your data structure, directly mapping to your existing database. By centralizing business logic within the model ("Fat Model" approach), we create a clean, maintainable, and highly testable codebase, ensuring data integrity and consistency across your application.

```python
# dailyreporting/models.py
from django.db import models

class VendorPlan(models.Model):
    # The 'ID' column is explicitly defined as the primary key in the ASP.NET GridView
    # Assuming it's an integer ID based on typical database practices.
    id = models.IntegerField(db_column='ID', primary_key=True, verbose_name="Plan ID")
    idwono = models.CharField(db_column='idwono', max_length=255, blank=True, null=True, verbose_name="Work Order No.")
    idsr = models.CharField(db_column='idsr', max_length=255, blank=True, null=True, verbose_name="Source Ref.")
    idfxn = models.CharField(db_column='idfxn', max_length=255, blank=True, null=True, verbose_name="Function")
    idnpm = models.CharField(db_column='idnpm', max_length=255, blank=True, null=True, verbose_name="NPM")
    idpln = models.CharField(db_column='idpln', max_length=255, blank=True, null=True, verbose_name="Plan")
    idfcl = models.CharField(db_column='idfcl', max_length=255, blank=True, null=True, verbose_name="Facility")
    idpri = models.CharField(db_column='idpri', max_length=255, blank=True, null=True, verbose_name="Priority")
    idwef = models.CharField(db_column='idwef', max_length=255, blank=True, null=True, verbose_name="W.E.F.")
    idwl = models.CharField(db_column='idwl', max_length=255, blank=True, null=True, verbose_name="Work Load")
    idnpr = models.CharField(db_column='idnpr', max_length=255, blank=True, null=True, verbose_name="NPR")
    idnap = models.CharField(db_column='idnap', max_length=255, blank=True, null=True, verbose_name="NAP")
    idpmp = models.CharField(db_column='idpmp', max_length=255, blank=True, null=True, verbose_name="PMP")
    idbpt = models.CharField(db_column='idbpt', max_length=255, blank=True, null=True, verbose_name="BPT")
    idpbp = models.CharField(db_column='idpbp', max_length=255, blank=True, null=True, verbose_name="PBP")
    idnpc = models.CharField(db_column='idnpc', max_length=255, blank=True, null=True, verbose_name="NPC")
    idnprap = models.CharField(db_column='idnprap', max_length=255, blank=True, null=True, verbose_name="NPRAP")

    class Meta:
        managed = False  # Important: tells Django not to manage table creation/deletion
        db_table = 'DRTS_VENDOR_PLAN'
        verbose_name = 'Vendor Plan'
        verbose_name_plural = 'Vendor Plans'

    def __str__(self):
        return f"Vendor Plan {self.id} - {self.idwono or 'No WO'}"
        
    # --- Fat Model: Business Logic Methods ---
    # Example: A method to derive a combined status or process some fields.
    # In a real scenario, this would contain logic from your ASP.NET business layer.
    def get_plan_status(self):
        """
        Derives a simple plan status based on 'idpri' (priority) and 'idwl' (work load).
        This is a placeholder for actual business logic from your ASP.NET application.
        """
        if self.idpri and self.idpri.lower() == 'high':
            return 'Urgent'
        elif self.idwl and self.idwl.lower() == 'completed':
            return 'Completed'
        else:
            return 'Pending'

    def update_plan_details(self, new_plan_data):
        """
        Example method to encapsulate update logic.
        This would handle validation and business rules before saving.
        """
        # Example: validate new_plan_data before updating fields
        if 'idpln' in new_plan_data and len(new_plan_data['idpln']) < 3:
            raise ValueError("Plan description too short.")
        
        for field, value in new_plan_data.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.save()
        return True
```

#### 4.2 Forms

**Business Value:** Django forms simplify user input management, ensuring data is validated and correctly formatted before being saved to the database. By using ModelForms, we automate form creation, align closely with the data model, and apply consistent styling, significantly reducing development effort.

```python
# dailyreporting/forms.py
from django import forms
from .models import VendorPlan

class VendorPlanForm(forms.ModelForm):
    class Meta:
        model = VendorPlan
        # 'id' is typically auto-generated or managed by the database,
        # so it's often not included in forms for new object creation.
        # It's used in the URL for updates.
        fields = [
            'idwono', 'idsr', 'idfxn', 'idnpm', 'idpln', 'idfcl',
            'idpri', 'idwef', 'idwl', 'idnpr', 'idnap', 'idpmp',
            'idbpt', 'idpbp', 'idnpc', 'idnprap'
        ]
        widgets = {
            'idwono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idsr': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idfxn': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idnpm': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idpln': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idfcl': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idpri': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idwef': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idwl': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idnpr': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idnap': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idpmp': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idbpt': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idpbp': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idnpc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idnprap': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_idwono(self):
        """
        Example custom validation: Ensure idwono is not empty if it's considered mandatory.
        This mirrors potential validation logic found in ASP.NET validators or code-behind.
        """
        idwono = self.cleaned_data.get('idwono')
        if not idwono:
            raise forms.ValidationError("Work Order Number is required.")
        return idwono
```

#### 4.3 Views

**Business Value:** Django's Class-Based Views (CBVs) provide a structured and efficient way to handle common web requests like displaying lists or managing data. By keeping views "thin" and delegating complex business logic to models, we ensure that the application remains modular, easy to debug, and highly scalable. HTMX integration ensures a highly responsive user experience.

```python
# dailyreporting/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import VendorPlan
from .forms import VendorPlanForm

# Main view for displaying the list page
class VendorPlanListView(ListView):
    model = VendorPlan
    template_name = 'dailyreporting/vendorplan/list.html'
    context_object_name = 'vendorplans' # Renamed for clarity and consistency

    def get_queryset(self):
        # Example of applying business logic from model in a view (if needed)
        # return VendorPlan.objects.all().order_by('idwono')
        return VendorPlan.objects.all()

# View to render only the table content, used by HTMX
class VendorPlanTablePartialView(ListView):
    model = VendorPlan
    template_name = 'dailyreporting/vendorplan/_vendorplan_table.html'
    context_object_name = 'vendorplans'

    def get_queryset(self):
        return VendorPlan.objects.all()

class VendorPlanCreateView(CreateView):
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailyreporting/vendorplan/_vendorplan_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('vendorplan_list') # Redirect to list view on success

    def form_valid(self, form):
        # Business logic can be called from the model here if needed,
        # e.g., form.instance.process_new_plan()
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList' # Event to refresh the table
                }
            )
        return response

class VendorPlanUpdateView(UpdateView):
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailyreporting/vendorplan/_vendorplan_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('vendorplan_list') # Redirect to list view on success

    def form_valid(self, form):
        # Example of calling fat model logic before saving
        # form.instance.update_plan_details(form.cleaned_data)
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList'
                }
            )
        return response

class VendorPlanDeleteView(DeleteView):
    model = VendorPlan
    template_name = 'dailyreporting/vendorplan/_vendorplan_confirm_delete.html' # Use partial for HTMX modal
    success_url = reverse_lazy('vendorplan_list') # Redirect to list view on success

    def delete(self, request, *args, **kwargs):
        # Example of calling fat model logic before deleting
        # self.object.archive_before_delete() # If you have an archive logic
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Vendor Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList'
                }
            )
        return response
```

#### 4.4 Templates

**Business Value:** Templates transform raw data into user-friendly web pages. By using modern techniques like HTMX for partial updates and DataTables for interactive lists, we provide a highly dynamic and performant user interface, improving user satisfaction and operational efficiency without complex JavaScript. DRY principles ensure consistency and easier maintenance.

**File: `dailyreporting/vendorplan/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Vendor Plans</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'vendorplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Vendor Plan
        </button>
    </div>
    
    <div id="vendorplanTable-container"
         hx-trigger="load, refreshVendorPlanList from:body"
         hx-get="{% url 'vendorplan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Vendor Plans...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalState', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });
</script>
{% endblock %}
```

**File: `dailyreporting/vendorplan/_vendorplan_table.html`**
```html
<div class="overflow-x-auto rounded-lg shadow overflow-y-auto relative" style="max-height: 70vh;">
    <table id="vendorplanTable" class="min-w-full bg-white table-auto">
        <thead class="bg-gray-50 sticky top-0">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order No.</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source Ref.</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Function</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in vendorplans %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.idwono }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.idsr }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.idfxn }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.idpln }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.idpri }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'vendorplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'vendorplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables
    $(document).ready(function() {
        $('#vendorplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 6] }, // Disable sorting for SN and Actions columns
                { "searchable": false, "targets": [0, 6] } // Disable searching for SN and Actions columns
            ]
        });
    });
</script>
```

**File: `dailyreporting/vendorplan/_vendorplan_form.html`**
```html
<div class="p-6" x-data="{}" x-init="$nextTick(() => document.querySelector('#modal input, #modal select, #modal textarea')?.focus())">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Vendor Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.querySelector('#modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `dailyreporting/vendorplan/_vendorplan_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete Vendor Plan ID: <strong>{{ vendorplan.idwono }} ({{ vendorplan.id }})</strong>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'vendorplan_delete' vendorplan.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.querySelector('#modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Business Value:** A clean and predictable URL structure makes your application easier to navigate for users and more maintainable for developers. By defining clear routes for each operation, we ensure that your application is well-organized and scalable.

```python
# dailyreporting/urls.py
from django.urls import path
from .views import VendorPlanListView, VendorPlanCreateView, VendorPlanUpdateView, VendorPlanDeleteView, VendorPlanTablePartialView

urlpatterns = [
    # Main list view (full page load, but table content loaded via HTMX)
    path('vendorplans/', VendorPlanListView.as_view(), name='vendorplan_list'),
    
    # HTMX endpoint for just the table content
    path('vendorplans/table/', VendorPlanTablePartialView.as_view(), name='vendorplan_table'),

    # CRUD operations, primarily loaded via HTMX into a modal
    path('vendorplans/add/', VendorPlanCreateView.as_view(), name='vendorplan_add'),
    path('vendorplans/edit/<int:pk>/', VendorPlanUpdateView.as_view(), name='vendorplan_edit'),
    path('vendorplans/delete/<int:pk>/', VendorPlanDeleteView.as_view(), name='vendorplan_delete'),
]
```

#### 4.6 Tests

**Business Value:** Comprehensive automated tests are crucial for ensuring the reliability and correctness of your modernized application. They act as a safety net, detecting regressions and validating business logic, which significantly reduces the risk of errors in production and lowers long-term maintenance costs. Achieving high test coverage is a cornerstone of a robust software system.

```python       
# dailyreporting/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import VendorPlan
from .forms import VendorPlanForm

class VendorPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        VendorPlan.objects.create(
            id=1, # Explicitly setting ID as it's the primary key from DB
            idwono='WO-12345',
            idsr='SR-A',
            idfxn='Func1',
            idnpm='NPM1',
            idpln='Plan-ABC',
            idfcl='Fac1',
            idpri='High',
            idwef='2023-01-01',
            idwl='Active',
            idnpr='NPR1',
            idnap='NAP1',
            idpmp='PMP1',
            idbpt='BPT1',
            idpbp='PBP1',
            idnpc='NPC1',
            idnprap='NPRAP1'
        )
        VendorPlan.objects.create(
            id=2,
            idwono='WO-67890',
            idsr='SR-B',
            idfxn='Func2',
            idnpm='NPM2',
            idpln='Plan-XYZ',
            idfcl='Fac2',
            idpri='Low',
            idwef='2023-02-01',
            idwl='Completed',
            idnpr='NPR2',
            idnap='NAP2',
            idpmp='PMP2',
            idbpt='BPT2',
            idpbp='PBP2',
            idnpc='NPC2',
            idnprap='NPRAP2'
        )
  
    def test_vendorplan_creation(self):
        obj = VendorPlan.objects.get(id=1)
        self.assertEqual(obj.idwono, 'WO-12345')
        self.assertEqual(obj.idpri, 'High')
        self.assertEqual(obj.id, 1) # Check if PK is correctly set

    def test_idwono_label(self):
        obj = VendorPlan.objects.get(id=1)
        field_label = obj._meta.get_field('idwono').verbose_name
        self.assertEqual(field_label, 'Work Order No.')
    
    def test_id_verbose_name(self):
        obj = VendorPlan.objects.get(id=1)
        field_label = obj._meta.get_field('id').verbose_name
        self.assertEqual(field_label, 'Plan ID')

    def test_str_representation(self):
        obj = VendorPlan.objects.get(id=1)
        self.assertEqual(str(obj), 'Vendor Plan 1 - WO-12345')
        
    def test_get_plan_status_urgent(self):
        obj = VendorPlan.objects.get(id=1) # idpri='High'
        self.assertEqual(obj.get_plan_status(), 'Urgent')

    def test_get_plan_status_completed(self):
        obj = VendorPlan.objects.get(id=2) # idwl='Completed'
        self.assertEqual(obj.get_plan_status(), 'Completed')

    def test_update_plan_details_success(self):
        obj = VendorPlan.objects.get(id=1)
        new_data = {'idpln': 'Updated Plan Description', 'idpri': 'Medium'}
        obj.update_plan_details(new_data)
        obj.refresh_from_db()
        self.assertEqual(obj.idpln, 'Updated Plan Description')
        self.assertEqual(obj.idpri, 'Medium')

    def test_update_plan_details_validation_error(self):
        obj = VendorPlan.objects.get(id=1)
        new_data = {'idpln': 'ab'} # Too short
        with self.assertRaises(ValueError) as cm:
            obj.update_plan_details(new_data)
        self.assertIn("Plan description too short.", str(cm.exception))


class VendorPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        VendorPlan.objects.create(
            id=10,
            idwono='WO-TEST-1',
            idsr='SR-TEST-1',
            idfxn='FXN-TEST-1',
            idnpm='NPM-TEST-1',
            idpln='PLN-TEST-1',
            idfcl='FCL-TEST-1',
            idpri='PRI-TEST-1',
            idwef='WEF-TEST-1',
            idwl='WL-TEST-1',
            idnpr='NPR-TEST-1',
            idnap='NAP-TEST-1',
            idpmp='PMP-TEST-1',
            idbpt='BPT-TEST-1',
            idpbp='PBP-TEST-1',
            idnpc='NPC-TEST-1',
            idnprap='NPRAP-TEST-1'
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('vendorplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/vendorplan/list.html')
        self.assertTrue('vendorplans' in response.context)
        # Verify initial loading message for HTMX container
        self.assertContains(response, 'Loading Vendor Plans...')
        
    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendorplan_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/vendorplan/_vendorplan_table.html')
        self.assertTrue('vendorplans' in response.context)
        self.assertContains(response, 'WO-TEST-1') # Check if data is present
        self.assertContains(response, '<table id="vendorplanTable"') # Ensure it's the table partial

    def test_create_view_get(self):
        response = self.client.get(reverse('vendorplan_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/vendorplan/_vendorplan_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Vendor Plan') # Check modal title

    def test_create_view_post_success(self):
        data = {
            'idwono': 'WO-NEW', 'idsr': 'SR-NEW', 'idfxn': 'FXN-NEW', 'idnpm': 'NPM-NEW',
            'idpln': 'PLN-NEW', 'idfcl': 'FCL-NEW', 'idpri': 'PRI-NEW', 'idwef': 'WEF-NEW',
            'idwl': 'WL-NEW', 'idnpr': 'NPR-NEW', 'idnap': 'NAP-NEW', 'idpmp': 'PMP-NEW',
            'idbpt': 'BPT-NEW', 'idpbp': 'PBP-NEW', 'idnpc': 'NPC-NEW', 'idnprap': 'NPRAP-NEW'
        }
        # Simulate HTMX request
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')
        
        # Verify object was created (ID is auto-assigned by Django's ORM for new objects)
        # We need to find the object by a unique field as the primary key 'id' isn't managed by form
        self.assertTrue(VendorPlan.objects.filter(idwono='WO-NEW').exists())
        self.assertEqual(VendorPlan.objects.filter(idwono='WO-NEW').count(), 1)


    def test_create_view_post_invalid_data(self):
        data = {
            'idwono': '', # Invalid: missing required field as per form clean method
            'idsr': 'SR-NEW',
            # Other fields can be omitted if not required
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'dailyreporting/vendorplan/_vendorplan_form.html')
        self.assertContains(response, 'Work Order Number is required.')
        self.assertFalse(VendorPlan.objects.filter(idwono='').exists()) # Ensure not created

    def test_update_view_get(self):
        obj = VendorPlan.objects.get(id=10)
        response = self.client.get(reverse('vendorplan_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/vendorplan/_vendorplan_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Vendor Plan')
        self.assertContains(response, 'WO-TEST-1') # Check if existing data is pre-filled

    def test_update_view_post_success(self):
        obj = VendorPlan.objects.get(id=10)
        data = {
            'idwono': 'WO-UPDATED', 'idsr': 'SR-UPDATED', 'idfxn': 'FXN-TEST-1', 'idnpm': 'NPM-TEST-1',
            'idpln': 'PLN-TEST-1', 'idfcl': 'FCL-TEST-1', 'idpri': 'PRI-TEST-1', 'idwef': 'WEF-TEST-1',
            'idwl': 'WL-TEST-1', 'idnpr': 'NPR-TEST-1', 'idnap': 'NAP-TEST-1', 'idpmp': 'PMP-TEST-1',
            'idbpt': 'BPT-TEST-1', 'idpbp': 'PBP-TEST-1', 'idnpc': 'NPC-TEST-1', 'idnprap': 'NPRAP-TEST-1'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_edit', args=[obj.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')
        
        obj.refresh_from_db()
        self.assertEqual(obj.idwono, 'WO-UPDATED')

    def test_delete_view_get(self):
        obj = VendorPlan.objects.get(id=10)
        response = self.client.get(reverse('vendorplan_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/vendorplan/_vendorplan_confirm_delete.html')
        self.assertTrue('vendorplan' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'WO-TEST-1')

    def test_delete_view_post_success(self):
        obj = VendorPlan.objects.get(id=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_delete', args=[obj.id]), **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')
        
        self.assertFalse(VendorPlan.objects.filter(id=10).exists())
```

---

### Step 5: HTMX and Alpine.js Integration

**Business Value:** This integration transforms a static page into a dynamic, responsive application. Users experience faster interactions, smoother transitions, and an overall more modern feel, leading to increased productivity and satisfaction. It significantly improves the user experience without the complexity and overhead of traditional Single Page Application (SPA) frameworks.

**Instructions:**

*   **List View with DataTables and HTMX Refresh:**
    *   The `list.html` template fetches the `_vendorplan_table.html` partial using `hx-get="{% url 'vendorplan_table' %}"` on `load` and on a custom `refreshVendorPlanList` event. This ensures the table is always up-to-date without a full page refresh.
    *   DataTables is initialized on the `_vendorplan_table.html` partial, applying client-side features like search, sort, and pagination.
*   **Modals for CRUD Operations:**
    *   "Add New", "Edit", and "Delete" buttons in `_vendorplan_table.html` use `hx-get` to fetch the respective form (`_vendorplan_form.html` or `_vendorplan_confirm_delete.html`) into a modal container (`#modalContent`).
    *   Alpine.js (with the `_` syntax from htmx.org/extensions/alpine-morph) is used to control the visibility of the modal (`on click add .is-active to #modal`) and to close it when clicking outside or pressing "Cancel".
*   **Form Submission with HTMX:**
    *   The forms within the modal (`_vendorplan_form.html`, `_vendorplan_confirm_delete.html`) use `hx-post` to submit data asynchronously.
    *   Upon successful submission (Django returns 204 No Content), the `HX-Trigger` header in the Django view (`refreshVendorPlanList`) signals the client-side to re-fetch and update the table, ensuring the list always reflects the latest data. The `hx-on::after-request` event on the form closes the modal.
*   **No Custom JavaScript:** All dynamic interactions are managed via HTMX attributes and Alpine.js directives directly in the HTML, eliminating the need for separate JavaScript files for these features.
*   **DRY Template Inheritance:** `base.html` is extended in `list.html`, and `_vendorplan_table.html`, `_vendorplan_form.html`, `_vendorplan_confirm_delete.html` are partials, promoting code reuse and modularity.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET `Report4.aspx` to a modern Django application. By leveraging AI-assisted automation, we can rapidly generate the necessary codebase, ensuring high quality, maintainability, and a significantly improved user experience. This approach minimizes manual effort, reduces the risk of human error, and delivers a future-proof solution aligned with modern web development best practices.