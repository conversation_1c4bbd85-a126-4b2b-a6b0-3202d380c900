## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code for `VENDOR_PLAN_Delete.aspx` and its code-behind `VENDOR_PLAN_Delete.aspx.cs` is largely empty, without explicit UI controls or business logic. However, the file name strongly implies a "delete" operation for a "Vendor Plan" entity. Based on this, we will infer the necessary database structure and functionality to build a complete Django module for managing Vendor Plans, including the delete functionality.

Our approach will focus on creating a `dailymaster` Django application, which aligns with the original `Module_DailyReportingSystem_Masters` structure. We will implement standard CRUD (Create, Read, Update, Delete) operations for a `VendorPlan` model.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the ASP.NET code is empty, we infer the following based on the file name `VENDOR_PLAN_Delete`:

- **Table Name:** `tbl_vendor_plan` (A common ASP.NET database naming convention prefixing tables with `tbl_`).
- **Columns (inferred for a typical `Vendor Plan` entity):**
    - `id` (Primary Key, integer)
    - `plan_name` (String, e.g., "Standard Plan")
    - `vendor_id` (Integer, referencing a vendor)
    - `description` (Text, optional)
    - `is_active` (Boolean, for soft deletion or status)
    - `created_at` (Datetime)
    - `updated_at` (Datetime)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Based on the `VENDOR_PLAN_Delete` file name, the primary functionality is **Delete**. To make this a comprehensive and useful module, we will implement the full set of CRUD operations:

- **Create:** Ability to add new vendor plans.
- **Read:** Display a list of all vendor plans.
- **Update:** Modify existing vendor plan details.
- **Delete:** Remove a vendor plan from the system.

No validation logic was explicitly present in the ASP.NET example, but we will add standard validation in the Django forms.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Since no UI components were provided in the ASP.NET file, we will infer them based on standard CRUD requirements:

- **List View:** A table (like a `GridView`) to display all `VendorPlan` records. This will be powered by DataTables for client-side functionality.
- **Form (Add/Edit):** Input fields (like `TextBox` for plan name, `DropDown` for vendor ID) to create or update a `VendorPlan`.
- **Action Buttons:** Buttons or links (like `Button` or `LinkButton`) for "Add New", "Edit", and "Delete" for each row.
- **Confirmation Dialog:** A modal or partial view to confirm deletion.

All dynamic interactions (form loading, submission, deletion confirmation) will be handled by HTMX, with Alpine.js for modal state management.

## Step 4: Generate Django Code

We will create a new Django application named `dailymaster`.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The model will be named `VendorPlan` and mapped to `tbl_vendor_plan`.

**File: `dailymaster/models.py`**

```python
from django.db import models
from django.utils import timezone

class VendorPlan(models.Model):
    # It's good practice to explicitly define primary key if not 'id'
    # For managed = False, you often rely on the existing PK
    # plan_id = models.AutoField(primary_key=True) # If PK is not named 'id'

    plan_name = models.CharField(
        max_length=255,
        db_column='plan_name',
        verbose_name='Plan Name',
        help_text='Unique name for the vendor plan'
    )
    vendor_id = models.IntegerField(
        db_column='vendor_id',
        verbose_name='Vendor ID',
        help_text='Identifier for the associated vendor'
    )
    description = models.TextField(
        blank=True,
        null=True,
        db_column='description',
        verbose_name='Description',
        help_text='Detailed description of the plan'
    )
    is_active = models.BooleanField(
        default=True,
        db_column='is_active',
        verbose_name='Is Active',
        help_text='Indicates if the plan is currently active'
    )
    created_at = models.DateTimeField(
        auto_now_add=True, # Automatically sets the field to now when the object is first created.
        db_column='created_at',
        verbose_name='Created At'
    )
    updated_at = models.DateTimeField(
        auto_now=True,     # Automatically updates the field to now every time the object is saved.
        db_column='updated_at',
        verbose_name='Updated At'
    )

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tbl_vendor_plan' # Name of the existing database table
        verbose_name = 'Vendor Plan'
        verbose_name_plural = 'Vendor Plans'
        # Consider unique_together for specific combinations if needed
        # unique_together = (('plan_name', 'vendor_id'),)


    def __str__(self):
        return self.plan_name

    # Business logic methods (Fat Model)
    def activate_plan(self):
        """Activates the vendor plan."""
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False

    def deactivate_plan(self):
        """Deactivates the vendor plan."""
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False

    def get_display_status(self):
        """Returns a user-friendly status string."""
        return "Active" if self.is_active else "Inactive"

    @classmethod
    def get_active_plans(cls):
        """Returns all active vendor plans."""
        return cls.objects.filter(is_active=True)

    @classmethod
    def get_plan_by_name(cls, name):
        """Retrieves a plan by its name."""
        try:
            return cls.objects.get(plan_name__iexact=name)
        except cls.DoesNotExist:
            return None
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for `VendorPlan`, including all editable fields and appropriate widgets with Tailwind CSS classes.

**File: `dailymaster/forms.py`**

```python
from django import forms
from .models import VendorPlan

class VendorPlanForm(forms.ModelForm):
    class Meta:
        model = VendorPlan
        fields = ['plan_name', 'vendor_id', 'description', 'is_active']
        widgets = {
            'plan_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Enterprise Plan'
            }),
            'vendor_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 101'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24',
                'placeholder': 'e.g., Comprehensive plan including all features'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded'
            }),
        }
        labels = {
            'plan_name': 'Plan Name',
            'vendor_id': 'Vendor ID',
            'description': 'Description',
            'is_active': 'Active',
        }

    # Add custom validation methods here if needed
    def clean_plan_name(self):
        plan_name = self.cleaned_data['plan_name']
        # Example: Ensure plan name is unique (case-insensitive)
        qs = VendorPlan.objects.filter(plan_name__iexact=plan_name)
        if self.instance.pk: # If it's an update, exclude the current instance
            qs = qs.exclude(pk=self.instance.pk)
        if qs.exists():
            raise forms.ValidationError("A vendor plan with this name already exists.")
        return plan_name
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are kept thin, delegating business logic to the `VendorPlan` model where appropriate. We'll include a `TablePartialView` to serve the DataTables content via HTMX.

**File: `dailymaster/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import VendorPlan
from .forms import VendorPlanForm

# Main view to display the list page, which then loads the table via HTMX
class VendorPlanListView(ListView):
    model = VendorPlan
    template_name = 'dailymaster/vendorplan/list.html'
    context_object_name = 'vendorplans' # Though actual data loading is in TablePartialView

# HTMX partial view to load the DataTables content
class VendorPlanTablePartialView(ListView):
    model = VendorPlan
    template_name = 'dailymaster/vendorplan/_vendorplan_table.html'
    context_object_name = 'vendorplans'

    def get_queryset(self):
        # Example of ordering; DataTables handles client-side sorting/filtering
        return VendorPlan.objects.all().order_by('plan_name')


class VendorPlanCreateView(CreateView):
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailymaster/vendorplan/form.html'
    success_url = reverse_lazy('vendorplan_list') # Redirect for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response to avoid page reload,
            # and trigger a client-side event to refresh the list.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshVendorPlanList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return response
        return response


class VendorPlanUpdateView(UpdateView):
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailymaster/vendorplan/form.html'
    success_url = reverse_lazy('vendorplan_list') # Redirect for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshVendorPlanList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response


class VendorPlanDeleteView(DeleteView):
    model = VendorPlan
    template_name = 'dailymaster/vendorplan/confirm_delete.html'
    success_url = reverse_lazy('vendorplan_list') # Redirect for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        vendor_plan = self.get_object()
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Vendor Plan "{vendor_plan.plan_name}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshVendorPlanList":true, "closeModal":true}'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will leverage DRY principles, HTMX for dynamic content, and Alpine.js for UI state.

**File: `dailymaster/templates/dailymaster/vendorplan/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Vendor Plans Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'vendorplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Vendor Plan
        </button>
    </div>

    <!-- Container for DataTable, loaded via HTMX -->
    <div id="vendorplanTable-container"
         hx-trigger="load, refreshVendorPlanList from:body"
         hx-get="{% url 'vendorplan_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-xl rounded-lg overflow-hidden">
        <!-- Initial loading indicator -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Vendor Plans...</p>
        </div>
    </div>

    <!-- Universal Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center z-50 hidden"
         _="on closeModal remove .is-active from me
            on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-lg w-full transform transition-all scale-95 duration-200">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });
</script>
{% endblock %}
```

**File: `dailymaster/templates/dailymaster/vendorplan/_vendorplan_table.html`**

```html
<div class="p-6">
    <table id="vendorplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in vendorplans %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.plan_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.vendor_id }}</td>
                <td class="py-3 px-4 text-sm text-gray-500">{{ obj.description|default:"N/A"|truncatechars:50 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ obj.get_display_status }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'vendorplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                        hx-get="{% url 'vendorplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to avoid re-initialization issues with HTMX
        if ($.fn.DataTable.isDataTable('#vendorplanTable')) {
            $('#vendorplanTable').DataTable().destroy();
        }
        $('#vendorplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers",
            "responsive": true
        });
    });
</script>
```

**File: `dailymaster/templates/dailymaster/vendorplan/form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Vendor Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.body.dispatchEvent(new CustomEvent('closeModal')) }">
        {% csrf_token %}

        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-2 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click document.body.dispatchEvent(new CustomEvent('closeModal'))">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-save mr-2"></i> Save Changes
            </button>
        </div>
    </form>
</div>
```

**File: `dailymaster/templates/dailymaster/vendorplan/confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-red-700 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the vendor plan "<span class="font-bold">{{ object.plan_name }}</span>"? This action cannot be undone.</p>

    <form hx-post="{% url 'vendorplan_delete' object.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.body.dispatchEvent(new CustomEvent('closeModal')) }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click document.body.dispatchEvent(new CustomEvent('closeModal'))">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

This assumes the `dailymaster` app's `urls.py` is included in the project's main `urls.py`.

**File: `dailymaster/urls.py`**

```python
from django.urls import path
from .views import (
    VendorPlanListView,
    VendorPlanCreateView,
    VendorPlanUpdateView,
    VendorPlanDeleteView,
    VendorPlanTablePartialView, # New partial view for HTMX
)

urlpatterns = [
    # Main list page
    path('vendorplans/', VendorPlanListView.as_view(), name='vendorplan_list'),

    # HTMX endpoint for the table content
    path('vendorplans/table/', VendorPlanTablePartialView.as_view(), name='vendorplan_table'),

    # CRUD operations, loaded into modal via HTMX
    path('vendorplans/add/', VendorPlanCreateView.as_view(), name='vendorplan_add'),
    path('vendorplans/edit/<int:pk>/', VendorPlanUpdateView.as_view(), name='vendorplan_edit'),
    path('vendorplans/delete/<int:pk>/', VendorPlanDeleteView.as_view(), name='vendorplan_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for model methods and integration tests for all views.

**File: `dailymaster/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import VendorPlan
from django.contrib.messages import get_messages

class VendorPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.plan1 = VendorPlan.objects.create(
            plan_name='Basic Plan A',
            vendor_id=101,
            description='A simple basic plan.',
            is_active=True
        )
        cls.plan2 = VendorPlan.objects.create(
            plan_name='Premium Plan B',
            vendor_id=102,
            description='An advanced premium plan.',
            is_active=False
        )

    def test_vendor_plan_creation(self):
        """Test that VendorPlan objects are created correctly."""
        self.assertEqual(self.plan1.plan_name, 'Basic Plan A')
        self.assertEqual(self.plan1.vendor_id, 101)
        self.assertTrue(self.plan1.is_active)
        self.assertIsNotNone(self.plan1.created_at)
        self.assertIsNotNone(self.plan1.updated_at)

    def test_plan_name_label(self):
        """Test the verbose name for plan_name field."""
        field_label = self.plan1._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')

    def test_str_method(self):
        """Test the __str__ method returns the plan name."""
        self.assertEqual(str(self.plan1), 'Basic Plan A')

    def test_activate_plan_method(self):
        """Test activating a plan."""
        self.assertFalse(self.plan2.is_active)
        self.plan2.activate_plan()
        self.assertTrue(self.plan2.is_active)
        self.plan2.refresh_from_db() # Reload to ensure database state is reflected
        self.assertTrue(self.plan2.is_active)

    def test_deactivate_plan_method(self):
        """Test deactivating a plan."""
        self.assertTrue(self.plan1.is_active)
        self.plan1.deactivate_plan()
        self.assertFalse(self.plan1.is_active)
        self.plan1.refresh_from_db()
        self.assertFalse(self.plan1.is_active)

    def test_get_display_status_method(self):
        """Test get_display_status method."""
        self.assertEqual(self.plan1.get_display_status(), 'Active')
        self.assertEqual(self.plan2.get_display_status(), 'Inactive')

    def test_get_active_plans_classmethod(self):
        """Test get_active_plans class method."""
        active_plans = VendorPlan.get_active_plans()
        self.assertIn(self.plan1, active_plans)
        self.assertNotIn(self.plan2, active_plans)
        self.assertEqual(active_plans.count(), 1) # Initially only plan1 is active

    def test_get_plan_by_name_classmethod(self):
        """Test get_plan_by_name class method."""
        retrieved_plan = VendorPlan.get_plan_by_name('Basic Plan A')
        self.assertEqual(retrieved_plan, self.plan1)
        self.assertIsNone(VendorPlan.get_plan_by_name('NonExistent Plan'))


class VendorPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.plan = VendorPlan.objects.create(
            plan_name='Test Plan X',
            vendor_id=300,
            description='Description for test plan X',
            is_active=True
        )

    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()

    def test_list_view_get(self):
        """Test GET request to the vendor plan list view."""
        response = self.client.get(reverse('vendorplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailymaster/vendorplan/list.html')
        # The list.html doesn't directly contain context, it uses HTMX for the table
        # self.assertTrue('vendorplans' in response.context) # This won't be true for the main list view

    def test_table_partial_view_get(self):
        """Test GET request to the HTMX table partial view."""
        response = self.client.get(reverse('vendorplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailymaster/vendorplan/_vendorplan_table.html')
        self.assertIn(self.plan, response.context['vendorplans'])

    def test_create_view_get(self):
        """Test GET request to the create vendor plan view."""
        response = self.client.get(reverse('vendorplan_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailymaster/vendorplan/form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].instance.pk) # Should be an unbound form

    def test_create_view_post_success(self):
        """Test POST request to create a new vendor plan successfully."""
        data = {
            'plan_name': 'New Plan C',
            'vendor_id': 200,
            'description': 'Description for New Plan C',
            'is_active': True
        }
        response = self.client.post(reverse('vendorplan_add'), data)
        # Check for redirect after successful creation (non-HTMX)
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('vendorplan_list'))
        self.assertTrue(VendorPlan.objects.filter(plan_name='New Plan C').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Vendor Plan added successfully.')

    def test_create_view_post_success_htmx(self):
        """Test HTMX POST request to create a new vendor plan."""
        data = {
            'plan_name': 'HTMX Plan D',
            'vendor_id': 201,
            'description': 'Description for HTMX Plan D',
            'is_active': True
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(VendorPlan.objects.filter(plan_name='HTMX Plan D').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshVendorPlanList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data for creation."""
        data = {
            'plan_name': '',  # Invalid: empty plan name
            'vendor_id': 200,
            'description': 'Description for invalid plan',
            'is_active': True
        }
        response = self.client.post(reverse('vendorplan_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertTemplateUsed(response, 'dailymaster/vendorplan/form.html')
        self.assertFalse(VendorPlan.objects.filter(vendor_id=200).exists())
        self.assertIn('plan_name', response.context['form'].errors)

    def test_update_view_get(self):
        """Test GET request to the update vendor plan view."""
        response = self.client.get(reverse('vendorplan_edit', args=[self.plan.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailymaster/vendorplan/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.plan)

    def test_update_view_post_success(self):
        """Test POST request to update an existing vendor plan."""
        data = {
            'plan_name': 'Updated Plan X',
            'vendor_id': 301,
            'description': 'Updated description.',
            'is_active': False
        }
        response = self.client.post(reverse('vendorplan_edit', args=[self.plan.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('vendorplan_list'))
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, 'Updated Plan X')
        self.assertEqual(self.plan.vendor_id, 301)
        self.assertFalse(self.plan.is_active)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Vendor Plan updated successfully.')

    def test_update_view_post_success_htmx(self):
        """Test HTMX POST request to update an existing vendor plan."""
        data = {
            'plan_name': 'HTMX Updated Plan X',
            'vendor_id': 302,
            'description': 'Updated description via HTMX.',
            'is_active': False
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_edit', args=[self.plan.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, 'HTMX Updated Plan X')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshVendorPlanList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation view."""
        response = self.client.get(reverse('vendorplan_delete', args=[self.plan.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailymaster/vendorplan/confirm_delete.html')
        self.assertEqual(response.context['object'], self.plan)

    def test_delete_view_post_success(self):
        """Test POST request to delete a vendor plan."""
        # Create an object to delete specifically for this test to avoid interfering with others
        plan_to_delete = VendorPlan.objects.create(plan_name='Plan to Delete', vendor_id=999)
        response = self.client.post(reverse('vendorplan_delete', args=[plan_to_delete.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('vendorplan_list'))
        self.assertFalse(VendorPlan.objects.filter(pk=plan_to_delete.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), f'Vendor Plan "{plan_to_delete.plan_name}" deleted successfully.')

    def test_delete_view_post_success_htmx(self):
        """Test HTMX POST request to delete a vendor plan."""
        plan_to_delete_htmx = VendorPlan.objects.create(plan_name='HTMX Delete Plan', vendor_id=888)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_delete', args=[plan_to_delete_htmx.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(VendorPlan.objects.filter(pk=plan_to_delete_htmx.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshVendorPlanList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   All dynamic interactions for CRUD operations (opening forms, submitting, confirming deletions) are handled by HTMX.
-   The list view reloads its data dynamically using `hx-trigger="refreshVendorPlanList"`, ensuring the DataTables content is always up-to-date after any CRUD operation.
-   Modals are managed with Alpine.js (via `on click add .is-active to #modal` and `on closeModal remove .is-active from me`).
-   DataTables is integrated into the `_vendorplan_table.html` partial for client-side searching, sorting, and pagination. The JS for DataTables is within the partial, ensuring it's re-initialized correctly when HTMX swaps in the new table content.
-   HTTP 204 (No Content) responses are used for successful HTMX form submissions/deletions, coupled with `HX-Trigger` headers to initiate a list refresh and close the modal.
-   No custom JavaScript beyond Alpine.js directives and DataTables initialization script is required, maintaining a clean frontend stack.

## Final Notes

-   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with specific names related to `VendorPlan` and `dailymaster` application.
-   **DRY Templates:** We use `core/base.html` for the overall layout and create partial templates (`_vendorplan_table.html`, `form.html`, `confirm_delete.html`) that are loaded dynamically via HTMX, reducing redundancy.
-   **Fat Model, Thin View:** Business logic, such as `activate_plan`, `deactivate_plan`, `get_active_plans` and validation for `plan_name` uniqueness, is implemented directly within the `VendorPlan` model and its form, keeping the views concise and focused on request handling and rendering.
-   **Comprehensive Tests:** Unit tests for model methods and integration tests for all view types (List, Table Partial, Create, Update, Delete) are provided, covering both standard and HTMX-specific interactions.
-   **Automation Readiness:** This structure is designed for AI-assisted automation. An AI tool could:
    1.  Parse the empty ASP.NET file and infer the entity name (`VendorPlan`) and likely operations (Delete implies CRUD).
    2.  Use the inferred entity name to populate the provided Django templates for models, forms, views, URLs, and tests.
    3.  Generate the full set of Django files as demonstrated, needing minimal human intervention.
    4.  The conversational AI could then guide a user through setting up the Django project, running migrations, and deploying, leveraging these generated components.