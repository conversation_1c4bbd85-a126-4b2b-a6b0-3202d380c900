This comprehensive Django modernization plan outlines the strategy and steps to transition your legacy ASP.NET application, specifically the "Design Plan" module, to a modern Django-based solution. Our approach emphasizes automation, clear separation of concerns, and the use of modern web technologies like HTMX and Alpine.js for a highly interactive user experience without complex JavaScript.

## ASP.NET to Django Conversion Plan: Design Plan Module

**Business Value:** Modernizing this module to Django will provide a more maintainable, scalable, and secure application. It will improve developer productivity, allow for easier integration with other systems, and offer a faster, more responsive user experience for your team managing design plans. The use of HTMX and Alpine.js ensures a single-page application feel with minimal complexity, reducing development time and maintenance overhead. DataTables will significantly enhance data readability, searchability, and navigation for large datasets.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the C# code-behind, specifically the `fun.insert` method call (`"DRTS_Desing_Plan_New", "idwono,idfxn,idconcpd,idintrnrw,iddaps,iddapr,idcrr,idfdap,idboulst,iddrwrls,idcncd,idcmmdt,idftlst,idmnl,iddtal,idtpletr,Id"`), we identify the following:

-   **Table Name:** `DRTS_Desing_Plan_New` (Note: The typo "Desing" is preserved to match the existing database table name).
-   **Columns and Inferred Data Types:**
    -   `Id`: Primary Key, `AutoField` (Django manages this automatically).
    -   `idwono`: Work Order Number (e.g., "WO-1234") - `CharField`
    -   `idfxn`: Fixture Number (e.g., "FX-001") - `CharField`
    -   `idconcpd`: Concept Design Date - `DateField`
    -   `idintrnrw`: Internal Review Date - `DateField`
    -   `iddaps`: DAP Send Date - `DateField`
    -   `iddapr`: DAP Received Date - `DateField`
    -   `idcrr`: Correction Date - `DateField`
    -   `idfdap`: Final DAP Date - `DateField`
    -   `idboulst`: Bought List Date - `DateField`
    -   `iddrwrls`: Drawing Release Date - `DateField`
    -   `idcncd`: CNC Data Date - `DateField`
    -   `idcmmdt`: CMM Data Date - `DateField`
    -   `idftlst`: Fit List Date - `DateField`
    -   `idmnl`: Manual Date - `DateField`
    -   `iddtal`: Detailing Date - `DateField`
    -   `idtpletr`: TPL Entry Date - `DateField`

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code primarily implements the **Create** operation:
-   **Create:** Triggered by the `submit` button's `submit_Click` event. It inserts a new record into the `DRTS_Desing_Plan_New` table. A new `Id` is generated by querying the count of existing records and incrementing it.
-   **Read:** No explicit read operation (like displaying a list of existing design plans) is present in the provided `.aspx` or code-behind. However, a modern application would require a list view.
-   **Update:** No explicit update operation is present.
-   **Delete:** No explicit delete operation is present.
-   **Validation:** A simple validation checks if *all* fields are non-empty. If any field is empty, a client-side alert is shown. This "all fields compulsory" logic will be replicated.
-   **Data Transformation:** `idconcpd` and `idintrnrw` values are converted to uppercase before insertion. This will be handled in the Django model's `save` method or form cleaning if the target database actually stores dates as uppercase strings, otherwise, `DateField` will handle standard date formats. For this plan, we assume `DateField` for all date fields, which will handle standard date formats.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
-   **Input Controls:** Multiple `asp:TextBox` controls are used for user input for all data fields (`idwono`, `idfxn`, `idconcpd`, `idintrnrw`, `iddaps`, `iddapr`, `idcrr`, `idfdap`, `idboulst`, `iddrwrls`, `idcncd`, `idcmmdt`, `idftlst`, `idmnl`, `iddtal`, `idtpletr`).
-   **Action Controls:** One `asp:Button` (`submit`) triggers the data submission.
-   **Layout/Display:** `asp:Label` for page title and field labels. `asp:Table` controls (`Table1`, `Table3`) are used for structuring and grouping input fields, resembling a form layout rather than a data grid.

---

### Step 4: Generate Django Code

**Assumed Django App Name:** `design_plan`

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `DesignPlan` will map to the `DRTS_Desing_Plan_New` table. All date fields will be `DateField`, and identifier fields will be `CharField`. The `Id` primary key will be handled by Django's `AutoField`.

```python
# design_plan/models.py
from django.db import models

class DesignPlan(models.Model):
    """
    Represents a design plan entry in the DRTS_Desing_Plan_New table.
    """
    id = models.AutoField(db_column='Id', primary_key=True) # Django handles this as default 'id' field
    work_order_no = models.CharField(db_column='idwono', max_length=100, verbose_name="Work Order No.")
    fixture_no = models.CharField(db_column='idfxn', max_length=100, verbose_name="Fixture No.")
    concept_design_date = models.DateField(db_column='idconcpd', verbose_name="Concept Design Date", blank=True, null=True)
    internal_review_date = models.DateField(db_column='idintrnrw', verbose_name="Internal Review Date", blank=True, null=True)
    dap_send_date = models.DateField(db_column='iddaps', verbose_name="DAP Send Date", blank=True, null=True)
    dap_recd_date = models.DateField(db_column='iddapr', verbose_name="DAP Recd. Date", blank=True, null=True)
    correction_date = models.DateField(db_column='idcrr', verbose_name="Correction Date", blank=True, null=True)
    final_dap_date = models.DateField(db_column='idfdap', verbose_name="Final DAP Date", blank=True, null=True)
    bought_list_date = models.DateField(db_column='idboulst', verbose_name="Bought List Date", blank=True, null=True)
    drawing_release_date = models.DateField(db_column='iddrwrls', verbose_name="Drawing Release Date", blank=True, null=True)
    cnc_data_date = models.DateField(db_column='idcncd', verbose_name="CNC Data Date", blank=True, null=True)
    cmm_data_date = models.DateField(db_column='idcmmdt', verbose_name="CMM Data Date", blank=True, null=True)
    fit_list_date = models.DateField(db_column='idftlst', verbose_name="Fit List Date", blank=True, null=True)
    manual_date = models.DateField(db_column='idmnl', verbose_name="Manual Date", blank=True, null=True)
    detailing_date = models.DateField(db_column='iddtal', verbose_name="Detailing Date", blank=True, null=True)
    tpl_entry_date = models.DateField(db_column='idtpletr', verbose_name="TPL Entry Date", blank=True, null=True)

    class Meta:
        managed = False  # Set to False because the table already exists in the database
        db_table = 'DRTS_Desing_Plan_New' # Existing table name with typo
        verbose_name = 'Design Plan'
        verbose_name_plural = 'Design Plans'
        ordering = ['-id'] # Order by latest entry first

    def __str__(self):
        return f"Design Plan for WO: {self.work_order_no or 'N/A'}"
        
    def get_absolute_url(self):
        """Returns the URL to access a particular instance of DesignPlan."""
        from django.urls import reverse
        return reverse('designplan_detail', args=[str(self.id)])

    def save(self, *args, **kwargs):
        # Example of business logic in model: ensure certain fields are uppercase
        # if they were CharFields. For DateFields, this is usually not necessary.
        # If the original ASP.NET stored text for dates, this needs re-evaluation.
        # For now, assuming DateFields, no special uppercase logic here.
        super().save(*args, **kwargs)

    # Example of a business logic method (Fat Model principle)
    def is_complete(self):
        """
        Checks if all critical dates in the design plan have been recorded.
        This is an example, customize based on actual business rules.
        """
        required_fields = [
            self.concept_design_date, self.internal_review_date,
            self.dap_send_date, self.dap_recd_date, self.final_dap_date
        ]
        return all(field is not None for field in required_fields)

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
Create a `ModelForm` for `DesignPlan`. All fields will be required as per the ASP.NET validation logic (all fields compulsory). Use `DateInput` widgets for date fields.

```python
# design_plan/forms.py
from django import forms
from .models import DesignPlan

class DesignPlanForm(forms.ModelForm):
    """
    Form for creating and updating DesignPlan records.
    """
    class Meta:
        model = DesignPlan
        fields = [
            'work_order_no', 'fixture_no', 'concept_design_date', 
            'internal_review_date', 'dap_send_date', 'dap_recd_date', 
            'correction_date', 'final_dap_date', 'bought_list_date', 
            'drawing_release_date', 'cnc_data_date', 'cmm_data_date', 
            'fit_list_date', 'manual_date', 'detailing_date', 
            'tpl_entry_date'
        ]
        widgets = {
            'work_order_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'fixture_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'concept_design_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'internal_review_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'dap_send_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'dap_recd_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'correction_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'final_dap_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'bought_list_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'drawing_release_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'cnc_data_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'cmm_data_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'fit_list_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'manual_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'detailing_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'tpl_entry_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
    
    # Custom validation example: Ensure work order number is unique (if business rule dictates)
    def clean_work_order_no(self):
        work_order_no = self.cleaned_data['work_order_no']
        # If this is a new object and a DesignPlan with this work_order_no already exists
        if not self.instance.pk and DesignPlan.objects.filter(work_order_no=work_order_no).exists():
            raise forms.ValidationError("This Work Order Number already exists.")
        return work_order_no

    # ASP.NET had a "all fields are compulsory" check.
    # By default, ModelForm fields are required if corresponding model fields are not blank=True/null=True.
    # However, for DateFields, blank=True, null=True is often desired if dates can be optional.
    # If the original ASP.NET truly enforced ALL fields, then we can add a custom clean method:
    def clean(self):
        cleaned_data = super().clean()
        for field_name in self.fields:
            if not cleaned_data.get(field_name):
                # Add an error to the specific field if it's empty
                # Note: This will override ModelForm's default required error if model fields are blank=True.
                # If model fields are blank=False, default validation is sufficient.
                # Assuming model fields are blank=True/null=True to allow missing dates, then this custom validation is needed.
                self.add_error(field_name, "This field is compulsory.")
        return cleaned_data
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views will be thin, primarily handling HTTP requests and responses. Business logic, including data validation beyond basic form checks, will reside in the model. HTMX headers are checked for partial updates and `HX-Trigger` events. A new `DesignPlanTablePartialView` will be added to render just the DataTables content.

```python
# design_plan/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string

from .models import DesignPlan
from .forms import DesignPlanForm

# --- List View for Design Plans ---
class DesignPlanListView(ListView):
    """
    Displays a list of all design plans.
    """
    model = DesignPlan
    template_name = 'design_plan/designplan/list.html'
    context_object_name = 'design_plans' # Renamed for clarity in template

# --- Partial View for DataTable content (HTMX) ---
class DesignPlanTablePartialView(ListView):
    """
    Renders only the DataTables HTML for HTMX requests.
    """
    model = DesignPlan
    template_name = 'design_plan/designplan/_designplan_table.html'
    context_object_name = 'design_plans'

# --- Create View for Design Plans ---
class DesignPlanCreateView(CreateView):
    """
    Handles creation of new design plan records.
    """
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'design_plan/designplan/_designplan_form.html' # Use partial for modal
    success_url = reverse_lazy('designplan_list') # Not directly used for HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan added successfully.')
        # For HTMX requests, return an empty 204 response with trigger header
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList' # HTMX event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX requests, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


# --- Update View for Design Plans ---
class DesignPlanUpdateView(UpdateView):
    """
    Handles updating existing design plan records.
    """
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'design_plan/designplan/_designplan_form.html' # Use partial for modal
    success_url = reverse_lazy('designplan_list') # Not directly used for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

# --- Delete View for Design Plans ---
class DesignPlanDeleteView(DeleteView):
    """
    Handles deletion of design plan records.
    """
    model = DesignPlan
    template_name = 'design_plan/designplan/_designplan_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('designplan_list') # Not directly used for HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Design Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates utilize DRY principles by extending `core/base.html` and using partials for HTMX-loaded content (forms, table). Tailwind CSS classes are applied for styling, and DataTables JavaScript is initialized for the list view.

**1. List Template (`design_plan/designplan/list.html`):**
This is the main page for displaying design plans.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Design Plans</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'designplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then remove .hidden from #modal">
            Add New Design Plan
        </button>
    </div>
    
    <div id="designplanTable-container"
         hx-trigger="load, refreshDesignPlanList from:body"
         hx-get="{% url 'designplan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Design Plans...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .flex from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-3xl w-full mx-4 my-8 relative max-h-[90vh] overflow-y-auto">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management.
        // Example: x-data="{ showModal: false }" could be managed here if not using HTMX directly.
        // For this pattern, HTMX is primarily managing modal visibility.
    });
</script>
{% endblock %}
```

**2. Table Partial Template (`design_plan/designplan/_designplan_table.html`):**
This template renders the DataTables content for dynamic loading via HTMX.

```html
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <table id="designplanTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Work Order No.</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fixture No.</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Concept Design</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Internal Review</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">DAP Send</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">DAP Recd.</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Correction</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Final DAP</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Bought List</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Drawing Release</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">CNC Data</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">CMM Data</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fit List</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Manual</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Detailing</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">TPL Entry</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in design_plans %}
            <tr class="hover:bg-gray-50">
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.work_order_no }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.fixture_no }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.concept_design_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.internal_review_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.dap_send_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.dap_recd_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.correction_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.final_dap_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.bought_list_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.drawing_release_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.cnc_data_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.cmm_data_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.fit_list_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.manual_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.detailing_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm">{{ obj.tpl_entry_date|date:"Y-m-d"|default:"-" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 text-sm whitespace-nowrap">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'designplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'designplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="18" class="text-center py-4 text-gray-500">No design plans found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the content is loaded via HTMX
    // Ensure jQuery is loaded in base.html for DataTables.
    $(document).ready(function() {
        $('#designplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers",
            "responsive": true
        });
    });
</script>
```

**3. Form Partial Template (`design_plan/designplan/_designplan_form.html`):**
This template renders the form for creating or updating records within the modal.

```html
<div class="relative p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Design Plan</h3>
    
    <!-- Close button for modal -->
    <button type="button" 
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl font-bold"
            _="on click remove .flex from #modal then add .hidden to #modal">
        &times;
    </button>

    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out">
                Save Design Plan
            </button>
        </div>
    </form>
</div>
```

**4. Delete Confirmation Partial Template (`design_plan/designplan/_designplan_confirm_delete.html`):**
This template provides a confirmation dialog for deleting a record within the modal.

```html
<div class="relative p-6 text-center">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Design Plan for Work Order No.: <strong>{{ object.work_order_no }}</strong>?</p>
    <p class="text-red-500 font-medium mb-8">This action cannot be undone.</p>

    <!-- Close button for modal -->
    <button type="button" 
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl font-bold"
            _="on click remove .flex from #modal then add .hidden to #modal">
        &times;
    </button>

    <div class="flex justify-center space-x-4">
        <button
            type="button"
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out"
            _="on click remove .flex from #modal then add .hidden to #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'designplan_delete' object.pk %}"
            hx-swap="none"
            class="inline-flex justify-center py-2 px-5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-300 ease-in-out">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URL patterns are set up for the list view, form creation/editing (which use the same partial), the table content partial, and deletion.

```python
# design_plan/urls.py
from django.urls import path
from .views import (
    DesignPlanListView,
    DesignPlanCreateView,
    DesignPlanUpdateView,
    DesignPlanDeleteView,
    DesignPlanTablePartialView
)

urlpatterns = [
    path('design-plans/', DesignPlanListView.as_view(), name='designplan_list'),
    path('design-plans/add/', DesignPlanCreateView.as_view(), name='designplan_add'),
    path('design-plans/edit/<int:pk>/', DesignPlanUpdateView.as_view(), name='designplan_edit'),
    path('design-plans/delete/<int:pk>/', DesignPlanDeleteView.as_view(), name='designplan_delete'),
    # HTMX partial endpoint for DataTables content
    path('design-plans/table/', DesignPlanTablePartialView.as_view(), name='designplan_table'),
]

# Don't forget to include these URLs in your project's main urls.py:
# path('daily-reporting/', include('design_plan.urls')),
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for the `DesignPlan` model and integration tests for all `DesignPlan` views (`ListView`, `CreateView`, `UpdateView`, `DeleteView`, `TablePartialView`) are crucial for ensuring the migrated functionality works as expected.

```python
# design_plan/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import DesignPlan
from datetime import date

class DesignPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a sample DesignPlan for tests
        DesignPlan.objects.create(
            work_order_no='WO-TEST-001',
            fixture_no='FX-TEST-A',
            concept_design_date=date(2023, 1, 15),
            internal_review_date=date(2023, 1, 20),
            dap_send_date=date(2023, 1, 25),
            dap_recd_date=date(2023, 1, 30),
            correction_date=date(2023, 2, 5),
            final_dap_date=date(2023, 2, 10),
            bought_list_date=date(2023, 2, 12),
            drawing_release_date=date(2023, 2, 15),
            cnc_data_date=date(2023, 2, 18),
            cmm_data_date=date(2023, 2, 20),
            fit_list_date=date(2023, 2, 22),
            manual_date=date(2023, 2, 25),
            detailing_date=date(2023, 2, 28),
            tpl_entry_date=date(2023, 3, 3),
        )

    def test_design_plan_creation(self):
        design_plan = DesignPlan.objects.get(id=1)
        self.assertEqual(design_plan.work_order_no, 'WO-TEST-001')
        self.assertEqual(design_plan.fixture_no, 'FX-TEST-A')
        self.assertEqual(design_plan.concept_design_date, date(2023, 1, 15))

    def test_verbose_name_plural(self):
        self.assertEqual(DesignPlan._meta.verbose_name_plural, 'Design Plans')

    def test_db_table_name(self):
        self.assertEqual(DesignPlan._meta.db_table, 'DRTS_Desing_Plan_New')

    def test_str_method(self):
        design_plan = DesignPlan.objects.get(id=1)
        self.assertEqual(str(design_plan), 'Design Plan for WO: WO-TEST-001')

    def test_is_complete_method(self):
        design_plan = DesignPlan.objects.get(id=1)
        self.assertTrue(design_plan.is_complete())

        # Test with a missing required field (e.g., final_dap_date)
        incomplete_plan = DesignPlan.objects.create(
            work_order_no='WO-INCOMPLETE', fixture_no='FX-INC',
            concept_design_date=date(2023, 1, 1), internal_review_date=date(2023, 1, 2),
            dap_send_date=date(2023, 1, 3), dap_recd_date=date(2023, 1, 4),
            final_dap_date=None # Missing field
        )
        self.assertFalse(incomplete_plan.is_complete())

class DesignPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        DesignPlan.objects.create(
            work_order_no='WO-LIST-001',
            fixture_no='FX-LIST-A',
            concept_design_date=date(2023, 1, 1),
            internal_review_date=date(2023, 1, 2),
            dap_send_date=date(2023, 1, 3),
            dap_recd_date=date(2023, 1, 4),
            correction_date=date(2023, 1, 5),
            final_dap_date=date(2023, 1, 6),
            bought_list_date=date(2023, 1, 7),
            drawing_release_date=date(2023, 1, 8),
            cnc_data_date=date(2023, 1, 9),
            cmm_data_date=date(2023, 1, 10),
            fit_list_date=date(2023, 1, 11),
            manual_date=date(2023, 1, 12),
            detailing_date=date(2023, 1, 13),
            tpl_entry_date=date(2023, 1, 14),
        )
        
    def setUp(self):
        self.client = Client()

    # --- Test List View ---
    def test_list_view_get(self):
        response = self.client.get(reverse('designplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_plan/designplan/list.html')
        self.assertIn('design_plans', response.context)
        self.assertIsInstance(response.context['design_plans'].first(), DesignPlan)

    # --- Test Create View ---
    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('designplan_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_plan/designplan/_designplan_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_htmx_success(self):
        initial_count = DesignPlan.objects.count()
        data = {
            'work_order_no': 'WO-NEW-001',
            'fixture_no': 'FX-NEW-B',
            'concept_design_date': '2023-03-01',
            'internal_review_date': '2023-03-02',
            'dap_send_date': '2023-03-03',
            'dap_recd_date': '2023-03-04',
            'correction_date': '2023-03-05',
            'final_dap_date': '2023-03-06',
            'bought_list_date': '2023-03-07',
            'drawing_release_date': '2023-03-08',
            'cnc_data_date': '2023-03-09',
            'cmm_data_date': '2023-03-10',
            'fit_list_date': '2023-03-11',
            'manual_date': '2023-03-12',
            'detailing_date': '2023-03-13',
            'tpl_entry_date': '2023-03-14',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplan_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(DesignPlan.objects.filter(work_order_no='WO-NEW-001').exists())
        self.assertEqual(DesignPlan.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')

    def test_create_view_post_htmx_invalid(self):
        initial_count = DesignPlan.objects.count()
        data = {
            'work_order_no': '',  # Invalid data: empty required field
            'fixture_no': 'FX-INVALID',
            # Other fields omitted or invalid as needed for error
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplan_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # Renders form with errors for HTMX
        self.assertTemplateUsed(response, 'design_plan/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertFalse(DesignPlan.objects.filter(work_order_no='').exists())
        self.assertEqual(DesignPlan.objects.count(), initial_count) # No new object created

    # --- Test Update View ---
    def test_update_view_get_htmx(self):
        design_plan = DesignPlan.objects.get(work_order_no='WO-LIST-001')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('designplan_edit', args=[design_plan.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_plan/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, design_plan)

    def test_update_view_post_htmx_success(self):
        design_plan = DesignPlan.objects.get(work_order_no='WO-LIST-001')
        updated_data = {
            'work_order_no': 'WO-LIST-001', # Keep same WO
            'fixture_no': 'FX-UPDATED-B', # Update fixture no
            'concept_design_date': '2023-01-01',
            'internal_review_date': '2023-01-02',
            'dap_send_date': '2023-01-03',
            'dap_recd_date': '2023-01-04',
            'correction_date': '2023-01-05',
            'final_dap_date': '2023-01-06',
            'bought_list_date': '2023-01-07',
            'drawing_release_date': '2023-01-08',
            'cnc_data_date': '2023-01-09',
            'cmm_data_date': '2023-01-10',
            'fit_list_date': '2023-01-11',
            'manual_date': '2023-01-12',
            'detailing_date': '2023-01-13',
            'tpl_entry_date': '2023-01-14',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplan_edit', args=[design_plan.pk]), updated_data, **headers)
        
        self.assertEqual(response.status_code, 204)
        design_plan.refresh_from_db()
        self.assertEqual(design_plan.fixture_no, 'FX-UPDATED-B')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')

    # --- Test Delete View ---
    def test_delete_view_get_htmx(self):
        design_plan = DesignPlan.objects.get(work_order_no='WO-LIST-001')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('designplan_delete', args=[design_plan.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_plan/designplan/_designplan_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], design_plan)

    def test_delete_view_post_htmx_success(self):
        design_plan = DesignPlan.objects.get(work_order_no='WO-LIST-001')
        initial_count = DesignPlan.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplan_delete', args=[design_plan.pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(DesignPlan.objects.filter(pk=design_plan.pk).exists())
        self.assertEqual(DesignPlan.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
    
    # --- Test Table Partial View (HTMX) ---
    def test_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('designplan_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_plan/designplan/_designplan_table.html')
        self.assertIn('design_plans', response.context)
        self.assertContains(response, 'id="designplanTable"') # Check for DataTable ID
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Dynamic Content:**
    -   The main `list.html` uses `hx-get` to load the `_designplan_table.html` initially and on `refreshDesignPlanList` events.
    -   "Add New", "Edit", and "Delete" buttons use `hx-get` to load forms/confirmations into a modal (`#modalContent`).
    -   Form submissions (`hx-post`) in the modal trigger `HX-Trigger: refreshDesignPlanList` to refresh the table.
    -   Modal display/hide is handled by HTMX and Alpine.js (via `_` attributes for class toggling).
-   **Alpine.js for UI State:**
    -   Basic `x-data` is included in the base template for `Alpine.js` initialization.
    -   `_` attributes in templates (part of Alpine.js's "magical properties" and Micro-framework for JS-less interactivity) handle modal visibility based on HTMX actions (`on click add .flex to #modal then remove .hidden from #modal`).
-   **DataTables for List Views:**
    -   The `_designplan_table.html` partial includes the `<table id="designplanTable">` tag and the JavaScript `$(document).ready(function() { $('#designplanTable').DataTable({...}); });` to initialize DataTables after the content is loaded via HTMX. Ensure jQuery and DataTables CDN links are in `core/base.html`.
-   **No Full Page Reloads:** All CRUD operations (add, edit, delete) are performed without full page reloads, providing a smooth user experience.
-   **Clean Frontend:** No complex custom JavaScript files are required for these interactions; everything is declarative with HTMX and minimal Alpine.js.

This comprehensive plan provides a clear, automated path to modernize your ASP.NET "Design Plan" module into a robust, maintainable Django application, leveraging modern web development best practices.