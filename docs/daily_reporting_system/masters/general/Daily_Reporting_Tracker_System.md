## ASP.NET to Django Conversion Script: Daily Reporting Tracker System

This modernization plan outlines the automated conversion of your legacy ASP.NET Daily Reporting Tracker System to a robust, modern Django application. Our approach prioritizes automation, leveraging AI-assisted tools to streamline the transition, ensuring a consistent, high-quality outcome with minimal manual intervention.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (`core/base.html`).
- Focus ONLY on component-specific code for the current module (`dailyreporting` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several database tables:

1.  **Main Data Table:** `DRT_Sys_New`
    *   **Inferred Columns:** `ID` (used for manual increment, likely the primary key), `E_name`, `Designation`, `Department`, `DOR`, `SALW`, `TCW`, `APC`, `APNC`, `AUC`, `PNW`, `IdDate`, `IdWo`, `IdActivity`, `IDET`, `IdStatus`, `IDperc`, `Idrmk`.
    *   **Data Types:** Most fields are `TextBox` or `DropDownList` (string text), implying `VARCHAR` or `TEXT`. Dates (`DOR`, `IdDate`) are `DATE`. `% Completed` (`IDperc`) is an `INT`.

2.  **Lookup Tables (for DropDownLists):**
    *   `tblHR_OfficeStaff`: Used for `E_name` (Employee Name).
        *   **Inferred Columns:** `ID` (PK), `EmployeeName`.
    *   `tblHR_Designation`: Used for `Designation` (Type).
        *   **Inferred Columns:** `ID` (PK), `Type`.
    *   `tblHR_Departments`: Used for `Department` (Description).
        *   **Inferred Columns:** `ID` (PK), `Description`.

**Instructions:**
The AI will automatically parse the `SqlDataSource` elements and `SqlCommand` strings to identify these tables and their corresponding columns. For instance, `SelectCommand="SELECT [EmployeeName] FROM [tblHR_OfficeStaff]"` clearly indicates `tblHR_OfficeStaff` with a `EmployeeName` column. The `INSERT` statement provides the complete list of columns for `DRT_Sys_New`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The provided ASP.NET code primarily focuses on **Create** (insertion) functionality:

*   **Create:** The `Submit_Click1` event handler explicitly performs an `INSERT` operation into the `DRT_Sys_New` table. It manually generates a new `ID` by counting existing records and incrementing.
*   **Read:** Implicitly, data is read from `tblHR_OfficeStaff`, `tblHR_Designation`, and `tblHR_Departments` to populate the dropdown lists. A dedicated "Read" or "List" view for `DRT_Sys_New` is not present but is a standard requirement for a "Tracker System" and will be generated.
*   **Update:** No explicit update functionality is present in the provided code.
*   **Delete:** No explicit delete functionality is present.

**Validation Logic:**
*   A basic validation check exists in `Submit_Click1`: it verifies if *all* fields are empty. If any field's `.Text` property is an empty string, an alert message "All fields are compulsory, please insert values." is displayed. This "all fields required" logic will be replicated in the Django form.

**Instructions:**
The AI tool analyzes event handlers and SQL commands to deduce these operations. The manual `ID` generation suggests an opportunity for modernization to auto-incrementing primary keys. The absence of explicit Read/Update/Delete operations for `DRT_Sys_New` implies these will be generated as standard CRUD features for a complete Django application.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Input Forms:**
    *   `asp:DropDownList`: Used for `E_name` (Employee Name), `Designation`, `Department`, and `IDperc` (% Completed). These will map to Django `forms.ChoiceField` or `forms.ModelChoiceField` within HTML `<select>` elements.
    *   `asp:TextBox`: Used for `DOR`, `SALW`, `TCW`, `APC`, `APNC`, `AUC`, `PNW`, `IdDate`, `IdWo`, `IdActivity`, `IDET`, `IdStatus`, `Idrmk`. These will map to Django `forms.CharField` or `forms.DateField` within HTML `<input type="text">` or `<input type="date">` elements.
    *   `cc1:CalendarExtender`: Provides date picker functionality for `DOR` and `IdDate`. In Django, setting `type="date"` for input fields often leverages native browser date pickers, or we can use a light Alpine.js/HTMX compatible date picker library if more advanced UI is needed.
*   **Action Buttons:**
    *   `asp:Button ID="Submit"`: Triggers the `Submit_Click1` server-side event. This will be converted to an HTML `<button type="submit">` with HTMX attributes for asynchronous form submission.
*   **Layout:** ASP.NET `asp:Table` controls are used for layout. These will be replaced with modern HTML `div` and Tailwind CSS for flexible, responsive layout.

**Instructions:**
The AI categorizes ASP.NET controls (e.g., `TextBox` to `CharField`, `DropDownList` to `ChoiceField`) and infers their HTML equivalents. It identifies server-side event handlers and converts them into appropriate HTMX-driven interactions for a dynamic user experience without full page reloads.

### Step 4: Generate Django Code

### 4.1 Models

**Task:** Create Django models based on the identified database schema. We will create models for the lookup tables (`Employee`, `Designation`, `Department`) and the main data table (`DailyReport`). Note that `DailyReport` fields like `employee_name` will be `CharField` to reflect the ASP.NET code *inserting the string text*, while the `Employee` model is for the lookup table. For full normalization, `DailyReport` should link to `Employee` via a `ForeignKey`, which would require a database schema change. We recommend this in the final notes.

**`dailyreporting/models.py`**

```python
from django.db import models

# Models for lookup tables (assumed to exist in the legacy database)
class Employee(models.Model):
    """
    Represents an employee from the tblHR_OfficeStaff table.
    Used for populating the Employee Name dropdown.
    """
    id = models.AutoField(db_column='ID', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, unique=True)

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'tblHR_OfficeStaff' # Maps to the existing legacy table
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class Designation(models.Model):
    """
    Represents a designation from the tblHR_Designation table.
    Used for populating the Designation dropdown.
    """
    id = models.AutoField(db_column='ID', primary_key=True)
    type_name = models.CharField(db_column='Type', max_length=255, unique=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.type_name

class Department(models.Model):
    """
    Represents a department from the tblHR_Departments table.
    Used for populating the Department dropdown.
    """
    id = models.AutoField(db_column='ID', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, unique=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

# Main Daily Reporting Tracker System table
class DailyReport(models.Model):
    """
    Represents a daily report entry from the DRT_Sys_New table.
    Note: Fields like employee_name, designation_name, department_name are CharFields
    to reflect the original ASP.NET code's behavior of storing the string values,
    not foreign keys, as seen in the SQL INSERT statement.
    """
    # The 'ID' column from DRT_Sys_New is assumed to be an auto-incrementing PK in the DB.
    # If not, further DB migration or custom PK management is needed.
    # Django's default 'id' field will map to this if it's the primary key.
    
    employee_name = models.CharField(
        db_column='E_name', max_length=255, verbose_name="Employee Name",
        help_text="Name of the employee who submitted the report."
    )
    designation_name = models.CharField(
        db_column='Designation', max_length=255, verbose_name="Designation",
        help_text="Designation of the employee."
    )
    department_name = models.CharField(
        db_column='Department', max_length=255, verbose_name="Department",
        help_text="Department of the employee."
    )
    
    date_of_reporting = models.DateField(
        db_column='DOR', verbose_name="Date of Reporting",
        help_text="The date on which the report is submitted."
    )
    significant_achievements_last_week = models.TextField(
        db_column='SALW', verbose_name="Significant Achievements Last Week",
        help_text="Key accomplishments from the previous week."
    )
    tasks_current_week = models.TextField(
        db_column='TCW', verbose_name="Activities/Task for Current Week",
        help_text="Main activities or tasks planned for the current week."
    )
    activities_planned_completed = models.TextField(
        db_column='APC', verbose_name="Activities Planned and Completed",
        help_text="Tasks that were planned and successfully finished."
    )
    activities_planned_not_completed = models.TextField(
        db_column='APNC', verbose_name="Activities Planned but Not Completed",
        help_text="Tasks that were planned but could not be completed."
    )
    activities_unplanned_completed = models.TextField(
        db_column='AUC', verbose_name="Activities Unplanned but Completed",
        help_text="New tasks that emerged and were completed during the week."
    )
    plan_for_next_week = models.TextField(
        db_column='PNW', verbose_name="Plan for Next Week",
        help_text="Outline of tasks and goals for the upcoming week."
    )
    
    daily_activity_date = models.DateField(
        db_column='IdDate', verbose_name="Daily Activity Date",
        help_text="Date of the specific daily activity."
    )
    work_order_no = models.CharField(
        db_column='IdWo', max_length=255, verbose_name="Work Order #",
        help_text="Associated work order number for the activity."
    )
    activity_description = models.TextField(
        db_column='IdActivity', verbose_name="Activity",
        help_text="Detailed description of the daily activity."
    )
    estimated_time = models.CharField( # Assuming string from ASP.NET TextBox
        db_column='IDET', max_length=255, verbose_name="Estimated Time",
        help_text="Estimated time duration for the activity (e.g., '2 hours', 'half day')."
    )
    status = models.CharField(
        db_column='IdStatus', max_length=255, verbose_name="Status",
        help_text="Current status of the activity (e.g., 'In Progress', 'Completed')."
    )
    percentage_completed = models.IntegerField(
        db_column='IDperc', verbose_name="% Completed",
        help_text="Percentage of the activity completed (0-100)."
    )
    remarks = models.TextField(
        db_column='Idrmk', verbose_name="Remarks",
        help_text="Any additional comments or notes for the activity."
    )

    class Meta:
        managed = False # Crucial for mapping to existing legacy database tables
        db_table = 'DRT_Sys_New' # The actual table name in your legacy database
        verbose_name = 'Daily Report'
        verbose_name_plural = 'Daily Reports'

    def __str__(self):
        return f"Report by {self.employee_name} on {self.date_of_reporting}"
        
    def validate_all_fields_present(self):
        """
        Business logic: Checks if all required fields are present.
        Mimics the ASP.NET 'All feilds are compulsary' validation.
        Returns True if all fields are non-empty/non-null, False otherwise.
        """
        required_fields = [
            self.employee_name, self.designation_name, self.department_name, self.date_of_reporting,
            self.significant_achievements_last_week, self.tasks_current_week, self.activities_planned_completed,
            self.activities_planned_not_completed, self.activities_unplanned_completed, self.plan_for_next_week,
            self.daily_activity_date, self.work_order_no, self.activity_description, self.estimated_time,
            self.status, self.percentage_completed, self.remarks
        ]
        
        # Check for empty strings for CharFields/TextFields, None for DateFields, 
        # and ensure percentage_completed is not None (0 is a valid value).
        for field_value in required_fields:
            if isinstance(field_value, str) and not field_value.strip(): # Check for empty or whitespace-only strings
                return False
            elif field_value is None: # Check for None values for DateFields or IntegerFields
                return False
        return True

```

### 4.2 Forms

**Task:** Define Django forms for user input. This will include the `DailyReportForm` and custom fields for the lookup dropdowns.

**`dailyreporting/forms.py`**

```python
from django import forms
from .models import DailyReport, Employee, Designation, Department

class DailyReportForm(forms.ModelForm):
    """
    Form for creating and updating DailyReport entries.
    Uses ModelChoiceField for employee, designation, and department dropdowns,
    pre-populating choices from the respective lookup tables.
    """

    # ModelChoiceFields to populate dropdowns from legacy tables
    # The 'empty_label' can be set to None if a choice is always required.
    employee_name = forms.ModelChoiceField(
        queryset=Employee.objects.all().order_by('employee_name'),
        to_field_name='employee_name', # Use employee_name as the value submitted
        empty_label="Select Employee",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Employee Name"
    )
    designation_name = forms.ModelChoiceField(
        queryset=Designation.objects.all().order_by('type_name'),
        to_field_name='type_name', # Use type_name as the value submitted
        empty_label="Select Designation",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Designation"
    )
    department_name = forms.ModelChoiceField(
        queryset=Department.objects.all().order_by('description'),
        to_field_name='description', # Use description as the value submitted
        empty_label="Select Department",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Department"
    )

    # Choices for Percentage Completed dropdown (0-100)
    PERCENTAGE_CHOICES = [(i, str(i)) for i in range(101)]
    percentage_completed = forms.ChoiceField(
        choices=PERCENTAGE_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="% Completed"
    )

    class Meta:
        model = DailyReport
        # List all fields explicitly to ensure proper order and control
        fields = [
            'employee_name', 'designation_name', 'department_name', 'date_of_reporting',
            'significant_achievements_last_week', 'tasks_current_week', 'activities_planned_completed',
            'activities_planned_not_completed', 'activities_unplanned_completed', 'plan_for_next_week',
            'daily_activity_date', 'work_order_no', 'activity_description', 'estimated_time',
            'status', 'percentage_completed', 'remarks'
        ]
        widgets = {
            'date_of_reporting': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'significant_achievements_last_week': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tasks_current_week': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'activities_planned_completed': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'activities_planned_not_completed': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'activities_unplanned_completed': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'plan_for_next_week': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'daily_activity_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_order_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'activity_description': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'estimated_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = { # Explicitly set labels for consistency
            'employee_name': 'Employee Name',
            'designation_name': 'Designation',
            'department_name': 'Department',
            'date_of_reporting': 'Date of Reporting',
            'significant_achievements_last_week': 'Significant Achievements Last Week',
            'tasks_current_week': 'Activities/Task for Current Week',
            'activities_planned_completed': 'Activities Planned and Completed',
            'activities_planned_not_completed': 'Activities Planned but Not Completed',
            'activities_unplanned_completed': 'Activities Unplanned but Completed',
            'plan_for_next_week': 'Plan for Next Week',
            'daily_activity_date': 'Daily Activity Date',
            'work_order_no': 'W/O #',
            'activity_description': 'Activity',
            'estimated_time': 'Estimated Time',
            'status': 'Status',
            'percentage_completed': '% Completed',
            'remarks': 'Remarks',
        }

    def clean(self):
        """
        Custom form validation to ensure all fields are considered "compulsory"
        as per the original ASP.NET logic.
        """
        cleaned_data = super().clean()
        for field_name in self.fields:
            field_value = cleaned_data.get(field_name)
            # Check if value is None (for date fields) or empty string (for text fields)
            if (field_value is None) or \
               (isinstance(field_value, str) and not field_value.strip()) or \
               (field_name == 'percentage_completed' and field_value is None): # Check specifically for percentage as well
                self.add_error(field_name, f"This field is compulsory and cannot be empty.")
        return cleaned_data

```

### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring views remain thin and business logic resides in models. A partial view for the DataTables content is also added for HTMX.

**`dailyreporting/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DailyReport
from .forms import DailyReportForm

class DailyReportListView(ListView):
    """
    Displays a list of all DailyReport entries.
    The main list view will contain the HTMX-driven DataTable container.
    """
    model = DailyReport
    template_name = 'dailyreporting/dailyreport/list.html'
    context_object_name = 'daily_reports' # Name for the queryset in the template

class DailyReportTablePartialView(ListView):
    """
    Returns the partial HTML for the DailyReport DataTable.
    This view is specifically for HTMX requests to refresh the table.
    """
    model = DailyReport
    template_name = 'dailyreporting/dailyreport/_dailyreport_table.html'
    context_object_name = 'daily_reports'
    
    def get_queryset(self):
        """
        Orders the daily reports by date of reporting in descending order by default.
        """
        return DailyReport.objects.all().order_by('-date_of_reporting')

class DailyReportCreateView(CreateView):
    """
    Handles the creation of new DailyReport entries via a modal form.
    """
    model = DailyReport
    form_class = DailyReportForm
    template_name = 'dailyreporting/dailyreport/_dailyreport_form.html' # Use partial template for modal
    success_url = reverse_lazy('dailyreport_list') # Not directly used for HTMX, but good practice

    def form_valid(self, form):
        """
        Called when form data is valid. Saves the instance and sends HTMX trigger.
        Includes model-level validation (e.g., all fields compulsory).
        """
        # Business logic from model: ensuring all fields are truly present
        if not form.instance.validate_all_fields_present():
            # If model-level validation fails, re-render form with errors
            messages.error(self.request, "All fields are compulsory, please insert values.")
            return self.form_invalid(form)

        response = super().form_valid(form)
        messages.success(self.request, 'Daily Report added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, send a 204 No Content response to close modal and trigger list refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDailyReportList' # Custom HTMX event to refresh the table
                }
            )
        return response

    def form_invalid(self, form):
        """
        Called when form data is invalid. Re-renders the form with errors.
        This also sends a 200 OK response with the form HTML for HTMX.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, render the form again with errors
            return response
        return response

class DailyReportUpdateView(UpdateView):
    """
    Handles updating existing DailyReport entries via a modal form.
    """
    model = DailyReport
    form_class = DailyReportForm
    template_name = 'dailyreporting/dailyreport/_dailyreport_form.html' # Use partial template
    success_url = reverse_lazy('dailyreport_list')

    def form_valid(self, form):
        """
        Called when form data is valid. Saves the instance and sends HTMX trigger.
        """
        if not form.instance.validate_all_fields_present():
            messages.error(self.request, "All fields are compulsory, please insert values.")
            return self.form_invalid(form)

        response = super().form_valid(form)
        messages.success(self.request, 'Daily Report updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDailyReportList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class DailyReportDeleteView(DeleteView):
    """
    Handles deleting DailyReport entries via a modal confirmation.
    """
    model = DailyReport
    template_name = 'dailyreporting/dailyreport/_dailyreport_confirm_delete.html' # Use partial template
    success_url = reverse_lazy('dailyreport_list')

    def delete(self, request, *args, **kwargs):
        """
        Called when the delete confirmation is confirmed. Deletes the instance
        and sends HTMX trigger.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Daily Report deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDailyReportList'
                }
            )
        return response

```

### 4.4 Templates

**Task:** Create templates for each view, ensuring DRY principles and HTMX/Alpine.js integration.

**`dailyreporting/templates/dailyreporting/dailyreport/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Daily Reports</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dailyreport_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Daily Report
        </button>
    </div>
    
    <!-- HTMX container for the DataTables list. It refreshes on load and on 'refreshDailyReportList' event. -->
    <div id="dailyreportTable-container"
         hx-trigger="load, refreshDailyReportList from:body"
         hx-get="{% url 'dailyreport_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Daily Reports...</p>
        </div>
    </div>
    
    <!-- Modal for add/edit/delete forms, managed by Alpine.js and HTMX -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js components here if needed for this page -->
{% endblock %}
```

**`dailyreporting/templates/dailyreporting/dailyreport/_dailyreport_table.html`**

```html
<table id="dailyreportTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Reporting</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order #</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% Completed</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for report in daily_reports %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.employee_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.date_of_reporting|date:"M d, Y" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.department_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.work_order_no }}</td>
            <td class="py-3 px-4 text-sm text-gray-900 truncate max-w-xs">{{ report.activity_description }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.status }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.percentage_completed }}%</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'dailyreport_edit' report.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                    hx-get="{% url 'dailyreport_delete' report.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 px-6 text-center text-gray-500">No daily reports found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Initialize DataTables after content is loaded -->
<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#dailyreportTable')) {
            $('#dailyreportTable').DataTable().destroy();
        }
        $('#dailyreportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Make table responsive
            "autoWidth": false // Disable auto-width to allow more control with Tailwind
        });
    });
</script>
```

**`dailyreporting/templates/dailyreporting/dailyreport/_dailyreport_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Daily Report</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="col-span-1">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-2 space-y-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <ul class="text-red-600 text-sm mt-4">
            {% for error in form.non_field_errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save Report
            </button>
        </div>
    </form>
</div>
```

**`dailyreporting/templates/dailyreporting/dailyreport/_dailyreport_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the daily report by **{{ dailyreport.employee_name }}** on **{{ dailyreport.date_of_reporting|date:"M d, Y" }}**?</p>
    <p class="text-sm text-red-600 mb-6">This action cannot be undone.</p>
    
    <div class="flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'dailyreport_delete' dailyreport.pk %}"
            hx-swap="none"
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
            Delete Report
        </button>
    </div>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views within the `dailyreporting` app.

**`dailyreporting/urls.py`**

```python
from django.urls import path
from .views import (
    DailyReportListView,
    DailyReportTablePartialView, # For HTMX refresh
    DailyReportCreateView,
    DailyReportUpdateView,
    DailyReportDeleteView,
)

urlpatterns = [
    # Main list view for daily reports
    path('dailyreports/', DailyReportListView.as_view(), name='dailyreport_list'),
    
    # HTMX endpoint to get the table content for dynamic updates
    path('dailyreports/table/', DailyReportTablePartialView.as_view(), name='dailyreport_table'),
    
    # Endpoint for adding a new daily report (loaded into modal via HTMX)
    path('dailyreports/add/', DailyReportCreateView.as_view(), name='dailyreport_add'),
    
    # Endpoint for editing an existing daily report (loaded into modal via HTMX)
    path('dailyreports/edit/<int:pk>/', DailyReportUpdateView.as_view(), name='dailyreport_edit'),
    
    # Endpoint for deleting a daily report (confirmation in modal via HTMX)
    path('dailyreports/delete/<int:pk>/', DailyReportDeleteView.as_view(), name='dailyreport_delete'),
]

```

### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views.

**`dailyreporting/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DailyReport, Employee, Designation, Department
from datetime import date

class LookupModelTest(TestCase):
    """
    Unit tests for Employee, Designation, and Department lookup models.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for lookup tables (managed=False)
        # In a real scenario, these would pre-exist in the database.
        # For testing, we simulate their presence if the DB is empty.
        Employee.objects.create(id=1, employee_name='John Doe')
        Designation.objects.create(id=1, type_name='Software Engineer')
        Department.objects.create(id=1, description='IT Department')

    def test_employee_creation(self):
        employee = Employee.objects.get(employee_name='John Doe')
        self.assertEqual(employee.employee_name, 'John Doe')
        self.assertEqual(str(employee), 'John Doe')
        self.assertEqual(Employee._meta.db_table, 'tblHR_OfficeStaff')

    def test_designation_creation(self):
        designation = Designation.objects.get(type_name='Software Engineer')
        self.assertEqual(designation.type_name, 'Software Engineer')
        self.assertEqual(str(designation), 'Software Engineer')
        self.assertEqual(Designation._meta.db_table, 'tblHR_Designation')

    def test_department_creation(self):
        department = Department.objects.get(description='IT Department')
        self.assertEqual(department.description, 'IT Department')
        self.assertEqual(str(department), 'IT Department')
        self.assertEqual(Department._meta.db_table, 'tblHR_Departments')

class DailyReportModelTest(TestCase):
    """
    Unit tests for the DailyReport model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a sample DailyReport instance for testing
        DailyReport.objects.create(
            employee_name='Jane Smith',
            designation_name='Project Manager',
            department_name='Operations',
            date_of_reporting=date(2023, 10, 26),
            significant_achievements_last_week='Completed phase 1 of project X.',
            tasks_current_week='Plan phase 2, review team progress.',
            activities_planned_completed='Attended client meeting, prepared report.',
            activities_planned_not_completed='Review budget (delayed).',
            activities_unplanned_completed='Resolved critical bug in system Y.',
            plan_for_next_week='Kick-off phase 2, hire new team member.',
            daily_activity_date=date(2023, 10, 25),
            work_order_no='WO-2023-001',
            activity_description='Daily stand-up meeting and task allocation.',
            estimated_time='1 hour',
            status='Completed',
            percentage_completed=100,
            remarks='All team members updated.'
        )
        
    def test_daily_report_creation(self):
        report = DailyReport.objects.get(employee_name='Jane Smith')
        self.assertEqual(report.designation_name, 'Project Manager')
        self.assertEqual(report.date_of_reporting, date(2023, 10, 26))
        self.assertEqual(report.percentage_completed, 100)
        self.assertEqual(DailyReport._meta.db_table, 'DRT_Sys_New')

    def test_str_method(self):
        report = DailyReport.objects.get(employee_name='Jane Smith')
        expected_str = "Report by Jane Smith on 2023-10-26"
        self.assertEqual(str(report), expected_str)

    def test_validate_all_fields_present_valid(self):
        report = DailyReport.objects.get(employee_name='Jane Smith')
        self.assertTrue(report.validate_all_fields_present())

    def test_validate_all_fields_present_invalid_empty_string(self):
        report = DailyReport.objects.get(employee_name='Jane Smith')
        report.employee_name = '' # Make a required field empty
        self.assertFalse(report.validate_all_fields_present())
    
    def test_validate_all_fields_present_invalid_none_date(self):
        report = DailyReport.objects.get(employee_name='Jane Smith')
        report.date_of_reporting = None # Make a required date field None
        self.assertFalse(report.validate_all_fields_present())

    def test_validate_all_fields_present_invalid_none_percentage(self):
        report = DailyReport.objects.get(employee_name='Jane Smith')
        report.percentage_completed = None # Make a required integer field None
        self.assertFalse(report.validate_all_fields_present())


class DailyReportViewsTest(TestCase):
    """
    Integration tests for DailyReport views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create lookup data (as ModelChoiceField needs actual objects)
        Employee.objects.create(id=1, employee_name='John Doe')
        Employee.objects.create(id=2, employee_name='Jane Smith')
        Designation.objects.create(id=1, type_name='Software Engineer')
        Department.objects.create(id=1, description='IT Department')
        
        # Create initial DailyReport for list/update/delete tests
        DailyReport.objects.create(
            id=1, # Manually set ID for specific retrieval in tests if DB PK is not default
            employee_name='John Doe',
            designation_name='Software Engineer',
            department_name='IT Department',
            date_of_reporting=date(2023, 1, 1),
            significant_achievements_last_week='Legacy report data 1',
            tasks_current_week='Legacy report data 1',
            activities_planned_completed='Legacy report data 1',
            activities_planned_not_completed='Legacy report data 1',
            activities_unplanned_completed='Legacy report data 1',
            plan_for_next_week='Legacy report data 1',
            daily_activity_date=date(2023, 1, 2),
            work_order_no='WO-L-001',
            activity_description='Initial activity description.',
            estimated_time='8 hours',
            status='Completed',
            percentage_completed=90,
            remarks='First test report.'
        )

    def setUp(self):
        self.client = Client()

    def test_dailyreport_list_view(self):
        response = self.client.get(reverse('dailyreport_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/dailyreport/list.html')
        self.assertIn('daily_reports', response.context) # Check if context contains the queryset

    def test_dailyreport_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dailyreport_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/dailyreport/_dailyreport_table.html')
        self.assertIn('daily_reports', response.context)
        self.assertContains(response, 'John Doe') # Check if existing data is rendered

    def test_dailyreport_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dailyreport_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/dailyreport/_dailyreport_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Daily Report')

    def test_dailyreport_create_view_post_success_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'employee_name': 'Jane Smith', # Must be a valid choice from Employee model
            'designation_name': 'Software Engineer',
            'department_name': 'IT Department',
            'date_of_reporting': '2023-11-01',
            'significant_achievements_last_week': 'New achievements.',
            'tasks_current_week': 'New tasks.',
            'activities_planned_completed': 'Completed new activities.',
            'activities_planned_not_completed': 'Did not complete new activities.',
            'activities_unplanned_completed': 'Completed unplanned activities.',
            'plan_for_next_week': 'Plan for new week.',
            'daily_activity_date': '2023-11-02',
            'work_order_no': 'WO-TEST-002',
            'activity_description': 'New activity log.',
            'estimated_time': '2 hours',
            'status': 'Pending',
            'percentage_completed': '50', # Must be string for ChoiceField
            'remarks': 'New report remarks.'
        }
        response = self.client.post(reverse('dailyreport_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDailyReportList')
        self.assertTrue(DailyReport.objects.filter(work_order_no='WO-TEST-002').exists())
        self.assertEqual(DailyReport.objects.count(), 2) # Initial + new one

    def test_dailyreport_create_view_post_invalid_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            # Missing many required fields to trigger validation errors
            'employee_name': '', # Empty employee name
            'date_of_reporting': '', # Empty date
            'percentage_completed': '50', # Valid, but other fields are missing
        }
        response = self.client.post(reverse('dailyreport_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'dailyreporting/dailyreport/_dailyreport_form.html')
        self.assertContains(response, 'This field is compulsory and cannot be empty.')
        self.assertEqual(DailyReport.objects.count(), 1) # No new object created

    def test_dailyreport_update_view_get_htmx(self):
        report = DailyReport.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dailyreport_edit', args=[report.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/dailyreport/_dailyreport_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Daily Report')
        self.assertContains(response, report.work_order_no) # Check if existing data is pre-filled

    def test_dailyreport_update_view_post_success_htmx(self):
        report = DailyReport.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'employee_name': report.employee_name,
            'designation_name': report.designation_name,
            'department_name': report.department_name,
            'date_of_reporting': report.date_of_reporting.isoformat(),
            'significant_achievements_last_week': 'Updated achievements.',
            'tasks_current_week': report.tasks_current_week,
            'activities_planned_completed': report.activities_planned_completed,
            'activities_planned_not_completed': report.activities_planned_not_completed,
            'activities_unplanned_completed': report.activities_unplanned_completed,
            'plan_for_next_week': report.plan_for_next_week,
            'daily_activity_date': report.daily_activity_date.isoformat(),
            'work_order_no': 'WO-UPDATED', # Changed field
            'activity_description': report.activity_description,
            'estimated_time': report.estimated_time,
            'status': report.status,
            'percentage_completed': str(report.percentage_completed),
            'remarks': report.remarks
        }
        response = self.client.post(reverse('dailyreport_edit', args=[report.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDailyReportList')
        report.refresh_from_db()
        self.assertEqual(report.work_order_no, 'WO-UPDATED')

    def test_dailyreport_delete_view_get_htmx(self):
        report = DailyReport.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dailyreport_delete', args=[report.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/dailyreport/_dailyreport_confirm_delete.html')
        self.assertIn('dailyreport', response.context)
        self.assertContains(response, f'delete the daily report by **{report.employee_name}**')

    def test_dailyreport_delete_view_post_success_htmx(self):
        report_to_delete = DailyReport.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dailyreport_delete', args=[report_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDailyReportList')
        self.assertFalse(DailyReport.objects.filter(id=report_to_delete.id).exists())
        self.assertEqual(DailyReport.objects.count(), 0)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already incorporate HTMX for:

*   **Dynamic Loading:** The `_dailyreport_table.html` partial is loaded via `hx-get` on `list.html` load and `refreshDailyReportList` event.
*   **Modal Forms:** Add/Edit/Delete buttons use `hx-get` to load `_dailyreport_form.html` or `_dailyreport_confirm_delete.html` into a modal.
*   **Form Submission:** Forms use `hx-post` with `hx-swap="none"` and rely on the 204 No Content response and `HX-Trigger` header to refresh the table.
*   **Loading Indicators:** `htmx-indicator` class is used for a visual loading spinner during form submission.

Alpine.js (`_="on click add .is-active to #modal"`) is used for simple UI state management, specifically to toggle the visibility of the modal when HTMX requests are initiated or the modal is clicked outside.

DataTables integration is explicit in `_dailyreport_table.html` with the `$(document).ready()` block, ensuring client-side searching, sorting, and pagination.

### Final Notes

This comprehensive plan provides a clear, actionable path for migrating your ASP.NET Daily Reporting Tracker System to Django.

**Key Modernization Benefits:**

*   **Scalability & Maintainability:** Moving from legacy ASP.NET Web Forms to Django promotes a more organized, maintainable, and scalable architecture with clear separation of concerns (Model-View-Template).
*   **Developer Efficiency:** Django's "batteries-included" approach, powerful ORM, and robust testing framework significantly boost developer productivity and reduce manual coding.
*   **Performance:** HTMX and Alpine.js enable highly interactive and responsive user interfaces without the overhead of heavy JavaScript frameworks, leading to faster perceived performance and reduced server load.
*   **Security:** Django's built-in security features (e.g., CSRF protection, SQL injection prevention via ORM) inherently improve the application's security posture compared to manually constructed SQL queries.
*   **Cost Savings:** Automated migration reduces the manual effort and potential for errors associated with re-writing code from scratch, translating into significant cost savings and faster time-to-market.

**Further Recommendations for Full Modernization:**

1.  **Database Normalization:** The current `DailyReport` model stores `employee_name`, `designation_name`, and `department_name` as plain `CharField`s, mirroring the legacy ASP.NET's denormalized storage. For a truly modern and efficient database design, it's highly recommended to convert these fields in the `DRT_Sys_New` table to store **Foreign Keys** referencing the `ID` (primary key) of `tblHR_OfficeStaff`, `tblHR_Designation`, and `tblHR_Departments` respectively. This would involve a database migration and changing the `DailyReport` model to use `models.ForeignKey` fields, e.g., `employee = models.ForeignKey(Employee, on_delete=models.PROTECT, db_column='E_name_FK')`.
2.  **Authentication and Authorization:** The original code doesn't show user management. Integrating Django's robust built-in authentication system for login, roles, and permissions is crucial for any production application.
3.  **Error Handling & Logging:** Implement comprehensive error logging (e.g., using Django's built-in logging) beyond simple `try-catch` blocks to monitor application health and debug issues effectively.
4.  **Asynchronous Operations:** For long-running tasks (e.g., complex report generation), consider integrating a task queue like Celery to process them asynchronously, improving user experience.
5.  **Environment Management:** Use `python-dotenv` or similar tools for managing environment variables (like database connection strings) securely and easily across different deployment environments.