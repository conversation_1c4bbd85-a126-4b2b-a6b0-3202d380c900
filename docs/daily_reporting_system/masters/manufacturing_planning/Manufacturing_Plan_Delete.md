This ASP.NET application snippet appears to be a placeholder or a very early stage of development for a "Manufacturing Plan Delete" page within a larger "Daily Reporting System." Crucially, the provided `.aspx` file contains no specific user interface elements, and the C# code-behind file has only an empty `Page_Load` method. This means there's no direct business logic, database interaction, or UI components to *convert*.

However, the file names (e.g., `Manufacturing_Plan_Delete.aspx.cs`, `Module_DailyReportingSystem_Masters_Manufacturing_Plan_Delete`) strongly suggest the existence of a core entity: a "Manufacturing Plan." Given the context of a delete page, it's reasonable to infer a full suite of CRUD (Create, Read, Update, Delete) operations for this entity.

Therefore, this modernization plan will focus on establishing a robust Django 5.0+ solution for managing "Manufacturing Plans" using the recommended modern architecture (fat models, thin views, HTMX, Alpine.js, DataTables), even though the specific ASP.NET implementation details are missing. This approach demonstrates how to systematically build out a module from its foundational entity, ready for AI-assisted code generation for more complex business rules once they are identified.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code contains no database interaction or UI elements that bind to data, we infer a database table based on the naming convention of the ASP.NET file.

-   **Inferred Table Name:** `Manufacturing_Plan`
-   **Inferred Column Names & Data Types:** To demonstrate a complete CRUD flow, we will assume a few essential columns for a "Manufacturing Plan" entity. These would typically be derived from actual database schema analysis in a real migration.
    *   `id` (Primary Key, handled by Django automatically)
    *   `plan_name` (Text, e.g., `VARCHAR(255)`)
    *   `start_date` (Date)
    *   `end_date` (Date)
    *   `quantity_produced` (Integer)
    *   `is_active` (Boolean, for logical deletion or status)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET code provides no explicit backend functionality (e.g., no SQL commands, data sources, or event handlers beyond an empty `Page_Load`). However, the file name `Manufacturing_Plan_Delete.aspx.cs` strongly implies that a "Delete" operation is part of the intended functionality for a `Manufacturing Plan` entity. For a complete module, we will assume the standard CRUD operations:

-   **Create:** Ability to add new manufacturing plans.
-   **Read:** Ability to view a list of all manufacturing plans.
-   **Update:** Ability to modify existing manufacturing plans.
-   **Delete:** Ability to remove manufacturing plans (as directly implied by the file name).

No specific validation logic is present in the ASP.NET code; standard form validation will be applied in Django.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

As the `.aspx` file is empty of specific controls, we will infer the typical UI components required for a CRUD application of "Manufacturing Plans."

-   **List View:** A table (Django template with DataTables) to display all manufacturing plans, with columns for `Plan Name`, `Start Date`, `End Date`, `Quantity Produced`, and `Actions` (Edit, Delete buttons).
-   **Add/Edit Form:** A modal form (Django form rendered in a partial template, loaded via HTMX) with input fields for `Plan Name` (text box), `Start Date` (date picker), `End Date` (date picker), `Quantity Produced` (number input), and `Is Active` (checkbox).
-   **Delete Confirmation:** A modal (partial template, loaded via HTMX) to confirm deletion.

All interactions will be handled with HTMX and Alpine.js to provide a modern, dynamic user experience without full page reloads.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

We will create a `ManufacturingPlan` model, mapping to the inferred `Manufacturing_Plan` table. We'll include a simple business logic method `is_currently_active` to demonstrate the "fat model" principle.

```python
# dailyreporting/models.py
from django.db import models

class ManufacturingPlan(models.Model):
    # Django will automatically create an 'id' primary key field unless 'primary_key=True' is specified on another field.
    plan_name = models.CharField(db_column='plan_name', max_length=255, verbose_name='Plan Name')
    start_date = models.DateField(db_column='start_date', verbose_name='Start Date')
    end_date = models.DateField(db_column='end_date', verbose_name='End Date')
    quantity_produced = models.IntegerField(db_column='quantity_produced', verbose_name='Quantity Produced')
    is_active = models.BooleanField(db_column='is_active', default=True, verbose_name='Is Active')

    class Meta:
        managed = False  # Tells Django not to manage table creation/alteration
        db_table = 'Manufacturing_Plan'  # Maps to the existing database table name
        verbose_name = 'Manufacturing Plan'
        verbose_name_plural = 'Manufacturing Plans'
        ordering = ['-start_date', 'plan_name'] # Default ordering

    def __str__(self):
        return f"{self.plan_name} ({self.start_date.year})"
        
    # Business logic methods go here (Fat Model)
    def is_currently_active(self):
        """
        Checks if the manufacturing plan is currently active based on its dates and active status.
        """
        from datetime import date
        today = date.today()
        return self.is_active and (self.start_date <= today <= self.end_date)

    def mark_as_inactive(self):
        """
        Marks the manufacturing plan as inactive.
        """
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be used for `ManufacturingPlan` to simplify form creation and validation. Widgets will be customized with Tailwind CSS classes for consistent styling.

```python
# dailyreporting/forms.py
from django import forms
from .models import ManufacturingPlan

class ManufacturingPlanForm(forms.ModelForm):
    class Meta:
        model = ManufacturingPlan
        fields = ['plan_name', 'start_date', 'end_date', 'quantity_produced', 'is_active']
        widgets = {
            'plan_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Q1 Production Plan'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date' # HTML5 date input
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date' # HTML5 date input
            }),
            'quantity_produced': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 1000'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'
            }),
        }
        
    # Add custom validation methods here if needed (e.g., end_date > start_date)
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            self.add_error('end_date', 'End date cannot be before start date.')
        
        return cleaned_data

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Class-based views will be used, adhering to the "thin view" principle by delegating business logic to the model or relying on Django's built-in `ModelForm` capabilities. HTMX-specific headers are used for dynamic refreshes. A `TablePartialView` is added to serve the DataTables content dynamically.

```python
# dailyreporting/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ManufacturingPlan
from .forms import ManufacturingPlanForm

class ManufacturingPlanListView(ListView):
    model = ManufacturingPlan
    template_name = 'dailyreporting/manufacturingplan/list.html'
    context_object_name = 'manufacturing_plans'

class ManufacturingPlanTablePartialView(ListView):
    model = ManufacturingPlan
    template_name = 'dailyreporting/manufacturingplan/_manufacturingplan_table.html'
    context_object_name = 'manufacturing_plans'
    # No custom logic needed, ListView handles fetching objects.

class ManufacturingPlanCreateView(CreateView):
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'dailyreporting/manufacturingplan/_manufacturingplan_form.html' # Use partial for modal
    success_url = reverse_lazy('manufacturingplan_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content and trigger HX-Trigger for HTMX refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList'
                }
            )
        return response

class ManufacturingPlanUpdateView(UpdateView):
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'dailyreporting/manufacturingplan/_manufacturingplan_form.html' # Use partial for modal
    context_object_name = 'manufacturing_plan'
    success_url = reverse_lazy('manufacturingplan_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content and trigger HX-Trigger for HTMX refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList'
                }
            )
        return response

class ManufacturingPlanDeleteView(DeleteView):
    model = ManufacturingPlan
    template_name = 'dailyreporting/manufacturingplan/_manufacturingplan_confirm_delete.html' # Use partial for modal
    context_object_name = 'manufacturing_plan'
    success_url = reverse_lazy('manufacturingplan_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        # Business logic for deletion (e.g., soft delete) could go in model method
        # self.get_object().mark_as_inactive() # Example of fat model usage
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Manufacturing Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            # Return 204 No Content and trigger HX-Trigger for HTMX refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates are designed for HTMX and Alpine.js integration. The main list view (`list.html`) contains the modal structure, and the table, form, and delete confirmation are rendered as partials (`_*.html`) for dynamic loading via HTMX.

List Template (`dailyreporting/manufacturingplan/list.html`):

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Manufacturing Plans</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'manufacturingplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Plan
        </button>
    </div>
    
    <div id="manufacturingplanTable-container"
         hx-trigger="load, refreshManufacturingPlanList from:body"
         hx-get="{% url 'manufacturingplan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading manufacturing plans...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            // Activate modal after content is swapped
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modalContent').classList.remove('scale-95', 'opacity-0');
            document.getElementById('modalContent').classList.add('scale-100', 'opacity-100');
        }
    });

    document.addEventListener('htmx:beforeSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // Hide modal before swapping (for 204 No Content response)
            document.getElementById('modalContent').classList.remove('scale-100', 'opacity-100');
            document.getElementById('modalContent').classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                document.getElementById('modal').classList.remove('is-active');
                document.getElementById('modalContent').innerHTML = ''; // Clear content
            }, 300); // Allow transition to complete
        }
    });

    // Handle Alpine.js initialization if any components were defined
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
    });
</script>
{% endblock %}
```

Table Partial Template (`dailyreporting/manufacturingplan/_manufacturingplan_table.html`):

```html
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <table id="manufacturingplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity Produced</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for plan in manufacturing_plans %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ plan.plan_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ plan.start_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ plan.end_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ plan.quantity_produced }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    {% if plan.is_currently_active %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                    {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-4"
                        hx-get="{% url 'manufacturingplan_edit' plan.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'manufacturingplan_delete' plan.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#manufacturingplanTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 6] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

Form Partial Template (`dailyreporting/manufacturingplan/_manufacturingplan_form.html`):

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Manufacturing Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                {% if field.field.required %}
                    <span class="text-red-500">*</span>
                {% endif %}
            </label>
            <div class="mt-1">
                {{ field }}
            </div>
            {% if field.help_text %}
            <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <ul class="mt-1 text-sm text-red-600 list-disc list-inside">
                {% for error in field.errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        {% endfor %}

        {% if form.non_field_errors %}
            <div class="rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94l-1.72-1.72z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul role="list" class="list-disc pl-5 space-y-1">
                                {% for error in form.non_field_errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Plan
            </button>
        </div>
    </form>
</div>
```

Delete Confirmation Partial Template (`dailyreporting/manufacturingplan/_manufacturingplan_confirm_delete.html`):

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the manufacturing plan "<strong>{{ manufacturing_plan.plan_name }}</strong>" starting on {{ manufacturing_plan.start_date|date:"Y-m-d" }}?</p>
    <p class="text-red-600 font-medium mb-6">This action cannot be undone.</p>

    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-delete="{% url 'manufacturingplan_delete' manufacturing_plan.pk %}"
            hx-swap="none"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
            Delete Plan
        </button>
    </div>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns are set up to map to the CBVs, including separate endpoints for the main list view and the HTMX-loaded table partial.

```python
# dailyreporting/urls.py
from django.urls import path
from .views import (
    ManufacturingPlanListView, 
    ManufacturingPlanCreateView, 
    ManufacturingPlanUpdateView, 
    ManufacturingPlanDeleteView,
    ManufacturingPlanTablePartialView
)

urlpatterns = [
    path('manufacturingplans/', ManufacturingPlanListView.as_view(), name='manufacturingplan_list'),
    path('manufacturingplans/add/', ManufacturingPlanCreateView.as_view(), name='manufacturingplan_add'),
    path('manufacturingplans/edit/<int:pk>/', ManufacturingPlanUpdateView.as_view(), name='manufacturingplan_edit'),
    path('manufacturingplans/delete/<int:pk>/', ManufacturingPlanDeleteView.as_view(), name='manufacturingplan_delete'),
    # HTMX-specific endpoint for the table partial
    path('manufacturingplans/table/', ManufacturingPlanTablePartialView.as_view(), name='manufacturingplan_table'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `ManufacturingPlan` model and integration tests for all CRUD views are provided to ensure functionality and maintain high test coverage.

```python
# dailyreporting/tests.py
from datetime import date, timedelta
from django.test import TestCase, Client
from django.urls import reverse
from .models import ManufacturingPlan

class ManufacturingPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.plan1 = ManufacturingPlan.objects.create(
            plan_name='Q1 2024 Production',
            start_date=date(2024, 1, 1),
            end_date=date(2024, 3, 31),
            quantity_produced=1000,
            is_active=True
        )
        cls.plan2 = ManufacturingPlan.objects.create(
            plan_name='Q4 2023 Shutdown',
            start_date=date(2023, 10, 1),
            end_date=date(2023, 12, 31),
            quantity_produced=0,
            is_active=False
        )
  
    def test_manufacturing_plan_creation(self):
        self.assertEqual(self.plan1.plan_name, 'Q1 2024 Production')
        self.assertEqual(self.plan1.quantity_produced, 1000)
        self.assertTrue(self.plan1.is_active)
        self.assertEqual(self.plan2.plan_name, 'Q4 2023 Shutdown')
        self.assertFalse(self.plan2.is_active)
        
    def test_plan_name_label(self):
        field_label = self.plan1._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')
        
    def test_quantity_produced_label(self):
        field_label = self.plan1._meta.get_field('quantity_produced').verbose_name
        self.assertEqual(field_label, 'Quantity Produced')

    def test_str_representation(self):
        expected_str = f"{self.plan1.plan_name} ({self.plan1.start_date.year})"
        self.assertEqual(str(self.plan1), expected_str)

    def test_is_currently_active_method(self):
        # Test for plan that should be active
        test_plan_active = ManufacturingPlan.objects.create(
            plan_name='Test Active Plan',
            start_date=date.today() - timedelta(days=5),
            end_date=date.today() + timedelta(days=5),
            quantity_produced=50,
            is_active=True
        )
        self.assertTrue(test_plan_active.is_currently_active())

        # Test for plan that is in future
        test_plan_future = ManufacturingPlan.objects.create(
            plan_name='Test Future Plan',
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=20),
            quantity_produced=50,
            is_active=True
        )
        self.assertFalse(test_plan_future.is_currently_active())

        # Test for plan that is in past
        test_plan_past = ManufacturingPlan.objects.create(
            plan_name='Test Past Plan',
            start_date=date.today() - timedelta(days=20),
            end_date=date.today() - timedelta(days=10),
            quantity_produced=50,
            is_active=True
        )
        self.assertFalse(test_plan_past.is_currently_active())
        
        # Test for inactive plan within date range
        test_plan_inactive_in_range = ManufacturingPlan.objects.create(
            plan_name='Test Inactive Plan',
            start_date=date.today() - timedelta(days=5),
            end_date=date.today() + timedelta(days=5),
            quantity_produced=50,
            is_active=False
        )
        self.assertFalse(test_plan_inactive_in_range.is_currently_active())

    def test_mark_as_inactive_method(self):
        plan_to_deactivate = ManufacturingPlan.objects.create(
            plan_name='Temp Plan',
            start_date=date.today(),
            end_date=date.today(),
            quantity_produced=10,
            is_active=True
        )
        self.assertTrue(plan_to_deactivate.is_active)
        self.assertTrue(plan_to_deactivate.mark_as_inactive())
        plan_to_deactivate.refresh_from_db()
        self.assertFalse(plan_to_deactivate.is_active)
        # Test calling again on an already inactive plan
        self.assertFalse(plan_to_deactivate.mark_as_inactive())


class ManufacturingPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.plan = ManufacturingPlan.objects.create(
            plan_name='Test Plan',
            start_date=date(2024, 7, 1),
            end_date=date(2024, 7, 31),
            quantity_produced=500,
            is_active=True
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('manufacturingplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/manufacturingplan/list.html')
        self.assertIn('manufacturing_plans', response.context)
        self.assertContains(response, 'Test Plan') # Check if object name is present
        
    def test_table_partial_view(self):
        response = self.client.get(reverse('manufacturingplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/manufacturingplan/_manufacturingplan_table.html')
        self.assertIn('manufacturing_plans', response.context)
        self.assertContains(response, 'Test Plan')

    def test_create_view_get(self):
        response = self.client.get(reverse('manufacturingplan_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/manufacturingplan/_manufacturingplan_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_success(self):
        initial_count = ManufacturingPlan.objects.count()
        data = {
            'plan_name': 'New Test Plan',
            'start_date': '2025-01-01',
            'end_date': '2025-03-31',
            'quantity_produced': 750,
            'is_active': 'on'
        }
        response = self.client.post(reverse('manufacturingplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(ManufacturingPlan.objects.filter(plan_name='New Test Plan').exists())
        self.assertEqual(ManufacturingPlan.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')
        
    def test_create_view_post_invalid(self):
        initial_count = ManufacturingPlan.objects.count()
        data = {
            'plan_name': '', # Invalid data
            'start_date': '2025-01-01',
            'end_date': '2024-12-31', # End date before start date
            'quantity_produced': 'abc', # Invalid number
            'is_active': 'on'
        }
        response = self.client.post(reverse('manufacturingplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'dailyreporting/manufacturingplan/_manufacturingplan_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End date cannot be before start date.')
        self.assertContains(response, 'Enter a whole number.')
        self.assertEqual(ManufacturingPlan.objects.count(), initial_count) # No object created

    def test_update_view_get(self):
        response = self.client.get(reverse('manufacturingplan_edit', args=[self.plan.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/manufacturingplan/_manufacturingplan_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.plan)
        
    def test_update_view_post_success(self):
        data = {
            'plan_name': 'Updated Plan Name',
            'start_date': '2024-07-01',
            'end_date': '2024-08-31',
            'quantity_produced': 600,
            'is_active': 'on'
        }
        response = self.client.post(reverse('manufacturingplan_edit', args=[self.plan.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, 'Updated Plan Name')
        self.assertEqual(self.plan.quantity_produced, 600)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

    def test_delete_view_get(self):
        response = self.client.get(reverse('manufacturingplan_delete', args=[self.plan.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/manufacturingplan/_manufacturingplan_confirm_delete.html')
        self.assertIn('manufacturing_plan', response.context)
        self.assertEqual(response.context['manufacturing_plan'], self.plan)
        
    def test_delete_view_post_success(self):
        plan_to_delete = ManufacturingPlan.objects.create(
            plan_name='Temp Delete Plan',
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 1),
            quantity_produced=10
        )
        initial_count = ManufacturingPlan.objects.count()
        response = self.client.delete(reverse('manufacturingplan_delete', args=[plan_to_delete.pk]), HTTP_HX_REQUEST='true') # Use DELETE method for DeleteView
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertFalse(ManufacturingPlan.objects.filter(pk=plan_to_delete.pk).exists())
        self.assertEqual(ManufacturingPlan.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for dynamic updates:** All CRUD operations (add, edit, delete) use HTMX to submit forms or trigger actions. Upon success, the server responds with a `204 No Content` status and an `HX-Trigger` header (`refreshManufacturingPlanList`). This signal is caught by the main list view's `hx-trigger` on the `manufacturingplanTable-container` div, causing it to re-fetch and re-render the `_manufacturingplan_table.html` partial, updating the list without a full page reload.
-   **Modal Management with Alpine.js & HTMX:**
    -   Buttons for "Add New Plan," "Edit," and "Delete" use `hx-get` to fetch their respective forms (`_manufacturingplan_form.html` or `_manufacturingplan_confirm_delete.html`) and load them into the `#modalContent` div.
    -   The `_` (Alpine.js shortcut) attribute on these buttons adds the `is-active` class to the `#modal` div, making it visible.
    -   An Alpine.js `on click` listener on the `#modal` itself allows clicking outside the modal content to close it by removing the `is-active` class.
    -   Custom JavaScript in `list.html` uses `htmx:afterSwap` and `htmx:beforeSwap` events to manage modal visibility and animations (scaling, opacity) smoothly.
-   **DataTables for List Views:** The `_manufacturingplan_table.html` partial includes a JavaScript snippet to initialize DataTables on the rendered table. This provides out-of-the-box client-side searching, sorting, and pagination without requiring custom Django view logic.
-   **DRY Templates:** By separating the table, form, and delete confirmation into partials (`_*.html`), we prevent code duplication and enable modular, dynamic loading of these components.

## Final Notes

This comprehensive Django modernization plan provides a robust, modern foundation for the "Manufacturing Plan" module. By adhering to the "Fat Model, Thin View" paradigm, strictly separating concerns, and leveraging HTMX, Alpine.js, and DataTables, the resulting application is highly maintainable, scalable, and offers a smooth user experience. This structure is ideal for further AI-assisted automation, as more complex business logic can be incrementally added to the models, and new UI components can be generated as partials, all integrating seamlessly into this architectural pattern.