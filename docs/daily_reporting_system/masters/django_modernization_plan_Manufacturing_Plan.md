## ASP.NET to Django Conversion Script:

This modernization plan outlines the transition of your legacy ASP.NET application, specifically the Manufacturing Plan module, to a robust and scalable Django 5.0+ solution. We will leverage AI-assisted automation to systematically convert your existing functionalities, focusing on modern web development practices like HTMX, Alpine.js, and DataTables for a dynamic user experience without complex JavaScript frameworks.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Benefit:** This step ensures that our new Django application accurately reflects your existing database structure, minimizing data migration complexities and preserving data integrity. It's the foundation for a smooth transition without losing valuable information.

**Analysis:**
The ASP.NET code-behind's `submit_Click` method provides a clear `INSERT` statement, revealing the target table and its columns. The table name is `DRTS_Manufacturing_Plan_New`.

**Identified Table and Columns:**
- **Table Name:** `DRTS_Manufacturing_Plan_New`
- **Columns:**
    - `Id` (Primary Key, implicitly handled by Django's `AutoField`)
    - `WONO` (Work Order Number)
    - `FIXTURE_NO`
    - `ITEM_NO`
    - `DESCRIPTION`
    - `QTY` (Quantity)
    - `DETAILING` (Drawing Release - Detailing)
    - `TPL_ENTRY` (Drawing Release - TPL Entry)
    - `FLAME_CUT` (Drawing Release - Flame Cut)
    - `C_FLAME_CUT` (Cutting - Flame Cut)
    - `CHANNLEL` (Cutting - Channlel)
    - `LIST` (Raw Material - List)
    - `RECEIVE` (Raw Material - Receive)
    - `FABRICATION`
    - `C_SR` (SR - Self-Reliance?)
    - `MC_ING` (Machining)
    - `TAPPING`
    - `PAINTING`

### Step 2: Identify Backend Functionality

**Business Benefit:** Understanding existing operations helps us replicate core business logic accurately. By knowing what your old system *does*, we can ensure the new Django application *does it better*, with improved efficiency and maintainability.

**Analysis:**
The provided ASP.NET code focuses exclusively on the **Create** operation for a "Manufacturing Plan" record.
- **Create:** Triggered by the `submit` button's `submit_Click` event. It collects data from various text boxes and inserts it into the `DRTS_Manufacturing_Plan_New` table.
- **Validation:** A client-side `alert` is shown if any field is empty.
- **Primary Key Management:** The ASP.NET code manually increments an `Id` field based on `COUNT(Id)`. This is a critical point of modernization as Django's ORM handles auto-incrementing primary keys much more robustly, preventing potential data integrity issues.
- **Data Transformation:** `ITEM_NO` and `DESCRIPTION` are converted to uppercase before insertion.

### Step 3: Infer UI Components

**Business Benefit:** By mapping old UI elements to modern counterparts, we streamline the user experience. The goal is to move from dated ASP.NET Web Forms to a fluid, interactive Django interface that's faster and more intuitive for your team.

**Analysis:**
The `.aspx` file uses a series of `asp:Label` and `asp:TextBox` controls, organized within `asp:Table` elements for layout.
- **Input Fields:** All data input is via `asp:TextBox` controls, suggesting all fields can be treated as text-based inputs initially. `QTY` can be inferred as an integer.
- **Action Buttons:** A single `asp:Button` labeled "Submit" is present for saving data.
- **Layout:** Heavy reliance on `asp:Table` for structuring the form.
- **No List View:** The current `.aspx` page does not display a list of existing manufacturing plans. For the Django modernization, we will implement a list view using DataTables, which is a standard requirement for such data entry modules.

### Step 4: Generate Django Code

We will now generate the complete Django application files for your Manufacturing Plan module. This section includes all necessary code for models, forms, views, templates, and tests, ready for integration into your Django project.

**App Name:** `manufacturing`

#### 4.1 Models

**File:** `manufacturing/models.py`

**Business Benefit:** Models are the backbone of your data. By defining them clearly in Django, we ensure your application's data structure is robust, scalable, and directly maps to your existing database, protecting your investment in historical data.

```python
from django.db import models

class ManufacturingPlan(models.Model):
    # Django automatically creates an 'id' AutoField as the primary key.
    # The existing 'Id' column in the database will be mapped to this default PK.
    # If the existing 'Id' was not an auto-incrementing integer, specific mapping would be needed.

    wono = models.CharField(max_length=255, db_column='WONO', verbose_name='W/O No.')
    fixture_no = models.CharField(max_length=255, db_column='FIXTURE_NO', verbose_name='Fixture No.')
    item_no = models.CharField(max_length=255, db_column='ITEM_NO', verbose_name='Item No.')
    description = models.CharField(max_length=255, db_column='DESCRIPTION', verbose_name='Description')
    qty = models.IntegerField(db_column='QTY', verbose_name='Quantity')
    detailing = models.CharField(max_length=255, db_column='DETAILING', verbose_name='Detailing')
    tpl_entry = models.CharField(max_length=255, db_column='TPL_ENTRY', verbose_name='TPL Entry')
    flame_cut = models.CharField(max_length=255, db_column='FLAME_CUT', verbose_name='Flame Cut (Drawing Release)')
    c_flame_cut = models.CharField(max_length=255, db_column='C_FLAME_CUT', verbose_name='Flame Cut (Cutting)')
    channlel = models.CharField(max_length=255, db_column='CHANNLEL', verbose_name='Channlel')
    list_rm = models.CharField(max_length=255, db_column='LIST', verbose_name='Raw Material List')
    receive_rm = models.CharField(max_length=255, db_column='RECEIVE', verbose_name='Raw Material Receive')
    fabrication = models.CharField(max_length=255, db_column='FABRICATION', verbose_name='Fabrication')
    c_sr = models.CharField(max_length=255, db_column='C_SR', verbose_name='SR')
    mc_ing = models.CharField(max_length=255, db_column='MC_ING', verbose_name='M/C Ing')
    tapping = models.CharField(max_length=255, db_column='TAPPING', verbose_name='Tapping')
    painting = models.CharField(max_length=255, db_column='PAINTING', verbose_name='Painting')

    class Meta:
        # managed = False tells Django not to manage the table schema (e.g., migrations won't create/alter it).
        # This is crucial when connecting to an existing database.
        managed = False
        db_table = 'DRTS_Manufacturing_Plan_New'
        verbose_name = 'Manufacturing Plan'
        verbose_name_plural = 'Manufacturing Plans'

    def __str__(self):
        return f"Plan for W/O No.: {self.wono} - Item: {self.item_no}"
        
    def save(self, *args, **kwargs):
        """
        Business logic: Convert ITEM_NO and DESCRIPTION to uppercase, as seen in ASP.NET code.
        This ensures data consistency as per legacy system behavior.
        """
        self.item_no = self.item_no.upper() if self.item_no else ''
        self.description = self.description.upper() if self.description else ''
        super().save(*args, **kwargs)

    @classmethod
    def get_all_plans(cls):
        """
        Model method to retrieve all manufacturing plans.
        Keeps query logic within the model, supporting thin views.
        """
        return cls.objects.all()

    def get_absolute_url(self):
        """
        Returns the URL to access a particular instance of ManufacturingPlan.
        """
        from django.urls import reverse
        return reverse('manufacturingplan_edit', args=[str(self.id)])

```

#### 4.2 Forms

**File:** `manufacturing/forms.py`

**Business Benefit:** Forms provide a clean and secure way to handle user input. By using Django's built-in forms, we ensure data validation, security, and consistent styling, leading to a more reliable and user-friendly experience.

```python
from django import forms
from .models import ManufacturingPlan

class ManufacturingPlanForm(forms.ModelForm):
    class Meta:
        model = ManufacturingPlan
        fields = [
            'wono', 'fixture_no', 'item_no', 'description', 'qty',
            'detailing', 'tpl_entry', 'flame_cut', 'c_flame_cut', 'channlel',
            'list_rm', 'receive_rm', 'fabrication', 'c_sr', 'mc_ing', 'tapping', 'painting'
        ]
        widgets = {
            'wono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fixture_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'detailing': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tpl_entry': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'flame_cut': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'c_flame_cut': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'channlel': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'list_rm': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'receive_rm': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fabrication': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'c_sr': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mc_ing': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tapping': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'painting': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'list_rm': 'List', # Override verbose_name for brevity if needed
            'receive_rm': 'Receive',
        }

    def clean_qty(self):
        qty = self.cleaned_data['qty']
        if qty is not None and qty < 0:
            raise forms.ValidationError("Quantity cannot be negative.")
        return qty

    # Example of a custom validation method if needed for other fields
    # def clean_wono(self):
    #     wono = self.cleaned_data['wono']
    #     if len(wono) < 3:
    #         raise forms.ValidationError("W/O Number must be at least 3 characters long.")
    #     return wono
```

#### 4.3 Views

**File:** `manufacturing/views.py`

**Business Benefit:** Views in Django act as the traffic controllers. Our "thin view" approach ensures that business logic stays in models, making views concise, easier to test, and more resilient to change. This translates to faster development cycles and reduced maintenance costs. HTMX integration means a snappier user interface without full page reloads, improving user satisfaction.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ManufacturingPlan
from .forms import ManufacturingPlanForm

# Max 15 lines per view method (excluding imports, class definition, and meta attributes)

class ManufacturingPlanListView(ListView):
    """
    Displays a list of all Manufacturing Plans.
    """
    model = ManufacturingPlan
    template_name = 'manufacturing/manufacturingplan/list.html'
    context_object_name = 'manufacturingplans'

    def get_queryset(self):
        """
        Retrieves all manufacturing plans using a model method.
        """
        return ManufacturingPlan.get_all_plans()

class ManufacturingPlanTablePartialView(ListView):
    """
    Renders only the table portion for HTMX requests to refresh the list.
    """
    model = ManufacturingPlan
    template_name = 'manufacturing/manufacturingplan/_manufacturingplan_table.html'
    context_object_name = 'manufacturingplans'

    def get_queryset(self):
        """
        Retrieves all manufacturing plans for the table.
        """
        return ManufacturingPlan.get_all_plans()

class ManufacturingPlanCreateView(CreateView):
    """
    Handles the creation of a new Manufacturing Plan.
    """
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'manufacturing/manufacturingplan/_manufacturingplan_form.html'
    success_url = reverse_lazy('manufacturingplan_list') # Redirect after successful creation

    def form_valid(self, form):
        """
        Handles valid form submission. Adds a success message and triggers HTMX refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshManufacturingPlanList'})
        return response

class ManufacturingPlanUpdateView(UpdateView):
    """
    Handles the updating of an existing Manufacturing Plan.
    """
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'manufacturing/manufacturingplan/_manufacturingplan_form.html'
    success_url = reverse_lazy('manufacturingplan_list') # Redirect after successful update

    def form_valid(self, form):
        """
        Handles valid form submission. Adds a success message and triggers HTMX refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshManufacturingPlanList'})
        return response

class ManufacturingPlanDeleteView(DeleteView):
    """
    Handles the deletion of a Manufacturing Plan.
    """
    model = ManufacturingPlan
    template_name = 'manufacturing/manufacturingplan/_manufacturingplan_confirm_delete.html'
    success_url = reverse_lazy('manufacturingplan_list') # Redirect after successful deletion

    def delete(self, request, *args, **kwargs):
        """
        Handles deletion and triggers HTMX refresh.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Manufacturing Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshManufacturingPlanList'})
        return response

```

#### 4.4 Templates

**Directory:** `manufacturing/templates/manufacturing/manufacturingplan/`

**Business Benefit:** Modern templates provide a seamless user experience. By adopting HTMX and Alpine.js, we eliminate full page reloads, making your application feel faster and more responsive, akin to single-page applications but with the simplicity of traditional web development.

**File:** `manufacturing/templates/manufacturing/manufacturingplan/list.html`

```html
{% extends 'core/base.html' %}

{% block title %}Manufacturing Plans{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Manufacturing Plans</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'manufacturingplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Manufacturing Plan
        </button>
    </div>
    
    <!-- Messages container for Django messages -->
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 rounded-md {% if message.tags %}bg-{{ message.tags }}-100 text-{{ message.tags }}-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>

    <div id="manufacturingplanTable-container"
         hx-trigger="load, refreshManufacturingPlanList from:body"
         hx-get="{% url 'manufacturingplan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex items-center justify-center h-48 bg-gray-50 rounded-lg shadow-inner">
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Manufacturing Plans...</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-4xl w-full mx-4 my-8"
             _="on htmx:afterOnLoad add .is-active to #modal then add .is-active to me then set showModal to true">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for broader UI state.
        // The modal visibility is handled by HTMX's _ attributes and Alpine's x-show.
    });

    // Listen for HX-Trigger event for messages
    document.body.addEventListener('htmx:trigger', function(event) {
        if (event.detail.trigger === 'refreshManufacturingPlanList') {
            // Optional: You can also handle displaying success messages more dynamically if needed
            // For now, Django's messages framework handles it on full reload or via HTMX triggers.
        }
    });

    // Close modal on success if form submission is from a modal
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.elt.closest('#modalContent') && event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**File:** `manufacturing/templates/manufacturing/manufacturingplan/_manufacturingplan_table.html`

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="manufacturingplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">W/O No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fixture No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in manufacturingplans %}
            <tr class="hover:bg-gray-100">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.wono }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.fixture_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.item_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.description }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'manufacturingplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'manufacturingplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center text-gray-500">No manufacturing plans found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#manufacturingplanTable')) {
            $('#manufacturingplanTable').DataTable().destroy();
        }
        $('#manufacturingplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false
        });
    });
</script>
```

**File:** `manufacturing/templates/manufacturing/manufacturingplan/_manufacturingplan_form.html`

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Manufacturing Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save Manufacturing Plan
            </button>
        </div>
        <!-- HTMX indicator -->
        <div id="form-indicator" class="htmx-indicator mt-4 text-center text-blue-500">
            <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            <span class="ml-2">Saving...</span>
        </div>
    </form>
</div>
```

**File:** `manufacturing/templates/manufacturing/manufacturingplan/_manufacturingplan_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Manufacturing Plan for W/O No. <strong>{{ manufacturingplan.wono }}</strong> - Item: <strong>{{ manufacturingplan.item_no }}</strong>?</p>
    
    <form hx-post="{% url 'manufacturingplan_delete' manufacturingplan.pk %}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
        <!-- HTMX indicator -->
        <div id="delete-indicator" class="htmx-indicator mt-4 text-center text-red-500">
            <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
            <span class="ml-2">Deleting...</span>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**File:** `manufacturing/urls.py`

**Business Benefit:** Clear URL patterns make your application's navigation intuitive and consistent. This organization improves maintainability and allows for easy integration with other parts of your Django system.

```python
from django.urls import path
from .views import (
    ManufacturingPlanListView, ManufacturingPlanCreateView, 
    ManufacturingPlanUpdateView, ManufacturingPlanDeleteView,
    ManufacturingPlanTablePartialView
)

urlpatterns = [
    # List view for all manufacturing plans
    path('manufacturingplans/', ManufacturingPlanListView.as_view(), name='manufacturingplan_list'),
    
    # HTMX partial for the DataTables table
    path('manufacturingplans/table/', ManufacturingPlanTablePartialView.as_view(), name='manufacturingplan_table'),

    # Create new manufacturing plan
    path('manufacturingplans/add/', ManufacturingPlanCreateView.as_view(), name='manufacturingplan_add'),
    
    # Edit existing manufacturing plan
    path('manufacturingplans/<int:pk>/edit/', ManufacturingPlanUpdateView.as_view(), name='manufacturingplan_edit'),
    
    # Delete manufacturing plan
    path('manufacturingplans/<int:pk>/delete/', ManufacturingPlanDeleteView.as_view(), name='manufacturingplan_delete'),
]

```

#### 4.6 Tests

**File:** `manufacturing/tests.py`

**Business Benefit:** Automated tests are critical for software quality. They ensure that your new Django application functions exactly as expected, reducing bugs, speeding up development, and providing confidence in the system's reliability. This proactive approach saves significant time and resources in the long run.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import ManufacturingPlan

class ManufacturingPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test ManufacturingPlan instance for use across all tests
        cls.plan1 = ManufacturingPlan.objects.create(
            wono='WO-001', 
            fixture_no='FX-001', 
            item_no='ITEM-A', 
            description='Test Description A', 
            qty=10,
            detailing='D1', tpl_entry='T1', flame_cut='FC1', c_flame_cut='CFC1',
            channlel='CH1', list_rm='L1', receive_rm='R1', fabrication='FAB1',
            c_sr='CSR1', mc_ing='MC1', tapping='TAP1', painting='PA1'
        )
        cls.plan2 = ManufacturingPlan.objects.create(
            wono='WO-002', 
            fixture_no='FX-002', 
            item_no='ITEM-B', 
            description='Another Description B', 
            qty=20,
            detailing='D2', tpl_entry='T2', flame_cut='FC2', c_flame_cut='CFC2',
            channlel='CH2', list_rm='L2', receive_rm='R2', fabrication='FAB2',
            c_sr='CSR2', mc_ing='MC2', tapping='TAP2', painting='PA2'
        )
  
    def test_manufacturing_plan_creation(self):
        """
        Verify that a ManufacturingPlan instance is created correctly.
        """
        self.assertEqual(self.plan1.wono, 'WO-001')
        self.assertEqual(self.plan1.item_no, 'ITEM-A') # Stored as is, not uppercase yet for initial check
        self.assertEqual(self.plan1.description, 'Test Description A')
        self.assertEqual(self.plan1.qty, 10)
        self.assertEqual(ManufacturingPlan.objects.count(), 2)

    def test_string_representation(self):
        """
        Test the __str__ method of the model.
        """
        expected_str = f"Plan for W/O No.: {self.plan1.wono} - Item: {self.plan1.item_no}"
        self.assertEqual(str(self.plan1), expected_str)
        
    def test_item_no_and_description_uppercasing_on_save(self):
        """
        Test the save method's uppercasing logic.
        """
        new_plan = ManufacturingPlan(
            wono='WO-003', fixture_no='FX-003', item_no='item-c', description='new description c', qty=30,
            detailing='D3', tpl_entry='T3', flame_cut='FC3', c_flame_cut='CFC3',
            channlel='CH3', list_rm='L3', receive_rm='R3', fabrication='FAB3',
            c_sr='CSR3', mc_ing='MC3', tapping='TAP3', painting='PA3'
        )
        new_plan.save() # Call save to trigger the uppercasing
        self.assertEqual(new_plan.item_no, 'ITEM-C')
        self.assertEqual(new_plan.description, 'NEW DESCRIPTION C')

    def test_get_all_plans_method(self):
        """
        Test the custom get_all_plans model method.
        """
        plans = ManufacturingPlan.get_all_plans()
        self.assertEqual(plans.count(), 2)
        self.assertIn(self.plan1, plans)
        self.assertIn(self.plan2, plans)

class ManufacturingPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views tests
        cls.plan = ManufacturingPlan.objects.create(
            wono='WO-VIEW-1', 
            fixture_no='FX-VIEW-1', 
            item_no='VIEW-ITEM-A', 
            description='View Test Desc A', 
            qty=100,
            detailing='VD1', tpl_entry='VT1', flame_cut='VFC1', c_flame_cut='VCFC1',
            channlel='VCH1', list_rm='VL1', receive_rm='VR1', fabrication='VFAB1',
            c_sr='VCSR1', mc_ing='VMC1', tapping='VTAP1', painting='VPA1'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        """
        Test that the list view displays correctly.
        """
        response = self.client.get(reverse('manufacturingplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manufacturing/manufacturingplan/list.html')
        self.assertContains(response, 'Manufacturing Plans')
        self.assertContains(response, self.plan.wono)

    def test_table_partial_view_htmx(self):
        """
        Test that the table partial view responds correctly to HTMX requests.
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manufacturingplan_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manufacturing/manufacturingplan/_manufacturingplan_table.html')
        self.assertContains(response, self.plan.wono)
        # Ensure it does not render the base template
        self.assertNotContains(response, '<!DOCTYPE html>')

    def test_create_view_get(self):
        """
        Test that the create form is rendered correctly.
        """
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX modal request
        response = self.client.get(reverse('manufacturingplan_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manufacturing/manufacturingplan/_manufacturingplan_form.html')
        self.assertContains(response, 'Add Manufacturing Plan')
        self.assertContains(response, '<form hx-post') # Check for HTMX form attribute

    def test_create_view_post_success(self):
        """
        Test successful creation of a new Manufacturing Plan via POST request.
        """
        data = {
            'wono': 'WO-NEW-1', 
            'fixture_no': 'FX-NEW-1', 
            'item_no': 'NEW-ITEM-X', 
            'description': 'New Description X', 
            'qty': 15,
            'detailing':'ND1', 'tpl_entry':'NT1', 'flame_cut':'NFC1', 'c_flame_cut':'NCFC1',
            'channlel':'NCH1', 'list_rm':'NL1', 'receive_rm':'NR1', 'fabrication':'NFAB1',
            'c_sr':'NCSR1', 'mc_ing':'NMC1', 'tapping':'NTAP1', 'painting':'NPA1'
        }
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX form submission
        response = self.client.post(reverse('manufacturingplan_add'), data, **headers)
        
        # For HTMX, a successful POST usually returns 204 No Content
        self.assertEqual(response.status_code, 204) 
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

        # Verify object was created in the database
        new_plan_exists = ManufacturingPlan.objects.filter(wono='WO-NEW-1', item_no='NEW-ITEM-X').exists()
        self.assertTrue(new_plan_exists)

        # Check for success message (messages are handled differently with HTMX 204, but would appear on next page load)
        # For a non-HTMX request, this would be a 302 redirect with a message.
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Manufacturing Plan added successfully.')

    def test_create_view_post_invalid(self):
        """
        Test form submission with invalid data.
        """
        data = { # Missing required fields or invalid data
            'wono': '', 
            'fixture_no': 'FX-NEW-INVALID',
            'qty': -5 # Invalid quantity
            # All other fields also need to be present even if empty strings as per ASP.NET validation
            # For simplicity, will only test one invalid field
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manufacturingplan_add'), data, **headers)
        
        # An invalid form submission should typically return 200 with the form errors
        self.assertEqual(response.status_code, 200) 
        self.assertTemplateUsed(response, 'manufacturing/manufacturingplan/_manufacturingplan_form.html')
        self.assertContains(response, 'Quantity cannot be negative.')
        self.assertContains(response, 'This field is required.') # For 'wono'

        # Verify no new object was created
        self.assertFalse(ManufacturingPlan.objects.filter(fixture_no='FX-NEW-INVALID').exists())

    def test_update_view_get(self):
        """
        Test that the update form is rendered correctly with existing data.
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manufacturingplan_edit', args=[self.plan.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manufacturing/manufacturingplan/_manufacturingplan_form.html')
        self.assertContains(response, 'Edit Manufacturing Plan')
        self.assertContains(response, self.plan.wono) # Check if existing data is pre-filled

    def test_update_view_post_success(self):
        """
        Test successful update of an existing Manufacturing Plan.
        """
        updated_data = {
            'wono': 'WO-UPDATED', 
            'fixture_no': self.plan.fixture_no, 
            'item_no': self.plan.item_no, 
            'description': 'Updated Description', 
            'qty': 110,
            'detailing':self.plan.detailing, 'tpl_entry':self.plan.tpl_entry, 'flame_cut':self.plan.flame_cut, 'c_flame_cut':self.plan.c_flame_cut,
            'channlel':self.plan.channlel, 'list_rm':self.plan.list_rm, 'receive_rm':self.plan.receive_rm, 'fabrication':self.plan.fabrication,
            'c_sr':self.plan.c_sr, 'mc_ing':self.plan.mc_ing, 'tapping':self.plan.tapping, 'painting':self.plan.painting
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manufacturingplan_edit', args=[self.plan.pk]), updated_data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

        # Verify object was updated in the database
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.wono, 'WO-UPDATED')
        self.assertEqual(self.plan.description, 'UPDATED DESCRIPTION') # Check for uppercasing on update

    def test_delete_view_get(self):
        """
        Test that the delete confirmation page is rendered.
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manufacturingplan_delete', args=[self.plan.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manufacturing/manufacturingplan/_manufacturingplan_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.plan.wono)

    def test_delete_view_post_success(self):
        """
        Test successful deletion of a Manufacturing Plan.
        """
        # Create an object specifically for deletion
        plan_to_delete = ManufacturingPlan.objects.create(
            wono='WO-DEL-1', 
            fixture_no='FX-DEL-1', 
            item_no='DEL-ITEM-Z', 
            description='Delete Test Desc Z', 
            qty=5,
            detailing='DD1', tpl_entry='DT1', flame_cut='DFC1', c_flame_cut='DCFC1',
            channlel='DCH1', list_rm='DL1', receive_rm='DR1', fabrication='DFAB1',
            c_sr='DCSR1', mc_ing='DMC1', tapping='DTAP1', painting='DPA1'
        )
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manufacturingplan_delete', args=[plan_to_delete.pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

        # Verify object was deleted from the database
        self.assertFalse(ManufacturingPlan.objects.filter(pk=plan_to_delete.pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Business Benefit:** This is where the application truly feels modern. By eliminating full-page reloads and enabling instant feedback, the system becomes more engaging and efficient for users. It mimics the speed of modern single-page applications without the frontend complexity, allowing your team to focus on core business logic.

**Instructions Compliance:**
- **HTMX for dynamic updates:** All CRUD forms are loaded into a modal using `hx-get` and form submissions use `hx-post` with `hx-swap="none"` and `HX-Trigger` headers for list refresh.
- **Alpine.js for UI state management:** Used for controlling the modal's visibility (`x-data`, `x-show`, `x-on:close-modal.window`) and the `_` (hyperscript) syntax for adding/removing classes.
- **DataTables for list views:** The `_manufacturingplan_table.html` partial includes the DataTables initialization script, ensuring client-side searching, sorting, and pagination.
- **No full page reloads:** All add/edit/delete actions are handled via HTMX, keeping the user on the main list page.
- **DRY template inheritance:** All main templates extend `core/base.html`, which is assumed to contain all CDN links for DataTables, HTMX, Alpine.js, and Tailwind CSS.
- **No custom JavaScript requirements:** The interactions are primarily declarative using HTMX and Alpine.js attributes.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for modernizing your ASP.NET Manufacturing Plan module to Django. By leveraging AI-assisted automation principles, we ensure that the transition is systematic, efficient, and delivers a modern, high-performance application that meets your business needs. Remember to replace placeholders like database connection details and `APP_NAME` in your Django project's `settings.py` (for `DATABASES` and `INSTALLED_APPS`).