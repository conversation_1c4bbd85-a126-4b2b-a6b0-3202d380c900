## ASP.NET to Django Conversion Script:

This modernization plan outlines the strategy to transition your ASP.NET application, specifically the `Design_Plan_Edit.aspx` module, to a modern Django-based solution. Our approach prioritizes automation, clean architecture, and enhanced user experience using Django's capabilities paired with HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

By analyzing the provided ASP.NET code-behind, particularly the `fun.update` function call and the `select count(Id)` query, we've identified the core database entity involved in this module.

-   **Database Table Name:** `DRTS_Desing_Plan_New`
-   **Identified Columns (and their inferred data types):**
    -   `Id`: This appears to be an auto-incrementing integer serving as the primary key. In Django, this will naturally map to the `id` field.
    -   `idwono`: Work Order Number (Text/String)
    -   `idsr`: Serial Number (Text/String)
    -   `idfxn`: Fixture Number (Text/String)
    -   `idconcpd`: Concept Design (Text/String; converted to uppercase in original C# logic)
    -   `idintrnrw`: Internal Review (Text/String; converted to uppercase in original C# logic)
    -   `iddaps`: DAP Send (Text/String)
    -   `iddapr`: DAP Recd. (Text/String)
    -   `idcrr`: Correction (Text/String)
    -   `idfdap`: Final DAP (Text/String)
    -   `idboulst`: Bought List (Text/String)
    -   `iddrwrls`: Drawing Release (Text/String)
    -   `idcncd`: CNC Data (Text/String)
    -   `idcmmdt`: CMM Data (Text/String)
    -   `idftlst`: Fit List (Text/String)
    -   `idmnl`: Manual (Text/String)
    -   `iddtal`: Detailing (Text/String)
    -   `idtpletr`: TPL Entry (Text/String)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The analysis reveals that the current `Design_Plan_Edit.aspx` page, despite its name and the "Update" button, is primarily used for **creating new records**. This is evident from the C# code generating a new `Id` by incrementing the count of existing records (`Id = Id + 1`) and then inserting a new row using the `fun.update` function.

-   **Create:** The page facilitates the creation of new "Design Plan" entries. All specified text fields are gathered and inserted into the `DRTS_Desing_Plan_New` table.
-   **Read:** No explicit read (displaying existing data) functionality is present on this specific page.
-   **Update:** Not implemented on this page. The "Update" button's action is to insert a new record.
-   **Delete:** Not implemented on this page.

**Validation Logic:**
-   The C# code includes basic validation checking if all input fields are non-empty. This indicates that all identified fields are considered mandatory.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The ASP.NET frontend uses standard Web Forms controls, which will be replaced by native HTML elements in Django templates, styled with Tailwind CSS, and made interactive with HTMX and Alpine.js.

-   **`<asp:Label>`:** Used for static text display, such as the page title "DESIGN PLAN" and field labels (e.g., "W/O NO.").
-   **`<asp:TextBox>`:** Employed for all user input fields, corresponding to the identified database columns.
-   **`<asp:Table>`:** Utilized purely for layout and grouping of form elements, not for displaying dynamic data grids. In Django, this will be replaced with modern CSS Grid/Flexbox layouts.
-   **`<asp:Button>`:** Triggers server-side actions, in this case, the `submit_Click` event for data insertion.

**Inferred Field Labels (for Django forms and templates):**

*   Work Order No. (`idwono`)
*   SR.NO. (`idsr`)
*   Fixture No. (`idfxn`)
*   Concept Design (`idconcpd`)
*   Internal Review (`idintrnrw`)
*   DAP Send (`iddaps`)
*   DAP Recd. (`iddapr`)
*   Correction (`idcrr`)
*   Final DAP (`idfdap`)
*   Bought List (`idboulst`)
*   Drawing Release (`iddrwrls`)
*   CNC Data (`idcncd`)
*   CMM Data (`idcmmdt`)
*   Fit List (`idftlst`)
*   Manual (`idmnl`)
*   Detailing (`iddtal`)
*   TPL Entry (`idtpletr`)

No custom client-side JavaScript was found in the ASP.NET code, which is ideal for a straightforward migration to HTMX and Alpine.js for dynamic interactions.

### Step 4: Generate Django Code

We will create a new Django application named `designplan` to encapsulate this module's functionality.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**

The `DesignPlan` model will be defined to represent the `DRTS_Desing_Plan_New` table. We explicitly map Django's `id` field to the existing `Id` database column. All text box inputs are mapped to `CharField` fields and marked as required (`blank=False, null=False`). Business logic from the C# code, such as converting `concept_design` and `internal_review` to uppercase, will be moved into the model's `save` method following the "fat model" principle.

**File:** `designplan/models.py`

```python
from django.db import models

class DesignPlan(models.Model):
    # Explicitly map Django's primary key 'id' to the existing 'Id' column.
    id = models.AutoField(db_column='Id', primary_key=True)
    work_order_no = models.CharField(max_length=255, db_column='idwono', verbose_name='Work Order No.', blank=False, null=False)
    sr_no = models.CharField(max_length=255, db_column='idsr', verbose_name='SR.NO.', blank=False, null=False)
    fixture_no = models.CharField(max_length=255, db_column='idfxn', verbose_name='Fixture No.', blank=False, null=False)
    concept_design = models.CharField(max_length=255, db_column='idconcpd', verbose_name='Concept Design', blank=False, null=False)
    internal_review = models.CharField(max_length=255, db_column='idintrnrw', verbose_name='Internal Review', blank=False, null=False)
    dap_send = models.CharField(max_length=255, db_column='iddaps', verbose_name='DAP Send', blank=False, null=False)
    dap_recd = models.CharField(max_length=255, db_column='iddapr', verbose_name='DAP Recd.', blank=False, null=False)
    correction = models.CharField(max_length=255, db_column='idcrr', verbose_name='Correction', blank=False, null=False)
    final_dap = models.CharField(max_length=255, db_column='idfdap', verbose_name='Final DAP', blank=False, null=False)
    bought_list = models.CharField(max_length=255, db_column='idboulst', verbose_name='Bought List', blank=False, null=False)
    drawing_release = models.CharField(max_length=255, db_column='iddrwrls', verbose_name='Drawing Release', blank=False, null=False)
    cnc_data = models.CharField(max_length=255, db_column='idcncd', verbose_name='CNC Data', blank=False, null=False)
    cmm_data = models.CharField(max_length=255, db_column='idcmmdt', verbose_name='CMM Data', blank=False, null=False)
    fit_list = models.CharField(max_length=255, db_column='idftlst', verbose_name='Fit List', blank=False, null=False)
    manual = models.CharField(max_length=255, db_column='idmnl', verbose_name='Manual', blank=False, null=False)
    detailing = models.CharField(max_length=255, db_column='iddtal', verbose_name='Detailing', blank=False, null=False)
    tpl_entry = models.CharField(max_length=255, db_column='idtpletr', verbose_name='TPL Entry', blank=False, null=False)

    class Meta:
        # 'managed = False' indicates that Django should not create or delete this table
        # as it already exists in the database.
        managed = False
        db_table = 'DRTS_Desing_Plan_New'
        verbose_name = 'Design Plan'
        verbose_name_plural = 'Design Plans'
        # Order by ID for consistent listing, mirroring sequential insert behavior
        ordering = ['id']

    def __str__(self):
        return f"Design Plan: {self.work_order_no} - {self.fixture_no}"

    # Business logic: Convert specific fields to uppercase as per original ASP.NET behavior
    def save(self, *args, **kwargs):
        self.concept_design = self.concept_design.upper()
        self.internal_review = self.internal_review.upper()
        super().save(*args, **kwargs)

    @classmethod
    def get_display_fields(cls):
        """Returns a list of fields suitable for display in a DataTables table,
        excluding the primary key for the SN column."""
        return [
            'work_order_no', 'sr_no', 'fixture_no', 'concept_design',
            'internal_review', 'dap_send', 'dap_recd', 'correction',
            'final_dap', 'bought_list', 'drawing_release', 'cnc_data',
            'cmm_data', 'fit_list', 'manual', 'detailing', 'tpl_entry'
        ]

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**

A `ModelForm` is created for the `DesignPlan` model. All relevant fields are included, and `widgets` are used to apply Tailwind CSS classes for proper styling. The form's validation will leverage Django's built-in capabilities, which handle the required fields based on the model definition.

**File:** `designplan/forms.py`

```python
from django import forms
from .models import DesignPlan

class DesignPlanForm(forms.ModelForm):
    class Meta:
        model = DesignPlan
        fields = [
            'work_order_no', 'sr_no', 'fixture_no', 'concept_design',
            'internal_review', 'dap_send', 'dap_recd', 'correction',
            'final_dap', 'bought_list', 'drawing_release', 'cnc_data',
            'cmm_data', 'fit_list', 'manual', 'detailing', 'tpl_entry'
        ]
        widgets = {
            'work_order_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sr_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fixture_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'concept_design': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'internal_review': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'dap_send': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'dap_recd': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'correction': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'final_dap': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bought_list': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'drawing_release': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cnc_data': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cmm_data': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fit_list': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manual': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'detailing': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tpl_entry': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

We will implement `ListView` (for displaying all records), `CreateView` (for adding new records), `UpdateView` (for modifying existing records), and `DeleteView` (for removing records). A `DesignPlanTablePartialView` will be added to render only the table content, allowing HTMX to dynamically update the list without full page reloads. Views remain concise, adhering to the "thin view" principle by moving complex data handling and business logic to the `DesignPlan` model. HTMX headers (`HX-Trigger`) are used to inform the client to refresh the list after any successful CRUD operation.

**File:** `designplan/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DesignPlan
from .forms import DesignPlanForm

# View for displaying a list of Design Plans with DataTables support
class DesignPlanListView(ListView):
    model = DesignPlan
    template_name = 'designplan/designplan_list.html'
    context_object_name = 'designplans' # Renamed for clarity in templates

# View for rendering the DataTables table content specifically for HTMX
class DesignPlanTablePartialView(ListView):
    model = DesignPlan
    template_name = 'designplan/_designplan_table.html'
    context_object_name = 'designplans'
    
    def get_queryset(self):
        # Ensure consistent ordering for DataTables
        return super().get_queryset().order_by('id')

# View for creating a new Design Plan record
class DesignPlanCreateView(CreateView):
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'designplan/_designplan_form.html' # Use partial template for modal forms
    success_url = reverse_lazy('designplan_list') # Redirect to list view on success

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList' # Trigger event to refresh list table
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If form is invalid for HTMX, return the form HTML with errors
            return response
        return response

# View for updating an existing Design Plan record
class DesignPlanUpdateView(UpdateView):
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'designplan/_designplan_form.html' # Use partial template for modal forms
    success_url = reverse_lazy('designplan_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

# View for deleting a Design Plan record
class DesignPlanDeleteView(DeleteView):
    model = DesignPlan
    template_name = 'designplan/_designplan_confirm_delete.html' # Use partial template for modal forms
    context_object_name = 'designplan' # Ensure the object is available in context
    success_url = reverse_lazy('designplan_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Design Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

Templates will adhere to DRY principles by using partials (`_` prefix) for content loaded dynamically via HTMX (forms, delete confirmations, table content). All main templates will extend `core/base.html` (which is assumed to exist and not included in this output). Styling will use Tailwind CSS, and client-side interactions like modal visibility will be managed with Alpine.js. DataTables will be initialized on the dynamically loaded table partial for rich data presentation.

**File:** `designplan/templates/designplan/designplan_list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-900">Design Plans Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'designplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Design Plan
        </button>
    </div>

    {# HTMX container for the DataTables list. It reloads on page load and after any refreshDesignPlanList event. #}
    <div id="designplanTable-container"
         hx-trigger="load, refreshDesignPlanList from:body"
         hx-get="{% url 'designplan_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        {# Loading indicator for HTMX content #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Design Plans...</p>
        </div>
    </div>

    {# Universal Modal for HTMX-loaded forms and confirmations #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 ease-out"
             _="on modal.is-active add .scale-100 .opacity-100 remove .scale-95 .opacity-0
                on modal.is-active remove .hidden">
            {# Content loaded via HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Optional: Alpine.js component initialization for any shared UI state if needed
    document.addEventListener('alpine:init', () => {
        Alpine.data('designPlanListState', () => ({
            // Example: a filter state, etc.
        }));
    });
</script>
{% endblock %}
```

**File:** `designplan/templates/designplan/_designplan_table.html` (Partial)

```html
<div class="overflow-x-auto">
    <table id="designplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Work Order No.</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Fixture No.</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Concept Design</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Internal Review</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">DAP Send</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">DAP Recd.</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Correction</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Final DAP</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Bought List</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Drawing Release</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">CNC Data</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">CMM Data</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Fit List</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Manual</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Detailing</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">TPL Entry</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in designplans %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.work_order_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.fixture_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.concept_design }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.internal_review }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.dap_send }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.dap_recd }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.correction }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.final_dap }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.bought_list }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.drawing_release }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.cnc_data }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.cmm_data }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.fit_list }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.manual }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.detailing }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.tpl_entry }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-200 ease-in-out"
                        hx-get="{% url 'designplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md transition duration-200 ease-in-out"
                        hx-get="{% url 'designplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="18" class="py-4 px-6 text-center text-gray-500 text-lg">No Design Plans found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the partial content is loaded by HTMX
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#designplanTable')) {
            $('#designplanTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true, // Make table responsive
                "language": { // Optional: Customize DataTables language
                    "searchPlaceholder": "Search Design Plans..."
                }
            });
        }
    });
</script>
```

**File:** `designplan/templates/designplan/_designplan_form.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Design Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="list-none p-0 mt-1">
                    {% for error in field.errors %}
                    <li class="text-red-600 text-xs">{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save Design Plan
            </button>
        </div>
    </form>
</div>
```

**File:** `designplan/templates/designplan/_designplan_confirm_delete.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="mb-4 text-gray-700">Are you absolutely sure you want to delete the Design Plan for Work Order No. "<span class="font-semibold text-blue-700">{{ designplan.work_order_no }}</span>"?</p>
    <p class="text-red-600 font-semibold mb-6">This action cannot be undone and will permanently remove this record.</p>

    <form hx-post="{% url 'designplan_delete' designplan.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**

URL patterns are set up in `designplan/urls.py` to route requests to the appropriate Django Class-Based Views. Dedicated URLs are included for the main list view and for the HTMX-loaded table partial, ensuring a smooth, dynamic user experience.

**File:** `designplan/urls.py`

```python
from django.urls import path
from .views import (
    DesignPlanListView,
    DesignPlanCreateView,
    DesignPlanUpdateView,
    DesignPlanDeleteView,
    DesignPlanTablePartialView # New view for HTMX partial
)

urlpatterns = [
    # Main page to display the list of Design Plans (will load table via HTMX)
    path('designplans/', DesignPlanListView.as_view(), name='designplan_list'),
    
    # HTMX endpoint for the DataTables table content
    path('designplans/table/', DesignPlanTablePartialView.as_view(), name='designplan_table'),
    
    # HTMX endpoint for rendering and submitting the Add New Design Plan form
    path('designplans/add/', DesignPlanCreateView.as_view(), name='designplan_add'),
    
    # HTMX endpoint for rendering and submitting the Edit Design Plan form
    path('designplans/edit/<int:pk>/', DesignPlanUpdateView.as_view(), name='designplan_edit'),
    
    # HTMX endpoint for rendering and submitting the Delete Design Plan confirmation
    path('designplans/delete/<int:pk>/', DesignPlanDeleteView.as_view(), name='designplan_delete'),
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**

Comprehensive unit tests for the `DesignPlan` model will verify correct field mapping, `__str__` representation, and ensure the `save` method correctly applies uppercase conversion. Integration tests for the views will cover GET and POST requests for list, create, update, and delete operations, checking HTTP status codes, template usage, context data, and successful object creation/modification/deletion. Crucially, tests will also verify correct HTMX responses and triggers.

**File:** `designplan/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DesignPlan

class DesignPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up non-changing objects for all test methods
        DesignPlan.objects.create(
            work_order_no='WO-001',
            sr_no='SR-001',
            fixture_no='FX-001',
            concept_design='initial concept',
            internal_review='initial review',
            dap_send='DAP1',
            dap_recd='DAP2',
            correction='CORR1',
            final_dap='FINAL1',
            bought_list='BList1',
            drawing_release='DRel1',
            cnc_data='CNC1',
            cmm_data='CMM1',
            fit_list='FList1',
            manual='Manual1',
            detailing='Detail1',
            tpl_entry='TPL1'
        )

    def test_design_plan_creation(self):
        """Test that a DesignPlan object is created correctly."""
        design_plan = DesignPlan.objects.get(id=1)
        self.assertEqual(design_plan.work_order_no, 'WO-001')
        self.assertEqual(design_plan.concept_design, 'INITIAL CONCEPT') # Check uppercase conversion
        self.assertEqual(design_plan.internal_review, 'INITIAL REVIEW') # Check uppercase conversion

    def test_verbose_name_plural(self):
        """Test the verbose_name_plural is correctly set."""
        self.assertEqual(DesignPlan._meta.verbose_name_plural, 'Design Plans')

    def test_work_order_no_label(self):
        """Test the verbose name for 'work_order_no' field."""
        design_plan = DesignPlan.objects.get(id=1)
        field_label = design_plan._meta.get_field('work_order_no').verbose_name
        self.assertEqual(field_label, 'Work Order No.')

    def test_str_method(self):
        """Test the __str__ method returns the expected string."""
        design_plan = DesignPlan.objects.get(id=1)
        expected_str = f"Design Plan: {design_plan.work_order_no} - {design_plan.fixture_no}"
        self.assertEqual(str(design_plan), expected_str)

    def test_save_method_uppercase(self):
        """Test the save method correctly converts fields to uppercase."""
        design_plan = DesignPlan.objects.create(
            work_order_no='WO-002',
            sr_no='SR-002',
            fixture_no='FX-002',
            concept_design='another concept',
            internal_review='another review',
            dap_send='DAP3',
            dap_recd='DAP4',
            correction='CORR2',
            final_dap='FINAL2',
            bought_list='BList2',
            drawing_release='DRel2',
            cnc_data='CNC2',
            cmm_data='CMM2',
            fit_list='FList2',
            manual='Manual2',
            detailing='Detail2',
            tpl_entry='TPL2'
        )
        self.assertEqual(design_plan.concept_design, 'ANOTHER CONCEPT')
        self.assertEqual(design_plan.internal_review, 'ANOTHER REVIEW')


class DesignPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create an initial DesignPlan object for testing existing record scenarios
        cls.design_plan = DesignPlan.objects.create(
            work_order_no='WO-TEST-001',
            sr_no='SR-TEST-001',
            fixture_no='FX-TEST-001',
            concept_design='original concept',
            internal_review='original review',
            dap_send='DAP_OLD',
            dap_recd='DAP_OLD_REC',
            correction='OLD_CORR',
            final_dap='OLD_FINAL',
            bought_list='OLD_BLIST',
            drawing_release='OLD_DREL',
            cnc_data='OLD_CNC',
            cmm_data='OLD_CMM',
            fit_list='OLD_FLIST',
            manual='OLD_MAN',
            detailing='OLD_DET',
            tpl_entry='OLD_TPL'
        )

    def setUp(self):
        self.client = Client()

    def test_design_plan_list_view(self):
        """Test the DesignPlan list view loads correctly."""
        response = self.client.get(reverse('designplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'designplan/designplan_list.html')
        self.assertContains(response, 'Design Plans Management')

    def test_design_plan_table_partial_view(self):
        """Test the HTMX partial for the DesignPlan table loads correctly."""
        response = self.client.get(reverse('designplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'designplan/_designplan_table.html')
        self.assertContains(response, self.design_plan.work_order_no) # Check if data is present
        self.assertContains(response, 'id="designplanTable"') # Check for DataTable ID

    def test_design_plan_create_view_get(self):
        """Test GET request to the create form view."""
        response = self.client.get(reverse('designplan_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'designplan/_designplan_form.html')
        self.assertContains(response, 'Add Design Plan') # Check form title
        self.assertContains(response, '<form hx-post=') # Ensure HTMX form is present

    def test_design_plan_create_view_post_success(self):
        """Test POST request to create a new Design Plan."""
        initial_count = DesignPlan.objects.count()
        data = {
            'work_order_no': 'WO-NEW-001',
            'sr_no': 'SR-NEW-001',
            'fixture_no': 'FX-NEW-001',
            'concept_design': 'new concept',
            'internal_review': 'new review',
            'dap_send': 'NEW_DAP',
            'dap_recd': 'NEW_DAP_REC',
            'correction': 'NEW_CORR',
            'final_dap': 'NEW_FINAL',
            'bought_list': 'NEW_BLIST',
            'drawing_release': 'NEW_DREL',
            'cnc_data': 'NEW_CNC',
            'cmm_data': 'NEW_CMM',
            'fit_list': 'NEW_FLIST',
            'manual': 'NEW_MAN',
            'detailing': 'NEW_DET',
            'tpl_entry': 'NEW_TPL'
        }
        response = self.client.post(reverse('designplan_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success code (No Content)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        self.assertEqual(DesignPlan.objects.count(), initial_count + 1)
        self.assertTrue(DesignPlan.objects.filter(work_order_no='WO-NEW-001').exists())
        # Verify uppercase conversion on saved object
        new_obj = DesignPlan.objects.get(work_order_no='WO-NEW-001')
        self.assertEqual(new_obj.concept_design, 'NEW CONCEPT')
        self.assertEqual(new_obj.internal_review, 'NEW REVIEW')

    def test_design_plan_create_view_post_invalid(self):
        """Test POST request with invalid data (missing required field)."""
        initial_count = DesignPlan.objects.count()
        data = {
            'work_order_no': '', # Missing required field
            'sr_no': 'SR-INVALID',
            'fixture_no': 'FX-INVALID',
            'concept_design': 'invalid concept',
            'internal_review': 'invalid review',
            'dap_send': 'INV_DAP',
            'dap_recd': 'INV_DAP_REC',
            'correction': 'INV_CORR',
            'final_dap': 'INV_FINAL',
            'bought_list': 'INV_BLIST',
            'drawing_release': 'INV_DREL',
            'cnc_data': 'INV_CNC',
            'cmm_data': 'INV_CMM',
            'fit_list': 'INV_FLIST',
            'manual': 'INV_MAN',
            'detailing': 'INV_DET',
            'tpl_entry': 'INV_TPL'
        }
        response = self.client.post(reverse('designplan_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertContains(response, 'This field is required.') # Check for error message
        self.assertEqual(DesignPlan.objects.count(), initial_count) # No new object created

    def test_design_plan_update_view_get(self):
        """Test GET request to the update form view."""
        response = self.client.get(reverse('designplan_edit', args=[self.design_plan.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'designplan/_designplan_form.html')
        self.assertContains(response, 'Edit Design Plan') # Check form title
        self.assertContains(response, self.design_plan.work_order_no) # Check if current data is pre-filled

    def test_design_plan_update_view_post_success(self):
        """Test POST request to update an existing Design Plan."""
        updated_work_order = 'WO-UPDATED-001'
        data = {
            'work_order_no': updated_work_order,
            'sr_no': self.design_plan.sr_no,
            'fixture_no': self.design_plan.fixture_no,
            'concept_design': 'updated concept', # Will be uppercased
            'internal_review': self.design_plan.internal_review,
            'dap_send': self.design_plan.dap_send,
            'dap_recd': self.design_plan.dap_recd,
            'correction': self.design_plan.correction,
            'final_dap': self.design_plan.final_dap,
            'bought_list': self.design_plan.bought_list,
            'drawing_release': self.design_plan.drawing_release,
            'cnc_data': self.design_plan.cnc_data,
            'cmm_data': self.design_plan.cmm_data,
            'fit_list': self.design_plan.fit_list,
            'manual': self.design_plan.manual,
            'detailing': self.design_plan.detailing,
            'tpl_entry': self.design_plan.tpl_entry
        }
        response = self.client.post(reverse('designplan_edit', args=[self.design_plan.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        
        self.design_plan.refresh_from_db()
        self.assertEqual(self.design_plan.work_order_no, updated_work_order)
        self.assertEqual(self.design_plan.concept_design, 'UPDATED CONCEPT') # Verify uppercase

    def test_design_plan_delete_view_get(self):
        """Test GET request to the delete confirmation view."""
        response = self.client.get(reverse('designplan_delete', args=[self.design_plan.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'designplan/_designplan_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion') # Check confirmation message
        self.assertContains(response, self.design_plan.work_order_no) # Check object name

    def test_design_plan_delete_view_post_success(self):
        """Test POST request to delete an existing Design Plan."""
        design_plan_to_delete = DesignPlan.objects.create(
            work_order_no='WO-DEL-001',
            sr_no='SR-DEL-001',
            fixture_no='FX-DEL-001',
            concept_design='delete concept',
            internal_review='delete review',
            dap_send='DEL_DAP',
            dap_recd='DEL_DAP_REC',
            correction='DEL_CORR',
            final_dap='DEL_FINAL',
            bought_list='DEL_BLIST',
            drawing_release='DEL_DREL',
            cnc_data='DEL_CNC',
            cmm_data='DEL_CMM',
            fit_list='DEL_FLIST',
            manual='DEL_MAN',
            detailing='DEL_DET',
            tpl_entry='DEL_TPL'
        )
        initial_count = DesignPlan.objects.count()
        response = self.client.post(reverse('designplan_delete', args=[design_plan_to_delete.pk]), HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        self.assertEqual(DesignPlan.objects.count(), initial_count - 1)
        self.assertFalse(DesignPlan.objects.filter(pk=design_plan_to_delete.pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided templates and views are already designed for seamless HTMX and Alpine.js integration:

-   **HTMX for Dynamic Updates:**
    -   The main list page (`designplan_list.html`) uses `hx-get` to dynamically load the DataTables content from `designplan_table` partial view.
    -   Add/Edit/Delete buttons use `hx-get` to load forms/confirmation modals into the `#modalContent` div.
    -   Form submissions use `hx-post` with `hx-swap="none"` to prevent partial swaps on success, instead relying on the `HX-Trigger` header from the backend to refresh the list table.
    -   The `HX-Trigger: 'refreshDesignPlanList'` header sent by the views after successful CRUD operations automatically triggers the `hx-get` on the list table container, ensuring the DataTables content is always up-to-date without a full page refresh.

-   **Alpine.js for UI State Management:**
    -   Alpine.js (used via `_` attributes) handles the modal's visibility (`add .is-active to #modal`, `remove .is-active from me`). It also manages transitions for a smooth user experience.
    -   For form errors, the form reloads the modal content, displaying Django form errors inline, which is handled by HTMX's partial swap for invalid form submissions.

-   **DataTables for List Views:**
    -   The `_designplan_table.html` partial initializes DataTables on the `<table>` element. This ensures that when the table content is reloaded via HTMX, DataTables re-initializes and provides its full search, sort, and pagination features.

This robust setup ensures that all user interactions are dynamic, efficient, and provide a modern, snappy experience, significantly improving upon traditional full-page postbacks.

### Final Notes

-   **Module Context:** The new Django app `designplan` is self-contained. To integrate it into a larger Django project, ensure it's added to `INSTALLED_APPS` in `settings.py` and its URLs are included in your project's main `urls.py`.
-   **Database Connection:** Remember to configure your Django `DATABASES` setting to connect to your existing SQL Server database that contains the `DRTS_Desing_Plan_New` table.
-   **Static Files:** Ensure Tailwind CSS is correctly set up and compiled in your Django project. The provided templates assume Tailwind classes are available.
-   **Error Handling:** The `try-catch` block in the original ASP.NET code was very broad. Django's error handling and logging (often integrated with Sentry or similar tools) will provide more robust and specific error reporting.
-   **Security:** Django's ORM and forms automatically provide protection against SQL injection and CSRF attacks, which were manually handled or potentially missed in the original ASP.NET code.