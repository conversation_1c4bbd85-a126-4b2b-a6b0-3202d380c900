## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Modernization Plan: Transitioning `VENDOR_PLAN_Edit` to Django

This plan outlines the strategic move from your existing ASP.NET `VENDOR_PLAN_Edit` functionality to a robust, modern Django application. By embracing Django, HTMX, and Alpine.js, we aim to deliver a highly performant, maintainable, and scalable solution that significantly enhances the user experience and streamlines development.

**Business Benefits:**

*   **Faster Development Cycles:** Django's "batteries included" philosophy and emphasis on conventions dramatically speed up new feature development.
*   **Improved User Experience:** HTMX and Alpine.js enable dynamic, interactive interfaces without complex JavaScript, leading to smoother, more responsive user interactions.
*   **Reduced Maintenance Costs:** A clean, well-structured Django codebase is easier to understand, debug, and update, lowering long-term maintenance efforts.
*   **Enhanced Scalability:** Django's architecture is built to handle increasing user loads and data volumes, ensuring your application grows with your business.
*   **Future-Proof Technology:** By moving to a widely adopted, actively maintained open-source framework, you mitigate vendor lock-in and ensure access to a vast community and continuous innovation.

This transition will be conducted through a series of automated and systematic steps, ensuring accuracy and minimizing manual effort, thereby allowing your team to focus on business logic and innovation.

---

## Conversion Steps:

Since the provided ASP.NET code (`.aspx` and C# code-behind) is minimal and contains no specific database schema or UI elements, we will infer a common structure for a "Vendor Plan" entity based on the file name `VENDOR_PLAN_Edit`. We will assume a `VendorPlan` model with essential fields for demonstration purposes.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code does not explicitly define database interactions (e.g., `SqlDataSource`, direct SQL commands, or specific UI bindings to data fields).

**Inference:** Based on the file name `VENDOR_PLAN_Edit.aspx`, we infer the primary entity is a "Vendor Plan."
*   **Table Name:** `VENDOR_PLAN` (This is a common convention where the file name relates directly to a database table or entity).
*   **Columns (Inferred):**
    *   `ID` (Primary Key, integer)
    *   `PlanName` (String, e.g., `VARCHAR(255)`)
    *   `VendorID` (Integer, assumed foreign key to a Vendor table, but treated as a simple integer for this example)
    *   `StartDate` (Date)
    *   `EndDate` (Date)
    *   `Status` (String, e.g., `VARCHAR(50)`, for 'Active', 'Inactive', 'Draft')
    *   `Description` (Text, e.g., `NTEXT` or `VARCHAR(MAX)`)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The C# code-behind only contains an empty `Page_Load` event handler. There are no explicit CRUD operations (Create, Read, Update, Delete) visible in the provided code.

**Inference:** The file name `VENDOR_PLAN_Edit` strongly suggests that the primary functionality of this page is **editing** an existing vendor plan, and by extension, also viewing/creating new ones. Therefore, we will implement full CRUD capabilities for the `VendorPlan` entity in Django, which is a standard pattern for "edit" pages in ASP.NET.

*   **Create:** Ability to add new `VendorPlan` records.
*   **Read:** Ability to display a list of `VendorPlan` records and view details of a single record.
*   **Update:** Ability to modify existing `VendorPlan` records.
*   **Delete:** Ability to remove `VendorPlan` records.
*   **Validation:** Basic field validation (e.g., required fields, date formats) will be implemented in Django Forms.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The `.aspx` file only contains `<asp:Content>` tags and no specific UI controls.

**Inference:** Given the inferred CRUD functionality, we anticipate the following common UI components that would typically be found on an ASP.NET "Edit" page and a corresponding list view:

*   **For List View:**
    *   A grid/table to display multiple `VendorPlan` records (e.g., `GridView`).
    *   Buttons/links for "Add New," "Edit," and "Delete" actions.
*   **For Edit/Create Form:**
    *   Input fields for `PlanName`, `VendorID`, `StartDate`, `EndDate`, `Status`, `Description` (e.g., `TextBox`, `CalendarExtender`, `DropDownList`, `TextArea`).
    *   A "Save" button to submit changes.
    *   A "Cancel" or "Back" button.
*   **Dynamic Interactions:** While not present in the given code, modern web applications often have dynamic elements. We will implement these using HTMX and Alpine.js for a smooth user experience (e.g., modal forms for edit/create, real-time table updates).

---

## Step 4: Generate Django Code

We will now generate the corresponding Django code, organized by file, to achieve the modernization.

### 4.1 Models (`dailysystem/models.py`)

This `VendorPlan` model will connect to your existing `VENDOR_PLAN` database table.

```python
from django.db import models

class VendorPlan(models.Model):
    # Django will automatically manage an 'id' primary key for new records.
    # If your existing 'VENDOR_PLAN' table has a specific primary key column name (e.g., 'VP_ID'),
    # you would add:
    # id = models.IntegerField(db_column='VP_ID', primary_key=True)
    # However, for simplicity and Django's default, we assume it's handled.

    plan_name = models.CharField(db_column='PlanName', max_length=255, verbose_name='Plan Name')
    vendor_id = models.IntegerField(db_column='VendorID', verbose_name='Vendor ID')
    start_date = models.DateField(db_column='StartDate', verbose_name='Start Date')
    end_date = models.DateField(db_column='EndDate', verbose_name='End Date')
    status = models.CharField(db_column='Status', max_length=50, verbose_name='Status')
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name='Description')

    class Meta:
        managed = False  # Important: tells Django not to manage table creation/alteration
        db_table = 'VENDOR_PLAN' # The exact name of your existing database table
        verbose_name = 'Vendor Plan'
        verbose_name_plural = 'Vendor Plans'

    def __str__(self):
        return f"{self.plan_name} ({self.status})"

    # --- Business Logic Methods (Fat Model) ---
    def is_active(self):
        """Checks if the vendor plan is currently active."""
        return self.status == 'Active' and self.end_date >= models.DateField.today()

    def update_status(self, new_status):
        """Updates the plan's status and saves it."""
        if new_status in ['Active', 'Inactive', 'Draft', 'Completed']:
            self.status = new_status
            self.save()
            return True
        return False

    def get_duration_in_days(self):
        """Calculates the duration of the plan in days."""
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days
        return 0

```

### 4.2 Forms (`dailysystem/forms.py`)

This form will handle data input and validation for `VendorPlan` objects.

```python
from django import forms
from .models import VendorPlan

class VendorPlanForm(forms.ModelForm):
    class Meta:
        model = VendorPlan
        fields = ['plan_name', 'vendor_id', 'start_date', 'end_date', 'status', 'description']
        widgets = {
            'plan_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter plan name'
            }),
            'vendor_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter vendor ID'
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date', # HTML5 date input for native date picker
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date', # HTML5 date input
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }),
            'status': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }, choices=[ # Example choices, adapt as per your DB enum/lookup
                ('Active', 'Active'),
                ('Inactive', 'Inactive'),
                ('Draft', 'Draft'),
                ('Completed', 'Completed'),
            ]),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-24',
                'placeholder': 'Enter plan description',
                'rows': 3
            }),
        }
        labels = { # Custom labels for display if needed
            'plan_name': 'Plan Name',
            'vendor_id': 'Vendor ID',
            'start_date': 'Start Date',
            'end_date': 'End Date',
            'status': 'Status',
            'description': 'Description',
        }

    # Custom validation example: Ensure end_date is not before start_date
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', 'End date cannot be before start date.')
        return cleaned_data

```

### 4.3 Views (`dailysystem/views.py`)

These views implement the CRUD operations using Django's Class-Based Views, adhering to the fat model/thin view principle and HTMX-first interaction.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import VendorPlan
from .forms import VendorPlanForm

# --- List View for full page load and initial HTMX table fetch ---
class VendorPlanListView(ListView):
    model = VendorPlan
    template_name = 'dailysystem/vendorplan/list.html'
    context_object_name = 'vendorplans' # Name of the queryset variable in template

# --- Partial View for HTMX to refresh only the table component ---
class VendorPlanTablePartialView(ListView):
    model = VendorPlan
    template_name = 'dailysystem/vendorplan/_vendorplan_table.html' # Note the underscore for partials
    context_object_name = 'vendorplans'

# --- Create View (for modal/HTMX form submission) ---
class VendorPlanCreateView(CreateView):
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailysystem/vendorplan/_vendorplan_form.html' # Partial template for HTMX
    success_url = reverse_lazy('vendorplan_list') # Redirect for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan added successfully.')
        # HTMX-specific response: return 204 No Content and trigger client-side event
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList' # Custom HTMX event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


# --- Update View (for modal/HTMX form submission) ---
class VendorPlanUpdateView(UpdateView):
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailysystem/vendorplan/_vendorplan_form.html' # Partial template for HTMX
    context_object_name = 'vendorplan'
    success_url = reverse_lazy('vendorplan_list') # Redirect for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan updated successfully.')
        # HTMX-specific response
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


# --- Delete View (for modal/HTMX confirmation) ---
class VendorPlanDeleteView(DeleteView):
    model = VendorPlan
    template_name = 'dailysystem/vendorplan/_vendorplan_confirm_delete.html' # Partial template for HTMX
    context_object_name = 'vendorplan'
    success_url = reverse_lazy('vendorplan_list') # Redirect for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Vendor Plan deleted successfully.')
        # HTMX-specific response
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList'
                }
            )
        return response

```

### 4.4 Templates (`dailysystem/templates/dailysystem/vendorplan/`)

**list.html**
This is the main page that loads the DataTables content via HTMX.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Vendor Plans</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'vendorplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Vendor Plan
        </button>
    </div>

    <!-- This div will be updated by HTMX -->
    <div id="vendorplanTable-container"
         hx-trigger="load, refreshVendorPlanList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'vendorplan_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Loading spinner for initial load -->
        <div class="flex flex-col items-center justify-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Vendor Plans...</p>
        </div>
    </div>

    <!-- Universal Modal for HTMX content -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-8 rounded-lg shadow-2xl max-w-3xl w-full mx-4 my-8 relative">
            <!-- Content loaded here via HTMX -->
            <button class="absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl"
                    _="on click remove .is-active from #modal">
                &times;
            </button>
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initiated globally or by adding x-data attributes.
    // No specific Alpine.js JS needed here unless for custom component logic.
</script>
{% endblock %}
```

**\_vendorplan_table.html**
This partial template contains the DataTables structure, loaded dynamically by HTMX.

```html
<table id="vendorplanTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Plan Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Vendor ID</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Start Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">End Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for obj in vendorplans %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.plan_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.vendor_id }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.start_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.end_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    {% if obj.status == 'Active' %}bg-green-100 text-green-800
                    {% elif obj.status == 'Inactive' %}bg-red-100 text-red-800
                    {% elif obj.status == 'Draft' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ obj.status }}
                </span>
            </td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out mr-2"
                    hx-get="{% url 'vendorplan_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                    hx-get="{% url 'vendorplan_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No vendor plans found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization must happen after the table is loaded into the DOM
$(document).ready(function() {
    $('#vendorplanTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers", // For next/previous/first/last page buttons
        "info": true, // Show "Showing X to Y of Z entries"
        "searching": true // Enable search box
    });
});
</script>
```

**\_vendorplan_form.html**
This partial template for creating/editing forms is loaded into the modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Vendor Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <label for="{{ form.plan_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.plan_name.label }}
                </label>
                {{ form.plan_name }}
                {% if form.plan_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.plan_name.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.vendor_id.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.vendor_id.label }}
                </label>
                {{ form.vendor_id }}
                {% if form.vendor_id.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.vendor_id.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.start_date.label }}
                </label>
                {{ form.start_date }}
                {% if form.start_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.start_date.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.end_date.label }}
                </label>
                {{ form.end_date }}
                {% if form.end_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.end_date.errors }}</p>
                {% endif %}
            </div>

            <div class="md:col-span-2">
                <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.status.label }}
                </label>
                {{ form.status }}
                {% if form.status.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.status.errors }}</p>
                {% endif %}
            </div>

            <div class="md:col-span-2">
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.description.label }}
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.description.errors }}</p>
                {% endif %}
            </div>
        </div>

        {% if form.non_field_errors %}
        <div class="text-red-500 text-sm mb-4">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save Vendor Plan
            </button>
        </div>
    </form>
</div>
```

**\_vendorplan_confirm_delete.html**
This partial template for delete confirmation is loaded into the modal via HTMX.

```html
<div class="p-6 text-center">
    <i class="fas fa-exclamation-triangle text-yellow-500 text-6xl mb-6"></i>
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Vendor Plan:
        <strong class="font-medium">"{{ vendorplan.plan_name }}"</strong>?
        This action cannot be undone.
    </p>

    <form hx-post="{% url 'vendorplan_delete' vendorplan.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out">
                <span id="delete-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`dailysystem/urls.py`)

These URL patterns map the views to specific paths within your Django application.

```python
from django.urls import path
from .views import (
    VendorPlanListView,
    VendorPlanCreateView,
    VendorPlanUpdateView,
    VendorPlanDeleteView,
    VendorPlanTablePartialView # Added for HTMX table loading
)

urlpatterns = [
    # Full page view for listing vendor plans
    path('vendorplans/', VendorPlanListView.as_view(), name='vendorplan_list'),

    # HTMX endpoints for modal forms and table updates
    path('vendorplans/add/', VendorPlanCreateView.as_view(), name='vendorplan_add'),
    path('vendorplans/edit/<int:pk>/', VendorPlanUpdateView.as_view(), name='vendorplan_edit'),
    path('vendorplans/delete/<int:pk>/', VendorPlanDeleteView.as_view(), name='vendorplan_delete'),
    path('vendorplans/table/', VendorPlanTablePartialView.as_view(), name='vendorplan_table'), # HTMX endpoint for table content
]

```

### 4.6 Tests (`dailysystem/tests.py`)

Comprehensive tests for models and views ensure reliability and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import VendorPlan
import datetime

class VendorPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data that will be used by all test methods
        VendorPlan.objects.create(
            plan_name='Q1 2024 Strategy',
            vendor_id=101,
            start_date=datetime.date(2024, 1, 1),
            end_date=datetime.date(2024, 3, 31),
            status='Active',
            description='Strategic plan for Q1'
        )
        VendorPlan.objects.create(
            plan_name='2023 Review',
            vendor_id=102,
            start_date=datetime.date(2023, 10, 1),
            end_date=datetime.date(2023, 12, 31),
            status='Completed',
            description='Annual review of vendor performance'
        )

    def test_vendor_plan_creation(self):
        obj = VendorPlan.objects.get(plan_name='Q1 2024 Strategy')
        self.assertEqual(obj.vendor_id, 101)
        self.assertEqual(obj.status, 'Active')
        self.assertFalse(obj.is_active()) # Assuming today is after 2024-03-31 for testing is_active()

    def test_plan_name_label(self):
        obj = VendorPlan.objects.get(pk=1) # Get the first object
        field_label = obj._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')

    def test_status_update_method(self):
        obj = VendorPlan.objects.get(plan_name='Q1 2024 Strategy')
        initial_status = obj.status
        self.assertTrue(obj.update_status('Inactive'))
        obj.refresh_from_db() # Reload the object from the database to get updated status
        self.assertEqual(obj.status, 'Inactive')
        self.assertFalse(obj.update_status('InvalidStatus')) # Test invalid status
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Inactive') # Status should not have changed

    def test_get_duration_in_days_method(self):
        obj = VendorPlan.objects.get(plan_name='Q1 2024 Strategy')
        self.assertEqual(obj.get_duration_in_days(), 90) # Jan 1 to March 31 is 90 days
        obj_no_dates = VendorPlan.objects.create(plan_name='No Date Plan', vendor_id=103)
        self.assertEqual(obj_no_dates.get_duration_in_days(), 0)


class VendorPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create data for views tests
        VendorPlan.objects.create(
            plan_name='Test Plan 1',
            vendor_id=201,
            start_date=datetime.date(2024, 1, 1),
            end_date=datetime.date(2024, 6, 30),
            status='Active'
        )
        VendorPlan.objects.create(
            plan_name='Test Plan 2',
            vendor_id=202,
            start_date=datetime.date(2023, 7, 1),
            end_date=datetime.date(2023, 12, 31),
            status='Completed'
        )

    def setUp(self):
        self.client = Client() # Client for making HTTP requests

    def test_list_view(self):
        response = self.client.get(reverse('vendorplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/list.html')
        self.assertContains(response, 'Test Plan 1')
        self.assertContains(response, 'Test Plan 2')
        self.assertQuerySetEqual(response.context['vendorplans'].order_by('pk'),
                                 ['<VendorPlan: Test Plan 1 (Active)>', '<VendorPlan: Test Plan 2 (Completed)>'],
                                 transform=lambda x: x.__str__())

    def test_table_partial_view_htmx(self):
        # Simulate HTMX request for the table partial
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendorplan_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_table.html')
        self.assertContains(response, '<table id="vendorplanTable"') # Check if the table HTML is rendered

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendorplan_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_form.html')
        self.assertContains(response, 'Add Vendor Plan')
        self.assertTrue('form' in response.context)

    def test_create_view_post_htmx_valid(self):
        self.assertEqual(VendorPlan.objects.count(), 2)
        data = {
            'plan_name': 'New HTMX Plan',
            'vendor_id': 301,
            'start_date': '2024-07-01',
            'end_date': '2024-09-30',
            'status': 'Draft',
            'description': 'A new plan added via HTMX.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertTrue(VendorPlan.objects.filter(plan_name='New HTMX Plan').exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')
        self.assertEqual(VendorPlan.objects.count(), 3)

    def test_create_view_post_htmx_invalid(self):
        self.assertEqual(VendorPlan.objects.count(), 2)
        data = {
            'plan_name': '', # Invalid, required field
            'vendor_id': 301,
            'start_date': '2024-07-01',
            'end_date': '2024-06-30', # Invalid, end date before start date
            'status': 'Draft',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Renders form again with errors
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End date cannot be before start date.')
        self.assertEqual(VendorPlan.objects.count(), 2) # No new object created

    def test_update_view_get_htmx(self):
        obj = VendorPlan.objects.get(plan_name='Test Plan 1')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendorplan_edit', args=[obj.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_form.html')
        self.assertContains(response, 'Edit Vendor Plan')
        self.assertContains(response, 'Test Plan 1')
        self.assertTrue('form' in response.context)

    def test_update_view_post_htmx_valid(self):
        obj = VendorPlan.objects.get(plan_name='Test Plan 1')
        data = {
            'plan_name': 'Updated Plan 1',
            'vendor_id': obj.vendor_id, # Keep original vendor_id
            'start_date': obj.start_date,
            'end_date': obj.end_date,
            'status': 'Inactive', # Change status
            'description': 'Updated description.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_edit', args=[obj.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.plan_name, 'Updated Plan 1')
        self.assertEqual(obj.status, 'Inactive')
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')

    def test_delete_view_get_htmx(self):
        obj = VendorPlan.objects.get(plan_name='Test Plan 1')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendorplan_delete', args=[obj.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Test Plan 1')
        self.assertTrue('vendorplan' in response.context)

    def test_delete_view_post_htmx(self):
        obj = VendorPlan.objects.get(plan_name='Test Plan 1')
        self.assertEqual(VendorPlan.objects.count(), 2)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendorplan_delete', args=[obj.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(VendorPlan.objects.filter(plan_name='Test Plan 1').exists())
        self.assertEqual(VendorPlan.objects.count(), 1)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated code fully leverages HTMX and Alpine.js principles:

*   **HTMX for Dynamic Updates:**
    *   The main `list.html` uses `hx-get` to load the table content from `{% url 'vendorplan_table' %}`.
    *   The table container `div` uses `hx-trigger="load, refreshVendorPlanList from:body"` to ensure the table loads on page view and refreshes automatically after any CRUD operation (add, edit, delete) triggers the `refreshVendorPlanList` event from the server (via `HX-Trigger` header).
    *   "Add," "Edit," and "Delete" buttons use `hx-get` to fetch the respective form/confirmation partials into a modal (`#modalContent`).
    *   Forms use `hx-post` for submission, with `hx-swap="none"` to prevent accidental content swap, relying on the `HX-Trigger` from the server to refresh the list.
    *   Loading indicators (`htmx-indicator`) are integrated for visual feedback during HTMX requests.
*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses `x-data="{ isOpen: false }"` (if globally defined in base.html) or `_=` attributes (h-script) for simple toggle logic (e.g., `_="on click add .is-active to #modal"` and `_="on click if event.target.id == 'modal' remove .is-active from me"`). This handles showing/hiding the modal purely on the client side, keeping JavaScript minimal.
*   **DataTables for List Views:**
    *   The `_vendorplan_table.html` partial includes the `<table>` element with the ID `vendorplanTable`.
    *   A `<script>` block within this partial initializes DataTables on `$(document).ready()`, ensuring DataTables is applied as soon as the table HTML is loaded by HTMX.
    *   The DataTables setup includes client-side searching, sorting, and pagination.
*   **No Full Page Reloads:** All CRUD operations and list refreshes happen dynamically via HTMX, providing a seamless single-page application-like experience without the complexity of a full SPA framework.

## Final Notes

*   This plan provides a structured, automated approach to migrate `VENDOR_PLAN_Edit` functionality to a modern Django application.
*   All placeholders (`[MODEL_NAME]`, `[TABLE_NAME]`, `[FIELD1]`, etc.) have been replaced with inferred values (`VendorPlan`, `VENDOR_PLAN`, `plan_name`, etc.) based on standard practices and the provided ASP.NET file name. In a real-world scenario, these would be derived directly from the code analysis.
*   The `core/base.html` template (not included as per instructions) should contain the necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS.
*   Business logic is concentrated within the `VendorPlan` model, keeping views concise and focused on handling HTTP requests and responses.
*   The included tests ensure the correctness and robustness of the model and view functionalities, covering essential scenarios and HTMX interactions.
*   This systematic breakdown ensures that the migration process is manageable, repeatable, and easily understandable by both technical and non-technical stakeholders.