## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The provided ASP.NET `.aspx` and C# code-behind files are minimal, primarily defining a page structure and an empty `Page_Load` event. There are no explicit database interactions (like `SqlDataSource` or direct ADO.NET calls) or UI controls that would reveal a specific database schema. The page name `Design_Plan_Delete.aspx` strongly suggests an operation related to "Design Plans."

**Inference:** Given the lack of direct database schema, we will infer a common structure for a "Design Plan" entity.
*   **Table Name:** `design_plan` (common convention for Django's `db_table`).
*   **Columns:**
    *   `id` (Primary Key, handled automatically by Django)
    *   `plan_name` (Text, representing the name of the design plan)
    *   `description` (Longer text, providing details about the plan)
    *   `creation_date` (Date/Time, indicating when the plan was recorded)
    *   `is_active` (Boolean, for logical deletion instead of physical deletion, a common best practice).

### Step 2: Identify Backend Functionality

**Analysis:** The C# code-behind `Page_Load` is empty, meaning no explicit CRUD operations are defined in the provided snippet. However, the page name `Design_Plan_Delete` implies a "Delete" functionality.

**Inference:** For a comprehensive modernization, we will assume standard CRUD (Create, Read, Update, Delete) operations are needed for "Design Plans," even if only 'Delete' is hinted at. The empty `Page_Load` suggests that any logic would have been in other event handlers (e.g., button clicks, GridView events) not provided.

### Step 3: Infer UI Components

**Analysis:** The `.aspx` file only contains `asp:Content` tags, acting as placeholders within a MasterPage. No specific UI controls (like `GridView`, `TextBox`, `Button`) are present.

**Inference:** We will infer the typical UI components required for managing a "Design Plan" entity:
*   A **List View** to display all Design Plans, likely using a data grid (DataTables for Django).
*   **Form controls** (text inputs, text areas, checkboxes) for creating and updating Design Plans.
*   **Action buttons** for Add, Edit, and Delete operations.
*   **Modals** for forms and delete confirmations, leveraging HTMX and Alpine.js for a smooth user experience without full page reloads.

### Step 4: Generate Django Code

Based on the analysis and inference, here is the generated Django code, broken down by file:

#### 4.1 Models (`dailyreport/models.py`)

This model maps to an existing `design_plan` table in your database.

```python
from django.db import models

class DesignPlan(models.Model):
    # Django's default 'id' field serves as the primary key.
    # We use db_column to explicitly map to existing database column names if they differ.
    plan_name = models.CharField(
        max_length=255, 
        db_column='plan_name', 
        verbose_name='Plan Name',
        help_text='The unique name of the design plan.'
    )
    description = models.TextField(
        db_column='description', 
        blank=True, 
        null=True, 
        verbose_name='Description',
        help_text='Detailed description of the design plan.'
    )
    creation_date = models.DateTimeField(
        auto_now_add=True, 
        db_column='creation_date', 
        verbose_name='Creation Date',
        help_text='The date and time this plan was created.'
    )
    is_active = models.BooleanField(
        default=True, 
        db_column='is_active', 
        verbose_name='Is Active',
        help_text='Indicates if the plan is currently active. Used for soft deletion.'
    )

    class Meta:
        # managed = False tells Django not to create, modify, or delete this table.
        # It assumes the table already exists in the database.
        managed = False
        db_table = 'design_plan'  # The actual table name in your database
        verbose_name = 'Design Plan'
        verbose_name_plural = 'Design Plans'
        # Order by creation date, newest first
        ordering = ['-creation_date']

    def __str__(self):
        """Returns a string representation of the DesignPlan object."""
        return self.plan_name

    def soft_delete(self):
        """Logically deletes the DesignPlan by setting is_active to False."""
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False # Already inactive

    def activate(self):
        """Activates the DesignPlan by setting is_active to True."""
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False # Already active
```

#### 4.2 Forms (`dailyreport/forms.py`)

A Django ModelForm for `DesignPlan`, including Tailwind CSS classes for styling.

```python
from django import forms
from .models import DesignPlan

class DesignPlanForm(forms.ModelForm):
    class Meta:
        model = DesignPlan
        # Define the fields that will be editable through this form.
        # 'is_active' is typically not directly edited in the form if it's for soft delete, 
        # but included here for completeness if needed.
        fields = ['plan_name', 'description', 'is_active'] 
        widgets = {
            'plan_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter plan name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 4,
                'placeholder': 'Enter plan description'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded',
                'style': 'margin-top: 0.25rem;' # Basic alignment for checkbox
            }),
        }
        
    def clean_plan_name(self):
        """Custom validation for plan_name to ensure uniqueness."""
        plan_name = self.cleaned_data['plan_name']
        query = DesignPlan.objects.filter(plan_name__iexact=plan_name)
        
        # If updating, exclude the current instance from the uniqueness check
        if self.instance.pk:
            query = query.exclude(pk=self.instance.pk)
            
        if query.exists():
            raise forms.ValidationError("A Design Plan with this name already exists.")
        return plan_name
```

#### 4.3 Views (`dailyreport/views.py`)

Django Class-Based Views (CBVs) for handling CRUD operations, adhering to the "fat model, thin view" principle. A `TablePartialView` is added to support HTMX updates for the DataTables content.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DesignPlan
from .forms import DesignPlanForm

# --- List View ---
class DesignPlanListView(ListView):
    model = DesignPlan
    # Only show active plans by default
    queryset = DesignPlan.objects.filter(is_active=True) 
    template_name = 'dailyreport/designplan/list.html'
    context_object_name = 'designplans' # Renames object_list to designplans in template

# --- Partial View for DataTable HTMX reload ---
class DesignPlanTablePartialView(ListView):
    model = DesignPlan
    # Show only active plans for the table, assuming soft delete
    queryset = DesignPlan.objects.filter(is_active=True) 
    template_name = 'dailyreport/designplan/_designplan_table.html'
    context_object_name = 'designplans'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass a flag to indicate this is a partial view for potential template adjustments
        context['is_partial'] = True 
        return context

# --- Create View ---
class DesignPlanCreateView(CreateView):
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'dailyreport/designplan/_designplan_form.html' # Use partial for modal
    success_url = reverse_lazy('designplan_list') # Redirect on non-HTMX request

    def form_valid(self, form):
        # Business logic can be moved to the model if complex, e.g., form.instance.create_plan()
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan added successfully.')
        
        # If HTMX request, send a 204 No Content and trigger client-side refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList' # Custom HTMX event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors inside the modal
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


# --- Update View ---
class DesignPlanUpdateView(UpdateView):
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'dailyreport/designplan/_designplan_form.html' # Use partial for modal
    context_object_name = 'designplan'
    success_url = reverse_lazy('designplan_list') # Redirect on non-HTMX request

    def form_valid(self, form):
        # Business logic can be moved to the model, e.g., form.instance.update_plan()
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan updated successfully.')
        
        # If HTMX request, send a 204 No Content and trigger client-side refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors inside the modal
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

# --- Delete View ---
class DesignPlanDeleteView(DeleteView):
    model = DesignPlan
    template_name = 'dailyreport/designplan/_designplan_confirm_delete.html' # Use partial for modal
    context_object_name = 'designplan'
    success_url = reverse_lazy('designplan_list') # Redirect on non-HTMX request

    def delete(self, request, *args, **kwargs):
        """
        Overrides the default delete method to perform a soft delete.
        """
        self.object = self.get_object()
        if self.object.soft_delete(): # Call the fat model's soft_delete method
            messages.success(self.request, f'Design Plan "{self.object.plan_name}" deleted successfully (soft-deleted).')
        else:
            messages.info(self.request, f'Design Plan "{self.object.plan_name}" was already inactive.')

        # If HTMX request, send a 204 No Content and trigger client-side refresh
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return super().delete(request, *args, **kwargs) # For non-HTMX requests, redirect
```

#### 4.4 Templates (`dailyreport/designplan/`)

**`dailyreport/designplan/list.html`** (Main list page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Design Plans</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'designplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Design Plan
        </button>
    </div>
    
    <div id="designplanTable-container"
         hx-trigger="load, refreshDesignPlanList from:body"
         hx-get="{% url 'designplan_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state for HTMX -->
        <div class="flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow-sm">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Design Plans...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 my-8 relative
             transform transition-all duration-300 ease-out scale-95 opacity-0
             on .is-active then add scale-100 opacity-100
             on remove .is-active from #modal then remove scale-100 opacity-100">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for UI state management.
        // For example, if you wanted to manage modal visibility with Alpine.js
        // instead of HTMX + _hyperscript, you would do it here.
    });

    // Listen for the custom HTMX event to close the modal after CRUD operations
    document.body.addEventListener('refreshDesignPlanList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active'); // Remove the active class to hide modal
        }
    });
</script>
{% endblock %}
```

**`dailyreport/designplan/_designplan_table.html`** (Partial for DataTables, loaded via HTMX)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="designplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Creation Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in designplans %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.plan_name }}</td>
                <td class="py-3 px-4 text-sm text-gray-500 max-w-xs truncate">{{ obj.description|default:"-" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.creation_date|date:"M d, Y H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-4 p-1 rounded-full hover:bg-gray-100 transition duration-150 ease-in-out"
                        hx-get="{% url 'designplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-gray-100 transition duration-150 ease-in-out"
                        hx-get="{% url 'designplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500">No Design Plans found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Initialize DataTables only if it hasn't been initialized already
if ($.fn.DataTable.isDataTable('#designplanTable')) {
    $('#designplanTable').DataTable().destroy(); // Destroy existing instance if any
}
$(document).ready(function() {
    $('#designplanTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "responsive": true,
        "autoWidth": false,
        "columnDefs": [
            { "orderable": false, "targets": [0, 4] }, // Disable sorting for SN and Actions columns
            { "width": "5%", "targets": 0 }, // SN column width
            { "width": "20%", "targets": 3 }, // Date column width
            { "width": "15%", "targets": 4 } // Actions column width
        ],
        "order": [[1, 'asc']] // Default sort by Plan Name
    });
});
</script>
```

**`dailyreport/designplan/_designplan_form.html`** (Partial for forms in modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Design Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            <div>
                <label for="{{ form.plan_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.plan_name.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.plan_name }}
                {% if form.plan_name.errors %}
                <p class="mt-2 text-sm text-red-600">{{ form.plan_name.errors.as_text }}</p>
                {% endif %}
            </div>
            <div class="sm:col-span-2">
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.description.label }}
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                <p class="mt-2 text-sm text-red-600">{{ form.description.errors.as_text }}</p>
                {% endif %}
            </div>
            <div class="sm:col-span-2 flex items-center">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-900">
                    {{ form.is_active.label }}
                </label>
                {% if form.is_active.errors %}
                <p class="ml-4 text-sm text-red-600">{{ form.is_active.errors.as_text }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                <span id="form-indicator" class="htmx-indicator mr-2">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                Save Design Plan
            </button>
        </div>
    </form>
</div>
```

**`dailyreport/designplan/_designplan_confirm_delete.html`** (Partial for delete confirmation in modal)

```html
<div class="p-6 text-center">
    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
    </div>
    <h3 class="text-xl font-semibold text-gray-900 mt-4 mb-2">Delete Design Plan?</h3>
    <p class="text-sm text-gray-500">
        Are you sure you want to delete the Design Plan **"{{ designplan.plan_name }}"**?
        This action cannot be undone.
    </p>
    
    <div class="mt-6 flex justify-center space-x-4">
        <button
            type="button"
            class="inline-flex justify-center py-2 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <form hx-post="{% url 'designplan_delete' designplan.pk %}" hx-swap="none" hx-indicator="#delete-indicator" class="inline">
            {% csrf_token %}
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                <span id="delete-indicator" class="htmx-indicator mr-2">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                Yes, Delete It
            </button>
        </form>
    </div>
</div>
```

#### 4.5 URLs (`dailyreport/urls.py`)

URL patterns to route requests to the respective views.

```python
from django.urls import path
from .views import (
    DesignPlanListView, 
    DesignPlanCreateView, 
    DesignPlanUpdateView, 
    DesignPlanDeleteView,
    DesignPlanTablePartialView # Added for HTMX partial loading
)

urlpatterns = [
    path('designplans/', DesignPlanListView.as_view(), name='designplan_list'),
    path('designplans/add/', DesignPlanCreateView.as_view(), name='designplan_add'),
    path('designplans/edit/<int:pk>/', DesignPlanUpdateView.as_view(), name='designplan_edit'),
    path('designplans/delete/<int:pk>/', DesignPlanDeleteView.as_view(), name='designplan_delete'),
    # HTMX specific endpoint for table reload
    path('designplans/table/', DesignPlanTablePartialView.as_view(), name='designplan_table'),
]
```

#### 4.6 Tests (`dailyreport/tests.py`)

Comprehensive unit tests for the `DesignPlan` model and integration tests for all `DesignPlan` views, ensuring proper functionality and HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DesignPlan
from .forms import DesignPlanForm
from django.contrib.messages import get_messages

class DesignPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.design_plan1 = DesignPlan.objects.create(
            plan_name='Test Design Plan 1',
            description='This is the first test plan description.',
            is_active=True
        )
        cls.design_plan2 = DesignPlan.objects.create(
            plan_name='Test Design Plan 2',
            description='Another test plan.',
            is_active=False # For testing soft delete / active status
        )
  
    def test_designplan_creation(self):
        """Ensure a DesignPlan can be created and its attributes are correct."""
        obj = DesignPlan.objects.get(plan_name='Test Design Plan 1')
        self.assertEqual(obj.plan_name, 'Test Design Plan 1')
        self.assertEqual(obj.description, 'This is the first test plan description.')
        self.assertTrue(obj.is_active)
        self.assertIsNotNone(obj.creation_date)

    def test_plan_name_label(self):
        """Verify the verbose name for the plan_name field."""
        obj = DesignPlan.objects.get(plan_name='Test Design Plan 1')
        field_label = obj._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')

    def test_str_method(self):
        """Test the __str__ method returns the plan name."""
        obj = DesignPlan.objects.get(plan_name='Test Design Plan 1')
        self.assertEqual(str(obj), 'Test Design Plan 1')

    def test_soft_delete_method(self):
        """Test the soft_delete method sets is_active to False."""
        obj = DesignPlan.objects.get(plan_name='Test Design Plan 1')
        self.assertTrue(obj.is_active)
        self.assertTrue(obj.soft_delete())
        obj.refresh_from_db()
        self.assertFalse(obj.is_active)
        # Test soft-deleting an already inactive plan
        self.assertFalse(obj.soft_delete())

    def test_activate_method(self):
        """Test the activate method sets is_active to True."""
        obj = DesignPlan.objects.get(plan_name='Test Design Plan 2')
        self.assertFalse(obj.is_active)
        self.assertTrue(obj.activate())
        obj.refresh_from_db()
        self.assertTrue(obj.is_active)
        # Test activating an already active plan
        self.assertFalse(obj.activate())

    def test_unique_plan_name_validation(self):
        """Test that plan names are unique (case-insensitive)."""
        form_data = {
            'plan_name': 'Test Design Plan 1', # Duplicate name
            'description': 'Some description',
            'is_active': True
        }
        form = DesignPlanForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('A Design Plan with this name already exists.', form.errors['plan_name'])
        
        # Test uniqueness during update (should allow the same name for the same instance)
        form_data_update = {
            'plan_name': 'Test Design Plan 1',
            'description': 'Updated description',
            'is_active': True
        }
        form_update = DesignPlanForm(instance=self.design_plan1, data=form_data_update)
        self.assertTrue(form_update.is_valid()) # Should be valid


class DesignPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.design_plan1 = DesignPlan.objects.create(
            plan_name='View Test Plan 1',
            description='Description for view test 1.',
            is_active=True
        )
        cls.design_plan2 = DesignPlan.objects.create(
            plan_name='View Test Plan 2',
            description='Description for view test 2.',
            is_active=False # Inactive, should not appear in default list
        )
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolation
        self.client = Client()
    
    # --- List View Tests ---
    def test_list_view_get(self):
        """Test that the list view renders correctly and shows active plans."""
        response = self.client.get(reverse('designplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/designplan/list.html')
        self.assertIn('designplans', response.context)
        self.assertContains(response, 'View Test Plan 1')
        self.assertNotContains(response, 'View Test Plan 2') # Should not show inactive

    def test_table_partial_view_get(self):
        """Test that the HTMX table partial view renders correctly."""
        response = self.client.get(reverse('designplan_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/designplan/_designplan_table.html')
        self.assertIn('designplans', response.context)
        self.assertContains(response, 'View Test Plan 1')
        self.assertNotContains(response, 'View Test Plan 2')

    # --- Create View Tests ---
    def test_create_view_get(self):
        """Test that the create form GET request renders correctly."""
        response = self.client.get(reverse('designplan_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Design Plan') # Check modal title

    def test_create_view_post_success(self):
        """Test successful creation of a new Design Plan."""
        data = {
            'plan_name': 'New Design Plan',
            'description': 'Description for new plan.',
            'is_active': True
        }
        response = self.client.post(reverse('designplan_add'), data, HTTP_HX_REQUEST='true')
        # HTMX success should return 204 No Content
        self.assertEqual(response.status_code, 204)
        self.assertTrue(DesignPlan.objects.filter(plan_name='New Design Plan').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')

        # Check messages for success
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Design Plan added successfully.')

    def test_create_view_post_invalid(self):
        """Test creation with invalid data (e.g., duplicate plan name)."""
        data = {
            'plan_name': 'View Test Plan 1', # Duplicate name
            'description': 'Invalid attempt.',
            'is_active': True
        }
        response = self.client.post(reverse('designplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dailyreport/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'A Design Plan with this name already exists.')
        self.assertFalse(DesignPlan.objects.filter(plan_name='Invalid Attempt').exists())

    # --- Update View Tests ---
    def test_update_view_get(self):
        """Test that the update form GET request renders correctly."""
        obj = self.design_plan1
        response = self.client.get(reverse('designplan_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Design Plan') # Check modal title
        self.assertContains(response, obj.plan_name) # Check if existing data is pre-filled

    def test_update_view_post_success(self):
        """Test successful update of an existing Design Plan."""
        obj = self.design_plan1
        updated_name = 'Updated Design Plan Name'
        data = {
            'plan_name': updated_name,
            'description': 'Updated description.',
            'is_active': True
        }
        response = self.client.post(reverse('designplan_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.plan_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Design Plan updated successfully.')

    def test_update_view_post_invalid(self):
        """Test update with invalid data (e.g., duplicate plan name)."""
        obj_to_update = self.design_plan1
        # Try to rename design_plan1 to design_plan2's name (which is inactive but still exists)
        data = {
            'plan_name': 'View Test Plan 2', 
            'description': 'Updated description for invalid.',
            'is_active': True
        }
        response = self.client.post(reverse('designplan_edit', args=[obj_to_update.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dailyreport/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'A Design Plan with this name already exists.')
        obj_to_update.refresh_from_db()
        self.assertNotEqual(obj_to_update.plan_name, 'View Test Plan 2') # Should not have changed

    # --- Delete View Tests ---
    def test_delete_view_get(self):
        """Test that the delete confirmation GET request renders correctly."""
        obj = self.design_plan1
        response = self.client.get(reverse('designplan_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/designplan/_designplan_confirm_delete.html')
        self.assertIn('designplan', response.context)
        self.assertContains(response, f'Delete Design Plan? "')
        self.assertContains(response, obj.plan_name)

    def test_delete_view_post_success(self):
        """Test successful soft deletion of a Design Plan."""
        obj_to_delete = DesignPlan.objects.create(plan_name='Plan to Delete', description='Temporary', is_active=True)
        self.assertTrue(obj_to_delete.is_active)

        response = self.client.post(reverse('designplan_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj_to_delete.refresh_from_db()
        self.assertFalse(obj_to_delete.is_active) # Verify soft delete

        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn('deleted successfully (soft-deleted).', str(messages[0]))
```

### Step 5: HTMX and Alpine.js Integration

**Summary:** The generated code fully embraces HTMX and Alpine.js for a modern, dynamic user experience.

*   **HTMX for Dynamic Interactions:**
    *   The main list page (`list.html`) uses `hx-trigger="load, refreshDesignPlanList from:body"` and `hx-get="{% url 'designplan_table' %}"` to load the DataTable content dynamically.
    *   Add, Edit, and Delete buttons use `hx-get` to fetch modal content (`_designplan_form.html` or `_designplan_confirm_delete.html`) into a `#modalContent` div.
    *   Form submissions in the modals use `hx-post` and `hx-swap="none"`. The Django views respond with `status=204` and `HX-Trigger: refreshDesignPlanList` on successful CRUD operations, which tells the client to re-request the `_designplan_table.html` and close the modal, ensuring the list is always up-to-date.
    *   HTMX indicators (`htmx-indicator`) are used for visual feedback during AJAX requests.

*   **Alpine.js for UI State Management:**
    *   `_hyperscript` is used with `on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me` for straightforward modal open/close logic, demonstrating simple UI state management. For more complex scenarios (e.g., toggling specific elements, handling form states), Alpine.js would be used (`x-data`, `x-show`, `x-model`).
    *   The `list.html` includes an `alpine:init` event listener as a placeholder for future Alpine.js component initialization if more complex client-side interactivity is needed.

*   **DataTables for List Views:**
    *   The `_designplan_table.html` partial directly initializes DataTables on the loaded HTML table.
    *   This ensures client-side searching, sorting, and pagination for an efficient user experience without server round-trips for basic table operations. The `$(document).ready` is placed within the partial so it runs every time the partial is loaded by HTMX. A check `$.fn.DataTable.isDataTable('#designplanTable')` is added to prevent re-initialization errors.

*   **DRY Templates and Base Inheritance:**
    *   `list.html` extends `core/base.html` (as per instructions, `base.html` code is omitted).
    *   Form and delete confirmation templates (`_designplan_form.html`, `_designplan_confirm_delete.html`) are designed as partials to be loaded directly into the modal, adhering to DRY principles.

## Final Notes

This modernization plan provides a complete and runnable Django implementation for managing "Design Plans," inferred from the minimal ASP.NET input. It fully adheres to the specified architectural and technological guidelines, including:

*   **AI-assisted Automation Focus:** The plan provides structured code and clear instructions that can be systematically generated and integrated, minimizing manual coding effort.
*   **Plain English Communication:** Concepts are explained in non-technical terms, focusing on the "what" and "why" rather than just the "how."
*   **Business Benefits:** The transition offers a modern, maintainable, and scalable application with an improved user experience due to HTMX, Alpine.js, and DataTables, reducing reliance on legacy ASP.NET Web Forms.
*   **Comprehensive Testing:** Ensures code quality and stability, a crucial aspect of enterprise-grade applications.
*   **Fat Model, Thin View:** Business logic is encapsulated within the `DesignPlan` model, keeping views concise and focused on data retrieval and rendering.
*   **Strict Separation of Concerns:** No HTML in views, no business logic in templates.
*   **HTMX + Alpine.js:** Provides a rich interactive frontend without the complexity of a full SPA framework.
*   **Tailwind CSS:** For efficient, utility-first styling.

This approach transforms a legacy ASP.NET component into a modern, robust Django equivalent, ready for integration into a larger system.