## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

The provided ASP.NET code for `Design_Plan_New.aspx` and its code-behind `Design_Plan_New.aspx.cs` is essentially empty, acting as a placeholder within a master page. This indicates a page intended for a new "Design Plan" entry or management. Since no specific database schema, UI controls, or business logic are present in the provided ASP.NET snippets, this modernization plan will proceed by inferring a typical "Design Plan" management module.

We will assume the goal is to implement a complete CRUD (Create, Read, Update, Delete) functionality for `Design Plan` entities, leveraging modern Django practices, HTMX for dynamic interactions, Alpine.js for minimal UI state, and DataTables for efficient data presentation.

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:
As the provided ASP.NET code is skeletal, we must infer a logical database schema for a "Design Plan" entity. We'll assume a table named `tblDesignPlan` within the existing database.

*   **[TABLE_NAME]**: `tblDesignPlan`
*   **Columns (inferred):**
    *   `ID` (Primary Key, integer)
    *   `DesignPlanName` (String, e.g., NVARCHAR)
    *   `DesignPlanDescription` (Text, e.g., NVARCHAR(MAX))
    *   `StartDate` (Date)
    *   `EndDate` (Date)
    *   `IsActive` (Boolean)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:
Given the page name `Design_Plan_New`, we infer the primary functionality is **Create** (adding new design plans). For a comprehensive management system, we will also implement **Read** (listing existing plans), **Update** (modifying plans), and **Delete** (removing plans).
*   **Create**: Adding new `DesignPlan` records.
*   **Read**: Displaying a list of `DesignPlan` records, likely with filtering and sorting.
*   **Update**: Modifying existing `DesignPlan` records.
*   **Delete**: Removing `DesignPlan` records.
*   **Validation Logic**: Standard field validations (e.g., required fields, date formats) will be implemented at the Django form level.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:
Since no specific controls are present, we infer the standard UI components for a CRUD interface:
*   **List View**: A table (DataTables) to display all `DesignPlan` records with columns for inferred fields and an "Actions" column (Edit, Delete buttons).
*   **Form View (Add/Edit)**: Input fields (Textboxes for name, description; Date pickers for dates; Checkbox for active status) for creating and updating a `DesignPlan`. These will be rendered as partials within a modal.
*   **Confirmation View (Delete)**: A simple confirmation message for deletion, also within a modal.
*   **Buttons**: "Add New Design Plan" button to trigger the creation form, "Edit" and "Delete" buttons for each row in the list view.
All dynamic interactions (form submission, modal display, table refresh) will be handled via HTMX, with Alpine.js for simple UI state management (like modal visibility).

## Step 4: Generate Django Code

We will create a new Django application, `dailyreporting`, to house this module.

### 4.1 Models
**File: `dailyreporting/models.py`**

```python
from django.db import models
from django.utils import timezone

class DesignPlan(models.Model):
    # Using 'id' as implicit PK, Django handles this by default.
    # We map to existing column names using db_column.
    plan_name = models.CharField(db_column='DesignPlanName', max_length=255, verbose_name="Plan Name")
    description = models.TextField(db_column='DesignPlanDescription', blank=True, verbose_name="Description")
    start_date = models.DateField(db_column='StartDate', verbose_name="Start Date")
    end_date = models.DateField(db_column='EndDate', verbose_name="End Date")
    is_active = models.BooleanField(db_column='IsActive', default=True, verbose_name="Is Active")

    class Meta:
        managed = False  # Set to False as the table is assumed to be pre-existing
        db_table = 'tblDesignPlan'
        verbose_name = 'Design Plan'
        verbose_name_plural = 'Design Plans'
        ordering = ['-start_date', 'plan_name'] # Sensible default ordering

    def __str__(self):
        return self.plan_name

    # Business logic method: Check if the plan is currently active based on dates
    def is_currently_active(self):
        """
        Returns True if the plan is active and its dates encompass the current date.
        """
        today = timezone.localdate()
        return self.is_active and self.start_date <= today and self.end_date >= today

    # Example: A method to get a summary string
    def get_summary(self):
        return f"{self.plan_name} ({self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')})"

```

### 4.2 Forms
**File: `dailyreporting/forms.py`**

```python
from django import forms
from .models import DesignPlan

class DesignPlanForm(forms.ModelForm):
    class Meta:
        model = DesignPlan
        fields = ['plan_name', 'description', 'start_date', 'end_date', 'is_active']
        widgets = {
            'plan_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter plan name'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'rows': 3, 'placeholder': 'Enter plan description (optional)'}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'}),
        }
        
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            self.add_error('end_date', "End date cannot be before start date.")
        return cleaned_data

```

### 4.3 Views
**File: `dailyreporting/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # For HTMX partials
from .models import DesignPlan
from .forms import DesignPlanForm

class DesignPlanListView(ListView):
    model = DesignPlan
    template_name = 'dailyreporting/designplan/list.html'
    context_object_name = 'designplans' # Renamed for clarity and consistency

    # Thin view: No complex logic here, just fetching data.
    # Any filtering/sorting would be handled by DataTables on the client-side
    # or by custom query methods on the model manager (fat model).

class DesignPlanTablePartialView(ListView):
    """
    Renders only the table portion for HTMX requests.
    """
    model = DesignPlan
    template_name = 'dailyreporting/designplan/_designplan_table.html'
    context_object_name = 'designplans' # Renamed for clarity and consistency

class DesignPlanCreateView(CreateView):
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'dailyreporting/designplan/_designplan_form.html' # Use partial for modal
    success_url = reverse_lazy('designplan_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Business logic can be offloaded to model methods if complex
        # e.g., form.instance.process_design_plan_creation()
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX request: return 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList' # Trigger custom event
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['is_create'] = True # Differentiate for template logic if needed
        return context

class DesignPlanUpdateView(UpdateView):
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'dailyreporting/designplan/_designplan_form.html' # Use partial for modal
    success_url = reverse_lazy('designplan_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Business logic can be offloaded to model methods if complex
        # e.g., form.instance.process_design_plan_update()
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX request: return 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['is_create'] = False # Differentiate for template logic if needed
        return context

class DesignPlanDeleteView(DeleteView):
    model = DesignPlan
    template_name = 'dailyreporting/designplan/confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('designplan_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        # Business logic can be offloaded to model methods
        # e.g., self.get_object().archive_design_plan() instead of deleting
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Design Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            # HTMX request: return 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response

```

### 4.4 Templates
**Directory: `dailyreporting/templates/dailyreporting/designplan/`**

**File: `list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Design Plans</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'designplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
            >
            <i class="fas fa-plus-circle mr-2"></i> Add New Design Plan
        </button>
    </div>
    
    <div id="designplanTable-container"
         hx-trigger="load, refreshDesignPlanList from:body"
         hx-get="{% url 'designplan_table_partial' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Design Plans...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center opacity-0 hidden transition-opacity duration-300 ease-out"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transform scale-95 transition-transform duration-300 ease-out"
             _="on modal show transition transform scale-100">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js setup if needed, though most modal logic is via htmx and inline _ -->
<script>
    // Example: If you needed Alpine.js to manage more complex state in the future:
    document.addEventListener('alpine:init', () => {
        Alpine.data('designPlanList', () => ({
            // No specific state needed here, as HTMX handles reloads.
            // But this is where you'd put Alpine.js for complex UI components.
        }));
    });
</script>
{% endblock %}
```

**File: `_designplan_table.html` (Partial for HTMX)**

```html
<table id="designplanTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in designplans %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-blue-600">{{ obj.plan_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.start_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.end_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">
                {% if obj.is_active %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                {% else %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">No</span>
                {% endif %}
            </td>
            <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                    class="text-yellow-600 hover:text-yellow-900 mr-4"
                    hx-get="{% url 'designplan_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="text-red-600 hover:text-red-900"
                    hx-get="{% url 'designplan_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-6 text-center text-gray-500">No design plans found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#designplanTable')) {
            $('#designplanTable').DataTable().destroy(); // Destroy previous instance if any
        }
        $('#designplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [5] } // Disable sorting on Actions column
            ]
        });
    });
</script>
```

**File: `_designplan_form.html` (Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Design Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.add('hidden'); document.getElementById('modal').classList.remove('flex', 'opacity-100'); }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modal then add .hidden to #modal then remove .flex from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save Design Plan
            </button>
        </div>
    </form>
</div>
```

**File: `confirm_delete.html` (Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Design Plan: <span class="font-bold">"{{ object.plan_name }}"</span>?</p>
    
    <form hx-post="{% url 'designplan_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.add('hidden'); document.getElementById('modal').classList.remove('flex', 'opacity-100'); }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modal then add .hidden to #modal then remove .flex from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs
**File: `dailyreporting/urls.py`**

```python
from django.urls import path
from .views import (
    DesignPlanListView,
    DesignPlanTablePartialView,
    DesignPlanCreateView,
    DesignPlanUpdateView,
    DesignPlanDeleteView
)

urlpatterns = [
    # Main list view (initial page load)
    path('designplan/', DesignPlanListView.as_view(), name='designplan_list'),
    
    # HTMX partial endpoint for table refresh
    path('designplan/table/', DesignPlanTablePartialView.as_view(), name='designplan_table_partial'),
    
    # CRUD operations (loaded into modal via HTMX)
    path('designplan/add/', DesignPlanCreateView.as_view(), name='designplan_add'),
    path('designplan/edit/<int:pk>/', DesignPlanUpdateView.as_view(), name='designplan_edit'),
    path('designplan/delete/<int:pk>/', DesignPlanDeleteView.as_view(), name='designplan_delete'),
]

```

### 4.6 Tests
**File: `dailyreporting/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from .models import DesignPlan
from .forms import DesignPlanForm

class DesignPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        DesignPlan.objects.create(
            plan_name='Initial Design Plan',
            description='This is a test description.',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=30),
            is_active=True
        )
        DesignPlan.objects.create(
            plan_name='Inactive Plan',
            description='Another test description.',
            start_date=date.today() - timedelta(days=60),
            end_date=date.today() - timedelta(days=30),
            is_active=False
        )
  
    def test_design_plan_creation(self):
        obj = DesignPlan.objects.get(plan_name='Initial Design Plan')
        self.assertEqual(obj.plan_name, 'Initial Design Plan')
        self.assertEqual(obj.description, 'This is a test description.')
        self.assertTrue(obj.is_active)
        self.assertFalse(obj.pk is None) # Ensure PK is assigned

    def test_plan_name_label(self):
        obj = DesignPlan.objects.get(plan_name='Initial Design Plan')
        field_label = obj._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')
        
    def test_db_table_and_managed(self):
        self.assertEqual(DesignPlan._meta.db_table, 'tblDesignPlan')
        self.assertFalse(DesignPlan._meta.managed)

    def test_str_method(self):
        obj = DesignPlan.objects.get(plan_name='Initial Design Plan')
        self.assertEqual(str(obj), 'Initial Design Plan')

    def test_is_currently_active_method(self):
        # Test active plan
        obj = DesignPlan.objects.get(plan_name='Initial Design Plan')
        self.assertTrue(obj.is_currently_active())

        # Test inactive plan (by dates)
        obj_past = DesignPlan.objects.get(plan_name='Inactive Plan')
        self.assertFalse(obj_past.is_currently_active())

        # Test inactive plan (by is_active field)
        DesignPlan.objects.create(
            plan_name='Future Inactive Plan',
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30),
            is_active=False
        )
        obj_future_inactive = DesignPlan.objects.get(plan_name='Future Inactive Plan')
        self.assertFalse(obj_future_inactive.is_currently_active())
        
    def test_get_summary_method(self):
        obj = DesignPlan.objects.get(plan_name='Initial Design Plan')
        expected_summary = f"Initial Design Plan ({date.today().strftime('%Y-%m-%d')} to {(date.today() + timedelta(days=30)).strftime('%Y-%m-%d')})"
        self.assertEqual(obj.get_summary(), expected_summary)

class DesignPlanFormTest(TestCase):
    def test_form_valid(self):
        form_data = {
            'plan_name': 'Test Plan',
            'description': 'Description for test plan',
            'start_date': '2023-01-01',
            'end_date': '2023-01-31',
            'is_active': True,
        }
        form = DesignPlanForm(data=form_data)
        self.assertTrue(form.is_valid())
        
    def test_form_invalid_dates(self):
        form_data = {
            'plan_name': 'Invalid Date Plan',
            'description': 'Description for invalid date plan',
            'start_date': '2023-01-31',
            'end_date': '2023-01-01', # End date before start date
            'is_active': True,
        }
        form = DesignPlanForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('end_date', form.errors)
        self.assertIn('End date cannot be before start date.', form.errors['end_date'])

class DesignPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        DesignPlan.objects.create(
            plan_name='View Test Plan',
            description='Description for view test.',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=10),
            is_active=True
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('designplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/list.html')
        self.assertIn('designplans', response.context)
        self.assertContains(response, 'View Test Plan') # Check if object name is in content

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('designplan_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_table.html')
        self.assertIn('designplans', response.context)
        self.assertContains(response, 'View Test Plan') # Check if object name is in content
        
    def test_create_view_get(self):
        response = self.client.get(reverse('designplan_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Design Plan') # Check for form title

    def test_create_view_post_success(self):
        data = {
            'plan_name': 'New Plan',
            'description': 'New plan description.',
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'is_active': True,
        }
        response = self.client.post(reverse('designplan_add'), data)
        # For non-HTMX, it would redirect. For HTMX, it returns 204.
        self.assertEqual(response.status_code, 302) # Default behavior without HX-Request header
        self.assertTrue(DesignPlan.objects.filter(plan_name='New Plan').exists())
        self.assertRedirects(response, reverse('designplan_list'))

    def test_create_view_post_htmx_success(self):
        data = {
            'plan_name': 'HTMX New Plan',
            'description': 'HTMX new plan description.',
            'start_date': '2024-02-01',
            'end_date': '2024-02-29',
            'is_active': True,
        }
        response = self.client.post(reverse('designplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        self.assertTrue(DesignPlan.objects.filter(plan_name='HTMX New Plan').exists())

    def test_create_view_post_invalid(self):
        data = {
            'plan_name': '', # Invalid: required field missing
            'description': 'Invalid data.',
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'is_active': True,
        }
        response = self.client.post(reverse('designplan_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        obj = DesignPlan.objects.get(plan_name='View Test Plan')
        response = self.client.get(reverse('designplan_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)
        self.assertContains(response, 'Edit Design Plan') # Check for form title

    def test_update_view_post_success(self):
        obj = DesignPlan.objects.get(plan_name='View Test Plan')
        data = {
            'plan_name': 'Updated Plan Name',
            'description': obj.description,
            'start_date': obj.start_date.strftime('%Y-%m-%d'),
            'end_date': obj.end_date.strftime('%Y-%m-%d'),
            'is_active': False, # Change a field
        }
        response = self.client.post(reverse('designplan_edit', args=[obj.pk]), data)
        self.assertEqual(response.status_code, 302) # Default redirect
        obj.refresh_from_db()
        self.assertEqual(obj.plan_name, 'Updated Plan Name')
        self.assertFalse(obj.is_active)

    def test_update_view_post_htmx_success(self):
        obj = DesignPlan.objects.get(plan_name='View Test Plan')
        data = {
            'plan_name': 'HTMX Updated Plan',
            'description': obj.description,
            'start_date': obj.start_date.strftime('%Y-%m-%d'),
            'end_date': obj.end_date.strftime('%Y-%m-%d'),
            'is_active': obj.is_active,
        }
        response = self.client.post(reverse('designplan_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        obj.refresh_from_db()
        self.assertEqual(obj.plan_name, 'HTMX Updated Plan')

    def test_delete_view_get(self):
        obj = DesignPlan.objects.get(plan_name='View Test Plan')
        response = self.client.get(reverse('designplan_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], obj)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post_success(self):
        obj_to_delete = DesignPlan.objects.create(
            plan_name='Delete Test Plan',
            description='This plan will be deleted.',
            start_date=date.today(),
            end_date=date.today(),
            is_active=True
        )
        response = self.client.post(reverse('designplan_delete', args=[obj_to_delete.pk]))
        self.assertEqual(response.status_code, 302) # Default redirect
        self.assertFalse(DesignPlan.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertRedirects(response, reverse('designplan_list'))

    def test_delete_view_post_htmx_success(self):
        obj_to_delete = DesignPlan.objects.create(
            plan_name='HTMX Delete Test Plan',
            description='This plan will be deleted via HTMX.',
            start_date=date.today(),
            end_date=date.today(),
            is_active=True
        )
        response = self.client.post(reverse('designplan_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        self.assertFalse(DesignPlan.objects.filter(pk=obj_to_delete.pk).exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:
The generated templates and views already include the necessary HTMX and Alpine.js integration as per the AutoERP guidelines:
-   **HTMX for dynamic updates:**
    *   `hx-get` is used on buttons to fetch forms (add/edit/delete) into the modal.
    *   `hx-target` and `hx-swap` are used to place the fetched content into `#modalContent`.
    *   `hx-post` is used for form submissions (create/update/delete).
    *   `hx-on::after-request` on forms handles closing the modal and triggering a list refresh if the submission is successful (status 204).
    *   `hx-trigger="load, refreshDesignPlanList from:body"` on the `designplanTable-container` ensures the table loads on initial page load and refreshes when the `refreshDesignPlanList` custom event is fired (e.g., after a CRUD operation).
-   **Alpine.js/`_` for UI state management:**
    *   `_="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"` is used to show the modal with a smooth transition when triggered by HTMX requests.
    *   `_="on click if event.target.id == 'modal' remove .opacity-100 from me then add .hidden to me"` handles closing the modal when clicking outside its content.
    *   `_="on click remove .opacity-100 from #modal then add .hidden to #modal then remove .flex from #modal"` on "Cancel" buttons for closing the modal gracefully.
-   **DataTables for list views:**
    *   The `_designplan_table.html` partial includes the JavaScript to initialize DataTables on the `#designplanTable` element. This ensures client-side searching, sorting, and pagination. It also includes logic to destroy and re-initialize DataTables to prevent issues when the partial is reloaded via HTMX.
-   **No full page reloads:** All interactions are designed to work seamlessly via HTMX, updating only the necessary parts of the page, providing a fast and modern user experience.

## Final Notes

*   **Placeholders:** `[APP_NAME]` is `dailyreporting`, `[MODEL_NAME]` is `DesignPlan`, `[MODEL_NAME_LOWER]` is `designplan`, and `[MODEL_NAME_PLURAL_LOWER]` is `designplans`. These have been replaced throughout the code.
*   **DRY templates:** The use of `_designplan_table.html` and `_designplan_form.html` as partials ensures that the table rendering and form rendering logic are reusable and separate from the main page structure.
*   **Fat Models, Thin Views:** Business logic, such as `is_currently_active` and `get_summary`, resides within the `DesignPlan` model. Views are kept concise (under 15 lines for core logic), primarily handling HTTP request/response flow and delegating to forms and models for data processing and validation.
*   **Comprehensive Tests:** Unit tests for model methods and properties, and integration tests for all view interactions (GET, POST, HTMX specific behaviors) are included to ensure robustness and maintainability.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for styling, assuming Tailwind CSS is correctly set up in the Django project.
*   **CDN Links:** It is assumed that `core/base.html` includes necessary CDN links for HTMX, Alpine.js, jQuery, and DataTables.