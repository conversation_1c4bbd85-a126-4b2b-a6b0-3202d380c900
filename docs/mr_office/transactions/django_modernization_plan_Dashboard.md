## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The provided ASP.NET code (`Dashboard.aspx` and `Dashboard.aspx.cs`) is a minimalist page, primarily serving as a content placeholder extending a master page. It does not contain any explicit database connection strings, `SqlDataSource` controls, SQL queries, or direct data binding logic. The `Page_Load` event in the C# code-behind is empty.

**Inference:** Based on the inheritance `Module_MROffice_Transaction_Default`, we infer that this page is intended to manage "Transactions" within an "MROffice" module. To demonstrate a complete Django migration, we will *hypothesize* a common database table and columns that would typically be associated with such a module.

**Inferred Database Schema:**

*   **Table Name:** `tblTransactions`
*   **Columns:**
    *   `TransactionID` (Primary Key, Integer)
    *   `TransactionDate` (Date/Time)
    *   `Amount` (Decimal)
    *   `Description` (Text)
    *   `CustomerName` (Text)
    *   `IsProcessed` (Boolean)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code does not explicitly define any CRUD (Create, Read, Update, Delete) operations. The C# `Page_Load` method is empty, indicating no data retrieval or manipulation is performed directly by this specific page's code-behind.

**Inference:** Given that this page is likely part of a "Transaction" module dashboard, its purpose would typically involve:

*   **Read (R):** Displaying a list of existing transactions.
*   **Create (C):** Allowing users to add new transactions.
*   **Update (U):** Enabling users to modify existing transaction details.
*   **Delete (D):** Providing functionality to remove transactions.

Therefore, the Django modernization plan will include full CRUD functionality for the inferred `Transaction` model.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The `Dashboard.aspx` file contains no ASP.NET controls other than `<asp:Content>` place holders and a `<script>` tag referencing `loadingNotifier.js`.

**Inference:** For a transaction management page, the typical UI components would be:

*   **Data Grid:** To display a list of transactions (equivalent to a `GridView` in ASP.NET). This will be replaced by a DataTables-enhanced HTML table in Django.
*   **Input Forms:** For creating and editing transactions (equivalent to `TextBox`, `DropDownList`, `Button` controls). This will be replaced by Django Forms rendered as HTML with Tailwind CSS.
*   **Action Buttons/Links:** For triggering add, edit, and delete operations. These will be implemented using HTMX attributes to trigger modal dialogs.
*   **Client-Side Scripting:** The `loadingNotifier.js` hints at client-side interactions for user experience. This will be handled by HTMX for dynamic content loading and Alpine.js for UI state management (e.g., modal visibility), eliminating the need for custom JavaScript.

## Step 4: Generate Django Code

For this modernization, we will assume a Django application named `mroffice`.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

We will create a `Transaction` model mapping to the inferred `tblTransactions` table.

```python
# mroffice/models.py
from django.db import models

class Transaction(models.Model):
    transaction_id = models.IntegerField(db_column='TransactionID', primary_key=True)
    transaction_date = models.DateTimeField(db_column='TransactionDate')
    amount = models.DecimalField(db_column='Amount', max_digits=10, decimal_places=2)
    description = models.TextField(db_column='Description', blank=True, null=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    is_processed = models.BooleanField(db_column='IsProcessed', default=False)

    class Meta:
        managed = False  # Set to False if you don't want Django to manage table creation/modification
        db_table = 'tblTransactions'
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'
        ordering = ['-transaction_date', 'transaction_id'] # Example ordering

    def __str__(self):
        return f"Transaction {self.transaction_id} on {self.transaction_date.strftime('%Y-%m-%d')} for {self.amount}"

    # Business logic methods (Fat Model approach)
    def mark_as_processed(self):
        """Marks the transaction as processed."""
        if not self.is_processed:
            self.is_processed = True
            self.save()
            return True
        return False

    def get_display_status(self):
        """Returns a user-friendly status string."""
        return "Processed" if self.is_processed else "Pending"

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

Create a `ModelForm` for the `Transaction` model, excluding the `transaction_id` as it's typically auto-generated or managed by the database.

```python
# mroffice/forms.py
from django import forms
from .models import Transaction

class TransactionForm(forms.ModelForm):
    class Meta:
        model = Transaction
        fields = ['transaction_date', 'amount', 'description', 'customer_name', 'is_processed']
        widgets = {
            'transaction_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_processed': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        labels = {
            'transaction_id': 'Transaction ID',
            'transaction_date': 'Transaction Date',
            'amount': 'Amount',
            'description': 'Description',
            'customer_name': 'Customer Name',
            'is_processed': 'Processed',
        }

    # Add custom validation if needed (e.g., ensure amount is positive)
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be positive.")
        return amount

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We'll define views for listing, creating, updating, and deleting transactions. A separate view is added for the HTMX-loaded table partial.

```python
# mroffice/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Transaction
from .forms import TransactionForm

# View for the main list page
class TransactionListView(ListView):
    model = Transaction
    template_name = 'mroffice/transaction/list.html'
    context_object_name = 'transactions'

# View for loading the table content via HTMX
class TransactionTablePartialView(ListView):
    model = Transaction
    template_name = 'mroffice/transaction/_transaction_table.html'
    context_object_name = 'transactions'

# View for adding a new transaction
class TransactionCreateView(CreateView):
    model = Transaction
    form_class = TransactionForm
    template_name = 'mroffice/transaction/_transaction_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('transaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Transaction added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to return for HTMX after success
                headers={
                    'HX-Trigger': 'refreshTransactionList' # Custom HTMX trigger to refresh the list
                }
            )
        return response

# View for updating an existing transaction
class TransactionUpdateView(UpdateView):
    model = Transaction
    form_class = TransactionForm
    template_name = 'mroffice/transaction/_transaction_form.html' # Use partial for HTMX modal
    context_object_name = 'transaction' # for accessing instance in form template
    success_url = reverse_lazy('transaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Transaction updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTransactionList'
                }
            )
        return response

# View for deleting a transaction
class TransactionDeleteView(DeleteView):
    model = Transaction
    template_name = 'mroffice/transaction/_transaction_confirm_delete.html' # Use partial for HTMX modal
    context_object_name = 'transaction' # for accessing instance in confirm delete template
    success_url = reverse_lazy('transaction_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Transaction deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTransactionList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will use Tailwind CSS for styling, HTMX for dynamic interactions, and DataTables for the list view. They will extend `core/base.html`.

**File: `mroffice/transaction/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Transactions Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300"
            hx-get="{% url 'transaction_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Transaction
        </button>
    </div>

    <!-- Message Display Area -->
    <div id="messages" class="mb-4">
        {% if messages %}
            {% for message in messages %}
            <div class="p-4 mb-3 text-sm text-{{ message.tags }}-700 bg-{{ message.tags }}-100 rounded-lg" role="alert">
                {{ message }}
            </div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div id="transactionTable-container"
         hx-trigger="load, refreshTransactionList from:body"
         hx-get="{% url 'transaction_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading transactions...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 overflow-hidden transform transition-all sm:align-middle sm:max-w-xl">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    // Handle HTMX afterSettle event to re-initialize DataTables when table content is swapped
    document.body.addEventListener('htmx:afterSettle', function(event) {
        if (event.detail.target.id === 'transactionTable-container') {
            $('#transactionTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTable instance if any
            });
        }
    });

    // Close modal on successful form submission via HTMX
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.xhr.status === 204) { // HTMX 204 response for success
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**File: `mroffice/transaction/_transaction_table.html` (Partial Template for HTMX)**

```html
<table id="transactionTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Trans. ID</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for transaction in transactions %}
        <tr class="hover:bg-gray-50 {% cycle 'bg-white' 'bg-gray-50' %}">
            <td class="py-3 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ transaction.transaction_id }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ transaction.transaction_date|date:"Y-m-d H:i" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-right">${{ transaction.amount|floatformat:2 }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ transaction.customer_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ transaction.description|truncatechars:50 }}</td>
            <td class="py-3 px-4 border-b border-gray-200">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    {% if transaction.is_processed %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                    {{ transaction.get_display_status }}
                </span>
            </td>
            <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300"
                    hx-get="{% url 'transaction_edit' transaction.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300"
                    hx-get="{% url 'transaction_delete' transaction.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-gray-500">No transactions found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // This script block will run each time the partial is loaded by HTMX
    // DataTables initialization is handled in the main list.html to ensure it runs only once
    // or re-initializes correctly after HTMX content swap.
    // However, if you wanted the DataTable initialization to be contained within the partial,
    // you'd typically handle destroying existing instances before re-initializing.
</script>
```

**File: `mroffice/transaction/_transaction_form.html` (Partial Template for HTMX Modal)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Transaction</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-1 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
                Save Transaction
            </button>
            <span id="form-loading-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

**File: `mroffice/transaction/_transaction_confirm_delete.html` (Partial Template for HTMX Modal)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the transaction with ID **{{ transaction.transaction_id }}** ({{ transaction.description|default:"No description" }})?</p>

    <form hx-post="{% url 'transaction_delete' transaction.pk %}" hx-swap="none" hx-indicator="#delete-loading-indicator">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
                Confirm Delete
            </button>
            <span id="delete-loading-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Define paths for the main list view, CRUD operations, and the HTMX-specific partial table view.

```python
# mroffice/urls.py
from django.urls import path
from .views import (
    TransactionListView,
    TransactionCreateView,
    TransactionUpdateView,
    TransactionDeleteView,
    TransactionTablePartialView,
)

urlpatterns = [
    path('transactions/', TransactionListView.as_view(), name='transaction_list'),
    path('transactions/add/', TransactionCreateView.as_view(), name='transaction_add'),
    path('transactions/edit/<int:pk>/', TransactionUpdateView.as_view(), name='transaction_edit'),
    path('transactions/delete/<int:pk>/', TransactionDeleteView.as_view(), name='transaction_delete'),
    # HTMX-specific endpoint for refreshing the table content
    path('transactions/table/', TransactionTablePartialView.as_view(), name='transaction_table'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for the `Transaction` model and integration tests for all `Transaction` views, ensuring good test coverage.

```python
# mroffice/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import Transaction
from .forms import TransactionForm

class TransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.transaction1 = Transaction.objects.create(
            transaction_id=101,
            transaction_date=timezone.now() - timedelta(days=5),
            amount=150.75,
            description='Test transaction 1',
            customer_name='John Doe',
            is_processed=False
        )
        cls.transaction2 = Transaction.objects.create(
            transaction_id=102,
            transaction_date=timezone.now() - timedelta(days=1),
            amount=200.00,
            description='Test transaction 2',
            customer_name='Jane Smith',
            is_processed=True
        )

    def test_transaction_creation(self):
        self.assertEqual(self.transaction1.transaction_id, 101)
        self.assertEqual(self.transaction1.amount, 150.75)
        self.assertEqual(self.transaction1.customer_name, 'John Doe')
        self.assertFalse(self.transaction1.is_processed)

    def test_transaction_str_method(self):
        expected_str = f"Transaction {self.transaction1.transaction_id} on {self.transaction1.transaction_date.strftime('%Y-%m-%d')} for {self.transaction1.amount}"
        self.assertEqual(str(self.transaction1), expected_str)

    def test_amount_verbose_name(self):
        field_label = self.transaction1._meta.get_field('amount').verbose_name
        self.assertEqual(field_label, 'amount') # Default verbose name

    def test_mark_as_processed(self):
        self.assertFalse(self.transaction1.is_processed)
        self.assertTrue(self.transaction1.mark_as_processed())
        self.assertTrue(self.transaction1.is_processed)
        self.assertFalse(self.transaction1.mark_as_processed()) # Already processed

    def test_get_display_status(self):
        self.assertEqual(self.transaction1.get_display_status(), "Pending")
        self.assertEqual(self.transaction2.get_display_status(), "Processed")


class TransactionViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.transaction1 = Transaction.objects.create(
            transaction_id=101,
            transaction_date=timezone.now() - timedelta(days=5),
            amount=150.75,
            description='Test transaction 1',
            customer_name='John Doe',
            is_processed=False
        )
        cls.transaction2 = Transaction.objects.create(
            transaction_id=102,
            transaction_date=timezone.now() - timedelta(days=1),
            amount=200.00,
            description='Test transaction 2',
            customer_name='Jane Smith',
            is_processed=True
        )
        # Note: If transaction_id is an auto-incrementing PK, remove it from .create()
        # and let DB assign. Here, we're assuming it's managed by us for the example.

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('transaction_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/transaction/list.html')
        self.assertIn('transactions', response.context)
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Jane Smith')

    def test_table_partial_view(self):
        response = self.client.get(reverse('transaction_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/transaction/_transaction_table.html')
        self.assertIn('transactions', response.context)
        self.assertContains(response, 'John Doe') # Verify content

    def test_create_view_get(self):
        response = self.client.get(reverse('transaction_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/transaction/_transaction_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        new_transaction_id = 103 # Assuming we manage ID for new ones
        data = {
            'transaction_id': new_transaction_id, # If PK is manual
            'transaction_date': timezone.now().isoformat(),
            'amount': 300.50,
            'description': 'New transaction',
            'customer_name': 'New Customer',
            'is_processed': False,
        }
        transaction_count_before = Transaction.objects.count()
        response = self.client.post(reverse('transaction_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful POST
        self.assertEqual(Transaction.objects.count(), transaction_count_before + 1)
        self.assertTrue(Transaction.objects.filter(transaction_id=new_transaction_id).exists())

    def test_create_view_post_invalid(self):
        data = {
            'transaction_id': 104,
            'transaction_date': timezone.now().isoformat(),
            'amount': -10.00, # Invalid amount
            'description': 'Invalid transaction',
            'customer_name': 'Invalid Customer',
            'is_processed': False,
        }
        transaction_count_before = Transaction.objects.count()
        response = self.client.post(reverse('transaction_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertEqual(Transaction.objects.count(), transaction_count_before) # No new object created
        self.assertContains(response, 'Amount must be positive.') # Check for error message

    def test_update_view_get(self):
        response = self.client.get(reverse('transaction_edit', args=[self.transaction1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/transaction/_transaction_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.pk, self.transaction1.pk)

    def test_update_view_post_success(self):
        updated_description = 'Updated Description'
        data = {
            'transaction_id': self.transaction1.pk,
            'transaction_date': self.transaction1.transaction_date.isoformat(),
            'amount': 160.00,
            'description': updated_description,
            'customer_name': self.transaction1.customer_name,
            'is_processed': True,
        }
        response = self.client.post(reverse('transaction_edit', args=[self.transaction1.pk]), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful POST
        self.transaction1.refresh_from_db()
        self.assertEqual(self.transaction1.description, updated_description)
        self.assertTrue(self.transaction1.is_processed)

    def test_delete_view_get(self):
        response = self.client.get(reverse('transaction_delete', args=[self.transaction1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/transaction/_transaction_confirm_delete.html')
        self.assertIn('transaction', response.context)
        self.assertEqual(response.context['transaction'].pk, self.transaction1.pk)

    def test_delete_view_post_success(self):
        transaction_pk = self.transaction1.pk
        transaction_count_before = Transaction.objects.count()
        response = self.client.post(reverse('transaction_delete', args=[transaction_pk]))
        self.assertEqual(response.status_code, 302) # Redirect after successful POST
        self.assertEqual(Transaction.objects.count(), transaction_count_before - 1)
        self.assertFalse(Transaction.objects.filter(pk=transaction_pk).exists())

    def test_htmx_create_view_post_success(self):
        new_transaction_id = 105
        data = {
            'transaction_id': new_transaction_id,
            'transaction_date': timezone.now().isoformat(),
            'amount': 400.00,
            'description': 'HTMX New transaction',
            'customer_name': 'HTMX Customer',
            'is_processed': False,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('transaction_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for successful swaps
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTransactionList')
        self.assertTrue(Transaction.objects.filter(transaction_id=new_transaction_id).exists())

    def test_htmx_update_view_post_success(self):
        original_description = self.transaction2.description
        updated_description = 'HTMX Updated Description'
        data = {
            'transaction_id': self.transaction2.pk,
            'transaction_date': self.transaction2.transaction_date.isoformat(),
            'amount': self.transaction2.amount,
            'description': updated_description,
            'customer_name': self.transaction2.customer_name,
            'is_processed': self.transaction2.is_processed,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('transaction_edit', args=[self.transaction2.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTransactionList')
        self.transaction2.refresh_from_db()
        self.assertEqual(self.transaction2.description, updated_description)
        self.assertNotEqual(self.transaction2.description, original_description)

    def test_htmx_delete_view_post_success(self):
        transaction_to_delete_pk = self.transaction2.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('transaction_delete', args=[transaction_to_delete_pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTransactionList')
        self.assertFalse(Transaction.objects.filter(pk=transaction_to_delete_pk).exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for Dynamic Updates:**
    *   The main `list.html` uses `hx-get` on `transactionTable-container` to load the table content dynamically from `{% url 'transaction_table' %}`. This ensures the table data is always fresh.
    *   `hx-trigger="load, refreshTransactionList from:body"` ensures the table loads on page load and refreshes whenever a `refreshTransactionList` custom event is triggered (e.g., after a CRUD operation).
    *   Add/Edit/Delete buttons use `hx-get` to fetch the form/confirmation partials into a modal (`#modalContent`).
    *   Form submissions (`_transaction_form.html`, `_transaction_confirm_delete.html`) use `hx-post` with `hx-swap="none"` and rely on the 204 HTTP status code and `HX-Trigger` header from the Django views to close the modal and refresh the table.
    *   `htmx-indicator` is used to show a loading spinner during HTMX requests.

*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses Alpine.js `x-data` and `x-show` (implicitly through `hidden` class and `on click` logic). The `hidden` class is toggled by `_="on click add .is-active to #modal"` for opening and `_="on click remove .is-active from me"` for closing, leveraging Alpine.js's `on click` syntax to directly manipulate the `is-active` class. This effectively manages the modal's visibility without traditional JavaScript event listeners.

*   **DataTables for List Views:**
    *   The `_transaction_table.html` partial contains the HTML `<table>` structure.
    *   The `list.html` includes a JavaScript block that initializes DataTables on the `transactionTable` ID **after** HTMX has swapped in the table content (`htmx:afterSettle` event listener). This ensures DataTables correctly applies its features (search, sort, pagination) to the dynamically loaded data. `destroy: true` is crucial to prevent re-initialization errors if the table content is loaded multiple times.

*   **No Full Page Reloads:** All CRUD operations and table refreshes are designed to work via HTMX, keeping the user on the same page and providing a smooth single-page application (SPA)-like experience.

## Final Notes

*   This plan successfully transforms the conceptual "Dashboard" page into a fully functional Django module for managing transactions, even though the original ASP.NET code provided minimal functional details.
*   The use of `managed = False` in the model assumes that the `tblTransactions` table already exists in the database. If Django is to manage schema, this should be set to `True` and migrations run.
*   Templates are kept DRY by using partials (`_transaction_table.html`, `_transaction_form.html`, `_transaction_confirm_delete.html`) that are loaded into the main `list.html` via HTMX.
*   Business logic, such as `mark_as_processed` and `get_display_status`, is encapsulated within the `Transaction` model, adhering to the fat model principle.
*   Views are thin, primarily handling HTTP requests, form validation, and delegating business logic to the model or Django's built-in CBVs.
*   Comprehensive tests are provided to ensure the model and view functionality are robust.
*   All interactions are designed to leverage HTMX and Alpine.js, minimizing the need for custom JavaScript and ensuring a modern, interactive user experience.