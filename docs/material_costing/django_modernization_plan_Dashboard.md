## ASP.NET to Django Conversion Script:

This document outlines a strategic plan to modernize your legacy ASP.NET Dashboard module within the Material Costing section into a robust, modern Django application. Our approach focuses on leveraging AI-assisted automation, minimizing manual coding, and ensuring a smooth transition with clear, actionable steps.

**Business Value of this Modernization:**

*   **Enhanced Agility & Scalability:** Django’s modern architecture makes your application easier to update, maintain, and scale to meet future business demands.
*   **Improved User Experience:** By adopting HTMX and Alpine.js, the user interface will become more responsive and interactive, providing a smoother experience akin to a single-page application without the complexity.
*   **Reduced Development Costs:** The "fat model, thin view" pattern, combined with AI-assisted automation, simplifies development and maintenance, leading to significant cost savings.
*   **Future-Proof Technology Stack:** Moving to Django, HTMX, and Alpine.js ensures your application is built on widely adopted, actively maintained, and secure open-source technologies.
*   **Standardized & Testable Codebase:** Our approach emphasizes clear separation of concerns and comprehensive testing, leading to a more reliable and less error-prone application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

The provided ASP.NET code is a minimalist Dashboard page lacking direct database interaction within the `.aspx` or code-behind files. It appears to be a container page within the `Module_MaterialCosting` section. For a typical "Dashboard" in a "Material Costing" module, we infer that it would likely display or manage "Material Costing" records.

**Inferred Database Table:** `tbl_material_costing`

**Inferred Columns and Data Types:**

*   `material_cost_id` (Primary Key, Integer)
*   `material_name` (Text/Varchar, e.g., 'Steel Bar')
*   `unit_cost` (Decimal/Money, e.g., 12.50)
*   `quantity` (Integer, e.g., 100)
*   `total_cost` (Decimal/Money, calculated, e.g., 1250.00)
*   `date_recorded` (Date/DateTime, e.g., 2023-10-26)
*   `is_active` (Boolean, e.g., 1/0 for soft deletes or status)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The provided ASP.NET Dashboard page (`Dashboard.aspx` and `Dashboard.aspx.cs`) has an empty `Page_Load` event and no declared server controls (like `GridView`, `TextBox`, `Button`) within the `.aspx` content sections. This indicates that this specific ASP.NET page is a static container or relies on other mechanisms (e.g., client-side scripts, master page elements not provided, or other linked modules) for its functionality.

Given it's a "Dashboard" for "Material Costing", the typical expected functionality would be:

*   **Read:** Displaying a list of material costing records.
*   **Create:** Adding new material costing records.
*   **Update:** Editing existing material costing records.
*   **Delete:** Removing material costing records.

Since no explicit logic is present, we will implement these standard CRUD operations in Django, assuming this is the intended purpose of a "Material Costing Dashboard" in a business application.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

Similar to the backend functionality, the provided ASP.NET markup for `Dashboard.aspx` contains no specific UI controls within its content placeholders. It only includes a `loadingNotifier.js` script, suggesting some form of client-side loading feedback.

Based on the inferred CRUD operations (Step 2) and typical business application patterns, we will infer the following UI components for the Django version:

*   **Data Table:** A main table to display the list of `MaterialCosting` records, similar to an ASP.NET `GridView`, but implemented with `DataTables.js` for enhanced client-side sorting, filtering, and pagination.
*   **Add Button:** A button to trigger the creation of a new `MaterialCosting` record, which will open a form in a modal.
*   **Edit Button:** Buttons next to each record in the data table to open an edit form for that specific record in a modal.
*   **Delete Button:** Buttons next to each record to confirm and delete a record, also within a modal.
*   **Form (Modal):** A reusable form for creating and updating `MaterialCosting` records, displayed dynamically using HTMX within a modal.
*   **Delete Confirmation (Modal):** A confirmation dialog for delete operations, also displayed dynamically using HTMX within a modal.

Client-side interactions will be managed by HTMX (for dynamic content loading and form submissions) and Alpine.js (for simple UI state, such as managing modal visibility). The `loadingNotifier.js` will be conceptually replaced by HTMX's built-in loading indicators and custom Alpine.js state management.

---

### Step 4: Generate Django Code

We will create a new Django application named `materialcosting` to house this module.

#### 4.1 Models

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**

This model `MaterialCosting` will map directly to the existing `tbl_material_costing` database table. It includes methods for business logic, such as calculating `total_cost`.

**File:** `materialcosting/models.py`

```python
from django.db import models
from decimal import Decimal

class MaterialCosting(models.Model):
    """
    Represents a record of material costing in the ERP system.
    This model maps to the existing 'tbl_material_costing' table.
    """
    material_cost_id = models.AutoField(
        db_column='material_cost_id',
        primary_key=True,
        verbose_name="ID"
    )
    material_name = models.CharField(
        db_column='material_name',
        max_length=255,
        verbose_name="Material Name",
        help_text="The name of the material."
    )
    unit_cost = models.DecimalField(
        db_column='unit_cost',
        max_digits=10,
        decimal_places=2,
        verbose_name="Unit Cost",
        help_text="Cost per unit of the material."
    )
    quantity = models.IntegerField(
        db_column='quantity',
        verbose_name="Quantity",
        help_text="Number of units of the material."
    )
    # total_cost could be a stored field or calculated on demand
    # For fat model, we will calculate it, but keep db_column for potential existing column
    total_cost = models.DecimalField(
        db_column='total_cost',
        max_digits=10,
        decimal_places=2,
        null=True, # Allow null if the field is not always populated from DB
        blank=True,
        verbose_name="Total Cost",
        help_text="Calculated total cost (Unit Cost * Quantity)."
    )
    date_recorded = models.DateField(
        db_column='date_recorded',
        verbose_name="Date Recorded",
        help_text="Date when the costing was recorded."
    )
    is_active = models.BooleanField(
        db_column='is_active',
        default=True,
        verbose_name="Is Active",
        help_text="Indicates if the record is active."
    )

    class Meta:
        managed = False  # Important: Django will not create/manage this table.
        db_table = 'tbl_material_costing'
        verbose_name = 'Material Costing Record'
        verbose_name_plural = 'Material Costing Records'
        ordering = ['-date_recorded', 'material_name']

    def __str__(self):
        """Returns a string representation of the material costing record."""
        return f"{self.material_name} (ID: {self.material_cost_id})"
        
    def save(self, *args, **kwargs):
        """
        Overrides save to calculate total_cost before saving.
        This ensures business logic is encapsulated in the model.
        """
        self.total_cost = self.unit_cost * Decimal(self.quantity)
        super().save(*args, **kwargs)

    def get_display_total_cost(self):
        """
        Business logic: Format the total cost for display.
        """
        return f"${self.total_cost:,.2f}"

    def soft_delete(self):
        """
        Business logic: Soft deletes the record by setting is_active to False.
        """
        self.is_active = False
        self.save()

    def activate(self):
        """
        Business logic: Activates the record by setting is_active to True.
        """
        self.is_active = True
        self.save()

```

#### 4.2 Forms

**Task:** Define a Django form for user input for `MaterialCosting`.

**Instructions:**

A `ModelForm` will be created to handle validation and rendering of the `MaterialCosting` model fields. Tailwind CSS classes are applied via widgets for consistent styling.

**File:** `materialcosting/forms.py`

```python
from django import forms
from .models import MaterialCosting

class MaterialCostingForm(forms.ModelForm):
    """
    Form for creating and updating MaterialCosting records.
    """
    class Meta:
        model = MaterialCosting
        fields = ['material_name', 'unit_cost', 'quantity', 'date_recorded', 'is_active']
        widgets = {
            'material_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Steel Bar'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 12.50',
                'step': '0.01'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 100'
            }),
            'date_recorded': forms.DateInput(attrs={
                'type': 'date', # Renders a date picker in modern browsers
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
            }),
        }
        labels = {
            'material_name': 'Material Name',
            'unit_cost': 'Unit Cost ($)',
            'quantity': 'Quantity (Units)',
            'date_recorded': 'Date Recorded',
            'is_active': 'Active Record',
        }

    def clean_quantity(self):
        """
        Custom validation for quantity to ensure it's a positive integer.
        """
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive number.")
        return quantity

    def clean_unit_cost(self):
        """
        Custom validation for unit_cost to ensure it's a positive decimal.
        """
        unit_cost = self.cleaned_data['unit_cost']
        if unit_cost <= 0:
            raise forms.ValidationError("Unit cost must be a positive value.")
        return unit_cost

```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs) and an HTMX partial view for the DataTables content.

**Instructions:**

Views are kept thin, delegating business logic (like total cost calculation) to the model. HTMX headers are handled to trigger UI updates without full page reloads.

**File:** `materialcosting/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import MaterialCosting
from .forms import MaterialCostingForm

class MaterialCostingListView(ListView):
    """
    Displays a list of MaterialCosting records.
    This view renders the main page with a placeholder for the DataTable.
    """
    model = MaterialCosting
    template_name = 'materialcosting/materialcosting_list.html'
    context_object_name = 'material_costings' # Plural name for the list of objects

    # No specific query logic here, it will be handled by the _table partial
    # or DataTables itself (client-side) after loading the table.

class MaterialCostingTablePartialView(ListView):
    """
    Renders the MaterialCosting table content, intended to be loaded via HTMX.
    This replaces the content of the DataTables container.
    """
    model = MaterialCosting
    template_name = 'materialcosting/_materialcosting_table.html'
    context_object_name = 'material_costings'
    
    # We might filter records here if needed, e.g., to only show active ones.
    def get_queryset(self):
        return MaterialCosting.objects.filter(is_active=True)

class MaterialCostingCreateView(CreateView):
    """
    Handles creation of new MaterialCosting records.
    Renders the form in a modal for HTMX requests.
    """
    model = MaterialCosting
    form_class = MaterialCostingForm
    template_name = 'materialcosting/_materialcosting_form.html' # Use partial for modal
    success_url = reverse_lazy('materialcosting_list')

    def form_valid(self, form):
        # Business logic (e.g., total_cost calculation) is handled in model's save method
        response = super().form_valid(form)
        messages.success(self.request, 'Material Costing record added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialCostingList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, re-render the form with errors for HTMX.
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class MaterialCostingUpdateView(UpdateView):
    """
    Handles updating existing MaterialCosting records.
    Renders the form in a modal for HTMX requests.
    """
    model = MaterialCosting
    form_class = MaterialCostingForm
    template_name = 'materialcosting/_materialcosting_form.html' # Use partial for modal
    success_url = reverse_lazy('materialcosting_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Costing record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialCostingList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class MaterialCostingDeleteView(DeleteView):
    """
    Handles deletion of MaterialCosting records (soft delete).
    Renders a confirmation dialog in a modal for HTMX requests.
    """
    model = MaterialCosting
    template_name = 'materialcosting/_materialcosting_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('materialcosting_list')

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to perform a soft delete.
        """
        self.object = self.get_object()
        self.object.soft_delete() # Call the fat model's business logic
        messages.success(self.request, 'Material Costing record deleted successfully (soft delete).')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialCostingList":true, "closeModal":true}'
                }
            )
        return super().delete(request, *args, **kwargs) # This will handle the redirect

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['object'] = self.get_object() # Ensure object is available in template
        return context

```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring DRY principles, HTMX integration, and DataTables for list views.

**Instructions:**

Templates leverage `core/base.html` for overall layout (not included here). Modals are controlled by Alpine.js, triggered by HTMX requests. DataTables is initialized via JavaScript.

**File:** `materialcosting/templates/materialcosting/materialcosting_list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Costing Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialcosting_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Record
        </button>
    </div>

    <!-- Container for DataTable, reloaded via HTMX -->
    <div id="materialcostingTable-container"
         hx-trigger="load, refreshMaterialCostingList from:body"
         hx-get="{% url 'materialcosting_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Material Costing data...</p>
        </div>
    </div>

    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on closeModal remove .is-active from me
            on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 sm:mx-auto">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is primarily used for managing modal state here.
        // The _="on click..." syntax in HTMX attributes leverages Alpine.js's functionality.
    });

    // Listen for HTMX triggers to close the modal
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // If the swap was a 204 (e.g., successful form submission), close the modal
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Re-initialize DataTables after HTMX loads new table content
    document.body.addEventListener('htmx:afterSettle', function(event) {
        if (event.detail.target.id === 'materialcostingTable-container') {
            $('#materialcostingTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true
            });
        }
    });
</script>
{% endblock %}
```

**File:** `materialcosting/templates/materialcosting/_materialcosting_table.html`

```html
<table id="materialcostingTable" class="min-w-full bg-white border-collapse border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-100">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Material Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Unit Cost</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Cost</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date Recorded</th>
            <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in material_costings %}
        <tr class="hover:bg-gray-50 {% if not obj.is_active %}bg-red-50 bg-opacity-50{% endif %}">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.material_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">${{ obj.unit_cost|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.quantity }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.get_display_total_cost }}</td> {# Uses model method #}
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.date_recorded|date:"M d, Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center text-sm">
                <span class="px-2 py-1 rounded-full text-xs font-medium {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {{ obj.is_active|yesno:"Active,Inactive" }}
                </span>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-200 ease-in-out"
                    hx-get="{% url 'materialcosting_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-200 ease-in-out"
                    hx-get="{% url 'materialcosting_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- DataTables initialization script. Re-initialized on HTMX swap. -->
<script>
    // This script block will run each time the partial is loaded by HTMX.
    // The main list.html has a listener to initialize DataTables after HTMX swap.
    // No direct DataTables init here to avoid conflicts if the partial is loaded multiple times.
</script>
```

**File:** `materialcosting/templates/materialcosting/_materialcosting_form.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Material Costing Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4 {% if field.field.widget.input_type == 'checkbox' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-red-600 text-sm list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                Save Record
            </button>
        </div>
    </form>
</div>
```

**File:** `materialcosting/templates/materialcosting/_materialcosting_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Material Costing record for **"{{ object.material_name }}"** recorded on **{{ object.date_recorded|date:"M d, Y" }}**?</p>
    <p class="text-red-600 font-medium">This action will mark the record as inactive (soft delete).</p>

    <form hx-post="{% url 'materialcosting_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                Yes, Delete It
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views, including HTMX partials.

**Instructions:**

These URLs map to the defined views, using `materialcosting_list`, `materialcosting_add`, etc., as consistent names for `reverse` lookups.

**File:** `materialcosting/urls.py`

```python
from django.urls import path
from .views import (
    MaterialCostingListView,
    MaterialCostingTablePartialView,
    MaterialCostingCreateView,
    MaterialCostingUpdateView,
    MaterialCostingDeleteView
)

urlpatterns = [
    # Main dashboard view
    path('dashboard/', MaterialCostingListView.as_view(), name='materialcosting_list'),
    
    # HTMX endpoint for the table content
    path('dashboard/table/', MaterialCostingTablePartialView.as_view(), name='materialcosting_table'),
    
    # HTMX endpoint for add form
    path('dashboard/add/', MaterialCostingCreateView.as_view(), name='materialcosting_add'),
    
    # HTMX endpoint for edit form
    path('dashboard/edit/<int:pk>/', MaterialCostingUpdateView.as_view(), name='materialcosting_edit'),
    
    # HTMX endpoint for delete confirmation
    path('dashboard/delete/<int:pk>/', MaterialCostingDeleteView.as_view(), name='materialcosting_delete'),
]

```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the model and integration tests for the views.

**Instructions:**

Tests cover model functionality (e.g., `save` method, `soft_delete`), form validation, and view behavior, including HTMX interactions.

**File:** `materialcosting/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from datetime import date
from .models import MaterialCosting
from .forms import MaterialCostingForm

class MaterialCostingModelTest(TestCase):
    """
    Unit tests for the MaterialCosting model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data that will be used by all test methods
        cls.material_cost_record = MaterialCosting.objects.create(
            material_name='Test Material',
            unit_cost=Decimal('10.00'),
            quantity=50,
            date_recorded=date(2023, 1, 1),
            is_active=True # Initially active
        )
        # Ensure total_cost is calculated on save
        cls.material_cost_record.refresh_from_db()

    def test_material_costing_creation(self):
        """
        Test that a MaterialCosting record is created correctly.
        """
        obj = MaterialCosting.objects.get(material_cost_id=self.material_cost_record.material_cost_id)
        self.assertEqual(obj.material_name, 'Test Material')
        self.assertEqual(obj.unit_cost, Decimal('10.00'))
        self.assertEqual(obj.quantity, 50)
        self.assertEqual(obj.total_cost, Decimal('500.00')) # Check calculated total
        self.assertEqual(obj.date_recorded, date(2023, 1, 1))
        self.assertTrue(obj.is_active)

    def test_str_method(self):
        """
        Test the __str__ method of the model.
        """
        expected_str = f"Test Material (ID: {self.material_cost_record.material_cost_id})"
        self.assertEqual(str(self.material_cost_record), expected_str)

    def test_total_cost_calculation_on_save(self):
        """
        Test that total_cost is automatically calculated on save.
        """
        new_record = MaterialCosting.objects.create(
            material_name='New Item',
            unit_cost=Decimal('25.50'),
            quantity=10,
            date_recorded=date(2023, 10, 26)
        )
        self.assertEqual(new_record.total_cost, Decimal('255.00'))

    def test_get_display_total_cost_method(self):
        """
        Test the get_display_total_cost formatting method.
        """
        self.assertEqual(self.material_cost_record.get_display_total_cost(), '$500.00')

    def test_soft_delete_method(self):
        """
        Test the soft_delete business logic.
        """
        self.material_cost_record.soft_delete()
        self.material_cost_record.refresh_from_db()
        self.assertFalse(self.material_cost_record.is_active)

    def test_activate_method(self):
        """
        Test the activate business logic.
        """
        self.material_cost_record.is_active = False
        self.material_cost_record.save() # Set to inactive first
        self.material_cost_record.activate()
        self.material_cost_record.refresh_from_db()
        self.assertTrue(self.material_cost_record.is_active)

class MaterialCostingFormTest(TestCase):
    """
    Unit tests for the MaterialCostingForm.
    """
    def test_form_valid_data(self):
        form = MaterialCostingForm(data={
            'material_name': 'Valid Name',
            'unit_cost': '10.00',
            'quantity': '5',
            'date_recorded': '2023-11-01',
            'is_active': True
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_quantity(self):
        form = MaterialCostingForm(data={
            'material_name': 'Valid Name',
            'unit_cost': '10.00',
            'quantity': '0', # Invalid quantity
            'date_recorded': '2023-11-01',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)
        self.assertIn('Quantity must be a positive number.', form.errors['quantity'])

    def test_form_invalid_unit_cost(self):
        form = MaterialCostingForm(data={
            'material_name': 'Valid Name',
            'unit_cost': '0.00', # Invalid unit cost
            'quantity': '5',
            'date_recorded': '2023-11-01',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('unit_cost', form.errors)
        self.assertIn('Unit cost must be a positive value.', form.errors['unit_cost'])

class MaterialCostingViewsTest(TestCase):
    """
    Integration tests for MaterialCosting views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single active record for testing list and detail views
        cls.active_record = MaterialCosting.objects.create(
            material_name='Active Material',
            unit_cost=Decimal('15.00'),
            quantity=10,
            date_recorded=date(2023, 5, 1),
            is_active=True
        )
        # Create an inactive record to test filtering (if applicable)
        cls.inactive_record = MaterialCosting.objects.create(
            material_name='Inactive Material',
            unit_cost=Decimal('5.00'),
            quantity=20,
            date_recorded=date(2023, 6, 1),
            is_active=False
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """
        Test that the list view renders correctly.
        """
        response = self.client.get(reverse('materialcosting_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materialcosting_list.html')
        # We don't check context_object directly for list view, as table is loaded via HTMX

    def test_table_partial_view_get(self):
        """
        Test that the HTMX table partial view renders correctly and includes active objects.
        """
        response = self.client.get(reverse('materialcosting_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/_materialcosting_table.html')
        self.assertContains(response, self.active_record.material_name)
        self.assertNotContains(response, self.inactive_record.material_name) # Assuming partial filters for active

    def test_create_view_get(self):
        """
        Test that the create form view renders correctly for GET requests (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialcosting_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/_materialcosting_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        """
        Test successful creation of a new record via POST request (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'material_name': 'New Test Material',
            'unit_cost': '50.00',
            'quantity': '10',
            'date_recorded': '2023-11-15',
            'is_active': 'on'
        }
        response = self.client.post(reverse('materialcosting_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(MaterialCosting.objects.filter(material_name='New Test Material', is_active=True).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialCostingList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """
        Test invalid form submission for creation via POST request (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'material_name': 'Invalid Data',
            'unit_cost': '0.00', # Invalid
            'quantity': '5',
            'date_recorded': '2023-11-15',
            'is_active': 'on'
        }
        response = self.client.post(reverse('materialcosting_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'materialcosting/_materialcosting_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertFalse(MaterialCosting.objects.filter(material_name='Invalid Data').exists())

    def test_update_view_get(self):
        """
        Test that the update form view renders correctly for GET requests (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialcosting_edit', args=[self.active_record.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/_materialcosting_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.active_record)

    def test_update_view_post_success(self):
        """
        Test successful update of an existing record via POST request (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_name = 'Updated Material Name'
        data = {
            'material_name': updated_name,
            'unit_cost': '16.00',
            'quantity': '12',
            'date_recorded': '2023-05-01',
            'is_active': 'on'
        }
        response = self.client.post(reverse('materialcosting_edit', args=[self.active_record.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.active_record.refresh_from_db()
        self.assertEqual(self.active_record.material_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialCostingList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        """
        Test invalid form submission for update via POST request (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        original_name = self.active_record.material_name
        data = {
            'material_name': original_name,
            'unit_cost': '-5.00', # Invalid
            'quantity': '10',
            'date_recorded': '2023-05-01',
            'is_active': 'on'
        }
        response = self.client.post(reverse('materialcosting_edit', args=[self.active_record.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/_materialcosting_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.active_record.refresh_from_db()
        self.assertNotEqual(self.active_record.unit_cost, Decimal('-5.00')) # Ensure not updated

    def test_delete_view_get(self):
        """
        Test that the delete confirmation view renders correctly for GET requests (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialcosting_delete', args=[self.active_record.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/_materialcosting_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.active_record)

    def test_delete_view_post_success(self):
        """
        Test successful soft deletion of a record via POST request (HTMX).
        """
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcosting_delete', args=[self.active_record.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.active_record.refresh_from_db()
        self.assertFalse(self.active_record.is_active) # Verify soft delete
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialCostingList', response.headers['HX-Trigger'])

    def test_delete_view_non_existent_record(self):
        """
        Test deletion of a non-existent record.
        """
        response = self.client.post(reverse('materialcosting_delete', args=[9999]), follow=True)
        self.assertEqual(response.status_code, 404) # Not Found if record doesn't exist

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided Django code snippets already fully integrate HTMX and Alpine.js as per the requirements:

*   **HTMX for Dynamic Interactions:**
    *   `materialcosting_list.html` uses `hx-get` to load the table content (`materialcosting_table`) on page load and upon `refreshMaterialCostingList` trigger.
    *   Buttons for "Add New Record", "Edit", and "Delete" use `hx-get` to fetch their respective forms/confirmations into the `#modalContent` div.
    *   Forms within `_materialcosting_form.html` and `_materialcosting_confirm_delete.html` use `hx-post` to submit data back to the server without full page reloads.
    *   Upon successful form submission or deletion, the views return `HTTPResponse(status=204)` with `HX-Trigger` headers (`refreshMaterialCostingList`, `closeModal`) to update the main list and close the modal.
*   **Alpine.js for UI State Management:**
    *   The `on click add .is-active to #modal` syntax and `on closeModal remove .is-active from me` directly use Alpine.js's declarative `_` attribute to manage the modal's visibility (adding/removing the `is-active` class).
*   **DataTables for List Views:**
    *   `_materialcosting_table.html` defines the `<table>` element with `id="materialcostingTable"`.
    *   The `materialcosting_list.html` includes a JavaScript block that listens for HTMX `afterSettle` events on the table container, and re-initializes `DataTables` whenever new table content is loaded. This ensures sorting, searching, and pagination work correctly with dynamically updated data.
*   **No Additional JavaScript:** The entire interaction pattern relies exclusively on HTMX, Alpine.js, and jQuery for DataTables, fulfilling the "no additional JavaScript" requirement beyond these libraries.
*   **DRY Template Inheritance:** All templates (`materialcosting_list.html`) extend `core/base.html`, ensuring CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables are managed centrally.

---

## Final Notes

This comprehensive plan provides a clear, automated path to modernize your `MaterialCosting` Dashboard. By focusing on Django's "fat model, thin view" architecture, HTMX for dynamic content, and DataTables for data presentation, you will achieve a scalable, maintainable, and user-friendly application.

Remember to:
*   Ensure your Django project settings are configured to include the `materialcosting` app and connect to your existing database.
*   Run database migrations (even if `managed=False`, Django still needs to know about the models for ORM).
*   Thoroughly review and adapt the inferred fields and business logic in the `MaterialCosting` model and form to match the actual intricacies of your legacy ASP.NET application's data structures and rules.
*   Expand the test coverage as more complex business rules are identified and implemented.
*   The `core/base.html` (not provided in this output) should include all necessary CDN links for Tailwind CSS, Font Awesome, HTMX, Alpine.js, jQuery, and DataTables.