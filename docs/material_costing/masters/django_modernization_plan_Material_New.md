## ASP.NET to Django Conversion Script: Material Costing Module

This document outlines a comprehensive modernization plan to transition the provided ASP.NET `Material_New.aspx` and its `Material_New.aspx.cs` code-behind to a modern Django-based solution. The focus is on leveraging Django 5.0+ best practices, HTMX for dynamic front-end interactions, Alpine.js for lightweight UI state, and DataTables for advanced data presentation. This plan prioritizes AI-assisted automation, minimizing manual coding, and ensuring scalability and maintainability.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the `SqlDataSource` definitions and SQL commands in the ASP.NET code, we identify three primary tables:

*   **`tblDG_Material`**: This table holds information about various materials.
    *   Columns: `Id` (Primary Key), `Material` (Material Name), `Unit` (Foreign Key to `Unit_Master`).
*   **`Unit_Master`**: This table stores Unit of Measurement (UOM) details.
    *   Columns: `Id` (Primary Key), `Symbol` (UOM Symbol, e.g., 'KG', 'PCS').
*   **`tblMLC_LiveCost`**: This is the central table for Material Live Costs.
    *   Columns: `Id` (Primary Key), `SySDate` (System Date of entry), `SysTime` (System Time of entry), `CompId` (Company ID), `FinYearId` (Financial Year ID), `SessionId` (User/Session ID), `Material` (Foreign Key to `tblDG_Material`), `EffDate` (Effective Date of Cost), `LiveCost` (The actual cost).

### Step 2: Identify Backend Functionality

The ASP.NET application provides the following functionalities:

*   **Create (Add New Material Cost)**:
    *   **Input**: User selects a `Material` from a dropdown, enters `Cost` and `Effective Date`.
    *   **Logic**:
        *   Retrieves `CompId`, `FinYearId`, `SessionId` from user's session context.
        *   Captures current system `SySDate` and `SysTime`.
        *   Validates `Cost` (numeric, format `^\d{1,15}(\.\d{0,3})?$`) and `Effective Date` (date format `dd-MM-yyyy`).
        *   Inserts a new record into `tblMLC_LiveCost` with the collected data.
        *   Refreshes the page to show the updated list.
*   **Read (Display Material Costs)**:
    *   **Material Dropdown**: Populated from `tblDG_Material` (Id, Material).
    *   **UOM Display**: When a material is selected in the dropdown, its corresponding `Unit_Master.Symbol` is fetched and displayed. This is a dynamic update.
    *   **Live Cost List**: Displays all records from `tblMLC_LiveCost` joined with `tblDG_Material` to show `Material` name, `Effective Date`, and `Live Cost`. The `Effective Date` has specific formatting applied during retrieval.
*   **Update**: Although not explicitly shown in the `GridView` as an editable column or separate update button, a modern Django application would include this. We will implement it for full CRUD.
*   **Delete**: Not explicitly shown, but will be implemented for full CRUD.
*   **Client-Side Validation**: Basic numeric and date format validation on `TxtCost` and `TxtDate`.

### Step 3: Infer UI Components

The ASP.NET UI components translate to Django as follows:

*   **Material Selection**: `asp:DropDownList` becomes a Django `forms.ModelChoiceField` rendered as a `<select>`.
*   **UOM Display**: `asp:Label` becomes a `<span>` updated dynamically via HTMX.
*   **Cost Input**: `asp:TextBox` becomes a `forms.DecimalField` rendered as an `<input type="text">`.
*   **Effective Date Input**: `asp:TextBox` with `CalendarExtender` becomes a `forms.DateField` rendered as an `<input type="date">` (or text with a modern JS date picker library, though Alpine.js could handle simple date pickers or we could rely on browser native). Given HTMX/Alpine.js, a simple text input with an Alpine.js-powered date picker or a dedicated library would be best.
*   **Add Button**: `asp:Button` becomes a `<button>` triggering an HTMX request for a modal form.
*   **Material Cost List**: `asp:GridView` becomes a `<table>` enhanced with DataTables. HTMX will be used to load this table partially.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `material_costing`.

#### 4.1 Models (`material_costing/models.py`)

We'll define models for `UnitMaster`, `Material`, and `LiveCost`, reflecting the database schema. The `LiveCost` model will encapsulate the logic for handling session/system details on save.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime
import re

class UnitMaster(models.Model):
    """
    Maps to Unit_Master table for Unit of Measurement details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class Material(models.Model):
    """
    Maps to tblDG_Material table for Material details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    material = models.CharField(db_column='Material', max_length=255)
    unit = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='Unit', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Material'
        verbose_name = 'Material'
        verbose_name_plural = 'Materials'

    def __str__(self):
        return self.material

    def get_unit_symbol(self):
        """Returns the symbol of the associated unit."""
        return self.unit.symbol if self.unit else ''

class LiveCost(models.Model):
    """
    Maps to tblMLC_LiveCost table for Material Live Costs.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK
    sysdate = models.CharField(db_column='SySDate', max_length=10) # Stored as 'dd-MM-yyyy'
    systime = models.CharField(db_column='SysTime', max_length=10) # Stored as 'HH:mm:ss'
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    sessionid = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    material = models.ForeignKey(Material, models.DO_NOTHING, db_column='Material')
    effdate = models.CharField(db_column='EffDate', max_length=10) # Stored as 'dd-MM-yyyy'
    livecost = models.DecimalField(db_column='LiveCost', max_digits=18, decimal_places=3) # Adjust max_digits as needed

    class Meta:
        managed = False
        db_table = 'tblMLC_LiveCost'
        verbose_name = 'Live Cost'
        verbose_name_plural = 'Live Costs'

    def __str__(self):
        return f"Live Cost for {self.material.material} on {self.effdate}: {self.livecost}"

    @property
    def formatted_eff_date(self):
        """
        Formats EffDate to display as 'dd-MM-yyyy' if it's stored in a different way
        or ensures consistency. The ASP.NET code had complex SQL for this.
        Assuming 'dd-MM-yyyy' is the consistent format.
        """
        return self.effdate

    def save(self, *args, **kwargs):
        """
        Overrides save to populate system date/time and session info.
        This assumes session_user, session_compid, session_finyearid are
        available in the request context or passed during model creation.
        For automation, these would be injected by a middleware or during
        the view's form_valid method.
        """
        if not self.pk: # Only on creation
            now = timezone.now()
            self.sysdate = now.strftime('%d-%m-%Y')
            self.systime = now.strftime('%H:%M:%S')
            # These values would typically come from the request.user or session
            # For demonstration, we'll use placeholders or derive from request in view.
            # In a real system, you'd map these from Django's auth/session.
            if hasattr(self, '_session_user_id'): # Injected by view
                self.sessionid = self._session_user_id
            if hasattr(self, '_session_comp_id'):
                self.compid = self._session_comp_id
            if hasattr(self, '_session_fin_year_id'):
                self.finyearid = self._session_fin_year_id
        super().save(*args, **kwargs)

    @staticmethod
    def parse_asp_date_format(date_str):
        """
        Parses a date string in 'dd-MM-yyyy' format.
        """
        try:
            return datetime.strptime(date_str, '%d-%m-%Y').date()
        except ValueError:
            return None
            
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('livecost_detail', args=[str(self.id)])
```

#### 4.2 Forms (`material_costing/forms.py`)

A `ModelForm` for `LiveCost` will handle input validation and mapping. We'll add custom clean methods for the specific ASP.NET validations.

```python
from django import forms
from .models import LiveCost, Material
import re
from datetime import datetime

class LiveCostForm(forms.ModelForm):
    material_uom_symbol = forms.CharField(
        label="UOM",
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 cursor-not-allowed sm:text-sm', 'readonly': 'readonly'}),
        help_text="Unit of Measurement for the selected material."
    )

    class Meta:
        model = LiveCost
        fields = ['material', 'livecost', 'effdate']
        widgets = {
            'material': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'hx-post': '/material-costing/api/material-uom/', # HTMX endpoint for UOM
                'hx-trigger': 'change',
                'hx-target': '#id_material_uom_symbol', # Target UOM field
                'hx-swap': 'value', # Swap only the value
                'name': 'material', # Ensure name attribute is present for POST
            }),
            'livecost': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter cost (e.g., 123.456)',
                'x-model': 'livecostInput', # For Alpine.js
                '@input': 'formatLiveCost(livecostInput)', # For Alpine.js client-side formatting
            }),
            'effdate': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'DD-MM-YYYY',
                'x-data': '', # Initialize Alpine.js
                'x-mask': '99-99-9999', # For input masking
                'x-model': 'effdateInput', # For Alpine.js
            }),
        }
        labels = {
            'material': 'Material',
            'livecost': 'Cost',
            'effdate': 'Effective Date',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial UOM symbol if editing existing instance
        if self.instance and self.instance.pk and self.instance.material:
            self.fields['material_uom_symbol'].initial = self.instance.material.get_unit_symbol()
        # Set choices for material dropdown
        self.fields['material'].queryset = Material.objects.all().order_by('material')
        self.fields['material'].empty_label = "Select" # Matches ASP.NET ListItem Value="0"

    def clean_livecost(self):
        livecost = self.cleaned_data['livecost']
        # Equivalent to ASP.NET RegTxtCost validation: ^\d{1,15}(\.\d{0,3})?$
        if not re.fullmatch(r'^\d{1,15}(\.\d{0,3})?$', str(livecost)):
            raise forms.ValidationError("Cost must be a number with up to 15 digits before and 3 after the decimal point.")
        return livecost

    def clean_effdate(self):
        effdate_str = self.cleaned_data['effdate']
        # Equivalent to ASP.NET RegTxtDate validation: ^([1-9]|0[1-9]|[12][0-9]|3[01])[- /.]([1-9]|0[1-9]|1[012])[- /.][0-9]{4}$
        try:
            # First, check the regex pattern for "dd-MM-yyyy" or "dd/MM/yyyy"
            if not re.fullmatch(r'^(0[1-9]|[12][0-9]|3[01])[- /.](0[1-9]|1[012])[- /.][0-9]{4}$', effdate_str):
                raise forms.ValidationError("Date must be in DD-MM-YYYY format.")
            # Then attempt to parse to ensure it's a valid date
            datetime.strptime(effdate_str, '%d-%m-%Y').date()
        except ValueError:
            raise forms.ValidationError("Please enter a valid date in DD-MM-YYYY format.")
        return effdate_str # Stored as string, as per existing DB design
```

#### 4.3 Views (`material_costing/views.py`)

We'll implement CBVs for CRUD operations and a specific HTMX endpoint for the UOM lookup.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from .models import LiveCost, Material
from .forms import LiveCostForm
from django.views import View

class LiveCostListView(ListView):
    """
    Displays the main page with the form and the table container.
    """
    model = LiveCost
    template_name = 'material_costing/livecost/list.html'
    context_object_name = 'livecosts' # Name for the list of objects
    # No direct queryset here, as the table content is loaded via HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = LiveCostForm() # Pass an empty form for the 'Add New' modal
        return context

class LiveCostTablePartialView(ListView):
    """
    Renders only the DataTables portion of the LiveCost list.
    Designed to be fetched via HTMX.
    """
    model = LiveCost
    template_name = 'material_costing/livecost/_livecost_table.html'
    context_object_name = 'livecosts'

    def get_queryset(self):
        # The complex SQL query from ASP.NET is simplified here.
        # Ensure your models correctly map to allow direct querying.
        # Ordering by Id Desc as in ASP.NET
        return LiveCost.objects.select_related('material').order_by('-id')

class LiveCostCreateView(CreateView):
    """
    Handles creation of new LiveCost records.
    Designed to be loaded via HTMX into a modal.
    """
    model = LiveCost
    form_class = LiveCostForm
    template_name = 'material_costing/livecost/_livecost_form.html' # Partial template
    success_url = reverse_lazy('livecost_list') # Not strictly used with HTMX swap="none"

    def form_valid(self, form):
        # Inject session-related data into the model instance before saving
        # These would typically come from request.user, session, or a custom middleware
        # For demonstration, we'll use placeholder values or extract from request.user.
        # In a real system, map them to your auth/session setup.
        # Assuming request.user is authenticated and has profile attributes
        if self.request.user.is_authenticated:
            form.instance._session_user_id = self.request.user.username # Or request.user.id
            # Placeholder for CompId and FinYearId - replace with actual logic
            form.instance._session_comp_id = 1 # Example Company ID
            form.instance._session_fin_year_id = 2024 # Example Financial Year ID

        response = super().form_valid(form)
        messages.success(self.request, 'Material Live Cost added successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX specific response: no content, but trigger client-side event to refresh list
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshLiveCostList'
                }
            )
        return response # Fallback for non-HTMX requests

class LiveCostUpdateView(UpdateView):
    """
    Handles updating existing LiveCost records.
    Designed to be loaded via HTMX into a modal.
    """
    model = LiveCost
    form_class = LiveCostForm
    template_name = 'material_costing/livecost/_livecost_form.html' # Partial template
    success_url = reverse_lazy('livecost_list') # Not strictly used with HTMX swap="none"

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Live Cost updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshLiveCostList'
                }
            )
        return response

class LiveCostDeleteView(DeleteView):
    """
    Handles deleting LiveCost records.
    Designed to be loaded via HTMX into a modal for confirmation.
    """
    model = LiveCost
    template_name = 'material_costing/livecost/_confirm_delete.html' # Partial template
    success_url = reverse_lazy('livecost_list') # Not strictly used with HTMX swap="none"

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Live Cost deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshLiveCostList'
                }
            )
        return response

class MaterialUOMAPIView(View):
    """
    API endpoint to fetch the UOM symbol for a given Material ID.
    Used by HTMX on the material dropdown change.
    """
    def post(self, request, *args, **kwargs):
        material_id = request.POST.get('material') # 'material' is the name of the select input
        if material_id:
            try:
                material = Material.objects.select_related('unit').get(id=material_id)
                return HttpResponse(material.get_unit_symbol())
            except Material.DoesNotExist:
                pass
        return HttpResponse('') # Return empty string if material not found or no ID
```

#### 4.4 Templates (`material_costing/templates/material_costing/livecost/`)

We'll define the main list page and partials for the table, form, and delete confirmation.

**`list.html`**:
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Live Costs</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'livecost_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Live Cost
        </button>
    </div>
    
    <!-- Container for the DataTables partial, refreshed by HTMX -->
    <div id="livecostTable-container"
         hx-trigger="load, refreshLiveCostList from:body"
         hx-get="{% url 'livecost_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="text-center p-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading material costs...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad add .is-active to #modal">
            <!-- Content loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Ensure DataTables and jQuery are loaded globally via base.html or here if only for this page -->
<!-- For example (if not in base.html): -->
<!-- <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script> -->
<!-- <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script> -->
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css"> -->

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('formLogic', () => ({
            livecostInput: '',
            effdateInput: '',
            
            // Client-side validation/formatting for livecost (similar to ASP.NET onkeyup)
            formatLiveCost(value) {
                // Remove non-numeric characters except for one decimal point
                let cleanedValue = value.replace(/[^\d.]/g, '');
                // Ensure only one decimal point
                const parts = cleanedValue.split('.');
                if (parts.length > 2) {
                    cleanedValue = parts[0] + '.' + parts.slice(1).join('');
                }
                // Limit to 15 digits before decimal and 3 after
                if (cleanedValue.includes('.')) {
                    const [integerPart, decimalPart] = cleanedValue.split('.');
                    cleanedValue = integerPart.substring(0, 15) + (decimalPart ? '.' + decimalPart.substring(0, 3) : '');
                } else {
                    cleanedValue = cleanedValue.substring(0, 15);
                }
                this.livecostInput = cleanedValue;
            },
            
            // Handle modal close on successful HTMX form submission
            // This listener must be on a parent element, or globally.
            // HX-Trigger is 'refreshLiveCostList' which the container listens to,
            // so we just need to close the modal here.
            setupModalClose() {
                htmx.on('htmx:afterRequest', (evt) => {
                    if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
                        document.getElementById('modal').classList.remove('is-active');
                    }
                });
            }
        }));
    });
    // Initialize modal close listener on body or a root element that's always present
    document.body.addEventListener('htmx:afterRequest', (evt) => {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**`_livecost_table.html`**:
```html
<table id="livecostTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Eff Date</th>
            <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Live Cost</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in livecosts %}
        <tr>
            <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.material.material }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-center">{{ obj.effdate }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ obj.livecost }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg mr-2 transition duration-200 ease-in-out"
                    hx-get="{% url 'livecost_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg transition duration-200 ease-in-out"
                    hx-get="{% url 'livecost_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-6 text-center text-lg text-gray-500">
                No data to display !
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // This script runs every time _livecost_table.html is loaded via HTMX
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#livecostTable')) {
            $('#livecostTable').DataTable().destroy();
        }
        $('#livecostTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] }, // SN and Actions columns not orderable
                { "searchable": false, "targets": [0, 4] } // SN and Actions columns not searchable
            ]
        });
    });
</script>
```

**`_livecost_form.html`**:
```html
<div class="p-6" x-data="formLogic">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Material Live Cost
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        <!-- Material Dropdown -->
        <div>
            <label for="{{ form.material.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.material.label }} <span class="text-red-500">*</span>
            </label>
            {{ form.material }}
            {% if form.material.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.material.errors }}</p>
            {% endif %}
        </div>

        <!-- UOM Label (dynamically updated by HTMX/Alpine.js) -->
        <div>
            <label for="{{ form.material_uom_symbol.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.material_uom_symbol.label }}
            </label>
            {{ form.material_uom_symbol }}
        </div>

        <!-- Cost Input -->
        <div>
            <label for="{{ form.livecost.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.livecost.label }} <span class="text-red-500">*</span>
            </label>
            <input type="text" name="{{ form.livecost.name }}" id="{{ form.livecost.id_for_label }}" 
                   class="{{ form.livecost.css_classes }}" 
                   placeholder="{{ form.livecost.field.widget.attrs.placeholder }}"
                   {% if form.instance.pk %}value="{{ form.livecost.value|default_if_none:'' }}"{% endif %}
                   x-model="livecostInput" @input="formatLiveCost(livecostInput)">
            {% if form.livecost.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.livecost.errors }}</p>
            {% endif %}
        </div>

        <!-- Effective Date Input -->
        <div>
            <label for="{{ form.effdate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.effdate.label }} <span class="text-red-500">*</span>
            </label>
            <input type="text" name="{{ form.effdate.name }}" id="{{ form.effdate.id_for_label }}"
                   class="{{ form.effdate.css_classes }}" 
                   placeholder="{{ form.effdate.field.widget.attrs.placeholder }}"
                   {% if form.instance.pk %}value="{{ form.effdate.value|default_if_none:'' }}"{% endif %}
                   x-data x-mask="99-99-9999" x-model="effdateInput">
            {% if form.effdate.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.effdate.errors }}</p>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

**`_confirm_delete.html`**:
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Live Cost for <strong>{{ livecost.material.material }}</strong> with Effective Date <strong>{{ livecost.effdate }}</strong> and Cost <strong>{{ livecost.livecost }}</strong>?</p>
    
    <form hx-post="{% url 'livecost_delete' livecost.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`material_costing/urls.py`)

```python
from django.urls import path
from .views import (
    LiveCostListView, LiveCostTablePartialView, 
    LiveCostCreateView, LiveCostUpdateView, LiveCostDeleteView,
    MaterialUOMAPIView
)

urlpatterns = [
    path('livecost/', LiveCostListView.as_view(), name='livecost_list'),
    path('livecost/table/', LiveCostTablePartialView.as_view(), name='livecost_table'),
    path('livecost/add/', LiveCostCreateView.as_view(), name='livecost_add'),
    path('livecost/edit/<int:pk>/', LiveCostUpdateView.as_view(), name='livecost_edit'),
    path('livecost/delete/<int:pk>/', LiveCostDeleteView.as_view(), name='livecost_delete'),
    path('api/material-uom/', MaterialUOMAPIView.as_view(), name='api_material_uom'),
]
```

#### 4.6 Tests (`material_costing/tests.py`)

Comprehensive tests for models, forms, and views to ensure functionality and adhere to test coverage requirements.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from datetime import datetime
import json
from .models import UnitMaster, Material, LiveCost
from .forms import LiveCostForm

class MaterialCostingModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_pcs = UnitMaster.objects.create(id=2, symbol='PCS')

        cls.material_cotton = Material.objects.create(id=101, material='Cotton', unit=cls.unit_kg)
        cls.material_button = Material.objects.create(id=102, material='Button', unit=cls.unit_pcs)

        cls.livecost1 = LiveCost.objects.create(
            id=1,
            sysdate='01-01-2023',
            systime='10:00:00',
            compid=1,
            finyearid=2023,
            sessionid='testuser',
            material=cls.material_cotton,
            effdate='15-01-2023',
            livecost=10.500
        )
        cls.livecost2 = LiveCost.objects.create(
            id=2,
            sysdate='02-01-2023',
            systime='11:00:00',
            compid=1,
            finyearid=2023,
            sessionid='testuser',
            material=cls.material_button,
            effdate='20-01-2023',
            livecost=0.150
        )

    def test_unit_master_creation(self):
        self.assertEqual(self.unit_kg.symbol, 'KG')
        self.assertEqual(str(self.unit_kg), 'KG')

    def test_material_creation(self):
        self.assertEqual(self.material_cotton.material, 'Cotton')
        self.assertEqual(self.material_cotton.unit, self.unit_kg)
        self.assertEqual(str(self.material_cotton), 'Cotton')
        self.assertEqual(self.material_cotton.get_unit_symbol(), 'KG')

    def test_material_without_unit(self):
        material_no_unit = Material.objects.create(id=103, material='Thread', unit=None)
        self.assertEqual(material_no_unit.get_unit_symbol(), '')

    def test_live_cost_creation(self):
        self.assertEqual(self.livecost1.material, self.material_cotton)
        self.assertEqual(self.livecost1.effdate, '15-01-2023')
        self.assertEqual(self.livecost1.livecost, 10.500)
        self.assertEqual(self.livecost1.formatted_eff_date, '15-01-2023')

    def test_live_cost_save_method_new_instance(self):
        # We need to simulate the view injecting context for sysdate/time etc.
        new_material = Material.objects.create(id=104, material='Dye', unit=self.unit_kg)
        new_livecost = LiveCost(
            material=new_material,
            effdate='01-02-2023',
            livecost=5.000
        )
        new_livecost._session_user_id = 'newuser'
        new_livecost._session_comp_id = 2
        new_livecost._session_fin_year_id = 2024
        new_livecost.save()

        self.assertIsNotNone(new_livecost.sysdate)
        self.assertIsNotNone(new_livecost.systime)
        self.assertEqual(new_livecost.sessionid, 'newuser')
        self.assertEqual(new_livecost.compid, 2)
        self.assertEqual(new_livecost.finyearid, 2024)
        
    def test_parse_asp_date_format(self):
        self.assertEqual(LiveCost.parse_asp_date_format('25-12-2023'), datetime(2023, 12, 25).date())
        self.assertIsNone(LiveCost.parse_asp_date_format('2023/12/25')) # Incorrect format
        self.assertIsNone(LiveCost.parse_asp_date_format('invalid-date'))

class MaterialCostingFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.material_cotton = Material.objects.create(id=101, material='Cotton', unit=cls.unit_kg)

    def test_live_cost_form_valid_data(self):
        data = {
            'material': self.material_cotton.id,
            'livecost': '123.456',
            'effdate': '01-01-2024'
        }
        form = LiveCostForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['livecost'], 123.456)
        self.assertEqual(form.cleaned_data['effdate'], '01-01-2024')

    def test_live_cost_form_invalid_cost_format(self):
        data = {
            'material': self.material_cotton.id,
            'livecost': 'abc.123',
            'effdate': '01-01-2024'
        }
        form = LiveCostForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('livecost', form.errors)
        self.assertIn('Cost must be a number', form.errors['livecost'][0])

    def test_live_cost_form_cost_max_digits(self):
        data = {
            'material': self.material_cotton.id,
            'livecost': '1234567890123456.123', # 16 digits before decimal
            'effdate': '01-01-2024'
        }
        form = LiveCostForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('livecost', form.errors)
        self.assertIn('Cost must be a number', form.errors['livecost'][0])

    def test_live_cost_form_invalid_date_format(self):
        data = {
            'material': self.material_cotton.id,
            'livecost': '10.000',
            'effdate': '2024-01-01' # Incorrect format
        }
        form = LiveCostForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('effdate', form.errors)
        self.assertIn('valid date in DD-MM-YYYY format', form.errors['effdate'][0])

    def test_live_cost_form_date_invalid_day(self):
        data = {
            'material': self.material_cotton.id,
            'livecost': '10.000',
            'effdate': '32-01-2024' # Invalid day
        }
        form = LiveCostForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('effdate', form.errors)
        self.assertIn('valid date in DD-MM-YYYY format', form.errors['effdate'][0])

    def test_live_cost_form_uom_initial_value(self):
        livecost_instance = LiveCost.objects.create(
            material=self.material_cotton, livecost=5.0, effdate='01-01-2024'
        )
        form = LiveCostForm(instance=livecost_instance)
        self.assertEqual(form.fields['material_uom_symbol'].initial, 'KG')


class MaterialCostingViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.material_cotton = Material.objects.create(id=101, material='Cotton', unit=cls.unit_kg)
        cls.material_button = Material.objects.create(id=102, material='Button', unit=UnitMaster.objects.create(id=2, symbol='PCS'))

        cls.livecost1 = LiveCost.objects.create(
            id=1,
            sysdate='01-01-2023', systime='10:00:00', compid=1, finyearid=2023, sessionid='testuser',
            material=cls.material_cotton, effdate='15-01-2023', livecost=10.500
        )
        cls.livecost2 = LiveCost.objects.create(
            id=2,
            sysdate='02-01-2023', systime='11:00:00', compid=1, finyearid=2023, sessionid='testuser',
            material=cls.material_button, effdate='20-01-2023', livecost=0.150
        )

    def setUp(self):
        self.client = Client()
        # Simulate authenticated user for views that rely on it
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(username='testuser', password='password')
        self.client.login(username='testuser', password='password')

    def test_livecost_list_view(self):
        response = self.client.get(reverse('livecost_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_costing/livecost/list.html')
        self.assertIsInstance(response.context['form'], LiveCostForm)

    def test_livecost_table_partial_view(self):
        response = self.client.get(reverse('livecost_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_costing/livecost/_livecost_table.html')
        self.assertIn('livecosts', response.context)
        self.assertEqual(len(response.context['livecosts']), 2) # Check if all objects are retrieved

    def test_livecost_create_view_get(self):
        response = self.client.get(reverse('livecost_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_costing/livecost/_livecost_form.html')
        self.assertIn('form', response.context)

    def test_livecost_create_view_post_success(self):
        data = {
            'material': self.material_cotton.id,
            'livecost': '99.999',
            'effdate': '25-03-2024'
        }
        response = self.client.post(reverse('livecost_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HX-Request expects 204 No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLiveCostList')
        self.assertTrue(LiveCost.objects.filter(material=self.material_cotton, livecost=99.999, effdate='25-03-2024').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Material Live Cost added successfully.')

    def test_livecost_create_view_post_validation_error(self):
        data = {
            'material': self.material_cotton.id,
            'livecost': 'invalid',
            'effdate': '25-03-2024'
        }
        response = self.client.post(reverse('livecost_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'material_costing/livecost/_livecost_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Cost must be a number', response.context['form'].errors['livecost'][0])

    def test_livecost_update_view_get(self):
        response = self.client.get(reverse('livecost_edit', args=[self.livecost1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_costing/livecost/_livecost_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.livecost1)

    def test_livecost_update_view_post_success(self):
        data = {
            'material': self.livecost1.material.id, # Keep material same or change
            'livecost': '12.345',
            'effdate': '01-02-2023'
        }
        response = self.client.post(reverse('livecost_edit', args=[self.livecost1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLiveCostList')
        self.livecost1.refresh_from_db()
        self.assertEqual(self.livecost1.livecost, 12.345)
        self.assertEqual(self.livecost1.effdate, '01-02-2023')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Material Live Cost updated successfully.')

    def test_livecost_delete_view_get(self):
        response = self.client.get(reverse('livecost_delete', args=[self.livecost1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_costing/livecost/_confirm_delete.html')
        self.assertIn('livecost', response.context)
        self.assertEqual(response.context['livecost'], self.livecost1)

    def test_livecost_delete_view_post_success(self):
        initial_count = LiveCost.objects.count()
        response = self.client.post(reverse('livecost_delete', args=[self.livecost1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLiveCostList')
        self.assertEqual(LiveCost.objects.count(), initial_count - 1)
        self.assertFalse(LiveCost.objects.filter(id=self.livecost1.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Material Live Cost deleted successfully.')

    def test_material_uom_api_view(self):
        response = self.client.post(reverse('api_material_uom'), {'material': self.material_cotton.id})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode('utf-8'), 'KG')

        response = self.client.post(reverse('api_material_uom'), {'material': self.material_button.id})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode('utf-8'), 'PCS')

        response = self.client.post(reverse('api_material_uom'), {'material': '9999'}) # Non-existent ID
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode('utf-8'), '')
```

### Step 5: HTMX and Alpine.js Integration

The provided Django code snippets (templates, views, forms) already incorporate HTMX and Alpine.js principles:

*   **HTMX for Dynamic Updates**:
    *   **List View Refresh**: `hx-trigger="load, refreshLiveCostList from:body"` on the `livecostTable-container` ensures the table is loaded initially and refreshed whenever a `refreshLiveCostList` custom event is triggered (e.g., after a successful form submission or deletion).
    *   **Modal Form Loading**: `hx-get` attributes on "Add New" and "Edit" buttons fetch the `_livecost_form.html` partial into the `#modalContent` div.
    *   **Modal Confirmation Loading**: `hx-get` on "Delete" buttons fetches `_confirm_delete.html`.
    *   **Form Submission**: `hx-post` on the form elements (within modals) submits data. `hx-swap="none"` prevents direct content swap, relying on the `HX-Trigger` from the view to refresh the list and Alpine.js to close the modal.
    *   **Dynamic UOM**: `hx-post` on the `material` dropdown (`<select>`) calls the `api_material_uom` endpoint, and `hx-target="#id_material_uom_symbol"` with `hx-swap="value"` updates only the value of the UOM input field.
*   **Alpine.js for UI State**:
    *   **Modal Visibility**: The `x-data` and `on click add/remove .is-active to #modal` directives (using `_` for `hyperscript` which Alpine.js often pairs with) manage the `hidden` class on the modal, controlling its display. The modal content `on htmx:afterOnLoad add .is-active to #modal` ensures it becomes visible after HTMX loads content.
    *   **Form Input Control**: `x-model` is used for `livecostInput` and `effdateInput` to bind input values for client-side logic.
    *   **Client-Side Formatting/Validation**: The `@input="formatLiveCost(livecostInput)"` Alpine.js directive on the cost input replicates the ASP.NET `onkeyup` logic for numeric input cleaning and formatting. `x-mask` is used for date input formatting.
*   **DataTables Integration**:
    *   The `_livecost_table.html` partial contains a `<table>` with `id="livecostTable"`.
    *   A `<script>` block within this partial ensures that `$(document).ready(function() { $('#livecostTable').DataTable({...}); });` is called every time the partial is loaded by HTMX, correctly initializing the DataTables instance on the dynamically inserted table. This includes destroying any previous instance to prevent re-initialization errors.

These integrations ensure a highly responsive, single-page application like experience without complex JavaScript frameworks, making the migration efficient and maintainable.

### Final Notes

*   **Authentication/Session**: The ASP.NET application heavily relies on `Session["username"]`, `Session["compid"]`, `Session["finyear"]`. In Django, `request.user` from the authentication system would replace `Session["username"]`. `CompId` and `FinYearId` would typically be stored in a UserProfile model linked to `request.user`, or managed via a custom middleware that sets these context variables based on the authenticated user's organization/role. For this migration, we've injected these into the `LiveCost` model's `save` method as attributes, assuming they are available from the `request`.
*   **Error Handling**: Basic `try-catch` in ASP.NET is replaced by Django's form validation and default error display. For production, more robust logging and user-friendly error messages should be implemented.
*   **Styling**: All templates assume Tailwind CSS is configured and available. Class names like `bg-blue-500`, `py-2`, `px-4`, `rounded` are Tailwind utility classes.
*   **Base Template**: The `core/base.html` is assumed to include global assets like jQuery, DataTables CDN links, FontAwesome for icons, HTMX, and Alpine.js. This ensures all components inherit necessary libraries and maintain a DRY structure.
*   **Date Handling**: The `EffDate` column in the ASP.NET database is stored as `VARCHAR`. Django's `CharField` is used to match this, with Python-side validation and formatting. For a clean-slate Django project, a `DateField` would be preferred, handling date objects directly. This choice respects the existing database schema.
*   **Automation Focus**: This plan details the architectural mapping and specific code transformations that an AI-assisted automation tool would perform, from schema inference to code generation for models, forms, views, templates, and tests. The conversion logic for ASP.NET control properties to Django/HTMX/Alpine.js attributes is systematic and repeatable.