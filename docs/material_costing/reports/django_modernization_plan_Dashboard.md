## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

This ASP.NET `Dashboard.aspx` page is a very minimal placeholder, primarily defining content regions for a master page and including a `loadingNotifier.js`. It does not contain any explicit database interactions, UI controls, or business logic.

To demonstrate a comprehensive Django modernization plan that adheres to all your requirements (CRUD, Fat Model/Thin View, HTMX, DataTables, etc.), we will infer a common dashboard functionality: managing "Dashboard Items" which could represent customizable widgets, key metrics, or report links on an ERP dashboard. This allows us to illustrate the full modernization process as if the ASP.NET page were intended to manage such entities.

We will create a new Django application named `dashboard_reports`.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is a blank dashboard placeholder with no explicit database definitions, we will infer a common table structure for managing dashboard items or report configurations. This allows us to demonstrate a full-featured migration.

**Inferred Database Table:** `tbl_dashboard_item`

**Inferred Columns:**
- `id` (Primary Key, integer)
- `title` (nvarchar, e.g., for the name of a dashboard widget or report link)
- `description` (nvarchar, for a brief explanation of the item)
- `last_updated` (datetime, to track when the item was last modified)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the minimal ASP.NET input, no explicit CRUD operations are defined. However, a "Dashboard" in an ERP system often implies the ability to configure or manage the items displayed. We will therefore implement full CRUD operations for our inferred `DashboardItem` to provide a complete and functional example.

-   **Create:** Ability to add new dashboard items (e.g., new reports, widgets).
-   **Read:** Display a list of all existing dashboard items, with details.
-   **Update:** Modify existing dashboard items.
-   **Delete:** Remove unwanted dashboard items.
-   **Validation Logic:** Basic validation for required fields (e.g., `title`).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The original ASP.NET page contains no UI controls. Based on the "Dashboard" context and the presence of `loadingNotifier.js` (suggesting dynamic content loading), we infer the following UI components for a modern Django implementation:

-   **List View:** A table-based display (DataTables) showing all `DashboardItem` records, including `title`, `description`, and `last_updated`. This will be dynamically loaded and refreshed using HTMX.
-   **Action Buttons:** Buttons within the list view for "Edit" and "Delete" actions. A "Add New" button to create new items.
-   **Modal Forms:** All Create/Update/Delete operations will be handled via HTMX-loaded forms within a modal dialog. This keeps the user on the same page and provides a smooth experience, mimicking modern single-page application feel.
-   **Client-Side Interactivity:** Alpine.js for modal management (showing/hiding) and general UI state. The `loadingNotifier.js` in the original ASP.NET directly maps to HTMX's inherent loading indicators.

## Step 4: Generate Django Code

We will generate the Django code within a new application, let's call it `dashboard_reports`.

### 4.1 Models (`dashboard_reports/models.py`)

Task: Create a Django model based on the database schema.

## Instructions:

The `DashboardItem` model will map to the `tbl_dashboard_item` table. We'll include methods for basic business logic, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
from datetime import timedelta

class DashboardItem(models.Model):
    # Primary key will be automatically managed by Django if not explicitly defined
    # Assuming 'id' is an auto-incrementing primary key in the existing database
    
    title = models.CharField(
        db_column='title', 
        max_length=255, 
        verbose_name='Dashboard Item Title',
        help_text='A concise title for the dashboard item or report link.'
    )
    description = models.TextField(
        db_column='description', 
        blank=True, 
        null=True, 
        verbose_name='Description',
        help_text='Optional detailed description of the dashboard item.'
    )
    last_updated = models.DateTimeField(
        db_column='last_updated', 
        auto_now=True, 
        verbose_name='Last Updated',
        help_text='Automatically updated timestamp of the last modification.'
    )

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tbl_dashboard_item'  # Map to the existing database table
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['title'] # Default ordering for lists

    def __str__(self):
        """String representation of the DashboardItem."""
        return self.title
        
    # Example of a 'Fat Model' business logic method
    def get_summary(self):
        """Returns a summary of the dashboard item."""
        if self.description:
            return f"{self.title}: {self.description[:100]}{'...' if len(self.description) > 100 else ''}"
        return self.title

    def is_recently_updated(self):
        """Checks if the item was updated in the last 24 hours."""
        return self.last_updated > (timezone.now() - timedelta(days=1))

```

### 4.2 Forms (`dashboard_reports/forms.py`)

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for `DashboardItem`, including styling with Tailwind CSS classes.

```python
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    class Meta:
        model = DashboardItem
        fields = ['title', 'description'] # 'last_updated' is auto_now, so not editable
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter dashboard item title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 3,
                'placeholder': 'Optional description for the dashboard item'
            }),
        }
        labels = {
            'title': 'Item Title',
            'description': 'Item Description',
        }
        
    def clean_title(self):
        """Custom validation for the title field."""
        title = self.cleaned_data['title']
        if len(title) < 3:
            raise forms.ValidationError("Title must be at least 3 characters long.")
        # Example of checking for uniqueness (case-insensitive) if needed
        # if DashboardItem.objects.filter(title__iexact=title).exclude(pk=self.instance.pk).exists():
        #     raise forms.ValidationError("A dashboard item with this title already exists.")
        return title

```

### 4.3 Views (`dashboard_reports/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

Views will be kept thin, leveraging Django's CBVs and HTMX for dynamic content. A `TablePartialView` will be added to serve the DataTables content via HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemListView(ListView):
    """
    Displays the main page for managing Dashboard Items, including the table container.
    """
    model = DashboardItem
    template_name = 'dashboard_reports/dashboarditem/list.html'
    context_object_name = 'dashboard_items'

class DashboardItemTablePartialView(ListView):
    """
    Renders only the DataTables table content, intended to be fetched via HTMX.
    """
    model = DashboardItem
    template_name = 'dashboard_reports/dashboarditem/_dashboarditem_table.html'
    context_object_name = 'dashboard_items'

class DashboardItemCreateView(CreateView):
    """
    Handles creation of new Dashboard Items. Renders a form within a modal.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_reports/dashboarditem/_dashboarditem_form.html' # Use partial template for HTMX
    success_url = reverse_lazy('dashboarditem_list') # Fallback, not used for HTMX

    def form_valid(self, form):
        """
        Handles valid form submission. If HTMX request, trigger a refresh event.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to send back, just trigger
                headers={
                    'HX-Trigger': 'refreshDashboardItemList' # Custom event to refresh table
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add Dashboard Item'
        return context

class DashboardItemUpdateView(UpdateView):
    """
    Handles updating existing Dashboard Items. Renders a form within a modal.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_reports/dashboarditem/_dashboarditem_form.html' # Use partial template for HTMX
    context_object_name = 'dashboard_item'
    success_url = reverse_lazy('dashboarditem_list') # Fallback, not used for HTMX

    def form_valid(self, form):
        """
        Handles valid form submission. If HTMX request, trigger a refresh event.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Dashboard Item'
        return context

class DashboardItemDeleteView(DeleteView):
    """
    Handles deletion of Dashboard Items. Renders a confirmation within a modal.
    """
    model = DashboardItem
    template_name = 'dashboard_reports/dashboarditem/_dashboarditem_confirm_delete.html' # Use partial template
    context_object_name = 'dashboard_item'
    success_url = reverse_lazy('dashboarditem_list') # Fallback

    def delete(self, request, *args, **kwargs):
        """
        Handles the actual deletion and returns HTMX-specific response.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will extend `core/base.html`, use DataTables for lists, and leverage HTMX/Alpine.js for dynamic interactions, ensuring no full page reloads for CRUD operations.

#### `dashboard_reports/dashboarditem/list.html`

```html
{% extends 'core/base.html' %}

{% block title %}Dashboard Items - AutoERP{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then transition my opacity to 1 and my scale to 1"
            >
            <i class="fas fa-plus mr-2"></i> Add New Item
        </button>
    </div>

    <div id="dashboardItemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-lg p-6">
        <!-- DataTables content will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>

    <!-- Universal Modal for HTMX content -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 items-center justify-center hidden z-50 transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .flex from me then transition my opacity to 0 and my scale to 0"
         style="opacity:0; transform:scale(0);">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             _="on load set my opacity to 1 and my scale to 1"
             >
            <!-- HTMX loaded content (form/delete confirmation) will appear here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables CDN is expected in base.html -->
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be initialized here if more complex UI state is needed
        // For simple modal show/hide, htmx + _hyperscript is often sufficient.
    });
</script>
{% endblock %}
```

#### `dashboard_reports/dashboarditem/_dashboarditem_table.html` (Partial)

```html
<table id="dashboardItemTable" class="min-w-full bg-white border border-gray-200 rounded-md shadow-sm">
    <thead>
        <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <th class="py-3 px-4 border-b border-gray-200">SN</th>
            <th class="py-3 px-4 border-b border-gray-200">Item Title</th>
            <th class="py-3 px-4 border-b border-gray-200">Description</th>
            <th class="py-3 px-4 border-b border-gray-200">Last Updated</th>
            <th class="py-3 px-4 border-b border-gray-200">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in dashboard_items %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ item.title }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ item.description|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ item.last_updated|date:"Y-m-d H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-xs mr-2 transition duration-200 ease-in-out"
                    hx-get="{% url 'dashboarditem_edit' item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then transition my opacity to 1 and my scale to 1">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-200 ease-in-out"
                    hx-get="{% url 'dashboarditem_delete' item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then transition my opacity to 1 and my scale to 1">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No dashboard items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        $('#dashboardItemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            "responsive": true, // Make table responsive
            "searching": true, // Enable search box
            "ordering": true,  // Enable sorting
            "paging": true     // Enable pagination
        });
    });
</script>
```

#### `dashboard_reports/dashboarditem/_dashboarditem_form.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) {
                                  document.querySelector('#modal').classList.remove('flex');
                                  document.querySelector('#modal').style.opacity = '0';
                                  document.querySelector('#modal').style.transform = 'scale(0)';
                                }">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out"
                _="on click remove .flex from #modal then transition my opacity to 0 and my scale to 0">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `dashboard_reports/dashboarditem/_dashboarditem_confirm_delete.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Dashboard Item: <span class="font-bold">"{{ dashboard_item.title }}"</span>?</p>
    <p class="text-red-600 text-sm mb-6">This action cannot be undone.</p>

    <form hx-post="{% url 'dashboarditem_delete' dashboard_item.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) {
                                  document.querySelector('#modal').classList.remove('flex');
                                  document.querySelector('#modal').style.opacity = '0';
                                  document.querySelector('#modal').style.transform = 'scale(0)';
                                }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out"
                _="on click remove .flex from #modal then transition my opacity to 0 and my scale to 0">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`dashboard_reports/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

URL patterns will be defined to map to the respective views, including separate endpoints for HTMX-loaded partials.

```python
from django.urls import path
from .views import (
    DashboardItemListView,
    DashboardItemTablePartialView,
    DashboardItemCreateView,
    DashboardItemUpdateView,
    DashboardItemDeleteView
)

urlpatterns = [
    # Main list page
    path('dashboard-items/', DashboardItemListView.as_view(), name='dashboarditem_list'),

    # HTMX endpoints for CRUD operations and partials
    path('dashboard-items/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'),
    path('dashboard-items/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('dashboard-items/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('dashboard-items/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]

```
*(Note: You would also need to include this `urls.py` in your project's main `urls.py`):*
```python
# In your main project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('reports/', include('dashboard_reports.urls')), # Assuming 'reports/' as the base path for this app
]
```

### 4.6 Tests (`dashboard_reports/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `DashboardItem` model and integration tests for all CRUD views, including HTMX-specific responses, will be provided.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item1 = DashboardItem.objects.create(
            title='Sales Overview Report',
            description='Key metrics for sales performance.',
            last_updated=timezone.now()
        )
        cls.item2 = DashboardItem.objects.create(
            title='Inventory Levels Dashboard',
            description='Real-time stock quantities and alerts.',
            last_updated=timezone.now() - timedelta(days=2) # Older item
        )

    def test_dashboard_item_creation(self):
        """Test that a DashboardItem can be created correctly."""
        self.assertEqual(self.item1.title, 'Sales Overview Report')
        self.assertEqual(self.item1.description, 'Key metrics for sales performance.')
        self.assertIsNotNone(self.item1.last_updated)

    def test_title_label(self):
        """Test the verbose name for the title field."""
        field_label = self.item1._meta.get_field('title').verbose_name
        self.assertEqual(field_label, 'Dashboard Item Title')

    def test_description_label(self):
        """Test the verbose name for the description field."""
        field_label = self.item1._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Description')

    def test_str_method(self):
        """Test the __str__ method returns the title."""
        self.assertEqual(str(self.item1), 'Sales Overview Report')

    def test_get_summary_method(self):
        """Test the get_summary method."""
        summary = self.item1.get_summary()
        self.assertIn(self.item1.title, summary)
        self.assertIn(self.item1.description[:50], summary)

        item_no_desc = DashboardItem.objects.create(title='No Description Item', description='')
        self.assertEqual(item_no_desc.get_summary(), 'No Description Item')

    def test_is_recently_updated_method(self):
        """Test the is_recently_updated method."""
        self.assertTrue(self.item1.is_recently_updated())
        self.assertFalse(self.item2.is_recently_updated()) # Updated 2 days ago

class DashboardItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all views tests
        cls.item = DashboardItem.objects.create(
            title='Financial Dashboard',
            description='Overview of company financials.',
            last_updated=timezone.now()
        )

    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()

    def test_list_view(self):
        """Test the main list view."""
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_reports/dashboarditem/list.html')
        self.assertIn('dashboard_items', response.context)
        self.assertContains(response, self.item.title)

    def test_table_partial_view(self):
        """Test the HTMX-loaded table partial view."""
        response = self.client.get(reverse('dashboarditem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_reports/dashboarditem/_dashboarditem_table.html')
        self.assertIn('dashboard_items', response.context)
        self.assertContains(response, self.item.title)
        # Ensure it's not a full page template
        self.assertNotContains(response, '<!DOCTYPE html>')


    def test_create_view_get(self):
        """Test getting the create form (via HTMX for modal)."""
        response = self.client.get(reverse('dashboarditem_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_reports/dashboarditem/_dashboarditem_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], DashboardItemForm)

    def test_create_view_post_success(self):
        """Test successful creation via POST."""
        initial_count = DashboardItem.objects.count()
        data = {
            'title': 'New Test Item',
            'description': 'A description for the new test item.',
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertEqual(DashboardItem.objects.count(), initial_count + 1)
        self.assertTrue(DashboardItem.objects.filter(title='New Test Item').exists())

    def test_create_view_post_invalid(self):
        """Test invalid creation via POST (e.g., missing title)."""
        initial_count = DashboardItem.objects.count()
        data = {
            'title': '', # Invalid title
            'description': 'Description',
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dashboard_reports/dashboarditem/_dashboarditem_form.html')
        self.assertFormError(response, 'form', 'title', 'Title must be at least 3 characters long.')
        self.assertEqual(DashboardItem.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test getting the update form for an existing item."""
        response = self.client.get(reverse('dashboarditem_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_reports/dashboarditem/_dashboarditem_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.item)

    def test_update_view_post_success(self):
        """Test successful update via POST."""
        data = {
            'title': 'Updated Financial Dashboard',
            'description': 'Revised overview of company financials.',
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.item.refresh_from_db()
        self.assertEqual(self.item.title, 'Updated Financial Dashboard')
        self.assertEqual(self.item.description, 'Revised overview of company financials.')

    def test_update_view_post_invalid(self):
        """Test invalid update via POST."""
        data = {
            'title': 'a', # Too short
            'description': 'Test',
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dashboard_reports/dashboarditem/_dashboarditem_form.html')
        self.assertFormError(response, 'form', 'title', 'Title must be at least 3 characters long.')
        self.item.refresh_from_db()
        self.assertNotEqual(self.item.title, 'a') # Title should not have changed


    def test_delete_view_get(self):
        """Test getting the delete confirmation (via HTMX for modal)."""
        response = self.client.get(reverse('dashboarditem_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_reports/dashboarditem/_dashboarditem_confirm_delete.html')
        self.assertIn('dashboard_item', response.context)
        self.assertEqual(response.context['dashboard_item'], self.item)

    def test_delete_view_post_success(self):
        """Test successful deletion via POST."""
        item_to_delete = DashboardItem.objects.create(title='Temporary Item', description='Will be deleted.')
        initial_count = DashboardItem.objects.count()
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertEqual(DashboardItem.objects.count(), initial_count - 1)
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for All Dynamic Updates:**
    -   The main list page (`list.html`) uses `hx-get` to load the table content (`_dashboarditem_table.html`) on initial page load and on `refreshDashboardItemList` custom event.
    -   CRUD forms (`_dashboarditem_form.html`, `_dashboarditem_confirm_delete.html`) are fetched via `hx-get` into a modal container (`#modalContent`).
    -   Form submissions use `hx-post` with `hx-swap="none"` and custom `hx-on::after-request` logic. On successful submission (status 204), a `HX-Trigger: refreshDashboardItemList` header is sent by the Django view, which then tells the main list page to reload its table via `hx-get`. This ensures the table is always up-to-date without full page reloads.
    -   Error handling for forms is handled by re-rendering the form within the modal if validation fails (HTTP 200 response with errors).

-   **Alpine.js for UI State Management:**
    -   Used indirectly via `_hyperscript` for modal display logic. The `list.html` uses `_` attributes for showing/hiding the modal (`add .flex to #modal`, `remove .flex from me`, `transition my opacity`). This provides a smooth transition effect for the modal.
    -   No complex `x-data` components are strictly necessary for this basic modal pattern, but Alpine.js is available for more involved client-side interactions if needed.

-   **DataTables for List Views:**
    -   The `_dashboarditem_table.html` partial directly initializes DataTables on `document.ready`. Since this partial is loaded via HTMX, the `$(document).ready()` inside the partial ensures DataTables is re-initialized whenever the table content is re-fetched.
    -   This provides client-side searching, sorting, and pagination for an enhanced user experience without backend overhead.

-   **No Additional JavaScript:**
    -   All dynamic interactions are handled by HTMX and _hyperscript, fulfilling the "no additional JavaScript" requirement beyond what's strictly necessary for DataTables and Alpine.js/hyperscript itself (which are CDNs managed by `base.html`).

This comprehensive plan provides a modern, maintainable, and highly interactive Django application that fully replaces the original placeholder ASP.NET dashboard, ready for further expansion with actual ERP-specific data and functionalities.