import sys


from IPython.display import HTM<PERSON>, Image, Markdown, display
from google import genai
from google.genai.types import (
    FunctionDeclaration,
    GenerateContentConfig,
    GoogleSearch,
    HarmBlockThreshold,
    HarmCategory,
    Part,
    SafetySetting,
    ThinkingConfig,
    Tool,
    ToolCodeExecution,
)

import os
from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv("GEMINI_API_KEY")
client = genai.Client(api_key=API_KEY)
MODEL_ID = os.getenv("MODEL_ID")


THINKING_BUDGET = 8024  # @param {type: "integer"}
INCLUDE_THOUGHTS = True  # @param {type: "boolean"}

thinking_config = ThinkingConfig(
    thinking_budget=THINKING_BUDGET,
    include_thoughts=INCLUDE_THOUGHTS,
)

file_path = "/Users/<USER>/workspace/autoerp/aaspnet/Module/SysAdmin/Country.aspx"

with open('imp')


with open(file_path, "r") as file:
    contents = file.read()

prompt = f"""
Read the file {} {file_path} and tell me what is the purpose of the file.
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=prompt,
    config=GenerateContentConfig(
        temperature=2.0,
        top_p=0.95,
        candidate_count=1,
        thinking_config=thinking_config,
    ),
)

print(response.text)
